{"ast": null, "code": "import { createContext as o, useContext as r, useMemo as i } from \"react\";\nimport { ListboxMachine as s } from './listbox-machine.js';\nconst c = o(null);\nfunction l(t) {\n  let e = r(c);\n  if (e === null) {\n    let n = new Error(`<${t} /> is missing a parent <Listbox /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(n, a), n;\n  }\n  return e;\n}\nfunction a() {\n  let {\n    __demoMode: t = !1\n  } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return i(() => s.new({\n    __demoMode: t\n  }), []);\n}\nexport { c as ListboxContext, a as useListboxMachine, l as useListboxMachineContext };", "map": {"version": 3, "names": ["createContext", "o", "useContext", "r", "useMemo", "i", "ListboxMachine", "s", "c", "l", "t", "e", "n", "Error", "captureStackTrace", "a", "__demoMode", "arguments", "length", "undefined", "new", "ListboxContext", "useListboxMachine", "useListboxMachineContext"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/node_modules/@headlessui/react/dist/components/listbox/listbox-machine-glue.js"], "sourcesContent": ["import{createContext as o,useContext as r,useMemo as i}from\"react\";import{ListboxMachine as s}from'./listbox-machine.js';const c=o(null);function l(t){let e=r(c);if(e===null){let n=new Error(`<${t} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,a),n}return e}function a({__demoMode:t=!1}={}){return i(()=>s.new({__demoMode:t}),[])}export{c as ListboxContext,a as useListboxMachine,l as useListboxMachineContext};\n"], "mappings": "AAAA,SAAOA,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,sBAAsB;AAAC,MAAMC,CAAC,GAACP,CAAC,CAAC,IAAI,CAAC;AAAC,SAASQ,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACR,CAAC,CAACK,CAAC,CAAC;EAAC,IAAGG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,IAAIH,CAAC,gDAAgD,CAAC;IAAC,MAAMG,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACG,CAAC,CAAC,EAACH,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAAA,EAAsB;EAAA,IAArB;IAACC,UAAU,EAACN,CAAC,GAAC,CAAC;EAAC,CAAC,GAAAO,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,CAAC,CAAC;EAAE,OAAOZ,CAAC,CAAC,MAAIE,CAAC,CAACa,GAAG,CAAC;IAACJ,UAAU,EAACN;EAAC,CAAC,CAAC,EAAC,EAAE,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIa,cAAc,EAACN,CAAC,IAAIO,iBAAiB,EAACb,CAAC,IAAIc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}