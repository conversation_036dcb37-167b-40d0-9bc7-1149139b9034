{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\HtmlEmailEditor.tsx\",\n  _s = $RefreshSig$();\nimport 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\n\n// Optional: Import a preset like grapesjs-preset-newsletter if desired for more features\n// import 'grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.min.css';\n// import grapesjsNewsletter from 'grapesjs-preset-newsletter';\nimport React, { forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';\nimport grapesjs from 'grapesjs';\n\n// Define the Ref type for exposing editor methods\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Performance optimization: Cache HTML to avoid expensive re-generations\nconst htmlCache = new Map();\n\n// Basic inline CSS styles for email compatibility\nconst emailBodyStyle = `\n  body {\n    margin: 0;\n    padding: 0;\n    font-family: Arial, sans-serif;\n    line-height: 1.5;\n    color: #333333;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    margin-top: 0;\n    font-family: Arial, sans-serif;\n    color: #333333;\n  }\n  img {\n    max-width: 100%;\n    height: auto;\n  }\n  .container {\n    max-width: 600px;\n    margin: 0 auto;\n  }\n  a {\n    color: #007bff;\n    text-decoration: none;\n  }\n  p {\n    margin: 10px 0;\n  }\n`;\n\n// Optimize blocks configuration with useMemo\nconst getEmailBlocks = useMemo(() => [{\n  id: 'text',\n  label: 'Text',\n  attributes: {\n    class: 'fa fa-font',\n    style: 'font-size: 24px;'\n  },\n  category: 'Content',\n  content: {\n    type: 'text',\n    content: '<p style=\"padding: 10px 20px; margin: 0;\">Add your text here. Edit the text by clicking on it.</p>',\n    style: {\n      padding: '10px'\n    },\n    activeOnRender: true\n  }\n}, {\n  id: 'image',\n  label: 'Image',\n  attributes: {\n    class: 'fa fa-image',\n    style: 'font-size: 24px;'\n  },\n  category: 'Content',\n  select: true,\n  // Open asset manager on drop\n  content: {\n    type: 'image',\n    style: {\n      color: '#000',\n      width: '100%'\n    },\n    activeOnRender: true\n  }\n}, {\n  id: 'button',\n  label: 'Button',\n  attributes: {\n    class: 'fa fa-link',\n    style: 'font-size: 24px;'\n  },\n  category: 'Content',\n  content: `<a href=\"#\" style=\"display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;\" data-gjs-type=\"link\">Button Text</a>`\n}, {\n  id: 'divider',\n  label: 'Divider',\n  attributes: {\n    class: 'fa fa-minus',\n    style: 'font-size: 24px;'\n  },\n  category: 'Content',\n  content: `<hr style=\"border-top: 1px solid #ccc; margin: 20px 0;\" />`\n}, {\n  id: 'spacer',\n  label: 'Spacer',\n  attributes: {\n    class: 'fa fa-arrows-v',\n    style: 'font-size: 24px;'\n  },\n  category: 'Layout',\n  content: `<div style=\"height: 50px;\"></div>`\n}, {\n  id: 'two-columns',\n  label: '2 Columns',\n  attributes: {\n    class: 'fa fa-columns',\n    style: 'font-size: 24px;'\n  },\n  category: 'Layout',\n  content: `\n      <div style=\"display: flex; flex-wrap: wrap; margin: 0 -10px;\">\n        <div style=\"width: 50%; padding: 0 10px; box-sizing: border-box;\">\n          <p style=\"padding: 10px; margin: 0;\">First column content</p>\n        </div>\n        <div style=\"width: 50%; padding: 0 10px; box-sizing: border-box;\">\n          <p style=\"padding: 10px; margin: 0;\">Second column content</p>\n        </div>\n      </div>\n    `\n}], []);\n\n// Cache common panel configurations\nconst getPanels = useMemo(() => ({\n  // Default panels\n  styleManager: {\n    appendTo: '.styles-container'\n  },\n  layerManager: {\n    appendTo: '.layers-container'\n  },\n  // Custom panel for better visibility of what's happening\n  'editor-status': {\n    el: '.status-bar',\n    appendTo: '.editor-row',\n    content: `<div class=\"editor-status\">\n                <div class=\"gjs-sm-sector\">\n                  <div class=\"gjs-sm-title\">Editor Status</div>\n                  <div class=\"gjs-sm-properties\">\n                    <div class=\"status-item\" data-status=\"components\">Loading...</div>\n                  </div>\n                </div>\n              </div>`\n  }\n}), []);\n\n// Debounce function for performance optimizations\nfunction debounce(func, wait) {\n  let timeoutId = null;\n  return function (...args) {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      func(...args);\n      timeoutId = null;\n    }, wait);\n  };\n}\n\n// Throttle function to limit execution frequency\nfunction throttle(func, limit) {\n  let inThrottle = false;\n  let lastFunc;\n  let lastRan;\n  return function (...args) {\n    if (!inThrottle) {\n      func(...args);\n      lastRan = Date.now();\n      inThrottle = true;\n      setTimeout(() => {\n        inThrottle = false;\n      }, limit);\n    } else {\n      clearTimeout(lastFunc);\n      lastFunc = setTimeout(() => {\n        if (Date.now() - lastRan >= limit) {\n          func(...args);\n          lastRan = Date.now();\n        }\n      }, limit - (Date.now() - lastRan));\n    }\n  };\n}\n\n// Worker function for HTML generation (would normally be in separate file)\nconst createHTMLGenerationWorker = () => {\n  const workerCode = `\n    self.onmessage = function(e) {\n      const { html, css } = e.data;\n      \n      try {\n        // Combine HTML and CSS\n        const fullHtml = \\`<!DOCTYPE html>\n        <html>\n          <head>\n            <meta charset=\"UTF-8\">\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n            <title>Email Template</title>\n            <style>\\${css}</style>\n          </head>\n          <body>\\${html}</body>\n        </html>\\`;\n        \n        self.postMessage({ html: fullHtml, error: null });\n      } catch (error) {\n        self.postMessage({ html: null, error: error.message });\n      }\n    };\n  `;\n  const blob = new Blob([workerCode], {\n    type: 'application/javascript'\n  });\n  return new Worker(URL.createObjectURL(blob));\n};\n\n// Main component implementation with memoization\nconst HtmlEmailEditorComponent = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s((props, ref) => {\n  _s();\n  const {\n    initialHtml = '',\n    onSave,\n    height = '500px'\n  } = props;\n  const containerRef = useRef(null);\n  const [editor, setEditor] = useState(null);\n  const [isEditorReady, setIsEditorReady] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [lastSavedAt, setLastSavedAt] = useState(null);\n  const lastHtmlRef = useRef(initialHtml);\n  const [activePanel, setActivePanel] = useState(null);\n  const [worker, setWorker] = useState(null);\n\n  // Create HTML generation worker once on component mount\n  useEffect(() => {\n    if (typeof Worker !== 'undefined') {\n      const newWorker = createHTMLGenerationWorker();\n      setWorker(newWorker);\n      return () => {\n        newWorker.terminate();\n      };\n    }\n  }, []);\n\n  // Expose methods to parent through ref\n  useImperativeHandle(ref, () => ({\n    // Save method - get the HTML from the editor\n    save: async () => {\n      if (!editor) {\n        console.error('[HtmlEmailEditor] Editor not initialized');\n        return {\n          html: ''\n        };\n      }\n      try {\n        var _editor$getDirtyCount;\n        setIsSaving(true);\n\n        // Get HTML and CSS from the editor\n        const html = editor.getHtml();\n        const css = editor.getCss({\n          avoidProtected: true\n        });\n\n        // Generate full HTML\n        let finalHtml;\n\n        // Use worker if available for performance\n        if (worker) {\n          finalHtml = await new Promise((resolve, reject) => {\n            const messageHandler = e => {\n              worker.removeEventListener('message', messageHandler);\n              if (e.data.error) {\n                reject(new Error(e.data.error));\n              } else {\n                resolve(e.data.html);\n              }\n            };\n            worker.addEventListener('message', messageHandler);\n            worker.postMessage({\n              html,\n              css\n            });\n          });\n        } else {\n          // Fallback to synchronous processing\n          finalHtml = `<!DOCTYPE html>\n          <html>\n            <head>\n              <meta charset=\"UTF-8\">\n              <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n              <title>Email Template</title>\n              <style>${css}</style>\n            </head>\n            <body>${html}</body>\n          </html>`;\n        }\n\n        // Cache the result for future reference\n        if (((_editor$getDirtyCount = editor.getDirtyCount) === null || _editor$getDirtyCount === void 0 ? void 0 : _editor$getDirtyCount.call(editor)) > 0 || !htmlCache.has(html)) {\n          htmlCache.set(html, finalHtml);\n        }\n\n        // If onSave callback is provided, call it with the HTML\n        if (onSave) {\n          onSave(finalHtml);\n        }\n\n        // Update the last saved time\n        setLastSavedAt(new Date());\n        lastHtmlRef.current = html;\n\n        // Clear the dirty state\n        if (editor.UndoManager) {\n          editor.UndoManager.clear();\n        }\n\n        // Or set dirty count directly if UndoManager isn't available\n        if (typeof editor.getDirtyCount === 'function') {\n          // Force clear dirty count by manually tracking it\n          editor._dirtyCount = 0;\n        }\n        console.log(\"[HtmlEmailEditor] Save complete:\", new Date().toISOString());\n        return {\n          html: finalHtml\n        };\n      } catch (error) {\n        console.error('[HtmlEmailEditor] Error saving:', error);\n        return {\n          html: ''\n        };\n      } finally {\n        setIsSaving(false);\n      }\n    },\n    // Get the editor instance\n    getEditor: () => editor\n  }));\n\n  // Create a debounced save function\n  const debouncedSave = useCallback(debounce(() => {\n    if (editor && onSave) {\n      // Only save if content has changed\n      const currentHtml = editor.getHtml();\n      if (currentHtml !== lastHtmlRef.current) {\n        console.log('[HtmlEmailEditor] Auto-saving...');\n        // Call the save method through ref - safely handled with optional chaining\n        if (typeof ref === 'function') {\n          // Can't directly call save() on a function ref\n          console.warn('[HtmlEmailEditor] Function refs cannot be used for auto-save');\n        } else if (ref !== null && ref !== void 0 && ref.current) {\n          ref.current.save();\n        }\n      }\n    }\n  }, 500),\n  // 500ms debounce\n  [editor, onSave, ref]);\n\n  // Initialize editor when component mounts\n  useEffect(() => {\n    if (!containerRef.current || editor) return;\n\n    // Create editor with cached options\n    const gjsEditor = grapesjs.init({\n      // Container where the editor will be mounted\n      container: containerRef.current,\n      // Disable default storage manager\n      storageManager: false,\n      // Default HTML when the editor starts\n      components: initialHtml,\n      // Default style when the editor starts\n      style: emailBodyStyle,\n      // Load panels on demand to improve initial load time\n      panels: {\n        defaults: []\n      },\n      // Disable unused features for better performance\n      deviceManager: {\n        devices: [{\n          name: 'Desktop',\n          width: ''\n        }, {\n          name: 'Tablet',\n          width: '768px',\n          widthMedia: '992px'\n        }, {\n          name: 'Mobile',\n          width: '320px',\n          widthMedia: '480px'\n        }]\n      },\n      // Configure asset manager with optimization\n      assetManager: {\n        assets: [],\n        uploadFile: null,\n        // Disable upload to avoid costly IO\n        autoAdd: true,\n        embedAsBase64: true // Inline images as base64 for portability\n      },\n      // Optimize plugin loading\n      plugins: [],\n      // Custom configuration for email compatibility\n      canvas: {\n        styles: ['https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css'],\n        scripts: []\n      }\n    });\n\n    // Set editor to state\n    setEditor(gjsEditor);\n\n    // Initialize blocks only when needed\n    gjsEditor.BlockManager.add('text', {\n      label: 'Text',\n      attributes: {\n        class: 'fa fa-font'\n      },\n      content: {\n        type: 'text',\n        content: '<p>Add your text here</p>',\n        style: {\n          padding: '10px'\n        }\n      }\n    });\n    gjsEditor.BlockManager.add('image', {\n      label: 'Image',\n      attributes: {\n        class: 'fa fa-image'\n      },\n      select: true,\n      content: {\n        type: 'image',\n        style: {\n          padding: '10px',\n          width: '100%'\n        },\n        attributes: {\n          src: 'data:image/svg+xml,%3Csvg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"%3E%3Crect width=\"24\" height=\"24\" fill=\"%23f0f0f0\"/%3E%3Cpath d=\"M12 6.5c-3.04 0-5.5 2.46-5.5 5.5s2.46 5.5 5.5 5.5 5.5-2.46 5.5-5.5-2.46-5.5-5.5-5.5z\" fill=\"%23ddd\"/%3E%3C/svg%3E'\n        }\n      }\n    });\n    gjsEditor.BlockManager.add('button', {\n      label: 'Button',\n      attributes: {\n        class: 'fa fa-square'\n      },\n      content: '<a class=\"button\" style=\"display:inline-block;padding:10px 20px;background-color:#4CAF50;color:#ffffff;text-decoration:none;border-radius:4px;text-align:center;font-weight:bold;\">Click me</a>'\n    });\n\n    // Add style manager panels on demand for better performance\n    gjsEditor.Panels.addPanel({\n      id: 'panel-switcher',\n      visible: true,\n      buttons: [{\n        id: 'show-style',\n        label: 'Styles',\n        command: 'show-styles',\n        active: false\n      }, {\n        id: 'show-layers',\n        label: 'Layers',\n        command: 'show-layers',\n        active: false\n      }, {\n        id: 'show-blocks',\n        label: 'Blocks',\n        command: 'show-blocks',\n        active: true\n      }]\n    });\n\n    // Add commands to switch between panels\n    gjsEditor.Commands.add('show-blocks', {\n      run: function (editor) {\n        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');\n        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');\n        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');\n        if (blocksBtn) blocksBtn.set('active', true);\n        if (styleBtn) styleBtn.set('active', false);\n        if (layersBtn) layersBtn.set('active', false);\n        setActivePanel('blocks');\n      }\n    });\n    gjsEditor.Commands.add('show-styles', {\n      run: function (editor) {\n        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');\n        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');\n        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');\n        if (blocksBtn) blocksBtn.set('active', false);\n        if (styleBtn) styleBtn.set('active', true);\n        if (layersBtn) layersBtn.set('active', false);\n        setActivePanel('styles');\n      }\n    });\n    gjsEditor.Commands.add('show-layers', {\n      run: function (editor) {\n        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');\n        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');\n        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');\n        if (blocksBtn) blocksBtn.set('active', false);\n        if (styleBtn) styleBtn.set('active', false);\n        if (layersBtn) layersBtn.set('active', true);\n        setActivePanel('layers');\n      }\n    });\n\n    // Run the blocks command by default\n    gjsEditor.runCommand('show-blocks');\n\n    // Add change event listener\n    const throttledOnChange = throttle(() => {\n      debouncedSave();\n    }, 300);\n    gjsEditor.on('change:changesCount', throttledOnChange);\n\n    // Add a beforeunload event listener to warn of unsaved changes\n    const handleBeforeUnload = e => {\n      var _gjsEditor$getDirtyCo;\n      if (((_gjsEditor$getDirtyCo = gjsEditor.getDirtyCount) === null || _gjsEditor$getDirtyCo === void 0 ? void 0 : _gjsEditor$getDirtyCo.call(gjsEditor)) > 0) {\n        e.preventDefault();\n        e.returnValue = '';\n        return '';\n      }\n    };\n    window.addEventListener('beforeunload', handleBeforeUnload);\n\n    // Set editor ready\n    setIsEditorReady(true);\n\n    // Clean up editor when component unmounts\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n      gjsEditor.off('change:changesCount', throttledOnChange);\n      gjsEditor.destroy();\n    };\n  }, [initialHtml, debouncedSave]);\n\n  // Fetch necessary components based on active panel\n  const renderActivePanel = () => {\n    if (!activePanel || !editor) return null;\n    switch (activePanel) {\n      case 'styles':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-container styles-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Style Manager\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"style-manager\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 11\n        }, this);\n      case 'layers':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-container layers-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Layers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"layers-manager\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 591,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this);\n      case 'blocks':\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-container blocks-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"Blocks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            id: \"blocks-manager\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"html-email-editor-container\",\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: typeof height === 'number' ? `${height}px` : height,\n      border: '1px solid #ddd',\n      borderRadius: '4px',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-header\",\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        padding: '10px',\n        borderBottom: '1px solid #ddd',\n        backgroundColor: '#f5f5f5'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-title\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            margin: 0,\n            fontSize: '16px'\n          },\n          children: \"HTML Email Editor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            if (typeof ref === 'function') {\n              console.warn('[HtmlEmailEditor] Function refs cannot be used for manual save');\n            } else if (ref !== null && ref !== void 0 && ref.current) {\n              ref.current.save();\n            }\n          },\n          disabled: isSaving || !isEditorReady,\n          style: {\n            padding: '8px 12px',\n            backgroundColor: '#4CAF50',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: isSaving || !isEditorReady ? 'not-allowed' : 'pointer',\n            opacity: isSaving || !isEditorReady ? 0.7 : 1\n          },\n          children: isSaving ? 'Saving...' : 'Save'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), lastSavedAt && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '10px',\n            fontSize: '12px',\n            color: '#666'\n          },\n          children: [\"Last saved: \", lastSavedAt.toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-content\",\n      style: {\n        display: 'flex',\n        flexGrow: 1,\n        overflow: 'hidden'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-sidebar\",\n        style: {\n          width: '250px',\n          borderRight: '1px solid #ddd',\n          display: 'flex',\n          flexDirection: 'column',\n          backgroundColor: '#f9f9f9'\n        },\n        children: renderActivePanel()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 676,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"editor-canvas\",\n        style: {\n          flexGrow: 1,\n          position: 'relative'\n        },\n        children: [!isEditorReady && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            backgroundColor: 'rgba(255, 255, 255, 0.7)',\n            zIndex: 10\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"Loading editor...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 699,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: containerRef,\n          style: {\n            height: '100%',\n            overflow: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 608,\n    columnNumber: 5\n  }, this);\n}, \"wwXXSku3yqG/mqR+78nvRtZOqRg=\")), \"wwXXSku3yqG/mqR+78nvRtZOqRg=\");\n\n// Apply memo to avoid unnecessary re-renders\n_c2 = HtmlEmailEditorComponent;\nconst HtmlEmailEditor = /*#__PURE__*/memo(HtmlEmailEditorComponent);\n\n// Add display name for better debugging\n_c3 = HtmlEmailEditor;\nHtmlEmailEditor.displayName = 'HtmlEmailEditor';\nexport default HtmlEmailEditor;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"HtmlEmailEditorComponent$forwardRef\");\n$RefreshReg$(_c2, \"HtmlEmailEditorComponent\");\n$RefreshReg$(_c3, \"HtmlEmailEditor\");", "map": {"version": 3, "names": ["React", "forwardRef", "memo", "useCallback", "useEffect", "useImperativeHandle", "useMemo", "useRef", "useState", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "htmlCache", "Map", "emailBodyStyle", "getEmailBlocks", "id", "label", "attributes", "class", "style", "category", "content", "type", "padding", "activeOnRender", "select", "color", "width", "getPanels", "styleManager", "appendTo", "layerManager", "el", "debounce", "func", "wait", "timeoutId", "args", "clearTimeout", "setTimeout", "throttle", "limit", "inThrottle", "lastFunc", "last<PERSON>an", "Date", "now", "createHTMLGenerationWorker", "workerCode", "blob", "Blob", "Worker", "URL", "createObjectURL", "HtmlEmailEditorComponent", "_s", "_c", "props", "ref", "initialHtml", "onSave", "height", "containerRef", "editor", "setEditor", "isEditorReady", "setIsEditorReady", "isSaving", "setIsSaving", "lastSavedAt", "setLastSavedAt", "lastHtmlRef", "activePanel", "setActivePanel", "worker", "setWorker", "newWorker", "terminate", "save", "console", "error", "html", "_editor$getDirtyCount", "getHtml", "css", "getCss", "avoidProtected", "finalHtml", "Promise", "resolve", "reject", "messageHandler", "e", "removeEventListener", "data", "Error", "addEventListener", "postMessage", "getDirtyCount", "call", "has", "set", "current", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clear", "_dirtyCount", "log", "toISOString", "getEditor", "debouncedSave", "currentHtml", "warn", "gjsEditor", "init", "container", "storageManager", "components", "panels", "defaults", "deviceManager", "devices", "name", "widthMedia", "assetManager", "assets", "uploadFile", "autoAdd", "embedAsBase64", "plugins", "canvas", "styles", "scripts", "BlockManager", "add", "src", "Panels", "addPanel", "visible", "buttons", "command", "active", "Commands", "run", "blocksBtn", "getButton", "styleBtn", "layersBtn", "runCommand", "throttledOnChange", "on", "handleBeforeUnload", "_gjsEditor$getDirtyCo", "preventDefault", "returnValue", "window", "off", "destroy", "renderActivePanel", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "flexDirection", "border", "borderRadius", "overflow", "justifyContent", "alignItems", "borderBottom", "backgroundColor", "margin", "fontSize", "onClick", "disabled", "cursor", "opacity", "marginLeft", "toLocaleTimeString", "flexGrow", "borderRight", "position", "top", "left", "right", "bottom", "zIndex", "_c2", "HtmlEmailEditor", "_c3", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/HtmlEmailEditor.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\n// Optional: Import a preset like grapesjs-preset-newsletter if desired for more features\r\n// import 'grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.min.css';\r\n// import grapesjsNewsletter from 'grapesjs-preset-newsletter';\r\nimport React, {\r\n  forwardRef,\r\n  memo,\r\n  useCallback,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useMemo,\r\n  useRef,\r\n  useState,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface HtmlEmailEditorRef {\r\n  save: () => Promise<{ html: string }>;\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface HtmlEmailEditorProps {\r\n  initialHtml?: string; // Use initialHtml as the primary source\r\n  onSave?: (html: string) => void; // Updated signature\r\n  height?: string | number;\r\n}\r\n\r\n// Performance optimization: Cache HTML to avoid expensive re-generations\r\nconst htmlCache = new Map<string, string>();\r\n\r\n// Basic inline CSS styles for email compatibility\r\nconst emailBodyStyle = `\r\n  body {\r\n    margin: 0;\r\n    padding: 0;\r\n    font-family: Arial, sans-serif;\r\n    line-height: 1.5;\r\n    color: #333333;\r\n  }\r\n  h1, h2, h3, h4, h5, h6 {\r\n    margin-top: 0;\r\n    font-family: Arial, sans-serif;\r\n    color: #333333;\r\n  }\r\n  img {\r\n    max-width: 100%;\r\n    height: auto;\r\n  }\r\n  .container {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n  }\r\n  a {\r\n    color: #007bff;\r\n    text-decoration: none;\r\n  }\r\n  p {\r\n    margin: 10px 0;\r\n  }\r\n`;\r\n\r\n// Optimize blocks configuration with useMemo\r\nconst getEmailBlocks = useMemo(() => [\r\n  {\r\n    id: 'text',\r\n    label: 'Text',\r\n    attributes: {\r\n      class: 'fa fa-font',\r\n      style: 'font-size: 24px;'\r\n    },\r\n    category: 'Content',\r\n    content: {\r\n      type: 'text',\r\n      content: '<p style=\"padding: 10px 20px; margin: 0;\">Add your text here. Edit the text by clicking on it.</p>',\r\n      style: { padding: '10px' },\r\n      activeOnRender: true\r\n    },\r\n  },\r\n  {\r\n    id: 'image',\r\n    label: 'Image',\r\n    attributes: {\r\n      class: 'fa fa-image',\r\n      style: 'font-size: 24px;'\r\n    },\r\n    category: 'Content',\r\n    select: true, // Open asset manager on drop\r\n    content: {\r\n      type: 'image',\r\n      style: { color: '#000', width: '100%' },\r\n      activeOnRender: true\r\n    },\r\n  },\r\n  {\r\n    id: 'button',\r\n    label: 'Button',\r\n    attributes: {\r\n      class: 'fa fa-link',\r\n      style: 'font-size: 24px;'\r\n    },\r\n    category: 'Content',\r\n    content: `<a href=\"#\" style=\"display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;\" data-gjs-type=\"link\">Button Text</a>`\r\n  },\r\n  {\r\n    id: 'divider',\r\n    label: 'Divider',\r\n    attributes: {\r\n      class: 'fa fa-minus',\r\n      style: 'font-size: 24px;'\r\n    },\r\n    category: 'Content',\r\n    content: `<hr style=\"border-top: 1px solid #ccc; margin: 20px 0;\" />`\r\n  },\r\n  {\r\n    id: 'spacer',\r\n    label: 'Spacer',\r\n    attributes: {\r\n      class: 'fa fa-arrows-v',\r\n      style: 'font-size: 24px;'\r\n    },\r\n    category: 'Layout',\r\n    content: `<div style=\"height: 50px;\"></div>`\r\n  },\r\n  {\r\n    id: 'two-columns',\r\n    label: '2 Columns',\r\n    attributes: {\r\n      class: 'fa fa-columns',\r\n      style: 'font-size: 24px;'\r\n    },\r\n    category: 'Layout',\r\n    content: `\r\n      <div style=\"display: flex; flex-wrap: wrap; margin: 0 -10px;\">\r\n        <div style=\"width: 50%; padding: 0 10px; box-sizing: border-box;\">\r\n          <p style=\"padding: 10px; margin: 0;\">First column content</p>\r\n        </div>\r\n        <div style=\"width: 50%; padding: 0 10px; box-sizing: border-box;\">\r\n          <p style=\"padding: 10px; margin: 0;\">Second column content</p>\r\n        </div>\r\n      </div>\r\n    `\r\n  }\r\n], []);\r\n\r\n// Cache common panel configurations\r\nconst getPanels = useMemo(() => ({\r\n  // Default panels\r\n  styleManager: {\r\n    appendTo: '.styles-container'\r\n  },\r\n  layerManager: {\r\n    appendTo: '.layers-container'\r\n  },\r\n  // Custom panel for better visibility of what's happening\r\n  'editor-status': {\r\n    el: '.status-bar',\r\n    appendTo: '.editor-row',\r\n    content: `<div class=\"editor-status\">\r\n                <div class=\"gjs-sm-sector\">\r\n                  <div class=\"gjs-sm-title\">Editor Status</div>\r\n                  <div class=\"gjs-sm-properties\">\r\n                    <div class=\"status-item\" data-status=\"components\">Loading...</div>\r\n                  </div>\r\n                </div>\r\n              </div>`,\r\n  }\r\n}), []);\r\n\r\n// Debounce function for performance optimizations\r\nfunction debounce<F extends (...args: any[]) => any>(func: F, wait: number): (...args: Parameters<F>) => void {\r\n  let timeoutId: ReturnType<typeof setTimeout> | null = null;\r\n  \r\n  return function(...args: Parameters<F>) {\r\n    if (timeoutId !== null) {\r\n      clearTimeout(timeoutId);\r\n    }\r\n    \r\n    timeoutId = setTimeout(() => {\r\n      func(...args);\r\n      timeoutId = null;\r\n    }, wait);\r\n  };\r\n}\r\n\r\n// Throttle function to limit execution frequency\r\nfunction throttle<F extends (...args: any[]) => any>(func: F, limit: number): (...args: Parameters<F>) => void {\r\n  let inThrottle = false;\r\n  let lastFunc: ReturnType<typeof setTimeout>;\r\n  let lastRan: number;\r\n  \r\n  return function(...args: Parameters<F>) {\r\n    if (!inThrottle) {\r\n      func(...args);\r\n      lastRan = Date.now();\r\n      inThrottle = true;\r\n      \r\n      setTimeout(() => {\r\n        inThrottle = false;\r\n      }, limit);\r\n    } else {\r\n      clearTimeout(lastFunc);\r\n      lastFunc = setTimeout(() => {\r\n        if (Date.now() - lastRan >= limit) {\r\n          func(...args);\r\n          lastRan = Date.now();\r\n        }\r\n      }, limit - (Date.now() - lastRan));\r\n    }\r\n  };\r\n}\r\n\r\n// Worker function for HTML generation (would normally be in separate file)\r\nconst createHTMLGenerationWorker = () => {\r\n  const workerCode = `\r\n    self.onmessage = function(e) {\r\n      const { html, css } = e.data;\r\n      \r\n      try {\r\n        // Combine HTML and CSS\r\n        const fullHtml = \\`<!DOCTYPE html>\r\n        <html>\r\n          <head>\r\n            <meta charset=\"UTF-8\">\r\n            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n            <title>Email Template</title>\r\n            <style>\\${css}</style>\r\n          </head>\r\n          <body>\\${html}</body>\r\n        </html>\\`;\r\n        \r\n        self.postMessage({ html: fullHtml, error: null });\r\n      } catch (error) {\r\n        self.postMessage({ html: null, error: error.message });\r\n      }\r\n    };\r\n  `;\r\n  \r\n  const blob = new Blob([workerCode], { type: 'application/javascript' });\r\n  return new Worker(URL.createObjectURL(blob));\r\n};\r\n\r\n// Main component implementation with memoization\r\nconst HtmlEmailEditorComponent = forwardRef<HtmlEmailEditorRef, HtmlEmailEditorProps>((props, ref) => {\r\n  const { initialHtml = '', onSave, height = '500px' } = props;\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [editor, setEditor] = useState<Editor | null>(null);\r\n  const [isEditorReady, setIsEditorReady] = useState<boolean>(false);\r\n  const [isSaving, setIsSaving] = useState<boolean>(false);\r\n  const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);\r\n  const lastHtmlRef = useRef<string>(initialHtml);\r\n  const [activePanel, setActivePanel] = useState<string | null>(null);\r\n  const [worker, setWorker] = useState<Worker | null>(null);\r\n  \r\n  // Create HTML generation worker once on component mount\r\n  useEffect(() => {\r\n    if (typeof Worker !== 'undefined') {\r\n      const newWorker = createHTMLGenerationWorker();\r\n      setWorker(newWorker);\r\n      \r\n      return () => {\r\n        newWorker.terminate();\r\n      };\r\n    }\r\n  }, []);\r\n  \r\n  // Expose methods to parent through ref\r\n  useImperativeHandle(ref, () => ({\r\n    // Save method - get the HTML from the editor\r\n    save: async () => {\r\n      if (!editor) {\r\n        console.error('[HtmlEmailEditor] Editor not initialized');\r\n        return { html: '' };\r\n      }\r\n      \r\n      try {\r\n        setIsSaving(true);\r\n        \r\n        // Get HTML and CSS from the editor\r\n        const html = editor.getHtml();\r\n        const css = editor.getCss({ avoidProtected: true });\r\n        \r\n        // Generate full HTML\r\n        let finalHtml: string;\r\n        \r\n        // Use worker if available for performance\r\n        if (worker) {\r\n          finalHtml = await new Promise<string>((resolve, reject) => {\r\n            const messageHandler = (e: MessageEvent) => {\r\n              worker.removeEventListener('message', messageHandler);\r\n              if (e.data.error) {\r\n                reject(new Error(e.data.error));\r\n              } else {\r\n                resolve(e.data.html);\r\n              }\r\n            };\r\n            \r\n            worker.addEventListener('message', messageHandler);\r\n            worker.postMessage({ html, css });\r\n          });\r\n        } else {\r\n          // Fallback to synchronous processing\r\n          finalHtml = `<!DOCTYPE html>\r\n          <html>\r\n            <head>\r\n              <meta charset=\"UTF-8\">\r\n              <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n              <title>Email Template</title>\r\n              <style>${css}</style>\r\n            </head>\r\n            <body>${html}</body>\r\n          </html>`;\r\n        }\r\n        \r\n        // Cache the result for future reference\r\n        if (editor.getDirtyCount?.() > 0 || !htmlCache.has(html)) {\r\n          htmlCache.set(html, finalHtml);\r\n        }\r\n        \r\n        // If onSave callback is provided, call it with the HTML\r\n        if (onSave) {\r\n          onSave(finalHtml);\r\n        }\r\n        \r\n        // Update the last saved time\r\n        setLastSavedAt(new Date());\r\n        lastHtmlRef.current = html;\r\n        \r\n        // Clear the dirty state\r\n        if (editor.UndoManager) {\r\n          editor.UndoManager.clear();\r\n        }\r\n        \r\n        // Or set dirty count directly if UndoManager isn't available\r\n        if (typeof editor.getDirtyCount === 'function') {\r\n          // Force clear dirty count by manually tracking it\r\n          (editor as any)._dirtyCount = 0;\r\n        }\r\n        \r\n        console.log(\"[HtmlEmailEditor] Save complete:\", new Date().toISOString());\r\n        return { html: finalHtml };\r\n      } catch (error) {\r\n        console.error('[HtmlEmailEditor] Error saving:', error);\r\n        return { html: '' };\r\n      } finally {\r\n        setIsSaving(false);\r\n      }\r\n    },\r\n    \r\n    // Get the editor instance\r\n    getEditor: () => editor\r\n  }));\r\n  \r\n  // Create a debounced save function\r\n  const debouncedSave = useCallback(\r\n    debounce(() => {\r\n      if (editor && onSave) {\r\n        // Only save if content has changed\r\n        const currentHtml = editor.getHtml();\r\n        if (currentHtml !== lastHtmlRef.current) {\r\n          console.log('[HtmlEmailEditor] Auto-saving...');\r\n          // Call the save method through ref - safely handled with optional chaining\r\n          if (typeof ref === 'function') {\r\n            // Can't directly call save() on a function ref\r\n            console.warn('[HtmlEmailEditor] Function refs cannot be used for auto-save');\r\n          } else if (ref?.current) {\r\n            ref.current.save();\r\n          }\r\n        }\r\n      }\r\n    }, 500), // 500ms debounce\r\n    [editor, onSave, ref]\r\n  );\r\n  \r\n  // Initialize editor when component mounts\r\n  useEffect(() => {\r\n    if (!containerRef.current || editor) return;\r\n    \r\n    // Create editor with cached options\r\n    const gjsEditor = grapesjs.init({\r\n      // Container where the editor will be mounted\r\n      container: containerRef.current,\r\n      \r\n      // Disable default storage manager\r\n      storageManager: false,\r\n      \r\n      // Default HTML when the editor starts\r\n      components: initialHtml,\r\n      \r\n      // Default style when the editor starts\r\n      style: emailBodyStyle,\r\n      \r\n      // Load panels on demand to improve initial load time\r\n      panels: { defaults: [] },\r\n      \r\n      // Disable unused features for better performance\r\n      deviceManager: {\r\n        devices: [\r\n          {\r\n            name: 'Desktop',\r\n            width: '',\r\n          },\r\n          {\r\n            name: 'Tablet',\r\n            width: '768px',\r\n            widthMedia: '992px',\r\n          },\r\n          {\r\n            name: 'Mobile',\r\n            width: '320px',\r\n            widthMedia: '480px',\r\n          },\r\n        ]\r\n      },\r\n      \r\n      // Configure asset manager with optimization\r\n      assetManager: {\r\n        assets: [],\r\n        uploadFile: null, // Disable upload to avoid costly IO\r\n        autoAdd: true,\r\n        embedAsBase64: true, // Inline images as base64 for portability\r\n      },\r\n      \r\n      // Optimize plugin loading\r\n      plugins: [],\r\n      \r\n      // Custom configuration for email compatibility\r\n      canvas: {\r\n        styles: [\r\n          'https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700',\r\n          'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css'\r\n        ],\r\n        scripts: []\r\n      },\r\n    });\r\n    \r\n    // Set editor to state\r\n    setEditor(gjsEditor);\r\n    \r\n    // Initialize blocks only when needed\r\n    gjsEditor.BlockManager.add('text', {\r\n      label: 'Text',\r\n      attributes: { class: 'fa fa-font' },\r\n      content: { \r\n        type: 'text',\r\n        content: '<p>Add your text here</p>',\r\n        style: { padding: '10px' }\r\n      }\r\n    });\r\n    \r\n    gjsEditor.BlockManager.add('image', {\r\n      label: 'Image',\r\n      attributes: { class: 'fa fa-image' },\r\n      select: true,\r\n      content: { \r\n        type: 'image',\r\n        style: { padding: '10px', width: '100%' },\r\n        attributes: { src: 'data:image/svg+xml,%3Csvg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"%3E%3Crect width=\"24\" height=\"24\" fill=\"%23f0f0f0\"/%3E%3Cpath d=\"M12 6.5c-3.04 0-5.5 2.46-5.5 5.5s2.46 5.5 5.5 5.5 5.5-2.46 5.5-5.5-2.46-5.5-5.5-5.5z\" fill=\"%23ddd\"/%3E%3C/svg%3E' }\r\n      }\r\n    });\r\n    \r\n    gjsEditor.BlockManager.add('button', {\r\n      label: 'Button',\r\n      attributes: { class: 'fa fa-square' },\r\n      content: '<a class=\"button\" style=\"display:inline-block;padding:10px 20px;background-color:#4CAF50;color:#ffffff;text-decoration:none;border-radius:4px;text-align:center;font-weight:bold;\">Click me</a>'\r\n    });\r\n    \r\n    // Add style manager panels on demand for better performance\r\n    gjsEditor.Panels.addPanel({\r\n      id: 'panel-switcher',\r\n      visible: true,\r\n      buttons: [\r\n        {\r\n          id: 'show-style',\r\n          label: 'Styles',\r\n          command: 'show-styles',\r\n          active: false\r\n        },\r\n        {\r\n          id: 'show-layers',\r\n          label: 'Layers',\r\n          command: 'show-layers',\r\n          active: false\r\n        },\r\n        {\r\n          id: 'show-blocks',\r\n          label: 'Blocks',\r\n          command: 'show-blocks',\r\n          active: true\r\n        }\r\n      ]\r\n    });\r\n    \r\n    // Add commands to switch between panels\r\n    gjsEditor.Commands.add('show-blocks', {\r\n      run: function(editor) {\r\n        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');\r\n        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');\r\n        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');\r\n        \r\n        if (blocksBtn) blocksBtn.set('active', true);\r\n        if (styleBtn) styleBtn.set('active', false);\r\n        if (layersBtn) layersBtn.set('active', false);\r\n        \r\n        setActivePanel('blocks');\r\n      }\r\n    });\r\n    \r\n    gjsEditor.Commands.add('show-styles', {\r\n      run: function(editor) {\r\n        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');\r\n        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');\r\n        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');\r\n        \r\n        if (blocksBtn) blocksBtn.set('active', false);\r\n        if (styleBtn) styleBtn.set('active', true);\r\n        if (layersBtn) layersBtn.set('active', false);\r\n        \r\n        setActivePanel('styles');\r\n      }\r\n    });\r\n    \r\n    gjsEditor.Commands.add('show-layers', {\r\n      run: function(editor) {\r\n        const blocksBtn = editor.Panels.getButton('panel-switcher', 'show-blocks');\r\n        const styleBtn = editor.Panels.getButton('panel-switcher', 'show-style');\r\n        const layersBtn = editor.Panels.getButton('panel-switcher', 'show-layers');\r\n        \r\n        if (blocksBtn) blocksBtn.set('active', false);\r\n        if (styleBtn) styleBtn.set('active', false);\r\n        if (layersBtn) layersBtn.set('active', true);\r\n        \r\n        setActivePanel('layers');\r\n      }\r\n    });\r\n    \r\n    // Run the blocks command by default\r\n    gjsEditor.runCommand('show-blocks');\r\n    \r\n    // Add change event listener\r\n    const throttledOnChange = throttle(() => {\r\n      debouncedSave();\r\n    }, 300);\r\n    \r\n    gjsEditor.on('change:changesCount', throttledOnChange);\r\n    \r\n    // Add a beforeunload event listener to warn of unsaved changes\r\n    const handleBeforeUnload = (e: BeforeUnloadEvent) => {\r\n      if (gjsEditor.getDirtyCount?.() > 0) {\r\n        e.preventDefault();\r\n        e.returnValue = '';\r\n        return '';\r\n      }\r\n    };\r\n    \r\n    window.addEventListener('beforeunload', handleBeforeUnload);\r\n    \r\n    // Set editor ready\r\n    setIsEditorReady(true);\r\n    \r\n    // Clean up editor when component unmounts\r\n    return () => {\r\n      window.removeEventListener('beforeunload', handleBeforeUnload);\r\n      gjsEditor.off('change:changesCount', throttledOnChange);\r\n      gjsEditor.destroy();\r\n    };\r\n  }, [initialHtml, debouncedSave]);\r\n  \r\n  // Fetch necessary components based on active panel\r\n  const renderActivePanel = () => {\r\n    if (!activePanel || !editor) return null;\r\n    \r\n    switch (activePanel) {\r\n      case 'styles':\r\n        return (\r\n          <div className=\"panel-container styles-container\">\r\n            <div className=\"panel-header\">\r\n              <h3>Style Manager</h3>\r\n            </div>\r\n            <div id=\"style-manager\" />\r\n          </div>\r\n        );\r\n      case 'layers':\r\n        return (\r\n          <div className=\"panel-container layers-container\">\r\n            <div className=\"panel-header\">\r\n              <h3>Layers</h3>\r\n            </div>\r\n            <div id=\"layers-manager\" />\r\n          </div>\r\n        );\r\n      case 'blocks':\r\n      default:\r\n        return (\r\n          <div className=\"panel-container blocks-container\">\r\n            <div className=\"panel-header\">\r\n              <h3>Blocks</h3>\r\n            </div>\r\n            <div id=\"blocks-manager\" />\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div \r\n      className=\"html-email-editor-container\"\r\n      style={{ \r\n        display: 'flex', \r\n        flexDirection: 'column',\r\n        height: typeof height === 'number' ? `${height}px` : height,\r\n        border: '1px solid #ddd',\r\n        borderRadius: '4px',\r\n        overflow: 'hidden'\r\n      }}\r\n    >\r\n      {/* Editor Header */}\r\n      <div \r\n        className=\"editor-header\" \r\n        style={{ \r\n          display: 'flex',\r\n          justifyContent: 'space-between',\r\n          alignItems: 'center',\r\n          padding: '10px',\r\n          borderBottom: '1px solid #ddd',\r\n          backgroundColor: '#f5f5f5'\r\n        }}\r\n      >\r\n        <div className=\"editor-title\">\r\n          <h2 style={{ margin: 0, fontSize: '16px' }}>HTML Email Editor</h2>\r\n        </div>\r\n        \r\n        <div className=\"editor-actions\">\r\n          <button\r\n            onClick={() => {\r\n              if (typeof ref === 'function') {\r\n                console.warn('[HtmlEmailEditor] Function refs cannot be used for manual save');\r\n              } else if (ref?.current) {\r\n                ref.current.save();\r\n              }\r\n            }}\r\n            disabled={isSaving || !isEditorReady}\r\n            style={{\r\n              padding: '8px 12px',\r\n              backgroundColor: '#4CAF50',\r\n              color: 'white',\r\n              border: 'none',\r\n              borderRadius: '4px',\r\n              cursor: isSaving || !isEditorReady ? 'not-allowed' : 'pointer',\r\n              opacity: isSaving || !isEditorReady ? 0.7 : 1\r\n            }}\r\n          >\r\n            {isSaving ? 'Saving...' : 'Save'}\r\n          </button>\r\n          \r\n          {lastSavedAt && (\r\n            <span style={{ marginLeft: '10px', fontSize: '12px', color: '#666' }}>\r\n              Last saved: {lastSavedAt.toLocaleTimeString()}\r\n            </span>\r\n          )}\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Editor Content */}\r\n      <div \r\n        className=\"editor-content\"\r\n        style={{ \r\n          display: 'flex',\r\n          flexGrow: 1,\r\n          overflow: 'hidden'\r\n        }}\r\n      >\r\n        {/* Side Panel */}\r\n        <div \r\n          className=\"editor-sidebar\"\r\n          style={{ \r\n            width: '250px',\r\n            borderRight: '1px solid #ddd',\r\n            display: 'flex',\r\n            flexDirection: 'column',\r\n            backgroundColor: '#f9f9f9'\r\n          }}\r\n        >\r\n          {renderActivePanel()}\r\n        </div>\r\n        \r\n        {/* Main Editor Canvas */}\r\n        <div \r\n          className=\"editor-canvas\"\r\n          style={{ \r\n            flexGrow: 1,\r\n            position: 'relative'\r\n          }}\r\n        >\r\n          {/* Render loading state if editor is not ready */}\r\n          {!isEditorReady && (\r\n            <div \r\n              style={{\r\n                position: 'absolute',\r\n                top: 0,\r\n                left: 0,\r\n                right: 0,\r\n                bottom: 0,\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                backgroundColor: 'rgba(255, 255, 255, 0.7)',\r\n                zIndex: 10\r\n              }}\r\n            >\r\n              <div>Loading editor...</div>\r\n            </div>\r\n          )}\r\n          \r\n          {/* GrapesJS will be mounted in this div */}\r\n          <div \r\n            ref={containerRef} \r\n            style={{ \r\n              height: '100%',\r\n              overflow: 'auto'\r\n            }}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n});\r\n\r\n// Apply memo to avoid unnecessary re-renders\r\nconst HtmlEmailEditor = memo(HtmlEmailEditorComponent);\r\n\r\n// Add display name for better debugging\r\nHtmlEmailEditor.displayName = 'HtmlEmailEditor';\r\n\r\nexport default HtmlEmailEditor; "], "mappings": ";;AAAA,OAAO,kCAAkC,CAAC,CAAC;;AAE3C;AACA;AACA;AACA,OAAOA,KAAK,IACVC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,mBAAmB,EACnBC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,OAAO;AAEd,OAAOC,QAAQ,MAAkB,UAAU;;AAE3C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYA;AACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAiB,CAAC;;AAE3C;AACA,MAAMC,cAAc,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMC,cAAc,GAAGT,OAAO,CAAC,MAAM,CACnC;EACEU,EAAE,EAAE,MAAM;EACVC,KAAK,EAAE,MAAM;EACbC,UAAU,EAAE;IACVC,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,SAAS;EACnBC,OAAO,EAAE;IACPC,IAAI,EAAE,MAAM;IACZD,OAAO,EAAE,oGAAoG;IAC7GF,KAAK,EAAE;MAAEI,OAAO,EAAE;IAAO,CAAC;IAC1BC,cAAc,EAAE;EAClB;AACF,CAAC,EACD;EACET,EAAE,EAAE,OAAO;EACXC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE;IACVC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,SAAS;EACnBK,MAAM,EAAE,IAAI;EAAE;EACdJ,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbH,KAAK,EAAE;MAAEO,KAAK,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAO,CAAC;IACvCH,cAAc,EAAE;EAClB;AACF,CAAC,EACD;EACET,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE;IACVC,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,SAAS;EACnBC,OAAO,EAAE;AACX,CAAC,EACD;EACEN,EAAE,EAAE,SAAS;EACbC,KAAK,EAAE,SAAS;EAChBC,UAAU,EAAE;IACVC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,SAAS;EACnBC,OAAO,EAAE;AACX,CAAC,EACD;EACEN,EAAE,EAAE,QAAQ;EACZC,KAAK,EAAE,QAAQ;EACfC,UAAU,EAAE;IACVC,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE;AACX,CAAC,EACD;EACEN,EAAE,EAAE,aAAa;EACjBC,KAAK,EAAE,WAAW;EAClBC,UAAU,EAAE;IACVC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE;EACT,CAAC;EACDC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE,CAAC,CACF,EAAE,EAAE,CAAC;;AAEN;AACA,MAAMO,SAAS,GAAGvB,OAAO,CAAC,OAAO;EAC/B;EACAwB,YAAY,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDC,YAAY,EAAE;IACZD,QAAQ,EAAE;EACZ,CAAC;EACD;EACA,eAAe,EAAE;IACfE,EAAE,EAAE,aAAa;IACjBF,QAAQ,EAAE,aAAa;IACvBT,OAAO,EAAE;AACb;AACA;AACA;AACA;AACA;AACA;AACA;EACE;AACF,CAAC,CAAC,EAAE,EAAE,CAAC;;AAEP;AACA,SAASY,QAAQA,CAAoCC,IAAO,EAAEC,IAAY,EAAoC;EAC5G,IAAIC,SAA+C,GAAG,IAAI;EAE1D,OAAO,UAAS,GAAGC,IAAmB,EAAE;IACtC,IAAID,SAAS,KAAK,IAAI,EAAE;MACtBE,YAAY,CAACF,SAAS,CAAC;IACzB;IAEAA,SAAS,GAAGG,UAAU,CAAC,MAAM;MAC3BL,IAAI,CAAC,GAAGG,IAAI,CAAC;MACbD,SAAS,GAAG,IAAI;IAClB,CAAC,EAAED,IAAI,CAAC;EACV,CAAC;AACH;;AAEA;AACA,SAASK,QAAQA,CAAoCN,IAAO,EAAEO,KAAa,EAAoC;EAC7G,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAuC;EAC3C,IAAIC,OAAe;EAEnB,OAAO,UAAS,GAAGP,IAAmB,EAAE;IACtC,IAAI,CAACK,UAAU,EAAE;MACfR,IAAI,CAAC,GAAGG,IAAI,CAAC;MACbO,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MACpBJ,UAAU,GAAG,IAAI;MAEjBH,UAAU,CAAC,MAAM;QACfG,UAAU,GAAG,KAAK;MACpB,CAAC,EAAED,KAAK,CAAC;IACX,CAAC,MAAM;MACLH,YAAY,CAACK,QAAQ,CAAC;MACtBA,QAAQ,GAAGJ,UAAU,CAAC,MAAM;QAC1B,IAAIM,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,OAAO,IAAIH,KAAK,EAAE;UACjCP,IAAI,CAAC,GAAGG,IAAI,CAAC;UACbO,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB;MACF,CAAC,EAAEL,KAAK,IAAII,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,OAAO,CAAC,CAAC;IACpC;EACF,CAAC;AACH;;AAEA;AACA,MAAMG,0BAA0B,GAAGA,CAAA,KAAM;EACvC,MAAMC,UAAU,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;EAED,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACF,UAAU,CAAC,EAAE;IAAE1B,IAAI,EAAE;EAAyB,CAAC,CAAC;EACvE,OAAO,IAAI6B,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CAAC;AAC9C,CAAC;;AAED;AACA,MAAMK,wBAAwB,gBAAAC,EAAA,cAAGvD,UAAU,CAAAwD,EAAA,GAAAD,EAAA,CAA2C,CAACE,KAAK,EAAEC,GAAG,KAAK;EAAAH,EAAA;EACpG,MAAM;IAAEI,WAAW,GAAG,EAAE;IAAEC,MAAM;IAAEC,MAAM,GAAG;EAAQ,CAAC,GAAGJ,KAAK;EAC5D,MAAMK,YAAY,GAAGxD,MAAM,CAAiB,IAAI,CAAC;EACjD,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAGzD,QAAQ,CAAgB,IAAI,CAAC;EACzD,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAU,KAAK,CAAC;EACxD,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAMgE,WAAW,GAAGjE,MAAM,CAASqD,WAAW,CAAC;EAC/C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAgB,IAAI,CAAC;EACnE,MAAM,CAACmE,MAAM,EAAEC,SAAS,CAAC,GAAGpE,QAAQ,CAAgB,IAAI,CAAC;;EAEzD;EACAJ,SAAS,CAAC,MAAM;IACd,IAAI,OAAOgD,MAAM,KAAK,WAAW,EAAE;MACjC,MAAMyB,SAAS,GAAG7B,0BAA0B,CAAC,CAAC;MAC9C4B,SAAS,CAACC,SAAS,CAAC;MAEpB,OAAO,MAAM;QACXA,SAAS,CAACC,SAAS,CAAC,CAAC;MACvB,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzE,mBAAmB,CAACsD,GAAG,EAAE,OAAO;IAC9B;IACAoB,IAAI,EAAE,MAAAA,CAAA,KAAY;MAChB,IAAI,CAACf,MAAM,EAAE;QACXgB,OAAO,CAACC,KAAK,CAAC,0CAA0C,CAAC;QACzD,OAAO;UAAEC,IAAI,EAAE;QAAG,CAAC;MACrB;MAEA,IAAI;QAAA,IAAAC,qBAAA;QACFd,WAAW,CAAC,IAAI,CAAC;;QAEjB;QACA,MAAMa,IAAI,GAAGlB,MAAM,CAACoB,OAAO,CAAC,CAAC;QAC7B,MAAMC,GAAG,GAAGrB,MAAM,CAACsB,MAAM,CAAC;UAAEC,cAAc,EAAE;QAAK,CAAC,CAAC;;QAEnD;QACA,IAAIC,SAAiB;;QAErB;QACA,IAAIb,MAAM,EAAE;UACVa,SAAS,GAAG,MAAM,IAAIC,OAAO,CAAS,CAACC,OAAO,EAAEC,MAAM,KAAK;YACzD,MAAMC,cAAc,GAAIC,CAAe,IAAK;cAC1ClB,MAAM,CAACmB,mBAAmB,CAAC,SAAS,EAAEF,cAAc,CAAC;cACrD,IAAIC,CAAC,CAACE,IAAI,CAACd,KAAK,EAAE;gBAChBU,MAAM,CAAC,IAAIK,KAAK,CAACH,CAAC,CAACE,IAAI,CAACd,KAAK,CAAC,CAAC;cACjC,CAAC,MAAM;gBACLS,OAAO,CAACG,CAAC,CAACE,IAAI,CAACb,IAAI,CAAC;cACtB;YACF,CAAC;YAEDP,MAAM,CAACsB,gBAAgB,CAAC,SAAS,EAAEL,cAAc,CAAC;YAClDjB,MAAM,CAACuB,WAAW,CAAC;cAAEhB,IAAI;cAAEG;YAAI,CAAC,CAAC;UACnC,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAG,SAAS,GAAG;AACtB;AACA;AACA;AACA;AACA;AACA,uBAAuBH,GAAG;AAC1B;AACA,oBAAoBH,IAAI;AACxB,kBAAkB;QACV;;QAEA;QACA,IAAI,EAAAC,qBAAA,GAAAnB,MAAM,CAACmC,aAAa,cAAAhB,qBAAA,uBAApBA,qBAAA,CAAAiB,IAAA,CAAApC,MAAuB,CAAC,IAAG,CAAC,IAAI,CAACpD,SAAS,CAACyF,GAAG,CAACnB,IAAI,CAAC,EAAE;UACxDtE,SAAS,CAAC0F,GAAG,CAACpB,IAAI,EAAEM,SAAS,CAAC;QAChC;;QAEA;QACA,IAAI3B,MAAM,EAAE;UACVA,MAAM,CAAC2B,SAAS,CAAC;QACnB;;QAEA;QACAjB,cAAc,CAAC,IAAIzB,IAAI,CAAC,CAAC,CAAC;QAC1B0B,WAAW,CAAC+B,OAAO,GAAGrB,IAAI;;QAE1B;QACA,IAAIlB,MAAM,CAACwC,WAAW,EAAE;UACtBxC,MAAM,CAACwC,WAAW,CAACC,KAAK,CAAC,CAAC;QAC5B;;QAEA;QACA,IAAI,OAAOzC,MAAM,CAACmC,aAAa,KAAK,UAAU,EAAE;UAC9C;UACCnC,MAAM,CAAS0C,WAAW,GAAG,CAAC;QACjC;QAEA1B,OAAO,CAAC2B,GAAG,CAAC,kCAAkC,EAAE,IAAI7D,IAAI,CAAC,CAAC,CAAC8D,WAAW,CAAC,CAAC,CAAC;QACzE,OAAO;UAAE1B,IAAI,EAAEM;QAAU,CAAC;MAC5B,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,OAAO;UAAEC,IAAI,EAAE;QAAG,CAAC;MACrB,CAAC,SAAS;QACRb,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC;IAED;IACAwC,SAAS,EAAEA,CAAA,KAAM7C;EACnB,CAAC,CAAC,CAAC;;EAEH;EACA,MAAM8C,aAAa,GAAG3G,WAAW,CAC/B+B,QAAQ,CAAC,MAAM;IACb,IAAI8B,MAAM,IAAIH,MAAM,EAAE;MACpB;MACA,MAAMkD,WAAW,GAAG/C,MAAM,CAACoB,OAAO,CAAC,CAAC;MACpC,IAAI2B,WAAW,KAAKvC,WAAW,CAAC+B,OAAO,EAAE;QACvCvB,OAAO,CAAC2B,GAAG,CAAC,kCAAkC,CAAC;QAC/C;QACA,IAAI,OAAOhD,GAAG,KAAK,UAAU,EAAE;UAC7B;UACAqB,OAAO,CAACgC,IAAI,CAAC,8DAA8D,CAAC;QAC9E,CAAC,MAAM,IAAIrD,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAE4C,OAAO,EAAE;UACvB5C,GAAG,CAAC4C,OAAO,CAACxB,IAAI,CAAC,CAAC;QACpB;MACF;IACF;EACF,CAAC,EAAE,GAAG,CAAC;EAAE;EACT,CAACf,MAAM,EAAEH,MAAM,EAAEF,GAAG,CACtB,CAAC;;EAED;EACAvD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2D,YAAY,CAACwC,OAAO,IAAIvC,MAAM,EAAE;;IAErC;IACA,MAAMiD,SAAS,GAAGxG,QAAQ,CAACyG,IAAI,CAAC;MAC9B;MACAC,SAAS,EAAEpD,YAAY,CAACwC,OAAO;MAE/B;MACAa,cAAc,EAAE,KAAK;MAErB;MACAC,UAAU,EAAEzD,WAAW;MAEvB;MACAxC,KAAK,EAAEN,cAAc;MAErB;MACAwG,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAG,CAAC;MAExB;MACAC,aAAa,EAAE;QACbC,OAAO,EAAE,CACP;UACEC,IAAI,EAAE,SAAS;UACf9F,KAAK,EAAE;QACT,CAAC,EACD;UACE8F,IAAI,EAAE,QAAQ;UACd9F,KAAK,EAAE,OAAO;UACd+F,UAAU,EAAE;QACd,CAAC,EACD;UACED,IAAI,EAAE,QAAQ;UACd9F,KAAK,EAAE,OAAO;UACd+F,UAAU,EAAE;QACd,CAAC;MAEL,CAAC;MAED;MACAC,YAAY,EAAE;QACZC,MAAM,EAAE,EAAE;QACVC,UAAU,EAAE,IAAI;QAAE;QAClBC,OAAO,EAAE,IAAI;QACbC,aAAa,EAAE,IAAI,CAAE;MACvB,CAAC;MAED;MACAC,OAAO,EAAE,EAAE;MAEX;MACAC,MAAM,EAAE;QACNC,MAAM,EAAE,CACN,mEAAmE,EACnE,4EAA4E,CAC7E;QACDC,OAAO,EAAE;MACX;IACF,CAAC,CAAC;;IAEF;IACAnE,SAAS,CAACgD,SAAS,CAAC;;IAEpB;IACAA,SAAS,CAACoB,YAAY,CAACC,GAAG,CAAC,MAAM,EAAE;MACjCrH,KAAK,EAAE,MAAM;MACbC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAa,CAAC;MACnCG,OAAO,EAAE;QACPC,IAAI,EAAE,MAAM;QACZD,OAAO,EAAE,2BAA2B;QACpCF,KAAK,EAAE;UAAEI,OAAO,EAAE;QAAO;MAC3B;IACF,CAAC,CAAC;IAEFyF,SAAS,CAACoB,YAAY,CAACC,GAAG,CAAC,OAAO,EAAE;MAClCrH,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC;MACpCO,MAAM,EAAE,IAAI;MACZJ,OAAO,EAAE;QACPC,IAAI,EAAE,OAAO;QACbH,KAAK,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAO,CAAC;QACzCV,UAAU,EAAE;UAAEqH,GAAG,EAAE;QAAqQ;MAC1R;IACF,CAAC,CAAC;IAEFtB,SAAS,CAACoB,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAE;MACnCrH,KAAK,EAAE,QAAQ;MACfC,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAe,CAAC;MACrCG,OAAO,EAAE;IACX,CAAC,CAAC;;IAEF;IACA2F,SAAS,CAACuB,MAAM,CAACC,QAAQ,CAAC;MACxBzH,EAAE,EAAE,gBAAgB;MACpB0H,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,CACP;QACE3H,EAAE,EAAE,YAAY;QAChBC,KAAK,EAAE,QAAQ;QACf2H,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACE7H,EAAE,EAAE,aAAa;QACjBC,KAAK,EAAE,QAAQ;QACf2H,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE;MACV,CAAC,EACD;QACE7H,EAAE,EAAE,aAAa;QACjBC,KAAK,EAAE,QAAQ;QACf2H,OAAO,EAAE,aAAa;QACtBC,MAAM,EAAE;MACV,CAAC;IAEL,CAAC,CAAC;;IAEF;IACA5B,SAAS,CAAC6B,QAAQ,CAACR,GAAG,CAAC,aAAa,EAAE;MACpCS,GAAG,EAAE,SAAAA,CAAS/E,MAAM,EAAE;QACpB,MAAMgF,SAAS,GAAGhF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAC1E,MAAMC,QAAQ,GAAGlF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,YAAY,CAAC;QACxE,MAAME,SAAS,GAAGnF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAE1E,IAAID,SAAS,EAAEA,SAAS,CAAC1C,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;QAC5C,IAAI4C,QAAQ,EAAEA,QAAQ,CAAC5C,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;QAC3C,IAAI6C,SAAS,EAAEA,SAAS,CAAC7C,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;QAE7C5B,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC;IAEFuC,SAAS,CAAC6B,QAAQ,CAACR,GAAG,CAAC,aAAa,EAAE;MACpCS,GAAG,EAAE,SAAAA,CAAS/E,MAAM,EAAE;QACpB,MAAMgF,SAAS,GAAGhF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAC1E,MAAMC,QAAQ,GAAGlF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,YAAY,CAAC;QACxE,MAAME,SAAS,GAAGnF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAE1E,IAAID,SAAS,EAAEA,SAAS,CAAC1C,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;QAC7C,IAAI4C,QAAQ,EAAEA,QAAQ,CAAC5C,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;QAC1C,IAAI6C,SAAS,EAAEA,SAAS,CAAC7C,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;QAE7C5B,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC;IAEFuC,SAAS,CAAC6B,QAAQ,CAACR,GAAG,CAAC,aAAa,EAAE;MACpCS,GAAG,EAAE,SAAAA,CAAS/E,MAAM,EAAE;QACpB,MAAMgF,SAAS,GAAGhF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAC1E,MAAMC,QAAQ,GAAGlF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,YAAY,CAAC;QACxE,MAAME,SAAS,GAAGnF,MAAM,CAACwE,MAAM,CAACS,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC;QAE1E,IAAID,SAAS,EAAEA,SAAS,CAAC1C,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;QAC7C,IAAI4C,QAAQ,EAAEA,QAAQ,CAAC5C,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;QAC3C,IAAI6C,SAAS,EAAEA,SAAS,CAAC7C,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;QAE5C5B,cAAc,CAAC,QAAQ,CAAC;MAC1B;IACF,CAAC,CAAC;;IAEF;IACAuC,SAAS,CAACmC,UAAU,CAAC,aAAa,CAAC;;IAEnC;IACA,MAAMC,iBAAiB,GAAG5G,QAAQ,CAAC,MAAM;MACvCqE,aAAa,CAAC,CAAC;IACjB,CAAC,EAAE,GAAG,CAAC;IAEPG,SAAS,CAACqC,EAAE,CAAC,qBAAqB,EAAED,iBAAiB,CAAC;;IAEtD;IACA,MAAME,kBAAkB,GAAI1D,CAAoB,IAAK;MAAA,IAAA2D,qBAAA;MACnD,IAAI,EAAAA,qBAAA,GAAAvC,SAAS,CAACd,aAAa,cAAAqD,qBAAA,uBAAvBA,qBAAA,CAAApD,IAAA,CAAAa,SAA0B,CAAC,IAAG,CAAC,EAAE;QACnCpB,CAAC,CAAC4D,cAAc,CAAC,CAAC;QAClB5D,CAAC,CAAC6D,WAAW,GAAG,EAAE;QAClB,OAAO,EAAE;MACX;IACF,CAAC;IAEDC,MAAM,CAAC1D,gBAAgB,CAAC,cAAc,EAAEsD,kBAAkB,CAAC;;IAE3D;IACApF,gBAAgB,CAAC,IAAI,CAAC;;IAEtB;IACA,OAAO,MAAM;MACXwF,MAAM,CAAC7D,mBAAmB,CAAC,cAAc,EAAEyD,kBAAkB,CAAC;MAC9DtC,SAAS,CAAC2C,GAAG,CAAC,qBAAqB,EAAEP,iBAAiB,CAAC;MACvDpC,SAAS,CAAC4C,OAAO,CAAC,CAAC;IACrB,CAAC;EACH,CAAC,EAAE,CAACjG,WAAW,EAAEkD,aAAa,CAAC,CAAC;;EAEhC;EACA,MAAMgD,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACrF,WAAW,IAAI,CAACT,MAAM,EAAE,OAAO,IAAI;IAExC,QAAQS,WAAW;MACjB,KAAK,QAAQ;QACX,oBACE9D,OAAA;UAAKoJ,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CrJ,OAAA;YAAKoJ,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BrJ,OAAA;cAAAqJ,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACNzJ,OAAA;YAAKK,EAAE,EAAC;UAAe;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAEV,KAAK,QAAQ;QACX,oBACEzJ,OAAA;UAAKoJ,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CrJ,OAAA;YAAKoJ,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BrJ,OAAA;cAAAqJ,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACNzJ,OAAA;YAAKK,EAAE,EAAC;UAAgB;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAEV,KAAK,QAAQ;MACb;QACE,oBACEzJ,OAAA;UAAKoJ,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CrJ,OAAA;YAAKoJ,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BrJ,OAAA;cAAAqJ,QAAA,EAAI;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACNzJ,OAAA;YAAKK,EAAE,EAAC;UAAgB;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;IAEZ;EACF,CAAC;EAED,oBACEzJ,OAAA;IACEoJ,SAAS,EAAC,6BAA6B;IACvC3I,KAAK,EAAE;MACLiJ,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBxG,MAAM,EAAE,OAAOA,MAAM,KAAK,QAAQ,GAAG,GAAGA,MAAM,IAAI,GAAGA,MAAM;MAC3DyG,MAAM,EAAE,gBAAgB;MACxBC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACZ,CAAE;IAAAT,QAAA,gBAGFrJ,OAAA;MACEoJ,SAAS,EAAC,eAAe;MACzB3I,KAAK,EAAE;QACLiJ,OAAO,EAAE,MAAM;QACfK,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBnJ,OAAO,EAAE,MAAM;QACfoJ,YAAY,EAAE,gBAAgB;QAC9BC,eAAe,EAAE;MACnB,CAAE;MAAAb,QAAA,gBAEFrJ,OAAA;QAAKoJ,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BrJ,OAAA;UAAIS,KAAK,EAAE;YAAE0J,MAAM,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAO,CAAE;UAAAf,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAENzJ,OAAA;QAAKoJ,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BrJ,OAAA;UACEqK,OAAO,EAAEA,CAAA,KAAM;YACb,IAAI,OAAOrH,GAAG,KAAK,UAAU,EAAE;cAC7BqB,OAAO,CAACgC,IAAI,CAAC,gEAAgE,CAAC;YAChF,CAAC,MAAM,IAAIrD,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAE4C,OAAO,EAAE;cACvB5C,GAAG,CAAC4C,OAAO,CAACxB,IAAI,CAAC,CAAC;YACpB;UACF,CAAE;UACFkG,QAAQ,EAAE7G,QAAQ,IAAI,CAACF,aAAc;UACrC9C,KAAK,EAAE;YACLI,OAAO,EAAE,UAAU;YACnBqJ,eAAe,EAAE,SAAS;YAC1BlJ,KAAK,EAAE,OAAO;YACd4I,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBU,MAAM,EAAE9G,QAAQ,IAAI,CAACF,aAAa,GAAG,aAAa,GAAG,SAAS;YAC9DiH,OAAO,EAAE/G,QAAQ,IAAI,CAACF,aAAa,GAAG,GAAG,GAAG;UAC9C,CAAE;UAAA8F,QAAA,EAED5F,QAAQ,GAAG,WAAW,GAAG;QAAM;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,EAER9F,WAAW,iBACV3D,OAAA;UAAMS,KAAK,EAAE;YAAEgK,UAAU,EAAE,MAAM;YAAEL,QAAQ,EAAE,MAAM;YAAEpJ,KAAK,EAAE;UAAO,CAAE;UAAAqI,QAAA,GAAC,cACxD,EAAC1F,WAAW,CAAC+G,kBAAkB,CAAC,CAAC;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzJ,OAAA;MACEoJ,SAAS,EAAC,gBAAgB;MAC1B3I,KAAK,EAAE;QACLiJ,OAAO,EAAE,MAAM;QACfiB,QAAQ,EAAE,CAAC;QACXb,QAAQ,EAAE;MACZ,CAAE;MAAAT,QAAA,gBAGFrJ,OAAA;QACEoJ,SAAS,EAAC,gBAAgB;QAC1B3I,KAAK,EAAE;UACLQ,KAAK,EAAE,OAAO;UACd2J,WAAW,EAAE,gBAAgB;UAC7BlB,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,eAAe,EAAE;QACnB,CAAE;QAAAb,QAAA,EAEDF,iBAAiB,CAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGNzJ,OAAA;QACEoJ,SAAS,EAAC,eAAe;QACzB3I,KAAK,EAAE;UACLkK,QAAQ,EAAE,CAAC;UACXE,QAAQ,EAAE;QACZ,CAAE;QAAAxB,QAAA,GAGD,CAAC9F,aAAa,iBACbvD,OAAA;UACES,KAAK,EAAE;YACLoK,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTvB,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBD,cAAc,EAAE,QAAQ;YACxBG,eAAe,EAAE,0BAA0B;YAC3CgB,MAAM,EAAE;UACV,CAAE;UAAA7B,QAAA,eAEFrJ,OAAA;YAAAqJ,QAAA,EAAK;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CACN,eAGDzJ,OAAA;UACEgD,GAAG,EAAEI,YAAa;UAClB3C,KAAK,EAAE;YACL0C,MAAM,EAAE,MAAM;YACd2G,QAAQ,EAAE;UACZ;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC,kCAAC;;AAEF;AAAA0B,GAAA,GAreMvI,wBAAwB;AAse9B,MAAMwI,eAAe,gBAAG7L,IAAI,CAACqD,wBAAwB,CAAC;;AAEtD;AAAAyI,GAAA,GAFMD,eAAe;AAGrBA,eAAe,CAACE,WAAW,GAAG,iBAAiB;AAE/C,eAAeF,eAAe;AAAC,IAAAtI,EAAA,EAAAqI,GAAA,EAAAE,GAAA;AAAAE,YAAA,CAAAzI,EAAA;AAAAyI,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}