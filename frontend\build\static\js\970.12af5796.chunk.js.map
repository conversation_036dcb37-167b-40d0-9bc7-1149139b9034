{"version": 3, "file": "static/js/970.12af5796.chunk.js", "mappings": "gMA8BA,MA6OA,EA7OgCA,KAC9B,MAAOC,EAASC,IAAcC,EAAAA,EAAAA,WAAkB,IACzCC,EAAgBC,IAAqBF,EAAAA,EAAAA,WAAkB,IACvDG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAC3CK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAiB,KAClDO,EAAYC,IAAiBR,EAAAA,EAAAA,UAAwB,OACrDS,EAAaC,IAAkBV,EAAAA,EAAAA,UAA0C,WACzEW,EAAgBC,IAAqBZ,EAAAA,EAAAA,UAA+B,KAI3Ea,EAAAA,EAAAA,YAAU,KACoBC,WAC1BZ,GAAkB,GAClBE,EAAS,MACT,IACE,MAAMW,QAAiBC,EAAAA,GAAqBC,oBACxCF,EAASG,QACXN,EAAkBG,EAASI,MAEzBf,EAAS,iCAEf,CAAE,MAAOgB,GACPhB,EAASgB,EAAIC,SAAW,kCACxBC,QAAQnB,MAAM,kCAAmCiB,EACnD,CAAC,QACClB,GAAkB,EACpB,GAEFqB,EAAqB,GACpB,IAiEH,OAEIC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SAAC,sBAE1DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,kFAIxCvB,IACCwB,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,KAAK,QAAQR,QAASlB,EAAO2B,QAASA,IAAM1B,EAAS,MAAOqB,UAAU,UAG/ED,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAGpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,uBACrFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAE5BF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOK,QAAQ,eAAeP,UAAU,UAASC,SAAC,wBAClDC,EAAAA,EAAAA,KAAA,YACEM,GAAG,eACHC,KAAK,eACLC,KAAM,GACNC,MAAO/B,EACPgC,SAAWC,GAAwChC,EAAgBgC,EAAEC,OAAOH,OAC5EI,YAAY,2DACZf,UAAU,0NAKdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,qDAAoDC,SAAC,iBAGtEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oFAAmFC,SAC/F,CAAE,SAAU,UAAW,UAAWe,KAAKC,IACnClB,EAAAA,EAAAA,MAAA,SAAkBQ,QAASU,EAAMjB,UAAW,0GAAyGhB,IAAgBiC,EAAO,wBAA0B,yCAA0ChB,SAAA,EAC9OC,EAAAA,EAAAA,KAAA,SACEM,GAAIS,EACJR,KAAK,aACLL,KAAK,QACLc,QAASlC,IAAgBiC,EACzBL,SAAUA,IAAM3B,EAAegC,GAC/BjB,UAAU,aAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,aAAYC,SAAEgB,MATpBA,WAgBrBlB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAClCC,EAAAA,EAAAA,KAACiB,EAAAA,EAAM,CACLC,QAzHY/B,UAC5B,GAAKT,EAAayC,OAAlB,CAIA/C,GAAW,GACXK,EAAS,MACTI,EAAc,MACd,IACE,MAAMO,QAAiBC,EAAAA,GAAqB+B,gBAAgB1C,EAAcI,GACtEM,EAASG,SACXV,EAAcO,EAASI,KAAKZ,YAEvBI,EAAeqC,MAAKC,GAAQA,EAAKhB,KAAOlB,EAASI,KAAKc,MACzDrB,GAAkBsC,GAAQ,CAACnC,EAASI,QAAS+B,MAG/C9C,EAASW,EAASM,SAAW,6BAEjC,CAAE,MAAOD,GACPhB,EAASgB,EAAIC,SAAW,6CAC1B,CAAC,QACCtB,GAAW,EACb,CAnBA,MAFEK,EAAS,4BAqBX,EAmGgB+C,SAAUrD,IAAYO,EAAayC,OACnCrB,UAAU,SAAQC,SAEjB5B,EAAU,gBAAkB,sBAE/B6B,EAAAA,EAAAA,KAACiB,EAAAA,EAAM,CACLC,QAtGU/B,UAC1B,IAAKT,EAAayC,OAEhB,YADA1C,EAAS,4CAGX,MAAMgD,EAAQC,OAAO,wCACrB,GAAKD,GAAU,eAAeE,KAAKF,GAAnC,CAIArD,GAAW,GACXK,EAAS,MACT,IACE,MAAMW,QAAiBC,EAAAA,GAAqBuC,cAAcH,EAAO/C,GAC7DU,EAASG,QACXsC,MAAM,mCAAmCJ,KAEzChD,EAASW,EAASM,SAAW,4BAEjC,CAAE,MAAOD,GACPhB,EAASgB,EAAIC,SAAW,6CAC1B,CAAC,QACCtB,GAAW,EACb,CAdA,MAFIyD,MAAM,kCAgBV,EAgFgBL,SAAUrD,IAAYO,EAAayC,OACnCW,QAAQ,YACRhC,UAAU,SAAQC,SACnB,8BAQNF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACFC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,oBACnFzB,GACE0B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,uBAC1B,IAA1Bf,EAAe+C,QAChB/B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,6BAErDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oDAAmDC,SAC9Df,EAAe8B,KAAKQ,IACnBtB,EAAAA,EAAAA,KAAA,MAAkBF,UAAU,yEAAyEoB,QAASA,KAAMc,OAhGxIrD,GAD6BsD,EAiGiIX,GAhGlIY,SAC5BrD,EAAcoD,EAAYrD,iBAC1BG,EAAekD,EAAYE,YAHEF,KAiGuI,EAAAlC,UAC7IF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCAAwCC,SAAEuB,EAAKa,cAC5DnC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8BAA6BC,SAAE,IAAIqC,KAAKd,EAAKe,WAAWC,uBAHnEhB,EAAKhB,eAc5BT,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,gBAAeC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,uEAAsEC,SAAA,CAAC,YAAUjB,EAAY,QAC3Ge,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iEAAgEC,SAAA,CAAC,IAC3EnB,GACGoB,EAAAA,EAAAA,KAAA,UAEIuC,MAAO,CACHC,MAAuB,WAAhB1D,EAA2B,QAAU,QAC5C2D,OAAwB,WAAhB3D,EAA2B,QAAU,QAC7C4D,OAAQ,kBACRC,aAAc,OACdC,gBAAiB,SAErBC,IAAKjE,EACLkE,MAAO,oBAAoBhE,IAC3BiE,QAAQ,oCAEZ5E,IAAYS,GACXiB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC5CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0FAA8F,4BAIlHD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC5CF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,uDAAqDjB,EAAY,QAEnEkB,EAAAA,EAAAA,KAAA,OAAKgD,MAAM,6BAA6BlD,UAAU,uCAAuCmD,KAAK,OAAOC,QAAQ,YAAYC,OAAO,eAAeC,YAAa,EAAErD,UAC5JC,EAAAA,EAAAA,KAAA,QAAMqD,cAAc,QAAQC,eAAe,QAAQC,EAAE,iGAS5E,C", "sources": ["pages/MobilePreview.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\n// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport { mobilePreviewService } from '../services'; // Corrected import path\n\n// --- Interfaces ---\n\ninterface Template {\n    id: string;\n    name: string;\n}\n\ninterface PreviewHistoryItem {\n    id: string;\n    content: string;\n    previewUrl: string;\n    deviceType: 'iphone' | 'android' | 'tablet' | string;\n    createdAt: string;\n    // Add other properties if needed\n}\n\n// --- Component ---\n\nconst MobilePreview: React.FC = () => {\n  const [loading, setLoading] = useState<boolean>(false);\n  const [historyLoading, setHistoryLoading] = useState<boolean>(true); // Separate loading for history\n  const [error, setError] = useState<string | null>(null);\n  const [emailContent, setEmailContent] = useState<string>('');\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\n  const [previewMode, setPreviewMode] = useState<'iphone' | 'android' | 'tablet'>('iphone');\n  const [previewHistory, setPreviewHistory] = useState<PreviewHistoryItem[]>([]);\n\n  // --- Effects ---\n\n  useEffect(() => {\n    const fetchPreviewHistory = async () => {\n      setHistoryLoading(true);\n      setError(null);\n      try {\n        const response = await mobilePreviewService.getPreviewHistory();\n        if (response.success) {\n          setPreviewHistory(response.data);\n        } else {\n            setError('Failed to load preview history');\n        }\n      } catch (err: any) {\n        setError(err.message || 'Error fetching preview history');\n        console.error('Error fetching preview history:', err);\n      } finally {\n        setHistoryLoading(false);\n      }\n    };\n    fetchPreviewHistory();\n  }, []);\n\n  // --- Handlers ---\n\n  const handleGeneratePreview = async () => {\n    if (!emailContent.trim()) {\n      setError('Email content is required');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setPreviewUrl(null); // Clear previous preview\n    try {\n      const response = await mobilePreviewService.generatePreview(emailContent, previewMode);\n      if (response.success) {\n        setPreviewUrl(response.data.previewUrl);\n        // Add to history if new\n        if (!previewHistory.some(item => item.id === response.data.id)) {\n          setPreviewHistory(prev => [response.data, ...prev]); // Add to start\n        }\n      } else {\n        setError(response.message || 'Failed to generate preview');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while generating preview');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSendTestEmail = async () => {\n    if (!emailContent.trim()) { // Check email content, not just preview URL\n      setError('Email content is required to send a test');\n      return;\n    }\n    const email = prompt('Enter email address to send test to:');\n    if (!email || !/\\S+@\\S+\\.\\S+/.test(email)) { // Basic email validation\n        alert('Invalid email address provided.');\n        return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await mobilePreviewService.sendTestEmail(email, emailContent);\n      if (response.success) {\n        alert(`Test email sent successfully to ${email}`); // Use alert for confirmation\n      } else {\n        setError(response.message || 'Failed to send test email');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while sending test email');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLoadFromHistory = (historyItem: PreviewHistoryItem) => {\n    setEmailContent(historyItem.content);\n    setPreviewUrl(historyItem.previewUrl);\n    setPreviewMode(historyItem.deviceType as any); // Cast might be needed if type mismatch\n    // Optionally scroll to preview or editor\n  };\n\n  // --- Render ---\n\n  return (\n    // <Sidebar> // Removed\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-2xl font-semibold text-text-primary\">Mobile Preview</h1>\n        </div>\n         <p className=\"text-text-secondary mb-6\">\n            Preview how your emails will look on different mobile devices before sending.\n         </p>\n\n        {error && (\n          <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n        )}\n\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n\n          {/* Column 1: Editor & Options */} \n          <div className=\"lg:col-span-1 space-y-6\">\n            <Card>\n              <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Content & Options</h2>\n              <div className=\"p-4 space-y-4\">\n                {/* Content Editor */} \n                <div>\n                  <label htmlFor=\"emailContent\" className=\"sr-only\">Email HTML Content</label>\n                  <textarea\n                    id=\"emailContent\"\n                    name=\"emailContent\"\n                    rows={15} // Increased rows\n                    value={emailContent}\n                    onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setEmailContent(e.target.value)}\n                    placeholder=\"Paste your email HTML content here or load a template...\"\n                    className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500 font-mono text-xs\"\n                  />\n                </div>\n\n                {/* Device Type Selector */} \n                <div>\n                  <label className=\"block text-sm font-medium text-text-secondary mb-2\">\n                    Device Type\n                  </label>\n                  <div className=\"flex items-center space-x-4 bg-secondary-bg p-2 rounded-md border border-gray-700\">\n                    {[ 'iphone', 'android', 'tablet' ].map((mode) => (\n                         <label key={mode} htmlFor={mode} className={`flex items-center justify-center flex-1 px-3 py-1 rounded-md text-sm cursor-pointer transition-colors ${previewMode === mode ? 'bg-primary text-white' : 'text-text-secondary hover:bg-gray-700'}`}>\n                           <input\n                             id={mode}\n                             name=\"deviceType\"\n                             type=\"radio\"\n                             checked={previewMode === mode}\n                             onChange={() => setPreviewMode(mode as any)} // Cast needed\n                             className=\"sr-only\" // Hide actual radio button\n                           />\n                           <span className=\"capitalize\">{mode}</span>\n                         </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Actions */} \n                <div className=\"flex space-x-3 pt-2\">\n                  <Button\n                    onClick={handleGeneratePreview}\n                    disabled={loading || !emailContent.trim()}\n                    className=\"flex-1\"\n                  >\n                    {loading ? 'Generating...' : 'Generate Preview'}\n                  </Button>\n                  <Button\n                    onClick={handleSendTestEmail}\n                    disabled={loading || !emailContent.trim()} // Allow sending even without preview URL now\n                    variant=\"secondary\"\n                    className=\"flex-1\"\n                  >\n                    Send Test Email\n                  </Button>\n                </div>\n              </div>\n            </Card>\n\n             {/* Preview History */}\n             <Card>\n                <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Preview History</h2>\n                 {historyLoading ? (\n                    <div className=\"p-4 text-center text-text-secondary\">Loading history...</div>\n                 ) : previewHistory.length === 0 ? (\n                    <div className=\"p-4 text-center text-text-secondary\">No preview history yet.</div>\n                 ) : (\n                    <ul className=\"divide-y divide-gray-700 max-h-60 overflow-y-auto\">\n                      {previewHistory.map((item) => (\n                        <li key={item.id} className=\"p-3 hover:bg-gray-700 cursor-pointer flex justify-between items-center\" onClick={() => handleLoadFromHistory(item)}>\n                           <div>\n                               <p className=\"text-xs text-text-secondary capitalize\">{item.deviceType}</p>\n                               <p className=\"text-xs text-text-secondary\">{new Date(item.createdAt).toLocaleString()}</p>\n                           </div>\n                           {/* Optionally add a small visual cue or icon */} \n                        </li>\n                      ))}\n                    </ul>\n                 )}\n             </Card>\n          </div>\n\n          {/* Column 2 & 3: Preview Area */} \n          <Card className=\"lg:col-span-2\">\n            <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Preview ({previewMode})</h2>\n            <div className=\"p-4 flex items-center justify-center bg-gray-800 min-h-[600px]\"> {/* Darker bg for contrast */}\n                {previewUrl ? (\n                    <iframe\n                        // Use device-specific dimensions based on previewMode\n                        style={{\n                            width: previewMode === 'tablet' ? '500px' : '375px', \n                            height: previewMode === 'tablet' ? '700px' : '667px',\n                            border: '10px solid #333', \n                            borderRadius: '20px',\n                            backgroundColor: 'white' // iframe background\n                        }}\n                        src={previewUrl}\n                        title={`Mobile Preview - ${previewMode}`}\n                        sandbox=\"allow-scripts allow-same-origin\" // Security: restrict iframe capabilities\n                    />\n                ) : loading && !previewUrl ? (\n                     <div className=\"text-center text-text-secondary\">\n                         <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4\"></div>\n                         Generating preview...\n                     </div>\n                 ) : (\n                    <div className=\"text-center text-text-secondary\">\n                        <p>Generate a preview to see how your email looks on a {previewMode}.</p>\n                        {/* Placeholder icon */} \n                         <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-24 w-24 text-gray-600 mx-auto mt-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={1}>\n                           <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                         </svg>\n                    </div>\n                 )}\n            </div>\n          </Card>\n\n        </div>\n      </div>\n    // </Sidebar> // Removed\n  );\n};\n\nexport default MobilePreview;\n"], "names": ["MobilePreview", "loading", "setLoading", "useState", "historyLoading", "setHistoryLoading", "error", "setError", "emailContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previewUrl", "setPreviewUrl", "previewMode", "setPreviewMode", "previewHistory", "setPreviewHistory", "useEffect", "async", "response", "mobilePreviewService", "getPreviewHistory", "success", "data", "err", "message", "console", "fetchPreviewHistory", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON>", "type", "onClose", "Card", "htmlFor", "id", "name", "rows", "value", "onChange", "e", "target", "placeholder", "map", "mode", "checked", "<PERSON><PERSON>", "onClick", "trim", "generatePreview", "some", "item", "prev", "disabled", "email", "prompt", "test", "sendTestEmail", "alert", "variant", "length", "handleLoadFromHistory", "historyItem", "content", "deviceType", "Date", "createdAt", "toLocaleString", "style", "width", "height", "border", "borderRadius", "backgroundColor", "src", "title", "sandbox", "xmlns", "fill", "viewBox", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d"], "sourceRoot": ""}