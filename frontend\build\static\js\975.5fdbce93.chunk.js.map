{"version": 3, "file": "static/js/975.5fdbce93.chunk.js", "mappings": "6JAqBAA,EAAAA,GAAQC,SACNC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,IAWF,MAuBA,EAvBoCC,IAA6C,IAA5C,KAAEC,EAAI,KAAEC,EAAI,QAAEC,EAAO,OAAEC,EAAM,MAAEC,GAAOL,EACzE,MAAMM,EAAe,CACnBC,YAAY,EACZC,qBAAqB,KAClBL,GAgBL,OAAOM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,SAbnBC,MAClB,OAAQX,GACN,IAAK,OACH,OAAOQ,EAAAA,EAAAA,KAACI,EAAAA,GAAI,CAACX,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACzE,IAAK,MACH,OAAOI,EAAAA,EAAAA,KAACK,EAAAA,GAAG,CAACZ,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACxE,IAAK,MACH,OAAOI,EAAAA,EAAAA,KAACM,EAAAA,GAAG,CAACb,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACxE,QACE,OAAOI,EAAAA,EAAAA,KAAA,KAAAE,SAAG,2BACd,EAGuCC,IAAoB,C,kIChD/D,MAsYA,EAtY4BI,KAAO,IAADC,EAChC,IAAIC,EACJ,IACEA,GAAmBC,EAAAA,EAAAA,KACnBC,QAAQC,IAAI,iDAAkDH,EAChE,CAAE,MAAOI,GAGP,MAFAF,QAAQG,MAAM,oCAAqCD,GAE7CA,CACR,CAEA,MAAM,KAAEE,GAASN,GACVO,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCJ,EAAOK,IAAYD,EAAAA,EAAAA,UAAS,KAC5BE,EAAeC,IAAoBH,EAAAA,EAAAA,UAAc,OACjDI,EAAeC,IAAoBL,EAAAA,EAAAA,UAAc,OAGxDM,EAAAA,EAAAA,YAAU,KACmBC,WACzB,IAEE,MAAMC,QAAiBC,EAAAA,EAAIC,IAAI,4BAA6B,CAC1DC,QAAS,CACP,cAAiB,UAAUC,aAAaC,QAAQ,cAIpD,IAAIL,EAASjC,KAAKuC,QAGhB,MAAM,IAAIC,MAAMP,EAASjC,KAAKyC,SAAW,kCAFzCX,EAAiBG,EAASjC,KAAKA,KAInC,CAAE,MAAOoB,GACPF,QAAQG,MAAM,iCAAkCD,GAChDM,EAAS,sCACX,GAGFgB,EAAoB,GACnB,KAGHX,EAAAA,EAAAA,YAAU,KAEmBC,WACzB,IAEE,MAAMW,EAAQN,aAAaC,QAAQ,SACnC,IAAKK,EACH,MAAM,IAAIH,MAAM,mCAIlB,MAAMP,QAAiBC,EAAAA,EAAIC,IAAI,uBAAwB,CACrDC,QAAS,CACP,cAAiB,UAAUO,OAK3BV,EAASjC,MAAQiC,EAASjC,KAAKuC,SAAWN,EAASjC,KAAKA,KACzD4B,EAAiBK,EAASjC,KAAKA,OAI/BkB,QAAQG,MAAM,qEAAsEY,EAASjC,MAC7F0B,EAAS,4CAGd,CAAE,MAAON,GAGwB,IAADwB,EAAAC,EAA7B,GAFA3B,QAAQG,MAAM,iCAAkCD,GAE5C0B,EAAAA,EAAMC,aAAa3B,GACrBM,EAAS,oCAA+C,QAAZkB,EAAAxB,EAAIa,gBAAQ,IAAAW,GAAM,QAANC,EAAZD,EAAc5C,YAAI,IAAA6C,OAAN,EAAZA,EAAoBJ,UAAWrB,EAAIqB,gBACtErB,aAAeoB,MACxBd,EAAS,mCAAmCN,EAAIqB,WAEhDf,EAAS,2DAEd,CAAC,QACCF,GAAW,EACb,GAGFwB,EAAoB,GACnB,CAAC1B,IAGJ,MAAM2B,EAAcC,GACL,IAAIC,KAAKD,GACVE,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,QACPC,IAAK,YAmEHC,EAAY7B,EA9DO8B,MACvB,IAAK9B,EAAe,OAAO,KAuD3B,MAAO,CACL+B,oBArD8B,CAC9BC,OAAQhC,EAAciC,cAAcC,KAAKC,GAAcb,EAAWa,EAAKC,OACvEC,SAAU,CACR,CACEC,MAAO,OACPjE,KAAM2B,EAAciC,cAAcC,KAAKC,GAAcA,EAAKI,OAC1DC,gBAAiB,0BACjBC,YAAa,oBACbC,YAAa,GAEf,CACEJ,MAAO,QACPjE,KAAM2B,EAAciC,cAAcC,KAAKC,GAAcA,EAAKQ,QAC1DH,gBAAiB,0BACjBC,YAAa,oBACbC,YAAa,GAEf,CACEJ,MAAO,SACPjE,KAAM2B,EAAciC,cAAcC,KAAKC,GAAcA,EAAKS,SAC1DJ,gBAAiB,yBACjBC,YAAa,mBACbC,YAAa,KAgCjBG,eA1ByB,CACzBb,OAAQhC,EAAciC,cAAcC,KAAKC,GAAcb,EAAWa,EAAKC,OACvEC,SAAU,CACR,CACEC,MAAO,gBACPjE,KAAM2B,EAAciC,cAAcC,KAAKC,GACrCA,EAAKI,KAAO,GAAKJ,EAAKQ,MAAQR,EAAKI,KAAO,KAAKO,QAAQ,GAAK,IAE9DN,gBAAiB,0BACjBC,YAAa,oBACbC,YAAa,GAEf,CACEJ,MAAO,iBACPjE,KAAM2B,EAAciC,cAAcC,KAAKC,GACrCA,EAAKI,KAAO,GAAKJ,EAAKS,OAAST,EAAKI,KAAO,KAAKO,QAAQ,GAAK,IAE/DN,gBAAiB,yBACjBC,YAAa,mBACbC,YAAa,KAQlB,EAG+BZ,GAAqB,KAEvD,OAAIlC,GAEAhB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oFAKjBa,GAEAd,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,SACzDY,IAKFM,GASH+C,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAlE,SAAA,EACEiE,EAAAA,EAAAA,MAAA,OAAKlE,UAAU,OAAMC,SAAA,EACnBiE,EAAAA,EAAAA,MAAA,MAAIlE,UAAU,8BAA6BC,SAAA,CAAC,YAAUkB,EAAcL,KAAKsD,KAAK,QAC9EF,EAAAA,EAAAA,MAAA,KAAGlE,UAAU,sBAAqBC,SAAA,CAAC,YACvBkB,EAAcL,KAAKuD,eAAe,QAA4C,IAAtClD,EAAcL,KAAKuD,eAAuB,IAAM,GAAG,cAC9D,YAAb,QAAzB9D,EAAAY,EAAcL,KAAKwD,cAAM,IAAA/D,OAAA,EAAzBA,EAA2BgE,UAC1BL,EAAAA,EAAAA,MAAA,QAAAjE,SAAA,CAAM,iBAAaF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,oBAAmBC,SAAEkB,EAAcL,KAAKwD,OAAOF,OAAc,8CAKxGF,EAAAA,EAAAA,MAAA,OAAKlE,UAAU,gJAA+IC,SAAA,EAC5JF,EAAAA,EAAAA,KAACyE,EAAAA,EAAQ,CACPC,MAAM,kBACNC,MAAOvD,EAAcwD,QAAQC,eAC7BC,KAAK,YACLC,QAASzD,EAAgB,CACvB,CAAEoC,MAAO,QAASiB,MAAOrD,EAAc0D,SAASC,OAChD,CAAEvB,MAAO,YAAaiB,MAAOrD,EAAc0D,SAASE,WACpD,CAAExB,MAAO,UAAWiB,MAAOrD,EAAc0D,SAASG,SAClD,CAAEzB,MAAO,YAAaiB,MAAOrD,EAAc0D,SAASI,iBAClDC,KAENrF,EAAAA,EAAAA,KAACyE,EAAAA,EAAQ,CACPC,MAAM,mBACNC,MAAOvD,EAAcwD,QAAQU,gBAC7BR,KAAK,gBACLS,QAAQ,8CAEVvF,EAAAA,EAAAA,KAACyE,EAAAA,EAAQ,CACPC,MAAM,mBACNC,OAAoB,OAAbrD,QAAa,IAAbA,OAAa,EAAbA,EAAekE,kBAAmB,EACzCV,KAAK,QACLC,QAASzD,EAAgB,CACvB,CAAEoC,MAAO,OAAQiB,MAAOrD,EAAcmE,WACtC,CAAE/B,MAAO,SAAUiB,MAAOrD,EAAcoE,mBACtCL,KAENrF,EAAAA,EAAAA,KAACyE,EAAAA,EAAQ,CACPC,MAAM,iBACNC,MAAO,GAAGvD,EAAcwD,QAAQe,gBAAgBzB,QAAQ,MACxDY,KAAK,sBAIK,OAAbxD,QAAa,IAAbA,OAAa,EAAbA,EAAesE,cACdzB,EAAAA,EAAAA,MAAA,OAAKlE,UAAU,2FAA0FC,SAAA,EACvGF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CC,SAAC,0BAC9DiE,EAAAA,EAAAA,MAAA,OAAKlE,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAACyE,EAAAA,EAAQ,CACPC,MAAM,oBACNC,MAAOrD,EAAcsE,WAAWC,YAChCf,KAAK,iBACLS,QAAQ,2CACRtF,UAAU,wBAEZD,EAAAA,EAAAA,KAACyE,EAAAA,EAAQ,CACPC,MAAM,kBACNC,MAAOrD,EAAcsE,WAAWZ,SAASrB,KACzCmB,KAAK,mBACLS,QAAQ,2BACRR,QAAS,CACP,CAAErB,MAAO,UAAWiB,MAAOrD,EAAcsE,WAAWZ,SAASc,SAC7D,CAAEpC,MAAO,OAAQiB,MAAOrD,EAAcsE,WAAWZ,SAASrB,MAC1D,CAAED,MAAO,UAAWiB,MAAOrD,EAAcsE,WAAWZ,SAASe,SAC7D,CAAErC,MAAO,eAAgBiB,MAAOrD,EAAcsE,WAAWZ,SAASgB,eAEpE/F,UAAU,8BAMlBkE,EAAAA,EAAAA,MAAA,OAAKlE,UAAU,6CAA4CC,SAAA,EACzDiE,EAAAA,EAAAA,MAAC8B,EAAAA,EAAI,CAACvB,MAAM,uBAAuBzE,UAAU,0DAAyDC,SAAA,EACpGF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CC,SAAC,yBAC7D+C,IACCjD,EAAAA,EAAAA,KAACkG,EAAAA,EAAK,CACJ1G,KAAK,MACLC,KAAMwD,EAAUE,oBAChBzD,QAAS,CACPI,YAAY,EACZC,qBAAqB,EACrBoG,QAAS,CAAEC,OAAQ,CAAEhD,OAAQ,CAAEiD,MAAO,iCACtCC,OAAQ,CACNC,EAAG,CACDC,aAAa,EACbC,KAAM,CAAEJ,MAAO,uBACfK,MAAO,CAAEL,MAAO,gCAElBM,EAAG,CACDF,KAAM,CAAEG,SAAS,GACjBF,MAAO,CAAEL,MAAO,wCAQ5BlC,EAAAA,EAAAA,MAAC8B,EAAAA,EAAI,CAACvB,MAAM,mBAAmBzE,UAAU,0DAAyDC,SAAA,EAChGF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CC,SAAC,qBAC7D+C,IACCjD,EAAAA,EAAAA,KAACkG,EAAAA,EAAK,CACJ1G,KAAK,OACLC,KAAMwD,EAAUgB,eAChBvE,QAAS,CACPI,YAAY,EACZC,qBAAqB,EACrBoG,QAAS,CAAEC,OAAQ,CAAEhD,OAAQ,CAAEiD,MAAO,iCACtCC,OAAQ,CACNC,EAAG,CACDC,aAAa,EACbK,IAAK,IACLJ,KAAM,CAAEJ,MAAO,uBACfK,MAAO,CAAEL,MAAO,gCAElBM,EAAG,CACDF,KAAM,CAAEG,SAAS,GACjBF,MAAO,CAAEL,MAAO,2CAS9BlC,EAAAA,EAAAA,MAAC8B,EAAAA,EAAI,CAACvB,MAAM,mBAAmBzE,UAAU,0DAAyDC,SAAA,EAChGF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gDAA+CC,SAAC,sBAC9DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BiE,EAAAA,EAAAA,MAAA,SAAOlE,UAAU,eAAcC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,SAAAE,UACEiE,EAAAA,EAAAA,MAAA,MAAAjE,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAI8G,MAAM,MAAK5G,SAAC,UAChBF,EAAAA,EAAAA,KAAA,MAAI8G,MAAM,MAAK5G,SAAC,YAChBF,EAAAA,EAAAA,KAAA,MAAI8G,MAAM,MAAK5G,SAAC,UAChBF,EAAAA,EAAAA,KAAA,MAAI8G,MAAM,MAAK5G,SAAC,WAChBF,EAAAA,EAAAA,KAAA,MAAI8G,MAAM,MAAK5G,SAAC,YAChBF,EAAAA,EAAAA,KAAA,MAAI8G,MAAM,MAAK5G,SAAC,kBAGpBF,EAAAA,EAAAA,KAAA,SAAAE,SAC4C,IAAzCkB,EAAc2F,gBAAgBC,QAC7BhH,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAAA,MAAIiH,QAAS,EAAGhH,UAAU,uCAAsCC,SAAC,uBAKnEkB,EAAc2F,gBAAgBzD,KAAK4D,IACjC/C,EAAAA,EAAAA,MAAA,MAAuBlE,UAAU,yBAAwBC,SAAA,EACvDF,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAAA,KACEmH,KAAM,mBAAmBD,EAAS1D,MAClCvD,UAAU,kBAAiBC,SAE1BgH,EAAS7C,UAGdrE,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAAA,QAAMC,UAAW,0CACK,UAApBiH,EAAS1C,OAAqB,uCACV,cAApB0C,EAAS1C,OAAyB,gCACd,YAApB0C,EAAS1C,OAAuB,gCACZ,cAApB0C,EAAS1C,OAAyB,gCAClC,2BACCtE,SACAgH,EAAS1C,OAAO4C,OAAO,GAAGC,cAAgBH,EAAS1C,OAAO8C,MAAM,QAGrEtH,EAAAA,EAAAA,KAAA,MAAAE,SAAKgH,EAASK,WAAa,KAC3BvH,EAAAA,EAAAA,KAAA,MAAAE,SACGgH,EAASK,UAAY,GACpBpD,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAlE,SAAA,CACGgH,EAASM,WAAa,EAAE,OAAKN,EAASM,WAAa,GAAKN,EAASK,UAAY,KAAKrD,QAAQ,GAAG,QAGhG,OAGJlE,EAAAA,EAAAA,KAAA,MAAAE,SACGgH,EAASK,UAAY,GACpBpD,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAlE,SAAA,CACGgH,EAASO,YAAc,EAAE,OAAKP,EAASO,YAAc,GAAKP,EAASK,UAAY,KAAKrD,QAAQ,GAAG,QAGlG,OAGJlE,EAAAA,EAAAA,KAAA,MAAAE,SAAKwC,EAAWwE,EAASQ,eAvClBR,EAAS1D,qBA9J9BxD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAqBC,SAAC,iCA4MpC,C,uDC/XP,MAAMyH,EAAOpI,IAAA,IAAC,KAAE8E,GAAwB9E,EAAA,OAAKS,EAAAA,EAAAA,KAAA,KAAGC,UAAW,oBAAoBoE,aAAkB,EAyDjG,EAvD0CuD,IAQnC,IARoC,MACzClD,EAAK,MACLC,EAAK,KACLG,EAAI,OACJ+C,EAAM,UACN5H,EAAY,GAAE,QACdsF,EAAO,QACPR,GACD6C,EAKC,OACEzD,EAAAA,EAAAA,MAAA,OAAKlE,UAAW,2CAAoBA,IAAYC,SAAA,EAC9CiE,EAAAA,EAAAA,MAAA,OAAKlE,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAEwE,IAC3BI,IAEC9E,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,UAC9CF,EAAAA,EAAAA,KAAC2H,EAAI,CAACtD,KAAMS,UAIlB9E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,SAAEyE,IACjCY,IACCvF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,SAAEqF,IAE/DsC,IAEC1D,EAAAA,EAAAA,MAAA,OAAKlE,UAAW,+CAA8C4H,EAAOC,WAAa,oBAAsB,eAAgB5H,SAAA,EACtHF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SACnB2H,EAAOC,YACN9H,EAAAA,EAAAA,KAAA,OAAK+H,MAAM,6BAA6B9H,UAAU,UAAU+H,QAAQ,YAAYC,KAAK,eAAc/H,UAACF,EAAAA,EAAAA,KAAA,QAAMkI,SAAS,UAAUC,EAAE,sHAAsHC,SAAS,eAC9PpI,EAAAA,EAAAA,KAAA,OAAK+H,MAAM,6BAA6B9H,UAAU,UAAU+H,QAAQ,YAAYC,KAAK,eAAc/H,UAACF,EAAAA,EAAAA,KAAA,QAAMkI,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,gBAGhQP,EAAOlD,SAGXI,GAAWA,EAAQiC,OAAS,IAE3BhH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtD6E,EAAQzB,KAAI,CAAC+E,EAAQC,KACpBnE,EAAAA,EAAAA,MAAA,OAAiBlE,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAEmI,EAAO3E,SACzD1D,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAEmI,EAAO1D,UAF9B2D,SAOZ,C", "sources": ["components/Chart.tsx", "pages/Dashboard.tsx", "components/StatCard.tsx"], "sourcesContent": ["import React from 'react';\n\nimport {\n  ArcElement,\n  BarElement,\n  CategoryScale,\n  Chart as ChartJS,\n  Legend,\n  LinearScale,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n} from 'chart.js';\nimport {\n  Bar,\n  Line,\n  Pie,\n} from 'react-chartjs-2';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface ChartProps {\n  type: 'line' | 'bar' | 'pie';\n  data: any;\n  options?: any;\n  height?: number;\n  width?: number;\n}\n\nconst Chart: React.FC<ChartProps> = ({ type, data, options, height, width }) => {\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: true,\n    ...options,\n  };\n\n  const renderChart = () => {\n    switch (type) {\n      case 'line':\n        return <Line data={data} options={chartOptions} height={height} width={width} />;\n      case 'bar':\n        return <Bar data={data} options={chartOptions} height={height} width={width} />;\n      case 'pie':\n        return <Pie data={data} options={chartOptions} height={height} width={width} />;\n      default:\n        return <p>Unsupported chart type</p>;\n    }\n  };\n\n  return <div className=\"chart-container\">{renderChart()}</div>;\n};\n\nexport default Chart;\n", "import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport axios from 'axios';\n\nimport Card from '../components/Card';\nimport Chart from '../components/Chart';\nimport StatCard from '../components/StatCard';\nimport { useAuth } from '../contexts/AuthContext';\nimport api from '../services/api';\n\nconst Dashboard: React.FC = () => {\n  let authContextValue;\n  try {\n    authContextValue = useAuth();\n    console.log(\"Dashboard: Successfully called useAuth. Value:\", authContextValue);\n  } catch (err) {\n    console.error(\"Dashboard: Error calling useAuth!\", err);\n    // Re-throw or handle error appropriately, maybe render an error message\n    throw err; // Re-throw to see the original error behavior\n  }\n\n  const { user } = authContextValue;\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [dashboardData, setDashboardData] = useState<any>(null);\n  const [campaignStats, setCampaignStats] = useState<any>(null);\n\n  // Fetch campaign stats\n  useEffect(() => {\n    const fetchCampaignStats = async () => {\n      try {\n        // Use the API URL from the api service instead of hardcoded path\n        const response = await api.get('/analytics/campaign-stats', {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n\n        if (response.data.success) {\n          setCampaignStats(response.data.data);\n        } else {\n          throw new Error(response.data.message || 'Failed to fetch campaign stats');\n        }\n      } catch (err) {\n        console.error('Error fetching campaign stats:', err);\n        setError('Failed to fetch campaign statistics');\n      }\n    };\n\n    fetchCampaignStats();\n  }, []);\n\n  // Fetch dashboard data\n  useEffect(() => {\n    // In a real app, this would be an API call\n    const fetchDashboardData = async () => {\n      try {\n        // Retrieve token safely\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Authentication token not found.');\n        }\n\n        // Use the API URL from the api service instead of hardcoded path\n        const response = await api.get('/analytics/dashboard', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        // Check if response data has the expected structure\n        if (response.data && response.data.success && response.data.data) {\n           setDashboardData(response.data.data);\n        } else {\n           // Handle cases where the response might be successful (status 200)\n           // but not contain the expected data structure or success flag\n           console.error('Dashboard data fetch successful but response format is unexpected:', response.data);\n           setError('Failed to retrieve valid dashboard data.');\n        }\n\n      } catch (err: any) { // Catch axios errors and other errors\n         console.error('Error fetching dashboard data:', err);\n         // More specific error handling based on error type if needed\n         if (axios.isAxiosError(err)) {\n           setError(`Failed to fetch dashboard data: ${err.response?.data?.message || err.message}`);\n         } else if (err instanceof Error) {\n           setError(`Failed to fetch dashboard data: ${err.message}`);\n         } else {\n           setError('An unknown error occurred while fetching dashboard data.');\n         }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData(); // Call the function\n  }, [user]); // Dependency array remains [user] for now. If campaignStats is needed immediately, might need adjustment.\n\n  // Format date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  // Prepare chart data\n  const prepareChartData = () => {\n    if (!dashboardData) return null;\n\n    // Campaign performance chart\n    const campaignPerformanceData = {\n      labels: dashboardData.timeAnalytics.map((item: any) => formatDate(item._id)),\n      datasets: [\n        {\n          label: 'Sent',\n          data: dashboardData.timeAnalytics.map((item: any) => item.sent),\n          backgroundColor: 'rgba(59, 130, 246, 0.5)',\n          borderColor: 'rgb(59, 130, 246)',\n          borderWidth: 1\n        },\n        {\n          label: 'Opens',\n          data: dashboardData.timeAnalytics.map((item: any) => item.opens),\n          backgroundColor: 'rgba(75, 192, 192, 0.5)',\n          borderColor: 'rgb(75, 192, 192)',\n          borderWidth: 1\n        },\n        {\n          label: 'Clicks',\n          data: dashboardData.timeAnalytics.map((item: any) => item.clicks),\n          backgroundColor: 'rgba(255, 94, 98, 0.5)',\n          borderColor: 'rgb(255, 94, 98)',\n          borderWidth: 1\n        }\n      ]\n    };\n\n    // Engagement rate chart\n    const engagementRateData = {\n      labels: dashboardData.timeAnalytics.map((item: any) => formatDate(item._id)),\n      datasets: [\n        {\n          label: 'Open Rate (%)',\n          data: dashboardData.timeAnalytics.map((item: any) =>\n            item.sent > 0 ? (item.opens / item.sent * 100).toFixed(1) : 0\n          ),\n          backgroundColor: 'rgba(75, 192, 192, 0.5)',\n          borderColor: 'rgb(75, 192, 192)',\n          borderWidth: 1\n        },\n        {\n          label: 'Click Rate (%)',\n          data: dashboardData.timeAnalytics.map((item: any) =>\n            item.sent > 0 ? (item.clicks / item.sent * 100).toFixed(1) : 0\n          ),\n          backgroundColor: 'rgba(255, 94, 98, 0.5)',\n          borderColor: 'rgb(255, 94, 98)',\n          borderWidth: 1\n        }\n      ]\n    };\n\n    return {\n      campaignPerformance: campaignPerformanceData,\n      engagementRate: engagementRateData\n    };\n  };\n\n  const chartData = dashboardData ? prepareChartData() : null;\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-accent-coral\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-danger/80 text-white p-4 rounded-md mb-6\">\n        {error}\n      </div>\n    );\n  }\n\n  if (!dashboardData) {\n    return (\n      <div className=\"text-center py-8\">\n        <p className=\"text-text-secondary\">No dashboard data available</p>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <div className=\"mb-8\">\n        <h2 className=\"text-3xl font-semibold mb-2\">Welcome, {dashboardData.user.name}!</h2>\n        <p className=\"text-text-secondary\">\n          You have {dashboardData.user.flowsPurchased} flow{dashboardData.user.flowsPurchased !== 1 ? 's' : ''} available.\n          {dashboardData.user.domain?.status === 'active' && (\n            <span> Your domain <strong className=\"text-text-primary\">{dashboardData.user.domain.name}</strong> is active and ready for sending.</span>\n          )}\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 p-4 glassmorphic rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\">\n        <StatCard\n          title=\"Total Campaigns\"\n          value={dashboardData.metrics.totalCampaigns}\n          icon=\"chart-bar\"\n          details={campaignStats ? [\n            { label: 'Draft', value: campaignStats.byStatus.draft },\n            { label: 'Scheduled', value: campaignStats.byStatus.scheduled },\n            { label: 'Sending', value: campaignStats.byStatus.sending },\n            { label: 'Completed', value: campaignStats.byStatus.completed }\n          ] : undefined}\n        />\n        <StatCard\n          title=\"Active Campaigns\"\n          value={dashboardData.metrics.activeCampaigns}\n          icon=\"rocket-launch\"\n          tooltip=\"Campaigns currently sending or scheduled\"\n        />\n        <StatCard\n          title=\"Total Recipients\"\n          value={campaignStats?.totalRecipients || 0}\n          icon=\"users\"\n          details={campaignStats ? [\n            { label: 'Sent', value: campaignStats.totalSent },\n            { label: 'Errors', value: campaignStats.totalErrors }\n          ] : undefined}\n        />\n        <StatCard\n          title=\"Avg. Open Rate\"\n          value={`${dashboardData.metrics.averageOpenRate.toFixed(1)}%`}\n          icon=\"envelope-open\"\n        />\n      </div>\n\n      {campaignStats?.recipients && (\n        <div className=\"mb-8 glassmorphic rounded-lg p-6 transition-all duration-300 ease-in-out hover:shadow-lg\">\n          <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Recipient Statistics</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <StatCard\n              title=\"Unique Recipients\"\n              value={campaignStats.recipients.totalUnique}\n              icon=\"identification\"\n              tooltip=\"Total unique recipients in your database\"\n              className=\"bg-neutral-base/80\"\n            />\n            <StatCard\n              title=\"Delivery Status\"\n              value={campaignStats.recipients.byStatus.sent}\n              icon=\"inbox-arrow-down\"\n              tooltip=\"Successfully sent emails\"\n              details={[\n                { label: 'Pending', value: campaignStats.recipients.byStatus.pending },\n                { label: 'Sent', value: campaignStats.recipients.byStatus.sent },\n                { label: 'Bounced', value: campaignStats.recipients.byStatus.bounced },\n                { label: 'Unsubscribed', value: campaignStats.recipients.byStatus.unsubscribed }\n              ]}\n              className=\"bg-neutral-base/80\"\n            />\n          </div>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n        <Card title=\"Campaign Performance\" className=\"transition-all duration-300 ease-in-out hover:shadow-lg\">\n          <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Campaign Performance</h3>\n          {chartData && (\n            <Chart\n              type=\"bar\"\n              data={chartData.campaignPerformance}\n              options={{\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: { legend: { labels: { color: 'var(--color-text-secondary)' } } },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    grid: { color: 'var(--color-border)' },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  },\n                  x: {\n                    grid: { display: false },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  }\n                }\n              }}\n            />\n          )}\n        </Card>\n\n        <Card title=\"Engagement Rates\" className=\"transition-all duration-300 ease-in-out hover:shadow-lg\">\n          <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Engagement Rates</h3>\n          {chartData && (\n            <Chart\n              type=\"line\"\n              data={chartData.engagementRate}\n              options={{\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: { legend: { labels: { color: 'var(--color-text-secondary)' } } },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    max: 100,\n                    grid: { color: 'var(--color-border)' },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  },\n                  x: {\n                    grid: { display: false },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  }\n                }\n              }}\n            />\n          )}\n        </Card>\n      </div>\n\n      <Card title=\"Recent Campaigns\" className=\"transition-all duration-300 ease-in-out hover:shadow-lg\">\n        <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Recent Campaigns</h3>\n        <div className=\"table-container\">\n          <table className=\"table w-full\">\n            <thead>\n              <tr>\n                <th scope=\"col\">Name</th>\n                <th scope=\"col\">Status</th>\n                <th scope=\"col\">Sent</th>\n                <th scope=\"col\">Opens</th>\n                <th scope=\"col\">Clicks</th>\n                <th scope=\"col\">Created</th>\n              </tr>\n            </thead>\n            <tbody>\n              {dashboardData.recentCampaigns.length === 0 ? (\n                <tr>\n                  <td colSpan={6} className=\"text-center py-4 text-text-secondary\">\n                    No campaigns yet\n                  </td>\n                </tr>\n              ) : (\n                dashboardData.recentCampaigns.map((campaign: any) => (\n                  <tr key={campaign._id} className=\"hover:bg-neutral-light\">\n                    <td>\n                      <a\n                        href={`/campaigns/edit/${campaign._id}`}\n                        className=\"hover:underline\"\n                      >\n                        {campaign.name}\n                      </a>\n                    </td>\n                    <td>\n                      <span className={`px-2 py-1 text-xs rounded font-medium ${ \n                        campaign.status === 'draft' ? 'bg-neutral-light text-text-secondary' :\n                        campaign.status === 'scheduled' ? 'bg-primary-blue/80 text-white' :\n                        campaign.status === 'sending' ? 'bg-accent-coral/80 text-white' :\n                        campaign.status === 'completed' ? 'bg-growth-green/80 text-white' :\n                        'bg-danger/80 text-white'\n                      }`}>\n                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}\n                      </span>\n                    </td>\n                    <td>{campaign.sentCount || 0}</td>\n                    <td>\n                      {campaign.sentCount > 0 ? (\n                        <>\n                          {campaign.openCount || 0} ({((campaign.openCount || 0) / campaign.sentCount * 100).toFixed(1)}%)\n                        </>\n                      ) : (\n                        '-'\n                      )}\n                    </td>\n                    <td>\n                      {campaign.sentCount > 0 ? (\n                        <>\n                          {campaign.clickCount || 0} ({((campaign.clickCount || 0) / campaign.sentCount * 100).toFixed(1)}%)\n                        </>\n                      ) : (\n                        '-'\n                      )}\n                    </td>\n                    <td>{formatDate(campaign.createdAt)}</td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n      </Card>\n    </>\n  );\n};\n\nexport default Dashboard;\n", "import React from 'react';\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  icon?: string;\n  change?: {\n    value: string | number;\n    isPositive: boolean;\n  };\n  className?: string;\n  tooltip?: string;\n  details?: { label: string; value: string | number }[];\n}\n\n// Helper for icon rendering (replace with your actual icon logic)\nconst Icon = ({ name }: { name: string }) => <i className={`placeholder-icon-${name} w-5 h-5`} />; \n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  icon,\n  change,\n  className = '',\n  tooltip,\n  details\n}) => {\n  // Use the base .card or apply .glassmorphic for futuristic feel\n  // Use container-futuristic if defined, otherwise default card styling from index.css\n  const baseCardClass = \"card container-futuristic flex flex-col\"; \n\n  return (\n    <div className={`${baseCardClass} ${className}`}>\n      <div className=\"flex justify-between items-start mb-2\">\n        <h3 className=\"stat-label\">{title}</h3>\n        {icon && (\n          // Use text-secondary for default icon color\n          <span className=\"text-text-secondary opacity-80\">\n            <Icon name={icon} />\n          </span>\n        )}\n      </div>\n      <div className=\"stat-value mb-1\">{value}</div>\n      {tooltip && (\n        <div className=\"text-xs text-text-secondary mt-1 opacity-90\">{tooltip}</div>\n      )}\n      {change && (\n        // Apply theme colors: growth-green for positive, danger for negative\n        <div className={`text-sm mt-2 flex items-center font-medium ${change.isPositive ? 'text-growth-green' : 'text-danger'}`}>\n          <span className=\"mr-1\">\n            {change.isPositive ? \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\" clipRule=\"evenodd\" /></svg> : \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" /></svg> \n            }\n          </span>\n          {change.value}\n        </div>\n      )}\n      {details && details.length > 0 && (\n        // Use the theme border color\n        <div className=\"mt-auto pt-3 border-t border-border mt-3\">\n          {details.map((detail, index) => (\n            <div key={index} className=\"flex justify-between text-xs py-1\">\n              <span className=\"text-text-secondary opacity-90\">{detail.label}</span>\n              <span className=\"font-medium\">{detail.value}</span>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StatCard;\n"], "names": ["ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "_ref", "type", "data", "options", "height", "width", "chartOptions", "responsive", "maintainAspectRatio", "_jsx", "className", "children", "<PERSON><PERSON><PERSON>", "Line", "Bar", "Pie", "Dashboard", "_dashboardData$user$d", "authContextValue", "useAuth", "console", "log", "err", "error", "user", "loading", "setLoading", "useState", "setError", "dashboardData", "setDashboardData", "campaignStats", "setCampaignStats", "useEffect", "async", "response", "api", "get", "headers", "localStorage", "getItem", "success", "Error", "message", "fetchCampaignStats", "token", "_err$response", "_err$response$data", "axios", "isAxiosError", "fetchDashboardData", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "chartData", "prepareChartData", "campaignPerformance", "labels", "timeAnalytics", "map", "item", "_id", "datasets", "label", "sent", "backgroundColor", "borderColor", "borderWidth", "opens", "clicks", "engagementRate", "toFixed", "_jsxs", "_Fragment", "name", "flowsPurchased", "domain", "status", "StatCard", "title", "value", "metrics", "totalCampaigns", "icon", "details", "byStatus", "draft", "scheduled", "sending", "completed", "undefined", "activeCampaigns", "tooltip", "totalRecipients", "totalSent", "totalErrors", "averageOpenRate", "recipients", "totalUnique", "pending", "bounced", "unsubscribed", "Card", "Chart", "plugins", "legend", "color", "scales", "y", "beginAtZero", "grid", "ticks", "x", "display", "max", "scope", "recentCampaigns", "length", "colSpan", "campaign", "href", "char<PERSON>t", "toUpperCase", "slice", "sentCount", "openCount", "clickCount", "createdAt", "Icon", "_ref2", "change", "isPositive", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "detail", "index"], "sourceRoot": ""}