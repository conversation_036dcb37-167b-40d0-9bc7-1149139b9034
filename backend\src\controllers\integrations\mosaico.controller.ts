import axios from 'axios';
import {
  NextFunction,
  Request,
  Response,
} from 'express';
import createError from 'http-errors';

import Campaign from '../../models/campaign.model';
import Integration from '../../models/integrations/integration.model';
import IntegrationConnectionModel
  from '../../models/integrations/integrationConnection.model';

// Configuration
const MOSAICO_BASE_URL = process.env.MOSAICO_BASE_URL || 'http://localhost:8080';

/**
 * Get Mosaico editor configuration
 */
export const getMosaicoConfig = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user?.id;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }

  try {
    // Check if user has connected to Mosaico integration
    const integration = await Integration.findOne({ id: 'mosaico' });
    if (!integration) {
      return next(createError(404, 'Mosaico integration not found'));
    }

    // Get connection if exists
    const connection = await IntegrationConnectionModel.findOne({
      userId,
      integrationId: integration._id,
      status: 'connected'
    });

    // Configuration to return to client
    const config = {
      editorUrl: MOSAICO_BASE_URL,
      uploadUrl: `${req.protocol}://${req.get('host')}/api/v1/integrations/mosaico/upload`,
      isConnected: !!connection
    };

    return res.status(200).json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Error getting Mosaico configuration:', error);
    return next(createError(500, (error as Error).message || 'Failed to get Mosaico configuration'));
  }
};

/**
 * Handle image uploads for Mosaico
 */
export const handleMosaicoUpload = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user?.id;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }

  try {
    if (!req.file) {
      return next(createError(400, 'No file uploaded'));
    }

    // In a real implementation, you'd process the uploaded file,
    // store it in your storage system, and return the URL

    // --- MODIFIED: Generate ABSOLUTE URL for secure serving endpoint ---
    const baseUrl = `${req.protocol}://${req.get('host')}`; 
    const secureFileUrl = `${baseUrl}/api/v1/images/serve/${req.file.filename}`; // Absolute URL
    console.log(`[handleMosaicoUpload] Generated secure ABSOLUTE URL: ${secureFileUrl}`);
    
    return res.status(200).json({
      success: true,
      data: {
        url: secureFileUrl // Return the secure URL
      }
    });
  } catch (error) {
    console.error('Error uploading file for Mosaico:', error);
    return next(createError(500, (error as Error).message || 'Failed to upload file'));
  }
};

/**
 * Save Mosaico template data to campaign
 */
export const saveMosaicoTemplate = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user?.id;
  const { campaignId } = req.params;
  const { mosaicoJson, html } = req.body;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }

  if (!campaignId || !mosaicoJson || !html) {
    return next(createError(400, 'Campaign ID, Mosaico JSON, and HTML content are required'));
  }

  try {
    // Find the campaign and verify ownership
    const campaign = await Campaign.findOne({ 
      _id: campaignId, 
      userId 
    });
    
    if (!campaign) {
      return next(createError(404, 'Campaign not found or you do not have permission to access it'));
    }

    // Update the campaign with Mosaico data
    campaign.mosaicoJson = mosaicoJson;
    campaign.htmlContent = html;
    await campaign.save();

    return res.status(200).json({
      success: true,
      message: 'Mosaico template saved successfully'
    });
  } catch (error) {
    console.error('Error saving Mosaico template:', error);
    return next(createError(500, (error as Error).message || 'Failed to save Mosaico template'));
  }
};

/**
 * Proxy API calls to Mosaico server
 */
export const proxyMosaicoRequest = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user?.id;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }

  const targetPath = req.params[0]; // Capture all path segments after /api/v1/integrations/mosaico/proxy/
  const targetUrl = `${MOSAICO_BASE_URL}/${targetPath}`;

  try {
    const response = await axios({
      method: req.method,
      url: targetUrl,
      data: req.body,
      headers: {
        'Content-Type': req.headers['content-type'],
        'Accept': req.headers['accept']
      },
      responseType: 'stream'
    });

    // Forward the Mosaico response
    response.data.pipe(res);
  } catch (error) {
    console.error('Error proxying request to Mosaico:', error);
    return next(createError(500, (error as Error).message || 'Failed to proxy request to Mosaico'));
  }
}; 