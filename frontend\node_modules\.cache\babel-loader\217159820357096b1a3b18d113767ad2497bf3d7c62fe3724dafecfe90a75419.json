{"ast": null, "code": "import React,{useEffect,useState}from'react';import Alert from'../components/Alert';import Button from'../components/Button';// Assuming components exist\nimport Card from'../components/Card';// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport{segmentService,sendTimeService}from'../services';// Define interfaces if needed\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SendTimeOptimization=()=>{const[optimalTimes,setOptimalTimes]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[optimizationJob,setOptimizationJob]=useState(null);const[jobStatus,setJobStatus]=useState(null);// e.g., 'running', 'completed', 'failed'\nconst[selectedSegmentId,setSelectedSegmentId]=useState('');const[segments,setSegments]=useState([]);const[jobPolling,setJobPolling]=useState(null);// Type for interval ID\n// Fetch optimal send times on component mount\nuseEffect(()=>{const fetchInitialData=async()=>{setLoading(true);setError(null);try{const[segmentsResponse,timesResponse]=await Promise.all([segmentService.getUserSegments(),sendTimeService.getOptimalTimes()]);if(segmentsResponse.success){setSegments(Array.isArray(segmentsResponse.data)?segmentsResponse.data:[]);}else{setError(prev=>prev?`${prev}; ${segmentsResponse.message}`:segmentsResponse.message||'Failed to fetch segments');}if(timesResponse.success){// Ensure data is an array before setting state\nsetOptimalTimes(Array.isArray(timesResponse.data)?timesResponse.data:[]);}else{setError(prev=>prev?`${prev}; ${timesResponse.message}`:timesResponse.message||'Failed to fetch optimal times');setOptimalTimes([]);// Explicitly set empty on fetch error\n}}catch(err){setError(err.message||'An error occurred while fetching initial data');}finally{setLoading(false);}};fetchInitialData();},[]);// Poll job status when a job is running\nuseEffect(()=>{if(!optimizationJob||jobStatus!=='running'){if(jobPolling)clearInterval(jobPolling);setJobPolling(null);return;// Stop polling if no job or job not running\n}const pollJobStatus=async()=>{try{console.log(`Polling job status for ${optimizationJob.id}...`);const response=await sendTimeService.getJobStatus(optimizationJob.id);if(response.success){const currentStatus=response.data.status;setJobStatus(currentStatus);console.log(`Job status: ${currentStatus}`);// If job is complete or failed, fetch updated optimal times and clear polling\nif(currentStatus==='completed'||currentStatus==='failed'){if(jobPolling)clearInterval(jobPolling);setJobPolling(null);setOptimizationJob(null);// Clear the job\nif(currentStatus==='completed'){console.log('Job completed, fetching updated times...');const timesResponse=await sendTimeService.getOptimalTimes();if(timesResponse.success){setOptimalTimes(Array.isArray(timesResponse.data)?timesResponse.data:[]);}else{setError('Failed to fetch updated optimal times after job completion.');}}else{setError('Optimization job failed. Please check logs or try again.');}}}else{console.error('Failed to poll job status:',response.message);// Decide if polling should stop on error\n// clearInterval(jobPolling); \n// setJobPolling(null);\n// setError('Error checking job status.');\n}}catch(err){console.error('Error polling job status:',err);// Consider stopping polling on error\n// if (jobPolling) clearInterval(jobPolling);\n// setJobPolling(null);\n// setError('An error occurred while checking job status.');\n}};// Set up polling interval only if not already polling\nif(!jobPolling){console.log('Starting job polling...');const intervalId=setInterval(pollJobStatus,5000);// Poll every 5 seconds\nsetJobPolling(intervalId);}// Clean up interval on unmount or when job changes/completes\nreturn()=>{if(jobPolling){console.log('Clearing job polling interval.');clearInterval(jobPolling);}};},[optimizationJob,jobStatus]);// Rerun effect if job or status changes\n// Handle running bulk optimization\nconst handleRunOptimization=async()=>{setError(null);setJobStatus(null);setOptimizationJob(null);setLoading(true);try{const response=await sendTimeService.runBulkOptimization(selectedSegmentId||undefined);if(response.success){setOptimizationJob(response.data);// Set the job object\nsetJobStatus('running');// Start polling\nconsole.log('Optimization job started:',response.data);}else{setError(response.message||'Failed to start optimization job');}}catch(err){setError(err.message||'An error occurred while starting optimization');}finally{setLoading(false);}};// Group optimal times by day of week\nconst groupedOptimalTimes=optimalTimes.reduce((acc,time)=>{const day=time.dayOfWeek||'Unknown';if(!acc[day]){acc[day]=[];}acc[day].push(time);return acc;},{});// Add type to accumulator\n// Days of week for sorting\nconst daysOfWeek=['Monday','Tuesday','Wednesday','Thursday','Friday','Saturday','Sunday'];// Remove Sidebar wrapper\nreturn(/*#__PURE__*/// <Sidebar> // Removed\n_jsxs(\"div\",{className:\"container mx-auto px-4 py-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-between items-center mb-6\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-semibold text-text-primary\",children:\"Predictive Send-Time Optimization\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-6\",children:\"Optimize email delivery times based on recipient engagement patterns.\"}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(null),className:\"mb-6\"}),/*#__PURE__*/_jsx(Card,{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-text-primary mb-4\",children:\"Run Optimization\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 sm:grid-cols-2\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"segmentId\",className:\"block text-sm font-medium text-text-secondary mb-1\",children:\"Segment (Optional)\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"segmentId\",name:\"segmentId\",value:selectedSegmentId,onChange:e=>setSelectedSegmentId(e.target.value),className:\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Recipients\"}),segments.map(segment=>/*#__PURE__*/_jsx(\"option\",{value:segment.id,children:segment.name},segment.id))]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-xs text-text-secondary\",children:\"Select a segment to optimize for, or leave blank for all.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-end\",children:/*#__PURE__*/_jsx(Button,{onClick:handleRunOptimization,disabled:loading||jobStatus==='running',className:\"w-full sm:w-auto\"// Adjust button width\n,children:jobStatus==='running'?'Optimization Running...':loading?'Starting...':'Run Optimization'})})]}),jobStatus==='running'&&/*#__PURE__*/_jsx(Alert,{type:\"info\",message:`Optimization in progress. Status: ${jobStatus}. This may take a few minutes...`,className:\"mt-4\"})]})}),/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-text-primary mb-4\",children:\"Optimal Send Times\"}),loading&&optimalTimes.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"})}):optimalTimes.length===0?/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-text-secondary text-center py-4\",children:\"No optimal send times available. Run an optimization to generate recommendations.\"}):/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:daysOfWeek.map(day=>{const timesForDay=groupedOptimalTimes[day]||[];if(timesForDay.length===0)return null;return/*#__PURE__*/_jsxs(\"div\",{className:\"border-t border-gray-700 pt-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-md font-medium text-text-primary mb-3\",children:day}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\",children:timesForDay.map((time,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-secondary-bg p-4 rounded-lg shadow-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-start mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-lg font-semibold text-primary\",children:time.time||'Time N/A'}),/*#__PURE__*/_jsx(\"div\",{className:`px-2 py-0.5 rounded-full text-xs font-medium capitalize ${time.confidence==='high'?'bg-green-800 text-green-100':time.confidence==='medium'?'bg-yellow-800 text-yellow-100':time.confidence==='low'?'bg-red-800 text-red-100':'bg-gray-700 text-gray-300'}`,children:time.confidence||'unknown'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-text-secondary mb-1\",children:time.segment?`Segment: ${time.segment}`:'All Recipients'}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-text-secondary mb-2\",children:[\"Based on \",time.dataPoints||'?',\" data points\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-text-secondary\",children:\"Est. Open Rate: \"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-text-primary\",children:time.expectedOpenRate||'N/A'})]})]},index))})]},day);})})]})})]})// </Sidebar> // Removed\n);};export default SendTimeOptimization;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "segmentService", "sendTimeService", "jsx", "_jsx", "jsxs", "_jsxs", "SendTimeOptimization", "optimalTimes", "setOptimalTimes", "loading", "setLoading", "error", "setError", "optimizationJob", "setOptimizationJob", "jobStatus", "setJobStatus", "selectedSegmentId", "setSelectedSegmentId", "segments", "setSegments", "jobPolling", "setJob<PERSON><PERSON>ing", "fetchInitialData", "segmentsResponse", "timesResponse", "Promise", "all", "getUserSegments", "getOptimalTimes", "success", "Array", "isArray", "data", "prev", "message", "err", "clearInterval", "pollJobStatus", "console", "log", "id", "response", "getJobStatus", "currentStatus", "status", "intervalId", "setInterval", "handleRunOptimization", "runBulkOptimization", "undefined", "groupedOptimalTimes", "reduce", "acc", "time", "day", "dayOfWeek", "push", "daysOfWeek", "className", "children", "type", "onClose", "htmlFor", "name", "value", "onChange", "e", "target", "map", "segment", "onClick", "disabled", "length", "timesForDay", "index", "confidence", "dataPoints", "expectedOpenRate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/SendTimeOptimization.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button'; // Assuming components exist\nimport Card from '../components/Card';\n// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport {\n  segmentService,\n  sendTimeService,\n} from '../services';\n\n// Define interfaces if needed\ninterface OptimalTime {\n  dayOfWeek: string;\n  time: string;\n  confidence: 'high' | 'medium' | 'low' | string; // Allow string for fallback\n  segment?: string;\n  dataPoints?: number;\n  expectedOpenRate?: string;\n  // Add other properties if they exist\n}\n\ninterface Segment {\n  id: string;\n  name: string;\n}\n\ninterface OptimizationJob {\n  id: string;\n  // Add other job properties if needed\n}\n\nconst SendTimeOptimization: React.FC = () => {\n  const [optimalTimes, setOptimalTimes] = useState<OptimalTime[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [optimizationJob, setOptimizationJob] = useState<OptimizationJob | null>(null);\n  const [jobStatus, setJobStatus] = useState<string | null>(null); // e.g., 'running', 'completed', 'failed'\n  const [selectedSegmentId, setSelectedSegmentId] = useState<string>('');\n  const [segments, setSegments] = useState<Segment[]>([]);\n  const [jobPolling, setJobPolling] = useState<NodeJS.Timeout | null>(null); // Type for interval ID\n\n  // Fetch optimal send times on component mount\n  useEffect(() => {\n    const fetchInitialData = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const [segmentsResponse, timesResponse] = await Promise.all([\n          segmentService.getUserSegments(),\n          sendTimeService.getOptimalTimes()\n        ]);\n\n        if (segmentsResponse.success) {\n          setSegments(Array.isArray(segmentsResponse.data) ? segmentsResponse.data : []);\n        } else {\n          setError(prev => prev ? `${prev}; ${segmentsResponse.message}` : segmentsResponse.message || 'Failed to fetch segments');\n        }\n\n        if (timesResponse.success) {\n          // Ensure data is an array before setting state\n          setOptimalTimes(Array.isArray(timesResponse.data) ? timesResponse.data : []);\n        } else {\n          setError(prev => prev ? `${prev}; ${timesResponse.message}` : timesResponse.message || 'Failed to fetch optimal times');\n          setOptimalTimes([]); // Explicitly set empty on fetch error\n        }\n\n      } catch (err: any) {\n        setError(err.message || 'An error occurred while fetching initial data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchInitialData();\n  }, []);\n\n  // Poll job status when a job is running\n  useEffect(() => {\n    if (!optimizationJob || jobStatus !== 'running') {\n        if (jobPolling) clearInterval(jobPolling);\n        setJobPolling(null);\n        return; // Stop polling if no job or job not running\n    }\n\n    const pollJobStatus = async () => {\n      try {\n        console.log(`Polling job status for ${optimizationJob.id}...`);\n        const response = await sendTimeService.getJobStatus(optimizationJob.id);\n        if (response.success) {\n          const currentStatus = response.data.status;\n          setJobStatus(currentStatus);\n          console.log(`Job status: ${currentStatus}`);\n\n          // If job is complete or failed, fetch updated optimal times and clear polling\n          if (currentStatus === 'completed' || currentStatus === 'failed') {\n            if (jobPolling) clearInterval(jobPolling);\n            setJobPolling(null);\n            setOptimizationJob(null); // Clear the job\n\n            if (currentStatus === 'completed') {\n                 console.log('Job completed, fetching updated times...');\n                 const timesResponse = await sendTimeService.getOptimalTimes();\n                 if (timesResponse.success) {\n                   setOptimalTimes(Array.isArray(timesResponse.data) ? timesResponse.data : []);\n                 } else {\n                     setError('Failed to fetch updated optimal times after job completion.');\n                 }\n            } else {\n                 setError('Optimization job failed. Please check logs or try again.');\n            }\n          }\n        } else {\n            console.error('Failed to poll job status:', response.message);\n            // Decide if polling should stop on error\n            // clearInterval(jobPolling); \n            // setJobPolling(null);\n            // setError('Error checking job status.');\n        }\n      } catch (err) {\n        console.error('Error polling job status:', err);\n        // Consider stopping polling on error\n        // if (jobPolling) clearInterval(jobPolling);\n        // setJobPolling(null);\n        // setError('An error occurred while checking job status.');\n      }\n    };\n\n    // Set up polling interval only if not already polling\n    if (!jobPolling) {\n        console.log('Starting job polling...');\n      const intervalId = setInterval(pollJobStatus, 5000); // Poll every 5 seconds\n      setJobPolling(intervalId);\n    }\n\n    // Clean up interval on unmount or when job changes/completes\n    return () => {\n      if (jobPolling) {\n        console.log('Clearing job polling interval.');\n        clearInterval(jobPolling);\n      }\n    };\n  }, [optimizationJob, jobStatus]); // Rerun effect if job or status changes\n\n  // Handle running bulk optimization\n  const handleRunOptimization = async () => {\n    setError(null);\n    setJobStatus(null);\n    setOptimizationJob(null);\n    setLoading(true);\n\n    try {\n      const response = await sendTimeService.runBulkOptimization(\n        selectedSegmentId || undefined\n      );\n\n      if (response.success) {\n        setOptimizationJob(response.data); // Set the job object\n        setJobStatus('running'); // Start polling\n        console.log('Optimization job started:', response.data);\n      } else {\n        setError(response.message || 'Failed to start optimization job');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while starting optimization');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Group optimal times by day of week\n  const groupedOptimalTimes = optimalTimes.reduce((acc, time) => {\n    const day = time.dayOfWeek || 'Unknown';\n    if (!acc[day]) {\n      acc[day] = [];\n    }\n    acc[day].push(time);\n    return acc;\n  }, {} as Record<string, OptimalTime[]>); // Add type to accumulator\n\n  // Days of week for sorting\n  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\n\n  // Remove Sidebar wrapper\n  return (\n    // <Sidebar> // Removed\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-2xl font-semibold text-text-primary\">Predictive Send-Time Optimization</h1>\n        </div>\n        <p className=\"text-text-secondary mb-6\">\n            Optimize email delivery times based on recipient engagement patterns.\n        </p>\n\n        {error && (\n          <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n        )}\n\n        <Card className=\"mb-6\">\n          <div className=\"p-6\">\n             <h2 className=\"text-lg font-semibold text-text-primary mb-4\">Run Optimization</h2>\n             <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n               {/* Use standard select element */}\n                <div>\n                    <label htmlFor=\"segmentId\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Segment (Optional)\n                    </label>\n                    <select\n                      id=\"segmentId\"\n                      name=\"segmentId\"\n                      value={selectedSegmentId}\n                      onChange={(e: ChangeEvent<HTMLSelectElement>) => setSelectedSegmentId(e.target.value)}\n                      className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                    >\n                        <option value=\"\">All Recipients</option>\n                        {segments.map((segment) => (\n                            <option key={segment.id} value={segment.id}>{segment.name}</option>\n                        ))}\n                    </select>\n                    <p className=\"mt-1 text-xs text-text-secondary\">Select a segment to optimize for, or leave blank for all.</p>\n                </div>\n\n                <div className=\"flex items-end\">\n                   <Button\n                    onClick={handleRunOptimization}\n                    disabled={loading || jobStatus === 'running'}\n                    className=\"w-full sm:w-auto\" // Adjust button width\n                  >\n                    {jobStatus === 'running' ? 'Optimization Running...' : loading ? 'Starting...' : 'Run Optimization'}\n                  </Button>\n                </div>\n              </div>\n\n             {/* Fix: Correct JSX conditional rendering */} \n             {jobStatus === 'running' && (\n                <Alert \n                  type=\"info\" \n                  message={`Optimization in progress. Status: ${jobStatus}. This may take a few minutes...`} \n                  className=\"mt-4\" \n                />\n             )}\n          </div>\n        </Card>\n\n        <Card>\n           <div className=\"p-6\">\n              <h2 className=\"text-lg font-semibold text-text-primary mb-4\">Optimal Send Times</h2>\n\n             {loading && optimalTimes.length === 0 ? (\n                <div className=\"flex justify-center py-8\">\n                   <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n                </div>\n             ) : optimalTimes.length === 0 ? (\n                <p className=\"text-sm text-text-secondary text-center py-4\">No optimal send times available. Run an optimization to generate recommendations.</p>\n             ) : (\n                <div className=\"space-y-6\">\n                  {daysOfWeek.map((day) => {\n                    const timesForDay = groupedOptimalTimes[day] || [];\n                    if (timesForDay.length === 0) return null;\n\n                    return (\n                      <div key={day} className=\"border-t border-gray-700 pt-4\">\n                        <h3 className=\"text-md font-medium text-text-primary mb-3\">{day}</h3>\n                        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n                          {timesForDay.map((time, index) => (\n                            <div key={index} className=\"bg-secondary-bg p-4 rounded-lg shadow-md\">\n                              <div className=\"flex justify-between items-start mb-2\">\n                                <div className=\"text-lg font-semibold text-primary\">\n                                  {time.time || 'Time N/A'}\n                                </div>\n                                <div className={`px-2 py-0.5 rounded-full text-xs font-medium capitalize ${ \n                                  time.confidence === 'high' ? 'bg-green-800 text-green-100' : \n                                  time.confidence === 'medium' ? 'bg-yellow-800 text-yellow-100' : \n                                  time.confidence === 'low' ? 'bg-red-800 text-red-100' :\n                                  'bg-gray-700 text-gray-300' \n                                }`}>\n                                  {time.confidence || 'unknown'}\n                                </div>\n                              </div>\n                              <div className=\"text-sm text-text-secondary mb-1\">\n                                {time.segment ? `Segment: ${time.segment}` : 'All Recipients'}\n                              </div>\n                              <div className=\"text-sm text-text-secondary mb-2\">\n                                Based on {time.dataPoints || '?'} data points\n                              </div>\n                              <div className=\"text-sm\">\n                                <span className=\"text-text-secondary\">Est. Open Rate: </span>\n                                <span className=\"font-medium text-text-primary\">{time.expectedOpenRate || 'N/A'}</span>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              )}\n           </div>\n        </Card>\n      </div>\n    // </Sidebar> // Removed\n  );\n};\n\nexport default SendTimeOptimization;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAEVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAE;AAC3C,MAAO,CAAAC,IAAI,KAAM,oBAAoB,CACrC;AACA,OACEC,cAAc,CACdC,eAAe,KACV,aAAa,CAEpB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAqBA,KAAM,CAAAC,oBAA8B,CAAGA,CAAA,GAAM,CAC3C,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGZ,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACa,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAU,IAAI,CAAC,CACrD,KAAM,CAACe,KAAK,CAAEC,QAAQ,CAAC,CAAGhB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACiB,eAAe,CAAEC,kBAAkB,CAAC,CAAGlB,QAAQ,CAAyB,IAAI,CAAC,CACpF,KAAM,CAACmB,SAAS,CAAEC,YAAY,CAAC,CAAGpB,QAAQ,CAAgB,IAAI,CAAC,CAAE;AACjE,KAAM,CAACqB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtB,QAAQ,CAAS,EAAE,CAAC,CACtE,KAAM,CAACuB,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACyB,UAAU,CAAEC,aAAa,CAAC,CAAG1B,QAAQ,CAAwB,IAAI,CAAC,CAAE;AAE3E;AACAD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4B,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnCb,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAACY,gBAAgB,CAAEC,aAAa,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAC1D3B,cAAc,CAAC4B,eAAe,CAAC,CAAC,CAChC3B,eAAe,CAAC4B,eAAe,CAAC,CAAC,CAClC,CAAC,CAEF,GAAIL,gBAAgB,CAACM,OAAO,CAAE,CAC5BV,WAAW,CAACW,KAAK,CAACC,OAAO,CAACR,gBAAgB,CAACS,IAAI,CAAC,CAAGT,gBAAgB,CAACS,IAAI,CAAG,EAAE,CAAC,CAChF,CAAC,IAAM,CACLrB,QAAQ,CAACsB,IAAI,EAAIA,IAAI,CAAG,GAAGA,IAAI,KAAKV,gBAAgB,CAACW,OAAO,EAAE,CAAGX,gBAAgB,CAACW,OAAO,EAAI,0BAA0B,CAAC,CAC1H,CAEA,GAAIV,aAAa,CAACK,OAAO,CAAE,CACzB;AACAtB,eAAe,CAACuB,KAAK,CAACC,OAAO,CAACP,aAAa,CAACQ,IAAI,CAAC,CAAGR,aAAa,CAACQ,IAAI,CAAG,EAAE,CAAC,CAC9E,CAAC,IAAM,CACLrB,QAAQ,CAACsB,IAAI,EAAIA,IAAI,CAAG,GAAGA,IAAI,KAAKT,aAAa,CAACU,OAAO,EAAE,CAAGV,aAAa,CAACU,OAAO,EAAI,+BAA+B,CAAC,CACvH3B,eAAe,CAAC,EAAE,CAAC,CAAE;AACvB,CAEF,CAAE,MAAO4B,GAAQ,CAAE,CACjBxB,QAAQ,CAACwB,GAAG,CAACD,OAAO,EAAI,+CAA+C,CAAC,CAC1E,CAAC,OAAS,CACRzB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDa,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA5B,SAAS,CAAC,IAAM,CACd,GAAI,CAACkB,eAAe,EAAIE,SAAS,GAAK,SAAS,CAAE,CAC7C,GAAIM,UAAU,CAAEgB,aAAa,CAAChB,UAAU,CAAC,CACzCC,aAAa,CAAC,IAAI,CAAC,CACnB,OAAQ;AACZ,CAEA,KAAM,CAAAgB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFC,OAAO,CAACC,GAAG,CAAC,0BAA0B3B,eAAe,CAAC4B,EAAE,KAAK,CAAC,CAC9D,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAzC,eAAe,CAAC0C,YAAY,CAAC9B,eAAe,CAAC4B,EAAE,CAAC,CACvE,GAAIC,QAAQ,CAACZ,OAAO,CAAE,CACpB,KAAM,CAAAc,aAAa,CAAGF,QAAQ,CAACT,IAAI,CAACY,MAAM,CAC1C7B,YAAY,CAAC4B,aAAa,CAAC,CAC3BL,OAAO,CAACC,GAAG,CAAC,eAAeI,aAAa,EAAE,CAAC,CAE3C;AACA,GAAIA,aAAa,GAAK,WAAW,EAAIA,aAAa,GAAK,QAAQ,CAAE,CAC/D,GAAIvB,UAAU,CAAEgB,aAAa,CAAChB,UAAU,CAAC,CACzCC,aAAa,CAAC,IAAI,CAAC,CACnBR,kBAAkB,CAAC,IAAI,CAAC,CAAE;AAE1B,GAAI8B,aAAa,GAAK,WAAW,CAAE,CAC9BL,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvD,KAAM,CAAAf,aAAa,CAAG,KAAM,CAAAxB,eAAe,CAAC4B,eAAe,CAAC,CAAC,CAC7D,GAAIJ,aAAa,CAACK,OAAO,CAAE,CACzBtB,eAAe,CAACuB,KAAK,CAACC,OAAO,CAACP,aAAa,CAACQ,IAAI,CAAC,CAAGR,aAAa,CAACQ,IAAI,CAAG,EAAE,CAAC,CAC9E,CAAC,IAAM,CACHrB,QAAQ,CAAC,6DAA6D,CAAC,CAC3E,CACL,CAAC,IAAM,CACFA,QAAQ,CAAC,0DAA0D,CAAC,CACzE,CACF,CACF,CAAC,IAAM,CACH2B,OAAO,CAAC5B,KAAK,CAAC,4BAA4B,CAAE+B,QAAQ,CAACP,OAAO,CAAC,CAC7D;AACA;AACA;AACA;AACJ,CACF,CAAE,MAAOC,GAAG,CAAE,CACZG,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,CAAEyB,GAAG,CAAC,CAC/C;AACA;AACA;AACA;AACF,CACF,CAAC,CAED;AACA,GAAI,CAACf,UAAU,CAAE,CACbkB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACxC,KAAM,CAAAM,UAAU,CAAGC,WAAW,CAACT,aAAa,CAAE,IAAI,CAAC,CAAE;AACrDhB,aAAa,CAACwB,UAAU,CAAC,CAC3B,CAEA;AACA,MAAO,IAAM,CACX,GAAIzB,UAAU,CAAE,CACdkB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7CH,aAAa,CAAChB,UAAU,CAAC,CAC3B,CACF,CAAC,CACH,CAAC,CAAE,CAACR,eAAe,CAAEE,SAAS,CAAC,CAAC,CAAE;AAElC;AACA,KAAM,CAAAiC,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxCpC,QAAQ,CAAC,IAAI,CAAC,CACdI,YAAY,CAAC,IAAI,CAAC,CAClBF,kBAAkB,CAAC,IAAI,CAAC,CACxBJ,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAgC,QAAQ,CAAG,KAAM,CAAAzC,eAAe,CAACgD,mBAAmB,CACxDhC,iBAAiB,EAAIiC,SACvB,CAAC,CAED,GAAIR,QAAQ,CAACZ,OAAO,CAAE,CACpBhB,kBAAkB,CAAC4B,QAAQ,CAACT,IAAI,CAAC,CAAE;AACnCjB,YAAY,CAAC,SAAS,CAAC,CAAE;AACzBuB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEE,QAAQ,CAACT,IAAI,CAAC,CACzD,CAAC,IAAM,CACLrB,QAAQ,CAAC8B,QAAQ,CAACP,OAAO,EAAI,kCAAkC,CAAC,CAClE,CACF,CAAE,MAAOC,GAAQ,CAAE,CACjBxB,QAAQ,CAACwB,GAAG,CAACD,OAAO,EAAI,+CAA+C,CAAC,CAC1E,CAAC,OAAS,CACRzB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyC,mBAAmB,CAAG5C,YAAY,CAAC6C,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAK,CAC7D,KAAM,CAAAC,GAAG,CAAGD,IAAI,CAACE,SAAS,EAAI,SAAS,CACvC,GAAI,CAACH,GAAG,CAACE,GAAG,CAAC,CAAE,CACbF,GAAG,CAACE,GAAG,CAAC,CAAG,EAAE,CACf,CACAF,GAAG,CAACE,GAAG,CAAC,CAACE,IAAI,CAACH,IAAI,CAAC,CACnB,MAAO,CAAAD,GAAG,CACZ,CAAC,CAAE,CAAC,CAAkC,CAAC,CAAE;AAEzC;AACA,KAAM,CAAAK,UAAU,CAAG,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CAEjG;AACA,oBACE;AACErD,KAAA,QAAKsD,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzD,IAAA,QAAKwD,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDzD,IAAA,OAAIwD,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,mCAAiC,CAAI,CAAC,CAC5F,CAAC,cACNzD,IAAA,MAAGwD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,uEAExC,CAAG,CAAC,CAEHjD,KAAK,eACJR,IAAA,CAACN,KAAK,EAACgE,IAAI,CAAC,OAAO,CAAC1B,OAAO,CAAExB,KAAM,CAACmD,OAAO,CAAEA,CAAA,GAAMlD,QAAQ,CAAC,IAAI,CAAE,CAAC+C,SAAS,CAAC,MAAM,CAAE,CACtF,cAEDxD,IAAA,CAACJ,IAAI,EAAC4D,SAAS,CAAC,MAAM,CAAAC,QAAA,cACpBvD,KAAA,QAAKsD,SAAS,CAAC,KAAK,CAAAC,QAAA,eACjBzD,IAAA,OAAIwD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAClFvD,KAAA,QAAKsD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEnDvD,KAAA,QAAAuD,QAAA,eACIzD,IAAA,UAAO4D,OAAO,CAAC,WAAW,CAACJ,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,oBAE1F,CAAO,CAAC,cACRvD,KAAA,WACEoC,EAAE,CAAC,WAAW,CACduB,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAEhD,iBAAkB,CACzBiD,QAAQ,CAAGC,CAAiC,EAAKjD,oBAAoB,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACtFN,SAAS,CAAC,4KAA4K,CAAAC,QAAA,eAEpLzD,IAAA,WAAQ8D,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,gBAAc,CAAQ,CAAC,CACvCzC,QAAQ,CAACkD,GAAG,CAAEC,OAAO,eAClBnE,IAAA,WAAyB8D,KAAK,CAAEK,OAAO,CAAC7B,EAAG,CAAAmB,QAAA,CAAEU,OAAO,CAACN,IAAI,EAA5CM,OAAO,CAAC7B,EAA6C,CACrE,CAAC,EACE,CAAC,cACTtC,IAAA,MAAGwD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,2DAAyD,CAAG,CAAC,EAC5G,CAAC,cAENzD,IAAA,QAAKwD,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC5BzD,IAAA,CAACL,MAAM,EACNyE,OAAO,CAAEvB,qBAAsB,CAC/BwB,QAAQ,CAAE/D,OAAO,EAAIM,SAAS,GAAK,SAAU,CAC7C4C,SAAS,CAAC,kBAAmB;AAAA,CAAAC,QAAA,CAE5B7C,SAAS,GAAK,SAAS,CAAG,yBAAyB,CAAGN,OAAO,CAAG,aAAa,CAAG,kBAAkB,CAC7F,CAAC,CACN,CAAC,EACH,CAAC,CAGNM,SAAS,GAAK,SAAS,eACrBZ,IAAA,CAACN,KAAK,EACJgE,IAAI,CAAC,MAAM,CACX1B,OAAO,CAAE,qCAAqCpB,SAAS,kCAAmC,CAC1F4C,SAAS,CAAC,MAAM,CACjB,CACH,EACC,CAAC,CACF,CAAC,cAEPxD,IAAA,CAACJ,IAAI,EAAA6D,QAAA,cACFvD,KAAA,QAAKsD,SAAS,CAAC,KAAK,CAAAC,QAAA,eACjBzD,IAAA,OAAIwD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,CAEpFnD,OAAO,EAAIF,YAAY,CAACkE,MAAM,GAAK,CAAC,cAClCtE,IAAA,QAAKwD,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACtCzD,IAAA,QAAKwD,SAAS,CAAC,0EAA0E,CAAM,CAAC,CAC9F,CAAC,CACLpD,YAAY,CAACkE,MAAM,GAAK,CAAC,cAC1BtE,IAAA,MAAGwD,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,mFAAiF,CAAG,CAAC,cAEjJzD,IAAA,QAAKwD,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBF,UAAU,CAACW,GAAG,CAAEd,GAAG,EAAK,CACvB,KAAM,CAAAmB,WAAW,CAAGvB,mBAAmB,CAACI,GAAG,CAAC,EAAI,EAAE,CAClD,GAAImB,WAAW,CAACD,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEzC,mBACEpE,KAAA,QAAesD,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eACtDzD,IAAA,OAAIwD,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAEL,GAAG,CAAK,CAAC,cACrEpD,IAAA,QAAKwD,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CACjFc,WAAW,CAACL,GAAG,CAAC,CAACf,IAAI,CAAEqB,KAAK,gBAC3BtE,KAAA,QAAiBsD,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACnEvD,KAAA,QAAKsD,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDzD,IAAA,QAAKwD,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChDN,IAAI,CAACA,IAAI,EAAI,UAAU,CACrB,CAAC,cACNnD,IAAA,QAAKwD,SAAS,CAAE,2DACdL,IAAI,CAACsB,UAAU,GAAK,MAAM,CAAG,6BAA6B,CAC1DtB,IAAI,CAACsB,UAAU,GAAK,QAAQ,CAAG,+BAA+B,CAC9DtB,IAAI,CAACsB,UAAU,GAAK,KAAK,CAAG,yBAAyB,CACrD,2BAA2B,EAC1B,CAAAhB,QAAA,CACAN,IAAI,CAACsB,UAAU,EAAI,SAAS,CAC1B,CAAC,EACH,CAAC,cACNzE,IAAA,QAAKwD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC9CN,IAAI,CAACgB,OAAO,CAAG,YAAYhB,IAAI,CAACgB,OAAO,EAAE,CAAG,gBAAgB,CAC1D,CAAC,cACNjE,KAAA,QAAKsD,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAC,WACvC,CAACN,IAAI,CAACuB,UAAU,EAAI,GAAG,CAAC,cACnC,EAAK,CAAC,cACNxE,KAAA,QAAKsD,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBzD,IAAA,SAAMwD,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,cAC7DzD,IAAA,SAAMwD,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAEN,IAAI,CAACwB,gBAAgB,EAAI,KAAK,CAAO,CAAC,EACpF,CAAC,GAvBEH,KAwBL,CACN,CAAC,CACC,CAAC,GA9BEpB,GA+BL,CAAC,CAEV,CAAC,CAAC,CACC,CACN,EACC,CAAC,CACH,CAAC,EACJ,CACP;AAAA,EAEJ,CAAC,CAED,cAAe,CAAAjD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}