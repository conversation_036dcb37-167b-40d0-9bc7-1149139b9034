{"version": 3, "file": "static/js/297.cb23e281.chunk.js", "mappings": "8KA0BA,MAAMA,GAAaC,EAAAA,EAAAA,aACjB,CAAAC,EAAkEC,KAAS,IAA1E,YAAEC,EAAc,GAAE,YAAEC,EAAc,GAAE,OAAEC,EAAM,OAAEC,EAAS,QAAQL,EAC9D,MAAMM,GAAYC,EAAAA,EAAAA,QAAuB,MACnCC,GAAeD,EAAAA,EAAAA,QAAsB,MAkT3C,OA/SAE,EAAAA,EAAAA,YAAU,KACR,GAAKH,EAAUI,QAAf,CAGIF,EAAaE,UACfC,QAAQC,IAAI,qDACZJ,EAAaE,QAAQG,UACrBL,EAAaE,QAAU,MAGzBC,QAAQC,IAAI,+CAAgD,CAC1DE,UAAWZ,EACXa,YAAuB,OAAXb,QAAW,IAAXA,OAAW,EAAXA,EAAac,SAAU,EACnCC,UAAWd,EACXe,YAAuB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAaa,SAAU,IAGrC,IACE,MAAMG,EAASC,EAAAA,GAASC,KAAK,CAC3BC,UAAWhB,EAAUI,QACrBa,aAAa,EACblB,OAAQmB,OAAOnB,GACfoB,MAAO,OACPC,gBAAgB,EAChBC,QAAS,CAACC,KACVC,YAAa,CACX,gBAAiB,CAGdC,cAAc,EACdC,aAAa,MAQpB,IAAKZ,EAEH,YADAR,QAAQqB,MAAM,4CAoFhB,IAAIC,EAhFJzB,EAAaE,QAAUS,EAGlBA,EAAOe,SAASC,IAAI,mBACvBxB,QAAQC,IAAI,0DACZO,EAAOe,SAASE,IAAI,gBAAiB,CACnCC,IAAMlB,IACJ,MAAMmB,EAAOnB,EAAOoB,UAEpB,MAAO,CACLD,KAAMA,EACNE,KAAMF,EACP,KAKFnB,EAAOe,SAASC,IAAI,kBACvBxB,QAAQC,IAAI,yDACZO,EAAOe,SAASE,IAAI,eAAgB,CAClCC,IAAMlB,GACGA,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,kBAMhDC,YAAW,KACT,GAAKnC,EAAaE,QAMlB,GAAKF,EAAaE,QAAQkC,cAK1B,IAEE,GAAI1C,EAAa,CACfS,QAAQC,IAAI,qCAAsCV,EAAY2C,UAAU,EAAG,KAAO,OAClF,IACErC,EAAaE,QAAQkC,cAAc1C,GACnCS,QAAQC,IAAI,gDACd,CAAE,MAAOkC,GACPnC,QAAQqB,MAAM,2CAA4Cc,GAEtD3C,IACFQ,QAAQC,IAAI,qDACZJ,EAAaE,QAAQkC,cAAczC,GACnCQ,QAAQC,IAAI,qDAEhB,CACF,MAAWT,GACTQ,QAAQC,IAAI,yDAA0DT,EAAY0C,UAAU,EAAG,KAAO,OACtGrC,EAAaE,QAAQkC,cAAczC,GACnCQ,QAAQC,IAAI,mDAGZD,QAAQC,IAAI,8DACZJ,EAAaE,QAAQkC,cAAc,oVAYvC,CAAE,MAAOZ,GACPrB,QAAQqB,MAAM,+CAAgDA,EAChE,MAzCErB,QAAQqB,MAAM,oEANdrB,QAAQqB,MAAM,2DA+ChB,GACC,KAIH,IAAIe,GAAW,EAmEf,OAhEA5B,EAAO6B,GAAG,uBAAuB,KAC3B5C,GAAUe,IAAW4B,IAEnBd,GAAagB,aAAahB,GAG9BA,EAAcU,YAAW,KACvB,IACEI,GAAW,EAEX,IAAIG,EAAY,GACZC,EAAY,GAEhB,IAEE,MAAMC,EAAWjC,EAAOkC,WAAW,iBAC/BD,GAAgC,kBAAbA,IACrBF,EAAYE,EAASd,MAAQ,GAC7Ba,EAAYC,EAASZ,MAAQ,GAC7B7B,QAAQC,IAAI,qDAEhB,CAAE,MAAO0C,GACN3C,QAAQ4C,KAAK,0DAA2DD,EAC3E,CAGA,IAAKJ,IAAcC,EAAW,CAC1BD,EAAY/B,EAAOoB,WAAa,GAChC,IAEEY,EAAYhC,EAAOkC,WAAW,iBAAmB,EACnD,CAAE,MAAMG,GACL7C,QAAQ4C,KAAK,iCAAkCC,EAClD,CAEKL,IACHA,EAAYhC,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,IAEpE/B,QAAQC,IAAI,uDAChB,CAGA,IAAKsC,EAAUO,OAGb,OAFA9C,QAAQC,IAAI,4DACZmC,GAAW,GAIbpC,QAAQC,IAAI,6CAGZR,EAAO8C,EAAWC,GAClBxC,QAAQC,IAAI,yCAEf,CAAE,MAAOoB,GACLrB,QAAQqB,MAAM,uCAAwCA,EAC1D,CAAC,QACGe,GAAW,CACf,IACE,KACL,IAIK,KAEL,GADId,GAAagB,aAAahB,GAC1BzB,EAAaE,QAAS,CAEvB,IACEF,EAAaE,QAAQG,SACvB,CAAE,MAAO6C,GACP/C,QAAQqB,MAAM,4CAA6C0B,EAC7D,CACAlD,EAAaE,QAAU,IAC1B,EAEJ,CAAE,MAAOiD,GACPhD,QAAQqB,MAAM,4DAA6D2B,EAC7E,CA5M8B,CA4M9B,GACC,CAACzD,EAAaC,EAAaE,EAAQD,KAGtCwD,EAAAA,EAAAA,qBAAoB3D,GAAK,MACvB4D,KAAMC,UACJ,IAAIC,EAAgB,CAAEzB,KAAM,GAAIE,KAAM,IACtC,GAAIhC,EAAaE,QACd,IAEI,MAAMS,EAASX,EAAaE,QAC5B,IAAIS,EAAOe,SAASC,IAAI,iBAetB,MAAM,IAAI6B,MAAM,uCAfwB,CAExC,MAAMC,EAAS9C,EAAOkC,WAAW,iBAEjC,KAAIY,GAA4B,kBAAXA,GAAuB,SAAUA,GAAU,SAAUA,GAOvE,MAFAtD,QAAQ4C,KAAK,6EAEP,IAAIS,MAAM,yCANjBD,EAAczB,KAAO2B,EAAO3B,MAAQ,GACpCyB,EAAcvB,KAAOyB,EAAOzB,MAAQ,GACpC7B,QAAQC,IAAI,qDAAsD,CAAE0B,KAAMyB,EAAczB,KAAKO,UAAU,EAAE,IAAI,MAAOL,KAAMuB,EAAcvB,KAAKK,UAAU,EAAE,IAAI,OAMjK,CAIJ,CAAE,MAAOS,GACL3C,QAAQ4C,KAAK,+DAAgED,GAC7E,IAEE,MAAMnC,EAASX,EAAaE,QACtBwD,EAAU/C,EAAOoB,WAAa,GACpC,IAAI4B,EAAgB,GAIlBA,EADEhD,EAAOe,SAASC,IAAI,gBACNhB,EAAOkC,WAAW,iBAAmB,GAGrClC,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,GAGpEwB,GAAWC,GACXJ,EAAczB,KAAO4B,EACrBH,EAAcvB,KAAO2B,GAAiBD,EACtCvD,QAAQC,IAAI,oEAEZD,QAAQqB,MAAM,0EAEpB,CAAE,MAAOoC,GACNzD,QAAQqB,MAAM,8DAA+DoC,EAChF,CACJ,MAEDzD,QAAQqB,MAAM,oDAOhB,SAHM,IAAIqC,SAAQC,GAAW3B,WAAW2B,EAAS,OAG7C9D,EAAaE,UAAYqD,EAAcvB,KAAKiB,OAAQ,CACpD9C,QAAQC,IAAI,8DACZ,IACK,MAAMO,EAASX,EAAaE,QAE5B,IAAI6D,EAAyB,GACzBpD,EAAOe,SAASC,IAAI,kBACtBoC,EAAyBpD,EAAOkC,WAAW,iBAGxCkB,IACHA,EAAyBpD,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,IAG7E6B,EAAuBd,QACvB9C,QAAQC,IAAI,8DACZmD,EAAcvB,KAAO+B,IAEtB5D,QAAQC,IAAI,4DAERmD,EAAczB,OAASyB,EAAcvB,OACvCuB,EAAcvB,KAAOuB,EAAczB,MAG7C,CAAE,MAAOkC,GACL7D,QAAQqB,MAAM,iEAAkEwC,EACpF,CACJ,CAGA,OAAOT,CAAa,EAErBU,UAAWA,IAAMjE,EAAaE,aAG1BgE,EAAAA,EAAAA,KAAA,OAAKzE,IAAKK,EAAWqE,MAAO,CAAEtE,OAAQA,IAAY,IAK7DP,EAAW8E,YAAc,aAEzB,S,uKC/TA,MAwiCC,EAxiCgCC,KAAO,IAADC,EACrC,MAAM,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OAGVC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAS,KAC1CC,EAASC,IAAcF,EAAAA,EAAAA,UAAS,KAChCG,EAAUC,IAAeJ,EAAAA,EAAAA,WAAa,OAAJR,QAAI,IAAJA,OAAI,EAAJA,EAAMa,OAAQ,KAChDC,EAAWC,IAAgBP,EAAAA,EAAAA,UACP,YAArB,OAAJR,QAAI,IAAJA,GAAY,QAARD,EAAJC,EAAMgB,cAAM,IAAAjB,OAAR,EAAJA,EAAckB,QAAsB,WAAWjB,EAAKgB,OAAOH,OAAS,KAE/DK,EAASC,IAAcX,EAAAA,EAAAA,UAAS,KAGhCvD,EAAOmE,IAAYZ,EAAAA,EAAAA,UAAS,KAC5Ba,EAASC,IAAcd,EAAAA,EAAAA,UAAS,KAChCe,EAASC,IAAchB,EAAAA,EAAAA,WAAS,IAChCiB,EAAiBC,IAAsBlB,EAAAA,EAAAA,WAAS,IAChDmB,EAAMC,IAAWpB,EAAAA,EAAAA,UAAS,IAG1BqB,EAAcC,IAAmBtB,EAAAA,EAAAA,UAAiB,IAClDuB,EAAeC,IAAoBxB,EAAAA,EAAAA,WAA2C,KACnF,MAAMyB,EAAQC,aAAaC,QAAQ,0CACnC,GAAIF,EACF,IACE,MAAMG,EAAMC,KAAKC,MAAML,GACvB,GAAIM,MAAMC,QAAQJ,IAAuB,KAAfA,EAAInG,OAC5B,OAAOmG,EAAIK,KAAKC,IAAS,CACvBnF,KAAMmF,EAAKnF,MAAQ,GACnBE,KAAMiF,EAAKjF,MAAQ,MAGzB,CAAE,MACA,CAGJ,OAAO8E,MAAMI,KAAK,CAAE1G,OAAQ,KAAM,MAASsB,KAAM,GAAIE,KAAM,MAAM,KAI5DmF,EAAkBC,IAAuBrC,EAAAA,EAAAA,UAG7C,CACDsC,UAAW,CAAC,IACZC,KAAM,WAIDC,EAAiBC,IAAsBzC,EAAAA,EAAAA,UAAc,OACrD0C,EAAoBC,IAAyB3C,EAAAA,EAAAA,WAAS,IACtD4C,EAAeC,IAAoB7C,EAAAA,EAAAA,UAAgB,KACnD8C,EAAkBC,IAAuB/C,EAAAA,EAAAA,WAAS,IAClDgD,EAAkBC,IAAuBjD,EAAAA,EAAAA,UAAqB,OAG9DkD,EAAoBC,IAAyBnD,EAAAA,EAAAA,UAA0B,UACvEoD,EAAmBC,KAAwBrD,EAAAA,EAAAA,WAAiB,IACpD,IAAIsD,KAAKA,KAAKC,MAAQ,MACvBC,cAAcC,MAAM,EAAG,OAE9BC,GAAYC,KAAiB3D,EAAAA,EAAAA,UAAgE,KAC7F4D,GAAiBC,KAAsB7D,EAAAA,EAAAA,UAAS,CAAE8D,MAAO,GAAIzD,KAAM,MACnE0D,GAAUC,KAAehE,EAAAA,EAAAA,UAAwD,KACjFiE,GAAoBC,KAAyBlE,EAAAA,EAAAA,UAAmB,KAChEmE,GAAiBC,KAAsBpE,EAAAA,EAAAA,WAAS,GAGjDjF,IAAYC,EAAAA,EAAAA,QAAsB,OAGjCqJ,GAAoBC,KAAyBtE,EAAAA,EAAAA,WAAmB,KAErE,MAAMuE,EAA0B,GAC1B9C,EAAQC,aAAaC,QAAQ,0CACnC,IAAI6C,EAAkBzC,MAAMI,KAAK,CAAE1G,OAAQ,KAAM,MAASsB,KAAM,GAAIE,KAAM,OAC1E,GAAIwE,EACF,IACE,MAAMG,EAAMC,KAAKC,MAAML,GACnBM,MAAMC,QAAQJ,IAAuB,KAAfA,EAAInG,SAC5B+I,EAAkB5C,EAAIK,KAAKC,IAAS,CAAQnF,KAAMmF,EAAKnF,MAAQ,GAAIE,KAAMiF,EAAKjF,MAAQ,OAE1F,CAAE,MAAO,CAOX,OALAuH,EAAgBC,SAAQ,CAACX,EAAOY,KAC1BZ,EAAM7G,MAAQ6G,EAAM7G,KAAKiB,QAC3BqG,EAAcI,KAAKD,EACrB,IAEKH,CAAa,IAKhBK,GADc,IAAIC,gBAAgBnF,EAASoF,QAClBC,IAAI,eACnC7J,EAAAA,EAAAA,YAAU,KACJ0J,IAAYI,GAAcJ,GAAW,GACxC,CAACA,KAGJ,MAAMI,GAAgBzG,UACpB2C,GAAmB,GACnB,IACE,MAAM+D,QAAYC,EAAAA,GAA8BC,gBAAgBC,GAChE,GAAIH,EAAIpE,SAAWoE,EAAII,SAAU,CAC/B,MAAMC,EAAOL,EAAII,SACZvF,GAAcC,EAAgB,qBAAqBuF,EAAKjF,QAC7D,MAAMkF,EAAU,IAAIhE,GACpBgE,EAAQ,GAAK,CACXxI,KAAMuI,EAAKE,aAAe,GAC1BvI,KAAMqI,EAAKG,SAAW,IAExBjE,EAAiB+D,EACnB,MACE3E,EAAS,2BAEb,CAAE,MACAA,EAAS,2BACX,CAAC,QACCM,GAAmB,EACrB,GAIIwE,GAAiBA,CAAC3I,EAAcE,KACpC7B,QAAQC,IAAI,uCAAuCgG,SACnDjG,QAAQC,IAAI,iBAAkB0B,EAAOA,EAAKO,UAAU,EAAG,KAAO,MAAQ,WACtElC,QAAQC,IAAI,iBAAkB4B,EAAOA,EAAKK,UAAU,EAAG,KAAO,MAAQ,WACtE,MAAMiI,EAAU,IAAIhE,GACdoE,EAAetE,EAAe,EACpCkE,EAAQI,GAAgB,CAAE5I,OAAME,QAChCuE,EAAiB+D,GACjBnK,QAAQC,IAAI,8BAA+BkK,GAC3C7D,aAAakE,QAAQ,yCAA0C/D,KAAKgE,UAAUN,IAG1EtI,GAAQA,EAAKiB,QACfoG,IAAsBwB,GACfA,EAAKC,SAASJ,GAIZG,EAFE,IAAIA,EAAMH,GAAcK,MAAK,CAACC,EAAGC,IAAMD,EAAIC,MAItD9K,QAAQC,IAAI,uBAAuBsK,6BAGnCrB,IAAsBwB,GAAQA,EAAKK,QAAOC,GAAKA,IAAMT,MACrDvK,QAAQC,IAAI,2BAA2BsK,mCACzC,GAIFzK,EAAAA,EAAAA,YAAU,KACJwH,IACFK,GAAoB,GACpBmC,EAAAA,GAA8BmB,kBAC3BC,MAAKC,GAAK1D,EAAiB0D,EAAEC,MAAQ,MACrCC,MAAMrL,QAAQqB,OACdiK,SAAQ,IAAM3D,GAAoB,KACvC,GACC,CAACL,IAEJ,MAwBMiE,GAAoBA,KAQxB,GAPAvL,QAAQC,IAAI,mCAAoC2H,EAAmB,CACjEoC,GAAIpC,EAAiBoC,IAAMpC,EAAiB4D,IAC5CvG,KAAM2C,EAAiB3C,KACvB9E,UAAWyH,EAAiBwC,YAC5BqB,aAAc7D,EAAiByC,SAC7B,wBAEAzC,EAAkB,CACpB,MAAMuC,EAAU,IAAIhE,GACpBgE,EAAQlE,EAAe,GAAK,CAC1BtE,KAAMiG,EAAiBwC,aAAe,GACtCvI,KAAM+F,EAAiByC,SAAW,IAGpCrK,QAAQC,IAAI,kCAAmC,CAC7CyL,WAAYzF,EAAe,EAC3B9F,UAAWgK,EAAQlE,EAAe,GAAGtE,KACrCrB,UAAW6J,EAAQlE,EAAe,GAAGpE,KACrCzB,WAAY+J,EAAQlE,EAAe,GAAGtE,KAAKtB,OAC3CE,WAAY4J,EAAQlE,EAAe,GAAGpE,KAAKxB,SAI7C+F,EAAiB+D,GACjB7D,aAAakE,QAAQ,yCAA0C/D,KAAKgE,UAAUN,IAG9EtC,EAAoB,MACpBN,GAAsB,GAItBvF,YAAW,KAGTkE,GAAiB,GAGjBlE,YAAW,KACTkE,EAAgBD,EAAa,GAC5B,GAAG,GACL,IACL,GAII0F,GAAuBxI,UAC3B,IAAKuB,IAAiBG,IAAYE,IAAaG,EAE7C,YADAM,EAAS,sCAIX,GAA2B,IADPW,EAAc4E,QAAO5I,GAAKA,EAAEN,KAAKiB,SACrCzC,OAIhB,GAA0B,IAAtBiI,GAAWjI,OAAf,CAKAuF,GAAW,GACX,IAAK,IAADgG,EAEF,MAAMC,EAAqB1F,EAAc4E,QAAO,CAACrC,EAAOY,IACtDL,GAAmB0B,SAASrB,IAAUZ,EAAM7G,MAAQ6G,EAAM7G,KAAKiB,SAIjE,GAAkC,IAA9B+I,EAAmBxL,OAGrB,OAFAmF,EAAS,0EACTI,GAAW,GAGb,GAA0B,IAAtB0C,GAAWjI,OAGb,OAFAmF,EAAS,0CACTI,GAAW,GAMb,MAAMkG,EAAoB,CACxB7G,KAAMP,EACNG,UACAE,WACAG,YACAI,QAASA,GAAWJ,EACpB6G,UAAkC,UAAvBjE,KACgB,UAAvBA,GAAkC,CAAEkE,aAAc,IAAI9D,KAAKF,GAAmBI,eAClF6D,cAAe3D,GACf4D,OAAY,OAAJ9H,QAAI,IAAJA,OAAI,EAAJA,EAAM4F,GACd3E,OAAQ,QAER8G,aAAkC,QAArBP,EAAAC,EAAmB,UAAE,IAAAD,OAAA,EAArBA,EAAuB/J,OAAQ,GAE5CsE,cAAe0F,EAAmBhF,KAAI1E,IAAC,CAAOR,KAAMQ,EAAER,KAAME,KAAMM,EAAEN,UAIlEgK,EAAmBxL,OAAS,IAC9ByL,EAAaM,SAAW,CACtBjF,KAAMH,EAAiBG,KAEvBkF,eAAgBrF,EAAiBE,UAAUmB,MAAM,EAAGwD,EAAmBxL,OAAS,GAC7EwG,KAAIyF,IAAC,CAAOC,MAAOD,EAAGnF,KAAMH,EAAiBG,WAIpDnH,QAAQC,IAAI,2CAA4C6L,GAGxD,MAAMjC,QAAY2C,EAAAA,EAAYC,eAAeX,GAEvCY,GAAgB,OAAH7C,QAAG,IAAHA,OAAG,EAAHA,EAAK2B,OAAU,OAAH3B,QAAG,IAAHA,OAAG,EAAHA,EAAKG,IACpChK,QAAQC,IAAI,4BAA6B4J,EAAK,gBAAiB6C,GAE/DhH,EAAkC,UAAvBoC,EACP,0BAA0B,IAAII,KAAKF,GAAmB2E,mBACtD,mDACJrG,aAAasG,WAAW,0CAExBtG,aAAasG,WAAW,0BACxBC,eAAerC,QAAQ,kBAAmB,QAC1CxI,YAAW,KAEL0K,EAAYlI,EAAS,cAAckI,cAErC1M,QAAQ4C,KAAK,+DACb4B,EAAS,cACX,GACC,KACL,CAAE,MAAOsI,GAAW,IAADC,EAAAC,EACjBxH,GAAqB,QAAZuH,EAAAD,EAAIG,gBAAQ,IAAAF,GAAM,QAANC,EAAZD,EAAc3B,YAAI,IAAA4B,OAAN,EAAZA,EAAoBE,UAAW,4BAC1C,CAAC,QACCtH,GAAW,EACb,CA7EA,MAFEJ,EAAS,0CAJTA,EAAS,wDAmFX,EAGI2H,GAAwBA,MACxBzI,GAAgBG,GAAWE,GAAYG,GAAaI,IACtDgB,aAAakE,QAAQ,yBACnB/D,KAAKgE,UAAU,CAAE/F,eAAcG,UAASE,WAAUG,YAAWI,aAEjEd,EAAS,mBAAmB,GAG9B1E,EAAAA,EAAAA,YAAU,KACR,MAAMsN,EAAQ9G,aAAaC,QAAQ,0BACnC,GAAI6G,EAAO,CACT,IACE,MAAMd,EAAI7F,KAAKC,MAAM0G,GACrBzI,EAAgB2H,EAAE5H,cAAgB,IAClCI,EAAWwH,EAAEzH,SAAW,IACxBG,EAAYsH,EAAEvH,WAAgB,OAAJX,QAAI,IAAJA,OAAI,EAAJA,EAAMa,OAAQ,IACxCE,EAAamH,EAAEpH,WAAa,IAC5BK,EAAW+G,EAAEhH,SAAW,GAC1B,CAAE,MAAO,CACTgB,aAAasG,WAAW,yBAC1B,IACC,CAACxI,KAIJtE,EAAAA,EAAAA,YAAU,KACR,GAAa,IAATiG,EAAY,CAEd,MAAMsH,EAAmBlH,EAAcmH,QAAO,CAACC,EAAO7E,EAAOY,IACvDL,GAAmB0B,SAASrB,IAAUZ,EAAM7G,MAAQ6G,EAAM7G,KAAKiB,OAC1DyK,EAAQ,EAEVA,GACN,GAEHvN,QAAQC,IAAI,0CAA2CoN,GAGrDpG,EADEoG,EAAmB,EACD3C,IAAI,IACnBA,EAEHxD,UAAWP,MAAM0G,EAAmB,GAAGG,KAAK9C,EAAKxD,UAAU,IAAM,MAI/CwD,IAAI,IAAUA,EAAMxD,UAAW,KAEvD,IACC,CAACnB,EAAMI,EAAe8C,MAqBzBnJ,EAAAA,EAAAA,YAAU,KAnBYqD,WACpB,GAAa,IAAT4C,EAAY,CACdiD,IAAmB,GACnB,IAAK,IAADyE,EACF,MAAMtC,QAAUuC,EAAAA,EAAI/D,IAAI,aACxB,IAAIgE,EAAOhH,MAAMC,QAAQuE,EAAEC,MAAQD,EAAEC,MAAa,QAANqC,EAAAtC,EAAEC,YAAI,IAAAqC,OAAA,EAANA,EAAQrC,OAAQ,GAC5DxC,GAAY+E,EAAK9G,KAAK+G,IAAM,CAC1B5D,GAAI4D,EAAE5D,IAAM4D,EAAEpC,KAAO3K,OAAOgN,KAAKC,UACjCpF,MAAOkF,EAAElF,MACTzD,KAAM2I,EAAE3I,MAAQ2I,EAAEG,UAAY,OAElC,CAAE,MACAvI,EAAS,0BACX,CAAC,QACCwD,IAAmB,EACrB,CACF,GAIAgF,EAAe,GACd,CAACjI,IAEJ,MAAMkI,GAAqBA,KACpBzF,GAAgBE,MAETJ,GAAW4F,MAAK/C,GAAKA,EAAEzC,QAAUF,GAAgBE,QAI3DlD,EAAS,6BAHT+C,GAAc,IAAID,GAAY,CAAE0B,GAAI,QAASxB,MAC7CC,GAAmB,CAAEC,MAAO,GAAIzD,KAAM,MAHtCO,EAAS,kCAOXxD,YAAW,IAAMwD,EAAS,KAAK,IAAK,EAGhC2I,GAAsBA,KAC1B,MAAMC,EAAQzF,GAASoC,QAAO6C,GAC5B/E,GAAmB8B,SAASiD,EAAE5D,MAAQ1B,GAAW4F,MAAK/C,GAAKA,EAAEzC,QAAUkF,EAAElF,UAE3EH,GAAc,IAAID,MAAe8F,IACjCtF,GAAsB,GAAG,EAErBuF,GAA0BlM,GAC9B2G,GAAsBnC,MAAMI,KAAK5E,EAAEmM,OAAOC,iBAAiBC,GAAKA,EAAEC,SAilBnE,OACEC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACE7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,4CAA2CD,SAAC,wBACzDvN,IAAS0C,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,iEAAgED,UAAC7K,EAAAA,EAAAA,KAAA,KAAA6K,SAAIvN,MAC7FoE,IAAW1B,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,uEAAsED,UAAC7K,EAAAA,EAAAA,KAAA,KAAA6K,SAAInJ,MACrGI,IAAmB9B,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,2BAA0BD,SAAC,yBAG9D7K,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,OAAMD,UACnB7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,+GAA8GD,SACzH,CAAC,EAAG,EAAG,EAAG,EAAG,GAAG/H,KAAIiI,IACnB/K,EAAAA,EAAAA,KAAA,MAAY8K,UAAW,+BAA+BC,IAAM/I,EAAO,uCAAyC,MAAM+I,EAAI,EAAI,yLAA6L,KAAKF,UAC1TF,EAAAA,EAAAA,MAAA,QAAMG,UAAW,8FAA6FC,EAAI/I,EAAO,uCAAyC,IAAK6I,SAAA,CACpKE,EAAI/I,IACHhC,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,mCAAmC,cAAY,OAAOE,MAAM,6BAA6BvB,KAAK,eAAewB,QAAQ,YAAWJ,UAC7I7K,EAAAA,EAAAA,KAAA,QAAMuI,EAAE,mKAGXwC,IAAM/I,IAAQhC,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,OAAMD,SAAEE,IACtCA,EAAI/I,IAAQhC,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,OAAMD,SAAEE,IACpC,CAAC,UAAW,UAAW,WAAY,aAAc,UAAUA,EAAE,OATzDA,UAiBf/K,EAAAA,EAAAA,KAACkL,EAAAA,EAAI,CAACJ,UAAU,OAAMD,SA3cDM,MAAO,IAADC,EAAAC,EAC9B,OAAQrJ,GACN,KAAK,EAAG,OACN2I,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EACxB7K,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CAACrF,GAAG,eAAe/E,KAAK,eAAeqK,MAAM,gBAAgBb,MAAO/J,EAAc6K,SAAUpN,GAAKwC,EAAgBxC,EAAEmM,OAAOG,OAAQe,UAAQ,KAChJzL,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CAACrF,GAAG,UAAU/E,KAAK,UAAUqK,MAAM,gBAAgBb,MAAO5J,EAAS0K,SAAUpN,GAAK2C,EAAW3C,EAAEmM,OAAOG,OAAQe,UAAQ,KAC5Hd,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wCAAuCD,SAAA,EACpD7K,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CAACrF,GAAG,WAAW/E,KAAK,WAAWqK,MAAM,YAAYb,MAAO1J,EAAUwK,SAAUpN,GAAK6C,EAAY7C,EAAEmM,OAAOG,OAAQe,UAAQ,KAC5HzL,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CACJrF,GAAG,YACH/E,KAAK,YACLqK,MAAM,aACNG,KAAK,QACLhB,MAAOvJ,EACPqK,SAAUpN,GAAKgD,EAAahD,EAAEmM,OAAOG,OACrCiB,SAAmC,YAArB,OAAJtL,QAAI,IAAJA,GAAY,QAAR+K,EAAJ/K,EAAMgB,cAAM,IAAA+J,OAAR,EAAJA,EAAc9J,QACxBmK,UAAQ,EACRG,SAAmC,YAArB,OAAJvL,QAAI,IAAJA,GAAY,QAARgL,EAAJhL,EAAMgB,cAAM,IAAAgK,OAAR,EAAJA,EAAc/J,QAAsB,0BAA0BjB,EAAKgB,OAAOH,OAAS,0CAGjGlB,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CAACrF,GAAG,UAAU/E,KAAK,UAAUqK,MAAM,4BAA4BG,KAAK,QAAQhB,MAAOnJ,EAASiK,SAAUpN,GAAKoD,EAAWpD,EAAEmM,OAAOG,YAGzI,KAAK,EACH,MAAMmB,EAAOzJ,EAAcF,EAAe,GAEhBE,EAAc4E,QAAO5I,GAAKA,EAAEN,KAAKiB,SAAQzC,OAEnE,OACEqO,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yCAAwCD,SAAA,EAErDF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,sCAAqCD,SAAA,EAClD7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,qBAAoBD,SAAC,+CAClCjI,MAAMI,KAAK,CAAE1G,OAAQ,KAAMwG,KAAI,CAACgJ,EAAGC,KAClC,MAAMC,KAAoB5J,EAAc2J,GAAKjO,OAAQsE,EAAc2J,GAAKjO,KAAKiB,QACvEkN,EAAa/J,IAAiB6J,EAAM,EACpCG,EAAWhH,GAAmB0B,SAASmF,GAG7C,IAAII,EAAyC,YACzCC,EAAgB,GAChBC,EAAa,SAASN,EAAM,IAC5BO,EAAO,KAEPN,EACEE,GACFC,EAAgB,UAChBG,GAAOtM,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,oDAAmDD,SAAC,WAC3EwB,GAAc,eAEdF,EAAgB,YAChBC,EAAgB,yGAChBE,GAAOtM,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,kDAAiDD,SAAC,WACzEwB,GAAc,gBAKfD,EAAgB,uEAChBC,GAAc,iCAGjB,MAAME,EAAqBN,EAAa,mEAAqE,GAE7G,OACEtB,EAAAA,EAAAA,MAAC6B,EAAAA,EAAM,CAELC,QAASN,EACTO,QAASA,KAEPvK,EAAgB4J,EAAM,GAGlBC,GACF7G,IAAsBwB,GACpBA,EAAKC,SAASmF,GAAOpF,EAAKK,QAAOC,GAAKA,IAAM8E,IAAO,IAAIpF,EAAMoF,IAEjE,EAEFY,KAAK,KACL7B,UAAW,sDAAsDyB,KAAsBH,IACvFQ,MAAOZ,EAAkBE,EAAW,gCAAkC,8BAAiC,2BAA2BrB,SAAA,EAElI7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,qBAAoBD,SAAEwB,IACrCC,IAlBIP,EAmBE,KAKbpB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,qEAAoED,SAAA,EACjF7K,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,mBAAkBD,SAAC,0BAChCF,EAAAA,EAAAA,MAAA,KAAGG,UAAU,oBAAmBD,SAAA,EAC9B7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,yDAAwDD,SAAC,YACzE7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,qCAAoCD,SAAC,+BAEvDF,EAAAA,EAAAA,MAAA,KAAGG,UAAU,oBAAmBD,SAAA,EAC9B7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,yEAChBH,EAAAA,EAAAA,MAAA,QAAMG,UAAU,mCAAkCD,SAAA,CAAC,iBAAa7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,YAAWD,SAAC,oBAE9FF,EAAAA,EAAAA,MAAA,KAAGG,UAAU,oBAAmBD,SAAA,EAC9B7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,iHAChB9K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,mCAAkCD,SAAC,mCAEpD7K,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,gDAA+CD,SAAC,gFAGhEF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,OAAMD,SAAA,EACnB7K,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASA,IAAMlJ,GAAsB,GAAMqH,SAAC,qBACxE7K,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAAStD,GAAuB0B,UAAU,OAAMD,SAAC,gCAIjFF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,gCAA+BD,SAAA,EAC5C7K,EAAAA,EAAAA,KAAC5E,EAAAA,EAAU,CAETG,IAAKK,GACLJ,YAAaqQ,EAAKjO,KAClBlC,OAAQ6K,GACR5K,OAAO,QAJF,SAASuG,KAAgBiC,KAAKC,UAMrCpE,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,gDAA+CD,SAAC,sHAMrE,KAAK,EAAG,CAEN,MAAMvB,EAAmBlH,EAAcmH,QAAO,CAACC,EAAO7E,EAAOY,IACvDL,GAAmB0B,SAASrB,IAAUZ,EAAM7G,MAAQ6G,EAAM7G,KAAKiB,OAC1DyK,EAAQ,EAEVA,GACN,GACHvN,QAAQC,IAAI,gDAAiDoN,GAG7D,MAAMuD,EAA8BzO,IAClC4F,EAAsB5F,EAAEmM,OAAOG,MAAyB,EAEpDoC,EAA4B1O,IAChC8F,GAAqB9F,EAAEmM,OAAOG,MAAM,EAIhCqC,EAAuBA,CAACxH,EAAemF,KAC3C,MAAMsC,EAAWC,SAASvC,EAAO,IACjC,IAAKwC,MAAMF,IAAaA,EAAW,EAAG,CACpC,MAAMG,EAAe,IAAIlK,EAAiBE,WAC1CgK,EAAa5H,GAASyH,EACtB9J,EAAoB,IAAKD,EAAkBE,UAAWgK,GACxD,GAEIC,EAAoBhP,IACxB8E,EAAoB,IAAKD,EAAkBG,KAAMhF,EAAEmM,OAAOG,OAAwC,EAGpG,OACEC,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EAExBF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACH7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6BAA4BD,SAAC,yBAC3CF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,oBAAmBD,SAAA,EAChC7K,EAAAA,EAAAA,KAAA,SACEiG,GAAG,cACH/E,KAAK,iBACLwK,KAAK,QACLhB,MAAM,MACN2C,QAAgC,QAAvBtJ,EACTyH,SAAUqB,EACV/B,UAAU,mEAEZ9K,EAAAA,EAAAA,KAAA,SAAOsN,QAAQ,cAAcxC,UAAU,kEAAiED,SAAC,yBAI3GF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,oBAAmBD,SAAA,EAChC7K,EAAAA,EAAAA,KAAA,SACEiG,GAAG,gBACH/E,KAAK,iBACLwK,KAAK,QACLhB,MAAM,QACN2C,QAAgC,UAAvBtJ,EACTyH,SAAUqB,EACV/B,UAAU,mEAEZ9K,EAAAA,EAAAA,KAAA,SAAOsN,QAAQ,gBAAgBxC,UAAU,kEAAiED,SAAC,0BAIrF,UAAvB9G,IACC4G,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EACxB7K,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CACJrF,GAAG,oBACH/E,KAAK,oBACLwK,KAAK,iBACLhB,MAAOzG,EACPuH,SAAUsB,EACVvB,MAAM,wBACNE,UAAQ,EACRX,UAAU,cAEX9K,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,gDAA+CD,SAAC,kCAOrEvB,EAAmB,IAClBqB,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACF7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6BAA4BD,SAAC,2BAC3C7K,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,wCAAuCD,SAAC,6IAGrDF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,+BAA8BD,SAAA,EAC3C7K,EAAAA,EAAAA,KAAA,SAAOsN,QAAQ,eAAexC,UAAU,6DAA4DD,SAAC,yBAGrGF,EAAAA,EAAAA,MAAA,UACE1E,GAAG,eACH/E,KAAK,eACLwJ,MAAOzH,EAAiBG,KACxBoI,SAAU4B,EACVtC,UAAU,2MAA0MD,SAAA,EAEpN7K,EAAAA,EAAAA,KAAA,UAAQ0K,MAAM,UAASG,SAAC,aACxB7K,EAAAA,EAAAA,KAAA,UAAQ0K,MAAM,QAAOG,SAAC,WACtB7K,EAAAA,EAAAA,KAAA,UAAQ0K,MAAM,OAAMG,SAAC,eAIxBjI,MAAMI,KAAK,CAAE1G,OAAQgN,EAAmB,IAAKxG,KAAI,CAACgJ,EAAGvG,KAAK,IAAAgI,EAAA,OACzD5C,EAAAA,EAAAA,MAAA,OAAiBG,UAAU,+BAA8BD,SAAA,EACvDF,EAAAA,EAAAA,MAAA,SAAO2C,QAAS,YAAY/H,IAASuF,UAAU,kEAAiED,SAAA,CAAC,8BACnFtF,EAAQ,EAAE,QAExCvF,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CACJrF,GAAI,YAAYV,IAChBrE,KAAM,YAAYqE,IAClBmG,KAAK,SACLhB,OAAwC,QAAjC6C,EAAAtK,EAAiBE,UAAUoC,UAAM,IAAAgI,OAAA,EAAjCA,EAAmCC,aAAc,KACxDhC,SAAWpN,GAAM2O,EAAqBxH,EAAOnH,EAAEmM,OAAOG,OACtDI,UAAU,YACVW,UAAQ,KAEVzL,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,8CAA6CD,SAAE5H,EAAiBG,SAbxEmC,EAcJ,OAMX+D,GAAoB,IACjBtJ,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,gDAA+CD,SAAC,8GAMxE,CACA,KAAK,EAEH,OACEF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EACxB7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6BAA4BD,SAAC,oBAG3CF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACH7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6CAA4CD,SAAC,kBAC3DF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,oCAAmCD,SAAA,EAChD7K,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,yBAAwBD,UACrC7K,EAAAA,EAAAA,KAACsL,EAAAA,EAAK,CACJrF,GAAG,kBACH/E,KAAK,kBACLqK,MAAM,gBACNG,KAAK,QACLhB,MAAOjG,GAAgBE,MACvB6G,SAAWpN,GAAMsG,GAAmB,IAAKD,GAAiBE,MAAOvG,EAAEmM,OAAOG,QAC1E+C,YAAY,2BAIhBzN,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACE,QAASxC,GAAoBuC,QAAQ,YAAY3B,UAAU,uBAAsBD,SAAC,yBAK9FF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACH7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6CAA4CD,SAAC,sBAC1D7F,IACChF,EAAAA,EAAAA,KAAA,KAAA6K,SAAG,wBACDjG,GAAStI,OAAS,GACpBqO,EAAAA,EAAAA,MAAA,OAAKG,UAAU,oCAAmCD,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yBAAwBD,SAAA,EACpC7K,EAAAA,EAAAA,KAAA,SAAOsN,QAAQ,gBAAgBxC,UAAU,qDAAoDD,SAAC,uDAG9F7K,EAAAA,EAAAA,KAAA,UACEiG,GAAG,gBACHyH,UAAQ,EACRhD,MAAO5F,GACP0G,SAAUlB,GACVQ,UAAU,yDAAwDD,SAEjEjG,GAAS9B,KAAI6K,IACZ3N,EAAAA,EAAAA,KAAA,UAAyB0K,MAAOiD,EAAQ1H,GAAG4E,SACxC8C,EAAQzM,KAAO,GAAGyM,EAAQzM,SAASyM,EAAQhJ,SAAWgJ,EAAQhJ,OADpDgJ,EAAQ1H,YAM7BjG,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACE,QAAStC,GAAqBqC,QAAQ,YAAY3B,UAAU,uBAAsBD,SAAC,qBAG7F7K,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,sBAAqBD,SAAC,2EAKvCF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACHF,EAAAA,EAAAA,MAAA,MAAIG,UAAU,6CAA4CD,SAAA,CAAC,qBAAmBtG,GAAWjI,OAAO,OAC/FiI,GAAWjI,OAAS,GACnB0D,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,kDAAiDD,SAC5DtG,GAAWzB,KAAI,CAAC8K,EAAKrI,KACpBoF,EAAAA,EAAAA,MAAA,MAAgBG,UAAU,yCAAwCD,SAAA,EAChE7K,EAAAA,EAAAA,KAAA,QAAA6K,SAAO+C,EAAI1M,KAAO,GAAG0M,EAAI1M,SAAS0M,EAAIjJ,SAAWiJ,EAAIjJ,SACrD3E,EAAAA,EAAAA,KAAA,UAAQ0M,QAASA,KAAMmB,OA1elBlJ,EA0ekCiJ,EAAIjJ,MAze7DH,GAAcD,GAAWyC,QAAOI,GAAKA,EAAEzC,QAAUA,KAD1BA,KA0e6C,EAACmG,UAAU,0CAAyCD,SAAC,kBAFhGtF,QAObvF,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,sBAAqBD,SAAC,mCAK7C,KAAK,EAAG,CAEL,MAAMiD,EAA6C,UAAvB/J,GAAkCE,EAC1D,IAAIE,KAAKF,GAAmB2E,iBAC5B,cACJ,OACE+B,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EACxB7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6BAA4BD,SAAC,uBAE3CF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACH7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6CAA4CD,SAAC,sBAC3DF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAG7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,gBAAeD,SAAC,UAAY,IAAElK,MACjDgK,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAG7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,gBAAeD,SAAC,aAAe,IAAE/J,MACpD6J,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAG7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,gBAAeD,SAAC,UAAY,IAAE7J,EAAS,KAAMG,EAAU,OACzEI,IAAWoJ,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAG7K,EAAAA,EAAAA,KAAA,QAAM8K,UAAU,gBAAeD,SAAC,cAAgB,IAAEtJ,SAGnEoJ,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACH7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6CAA4CD,SAAC,8BAE1DzI,EAAc4E,QAAO,CAAC5I,EAAG2N,IAAQ7G,GAAmB0B,SAASmF,IAAQ3N,EAAEN,KAAKiB,SAAQzC,OAAS,GAC5F0D,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,8BAA6BD,SACxCzI,EACEU,KAAI,CAAC6B,EAAOY,KAEX,GAAIL,GAAmB0B,SAASrB,IAAUZ,EAAM7G,KAAKiB,OAAQ,CAE3D,MAAMgP,EAAe3L,EAClBU,KAAI,CAAC1E,EAAG6I,KAAC,CAAQtC,MAAOvG,EAAG4P,cAAe/G,MAC1CD,QAAO1L,IAAA,IAAC,MAAEqJ,EAAK,cAAEqJ,GAAe1S,EAAA,OAC/B4J,GAAmB0B,SAASoH,IAAkBrJ,EAAM7G,KAAKiB,MAAM,IAE7DkP,EAAcF,EAAaG,WAAUnL,GAAQA,EAAKiL,gBAAkBzI,IAEpE4I,EAA8BF,EAAc,EAElD,IAAIG,EAAa,8BAMjB,OAJID,EAA8B,QAAqEE,IAAhEpL,EAAiBE,UAAUgL,EAA8B,KAC9FC,EAAa,QAAQnL,EAAiBE,UAAUgL,EAA8B,MAAMlL,EAAiBG,qBAAqB2K,EAAaE,EAAc,GAAGD,cAAgB,MAIxKrD,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAgB,UACNtF,EAAQ,EAAE,MAAI6I,IADf7I,EAIb,CACA,OAAO,IAAI,IAEZyB,OAAOsH,YAIZtO,EAAAA,EAAAA,KAAA,KAAG8K,UAAU,sBAAqBD,SAAC,8DAIvCF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACH7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6CAA4CD,SAAC,gBAC3DF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAItG,GAAWjI,OAAO,mBACrBiI,GAAWjI,OAAS,IACnBqO,EAAAA,EAAAA,MAAA,MAAIG,UAAU,kDAAiDD,SAAA,CAC5DtG,GAAWD,MAAM,EAAG,GAAGxB,KAAI,CAACsE,EAAGH,KAAMjH,EAAAA,EAAAA,KAAA,MAAA6K,SAAazD,EAAElG,KAAO,GAAGkG,EAAElG,SAASkG,EAAEzC,SAAWyC,EAAEzC,OAA1CsC,KAC9C1C,GAAWjI,OAAS,IAAKqO,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAQtG,GAAWjI,OAAS,EAAE,kBAKlEqO,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAL,SAAA,EACH7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,6CAA4CD,SAAC,cAC3DF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,kBAAgBiD,SAGrB9N,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,wBAAuBD,UAEpC7K,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CACLE,QAAS9E,GACT+D,SAAU/J,EACVkJ,UAAU,UAAUD,SAEnBjJ,EAAU,cAAgB,mCAKrC,CACA,QAAS,OAAO,KAClB,EAiCKuJ,MAIHR,EAAAA,EAAAA,MAAA,OAAKG,UAAU,4BAA2BD,SAAA,EACxC7K,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACE,QA9fQ6B,KACrB9M,EAAS,IACLO,EAAO,GAAGC,EAAQD,EAAO,EAAE,EA4fO2J,SAAmB,IAAT3J,GAAcJ,EAAS6K,QAAQ,YAAW5B,SAAC,aACrF7I,EAAO,GACNhC,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACE,QAhnBEtN,UAIjB,GAHAqC,EAAS,IAGI,IAATO,GAAgBrB,GAAiBG,GAAYE,GAAaG,EAA9D,CAMA,GAAa,IAATa,EAAY,CACd/F,QAAQC,IAAI,yCACZ,IAAIsS,EAAuD,KACvDC,GAAsB,EACtBC,GAAe,EAGf9S,GAAUI,SACZC,QAAQC,IAAI,yCAA0CgG,GAEtDsM,QAAsB5S,GAAUI,QAAQmD,OACpCqP,GACFvS,QAAQC,IAAI,yCAEZD,QAAQC,IAAI,iBAAkBsS,EAAc5Q,KAAO4Q,EAAc5Q,KAAKO,UAAU,EAAG,IAAM,MAAQ,WACjGlC,QAAQC,IAAI,iBAAkBsS,EAAc1Q,KAAO0Q,EAAc1Q,KAAKK,UAAU,EAAG,IAAM,MAAQ,WAG7FqQ,EAAc1Q,MAAQ0Q,EAAc1Q,KAAKiB,QAC3C9C,QAAQC,IAAI,wCACZuS,GAAsB,GAEtBxS,QAAQC,IAAI,0CAIdD,QAAQC,IAAI,6CACZqK,GAAeiI,EAAc5Q,KAAM4Q,EAAc1Q,OAIjD7B,QAAQ4C,KAAK,qEAGd5C,QAAQqB,MAAM,oDASjB,MAAMqR,EAAuBF,GAAuBvJ,GAAmB0B,SAAS1E,EAAe,GAC3FyM,GACF1S,QAAQC,IAAI,yCAGdD,QAAQC,IAAI,sEAEZwS,EAAetM,EAAc+H,MAAK,CAACxF,EAAOY,KACxC,MAAMhJ,EAAUoI,EAAM7G,MAAQ6G,EAAM7G,KAAKiB,OACnCmN,EAAWhH,GAAmB0B,SAASrB,GAC7C,SAAIhJ,IAAW2P,KACVjQ,QAAQC,IAAI,eAAeqJ,uCACpB,EAEA,IAETmJ,GACDzS,QAAQC,IAAI,kDAIhB,MAAM0S,EAAoBD,GAAwBD,EAGlD,GAFAzS,QAAQC,IAAI,sCAAuC0S,EAAmB,kBAAkBD,oBAAuCD,OAE1HE,EAEH,YADAnN,EAAS,2FAGXxF,QAAQC,IAAI,gDACd,CAGA,GAAa,IAAT8F,EAAY,CACd,MAAM6M,EAAMzM,EAAc4E,QAAO5I,GAAKA,EAAEN,KAAKiB,SAAQzC,OACrD,GAAIuS,EAAM,EAAG,CACX,GAAI5L,EAAiBE,UAAU7G,SAAWuS,EAAM,EAE9C,YADApN,EAAS,UAAUoN,EAAM,gBAG3B,GAAI5L,EAAiBE,UAAUgH,MAAK2E,GAAKA,GAAK,IAE5C,YADArN,EAAS,8BAGb,CACF,CAGa,IAATO,GAAoC,IAAtBuC,GAAWjI,OAMzB0F,EAAO,IACT/F,QAAQC,IAAI,wBAAwB8F,aAAgBA,EAAO,KAC3DC,EAAQD,EAAO,IAPfP,EAAS,qCA9FX,MAFEA,EAAS,sCAwGX,EAmgBoCkK,SAAU/J,EAAS6K,QAAQ,UAAS5B,SAAC,UAElEF,EAAAA,EAAAA,MAAC6B,EAAAA,EAAM,CAACE,QAAS9E,GAAsB+D,SAAU/J,EAASkJ,UAAU,UAASD,SAAA,CAAC,IACzEjJ,EAAU,cAAgB,oCAjftC+I,EAAAA,EAAAA,MAACoE,EAAAA,EAAK,CAACC,OAAQzL,EAAoB0L,QAASA,IAAMzL,GAAsB,GAAQoJ,MAAM,kBAAiB/B,SAAA,EACrG7K,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,yEAAwED,SACpFlH,GACG3D,EAAAA,EAAAA,KAAA,KAAA6K,SAAG,eACHpH,EAAcX,KAAIoM,IAClBvE,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAEHJ,UAAW,oBAAkC,OAAhBjH,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAkBoC,MAAOiJ,EAAEjJ,GAAK,sBAAwB,IACrFyG,QAASA,IAhZQtN,WAC3BwE,GAAoB,GACpB,IACE3H,QAAQC,IAAI,uCAAuC+J,KACnD,MAAMmB,QAAUrB,EAAAA,GAA8BC,gBAAgBC,GAEhC,IAADkJ,EAD7BlT,QAAQC,IAAI,iCAAkCkL,GAC1CA,EAAE1F,SAAW0F,EAAElB,UACjBjK,QAAQC,IAAI,qCAAsC,CAChD+J,GAAImB,EAAElB,SAASD,IAAMmB,EAAElB,SAASuB,IAChCvG,KAAMkG,EAAElB,SAAShF,KACjB9E,UAAWgL,EAAElB,SAASG,YACtBqB,aAAcN,EAAElB,SAASI,QACzB8I,eAAiC,QAAlBD,EAAA/H,EAAElB,SAASI,eAAO,IAAA6I,OAAA,EAAlBA,EAAoB7S,SAAU,IAGjDwH,EAAoBsD,EAAElB,SACxB,CAAE,MAAO6C,GACP9M,QAAQqB,MAAM,oCAAqCyL,GACnDtH,EAAS,oCACX,CAAC,QACCmC,GAAoB,EACtB,GA2XyByL,CAAqBH,EAAEjJ,IAAI4E,SAAA,EAE1C7K,EAAAA,EAAAA,KAAA,MAAI8K,UAAU,8BAA6BD,SAAEqE,EAAEhO,QAC/ClB,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,2FAA0FD,SACtGqE,EAAEI,cAAetP,EAAAA,EAAAA,KAAA,OAAKuP,IAAKL,EAAEI,aAAcE,IAAKN,EAAEhO,KAAM4J,UAAU,iCAAoC,iBANpGoE,EAAEjJ,SAYf0E,EAAAA,EAAAA,MAAA,OAAKG,UAAU,8BAA6BD,SAAA,EAC1C7K,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASA,IAAMlJ,GAAsB,GAAOqH,SAAC,YACzE7K,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACE,QAASlF,GAAmBmE,UAAW9H,GAAoBF,EAAiBkH,SAAC,yBAmerF,C,mGCviCD,MAAMkE,EAA8BzT,IASpC,IATqC,OAC1C0T,EAAM,QACNC,EAAO,MACPrC,EAAK,SACL/B,EAAQ,UACR4E,EAAS,YACTC,EAAc,UAAS,eACvBC,EAAiB,UAAS,WAC1BC,EAAa,UACdtU,EACC,OACE0E,EAAAA,EAAAA,KAAC6P,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMf,EAAQgB,GAAIC,EAAAA,SAASpF,UAC5CF,EAAAA,EAAAA,MAACuF,EAAAA,GAAM,CAACF,GAAG,MAAMlF,UAAU,gBAAgBmE,QAASA,EAAQpE,SAAA,EAC1D7K,EAAAA,EAAAA,KAAC6P,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW5F,UAEnB7K,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,4CAGjB9K,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,gCAA+BD,UAC5C7K,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,8DAA6DD,UAC1E7K,EAAAA,EAAAA,KAAC6P,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB5F,UAE5BF,EAAAA,EAAAA,MAACuF,EAAAA,GAAOQ,MAAK,CAAC5F,UAAU,uHAAsHD,SAAA,EAC5I7K,EAAAA,EAAAA,KAACkQ,EAAAA,GAAOS,MAAK,CACXX,GAAG,KACHlF,UAAU,gDAA+CD,SAExD+B,KAEH5M,EAAAA,EAAAA,KAAA,OAAK8K,UAAU,6BAA4BD,SACxCA,KAGHF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kCAAiCD,SAAA,EAC9C7K,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASuC,EAAQpE,SAC1C+E,IAEFH,IACCzP,EAAAA,EAAAA,KAACwM,EAAAA,EAAM,CAACC,QAASkD,EAAgBjD,QAAS+C,EAAU5E,SACjD6E,oBASN,C", "sources": ["components/MjmlEditor.tsx", "pages/campaigns/CampaignCreate.tsx", "components/Modal.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n// @ts-ignore - grapesjs-mjml lacks official types\r\nimport grapesjsMjml from 'grapesjs-mjml';\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface MjmlEditorRef {\r\n  save: () => Promise<{ mjml: string; html: string }>;\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface MjmlEditorProps {\r\n  initialMjml?: string;\r\n  initialHtml?: string; // Added to potentially load HTML if MJML is missing\r\n  onSave?: (mjml: string, html: string) => void;\r\n  height?: string | number;\r\n}\r\n\r\nconst MjmlEditor = forwardRef<MjmlEditorRef, MjmlEditorProps>(\r\n  ({ initialMjml = '', initialHtml = '', onSave, height = '70vh' }, ref) => {\r\n    const editorRef = useRef<HTMLDivElement>(null);\r\n    const grapesEditor = useRef<Editor | null>(null);\r\n\r\n    // Initialize GrapesJS Editor\r\n    useEffect(() => {\r\n      if (!editorRef.current) return; // Check for DOM element\r\n      \r\n      // Always destroy and recreate the editor to ensure consistent behavior\r\n      if (grapesEditor.current) {\r\n        console.log(\"[MjmlEditor] Cleaning up previous editor instance\");\r\n        grapesEditor.current.destroy();\r\n        grapesEditor.current = null;\r\n      }\r\n\r\n      console.log(\"[MjmlEditor] Initializing editor with props:\", {\r\n        hasMjml: !!initialMjml,\r\n        mjmlLength: initialMjml?.length || 0,\r\n        hasHtml: !!initialHtml,\r\n        htmlLength: initialHtml?.length || 0\r\n      });\r\n\r\n      try {\r\n        const editor = grapesjs.init({\r\n          container: editorRef.current,\r\n          fromElement: false, // Don't load from existing HTML/CSS in the container\r\n          height: String(height),\r\n          width: 'auto',\r\n          storageManager: false, // Disable default storage manager\r\n          plugins: [grapesjsMjml],\r\n          pluginsOpts: {\r\n            'grapesjs-mjml': {\r\n              // MJML plugin options (optional)\r\n              // columnsPadding: '0px',\r\n               useXmlParser: true, // Use the faster XML parser\r\n               resetBlocks: false, // Try keeping default GrapesJS blocks\r\n               // ... other options\r\n            }\r\n          },\r\n          // Optional: Configure panels, blocks, styles etc.\r\n        });\r\n\r\n        // Make sure the editor was initialized properly\r\n        if (!editor) {\r\n          console.error(\"[MjmlEditor] Failed to initialize editor\");\r\n          return;\r\n        }\r\n\r\n        grapesEditor.current = editor;\r\n\r\n        // Register missing commands that are expected by the editor\r\n        if (!editor.Commands.has('mjml-get-code')) {\r\n          console.log(\"[MjmlEditor] Registering missing mjml-get-code command\");\r\n          editor.Commands.add('mjml-get-code', {\r\n            run: (editor) => {\r\n              const mjml = editor.getHtml();\r\n              // Simple implementation for missing command\r\n              return { \r\n                mjml: mjml,\r\n                html: mjml // We'll process this later if needed\r\n              };\r\n            }\r\n          });\r\n        }\r\n\r\n        if (!editor.Commands.has('gjs-get-html')) {\r\n          console.log(\"[MjmlEditor] Registering missing gjs-get-html command\");\r\n          editor.Commands.add('gjs-get-html', {\r\n            run: (editor) => {\r\n              return editor.getHtml({ component: editor.getWrapper() });\r\n            }\r\n          });\r\n        }\r\n\r\n        // Use a small timeout to ensure editor is fully initialized\r\n        setTimeout(() => {\r\n          if (!grapesEditor.current) {\r\n            console.error(\"[MjmlEditor] Editor instance not available after timeout\");\r\n            return;\r\n          }\r\n          \r\n          // Verify the editor's components API is available\r\n          if (!grapesEditor.current.setComponents) {\r\n            console.error(\"[MjmlEditor] Editor's setComponents method is not available\");\r\n            return;\r\n          }\r\n          \r\n          try {\r\n            // Load initial content\r\n            if (initialMjml) {\r\n              console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\r\n              try {\r\n                grapesEditor.current.setComponents(initialMjml); // Use setComponents for MJML\r\n                console.log(\"[MjmlEditor] Successfully loaded MJML content\");\r\n              } catch (e) {\r\n                console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\r\n                // Fallback to HTML if MJML fails?\r\n                if (initialHtml) {\r\n                  console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\r\n                  grapesEditor.current.setComponents(initialHtml); // Use setComponents for HTML as well\r\n                  console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\r\n                }\r\n              }\r\n            } else if (initialHtml) {\r\n              console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\r\n              grapesEditor.current.setComponents(initialHtml);\r\n              console.log(\"[MjmlEditor] Successfully loaded HTML content\");\r\n            } else {\r\n              // Load default MJML template if nothing is provided\r\n              console.log(\"[MjmlEditor] No content provided, loading default template\");\r\n              grapesEditor.current.setComponents(`\r\n                <mjml>\r\n                  <mj-body>\r\n                    <mj-section>\r\n                      <mj-column>\r\n                        <mj-text>Start designing your email!</mj-text>\r\n                      </mj-column>\r\n                    </mj-section>\r\n                  </mj-body>\r\n                </mjml>\r\n              `);\r\n            }\r\n          } catch (error) {\r\n            console.error(\"[MjmlEditor] Error in content loading phase:\", error);\r\n          }\r\n        }, 100);\r\n\r\n        // Declare timeout variable in outer scope so it's accessible in cleanup function\r\n        let saveTimeout: NodeJS.Timeout | undefined;\r\n        let isSaving = false; // Flag to prevent multiple simultaneous save operations\r\n        \r\n        // Attach save listener with debounce\r\n        editor.on('change:changesCount', () => {\r\n          if (onSave && editor && !isSaving) { // Only proceed if not already saving\r\n            // Clear existing timeout\r\n            if (saveTimeout) clearTimeout(saveTimeout);\r\n            \r\n            // Set a new timeout\r\n            saveTimeout = setTimeout(() => { // Assign to the outer scope variable\r\n              try {\r\n                isSaving = true; // Set saving flag\r\n                // Simplify code retrieval: Prioritize commands, fallback to getHtml()\r\n                let finalMjml = '';\r\n                let finalHtml = '';\r\n\r\n                try {\r\n                  // Try the specific mjml command first\r\n                  const mjmlCode = editor.runCommand('mjml-get-code');\r\n                  if (mjmlCode && typeof mjmlCode === 'object') { // Command should return object {mjml, html}\r\n                    finalMjml = mjmlCode.mjml || '';\r\n                    finalHtml = mjmlCode.html || '';\r\n                    console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\r\n                  }\r\n                } catch (cmdErr) {\r\n                   console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\r\n                }\r\n\r\n                // If command failed or didn't return expected structure, use fallbacks\r\n                if (!finalMjml && !finalHtml) {\r\n                    finalMjml = editor.getHtml() || ''; // Often gets MJML\r\n                    try {\r\n                      // Try getting HTML via command\r\n                      finalHtml = editor.runCommand('gjs-get-html') || ''; \r\n                    } catch(htmlCmdErr) {\r\n                       console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\r\n                    }\r\n                    // As a last resort for HTML, maybe just use the component HTML (less reliable)\r\n                    if (!finalHtml) {\r\n                      finalHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                    }\r\n                    console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\r\n                }\r\n                \r\n                // Don't save if we have no content, prevents potential refresh cycles\r\n                if (!finalMjml.trim()) {\r\n                  console.log(\"[MjmlEditor] No MJML content to save, skipping save\");\r\n                  isSaving = false;\r\n                  return;\r\n                }\r\n                \r\n                console.log(\"[MjmlEditor] Attempting to call onSave...\");\r\n                // Call onSave as long as the editor instance exists and the prop was passed\r\n                // Even if mjml/html strings are empty, let the parent decide what to do\r\n                onSave(finalMjml, finalHtml);\r\n                console.log(\"[MjmlEditor] onSave callback executed.\");\r\n\r\n             } catch (error) {\r\n                 console.error(\"Error during editor change listener:\", error);\r\n             } finally {\r\n                 isSaving = false; // Reset flag whether save succeeded or failed\r\n             }\r\n            }, 500); // 500ms debounce\r\n          }\r\n        });\r\n\r\n        // Return cleanup function\r\n        return () => {\r\n          if (saveTimeout) clearTimeout(saveTimeout); // Now accessible here\r\n          if (grapesEditor.current) {\r\n             // Clean up panels, commands, etc. specific to this instance if necessary\r\n             try {\r\n               grapesEditor.current.destroy();\r\n             } catch (destroyError) {\r\n               console.error(\"[MjmlEditor] Error during editor cleanup:\", destroyError);\r\n             }\r\n             grapesEditor.current = null;\r\n          }\r\n        };\r\n      } catch (initError) {\r\n        console.error(\"[MjmlEditor] Critical error during editor initialization:\", initError);\r\n      }\r\n    }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\r\n\r\n    // Expose save method via ref\r\n    useImperativeHandle(ref, () => ({\r\n      save: async () => {\r\n        let generatedCode = { mjml: '', html: '' }; // Initialize with empty strings\r\n        if (grapesEditor.current) {\r\n           try {\r\n               // Check if command exists first to avoid warnings\r\n               const editor = grapesEditor.current;\r\n               if (editor.Commands.has('mjml-get-code')) {\r\n                 // Try the primary command\r\n                 const result = editor.runCommand('mjml-get-code');\r\n                 // Check if the command returned the expected object structure\r\n                 if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\r\n                   generatedCode.mjml = result.mjml || '';\r\n                   generatedCode.html = result.html || '';\r\n                   console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", { mjml: generatedCode.mjml.substring(0,50)+'...', html: generatedCode.html.substring(0,50)+'...' });\r\n                 } else {\r\n                    console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\r\n                    // Throw an error to trigger the catch block for fallback logic\r\n                    throw new Error(\"Command returned unexpected structure\"); \r\n                 }\r\n               } else {\r\n                 // Command doesn't exist, go straight to fallback\r\n                 throw new Error(\"mjml-get-code command not available\");\r\n               }\r\n           } catch (cmdErr) {\r\n               console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\r\n               try {\r\n                 // Fallback attempts\r\n                 const editor = grapesEditor.current;\r\n                 const rawMjml = editor.getHtml() || '';\r\n                 let generatedHtml = '';\r\n                 \r\n                 // Try gjs-get-html only if it exists\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   generatedHtml = editor.runCommand('gjs-get-html') || '';\r\n                 } else {\r\n                   // Direct fallback to component HTML\r\n                   generatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                  \r\n                 if (rawMjml || generatedHtml) { // Use if *either* fallback worked\r\n                     generatedCode.mjml = rawMjml;\r\n                     generatedCode.html = generatedHtml || rawMjml; // Use MJML as HTML if no HTML generated\r\n                     console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\r\n                 } else {\r\n                     console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\r\n                 }\r\n               } catch (fallbackErr) {\r\n                  console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\r\n               }\r\n           }\r\n        } else {\r\n          console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\r\n        }\r\n\r\n        // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\r\n        await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\r\n        \r\n        // Re-fetch the HTML specifically after the delay, as it might have updated\r\n        if (grapesEditor.current && !generatedCode.html.trim()) { // Only re-fetch if HTML was initially empty\r\n            console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\r\n            try {\r\n                 const editor = grapesEditor.current;\r\n                 \r\n                 let potentiallyUpdatedHtml = '';\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   potentiallyUpdatedHtml = editor.runCommand('gjs-get-html');\r\n                 }\r\n                 \r\n                 if (!potentiallyUpdatedHtml) {\r\n                   potentiallyUpdatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                 \r\n                 if (potentiallyUpdatedHtml.trim()) {\r\n                     console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\r\n                     generatedCode.html = potentiallyUpdatedHtml;\r\n                 } else {\r\n                    console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\r\n                    // If still no HTML but we have MJML, use that\r\n                    if (generatedCode.mjml && !generatedCode.html) {\r\n                      generatedCode.html = generatedCode.mjml;\r\n                    }\r\n                 }\r\n            } catch (refetchErr) {\r\n                console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\r\n            }\r\n        }\r\n\r\n        // ALWAYS return the potentially updated generatedCode object\r\n        return generatedCode; \r\n      },\r\n       getEditor: () => grapesEditor.current,\r\n    }));\r\n\r\n    return <div ref={editorRef} style={{ height: height }} />;\r\n  }\r\n);\r\n\r\n// Assign display name for debugging\r\nMjmlEditor.displayName = 'MjmlEditor';\r\n\r\nexport default MjmlEditor;", "// frontend/src/pages/campaigns/CampaignCreate.tsx\n\nimport '../../styles/editor.css';\n\nimport React, {\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\n\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport Input from 'components/Input';\nimport MjmlEditor, { MjmlEditorRef } from 'components/MjmlEditor';\nimport { Modal } from 'components/Modal';\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useLocation,\n  useNavigate,\n} from 'react-router-dom';\nimport { templateRecommendationService } from 'services';\nimport api, { campaignAPI } from 'services/api';\n\nconst CampaignCreate: React.FC = () => {\n  const { user } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // Campaign metadata\n  const [campaignName, setCampaignName] = useState('');\n  const [subject, setSubject] = useState('');\n  const [fromName, setFromName] = useState(user?.name || '');\n  const [fromEmail, setFromEmail] = useState(\n    user?.domain?.status === 'active' ? `noreply@${user.domain.name}` : ''\n  );\n  const [replyTo, setReplyTo] = useState('');\n\n  // UI state\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [templateLoading, setTemplateLoading] = useState(false);\n  const [step, setStep] = useState(1);\n\n  // Email sequence (up to 10)\n  const [currentEmail, setCurrentEmail] = useState<number>(1);\n  const [emailContents, setEmailContents] = useState<{ mjml: string; html: string }[]>(() => {\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          return arr.map((item: any) => ({\n            mjml: item.mjml || '',\n            html: item.html || '',\n          }));\n        }\n      } catch {\n        // ignore parse errors\n      }\n    }\n    return Array.from({ length: 10 }, () => ({ mjml: '', html: '' }));\n  });\n\n  // Scheduling\n  const [scheduleSettings, setScheduleSettings] = useState<{\n    intervals: number[];\n    unit: 'minutes' | 'hours' | 'days';\n  }>({\n    intervals: [24],\n    unit: 'hours',\n  });\n\n  // Preview and template picker\n  const [campaignPreview, setCampaignPreview] = useState<any>(null);\n  const [showTemplatePicker, setShowTemplatePicker] = useState(false);\n  const [templatesList, setTemplatesList] = useState<any[]>([]);\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);\n\n  // Recipients\n  const [sendScheduleOption, setSendScheduleOption] = useState<'now' | 'later'>('later');\n  const [scheduledDateTime, setScheduledDateTime] = useState<string>(() => {\n    const date = new Date(Date.now() + 60 * 60 * 1000);\n    return date.toISOString().slice(0, 16);\n  });\n  const [recipients, setRecipients] = useState<{ id: string | null; email: string; name?: string }[]>([]);\n  const [manualRecipient, setManualRecipient] = useState({ email: '', name: '' });\n  const [contacts, setContacts] = useState<{ id: string; email: string; name: string }[]>([]);\n  const [selectedContactIds, setSelectedContactIds] = useState<string[]>([]);\n  const [loadingContacts, setLoadingContacts] = useState(false);\n\n  // Ref to MJML editor\n  const editorRef = useRef<MjmlEditorRef>(null);\n\n  // State to track which emails are actively selected for sending\n  const [activeEmailIndices, setActiveEmailIndices] = useState<number[]>(() => {\n    // Initialize based on emails that already have HTML content\n    const initialActive: number[] = [];\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    let initialContents = Array.from({ length: 10 }, () => ({ mjml: '', html: '' }));\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          initialContents = arr.map((item: any) => ({ mjml: item.mjml || '', html: item.html || '' }));\n        }\n      } catch {}\n    }\n    initialContents.forEach((email, index) => {\n      if (email.html && email.html.trim()) {\n        initialActive.push(index);\n      }\n    });\n    return initialActive;\n  });\n\n  // Handle URL param for templateId\n  const queryParams = new URLSearchParams(location.search);\n  const templateId = queryParams.get('templateId');\n  useEffect(() => {\n    if (templateId) fetchTemplate(templateId);\n  }, [templateId]);\n\n  // Fetch AI or saved template by ID\n  const fetchTemplate = async (id: string) => {\n    setTemplateLoading(true);\n    try {\n      const res = await templateRecommendationService.getTemplateById(id);\n      if (res.success && res.template) {\n        const temp = res.template;\n        if (!campaignName) setCampaignName(`Campaign based on ${temp.name}`);\n        const updated = [...emailContents];\n        updated[0] = {\n          mjml: temp.mjmlContent || '',\n          html: temp.content || '',\n        };\n        setEmailContents(updated);\n      } else {\n        setError('Failed to load template.');\n      }\n    } catch {\n      setError('Failed to load template.');\n    } finally {\n      setTemplateLoading(false);\n    }\n  };\n\n  // Save handler from MJML editor\n  const handleMjmlSave = (mjml: string, html: string) => {\n    console.log(`--- handleMjmlSave called for email ${currentEmail} ---`);\n    console.log('Received MJML:', mjml ? mjml.substring(0, 100) + '...' : '(empty)');\n    console.log('Received HTML:', html ? html.substring(0, 100) + '...' : '(empty)');\n    const updated = [...emailContents];\n    const currentIndex = currentEmail - 1; // Get index before state update\n    updated[currentIndex] = { mjml, html };\n    setEmailContents(updated);\n    console.log('State AFTER update attempt:', updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n\n    // If saving resulted in valid HTML, ensure this email is marked active\n    if (html && html.trim()) {\n      setActiveEmailIndices(prev => {\n        if (!prev.includes(currentIndex)) {\n          // Add if not already present, keep sorted for consistency\n          return [...prev, currentIndex].sort((a, b) => a - b);\n        }\n        return prev; // Already active, no change needed\n      });\n      console.log(`Ensured email index ${currentIndex} is active after save.`);\n    } else {\n      // If saving resulted in empty HTML, ensure it's deactivated\n      setActiveEmailIndices(prev => prev.filter(i => i !== currentIndex));\n      console.log(`Deactivated email index ${currentIndex} after save due to empty HTML.`);\n    }\n  };\n\n  // Template picker effects and handlers\n  useEffect(() => {\n    if (showTemplatePicker) {\n      setLoadingTemplates(true);\n      templateRecommendationService.getAllTemplates()\n        .then(r => setTemplatesList(r.data || []))\n        .catch(console.error)\n        .finally(() => setLoadingTemplates(false));\n    }\n  }, [showTemplatePicker]);\n\n  const handleTemplateSelect = async (id: string) => {\n    setLoadingTemplates(true);\n    try {\n      console.log(`[Debug] Selecting template with ID: ${id}`);\n      const r = await templateRecommendationService.getTemplateById(id);\n      console.log('[Debug] Template API response:', r);\n      if (r.success && r.template) {\n        console.log('[Debug] Setting selected template:', {\n          id: r.template.id || r.template._id,\n          name: r.template.name,\n          hasMjml: !!r.template.mjmlContent,\n          hasContent: !!r.template.content,\n          contentLength: r.template.content?.length || 0\n        });\n      }\n      setSelectedTemplate(r.template);\n    } catch (err) {\n      console.error('[Debug] Error selecting template:', err);\n      setError('Failed to load selected template.');\n    } finally {\n      setLoadingTemplates(false);\n    }\n  };\n\n  const handleUseTemplate = () => {\n    console.log('[Debug] Using selected template:', selectedTemplate ? {\n      id: selectedTemplate.id || selectedTemplate._id,\n      name: selectedTemplate.name,\n      hasMjml: !!selectedTemplate.mjmlContent,\n      hasContent: !!selectedTemplate.content\n    } : 'No template selected');\n    \n    if (selectedTemplate) {\n      const updated = [...emailContents];\n      updated[currentEmail - 1] = {\n        mjml: selectedTemplate.mjmlContent || '',\n        html: selectedTemplate.content || '',\n      };\n      \n      console.log('[Debug] Updated email contents:', {\n        emailIndex: currentEmail - 1,\n        hasMjml: !!updated[currentEmail - 1].mjml,\n        hasHtml: !!updated[currentEmail - 1].html,\n        mjmlLength: updated[currentEmail - 1].mjml.length,\n        htmlLength: updated[currentEmail - 1].html.length\n      });\n      \n      // Save the updated contents to state and local storage\n      setEmailContents(updated);\n      localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n      \n      // Close the modal first to reduce complexity\n      setSelectedTemplate(null);\n      setShowTemplatePicker(false);\n      \n      // Force a complete re-render of the editor component\n      // Wait a moment to ensure state updates have completed\n      setTimeout(() => {\n        // This will force the MjmlEditor to completely remount with the new content\n        // Temporarily set current email to a value that won't match any existing email\n        setCurrentEmail(-1);\n        \n        // After a brief delay, restore the current email index\n        setTimeout(() => {\n          setCurrentEmail(currentEmail);\n        }, 50);\n      }, 100);\n    }\n  };\n\n  // Campaign creation\n  const handleCreateCampaign = async () => {\n    if (!campaignName || !subject || !fromName || !fromEmail) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    const validEmails = emailContents.filter(e => e.html.trim());\n    if (validEmails.length === 0) {\n      setError('Please add at least one email with valid HTML content');\n      return;\n    }\n    if (recipients.length === 0) {\n      setError('Please add at least one recipient');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Filter emails with actual HTML content AND that are marked active\n      const activeEmailsToSend = emailContents.filter((email, index) => \n        activeEmailIndices.includes(index) && email.html && email.html.trim()\n      );\n\n      // Validation based on filtered list\n      if (activeEmailsToSend.length === 0) {\n        setError('Please select at least one email with valid HTML content to send.');\n        setLoading(false);\n        return;\n      }\n      if (recipients.length === 0) {\n        setError('Please add at least one recipient');\n        setLoading(false);\n        return;\n      }\n      // --- End new validation ---\n\n      // Construct the base campaign data\n      const campaignData: any = {\n        name: campaignName,\n        subject,\n        fromName,\n        fromEmail,\n        replyTo: replyTo || fromEmail,\n        scheduled: sendScheduleOption === 'later',\n        ...(sendScheduleOption === 'later' && { scheduledFor: new Date(scheduledDateTime).toISOString() }),\n        recipientList: recipients,\n        userId: user?.id,\n        status: 'draft',\n        // Use the first ACTIVE email for top-level content\n        htmlContent: activeEmailsToSend[0]?.html || '', \n        // Send ONLY the emails that are ACTIVE and have content\n        emailContents: activeEmailsToSend.map(e => ({ mjml: e.mjml, html: e.html })),\n      };\n\n      // Add schedule only if there's more than one ACTIVE email with content\n      if (activeEmailsToSend.length > 1) {\n        campaignData.schedule = {\n          unit: scheduleSettings.unit,\n          // Slice intervals to match the number of gaps between ACTIVE emails\n          emailIntervals: scheduleSettings.intervals.slice(0, activeEmailsToSend.length - 1)\n            .map(d => ({ delay: d, unit: scheduleSettings.unit })),\n        };\n      }\n\n      console.log(\"Sending filtered & active campaign data:\", campaignData);\n\n      // Send the filtered data to the API\n      const res = await campaignAPI.createCampaign(campaignData);\n      // Check for _id primarily, then id\n      const campaignId = res?._id || res?.id;\n      console.log('Create campaign response:', res, 'Extracted ID:', campaignId);\n\n      setSuccess(sendScheduleOption === 'later'\n        ? `Campaign scheduled for ${new Date(scheduledDateTime).toLocaleString()}`\n        : 'Campaign created and will start sending shortly');\n      localStorage.removeItem('driftly_campaign_create_email_contents');\n      // Clear draft info if it exists\n      localStorage.removeItem('driftly_campaign_draft'); \n      sessionStorage.setItem('reloadCampaigns', 'true');\n      setTimeout(() => {\n        // Navigate using the extracted ID\n        if (campaignId) navigate(`/campaigns/${campaignId}/summary`);\n        else {\n          console.warn('No ID found in createCampaign response, navigating to list.');\n          navigate('/campaigns');\n        }\n      }, 1500);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBrowseTemplates = () => {\n    if (campaignName || subject || fromName || fromEmail || replyTo) {\n      localStorage.setItem('driftly_campaign_draft',\n        JSON.stringify({ campaignName, subject, fromName, fromEmail, replyTo }));\n    }\n    navigate('/email-templates');\n  };\n\n  useEffect(() => {\n    const draft = localStorage.getItem('driftly_campaign_draft');\n    if (draft) {\n      try {\n        const d = JSON.parse(draft);\n        setCampaignName(d.campaignName || '');\n        setSubject(d.subject || '');\n        setFromName(d.fromName || user?.name || '');\n        setFromEmail(d.fromEmail || '');\n        setReplyTo(d.replyTo || '');\n      } catch {}\n      localStorage.removeItem('driftly_campaign_draft');\n    }\n  }, [user]);\n\n  const handleEmailSelect = (i: number) => setCurrentEmail(i);\n\n  useEffect(() => {\n    if (step === 3) {\n      // Count emails that are BOTH active AND have HTML\n      const activeEmailCount = emailContents.reduce((count, email, index) => {\n        if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n          return count + 1;\n        }\n        return count;\n      }, 0);\n\n      console.log('Active emails with HTML for scheduling:', activeEmailCount);\n\n      if (activeEmailCount > 1) {\n        setScheduleSettings(prev => ({\n          ...prev,\n          // Ensure intervals array matches the number of gaps between ACTIVE emails\n          intervals: Array(activeEmailCount - 1).fill(prev.intervals[0] || 24),\n        }));\n      } else {\n        // If 0 or 1 active emails, no intervals needed\n        setScheduleSettings(prev => ({ ...prev, intervals: [] }));\n      }\n    }\n  }, [step, emailContents, activeEmailIndices]); // Add activeEmailIndices dependency\n\n  const fetchContacts = async () => {\n    if (step === 4) {\n      setLoadingContacts(true);\n      try {\n        const r = await api.get('/contacts');\n        let list = Array.isArray(r.data) ? r.data : r.data?.data || [];\n        setContacts(list.map((c: any) => ({\n          id: c.id || c._id || String(Math.random()),\n          email: c.email,\n          name: c.name || c.fullName || '',\n        })));\n      } catch {\n        setError('Failed to load contacts');\n      } finally {\n        setLoadingContacts(false);\n      }\n    }\n  };\n\n  useEffect(() => {\n    fetchContacts();\n  }, [step]);\n\n  const addManualRecipient = () => {\n    if (!manualRecipient.email) {\n      setError('Please enter an email address.');\n    } else if (!recipients.some(r => r.email === manualRecipient.email)) {\n      setRecipients([...recipients, { id: null, ...manualRecipient }]);\n      setManualRecipient({ email: '', name: '' });\n    } else {\n      setError('Recipient already added.');\n    }\n    setTimeout(() => setError(''), 3000);\n  };\n\n  const addSelectedContacts = () => {\n    const toAdd = contacts.filter(c =>\n      selectedContactIds.includes(c.id) && !recipients.some(r => r.email === c.email)\n    );\n    setRecipients([...recipients, ...toAdd]);\n    setSelectedContactIds([]);\n  };\n  const handleContactSelection = (e: React.ChangeEvent<HTMLSelectElement>) =>\n    setSelectedContactIds(Array.from(e.target.selectedOptions, o => o.value));\n  const removeRecipient = (email: string) =>\n    setRecipients(recipients.filter(r => r.email !== email));\n\n  // Step navigation\n  const handleNext = async () => {\n    setError('');\n\n    // Validation for Step 1\n    if (step === 1 && (!campaignName || !subject || !fromName || !fromEmail)) {\n      setError('Please fill in all required fields.');\n      return;\n    }\n\n    // Validation for Step 2\n    if (step === 2) {\n      console.log('--- handleNext: Step 2 Validation ---');\n      let latestContent: { mjml: string; html: string } | null = null;\n      let savedContentHasHtml = false;\n      let stateHasHtml = false;\n\n      // --- Force save current editor's content FIRST ---\n      if (editorRef.current) {\n        console.log('Forcing editor save for current email:', currentEmail);\n        // Await the result of the now async save method\n        latestContent = await editorRef.current.save(); \n        if (latestContent) {\n          console.log('Async save function returned content.');\n          // Log the returned HTML content (or lack thereof)\n          console.log('Returned MJML:', latestContent.mjml ? latestContent.mjml.substring(0, 50) + '...' : '(empty)');\n          console.log('Returned HTML:', latestContent.html ? latestContent.html.substring(0, 50) + '...' : '(empty)');\n\n          // Check if the *returned* content has HTML\n          if (latestContent.html && latestContent.html.trim()) {\n            console.log('RETURNED content has non-empty HTML.');\n            savedContentHasHtml = true;\n          } else {\n            console.log('RETURNED content has empty or no HTML.');\n          }\n\n          // Trigger state update (async) - Allow this to proceed even if HTML is missing\n          console.log('Triggering handleMjmlSave state update...');\n          handleMjmlSave(latestContent.mjml, latestContent.html);\n\n        } else {\n          // This case might be less likely now if save always returns an object\n          console.warn('editorRef.current.save() did not return expected content object.');\n        }\n      } else {\n         console.error('MjmlEditor ref is not available for forced save.');\n      }\n      // --- End Force save ---\n\n      // --- Validation ---\n      // Since save is now awaited and includes a delay, checking the returned content\n      // (savedContentHasHtml) should be more reliable. We also check state.\n      \n      // Check if the SAVED content belongs to an index currently marked ACTIVE\n      const savedContentIsActive = savedContentHasHtml && activeEmailIndices.includes(currentEmail - 1);\n      if (savedContentIsActive) {\n        console.log('Saved content is for an active email.');\n      }\n\n      console.log('Checking emailContents state AFTER awaiting save/update attempt...');\n      // Check if ANY email in state is BOTH active AND has HTML\n      stateHasHtml = emailContents.some((email, index) => {\n        const hasHtml = email.html && email.html.trim();\n        const isActive = activeEmailIndices.includes(index);\n        if (hasHtml && isActive) {\n             console.log(`Email index ${index} in STATE is ACTIVE and has HTML.`);\n             return true;\n        }\n        return false;\n      });\n      if (!stateHasHtml) {\n          console.log('No email in STATE is both active and has HTML.');\n      }\n\n      // Final decision: Did *either* the direct save (if active) *or* the state check find an ACTIVE email with HTML?\n      const finalHasHtmlCheck = savedContentIsActive || stateHasHtml;\n      console.log('Final check result (Active & HTML):', finalHasHtmlCheck, `(Saved Active: ${savedContentIsActive}, State Active: ${stateHasHtml})`);\n\n      if (!finalHasHtmlCheck) {\n        setError('Please ensure at least one selected email (marked as \\'Sending\\') has valid HTML content.');\n        return; // Stop execution if validation fails specifically for step 2\n      }\n      console.log('Step 2 validation passed (Active HTML check).');\n    }\n\n    // Validation for Step 3\n    if (step === 3) {\n      const cnt = emailContents.filter(e => e.html.trim()).length;\n      if (cnt > 1) {\n        if (scheduleSettings.intervals.length !== cnt - 1) {\n          setError(`Define ${cnt - 1} intervals.`);\n          return;\n        }\n        if (scheduleSettings.intervals.some(v => v <= 0)) {\n          setError('Intervals must be positive.');\n          return;\n        }\n      }\n    }\n\n    // Validation for Step 4\n    if (step === 4 && recipients.length === 0) {\n      setError('Please add at least one recipient.');\n      return;\n    }\n\n    // Proceed to next step if all relevant validations passed and not on the last step\n    if (step < 5) {\n      console.log(`Proceeding from step ${step} to step ${step + 1}`);\n      setStep(step + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    setError('');\n    if (step > 1) setStep(step - 1);\n  };\n\n  // --- Add this handler ---\n  const handleClearEmailContent = (index: number) => {\n    const updated = [...emailContents];\n    updated[index] = { mjml: '', html: '' };\n    setEmailContents(updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n    // Also remove from active indices when cleared\n    setActiveEmailIndices(prev => prev.filter(i => i !== index));\n    console.log(`Cleared content and deactivated email index ${index}`);\n  };\n  // --- End Add Handler ---\n\n  const renderTemplatePicker = () => (\n    <Modal isOpen={showTemplatePicker} onClose={() => setShowTemplatePicker(false)} title=\"Choose Template\">\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\">\n        {loadingTemplates\n          ? <p>Loading...</p>\n          : templatesList.map(t => (\n            <Card\n              key={t.id}\n              className={`cursor-pointer ${selectedTemplate?.id === t.id ? 'ring-2 ring-primary' : ''}`}\n              onClick={() => handleTemplateSelect(t.id)}\n            >\n              <h4 className=\"font-semibold mb-2 truncate\">{t.name}</h4>\n              <div className=\"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\">\n                {t.thumbnailUrl ? <img src={t.thumbnailUrl} alt={t.name} className=\"object-contain h-full w-full\" /> : 'No Preview'}\n              </div>\n            </Card>\n          ))\n        }\n      </div>\n      <div className=\"mt-4 flex justify-end gap-2\">\n        <Button variant=\"secondary\" onClick={() => setShowTemplatePicker(false)}>Cancel</Button>\n        <Button onClick={handleUseTemplate} disabled={!selectedTemplate || loadingTemplates}>\n          Use Selected\n        </Button>\n      </div>\n    </Modal>\n  );\n\n  const renderStepContent = () => {\n    switch (step) {\n      case 1: return (\n        <div className=\"space-y-4\">\n          <Input id=\"campaignName\" name=\"campaignName\" label=\"Campaign Name\" value={campaignName} onChange={e => setCampaignName(e.target.value)} required />\n          <Input id=\"subject\" name=\"subject\" label=\"Email Subject\" value={subject} onChange={e => setSubject(e.target.value)} required />\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input id=\"fromName\" name=\"fromName\" label=\"From Name\" value={fromName} onChange={e => setFromName(e.target.value)} required />\n            <Input\n              id=\"fromEmail\"\n              name=\"fromEmail\"\n              label=\"From Email\"\n              type=\"email\"\n              value={fromEmail}\n              onChange={e => setFromEmail(e.target.value)}\n              disabled={user?.domain?.status === 'active'}\n              required\n              helpText={user?.domain?.status === 'active' ? `Using verified domain: ${user.domain.name}` : 'Verify domain for deliverability.'}\n            />\n          </div>\n          <Input id=\"replyTo\" name=\"replyTo\" label=\"Reply-To Email (optional)\" type=\"email\" value={replyTo} onChange={e => setReplyTo(e.target.value)} />\n        </div>\n      );\n      case 2:\n        const curr = emailContents[currentEmail - 1];\n        // Use stricter validation: check for non-empty HTML\n        const emailsWithContent = emailContents.filter(e => e.html.trim()).length;\n        \n        return (\n          <div className=\"flex flex-col lg:flex-row gap-4 h-full\">\n            {/* Email sequence sidebar */}\n            <div className=\"w-full lg:w-1/5 flex flex-col gap-2\">\n              <h3 className=\"font-semibold mb-2\">Email Sequence (Click to include/exclude):</h3>\n              {Array.from({ length: 10 }).map((_, idx) => {\n                const hasHtmlContent = !!(emailContents[idx].html && emailContents[idx].html.trim());\n                const isSelected = currentEmail === idx + 1;\n                const isActive = activeEmailIndices.includes(idx);\n\n                // Determine button appearance based on content and active state\n                let buttonVariant: \"primary\" | \"secondary\" = \"secondary\";\n                let customClasses = \"\";\n                let buttonText = `Email ${idx + 1}`;\n                let icon = null;\n\n                if (hasHtmlContent) {\n                  if (isActive) {\n                    buttonVariant = \"primary\"; // Has content, is active\n                    icon = <span className=\"ml-1 text-green-500 dark:text-green-400 font-bold\">✓</span>;\n                    buttonText += \" (Sending)\";\n                  } else {\n                    buttonVariant = \"secondary\"; // Has content, but inactive - use secondary variant\n                    customClasses = \"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\"; // Custom styling for inactive\n                    icon = <span className=\"ml-1 text-gray-500 dark:text-gray-400 font-bold\">⏸</span>; // Pause icon maybe?\n                    buttonText += \" (Excluded)\";\n                  }\n                } else {\n                   // No content, always secondary, cannot be active\n                   // Make it look distinct but clickable\n                   customClasses = \"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\"; \n                   buttonText += \" (No Content - Click to Edit)\";\n                }\n                \n                const selectionRingClass = isSelected ? \"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\" : \"\";\n                \n                return (\n                  <Button\n                    key={idx}\n                    variant={buttonVariant} // Will be 'secondary' for no content\n                    onClick={() => {\n                      // Set as current email for editing - ALWAYS DO THIS\n                      setCurrentEmail(idx + 1);\n                      \n                      // Toggle active state ONLY if it has content\n                      if (hasHtmlContent) {\n                        setActiveEmailIndices(prev => \n                          prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx]\n                        );\n                      }\n                    }}\n                    size=\"sm\"\n                    className={`w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses}`}\n                    title={hasHtmlContent ? (isActive ? \"Click to exclude from sending\" : \"Click to include in sending\") : \"Click to edit this email\"}\n                  >\n                    <span className=\"flex-grow truncate\">{buttonText}</span>\n                    {icon} \n                  </Button>\n                );\n              })}\n              \n              {/* Add helper text explaining the buttons */}\n              <div className=\"mt-4 p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm space-y-1\">\n                <p className=\"font-medium mb-1\">Email Status Legend:</p>\n                <p className=\"flex items-center\">\n                  <span className=\"ml-1 mr-2 text-green-500 dark:text-green-400 font-bold\">✓</span> \n                  <span className=\"text-green-700 dark:text-green-400\">Has Content, Will Send</span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"inline-block w-3 h-3 mr-2 bg-gray-400 dark:bg-gray-500 rounded-full\"></span> \n                  <span className=\"text-gray-700 dark:text-gray-300\">Has Content, <span className='font-bold'>Excluded</span></span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"inline-block w-3 h-3 mr-2 border border-dashed border-gray-400 dark:border-gray-600 rounded-full opacity-75\"></span> \n                  <span className=\"text-gray-700 dark:text-gray-300\">No Content (Click to Edit)</span>\n                 </p>\n                 <p className=\"text-xs mt-2 text-gray-600 dark:text-gray-400\">Click emails with content to toggle inclusion. Click any email to edit.</p>\n              </div>\n              \n              <div className=\"mt-4\">\n                <Button variant=\"secondary\" onClick={() => setShowTemplatePicker(true)}>Choose Template</Button>\n                <Button variant=\"secondary\" onClick={handleBrowseTemplates} className=\"mt-2\">Browse All Templates</Button>\n              </div>\n            </div>\n            {/* Editor */}\n            <div className=\"w-full lg:w-4/5 flex flex-col\">\n              <MjmlEditor\n                key={`email-${currentEmail}-${Date.now()}`}\n                ref={editorRef}\n                initialMjml={curr.mjml}\n                onSave={handleMjmlSave}\n                height=\"70vh\"\n              />\n              <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-2\">\n                  Changes are saved automatically. Emails with a green check mark (✓) will be included in your campaign.\n              </p>\n            </div>\n          </div>\n        );\n      case 3: {\n        // Calculate count based on emails that are BOTH active AND have HTML\n        const activeEmailCount = emailContents.reduce((count, email, index) => {\n          if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n            return count + 1;\n          }\n          return count;\n        }, 0);\n        console.log('Active emails with HTML for rendering Step 3:', activeEmailCount);\n\n        // Handlers for main scheduling options\n        const handleScheduleOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n          setSendScheduleOption(e.target.value as 'now' | 'later');\n        };\n        const handleScheduleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n          setScheduledDateTime(e.target.value);\n        };\n\n        // Handlers for interval settings (only relevant if emailCount > 1)\n        const handleIntervalChange = (index: number, value: string) => {\n          const numValue = parseInt(value, 10);\n          if (!isNaN(numValue) && numValue > 0) {\n            const newIntervals = [...scheduleSettings.intervals];\n            newIntervals[index] = numValue;\n            setScheduleSettings({ ...scheduleSettings, intervals: newIntervals });\n          }\n        };\n        const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n          setScheduleSettings({ ...scheduleSettings, unit: e.target.value as 'minutes' | 'hours' | 'days' });\n        };\n\n        return (\n          <div className=\"space-y-6\">\n            {/* --- Start Time Scheduling --- */}\n            <Card>\n              <h2 className=\"text-xl font-semibold mb-4\">Campaign Start Time</h2>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"scheduleNow\"\n                    name=\"scheduleOption\"\n                    type=\"radio\"\n                    value=\"now\"\n                    checked={sendScheduleOption === 'now'}\n                    onChange={handleScheduleOptionChange}\n                    className=\"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  />\n                  <label htmlFor=\"scheduleNow\" className=\"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Send Immediately\n                  </label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"scheduleLater\"\n                    name=\"scheduleOption\"\n                    type=\"radio\"\n                    value=\"later\"\n                    checked={sendScheduleOption === 'later'}\n                    onChange={handleScheduleOptionChange}\n                    className=\"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  />\n                  <label htmlFor=\"scheduleLater\" className=\"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Schedule for Later\n                  </label>\n                </div>\n                {sendScheduleOption === 'later' && (\n                  <div className=\"pl-7 mt-2\">\n                    <Input\n                      id=\"scheduledDateTime\"\n                      name=\"scheduledDateTime\"\n                      type=\"datetime-local\"\n                      value={scheduledDateTime}\n                      onChange={handleScheduleTimeChange}\n                      label=\"Scheduled Date & Time\"\n                      required\n                      className=\"max-w-sm\"\n                    />\n                     <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">Your local timezone.</p>\n                  </div>\n                )}\n              </div>\n            </Card>\n            \n            {/* --- Sequence Interval Timing (Conditional) --- */}\n            {activeEmailCount > 1 && (\n              <Card>\n                 <h2 className=\"text-xl font-semibold mb-4\">Email Sequence Timing</h2>\n                 <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                    Set the time interval between each subsequent email send. The first email is sent according to the 'Campaign Start Time' setting above.\n                 </p>\n                 <div className=\"flex items-center gap-4 mb-6\">\n                   <label htmlFor=\"scheduleUnit\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                     Interval Time Unit:\n                   </label>\n                   <select\n                     id=\"scheduleUnit\"\n                     name=\"scheduleUnit\"\n                     value={scheduleSettings.unit}\n                     onChange={handleUnitChange}\n                     className=\"mt-1 block w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                   >\n                     <option value=\"minutes\">Minutes</option>\n                     <option value=\"hours\">Hours</option>\n                     <option value=\"days\">Days</option>\n                   </select>\n                 </div>\n\n                 {Array.from({ length: activeEmailCount - 1 }).map((_, index) => (\n                   <div key={index} className=\"flex items-center gap-4 mt-4\">\n                     <label htmlFor={`interval-${index}`} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 w-48\">\n                       Wait before sending Email #{index + 2}:\n                     </label>\n                     <Input\n                       id={`interval-${index}`}\n                       name={`interval-${index}`}\n                       type=\"number\"\n                       value={scheduleSettings.intervals[index]?.toString() || '24'}\n                       onChange={(e) => handleIntervalChange(index, e.target.value)}\n                       className=\"w-24 mb-0\" // Adjusted margin\n                       required\n                     />\n                     <span className=\"text-gray-600 dark:text-gray-400 capitalize\">{scheduleSettings.unit}</span>\n                   </div>\n                 ))}\n               </Card>\n             )}\n             \n             {/* Informative text if only one active email */}\n             {activeEmailCount <= 1 && (\n                 <p className=\"text-gray-600 dark:text-gray-400 text-sm px-4\">\n                     Sequence timing options are available when your campaign has more than one selected email with content.\n                 </p>\n             )}\n          </div>\n        );\n      }\n      case 4:\n        // Add Recipients Step\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Add Recipients</h2>\n\n            {/* Manual Add */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Add Manually</h3>\n              <div className=\"md:flex md:items-end md:space-x-3\">\n                <div className=\"flex-grow mb-3 md:mb-0\">\n                  <Input\n                    id=\"manualRecipient\"\n                    name=\"manualRecipient\"\n                    label=\"Email Address\"\n                    type=\"email\"\n                    value={manualRecipient.email}\n                    onChange={(e) => setManualRecipient({ ...manualRecipient, email: e.target.value })}\n                    placeholder=\"Enter email address\"\n                  />\n                </div>\n                {/* Apply Coral CTA Style */}\n                <Button onClick={addManualRecipient} variant=\"secondary\" className=\"btn-cta mt-4 md:mt-0\">Add Recipient</Button>\n              </div>\n            </Card>\n\n            {/* Add from Contacts */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Add From Contacts</h3>\n              {loadingContacts ? (\n                <p>Loading contacts...</p>\n              ) : contacts.length > 0 ? (\n                <div className=\"md:flex md:items-end md:space-x-3\">\n                   <div className=\"flex-grow mb-3 md:mb-0\">\n                      <label htmlFor=\"contactSelect\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Select Contacts (use Ctrl/Cmd to select multiple)\n                      </label>\n                      <select\n                        id=\"contactSelect\"\n                        multiple\n                        value={selectedContactIds}\n                        onChange={handleContactSelection}\n                        className=\"form-input w-full h-32 border border-border rounded-md\"\n                      >\n                        {contacts.map(contact => (\n                          <option key={contact.id} value={contact.id}>\n                            {contact.name ? `${contact.name} (${contact.email})` : contact.email}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  <Button onClick={addSelectedContacts} variant=\"secondary\" className=\"btn-cta mt-4 md:mt-0\">Add Selected</Button>\n                </div>\n              ) : (\n                <p className=\"text-text-secondary\">No contacts found. You can add contacts in the 'Contacts' section.</p>\n              )}\n            </Card>\n\n            {/* Added Recipients List */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Recipients Added ({recipients.length})</h3>\n              {recipients.length > 0 ? (\n                <ul className=\"divide-y divide-border max-h-60 overflow-y-auto\">\n                  {recipients.map((rec, index) => (\n                    <li key={index} className=\"py-2 flex justify-between items-center\">\n                      <span>{rec.name ? `${rec.name} (${rec.email})` : rec.email}</span>\n                      <button onClick={() => removeRecipient(rec.email)} className=\"text-red-500 hover:text-red-700 text-sm\">&times; Remove</button>\n                    </li>\n                  ))}\n                </ul>\n              ) : (\n                <p className=\"text-text-secondary\">No recipients added yet.</p>\n              )}\n            </Card>\n          </div>\n        );\n      case 5: {\n         // Review and Schedule Step\n         const scheduledTimeString = sendScheduleOption === 'later' && scheduledDateTime\n           ? new Date(scheduledDateTime).toLocaleString()\n           : 'Immediately';\n         return (\n           <div className=\"space-y-6\">\n             <h2 className=\"text-xl font-semibold mb-4\">Review & Schedule</h2>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Campaign Details</h3>\n               <p><span className=\"font-semibold\">Name:</span> {campaignName}</p>\n               <p><span className=\"font-semibold\">Subject:</span> {subject}</p>\n               <p><span className=\"font-semibold\">From:</span> {fromName} &lt;{fromEmail}&gt;</p>\n               {replyTo && <p><span className=\"font-semibold\">Reply To:</span> {replyTo}</p>}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Sequence (Emails to Send)</h3>\n               {/* Filter based on ACTIVE and HTML content */}\n               {emailContents.filter((e, idx) => activeEmailIndices.includes(idx) && e.html.trim()).length > 0 ? (\n                 <ul className=\"list-decimal pl-5 space-y-1\">\n                   {emailContents\n                     .map((email, index) => {\n                       // Use stricter validation: check for non-empty HTML AND active status\n                       if (activeEmailIndices.includes(index) && email.html.trim()) {\n                         // Find the index of this email within the *filtered* list of active emails\n                         const activeEmails = emailContents\n                           .map((e, i) => ({ email: e, originalIndex: i })) // Keep track of original index\n                           .filter(({ email, originalIndex }) => \n                             activeEmailIndices.includes(originalIndex) && email.html.trim()\n                           );\n                         const activeIndex = activeEmails.findIndex(item => item.originalIndex === index);\n                         \n                         const emailNumberInActiveSequence = activeIndex + 1; // 1-based index for display\n\n                         let timingText = 'Sent at Campaign Start Time'; // Default text\n                         // Calculate timing based on the position in the *active* sequence\n                         if (emailNumberInActiveSequence > 1 && scheduleSettings.intervals[emailNumberInActiveSequence - 2] !== undefined) {\n                           timingText = `Sent ${scheduleSettings.intervals[emailNumberInActiveSequence - 2]} ${scheduleSettings.unit} after Email #${activeEmails[activeIndex - 1].originalIndex + 1}`;\n                         }\n                       \n                         return (\n                           <li key={index}>\n                             Email #{index + 1} - {timingText}\n                           </li>\n                         );\n                       }\n                       return null; // Don't render anything for inactive/empty emails\n                     })\n                     .filter(Boolean) // Remove null entries from the map result\n                   }\n                 </ul>\n               ) : (\n                 <p className=\"text-text-secondary\">No emails selected or content found for the sequence.</p>\n               )}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Recipients</h3>\n               <p>{recipients.length} recipient(s)</p>\n               {recipients.length > 0 && (\n                 <ul className=\"list-disc pl-5 text-sm max-h-20 overflow-y-auto\">\n                   {recipients.slice(0, 5).map((r, i) => <li key={i}>{r.name ? `${r.name} (${r.email})` : r.email}</li>)}\n                   {recipients.length > 5 && <li>...and {recipients.length - 5} more</li>}\n                 </ul>\n               )}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Schedule</h3>\n               <p>Start sending: {scheduledTimeString}</p>\n             </Card>\n\n             <div className=\"mt-6 flex justify-end\">\n               {/* Coral Style CTA for final step */}\n               <Button \n                 onClick={handleCreateCampaign} \n                 disabled={loading}\n                 className=\"btn-cta\" // Apply Coral CTA style\n               >\n                 {loading ? 'Creating...' : 'Create & Schedule Campaign'}\n               </Button>\n             </div>\n           </div> // Closing the main div for step 5\n         ); // Closing the return statement for case 5\n       } // Closing the switch statement\n       default: return null;\n     } // Closing the outer switch\n   }; // Closing renderStepContent\n\n   // --- Main Component Render ---\n   return (\n     <>\n       <h1 className=\"text-2xl font-bold mb-6 text-text-primary\">Create New Campaign</h1>\n       {error && <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\"><p>{error}</p></div>}\n       {success && <div className=\"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded\"><p>{success}</p></div>}\n       {templateLoading && <div className=\"mb-4 text-text-secondary\">Loading template...</div>}\n\n       {/* Step Indicator */}\n       <div className=\"mb-8\">\n         <ol className=\"flex items-center w-full text-sm font-medium text-center text-text-secondary dark:text-gray-400 sm:text-base\">\n           {[1, 2, 3, 4, 5].map(s => (\n             <li key={s} className={`flex md:w-full items-center ${s === step ? 'text-accent-coral dark:text-blue-500' : ''} ${s < 5 ? 'sm:after:content-[\\'\\'] after:w-full after:h-1 after:border-b after:border-border dark:after:border-gray-700 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10' : ''}`}>\n               <span className={`flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-text-disabled ${s < step ? 'text-text-primary dark:text-gray-200' : ''}`}>\n                 {s < step && (\n                   <svg className=\"w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2.5\" aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                     <path d=\"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z\"/>\n                   </svg>\n                 )}\n                 {s === step && <span className=\"mr-2\">{s}</span>}\n                 {s > step && <span className=\"mr-2\">{s}</span>}\n                 {['Details', 'Content', 'Schedule', 'Recipients', 'Review'][s-1]}\n               </span>\n             </li>\n           ))}\n         </ol>\n       </div>\n\n\n       <Card className=\"mb-6\">\n         {renderStepContent()}\n       </Card>\n\n       {/* Navigation Buttons */}\n       <div className=\"flex justify-between mt-8\">\n         <Button onClick={handlePrevious} disabled={step === 1 || loading} variant=\"secondary\">Previous</Button>\n         {step < 5 ? (\n           <Button onClick={handleNext} disabled={loading} variant=\"primary\">Next</Button>\n         ) : (\n           <Button onClick={handleCreateCampaign} disabled={loading} className=\"btn-cta\"> {/* Replicate final CTA style here */}\n               {loading ? 'Creating...' : 'Create & Schedule Campaign'}\n           </Button>\n         )}\n       </div>\n\n       {renderTemplatePicker()}\n     </>\n   );\n }; // Closing the component function\n\n export default CampaignCreate; // Export the component\n", "import React, { Fragment } from 'react';\r\n\r\nimport {\r\n  Dialog,\r\n  Transition,\r\n} from '@headlessui/react';\r\n\r\nimport Button from './Button'; // Assuming Button component exists\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: React.ReactNode;\r\n  onConfirm?: () => void;\r\n  confirmText?: string;\r\n  confirmVariant?: 'primary' | 'secondary' | 'danger';\r\n  cancelText?: string;\r\n}\r\n\r\nexport const Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  onConfirm,\r\n  confirmText = 'Confirm',\r\n  confirmVariant = 'primary',\r\n  cancelText = 'Cancel',\r\n}) => {\r\n  return (\r\n    <Transition appear show={isOpen} as={Fragment}>\r\n      <Dialog as=\"div\" className=\"relative z-10\" onClose={onClose}>\r\n        <Transition.Child\r\n          as={Fragment}\r\n          enter=\"ease-out duration-300\"\r\n          enterFrom=\"opacity-0\"\r\n          enterTo=\"opacity-100\"\r\n          leave=\"ease-in duration-200\"\r\n          leaveFrom=\"opacity-100\"\r\n          leaveTo=\"opacity-0\"\r\n        >\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" />\r\n        </Transition.Child>\r\n\r\n        <div className=\"fixed inset-0 overflow-y-auto\">\r\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n            <Transition.Child\r\n              as={Fragment}\r\n              enter=\"ease-out duration-300\"\r\n              enterFrom=\"opacity-0 scale-95\"\r\n              enterTo=\"opacity-100 scale-100\"\r\n              leave=\"ease-in duration-200\"\r\n              leaveFrom=\"opacity-100 scale-100\"\r\n              leaveTo=\"opacity-0 scale-95\"\r\n            >\r\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all\">\r\n                <Dialog.Title\r\n                  as=\"h3\"\r\n                  className=\"text-lg font-medium leading-6 text-white mb-4\"\r\n                >\r\n                  {title}\r\n                </Dialog.Title>\r\n                <div className=\"mt-2 text-sm text-gray-300\">\r\n                  {children}\r\n                </div>\r\n\r\n                <div className=\"mt-6 flex justify-end space-x-3\">\r\n                  <Button variant=\"secondary\" onClick={onClose}>\r\n                    {cancelText}\r\n                  </Button>\r\n                  {onConfirm && (\r\n                    <Button variant={confirmVariant} onClick={onConfirm}>\r\n                      {confirmText}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </Dialog.Panel>\r\n            </Transition.Child>\r\n          </div>\r\n        </div>\r\n      </Dialog>\r\n    </Transition>\r\n  );\r\n}; "], "names": ["MjmlEditor", "forwardRef", "_ref", "ref", "initialMjml", "initialHtml", "onSave", "height", "editor<PERSON><PERSON>", "useRef", "grapesEditor", "useEffect", "current", "console", "log", "destroy", "hasMjml", "mjm<PERSON><PERSON><PERSON><PERSON>", "length", "hasHtml", "htmlLength", "editor", "<PERSON><PERSON><PERSON>", "init", "container", "fromElement", "String", "width", "storageManager", "plugins", "grapesjsMjml", "pluginsOpts", "useXmlParser", "resetBlocks", "error", "saveTimeout", "Commands", "has", "add", "run", "mjml", "getHtml", "html", "component", "getWrapper", "setTimeout", "setComponents", "substring", "e", "isSaving", "on", "clearTimeout", "finalMjml", "finalHtml", "mjmlCode", "runCommand", "cmdErr", "warn", "htmlCmdErr", "trim", "destroyError", "initError", "useImperativeHandle", "save", "async", "generatedCode", "Error", "result", "rawMjml", "generatedHtml", "fallbackErr", "Promise", "resolve", "potentiallyUpdatedHtml", "refetchErr", "getEditor", "_jsx", "style", "displayName", "CampaignCreate", "_user$domain", "user", "useAuth", "location", "useLocation", "navigate", "useNavigate", "campaignName", "setCampaignName", "useState", "subject", "setSubject", "fromName", "setFromName", "name", "fromEmail", "setFromEmail", "domain", "status", "replyTo", "setReplyTo", "setError", "success", "setSuccess", "loading", "setLoading", "templateLoading", "setTemplateLoading", "step", "setStep", "currentEmail", "setCurrentEmail", "emailContents", "setEmailContents", "saved", "localStorage", "getItem", "arr", "JSON", "parse", "Array", "isArray", "map", "item", "from", "scheduleSettings", "setScheduleSettings", "intervals", "unit", "campaignPreview", "setCampaignPreview", "showTemplatePicker", "setShowTemplatePicker", "templatesList", "setTemplatesList", "loadingTemplates", "setLoadingTemplates", "selectedTemplate", "setSelectedTemplate", "sendScheduleOption", "setSendScheduleOption", "scheduledDateTime", "setScheduledDateTime", "Date", "now", "toISOString", "slice", "recipients", "setRecipients", "manualRecipient", "set<PERSON>anualRecip<PERSON>", "email", "contacts", "setContacts", "selectedContactIds", "setSelectedContactIds", "loadingContacts", "setLoadingContacts", "activeEmailIndices", "setActiveEmailIndices", "initialActive", "initialContents", "for<PERSON>ach", "index", "push", "templateId", "URLSearchParams", "search", "get", "fetchTemplate", "res", "templateRecommendationService", "getTemplateById", "id", "template", "temp", "updated", "mjml<PERSON><PERSON><PERSON>", "content", "handleMjmlSave", "currentIndex", "setItem", "stringify", "prev", "includes", "sort", "a", "b", "filter", "i", "getAllTemplates", "then", "r", "data", "catch", "finally", "handleUseTemplate", "_id", "<PERSON><PERSON><PERSON><PERSON>", "emailIndex", "handleCreateCampaign", "_activeEmailsToSend$", "activeEmailsToSend", "campaignData", "scheduled", "scheduledFor", "recipientList", "userId", "htmlContent", "schedule", "emailIntervals", "d", "delay", "campaignAPI", "createCampaign", "campaignId", "toLocaleString", "removeItem", "sessionStorage", "err", "_err$response", "_err$response$data", "response", "message", "handleBrowseTemplates", "draft", "activeEmailCount", "reduce", "count", "fill", "_r$data", "api", "list", "c", "Math", "random", "fullName", "fetchContacts", "addManualRecipient", "some", "addSelectedContacts", "toAdd", "handleContactSelection", "target", "selectedOptions", "o", "value", "_jsxs", "_Fragment", "children", "className", "s", "xmlns", "viewBox", "Card", "renderStepContent", "_user$domain2", "_user$domain3", "Input", "label", "onChange", "required", "type", "disabled", "helpText", "curr", "_", "idx", "hasHtmlContent", "isSelected", "isActive", "buttonVariant", "customClasses", "buttonText", "icon", "selectionRingClass", "<PERSON><PERSON>", "variant", "onClick", "size", "title", "handleScheduleOptionChange", "handleScheduleTimeChange", "handleIntervalChange", "numValue", "parseInt", "isNaN", "newIntervals", "handleUnitChange", "checked", "htmlFor", "_scheduleSettings$int", "toString", "placeholder", "multiple", "contact", "rec", "removeRecipient", "scheduledTimeString", "activeEmails", "originalIndex", "activeIndex", "findIndex", "emailNumberInActiveSequence", "timingText", "undefined", "Boolean", "handlePrevious", "latestContent", "savedContentHasHtml", "stateHasHtml", "savedContentIsActive", "finalHasHtmlCheck", "cnt", "v", "Modal", "isOpen", "onClose", "t", "_r$template$content", "contentLength", "handleTemplateSelect", "thumbnailUrl", "src", "alt", "onConfirm", "confirmText", "confirmVariant", "cancelText", "Transition", "appear", "show", "as", "Fragment", "Dialog", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Panel", "Title"], "sourceRoot": ""}