{"version": 3, "file": "static/js/104.4b73d772.chunk.js", "mappings": "+MAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAASC,IAC/BH,EAAqBH,EAAaM,IAAQA,CAAG,IAEjD,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,oBAATC,MACT,qBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,oBAAhBC,YAE/BC,EAAUC,GACyB,oBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,YAEjCI,EAAeA,CAAAC,EAAiBC,EAAgBC,KAAa,IAA7C,KAAEf,EAAI,KAAEC,GAAMY,EAChC,OAAIX,GAAkBD,aAAgBE,KAC9BW,EACOC,EAASd,GAGTe,EAAmBf,EAAMc,GAG/BR,IACJN,aAAgBO,aAAeC,EAAOR,IACnCa,EACOC,EAASd,GAGTe,EAAmB,IAAIb,KAAK,CAACF,IAAQc,GAI7CA,EAASvB,EAAaQ,IAASC,GAAQ,IAAI,EAEhDe,EAAqBA,CAACf,EAAMc,KAC9B,MAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,IAC/B,EACOH,EAAWM,cAActB,EAAK,EAEzC,SAASuB,EAAQvB,GACb,OAAIA,aAAgBwB,WACTxB,EAEFA,aAAgBO,YACd,IAAIiB,WAAWxB,GAGf,IAAIwB,WAAWxB,EAAKU,OAAQV,EAAKyB,WAAYzB,EAAK0B,WAEjE,CACA,IAAIC,EClDJ,MAAMC,EAAQ,mEAERC,EAA+B,qBAAfL,WAA6B,GAAK,IAAIA,WAAW,KACvE,IAAK,IAAIM,GAAI,EAAGA,GAAIF,GAAcE,KAC9BD,EAAOD,EAAMG,WAAWD,KAAMA,GAE3B,MCLDxB,EAA+C,oBAAhBC,YACxByB,EAAeA,CAACC,EAAeC,KACxC,GAA6B,kBAAlBD,EACP,MAAO,CACHlC,KAAM,UACNC,KAAMmC,EAAUF,EAAeC,IAGvC,MAAMnC,EAAOkC,EAAcG,OAAO,GAClC,GAAa,MAATrC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMqC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBxC,EAAqBK,GAIjCkC,EAAcM,OAAS,EACxB,CACExC,KAAML,EAAqBK,GAC3BC,KAAMiC,EAAcK,UAAU,IAEhC,CACEvC,KAAML,EAAqBK,IARxBD,CASN,EAEHuC,EAAqBA,CAACrC,EAAMkC,KAC9B,GAAI5B,EAAuB,CACvB,MAAMkC,EDTSC,KACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOF,OAAeQ,EAAMN,EAAOF,OAAWS,EAAI,EACnC,MAA9BP,EAAOA,EAAOF,OAAS,KACvBO,IACkC,MAA9BL,EAAOA,EAAOF,OAAS,IACvBO,KAGR,MAAMG,EAAc,IAAI1C,YAAYuC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWb,EAAOY,EAAOV,WAAWD,IACpCa,EAAWd,EAAOY,EAAOV,WAAWD,EAAI,IACxCc,EAAWf,EAAOY,EAAOV,WAAWD,EAAI,IACxCe,EAAWhB,EAAOY,EAAOV,WAAWD,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CAAW,ECTEE,CAAOnD,GACvB,OAAOmC,EAAUK,EAASN,EAC9B,CAEI,MAAO,CAAEO,QAAQ,EAAMzC,OAC3B,EAEEmC,EAAYA,CAACnC,EAAMkC,IAEZ,SADDA,EAEIlC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,OCvDtB0C,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvBC,SAAAA,CAAUC,EAAQC,IHmBnB,SAA8BD,EAAQ5C,GACrCb,GAAkByD,EAAO1D,gBAAgBE,KAClCwD,EAAO1D,KAAK4D,cAAcC,KAAKtC,GAASsC,KAAK/C,GAE/CR,IACJoD,EAAO1D,gBAAgBO,aAAeC,EAAOkD,EAAO1D,OAC9Cc,EAASS,EAAQmC,EAAO1D,OAEnCW,EAAa+C,GAAQ,GAAQI,IACpBnC,IACDA,EAAe,IAAIoC,aAEvBjD,EAASa,EAAaqC,OAAOF,GAAS,GAE9C,CGhCYG,CAAqBP,GAASzB,IAC1B,MAAMiC,EAAgBjC,EAAcM,OACpC,IAAI4B,EAEJ,GAAID,EAAgB,IAChBC,EAAS,IAAI3C,WAAW,GACxB,IAAI4C,SAASD,EAAOzD,QAAQ2D,SAAS,EAAGH,QAEvC,GAAIA,EAAgB,MAAO,CAC5BC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOzD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGL,EACtB,KACK,CACDC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOzD,QACjC4D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAOP,GAChC,CAEIR,EAAO1D,MAA+B,kBAAhB0D,EAAO1D,OAC7BmE,EAAO,IAAM,KAEjBR,EAAWe,QAAQP,GACnBR,EAAWe,QAAQzC,EAAc,GAEzC,GAER,CACA,IAAI0C,EACJ,SAASC,EAAYC,GACjB,OAAOA,EAAOC,QAAO,CAACC,EAAKC,IAAUD,EAAMC,EAAMzC,QAAQ,EAC7D,CACA,SAAS0C,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAGtC,SAAW2C,EACrB,OAAOL,EAAOM,QAElB,MAAMzE,EAAS,IAAIc,WAAW0D,GAC9B,IAAIE,EAAI,EACR,IAAK,IAAItD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBpB,EAAOoB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAGtC,SAChBsC,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAOtC,QAAU6C,EAAIP,EAAO,GAAGtC,SAC/BsC,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzB1E,CACX,CC/EO,SAAS4E,EAAQ7E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIZ,KAAOyF,EAAQnF,UACtBM,EAAIZ,GAAOyF,EAAQnF,UAAUN,GAE/B,OAAOY,CACT,CAhBkB8E,CAAM9E,EACxB,CA0BA6E,EAAQnF,UAAUqF,GAClBF,EAAQnF,UAAUsF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAC,GACrCD,KAAKC,WAAW,IAAMH,GAASE,KAAKC,WAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQnF,UAAU4F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UACjB,CAIA,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQnF,UAAU6F,IAClBV,EAAQnF,UAAUgG,eAClBb,EAAQnF,UAAUiG,mBAClBd,EAAQnF,UAAUkG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,WAAaD,KAAKC,YAAc,CAAC,EAGlC,GAAKK,UAAU3D,OAEjB,OADAqD,KAAKC,WAAa,CAAC,EACZD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,WAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAU3D,OAEjB,cADOqD,KAAKC,WAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAUhE,OAAQT,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,KACF,CASF,OAJyB,IAArByE,EAAUhE,eACLqD,KAAKC,WAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQnF,UAAUsG,KAAO,SAASf,GAChCE,KAAKC,WAAaD,KAAKC,YAAc,CAAC,EAKtC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAU3D,OAAS,GACpCgE,EAAYX,KAAKC,WAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAU3D,OAAQT,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWiB,GADhBwD,EAAYA,EAAUlB,MAAM,IACI9C,OAAQT,EAAIiB,IAAOjB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADKnE,CAKlC,OAAOqD,IACT,EAGAN,EAAQnF,UAAUyG,aAAetB,EAAQnF,UAAUsG,KAUnDnB,EAAQnF,UAAU0G,UAAY,SAASnB,GAErC,OADAE,KAAKC,WAAaD,KAAKC,YAAc,CAAC,EAC/BD,KAAKC,WAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQnF,UAAU2G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAOnD,MAClC,ECxKO,MAAMwE,EACqC,oBAAZC,SAAqD,oBAApBA,QAAQC,QAE/DX,GAAOU,QAAQC,UAAUpD,KAAKyC,GAG/B,CAACA,EAAIY,IAAiBA,EAAaZ,EAAI,GAGzCa,EACW,qBAATC,KACAA,KAEgB,qBAAXC,OACLA,OAGAC,SAAS,cAATA,GChBR,SAASC,EAAK9G,GAAc,QAAA+G,EAAAtB,UAAA3D,OAANkF,EAAI,IAAAd,MAAAa,EAAA,EAAAA,EAAA,KAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJD,EAAIC,EAAA,GAAAxB,UAAAwB,GAC7B,OAAOD,EAAK3C,QAAO,CAACC,EAAK4C,KACjBlH,EAAImH,eAAeD,KACnB5C,EAAI4C,GAAKlH,EAAIkH,IAEV5C,IACR,CAAC,EACR,CAEA,MAAM8C,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBzH,EAAK0H,GACnCA,EAAKC,iBACL3H,EAAIyG,aAAeW,EAAmBQ,KAAKP,GAC3CrH,EAAI6H,eAAiBN,EAAqBK,KAAKP,KAG/CrH,EAAIyG,aAAeY,EAAWC,WAAWM,KAAKP,GAC9CrH,EAAI6H,eAAiBR,EAAWG,aAAaI,KAAKP,GAE1D,CAkCO,SAASS,IACZ,OAAQC,KAAKC,MAAMrI,SAAS,IAAIkC,UAAU,GACtCoG,KAAKC,SAASvI,SAAS,IAAIkC,UAAU,EAAG,EAChD,CCtDO,MAAMsG,UAAuBC,MAChCC,WAAAA,CAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACNnD,KAAKoD,YAAcA,EACnBpD,KAAKqD,QAAUA,EACfrD,KAAK7F,KAAO,gBAChB,EAEG,MAAMoJ,UAAkB7D,EAO3BwD,WAAAA,CAAYX,GACRe,QACAtD,KAAKwD,UAAW,EAChBlB,EAAsBtC,KAAMuC,GAC5BvC,KAAKuC,KAAOA,EACZvC,KAAKyD,MAAQlB,EAAKkB,MAClBzD,KAAK0D,OAASnB,EAAKmB,OACnB1D,KAAK/E,gBAAkBsH,EAAKoB,WAChC,CAUAC,OAAAA,CAAQT,EAAQC,EAAaC,GAEzB,OADAC,MAAMtC,aAAa,QAAS,IAAIgC,EAAeG,EAAQC,EAAaC,IAC7DrD,IACX,CAIA6D,IAAAA,GAGI,OAFA7D,KAAK8D,WAAa,UAClB9D,KAAK+D,SACE/D,IACX,CAIAgE,KAAAA,GAKI,MAJwB,YAApBhE,KAAK8D,YAAgD,SAApB9D,KAAK8D,aACtC9D,KAAKiE,UACLjE,KAAKkE,WAEFlE,IACX,CAMAmE,IAAAA,CAAKC,GACuB,SAApBpE,KAAK8D,YACL9D,KAAKqE,MAAMD,EAKnB,CAMAE,MAAAA,GACItE,KAAK8D,WAAa,OAClB9D,KAAKwD,UAAW,EAChBF,MAAMtC,aAAa,OACvB,CAOAuD,MAAAA,CAAOnK,GACH,MAAM0D,EAAS1B,EAAahC,EAAM4F,KAAK0D,OAAOpH,YAC9C0D,KAAKwE,SAAS1G,EAClB,CAMA0G,QAAAA,CAAS1G,GACLwF,MAAMtC,aAAa,SAAUlD,EACjC,CAMAoG,OAAAA,CAAQO,GACJzE,KAAK8D,WAAa,SAClBR,MAAMtC,aAAa,QAASyD,EAChC,CAMAC,KAAAA,CAAMC,GAAW,CACjBC,SAAAA,CAAUC,GAAoB,IAAZpB,EAAKnD,UAAA3D,OAAA,QAAAmI,IAAAxE,UAAA,GAAAA,UAAA,GAAG,CAAC,EACvB,OAAQuE,EACJ,MACA7E,KAAK+E,YACL/E,KAAKgF,QACLhF,KAAKuC,KAAK0C,KACVjF,KAAKkF,OAAOzB,EACpB,CACAsB,SAAAA,GACI,MAAMI,EAAWnF,KAAKuC,KAAK4C,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,GACtE,CACAH,KAAAA,GACI,OAAIhF,KAAKuC,KAAK8C,OACRrF,KAAKuC,KAAK+C,QAAUC,OAA0B,MAAnBvF,KAAKuC,KAAK8C,QACjCrF,KAAKuC,KAAK+C,QAAqC,KAA3BC,OAAOvF,KAAKuC,KAAK8C,OACpC,IAAMrF,KAAKuC,KAAK8C,KAGhB,EAEf,CACAH,MAAAA,CAAOzB,GACH,MAAM+B,EClIP,SAAgB3K,GACnB,IAAI4K,EAAM,GACV,IAAK,IAAIvJ,KAAKrB,EACNA,EAAImH,eAAe9F,KACfuJ,EAAI9I,SACJ8I,GAAO,KACXA,GAAOC,mBAAmBxJ,GAAK,IAAMwJ,mBAAmB7K,EAAIqB,KAGpE,OAAOuJ,CACX,CDwH6BrH,CAAOqF,GAC5B,OAAO+B,EAAa7I,OAAS,IAAM6I,EAAe,EACtD,EEzIG,MAAMG,UAAgBpC,EACzBL,WAAAA,GACII,SAAShD,WACTN,KAAK4F,UAAW,CACpB,CACA,QAAIC,GACA,MAAO,SACX,CAOA9B,MAAAA,GACI/D,KAAK8F,OACT,CAOApB,KAAAA,CAAMC,GACF3E,KAAK8D,WAAa,UAClB,MAAMY,EAAQA,KACV1E,KAAK8D,WAAa,SAClBa,GAAS,EAEb,GAAI3E,KAAK4F,WAAa5F,KAAKwD,SAAU,CACjC,IAAIuC,EAAQ,EACR/F,KAAK4F,WACLG,IACA/F,KAAKG,KAAK,gBAAgB,aACpB4F,GAASrB,GACf,KAEC1E,KAAKwD,WACNuC,IACA/F,KAAKG,KAAK,SAAS,aACb4F,GAASrB,GACf,IAER,MAEIA,GAER,CAMAoB,KAAAA,GACI9F,KAAK4F,UAAW,EAChB5F,KAAKgG,SACLhG,KAAKgB,aAAa,OACtB,CAMAuD,MAAAA,CAAOnK,GN/CW6L,EAACC,EAAgB5J,KACnC,MAAM6J,EAAiBD,EAAezK,MAAM+B,GACtC4G,EAAU,GAChB,IAAK,IAAIlI,EAAI,EAAGA,EAAIiK,EAAexJ,OAAQT,IAAK,CAC5C,MAAMkK,EAAgBhK,EAAa+J,EAAejK,GAAII,GAEtD,GADA8H,EAAQlE,KAAKkG,GACc,UAAvBA,EAAcjM,KACd,KAER,CACA,OAAOiK,CAAO,EMoDV6B,CAAc7L,EAAM4F,KAAK0D,OAAOpH,YAAYtC,SAd1B8D,IAMd,GAJI,YAAckC,KAAK8D,YAA8B,SAAhBhG,EAAO3D,MACxC6F,KAAKsE,SAGL,UAAYxG,EAAO3D,KAEnB,OADA6F,KAAKkE,QAAQ,CAAEd,YAAa,oCACrB,EAGXpD,KAAKwE,SAAS1G,EAAO,IAKrB,WAAakC,KAAK8D,aAElB9D,KAAK4F,UAAW,EAChB5F,KAAKgB,aAAa,gBACd,SAAWhB,KAAK8D,YAChB9D,KAAK8F,QAKjB,CAMA7B,OAAAA,GACI,MAAMD,EAAQA,KACVhE,KAAKqE,MAAM,CAAC,CAAElK,KAAM,UAAW,EAE/B,SAAW6F,KAAK8D,WAChBE,IAKAhE,KAAKG,KAAK,OAAQ6D,EAE1B,CAOAK,KAAAA,CAAMD,GACFpE,KAAKwD,UAAW,ENnHF6C,EAACjC,EAASlJ,KAE5B,MAAMyB,EAASyH,EAAQzH,OACjBwJ,EAAiB,IAAIpF,MAAMpE,GACjC,IAAI2J,EAAQ,EACZlC,EAAQpK,SAAQ,CAAC8D,EAAQ5B,KAErBnB,EAAa+C,GAAQ,GAAQzB,IACzB8J,EAAejK,GAAKG,IACdiK,IAAU3J,GACZzB,EAASiL,EAAeI,KAAK/I,GACjC,GACF,GACJ,EMuGE6I,CAAcjC,GAAUhK,IACpB4F,KAAKwG,QAAQpM,GAAM,KACf4F,KAAKwD,UAAW,EAChBxD,KAAKgB,aAAa,QAAQ,GAC5B,GAEV,CAMAyF,GAAAA,GACI,MAAM5B,EAAS7E,KAAKuC,KAAK+C,OAAS,QAAU,OACtC7B,EAAQzD,KAAKyD,OAAS,CAAC,EAQ7B,OANI,IAAUzD,KAAKuC,KAAKmE,oBACpBjD,EAAMzD,KAAKuC,KAAKoE,gBAAkBhE,KAEjC3C,KAAK/E,gBAAmBwI,EAAMmD,MAC/BnD,EAAMoD,IAAM,GAET7G,KAAK4E,UAAUC,EAAQpB,EAClC,EC9IJ,IAAIqD,GAAQ,EACZ,IACIA,EAAkC,qBAAnBC,gBACX,oBAAqB,IAAIA,cACjC,CACA,MAAOC,IAEH,CAEG,MAAMC,EAAUH,ECLvB,SAASI,IAAU,CACZ,MAAMC,UAAgBxB,EAOzBzC,WAAAA,CAAYX,GAER,GADAe,MAAMf,GACkB,qBAAb6E,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAIjC,EAAO+B,SAAS/B,KAEfA,IACDA,EAAOgC,EAAQ,MAAQ,MAE3BrH,KAAKuH,GACoB,qBAAbH,UACJ7E,EAAK4C,WAAaiC,SAASjC,UAC3BE,IAAS9C,EAAK8C,IAC1B,CACJ,CAQAmB,OAAAA,CAAQpM,EAAM2F,GACV,MAAMyH,EAAMxH,KAAKyH,QAAQ,CACrBC,OAAQ,OACRtN,KAAMA,IAEVoN,EAAI5H,GAAG,UAAWG,GAClByH,EAAI5H,GAAG,SAAS,CAAC+H,EAAWtE,KACxBrD,KAAK4D,QAAQ,iBAAkB+D,EAAWtE,EAAQ,GAE1D,CAMA2C,MAAAA,GACI,MAAMwB,EAAMxH,KAAKyH,UACjBD,EAAI5H,GAAG,OAAQI,KAAKuE,OAAO9B,KAAKzC,OAChCwH,EAAI5H,GAAG,SAAS,CAAC+H,EAAWtE,KACxBrD,KAAK4D,QAAQ,iBAAkB+D,EAAWtE,EAAQ,IAEtDrD,KAAK4H,QAAUJ,CACnB,EAEG,MAAMK,UAAgBnI,EAOzBwD,WAAAA,CAAY4E,EAAerB,EAAKlE,GAC5Be,QACAtD,KAAK8H,cAAgBA,EACrBxF,EAAsBtC,KAAMuC,GAC5BvC,KAAK+H,MAAQxF,EACbvC,KAAKgI,QAAUzF,EAAKmF,QAAU,MAC9B1H,KAAKiI,KAAOxB,EACZzG,KAAKkI,WAAQpD,IAAcvC,EAAKnI,KAAOmI,EAAKnI,KAAO,KACnD4F,KAAKmI,SACT,CAMAA,OAAAA,GACI,IAAIC,EACJ,MAAM7F,EAAOZ,EAAK3B,KAAK+H,MAAO,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aAClHxF,EAAK8F,UAAYrI,KAAK+H,MAAMR,GAC5B,MAAMe,EAAOtI,KAAKuI,KAAOvI,KAAK8H,cAAcvF,GAC5C,IACI+F,EAAIzE,KAAK7D,KAAKgI,QAAShI,KAAKiI,MAAM,GAClC,IACI,GAAIjI,KAAK+H,MAAMS,aAAc,CAEzBF,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACvD,IAAK,IAAIvM,KAAK8D,KAAK+H,MAAMS,aACjBxI,KAAK+H,MAAMS,aAAaxG,eAAe9F,IACvCoM,EAAII,iBAAiBxM,EAAG8D,KAAK+H,MAAMS,aAAatM,GAG5D,CACJ,CACA,MAAOyM,GAAK,CACZ,GAAI,SAAW3I,KAAKgI,QAChB,IACIM,EAAII,iBAAiB,eAAgB,2BACzC,CACA,MAAOC,GAAK,CAEhB,IACIL,EAAII,iBAAiB,SAAU,MACnC,CACA,MAAOC,GAAK,CACoB,QAA/BP,EAAKpI,KAAK+H,MAAMa,iBAA8B,IAAPR,GAAyBA,EAAGS,WAAWP,GAE3E,oBAAqBA,IACrBA,EAAIQ,gBAAkB9I,KAAK+H,MAAMe,iBAEjC9I,KAAK+H,MAAMgB,iBACXT,EAAIU,QAAUhJ,KAAK+H,MAAMgB,gBAE7BT,EAAIW,mBAAqB,KACrB,IAAIb,EACmB,IAAnBE,EAAIxE,aAC4B,QAA/BsE,EAAKpI,KAAK+H,MAAMa,iBAA8B,IAAPR,GAAyBA,EAAGc,aAEpEZ,EAAIa,kBAAkB,gBAEtB,IAAMb,EAAIxE,aAEV,MAAQwE,EAAIc,QAAU,OAASd,EAAIc,OACnCpJ,KAAKqJ,UAKLrJ,KAAKsB,cAAa,KACdtB,KAAKsJ,SAA+B,kBAAfhB,EAAIc,OAAsBd,EAAIc,OAAS,EAAE,GAC/D,GACP,EAEJd,EAAInE,KAAKnE,KAAKkI,MAClB,CACA,MAAOS,GAOH,YAHA3I,KAAKsB,cAAa,KACdtB,KAAKsJ,SAASX,EAAE,GACjB,EAEP,CACwB,qBAAbY,WACPvJ,KAAKwJ,OAAS3B,EAAQ4B,gBACtB5B,EAAQ6B,SAAS1J,KAAKwJ,QAAUxJ,KAExC,CAMAsJ,QAAAA,CAAStC,GACLhH,KAAKgB,aAAa,QAASgG,EAAKhH,KAAKuI,MACrCvI,KAAK2J,UAAS,EAClB,CAMAA,QAAAA,CAASC,GACL,GAAI,qBAAuB5J,KAAKuI,MAAQ,OAASvI,KAAKuI,KAAtD,CAIA,GADAvI,KAAKuI,KAAKU,mBAAqB/B,EAC3B0C,EACA,IACI5J,KAAKuI,KAAKsB,OACd,CACA,MAAOlB,GAAK,CAEQ,qBAAbY,iBACA1B,EAAQ6B,SAAS1J,KAAKwJ,QAEjCxJ,KAAKuI,KAAO,IAXZ,CAYJ,CAMAc,OAAAA,GACI,MAAMjP,EAAO4F,KAAKuI,KAAKuB,aACV,OAAT1P,IACA4F,KAAKgB,aAAa,OAAQ5G,GAC1B4F,KAAKgB,aAAa,WAClBhB,KAAK2J,WAEb,CAMAE,KAAAA,GACI7J,KAAK2J,UACT,EASJ,GAPA9B,EAAQ4B,cAAgB,EACxB5B,EAAQ6B,SAAW,CAAC,EAMI,qBAAbH,SAEP,GAA2B,oBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,oBAArBnK,iBAAiC,CAE7CA,iBADyB,eAAgB,EAAa,WAAa,SAChCmK,GAAe,EACtD,CAEJ,SAASA,IACL,IAAK,IAAI9N,KAAK2L,EAAQ6B,SACd7B,EAAQ6B,SAAS1H,eAAe9F,IAChC2L,EAAQ6B,SAASxN,GAAG2N,OAGhC,CACA,MAAMI,EAAW,WACb,MAAM3B,EAAM4B,EAAW,CACnB7B,SAAS,IAEb,OAAOC,GAA4B,OAArBA,EAAI6B,YACtB,CALiB,GAwBjB,SAASD,EAAW3H,GAChB,MAAM8F,EAAU9F,EAAK8F,QAErB,IACI,GAAI,qBAAuBtB,kBAAoBsB,GAAWpB,GACtD,OAAO,IAAIF,cAEnB,CACA,MAAO4B,GAAK,CACZ,IAAKN,EACD,IACI,OAAO,IAAInG,EAAW,CAAC,UAAUkI,OAAO,UAAU7D,KAAK,OAAM,oBACjE,CACA,MAAOoC,GAAK,CAEpB,CCzQA,MAAM0B,EAAqC,qBAAdC,WACI,kBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACf,MAAMC,UAAelH,EACxB,QAAIsC,GACA,MAAO,WACX,CACA9B,MAAAA,GACI,MAAM0C,EAAMzG,KAAKyG,MACXiE,EAAY1K,KAAKuC,KAAKmI,UAEtBnI,EAAO8H,EACP,CAAC,EACD1I,EAAK3B,KAAKuC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMvC,KAAKuC,KAAKiG,eACVjG,EAAKoI,QAAU3K,KAAKuC,KAAKiG,cAE7B,IACIxI,KAAK4K,GAAK5K,KAAK6K,aAAapE,EAAKiE,EAAWnI,EAChD,CACA,MAAOyE,IACH,OAAOhH,KAAKgB,aAAa,QAASgG,GACtC,CACAhH,KAAK4K,GAAGtO,WAAa0D,KAAK0D,OAAOpH,WACjC0D,KAAK8K,mBACT,CAMAA,iBAAAA,GACI9K,KAAK4K,GAAGG,OAAS,KACT/K,KAAKuC,KAAKyI,WACVhL,KAAK4K,GAAGK,QAAQC,QAEpBlL,KAAKsE,QAAQ,EAEjBtE,KAAK4K,GAAGO,QAAWC,GAAepL,KAAKkE,QAAQ,CAC3Cd,YAAa,8BACbC,QAAS+H,IAEbpL,KAAK4K,GAAGS,UAAaC,GAAOtL,KAAKuE,OAAO+G,EAAGlR,MAC3C4F,KAAK4K,GAAGW,QAAW5C,GAAM3I,KAAK4D,QAAQ,kBAAmB+E,EAC7D,CACAtE,KAAAA,CAAMD,GACFpE,KAAKwD,UAAW,EAGhB,IAAK,IAAItH,EAAI,EAAGA,EAAIkI,EAAQzH,OAAQT,IAAK,CACrC,MAAM4B,EAASsG,EAAQlI,GACjBsP,EAAatP,IAAMkI,EAAQzH,OAAS,EAC1C5B,EAAa+C,EAAQkC,KAAK/E,gBAAiBb,IAIvC,IACI4F,KAAKwG,QAAQ1I,EAAQ1D,EACzB,CACA,MAAOuO,GACP,CACI6C,GAGArK,GAAS,KACLnB,KAAKwD,UAAW,EAChBxD,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKsB,aACZ,GAER,CACJ,CACA2C,OAAAA,GAC2B,qBAAZjE,KAAK4K,KACZ5K,KAAK4K,GAAGW,QAAU,OAClBvL,KAAK4K,GAAG5G,QACRhE,KAAK4K,GAAK,KAElB,CAMAnE,GAAAA,GACI,MAAM5B,EAAS7E,KAAKuC,KAAK+C,OAAS,MAAQ,KACpC7B,EAAQzD,KAAKyD,OAAS,CAAC,EAS7B,OAPIzD,KAAKuC,KAAKmE,oBACVjD,EAAMzD,KAAKuC,KAAKoE,gBAAkBhE,KAGjC3C,KAAK/E,iBACNwI,EAAMoD,IAAM,GAET7G,KAAK4E,UAAUC,EAAQpB,EAClC,EAEJ,MAAMgI,EAAgBvJ,EAAWwJ,WAAaxJ,EAAWyJ,aCpGlD,MAAMC,EAAa,CACtBC,UD6GG,cAAiBpB,EACpBI,YAAAA,CAAapE,EAAKiE,EAAWnI,GACzB,OAAQ8H,EAIF,IAAIoB,EAAchF,EAAKiE,EAAWnI,GAHlCmI,EACI,IAAIe,EAAchF,EAAKiE,GACvB,IAAIe,EAAchF,EAEhC,CACAD,OAAAA,CAAQsF,EAAS1R,GACb4F,KAAK4K,GAAGzG,KAAK/J,EACjB,GCtHA2R,aCMG,cAAiBxI,EACpB,QAAIsC,GACA,MAAO,cACX,CACA9B,MAAAA,GACI,IAEI/D,KAAKgM,WAAa,IAAIC,aAAajM,KAAK4E,UAAU,SAAU5E,KAAKuC,KAAK2J,iBAAiBlM,KAAK6F,MAChG,CACA,MAAOmB,IACH,OAAOhH,KAAKgB,aAAa,QAASgG,GACtC,CACAhH,KAAKgM,WAAWG,OACXlO,MAAK,KACN+B,KAAKkE,SAAS,IAEbkI,OAAOpF,IACRhH,KAAK4D,QAAQ,qBAAsBoD,EAAI,IAG3ChH,KAAKgM,WAAWK,MAAMpO,MAAK,KACvB+B,KAAKgM,WAAWM,4BAA4BrO,MAAMsO,IAC9C,MAAMC,EXqDf,SAAmCC,EAAYnQ,GAC7CyC,IACDA,EAAe,IAAI2N,aAEvB,MAAMzN,EAAS,GACf,IAAI0N,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAIjP,gBAAgB,CACvBC,SAAAA,CAAUuB,EAAOrB,GAEb,IADAkB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVuN,EAAqC,CACrC,GAAI3N,EAAYC,GAAU,EACtB,MAEJ,MAAMV,EAASc,EAAaJ,EAAQ,GACpC4N,EAAkC,OAAV,IAAZtO,EAAO,IACnBqO,EAA6B,IAAZrO,EAAO,GAEpBoO,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,CAEhB,MACK,GAAc,IAAVD,EAAiD,CACtD,GAAI3N,EAAYC,GAAU,EACtB,MAEJ,MAAM6N,EAAczN,EAAaJ,EAAQ,GACzC2N,EAAiB,IAAIpO,SAASsO,EAAYhS,OAAQgS,EAAYjR,WAAYiR,EAAYnQ,QAAQoQ,UAAU,GACxGJ,EAAQ,CACZ,MACK,GAAc,IAAVA,EAAiD,CACtD,GAAI3N,EAAYC,GAAU,EACtB,MAEJ,MAAM6N,EAAczN,EAAaJ,EAAQ,GACnCP,EAAO,IAAIF,SAASsO,EAAYhS,OAAQgS,EAAYjR,WAAYiR,EAAYnQ,QAC5EqQ,EAAItO,EAAKuO,UAAU,GACzB,GAAID,EAAIlK,KAAKoK,IAAI,EAAG,IAAW,EAAG,CAE9BnP,EAAWe,QAAQ5E,GACnB,KACJ,CACA0S,EAAiBI,EAAIlK,KAAKoK,IAAI,EAAG,IAAMxO,EAAKuO,UAAU,GACtDN,EAAQ,CACZ,KACK,CACD,GAAI3N,EAAYC,GAAU2N,EACtB,MAEJ,MAAMxS,EAAOiF,EAAaJ,EAAQ2N,GAClC7O,EAAWe,QAAQ1C,EAAayQ,EAAWzS,EAAO2E,EAAaxB,OAAOnD,GAAOkC,IAC7EqQ,EAAQ,CACZ,CACA,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrD1O,EAAWe,QAAQ5E,GACnB,KACJ,CACJ,CACJ,GAER,CWxHsCiT,CAA0B5H,OAAO6H,iBAAkBpN,KAAK0D,OAAOpH,YAC/E+Q,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgB9P,IACtB8P,EAAcH,SAASI,OAAOnB,EAAO/I,UACrCxD,KAAK2N,QAAUF,EAAcjK,SAASoK,YACtC,MAAMC,EAAOA,KACTR,EACKQ,OACA5P,MAAKjD,IAAqB,IAApB,KAAE8S,EAAI,MAAEhH,GAAO9L,EAClB8S,IAGJ9N,KAAKwE,SAASsC,GACd+G,IAAM,IAELzB,OAAOpF,OACV,EAEN6G,IACA,MAAM/P,EAAS,CAAE3D,KAAM,QACnB6F,KAAKyD,MAAMmD,MACX9I,EAAO1D,KAAO,WAAW4F,KAAKyD,MAAMmD,SAExC5G,KAAK2N,QAAQtJ,MAAMvG,GAAQG,MAAK,IAAM+B,KAAKsE,UAAS,GACtD,GAEV,CACAD,KAAAA,CAAMD,GACFpE,KAAKwD,UAAW,EAChB,IAAK,IAAItH,EAAI,EAAGA,EAAIkI,EAAQzH,OAAQT,IAAK,CACrC,MAAM4B,EAASsG,EAAQlI,GACjBsP,EAAatP,IAAMkI,EAAQzH,OAAS,EAC1CqD,KAAK2N,QAAQtJ,MAAMvG,GAAQG,MAAK,KACxBuN,GACArK,GAAS,KACLnB,KAAKwD,UAAW,EAChBxD,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKsB,aACZ,GAER,CACJ,CACA2C,OAAAA,GACI,IAAImE,EACuB,QAA1BA,EAAKpI,KAAKgM,kBAA+B,IAAP5D,GAAyBA,EAAGpE,OACnE,GDxEA+J,QF8OG,cAAkB5G,EACrBjE,WAAAA,CAAYX,GACRe,MAAMf,GACN,MAAMoB,EAAcpB,GAAQA,EAAKoB,YACjC3D,KAAK/E,eAAiBgP,IAAYtG,CACtC,CACA8D,OAAAA,GAAmB,IAAXlF,EAAIjC,UAAA3D,OAAA,QAAAmI,IAAAxE,UAAA,GAAAA,UAAA,GAAG,CAAC,EAEZ,OADA1G,OAAOoU,OAAOzL,EAAM,CAAEgF,GAAIvH,KAAKuH,IAAMvH,KAAKuC,MACnC,IAAIsF,EAAQqC,EAAYlK,KAAKyG,MAAOlE,EAC/C,II1OE0L,EAAK,sPACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAM1I,GAClB,GAAIA,EAAI9I,OAAS,IACb,KAAM,eAEV,MAAMyR,EAAM3I,EAAK4I,EAAI5I,EAAIL,QAAQ,KAAMuD,EAAIlD,EAAIL,QAAQ,MAC7C,GAANiJ,IAAiB,GAAN1F,IACXlD,EAAMA,EAAI/I,UAAU,EAAG2R,GAAK5I,EAAI/I,UAAU2R,EAAG1F,GAAG2F,QAAQ,KAAM,KAAO7I,EAAI/I,UAAUiM,EAAGlD,EAAI9I,SAE9F,IAAI4R,EAAIN,EAAGO,KAAK/I,GAAO,IAAKgB,EAAM,CAAC,EAAGvK,EAAI,GAC1C,KAAOA,KACHuK,EAAIyH,EAAMhS,IAAMqS,EAAErS,IAAM,GAU5B,OARU,GAANmS,IAAiB,GAAN1F,IACXlC,EAAIgI,OAASL,EACb3H,EAAIiI,KAAOjI,EAAIiI,KAAKhS,UAAU,EAAG+J,EAAIiI,KAAK/R,OAAS,GAAG2R,QAAQ,KAAM,KACpE7H,EAAIkI,UAAYlI,EAAIkI,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9E7H,EAAImI,SAAU,GAElBnI,EAAIoI,UAIR,SAAmBhU,EAAKoK,GACpB,MAAM6J,EAAO,WAAYC,EAAQ9J,EAAKqJ,QAAQQ,EAAM,KAAKrT,MAAM,KACvC,KAApBwJ,EAAKxF,MAAM,EAAG,IAA6B,IAAhBwF,EAAKtI,QAChCoS,EAAMnO,OAAO,EAAG,GAEE,KAAlBqE,EAAKxF,OAAO,IACZsP,EAAMnO,OAAOmO,EAAMpS,OAAS,EAAG,GAEnC,OAAOoS,CACX,CAboBF,CAAUpI,EAAKA,EAAU,MACzCA,EAAIuI,SAaR,SAAkBvI,EAAKhD,GACnB,MAAMrJ,EAAO,CAAC,EAMd,OALAqJ,EAAM6K,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA9U,EAAK8U,GAAMC,EAEnB,IACO/U,CACX,CArBmB4U,CAASvI,EAAKA,EAAW,OACjCA,CACX,CCrCA,MAAM2I,EAAiD,oBAArBvP,kBACC,oBAAxBY,oBACL4O,EAA0B,GAC5BD,GAGAvP,iBAAiB,WAAW,KACxBwP,EAAwBrV,SAASsV,GAAaA,KAAW,IAC1D,GAyBA,MAAMC,UAA6B7P,EAOtCwD,WAAAA,CAAYuD,EAAKlE,GAiBb,GAhBAe,QACAtD,KAAK1D,WX7BoB,cW8BzB0D,KAAKwP,YAAc,GACnBxP,KAAKyP,eAAiB,EACtBzP,KAAK0P,eAAiB,EACtB1P,KAAK2P,cAAgB,EACrB3P,KAAK4P,aAAe,EAKpB5P,KAAK6P,iBAAmBC,IACpBrJ,GAAO,kBAAoBA,IAC3BlE,EAAOkE,EACPA,EAAM,MAENA,EAAK,CACL,MAAMsJ,EAAY5B,EAAM1H,GACxBlE,EAAK4C,SAAW4K,EAAUrB,KAC1BnM,EAAK+C,OACsB,UAAvByK,EAAUzI,UAA+C,QAAvByI,EAAUzI,SAChD/E,EAAK8C,KAAO0K,EAAU1K,KAClB0K,EAAUtM,QACVlB,EAAKkB,MAAQsM,EAAUtM,MAC/B,MACSlB,EAAKmM,OACVnM,EAAK4C,SAAWgJ,EAAM5L,EAAKmM,MAAMA,MAErCpM,EAAsBtC,KAAMuC,GAC5BvC,KAAKsF,OACD,MAAQ/C,EAAK+C,OACP/C,EAAK+C,OACe,qBAAb8B,UAA4B,WAAaA,SAASE,SAC/D/E,EAAK4C,WAAa5C,EAAK8C,OAEvB9C,EAAK8C,KAAOrF,KAAKsF,OAAS,MAAQ,MAEtCtF,KAAKmF,SACD5C,EAAK4C,WACoB,qBAAbiC,SAA2BA,SAASjC,SAAW,aAC/DnF,KAAKqF,KACD9C,EAAK8C,OACoB,qBAAb+B,UAA4BA,SAAS/B,KACvC+B,SAAS/B,KACTrF,KAAKsF,OACD,MACA,MAClBtF,KAAK4L,WAAa,GAClB5L,KAAKgQ,kBAAoB,CAAC,EAC1BzN,EAAKqJ,WAAW5R,SAASiW,IACrB,MAAMC,EAAgBD,EAAE1V,UAAUsL,KAClC7F,KAAK4L,WAAW1L,KAAKgQ,GACrBlQ,KAAKgQ,kBAAkBE,GAAiBD,CAAC,IAE7CjQ,KAAKuC,KAAO3I,OAAOoU,OAAO,CACtB/I,KAAM,aACNkL,OAAO,EACPrH,iBAAiB,EACjBsH,SAAS,EACTzJ,eAAgB,IAChB0J,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfvE,iBAAkB,CAAC,EACnBwE,qBAAqB,GACtBnO,GACHvC,KAAKuC,KAAK0C,KACNjF,KAAKuC,KAAK0C,KAAKqJ,QAAQ,MAAO,KACzBtO,KAAKuC,KAAK+N,iBAAmB,IAAM,IACb,kBAApBtQ,KAAKuC,KAAKkB,QACjBzD,KAAKuC,KAAKkB,MRhGf,SAAgBkN,GACnB,IAAIC,EAAM,CAAC,EACPC,EAAQF,EAAGlV,MAAM,KACrB,IAAK,IAAIS,EAAI,EAAG4U,EAAID,EAAMlU,OAAQT,EAAI4U,EAAG5U,IAAK,CAC1C,IAAI6U,EAAOF,EAAM3U,GAAGT,MAAM,KAC1BmV,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC/D,CACA,OAAOH,CACX,CQwF8BrT,CAAOyC,KAAKuC,KAAKkB,QAEnC2L,IACIpP,KAAKuC,KAAKmO,sBAIV1Q,KAAKiR,2BAA6B,KAC1BjR,KAAKkR,YAELlR,KAAKkR,UAAU1Q,qBACfR,KAAKkR,UAAUlN,QACnB,EAEJnE,iBAAiB,eAAgBG,KAAKiR,4BAA4B,IAEhD,cAAlBjR,KAAKmF,WACLnF,KAAKmR,sBAAwB,KACzBnR,KAAKoR,SAAS,kBAAmB,CAC7BhO,YAAa,2BACf,EAENiM,EAAwBnP,KAAKF,KAAKmR,yBAGtCnR,KAAKuC,KAAKuG,kBACV9I,KAAKqR,gBAAaC,GAEtBtR,KAAKuR,OACT,CAQAC,eAAAA,CAAgB3L,GACZ,MAAMpC,EAAQ7J,OAAOoU,OAAO,CAAC,EAAGhO,KAAKuC,KAAKkB,OAE1CA,EAAMgO,IbPU,EaShBhO,EAAMyN,UAAYrL,EAEd7F,KAAK0R,KACLjO,EAAMmD,IAAM5G,KAAK0R,IACrB,MAAMnP,EAAO3I,OAAOoU,OAAO,CAAC,EAAGhO,KAAKuC,KAAM,CACtCkB,QACAC,OAAQ1D,KACRmF,SAAUnF,KAAKmF,SACfG,OAAQtF,KAAKsF,OACbD,KAAMrF,KAAKqF,MACZrF,KAAKuC,KAAK2J,iBAAiBrG,IAC9B,OAAO,IAAI7F,KAAKgQ,kBAAkBnK,GAAMtD,EAC5C,CAMAgP,KAAAA,GACI,GAA+B,IAA3BvR,KAAK4L,WAAWjP,OAKhB,YAHAqD,KAAKsB,cAAa,KACdtB,KAAKgB,aAAa,QAAS,0BAA0B,GACtD,GAGP,MAAMkP,EAAgBlQ,KAAKuC,KAAK8N,iBAC5Bd,EAAqBoC,wBACqB,IAA1C3R,KAAK4L,WAAWxG,QAAQ,aACtB,YACApF,KAAK4L,WAAW,GACtB5L,KAAK8D,WAAa,UAClB,MAAMoN,EAAYlR,KAAKwR,gBAAgBtB,GACvCgB,EAAUrN,OACV7D,KAAK4R,aAAaV,EACtB,CAMAU,YAAAA,CAAaV,GACLlR,KAAKkR,WACLlR,KAAKkR,UAAU1Q,qBAGnBR,KAAKkR,UAAYA,EAEjBA,EACKtR,GAAG,QAASI,KAAK6R,SAASpP,KAAKzC,OAC/BJ,GAAG,SAAUI,KAAK8R,UAAUrP,KAAKzC,OACjCJ,GAAG,QAASI,KAAKsJ,SAAS7G,KAAKzC,OAC/BJ,GAAG,SAAUuD,GAAWnD,KAAKoR,SAAS,kBAAmBjO,IAClE,CAMAmB,MAAAA,GACItE,KAAK8D,WAAa,OAClByL,EAAqBoC,sBACjB,cAAgB3R,KAAKkR,UAAUrL,KACnC7F,KAAKgB,aAAa,QAClBhB,KAAK+R,OACT,CAMAD,SAAAA,CAAUhU,GACN,GAAI,YAAckC,KAAK8D,YACnB,SAAW9D,KAAK8D,YAChB,YAAc9D,KAAK8D,WAInB,OAHA9D,KAAKgB,aAAa,SAAUlD,GAE5BkC,KAAKgB,aAAa,aACVlD,EAAO3D,MACX,IAAK,OACD6F,KAAKgS,YAAYC,KAAK9D,MAAMrQ,EAAO1D,OACnC,MACJ,IAAK,OACD4F,KAAKkS,YAAY,QACjBlS,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClBhB,KAAKmS,oBACL,MACJ,IAAK,QACD,MAAMnL,EAAM,IAAI/D,MAAM,gBAEtB+D,EAAIoL,KAAOtU,EAAO1D,KAClB4F,KAAKsJ,SAAStC,GACd,MACJ,IAAK,UACDhH,KAAKgB,aAAa,OAAQlD,EAAO1D,MACjC4F,KAAKgB,aAAa,UAAWlD,EAAO1D,MAMpD,CAOA4X,WAAAA,CAAY5X,GACR4F,KAAKgB,aAAa,YAAa5G,GAC/B4F,KAAK0R,GAAKtX,EAAKwM,IACf5G,KAAKkR,UAAUzN,MAAMmD,IAAMxM,EAAKwM,IAChC5G,KAAK0P,cAAgBtV,EAAKiY,aAC1BrS,KAAK2P,aAAevV,EAAKkY,YACzBtS,KAAK4P,YAAcxV,EAAKqS,WACxBzM,KAAKsE,SAED,WAAatE,KAAK8D,YAEtB9D,KAAKmS,mBACT,CAMAA,iBAAAA,GACInS,KAAK0C,eAAe1C,KAAKuS,mBACzB,MAAMC,EAAQxS,KAAK0P,cAAgB1P,KAAK2P,aACxC3P,KAAK6P,iBAAmBjN,KAAKC,MAAQ2P,EACrCxS,KAAKuS,kBAAoBvS,KAAKsB,cAAa,KACvCtB,KAAKoR,SAAS,eAAe,GAC9BoB,GACCxS,KAAKuC,KAAKyI,WACVhL,KAAKuS,kBAAkBrH,OAE/B,CAMA2G,QAAAA,GACI7R,KAAKwP,YAAY5O,OAAO,EAAGZ,KAAKyP,gBAIhCzP,KAAKyP,eAAiB,EAClB,IAAMzP,KAAKwP,YAAY7S,OACvBqD,KAAKgB,aAAa,SAGlBhB,KAAK+R,OAEb,CAMAA,KAAAA,GACI,GAAI,WAAa/R,KAAK8D,YAClB9D,KAAKkR,UAAU1N,WACdxD,KAAKyS,WACNzS,KAAKwP,YAAY7S,OAAQ,CACzB,MAAMyH,EAAUpE,KAAK0S,sBACrB1S,KAAKkR,UAAU/M,KAAKC,GAGpBpE,KAAKyP,eAAiBrL,EAAQzH,OAC9BqD,KAAKgB,aAAa,QACtB,CACJ,CAOA0R,mBAAAA,GAII,KAH+B1S,KAAK4P,aACR,YAAxB5P,KAAKkR,UAAUrL,MACf7F,KAAKwP,YAAY7S,OAAS,GAE1B,OAAOqD,KAAKwP,YAEhB,IAAImD,EAAc,EAClB,IAAK,IAAIzW,EAAI,EAAGA,EAAI8D,KAAKwP,YAAY7S,OAAQT,IAAK,CAC9C,MAAM9B,EAAO4F,KAAKwP,YAAYtT,GAAG9B,KAIjC,GAHIA,IACAuY,GVxUO,kBADI9X,EUyUeT,GVlU1C,SAAoBqL,GAChB,IAAImN,EAAI,EAAGjW,EAAS,EACpB,IAAK,IAAIT,EAAI,EAAG4U,EAAIrL,EAAI9I,OAAQT,EAAI4U,EAAG5U,IACnC0W,EAAInN,EAAItJ,WAAWD,GACf0W,EAAI,IACJjW,GAAU,EAELiW,EAAI,KACTjW,GAAU,EAELiW,EAAI,OAAUA,GAAK,MACxBjW,GAAU,GAGVT,IACAS,GAAU,GAGlB,OAAOA,CACX,CAxBekW,CAAWhY,GAGfiI,KAAKgQ,KAPQ,MAOFjY,EAAIiB,YAAcjB,EAAIyE,QUsU5BpD,EAAI,GAAKyW,EAAc3S,KAAK4P,YAC5B,OAAO5P,KAAKwP,YAAY/P,MAAM,EAAGvD,GAErCyW,GAAe,CACnB,CV/UD,IAAoB9X,EUgVnB,OAAOmF,KAAKwP,WAChB,CAUcuD,eAAAA,GACV,IAAK/S,KAAK6P,iBACN,OAAO,EACX,MAAMmD,EAAapQ,KAAKC,MAAQ7C,KAAK6P,iBAOrC,OANImD,IACAhT,KAAK6P,iBAAmB,EACxB1O,GAAS,KACLnB,KAAKoR,SAAS,eAAe,GAC9BpR,KAAKsB,eAEL0R,CACX,CASA3O,KAAAA,CAAM4O,EAAKC,EAASnT,GAEhB,OADAC,KAAKkS,YAAY,UAAWe,EAAKC,EAASnT,GACnCC,IACX,CASAmE,IAAAA,CAAK8O,EAAKC,EAASnT,GAEf,OADAC,KAAKkS,YAAY,UAAWe,EAAKC,EAASnT,GACnCC,IACX,CAUAkS,WAAAA,CAAY/X,EAAMC,EAAM8Y,EAASnT,GAS7B,GARI,oBAAsB3F,IACtB2F,EAAK3F,EACLA,OAAO0K,GAEP,oBAAsBoO,IACtBnT,EAAKmT,EACLA,EAAU,MAEV,YAAclT,KAAK8D,YAAc,WAAa9D,KAAK8D,WACnD,QAEJoP,EAAUA,GAAW,CAAC,GACdC,UAAW,IAAUD,EAAQC,SACrC,MAAMrV,EAAS,CACX3D,KAAMA,EACNC,KAAMA,EACN8Y,QAASA,GAEblT,KAAKgB,aAAa,eAAgBlD,GAClCkC,KAAKwP,YAAYtP,KAAKpC,GAClBiC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAK+R,OACT,CAIA/N,KAAAA,GACI,MAAMA,EAAQA,KACVhE,KAAKoR,SAAS,gBACdpR,KAAKkR,UAAUlN,OAAO,EAEpBoP,EAAkBA,KACpBpT,KAAKI,IAAI,UAAWgT,GACpBpT,KAAKI,IAAI,eAAgBgT,GACzBpP,GAAO,EAELqP,EAAiBA,KAEnBrT,KAAKG,KAAK,UAAWiT,GACrBpT,KAAKG,KAAK,eAAgBiT,EAAgB,EAqB9C,MAnBI,YAAcpT,KAAK8D,YAAc,SAAW9D,KAAK8D,aACjD9D,KAAK8D,WAAa,UACd9D,KAAKwP,YAAY7S,OACjBqD,KAAKG,KAAK,SAAS,KACXH,KAAKyS,UACLY,IAGArP,GACJ,IAGChE,KAAKyS,UACVY,IAGArP,KAGDhE,IACX,CAMAsJ,QAAAA,CAAStC,GAEL,GADAuI,EAAqBoC,uBAAwB,EACzC3R,KAAKuC,KAAK+Q,kBACVtT,KAAK4L,WAAWjP,OAAS,GACL,YAApBqD,KAAK8D,WAEL,OADA9D,KAAK4L,WAAWrM,QACTS,KAAKuR,QAEhBvR,KAAKgB,aAAa,QAASgG,GAC3BhH,KAAKoR,SAAS,kBAAmBpK,EACrC,CAMAoK,QAAAA,CAASjO,EAAQC,GACb,GAAI,YAAcpD,KAAK8D,YACnB,SAAW9D,KAAK8D,YAChB,YAAc9D,KAAK8D,WAAY,CAS/B,GAPA9D,KAAK0C,eAAe1C,KAAKuS,mBAEzBvS,KAAKkR,UAAU1Q,mBAAmB,SAElCR,KAAKkR,UAAUlN,QAEfhE,KAAKkR,UAAU1Q,qBACX4O,IACIpP,KAAKiR,4BACLxQ,oBAAoB,eAAgBT,KAAKiR,4BAA4B,GAErEjR,KAAKmR,uBAAuB,CAC5B,MAAMjV,EAAImT,EAAwBjK,QAAQpF,KAAKmR,wBACpC,IAAPjV,GACAmT,EAAwBzO,OAAO1E,EAAG,EAE1C,CAGJ8D,KAAK8D,WAAa,SAElB9D,KAAK0R,GAAK,KAEV1R,KAAKgB,aAAa,QAASmC,EAAQC,GAGnCpD,KAAKwP,YAAc,GACnBxP,KAAKyP,eAAiB,CAC1B,CACJ,EAEJF,EAAqBjI,SbhYG,EawZjB,MAAMiM,WAA0BhE,EACnCrM,WAAAA,GACII,SAAShD,WACTN,KAAKwT,UAAY,EACrB,CACAlP,MAAAA,GAEI,GADAhB,MAAMgB,SACF,SAAWtE,KAAK8D,YAAc9D,KAAKuC,KAAK6N,QACxC,IAAK,IAAIlU,EAAI,EAAGA,EAAI8D,KAAKwT,UAAU7W,OAAQT,IACvC8D,KAAKyT,OAAOzT,KAAKwT,UAAUtX,GAGvC,CAOAuX,MAAAA,CAAO5N,GACH,IAAIqL,EAAYlR,KAAKwR,gBAAgB3L,GACjC6N,GAAS,EACbnE,EAAqBoC,uBAAwB,EAC7C,MAAMgC,EAAkBA,KAChBD,IAEJxC,EAAU/M,KAAK,CAAC,CAAEhK,KAAM,OAAQC,KAAM,WACtC8W,EAAU/Q,KAAK,UAAW8S,IACtB,IAAIS,EAEJ,GAAI,SAAWT,EAAI9Y,MAAQ,UAAY8Y,EAAI7Y,KAAM,CAG7C,GAFA4F,KAAKyS,WAAY,EACjBzS,KAAKgB,aAAa,YAAakQ,IAC1BA,EACD,OACJ3B,EAAqBoC,sBACjB,cAAgBT,EAAUrL,KAC9B7F,KAAKkR,UAAUxM,OAAM,KACbgP,GAEA,WAAa1T,KAAK8D,aAEtB8P,IACA5T,KAAK4R,aAAaV,GAClBA,EAAU/M,KAAK,CAAC,CAAEhK,KAAM,aACxB6F,KAAKgB,aAAa,UAAWkQ,GAC7BA,EAAY,KACZlR,KAAKyS,WAAY,EACjBzS,KAAK+R,QAAO,GAEpB,KACK,CACD,MAAM/K,EAAM,IAAI/D,MAAM,eAEtB+D,EAAIkK,UAAYA,EAAUrL,KAC1B7F,KAAKgB,aAAa,eAAgBgG,EACtC,KACF,EAEN,SAAS6M,IACDH,IAGJA,GAAS,EACTE,IACA1C,EAAUlN,QACVkN,EAAY,KAChB,CAEA,MAAM3F,EAAWvE,IACb,MAAM8M,EAAQ,IAAI7Q,MAAM,gBAAkB+D,GAE1C8M,EAAM5C,UAAYA,EAAUrL,KAC5BgO,IACA7T,KAAKgB,aAAa,eAAgB8S,EAAM,EAE5C,SAASC,IACLxI,EAAQ,mBACZ,CAEA,SAASJ,IACLI,EAAQ,gBACZ,CAEA,SAASyI,EAAUC,GACX/C,GAAa+C,EAAGpO,OAASqL,EAAUrL,MACnCgO,GAER,CAEA,MAAMD,EAAUA,KACZ1C,EAAU3Q,eAAe,OAAQoT,GACjCzC,EAAU3Q,eAAe,QAASgL,GAClC2F,EAAU3Q,eAAe,QAASwT,GAClC/T,KAAKI,IAAI,QAAS+K,GAClBnL,KAAKI,IAAI,YAAa4T,EAAU,EAEpC9C,EAAU/Q,KAAK,OAAQwT,GACvBzC,EAAU/Q,KAAK,QAASoL,GACxB2F,EAAU/Q,KAAK,QAAS4T,GACxB/T,KAAKG,KAAK,QAASgL,GACnBnL,KAAKG,KAAK,YAAa6T,IACyB,IAA5ChU,KAAKwT,UAAUpO,QAAQ,iBACd,iBAATS,EAEA7F,KAAKsB,cAAa,KACToS,GACDxC,EAAUrN,MACd,GACD,KAGHqN,EAAUrN,MAElB,CACAmO,WAAAA,CAAY5X,GACR4F,KAAKwT,UAAYxT,KAAKkU,gBAAgB9Z,EAAK+Z,UAC3C7Q,MAAM0O,YAAY5X,EACtB,CAOA8Z,eAAAA,CAAgBC,GACZ,MAAMC,EAAmB,GACzB,IAAK,IAAIlY,EAAI,EAAGA,EAAIiY,EAASxX,OAAQT,KAC5B8D,KAAK4L,WAAWxG,QAAQ+O,EAASjY,KAClCkY,EAAiBlU,KAAKiU,EAASjY,IAEvC,OAAOkY,CACX,EAqBG,MAAMC,WAAed,GACxBrQ,WAAAA,CAAYuD,GACR,MAAM6N,EAAmB,kBAAR7N,EAAmBA,EADnBnG,UAAA3D,OAAA,QAAAmI,IAAAxE,UAAA,GAAAA,UAAA,GAAG,CAAC,IAEhBgU,EAAE1I,YACF0I,EAAE1I,YAAyC,kBAApB0I,EAAE1I,WAAW,MACrC0I,EAAE1I,YAAc0I,EAAE1I,YAAc,CAAC,UAAW,YAAa,iBACpD2I,KAAKrE,GAAkBsE,EAAmBtE,KAC1CuE,QAAQxE,KAAQA,KAEzB3M,MAAMmD,EAAK6N,EACf,ECrtBJ,MAAM5Z,GAA+C,oBAAhBC,YAM/BH,GAAWZ,OAAOW,UAAUC,SAC5BH,GAAiC,oBAATC,MACT,qBAATA,MACoB,6BAAxBE,GAASC,KAAKH,MAChBoa,GAAiC,oBAATC,MACT,qBAATA,MACoB,6BAAxBna,GAASC,KAAKka,MAMf,SAAS9H,GAAShS,GACrB,OAASH,KAA0BG,aAAeF,aAlBtCE,IACyB,oBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,EAAIC,kBAAkBH,YAeqCC,CAAOC,KACnER,IAAkBQ,aAAeP,MACjCoa,IAAkB7Z,aAAe8Z,IAC1C,CACO,SAASC,GAAU/Z,EAAKga,GAC3B,IAAKha,GAAsB,kBAARA,EACf,OAAO,EAEX,GAAIkG,MAAM+T,QAAQja,GAAM,CACpB,IAAK,IAAIqB,EAAI,EAAG4U,EAAIjW,EAAI8B,OAAQT,EAAI4U,EAAG5U,IACnC,GAAI0Y,GAAU/Z,EAAIqB,IACd,OAAO,EAGf,OAAO,CACX,CACA,GAAI2Q,GAAShS,GACT,OAAO,EAEX,GAAIA,EAAIga,QACkB,oBAAfha,EAAIga,QACU,IAArBvU,UAAU3D,OACV,OAAOiY,GAAU/Z,EAAIga,UAAU,GAEnC,IAAK,MAAM5a,KAAOY,EACd,GAAIjB,OAAOW,UAAUyH,eAAevH,KAAKI,EAAKZ,IAAQ2a,GAAU/Z,EAAIZ,IAChE,OAAO,EAGf,OAAO,CACX,CCzCO,SAAS8a,GAAkBjX,GAC9B,MAAMkX,EAAU,GACVC,EAAanX,EAAO1D,KACpB8a,EAAOpX,EAGb,OAFAoX,EAAK9a,KAAO+a,GAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQrY,OACpB,CAAEmB,OAAQoX,EAAMF,QAASA,EACpC,CACA,SAASG,GAAmB/a,EAAM4a,GAC9B,IAAK5a,EACD,OAAOA,EACX,GAAIyS,GAASzS,GAAO,CAChB,MAAMib,EAAc,CAAEC,cAAc,EAAMC,IAAKP,EAAQrY,QAEvD,OADAqY,EAAQ9U,KAAK9F,GACNib,CACX,CACK,GAAItU,MAAM+T,QAAQ1a,GAAO,CAC1B,MAAMob,EAAU,IAAIzU,MAAM3G,EAAKuC,QAC/B,IAAK,IAAIT,EAAI,EAAGA,EAAI9B,EAAKuC,OAAQT,IAC7BsZ,EAAQtZ,GAAKiZ,GAAmB/a,EAAK8B,GAAI8Y,GAE7C,OAAOQ,CACX,CACK,GAAoB,kBAATpb,KAAuBA,aAAgBwI,MAAO,CAC1D,MAAM4S,EAAU,CAAC,EACjB,IAAK,MAAMvb,KAAOG,EACVR,OAAOW,UAAUyH,eAAevH,KAAKL,EAAMH,KAC3Cub,EAAQvb,GAAOkb,GAAmB/a,EAAKH,GAAM+a,IAGrD,OAAOQ,CACX,CACA,OAAOpb,CACX,CASO,SAASqb,GAAkB3X,EAAQkX,GAGtC,OAFAlX,EAAO1D,KAAOsb,GAAmB5X,EAAO1D,KAAM4a,UACvClX,EAAOsX,YACPtX,CACX,CACA,SAAS4X,GAAmBtb,EAAM4a,GAC9B,IAAK5a,EACD,OAAOA,EACX,GAAIA,IAA8B,IAAtBA,EAAKkb,aAAuB,CAIpC,GAHyC,kBAAblb,EAAKmb,KAC7Bnb,EAAKmb,KAAO,GACZnb,EAAKmb,IAAMP,EAAQrY,OAEnB,OAAOqY,EAAQ5a,EAAKmb,KAGpB,MAAM,IAAItS,MAAM,sBAExB,CACK,GAAIlC,MAAM+T,QAAQ1a,GACnB,IAAK,IAAI8B,EAAI,EAAGA,EAAI9B,EAAKuC,OAAQT,IAC7B9B,EAAK8B,GAAKwZ,GAAmBtb,EAAK8B,GAAI8Y,QAGzC,GAAoB,kBAAT5a,EACZ,IAAK,MAAMH,KAAOG,EACVR,OAAOW,UAAUyH,eAAevH,KAAKL,EAAMH,KAC3CG,EAAKH,GAAOyb,GAAmBtb,EAAKH,GAAM+a,IAItD,OAAO5a,CACX,CC5EA,MAAMub,GAAkB,CACpB,UACA,gBACA,aACA,gBACA,cACA,kBAOSrO,GAAW,EACjB,IAAIsO,IACX,SAAWA,GACPA,EAAWA,EAAoB,QAAI,GAAK,UACxCA,EAAWA,EAAuB,WAAI,GAAK,aAC3CA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAgB,IAAI,GAAK,MACpCA,EAAWA,EAA0B,cAAI,GAAK,gBAC9CA,EAAWA,EAAyB,aAAI,GAAK,eAC7CA,EAAWA,EAAuB,WAAI,GAAK,YAC9C,CARD,CAQGA,KAAeA,GAAa,CAAC,IAIzB,MAAMC,GAMT3S,WAAAA,CAAY4S,GACR9V,KAAK8V,SAAWA,CACpB,CAOA1X,MAAAA,CAAOvD,GACH,OAAIA,EAAIV,OAASyb,GAAWG,OAASlb,EAAIV,OAASyb,GAAWI,MACrDpB,GAAU/Z,GAWX,CAACmF,KAAKiW,eAAepb,IAVbmF,KAAKkW,eAAe,CACvB/b,KAAMU,EAAIV,OAASyb,GAAWG,MACxBH,GAAWO,aACXP,GAAWQ,WACjBC,IAAKxb,EAAIwb,IACTjc,KAAMS,EAAIT,KACVsX,GAAI7W,EAAI6W,IAKxB,CAIAuE,cAAAA,CAAepb,GAEX,IAAI4K,EAAM,GAAK5K,EAAIV,KAmBnB,OAjBIU,EAAIV,OAASyb,GAAWO,cACxBtb,EAAIV,OAASyb,GAAWQ,aACxB3Q,GAAO5K,EAAIua,YAAc,KAIzBva,EAAIwb,KAAO,MAAQxb,EAAIwb,MACvB5Q,GAAO5K,EAAIwb,IAAM,KAGjB,MAAQxb,EAAI6W,KACZjM,GAAO5K,EAAI6W,IAGX,MAAQ7W,EAAIT,OACZqL,GAAOwM,KAAKqE,UAAUzb,EAAIT,KAAM4F,KAAK8V,WAElCrQ,CACX,CAMAyQ,cAAAA,CAAerb,GACX,MAAM0b,EAAiBxB,GAAkBla,GACnCqa,EAAOlV,KAAKiW,eAAeM,EAAezY,QAC1CkX,EAAUuB,EAAevB,QAE/B,OADAA,EAAQwB,QAAQtB,GACTF,CACX,EAGJ,SAASyB,GAAS3P,GACd,MAAiD,oBAA1ClN,OAAOW,UAAUC,SAASC,KAAKqM,EAC1C,CAMO,MAAM4P,WAAgBhX,EAMzBwD,WAAAA,CAAYyT,GACRrT,QACAtD,KAAK2W,QAAUA,CACnB,CAMAC,GAAAA,CAAI/b,GACA,IAAIiD,EACJ,GAAmB,kBAARjD,EAAkB,CACzB,GAAImF,KAAK6W,cACL,MAAM,IAAI5T,MAAM,mDAEpBnF,EAASkC,KAAK8W,aAAajc,GAC3B,MAAMkc,EAAgBjZ,EAAO3D,OAASyb,GAAWO,aAC7CY,GAAiBjZ,EAAO3D,OAASyb,GAAWQ,YAC5CtY,EAAO3D,KAAO4c,EAAgBnB,GAAWG,MAAQH,GAAWI,IAE5DhW,KAAK6W,cAAgB,IAAIG,GAAoBlZ,GAElB,IAAvBA,EAAOsX,aACP9R,MAAMtC,aAAa,UAAWlD,IAKlCwF,MAAMtC,aAAa,UAAWlD,EAEtC,KACK,KAAI+O,GAAShS,KAAQA,EAAIgC,OAe1B,MAAM,IAAIoG,MAAM,iBAAmBpI,GAbnC,IAAKmF,KAAK6W,cACN,MAAM,IAAI5T,MAAM,oDAGhBnF,EAASkC,KAAK6W,cAAcI,eAAepc,GACvCiD,IAEAkC,KAAK6W,cAAgB,KACrBvT,MAAMtC,aAAa,UAAWlD,GAM1C,CACJ,CAOAgZ,YAAAA,CAAarR,GACT,IAAIvJ,EAAI,EAER,MAAMkB,EAAI,CACNjD,KAAMoL,OAAOE,EAAIjJ,OAAO,KAE5B,QAA2BsI,IAAvB8Q,GAAWxY,EAAEjD,MACb,MAAM,IAAI8I,MAAM,uBAAyB7F,EAAEjD,MAG/C,GAAIiD,EAAEjD,OAASyb,GAAWO,cACtB/Y,EAAEjD,OAASyb,GAAWQ,WAAY,CAClC,MAAMc,EAAQhb,EAAI,EAClB,KAA2B,MAApBuJ,EAAIjJ,SAASN,IAAcA,GAAKuJ,EAAI9I,SAC3C,MAAMwa,EAAM1R,EAAI/I,UAAUwa,EAAOhb,GACjC,GAAIib,GAAO5R,OAAO4R,IAA0B,MAAlB1R,EAAIjJ,OAAON,GACjC,MAAM,IAAI+G,MAAM,uBAEpB7F,EAAEgY,YAAc7P,OAAO4R,EAC3B,CAEA,GAAI,MAAQ1R,EAAIjJ,OAAON,EAAI,GAAI,CAC3B,MAAMgb,EAAQhb,EAAI,EAClB,OAASA,GAAG,CAER,GAAI,MADMuJ,EAAIjJ,OAAON,GAEjB,MACJ,GAAIA,IAAMuJ,EAAI9I,OACV,KACR,CACAS,EAAEiZ,IAAM5Q,EAAI/I,UAAUwa,EAAOhb,EACjC,MAEIkB,EAAEiZ,IAAM,IAGZ,MAAMe,EAAO3R,EAAIjJ,OAAON,EAAI,GAC5B,GAAI,KAAOkb,GAAQ7R,OAAO6R,IAASA,EAAM,CACrC,MAAMF,EAAQhb,EAAI,EAClB,OAASA,GAAG,CACR,MAAM0W,EAAInN,EAAIjJ,OAAON,GACrB,GAAI,MAAQ0W,GAAKrN,OAAOqN,IAAMA,EAAG,GAC3B1W,EACF,KACJ,CACA,GAAIA,IAAMuJ,EAAI9I,OACV,KACR,CACAS,EAAEsU,GAAKnM,OAAOE,EAAI/I,UAAUwa,EAAOhb,EAAI,GAC3C,CAEA,GAAIuJ,EAAIjJ,SAASN,GAAI,CACjB,MAAMmb,EAAUrX,KAAKsX,SAAS7R,EAAI8R,OAAOrb,IACzC,IAAIwa,GAAQc,eAAepa,EAAEjD,KAAMkd,GAI/B,MAAM,IAAIpU,MAAM,mBAHhB7F,EAAEhD,KAAOid,CAKjB,CACA,OAAOja,CACX,CACAka,QAAAA,CAAS7R,GACL,IACI,OAAOwM,KAAK9D,MAAM1I,EAAKzF,KAAK2W,QAChC,CACA,MAAOhO,GACH,OAAO,CACX,CACJ,CACA,qBAAO6O,CAAerd,EAAMkd,GACxB,OAAQld,GACJ,KAAKyb,GAAW6B,QACZ,OAAOhB,GAASY,GACpB,KAAKzB,GAAW8B,WACZ,YAAmB5S,IAAZuS,EACX,KAAKzB,GAAW+B,cACZ,MAA0B,kBAAZN,GAAwBZ,GAASY,GACnD,KAAKzB,GAAWG,MAChB,KAAKH,GAAWO,aACZ,OAAQpV,MAAM+T,QAAQuC,KACK,kBAAfA,EAAQ,IACW,kBAAfA,EAAQ,KAC6B,IAAzC1B,GAAgBvQ,QAAQiS,EAAQ,KAChD,KAAKzB,GAAWI,IAChB,KAAKJ,GAAWQ,WACZ,OAAOrV,MAAM+T,QAAQuC,GAEjC,CAIAO,OAAAA,GACQ5X,KAAK6W,gBACL7W,KAAK6W,cAAcgB,yBACnB7X,KAAK6W,cAAgB,KAE7B,EAUJ,MAAMG,GACF9T,WAAAA,CAAYpF,GACRkC,KAAKlC,OAASA,EACdkC,KAAKgV,QAAU,GACfhV,KAAK8X,UAAYha,CACrB,CASAmZ,cAAAA,CAAec,GAEX,GADA/X,KAAKgV,QAAQ9U,KAAK6X,GACd/X,KAAKgV,QAAQrY,SAAWqD,KAAK8X,UAAU1C,YAAa,CAEpD,MAAMtX,EAAS2X,GAAkBzV,KAAK8X,UAAW9X,KAAKgV,SAEtD,OADAhV,KAAK6X,yBACE/Z,CACX,CACA,OAAO,IACX,CAIA+Z,sBAAAA,GACI7X,KAAK8X,UAAY,KACjB9X,KAAKgV,QAAU,EACnB,ECrTG,SAASpV,GAAG/E,EAAKyQ,EAAIvL,GAExB,OADAlF,EAAI+E,GAAG0L,EAAIvL,GACJ,WACHlF,EAAIuF,IAAIkL,EAAIvL,EAChB,CACJ,CCEA,MAAM4V,GAAkB/b,OAAOoe,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb9X,eAAgB,IA0Bb,MAAM8T,WAAe3U,EAIxBwD,WAAAA,CAAYoV,EAAIjC,EAAK9T,GACjBe,QAeAtD,KAAKuY,WAAY,EAKjBvY,KAAKwY,WAAY,EAIjBxY,KAAKyY,cAAgB,GAIrBzY,KAAK0Y,WAAa,GAOlB1Y,KAAK2Y,OAAS,GAKd3Y,KAAK4Y,UAAY,EACjB5Y,KAAK6Y,IAAM,EAwBX7Y,KAAK8Y,KAAO,CAAC,EACb9Y,KAAK+Y,MAAQ,CAAC,EACd/Y,KAAKsY,GAAKA,EACVtY,KAAKqW,IAAMA,EACP9T,GAAQA,EAAKyW,OACbhZ,KAAKgZ,KAAOzW,EAAKyW,MAErBhZ,KAAK+H,MAAQnO,OAAOoU,OAAO,CAAC,EAAGzL,GAC3BvC,KAAKsY,GAAGW,cACRjZ,KAAK6D,MACb,CAeA,gBAAIqV,GACA,OAAQlZ,KAAKuY,SACjB,CAMAY,SAAAA,GACI,GAAInZ,KAAKoZ,KACL,OACJ,MAAMd,EAAKtY,KAAKsY,GAChBtY,KAAKoZ,KAAO,CACRxZ,GAAG0Y,EAAI,OAAQtY,KAAK+K,OAAOtI,KAAKzC,OAChCJ,GAAG0Y,EAAI,SAAUtY,KAAKqZ,SAAS5W,KAAKzC,OACpCJ,GAAG0Y,EAAI,QAAStY,KAAKuL,QAAQ9I,KAAKzC,OAClCJ,GAAG0Y,EAAI,QAAStY,KAAKmL,QAAQ1I,KAAKzC,OAE1C,CAkBA,UAAIsZ,GACA,QAAStZ,KAAKoZ,IAClB,CAWAnB,OAAAA,GACI,OAAIjY,KAAKuY,YAETvY,KAAKmZ,YACAnZ,KAAKsY,GAAkB,eACxBtY,KAAKsY,GAAGzU,OACR,SAAW7D,KAAKsY,GAAGiB,aACnBvZ,KAAK+K,UALE/K,IAOf,CAIA6D,IAAAA,GACI,OAAO7D,KAAKiY,SAChB,CAgBA9T,IAAAA,GAAc,QAAAvC,EAAAtB,UAAA3D,OAANmE,EAAI,IAAAC,MAAAa,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJhB,EAAIgB,GAAAxB,UAAAwB,GAGR,OAFAhB,EAAK0V,QAAQ,WACbxW,KAAKa,KAAKR,MAAML,KAAMc,GACfd,IACX,CAkBAa,IAAAA,CAAKyK,GACD,IAAIlD,EAAIoR,EAAIC,EACZ,GAAI9D,GAAgB3T,eAAesJ,GAC/B,MAAM,IAAIrI,MAAM,IAAMqI,EAAG9Q,WAAa,8BACzC,QAAAkf,EAAApZ,UAAA3D,OAJOmE,EAAI,IAAAC,MAAA2Y,EAAA,EAAAA,EAAA,KAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ7Y,EAAI6Y,EAAA,GAAArZ,UAAAqZ,GAMZ,GADA7Y,EAAK0V,QAAQlL,GACTtL,KAAK+H,MAAM6R,UAAY5Z,KAAK+Y,MAAMc,YAAc7Z,KAAK+Y,MAAMe,SAE3D,OADA9Z,KAAK+Z,YAAYjZ,GACVd,KAEX,MAAMlC,EAAS,CACX3D,KAAMyb,GAAWG,MACjB3b,KAAM0G,EAEVhD,QAAiB,CAAC,GAGlB,GAFAA,EAAOoV,QAAQC,UAAmC,IAAxBnT,KAAK+Y,MAAM5F,SAEjC,oBAAsBrS,EAAKA,EAAKnE,OAAS,GAAI,CAC7C,MAAM+U,EAAK1R,KAAK6Y,MACVmB,EAAMlZ,EAAKmZ,MACjBja,KAAKka,qBAAqBxI,EAAIsI,GAC9Blc,EAAO4T,GAAKA,CAChB,CACA,MAAMyI,EAAyG,QAAlFX,EAA+B,QAAzBpR,EAAKpI,KAAKsY,GAAG8B,cAA2B,IAAPhS,OAAgB,EAASA,EAAG8I,iBAA8B,IAAPsI,OAAgB,EAASA,EAAGhW,SAC7I6W,EAAcra,KAAKuY,aAAyC,QAAzBkB,EAAKzZ,KAAKsY,GAAG8B,cAA2B,IAAPX,OAAgB,EAASA,EAAG1G,mBAYtG,OAXsB/S,KAAK+Y,MAAMe,WAAaK,IAGrCE,GACLra,KAAKsa,wBAAwBxc,GAC7BkC,KAAKlC,OAAOA,IAGZkC,KAAK0Y,WAAWxY,KAAKpC,IAEzBkC,KAAK+Y,MAAQ,CAAC,EACP/Y,IACX,CAIAka,oBAAAA,CAAqBxI,EAAIsI,GAAK,IACtB5R,EADsBmS,EAAA,KAE1B,MAAMvR,EAAwC,QAA7BZ,EAAKpI,KAAK+Y,MAAM/P,eAA4B,IAAPZ,EAAgBA,EAAKpI,KAAK+H,MAAMyS,WACtF,QAAgB1V,IAAZkE,EAEA,YADAhJ,KAAK8Y,KAAKpH,GAAMsI,GAIpB,MAAMS,EAAQza,KAAKsY,GAAGhX,cAAa,YACxBtB,KAAK8Y,KAAKpH,GACjB,IAAK,IAAIxV,EAAI,EAAGA,EAAI8D,KAAK0Y,WAAW/b,OAAQT,IACpC8D,KAAK0Y,WAAWxc,GAAGwV,KAAOA,GAC1B1R,KAAK0Y,WAAW9X,OAAO1E,EAAG,GAGlC8d,EAAIvf,KAAKuF,KAAM,IAAIiD,MAAM,2BAA2B,GACrD+F,GACGjJ,EAAK,WAEPwa,EAAKjC,GAAG5V,eAAe+X,GAAO,QAAAC,EAAApa,UAAA3D,OAFnBmE,EAAI,IAAAC,MAAA2Z,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ7Z,EAAI6Z,GAAAra,UAAAqa,GAGfX,EAAI3Z,MAAMka,EAAMzZ,EACpB,EACAf,EAAG6a,WAAY,EACf5a,KAAK8Y,KAAKpH,GAAM3R,CACpB,CAiBA8a,WAAAA,CAAYvP,GAAa,QAAAwP,EAAAxa,UAAA3D,OAANmE,EAAI,IAAAC,MAAA+Z,EAAA,EAAAA,EAAA,KAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJja,EAAIia,EAAA,GAAAza,UAAAya,GACnB,OAAO,IAAI3Z,SAAQ,CAACC,EAAS2Z,KACzB,MAAMjb,EAAKA,CAACkb,EAAMC,IACPD,EAAOD,EAAOC,GAAQ5Z,EAAQ6Z,GAEzCnb,EAAG6a,WAAY,EACf9Z,EAAKZ,KAAKH,GACVC,KAAKa,KAAKyK,KAAOxK,EAAK,GAE9B,CAMAiZ,WAAAA,CAAYjZ,GAAM,IAAAqa,EAAA,KACd,IAAInB,EACiC,oBAA1BlZ,EAAKA,EAAKnE,OAAS,KAC1Bqd,EAAMlZ,EAAKmZ,OAEf,MAAMnc,EAAS,CACX4T,GAAI1R,KAAK4Y,YACTwC,SAAU,EACVC,SAAS,EACTva,OACAiY,MAAOnf,OAAOoU,OAAO,CAAE6L,WAAW,GAAQ7Z,KAAK+Y,QAEnDjY,EAAKZ,MAAK,SAAC8G,GACP,GAAIlJ,IAAWqd,EAAKxC,OAAO,GAEvB,OAGJ,GADyB,OAAR3R,EAETlJ,EAAOsd,SAAWD,EAAKpT,MAAM6R,UAC7BuB,EAAKxC,OAAOpZ,QACRya,GACAA,EAAIhT,SAMZ,GADAmU,EAAKxC,OAAOpZ,QACRya,EAAK,SAAAsB,EAAAhb,UAAA3D,OAhBE4e,EAAY,IAAAxa,MAAAua,EAAA,EAAAA,EAAA,KAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAZD,EAAYC,EAAA,GAAAlb,UAAAkb,GAiBnBxB,EAAI,QAASuB,EACjB,CAGJ,OADAzd,EAAOud,SAAU,EACVF,EAAKM,aAChB,IACAzb,KAAK2Y,OAAOzY,KAAKpC,GACjBkC,KAAKyb,aACT,CAOAA,WAAAA,GAA2B,IAAfC,EAAKpb,UAAA3D,OAAA,QAAAmI,IAAAxE,UAAA,IAAAA,UAAA,GACb,IAAKN,KAAKuY,WAAoC,IAAvBvY,KAAK2Y,OAAOhc,OAC/B,OAEJ,MAAMmB,EAASkC,KAAK2Y,OAAO,GACvB7a,EAAOud,UAAYK,IAGvB5d,EAAOud,SAAU,EACjBvd,EAAOsd,WACPpb,KAAK+Y,MAAQjb,EAAOib,MACpB/Y,KAAKa,KAAKR,MAAML,KAAMlC,EAAOgD,MACjC,CAOAhD,MAAAA,CAAOA,GACHA,EAAOuY,IAAMrW,KAAKqW,IAClBrW,KAAKsY,GAAGxM,QAAQhO,EACpB,CAMAiN,MAAAA,GAC4B,mBAAb/K,KAAKgZ,KACZhZ,KAAKgZ,MAAM5e,IACP4F,KAAK2b,mBAAmBvhB,EAAK,IAIjC4F,KAAK2b,mBAAmB3b,KAAKgZ,KAErC,CAOA2C,kBAAAA,CAAmBvhB,GACf4F,KAAKlC,OAAO,CACR3D,KAAMyb,GAAW6B,QACjBrd,KAAM4F,KAAK4b,KACLhiB,OAAOoU,OAAO,CAAE6N,IAAK7b,KAAK4b,KAAME,OAAQ9b,KAAK+b,aAAe3hB,GAC5DA,GAEd,CAOAmR,OAAAA,CAAQvE,GACChH,KAAKuY,WACNvY,KAAKgB,aAAa,gBAAiBgG,EAE3C,CAQAmE,OAAAA,CAAQhI,EAAQC,GACZpD,KAAKuY,WAAY,SACVvY,KAAK0R,GACZ1R,KAAKgB,aAAa,aAAcmC,EAAQC,GACxCpD,KAAKgc,YACT,CAOAA,UAAAA,GACIpiB,OAAOG,KAAKiG,KAAK8Y,MAAM9e,SAAS0X,IAE5B,IADmB1R,KAAK0Y,WAAWuD,MAAMne,GAAWL,OAAOK,EAAO4T,MAAQA,IACzD,CAEb,MAAMsI,EAAMha,KAAK8Y,KAAKpH,UACf1R,KAAK8Y,KAAKpH,GACbsI,EAAIY,WACJZ,EAAIvf,KAAKuF,KAAM,IAAIiD,MAAM,gCAEjC,IAER,CAOAoW,QAAAA,CAASvb,GAEL,GADsBA,EAAOuY,MAAQrW,KAAKqW,IAG1C,OAAQvY,EAAO3D,MACX,KAAKyb,GAAW6B,QACR3Z,EAAO1D,MAAQ0D,EAAO1D,KAAKwM,IAC3B5G,KAAKkc,UAAUpe,EAAO1D,KAAKwM,IAAK9I,EAAO1D,KAAKyhB,KAG5C7b,KAAKgB,aAAa,gBAAiB,IAAIiC,MAAM,8LAEjD,MACJ,KAAK2S,GAAWG,MAChB,KAAKH,GAAWO,aACZnW,KAAKmc,QAAQre,GACb,MACJ,KAAK8X,GAAWI,IAChB,KAAKJ,GAAWQ,WACZpW,KAAKoc,MAAMte,GACX,MACJ,KAAK8X,GAAW8B,WACZ1X,KAAKqc,eACL,MACJ,KAAKzG,GAAW+B,cACZ3X,KAAK4X,UACL,MAAM5Q,EAAM,IAAI/D,MAAMnF,EAAO1D,KAAKkiB,SAElCtV,EAAI5M,KAAO0D,EAAO1D,KAAKA,KACvB4F,KAAKgB,aAAa,gBAAiBgG,GAG/C,CAOAmV,OAAAA,CAAQre,GACJ,MAAMgD,EAAOhD,EAAO1D,MAAQ,GACxB,MAAQ0D,EAAO4T,IACf5Q,EAAKZ,KAAKF,KAAKga,IAAIlc,EAAO4T,KAE1B1R,KAAKuY,UACLvY,KAAKuc,UAAUzb,GAGfd,KAAKyY,cAAcvY,KAAKtG,OAAOoe,OAAOlX,GAE9C,CACAyb,SAAAA,CAAUzb,GACN,GAAId,KAAKwc,eAAiBxc,KAAKwc,cAAc7f,OAAQ,CACjD,MAAMsE,EAAYjB,KAAKwc,cAAc/c,QACrC,IAAK,MAAM6P,KAAYrO,EACnBqO,EAASjP,MAAML,KAAMc,EAE7B,CACAwC,MAAMzC,KAAKR,MAAML,KAAMc,GACnBd,KAAK4b,MAAQ9a,EAAKnE,QAA2C,kBAA1BmE,EAAKA,EAAKnE,OAAS,KACtDqD,KAAK+b,YAAcjb,EAAKA,EAAKnE,OAAS,GAE9C,CAMAqd,GAAAA,CAAItI,GACA,MAAMlQ,EAAOxB,KACb,IAAIyc,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAAK,QAAAC,EAAApc,UAAA3D,OAJImE,EAAI,IAAAC,MAAA2b,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ7b,EAAI6b,GAAArc,UAAAqc,GAKpBnb,EAAK1D,OAAO,CACR3D,KAAMyb,GAAWI,IACjBtE,GAAIA,EACJtX,KAAM0G,GALA,CAOd,CACJ,CAOAsb,KAAAA,CAAMte,GACF,MAAMkc,EAAMha,KAAK8Y,KAAKhb,EAAO4T,IACV,oBAARsI,WAGJha,KAAK8Y,KAAKhb,EAAO4T,IAEpBsI,EAAIY,WACJ9c,EAAO1D,KAAKoc,QAAQ,MAGxBwD,EAAI3Z,MAAML,KAAMlC,EAAO1D,MAC3B,CAMA8hB,SAAAA,CAAUxK,EAAImK,GACV7b,KAAK0R,GAAKA,EACV1R,KAAKwY,UAAYqD,GAAO7b,KAAK4b,OAASC,EACtC7b,KAAK4b,KAAOC,EACZ7b,KAAKuY,WAAY,EACjBvY,KAAK4c,eACL5c,KAAKgB,aAAa,WAClBhB,KAAKyb,aAAY,EACrB,CAMAmB,YAAAA,GACI5c,KAAKyY,cAAcze,SAAS8G,GAASd,KAAKuc,UAAUzb,KACpDd,KAAKyY,cAAgB,GACrBzY,KAAK0Y,WAAW1e,SAAS8D,IACrBkC,KAAKsa,wBAAwBxc,GAC7BkC,KAAKlC,OAAOA,EAAO,IAEvBkC,KAAK0Y,WAAa,EACtB,CAMA2D,YAAAA,GACIrc,KAAK4X,UACL5X,KAAKmL,QAAQ,uBACjB,CAQAyM,OAAAA,GACQ5X,KAAKoZ,OAELpZ,KAAKoZ,KAAKpf,SAAS6iB,GAAeA,MAClC7c,KAAKoZ,UAAOtU,GAEhB9E,KAAKsY,GAAa,SAAEtY,KACxB,CAiBAmY,UAAAA,GAUI,OATInY,KAAKuY,WACLvY,KAAKlC,OAAO,CAAE3D,KAAMyb,GAAW8B,aAGnC1X,KAAK4X,UACD5X,KAAKuY,WAELvY,KAAKmL,QAAQ,wBAEVnL,IACX,CAMAgE,KAAAA,GACI,OAAOhE,KAAKmY,YAChB,CAUAhF,QAAAA,CAASA,GAEL,OADAnT,KAAK+Y,MAAM5F,SAAWA,EACfnT,IACX,CAUA,YAAI8Z,GAEA,OADA9Z,KAAK+Y,MAAMe,UAAW,EACf9Z,IACX,CAcAgJ,OAAAA,CAAQA,GAEJ,OADAhJ,KAAK+Y,MAAM/P,QAAUA,EACdhJ,IACX,CAYA8c,KAAAA,CAAMxN,GAGF,OAFAtP,KAAKwc,cAAgBxc,KAAKwc,eAAiB,GAC3Cxc,KAAKwc,cAActc,KAAKoP,GACjBtP,IACX,CAYA+c,UAAAA,CAAWzN,GAGP,OAFAtP,KAAKwc,cAAgBxc,KAAKwc,eAAiB,GAC3Cxc,KAAKwc,cAAchG,QAAQlH,GACpBtP,IACX,CAmBAgd,MAAAA,CAAO1N,GACH,IAAKtP,KAAKwc,cACN,OAAOxc,KAEX,GAAIsP,EAAU,CACV,MAAMrO,EAAYjB,KAAKwc,cACvB,IAAK,IAAItgB,EAAI,EAAGA,EAAI+E,EAAUtE,OAAQT,IAClC,GAAIoT,IAAarO,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,IAGnB,MAEIA,KAAKwc,cAAgB,GAEzB,OAAOxc,IACX,CAKAid,YAAAA,GACI,OAAOjd,KAAKwc,eAAiB,EACjC,CAcAU,aAAAA,CAAc5N,GAGV,OAFAtP,KAAKmd,sBAAwBnd,KAAKmd,uBAAyB,GAC3Dnd,KAAKmd,sBAAsBjd,KAAKoP,GACzBtP,IACX,CAcAod,kBAAAA,CAAmB9N,GAGf,OAFAtP,KAAKmd,sBAAwBnd,KAAKmd,uBAAyB,GAC3Dnd,KAAKmd,sBAAsB3G,QAAQlH,GAC5BtP,IACX,CAmBAqd,cAAAA,CAAe/N,GACX,IAAKtP,KAAKmd,sBACN,OAAOnd,KAEX,GAAIsP,EAAU,CACV,MAAMrO,EAAYjB,KAAKmd,sBACvB,IAAK,IAAIjhB,EAAI,EAAGA,EAAI+E,EAAUtE,OAAQT,IAClC,GAAIoT,IAAarO,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,IAGnB,MAEIA,KAAKmd,sBAAwB,GAEjC,OAAOnd,IACX,CAKAsd,oBAAAA,GACI,OAAOtd,KAAKmd,uBAAyB,EACzC,CAQA7C,uBAAAA,CAAwBxc,GACpB,GAAIkC,KAAKmd,uBAAyBnd,KAAKmd,sBAAsBxgB,OAAQ,CACjE,MAAMsE,EAAYjB,KAAKmd,sBAAsB1d,QAC7C,IAAK,MAAM6P,KAAYrO,EACnBqO,EAASjP,MAAML,KAAMlC,EAAO1D,KAEpC,CACJ,ECr2BG,SAASmjB,GAAQhb,GACpBA,EAAOA,GAAQ,CAAC,EAChBvC,KAAKwd,GAAKjb,EAAKkb,KAAO,IACtBzd,KAAK0d,IAAMnb,EAAKmb,KAAO,IACvB1d,KAAK2d,OAASpb,EAAKob,QAAU,EAC7B3d,KAAK4d,OAASrb,EAAKqb,OAAS,GAAKrb,EAAKqb,QAAU,EAAIrb,EAAKqb,OAAS,EAClE5d,KAAK6d,SAAW,CACpB,CAOAN,GAAQhjB,UAAUujB,SAAW,WACzB,IAAIN,EAAKxd,KAAKwd,GAAK1a,KAAKoK,IAAIlN,KAAK2d,OAAQ3d,KAAK6d,YAC9C,GAAI7d,KAAK4d,OAAQ,CACb,IAAIG,EAAOjb,KAAKC,SACZib,EAAYlb,KAAKmb,MAAMF,EAAO/d,KAAK4d,OAASJ,GAChDA,EAAoC,IAAN,EAAxB1a,KAAKmb,MAAa,GAAPF,IAAuBP,EAAKQ,EAAYR,EAAKQ,CAClE,CACA,OAAgC,EAAzBlb,KAAK2a,IAAID,EAAIxd,KAAK0d,IAC7B,EAMAH,GAAQhjB,UAAU2jB,MAAQ,WACtBle,KAAK6d,SAAW,CACpB,EAMAN,GAAQhjB,UAAU4jB,OAAS,SAAUV,GACjCzd,KAAKwd,GAAKC,CACd,EAMAF,GAAQhjB,UAAU6jB,OAAS,SAAUV,GACjC1d,KAAK0d,IAAMA,CACf,EAMAH,GAAQhjB,UAAU8jB,UAAY,SAAUT,GACpC5d,KAAK4d,OAASA,CAClB,EC3DO,MAAMU,WAAgB5e,EACzBwD,WAAAA,CAAYuD,EAAKlE,GACb,IAAI6F,EACJ9E,QACAtD,KAAKue,KAAO,CAAC,EACbve,KAAKoZ,KAAO,GACR3S,GAAO,kBAAoBA,IAC3BlE,EAAOkE,EACPA,OAAM3B,IAEVvC,EAAOA,GAAQ,CAAC,GACX0C,KAAO1C,EAAK0C,MAAQ,aACzBjF,KAAKuC,KAAOA,EACZD,EAAsBtC,KAAMuC,GAC5BvC,KAAKwe,cAAmC,IAAtBjc,EAAKic,cACvBxe,KAAKye,qBAAqBlc,EAAKkc,sBAAwB3O,KACvD9P,KAAK0e,kBAAkBnc,EAAKmc,mBAAqB,KACjD1e,KAAK2e,qBAAqBpc,EAAKoc,sBAAwB,KACvD3e,KAAK4e,oBAAwD,QAAnCxW,EAAK7F,EAAKqc,2BAAwC,IAAPxW,EAAgBA,EAAK,IAC1FpI,KAAK6e,QAAU,IAAItB,GAAQ,CACvBE,IAAKzd,KAAK0e,oBACVhB,IAAK1d,KAAK2e,uBACVf,OAAQ5d,KAAK4e,wBAEjB5e,KAAKgJ,QAAQ,MAAQzG,EAAKyG,QAAU,IAAQzG,EAAKyG,SACjDhJ,KAAKuZ,YAAc,SACnBvZ,KAAKyG,IAAMA,EACX,MAAMqY,EAAUvc,EAAKwc,QAAUA,EAC/B/e,KAAKgf,QAAU,IAAIF,EAAQjJ,QAC3B7V,KAAKif,QAAU,IAAIH,EAAQpI,QAC3B1W,KAAKiZ,cAAoC,IAArB1W,EAAK2c,YACrBlf,KAAKiZ,cACLjZ,KAAK6D,MACb,CACA2a,YAAAA,CAAaW,GACT,OAAK7e,UAAU3D,QAEfqD,KAAKof,gBAAkBD,EAClBA,IACDnf,KAAKqf,eAAgB,GAElBrf,MALIA,KAAKof,aAMpB,CACAX,oBAAAA,CAAqBU,GACjB,YAAUra,IAANqa,EACOnf,KAAKsf,uBAChBtf,KAAKsf,sBAAwBH,EACtBnf,KACX,CACA0e,iBAAAA,CAAkBS,GACd,IAAI/W,EACJ,YAAUtD,IAANqa,EACOnf,KAAKuf,oBAChBvf,KAAKuf,mBAAqBJ,EACF,QAAvB/W,EAAKpI,KAAK6e,eAA4B,IAAPzW,GAAyBA,EAAG+V,OAAOgB,GAC5Dnf,KACX,CACA4e,mBAAAA,CAAoBO,GAChB,IAAI/W,EACJ,YAAUtD,IAANqa,EACOnf,KAAKwf,sBAChBxf,KAAKwf,qBAAuBL,EACJ,QAAvB/W,EAAKpI,KAAK6e,eAA4B,IAAPzW,GAAyBA,EAAGiW,UAAUc,GAC/Dnf,KACX,CACA2e,oBAAAA,CAAqBQ,GACjB,IAAI/W,EACJ,YAAUtD,IAANqa,EACOnf,KAAKyf,uBAChBzf,KAAKyf,sBAAwBN,EACL,QAAvB/W,EAAKpI,KAAK6e,eAA4B,IAAPzW,GAAyBA,EAAGgW,OAAOe,GAC5Dnf,KACX,CACAgJ,OAAAA,CAAQmW,GACJ,OAAK7e,UAAU3D,QAEfqD,KAAK0f,SAAWP,EACTnf,MAFIA,KAAK0f,QAGpB,CAOAC,oBAAAA,IAES3f,KAAK4f,eACN5f,KAAKof,eACqB,IAA1Bpf,KAAK6e,QAAQhB,UAEb7d,KAAK6f,WAEb,CAQAhc,IAAAA,CAAK9D,GACD,IAAKC,KAAKuZ,YAAYnU,QAAQ,QAC1B,OAAOpF,KACXA,KAAKoa,OAAS,IAAI0F,GAAO9f,KAAKyG,IAAKzG,KAAKuC,MACxC,MAAMmB,EAAS1D,KAAKoa,OACd5Y,EAAOxB,KACbA,KAAKuZ,YAAc,UACnBvZ,KAAKqf,eAAgB,EAErB,MAAMU,EAAiBngB,GAAG8D,EAAQ,QAAQ,WACtClC,EAAKuJ,SACLhL,GAAMA,GACV,IACM6D,EAAWoD,IACbhH,KAAK4T,UACL5T,KAAKuZ,YAAc,SACnBvZ,KAAKgB,aAAa,QAASgG,GACvBjH,EACAA,EAAGiH,GAIHhH,KAAK2f,sBACT,EAGEK,EAAWpgB,GAAG8D,EAAQ,QAASE,GACrC,IAAI,IAAU5D,KAAK0f,SAAU,CACzB,MAAM1W,EAAUhJ,KAAK0f,SAEfjF,EAAQza,KAAKsB,cAAa,KAC5Bye,IACAnc,EAAQ,IAAIX,MAAM,YAClBS,EAAOM,OAAO,GACfgF,GACChJ,KAAKuC,KAAKyI,WACVyP,EAAMvP,QAEVlL,KAAKoZ,KAAKlZ,MAAK,KACXF,KAAK0C,eAAe+X,EAAM,GAElC,CAGA,OAFAza,KAAKoZ,KAAKlZ,KAAK6f,GACf/f,KAAKoZ,KAAKlZ,KAAK8f,GACRhgB,IACX,CAOAiY,OAAAA,CAAQlY,GACJ,OAAOC,KAAK6D,KAAK9D,EACrB,CAMAgL,MAAAA,GAEI/K,KAAK4T,UAEL5T,KAAKuZ,YAAc,OACnBvZ,KAAKgB,aAAa,QAElB,MAAM0C,EAAS1D,KAAKoa,OACpBpa,KAAKoZ,KAAKlZ,KAAKN,GAAG8D,EAAQ,OAAQ1D,KAAKigB,OAAOxd,KAAKzC,OAAQJ,GAAG8D,EAAQ,OAAQ1D,KAAKkgB,OAAOzd,KAAKzC,OAAQJ,GAAG8D,EAAQ,QAAS1D,KAAKuL,QAAQ9I,KAAKzC,OAAQJ,GAAG8D,EAAQ,QAAS1D,KAAKmL,QAAQ1I,KAAKzC,OAE3LJ,GAAGI,KAAKif,QAAS,UAAWjf,KAAKmgB,UAAU1d,KAAKzC,OACpD,CAMAigB,MAAAA,GACIjgB,KAAKgB,aAAa,OACtB,CAMAkf,MAAAA,CAAO9lB,GACH,IACI4F,KAAKif,QAAQrI,IAAIxc,EACrB,CACA,MAAOuO,GACH3I,KAAKmL,QAAQ,cAAexC,EAChC,CACJ,CAMAwX,SAAAA,CAAUriB,GAENqD,GAAS,KACLnB,KAAKgB,aAAa,SAAUlD,EAAO,GACpCkC,KAAKsB,aACZ,CAMAiK,OAAAA,CAAQvE,GACJhH,KAAKgB,aAAa,QAASgG,EAC/B,CAOAtD,MAAAA,CAAO2S,EAAK9T,GACR,IAAImB,EAAS1D,KAAKue,KAAKlI,GAQvB,OAPK3S,EAII1D,KAAKiZ,eAAiBvV,EAAO4V,QAClC5V,EAAOuU,WAJPvU,EAAS,IAAI2Q,GAAOrU,KAAMqW,EAAK9T,GAC/BvC,KAAKue,KAAKlI,GAAO3S,GAKdA,CACX,CAOA0c,QAAAA,CAAS1c,GACL,MAAM6a,EAAO3kB,OAAOG,KAAKiG,KAAKue,MAC9B,IAAK,MAAMlI,KAAOkI,EAAM,CAEpB,GADeve,KAAKue,KAAKlI,GACdiD,OACP,MAER,CACAtZ,KAAKqgB,QACT,CAOAvU,OAAAA,CAAQhO,GACJ,MAAMqI,EAAiBnG,KAAKgf,QAAQ5gB,OAAON,GAC3C,IAAK,IAAI5B,EAAI,EAAGA,EAAIiK,EAAexJ,OAAQT,IACvC8D,KAAKoa,OAAO/V,MAAM8B,EAAejK,GAAI4B,EAAOoV,QAEpD,CAMAU,OAAAA,GACI5T,KAAKoZ,KAAKpf,SAAS6iB,GAAeA,MAClC7c,KAAKoZ,KAAKzc,OAAS,EACnBqD,KAAKif,QAAQrH,SACjB,CAMAyI,MAAAA,GACIrgB,KAAKqf,eAAgB,EACrBrf,KAAK4f,eAAgB,EACrB5f,KAAKmL,QAAQ,eACjB,CAMAgN,UAAAA,GACI,OAAOnY,KAAKqgB,QAChB,CAUAlV,OAAAA,CAAQhI,EAAQC,GACZ,IAAIgF,EACJpI,KAAK4T,UACkB,QAAtBxL,EAAKpI,KAAKoa,cAA2B,IAAPhS,GAAyBA,EAAGpE,QAC3DhE,KAAK6e,QAAQX,QACble,KAAKuZ,YAAc,SACnBvZ,KAAKgB,aAAa,QAASmC,EAAQC,GAC/BpD,KAAKof,gBAAkBpf,KAAKqf,eAC5Brf,KAAK6f,WAEb,CAMAA,SAAAA,GACI,GAAI7f,KAAK4f,eAAiB5f,KAAKqf,cAC3B,OAAOrf,KACX,MAAMwB,EAAOxB,KACb,GAAIA,KAAK6e,QAAQhB,UAAY7d,KAAKsf,sBAC9Btf,KAAK6e,QAAQX,QACble,KAAKgB,aAAa,oBAClBhB,KAAK4f,eAAgB,MAEpB,CACD,MAAMpN,EAAQxS,KAAK6e,QAAQf,WAC3B9d,KAAK4f,eAAgB,EACrB,MAAMnF,EAAQza,KAAKsB,cAAa,KACxBE,EAAK6d,gBAETrf,KAAKgB,aAAa,oBAAqBQ,EAAKqd,QAAQhB,UAEhDrc,EAAK6d,eAET7d,EAAKqC,MAAMmD,IACHA,GACAxF,EAAKoe,eAAgB,EACrBpe,EAAKqe,YACL7f,KAAKgB,aAAa,kBAAmBgG,IAGrCxF,EAAK8e,aACT,IACF,GACH9N,GACCxS,KAAKuC,KAAKyI,WACVyP,EAAMvP,QAEVlL,KAAKoZ,KAAKlZ,MAAK,KACXF,KAAK0C,eAAe+X,EAAM,GAElC,CACJ,CAMA6F,WAAAA,GACI,MAAMC,EAAUvgB,KAAK6e,QAAQhB,SAC7B7d,KAAK4f,eAAgB,EACrB5f,KAAK6e,QAAQX,QACble,KAAKgB,aAAa,YAAauf,EACnC,ECvWJ,MAAMC,GAAQ,CAAC,EACf,SAASvkB,GAAOwK,EAAKlE,GACE,kBAARkE,IACPlE,EAAOkE,EACPA,OAAM3B,GAGV,MAAM2b,ECHH,SAAaha,GAAqB,IAAhBxB,EAAI3E,UAAA3D,OAAA,QAAAmI,IAAAxE,UAAA,GAAAA,UAAA,GAAG,GAAIogB,EAAGpgB,UAAA3D,OAAA,EAAA2D,UAAA,QAAAwE,EAC/BjK,EAAM4L,EAEVia,EAAMA,GAA4B,qBAAbtZ,UAA4BA,SAC7C,MAAQX,IACRA,EAAMia,EAAIpZ,SAAW,KAAOoZ,EAAIhS,MAEjB,kBAARjI,IACH,MAAQA,EAAIjK,OAAO,KAEfiK,EADA,MAAQA,EAAIjK,OAAO,GACbkkB,EAAIpZ,SAAWb,EAGfia,EAAIhS,KAAOjI,GAGpB,sBAAsBka,KAAKla,KAExBA,EADA,qBAAuBia,EACjBA,EAAIpZ,SAAW,KAAOb,EAGtB,WAAaA,GAI3B5L,EAAMsT,EAAM1H,IAGX5L,EAAIwK,OACD,cAAcsb,KAAK9lB,EAAIyM,UACvBzM,EAAIwK,KAAO,KAEN,eAAesb,KAAK9lB,EAAIyM,YAC7BzM,EAAIwK,KAAO,QAGnBxK,EAAIoK,KAAOpK,EAAIoK,MAAQ,IACvB,MACMyJ,GADkC,IAA3B7T,EAAI6T,KAAKtJ,QAAQ,KACV,IAAMvK,EAAI6T,KAAO,IAAM7T,EAAI6T,KAS/C,OAPA7T,EAAI6W,GAAK7W,EAAIyM,SAAW,MAAQoH,EAAO,IAAM7T,EAAIwK,KAAOJ,EAExDpK,EAAI+lB,KACA/lB,EAAIyM,SACA,MACAoH,GACCgS,GAAOA,EAAIrb,OAASxK,EAAIwK,KAAO,GAAK,IAAMxK,EAAIwK,MAChDxK,CACX,CD7CmBgmB,CAAIpa,GADnBlE,EAAOA,GAAQ,CAAC,GACa0C,MAAQ,cAC/BwJ,EAASgS,EAAOhS,OAChBiD,EAAK+O,EAAO/O,GACZzM,EAAOwb,EAAOxb,KACd6b,EAAgBN,GAAM9O,IAAOzM,KAAQub,GAAM9O,GAAU,KAK3D,IAAI4G,EAaJ,OAjBsB/V,EAAKwe,UACvBxe,EAAK,0BACL,IAAUA,EAAKye,WACfF,EAGAxI,EAAK,IAAIgG,GAAQ7P,EAAQlM,IAGpBie,GAAM9O,KACP8O,GAAM9O,GAAM,IAAI4M,GAAQ7P,EAAQlM,IAEpC+V,EAAKkI,GAAM9O,IAEX+O,EAAOhd,QAAUlB,EAAKkB,QACtBlB,EAAKkB,MAAQgd,EAAOzR,UAEjBsJ,EAAG5U,OAAO+c,EAAOxb,KAAM1C,EAClC,CAGA3I,OAAOoU,OAAO/R,GAAQ,CAClBqiB,QAAO,GACPjK,OAAM,GACNiE,GAAIrc,GACJgc,QAAShc,I", "sources": ["../node_modules/engine.io-parser/build/esm/commons.js", "../node_modules/engine.io-parser/build/esm/encodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../node_modules/engine.io-parser/build/esm/decodePacket.browser.js", "../node_modules/engine.io-parser/build/esm/index.js", "../node_modules/@socket.io/component-emitter/lib/esm/index.js", "../node_modules/engine.io-client/build/esm/globals.js", "../node_modules/engine.io-client/build/esm/util.js", "../node_modules/engine.io-client/build/esm/transport.js", "../node_modules/engine.io-client/build/esm/contrib/parseqs.js", "../node_modules/engine.io-client/build/esm/transports/polling.js", "../node_modules/engine.io-client/build/esm/contrib/has-cors.js", "../node_modules/engine.io-client/build/esm/transports/polling-xhr.js", "../node_modules/engine.io-client/build/esm/transports/websocket.js", "../node_modules/engine.io-client/build/esm/transports/index.js", "../node_modules/engine.io-client/build/esm/transports/webtransport.js", "../node_modules/engine.io-client/build/esm/contrib/parseuri.js", "../node_modules/engine.io-client/build/esm/socket.js", "../node_modules/socket.io-parser/build/esm/is-binary.js", "../node_modules/socket.io-parser/build/esm/binary.js", "../node_modules/socket.io-parser/build/esm/index.js", "../node_modules/socket.io-client/build/esm/on.js", "../node_modules/socket.io-client/build/esm/socket.js", "../node_modules/socket.io-client/build/esm/contrib/backo2.js", "../node_modules/socket.io-client/build/esm/manager.js", "../node_modules/socket.io-client/build/esm/index.js", "../node_modules/socket.io-client/build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "_ref", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "chars", "lookup", "i", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "length", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "nextTick", "Promise", "resolve", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "pick", "_len", "attr", "_key", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "bind", "clearTimeoutFn", "randomString", "Date", "now", "Math", "random", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "socket", "forceBase64", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "undefined", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "Polling", "_polling", "name", "_poll", "total", "doPoll", "decodePayload", "encodedPayload", "encodedPackets", "decodedPacket", "encodePayload", "count", "join", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "value", "XMLHttpRequest", "err", "hasCORS", "empty", "BaseXHR", "location", "isSSL", "protocol", "xd", "req", "request", "method", "xhrStatus", "pollXhr", "Request", "createRequest", "_opts", "_method", "_uri", "_data", "_create", "_a", "xdomain", "xhr", "_xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "getResponseHeader", "status", "_onLoad", "_onError", "document", "_index", "requestsCount", "requests", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "hasXHR2", "newRequest", "responseType", "concat", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "protocols", "headers", "ws", "createSocket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "WebSocketCtor", "WebSocket", "MozWebSocket", "transports", "websocket", "_packet", "webtransport", "_transport", "WebTransport", "transportOptions", "closed", "catch", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "_writer", "getWriter", "read", "done", "polling", "assign", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "withEventListeners", "OFFLINE_EVENT_LISTENERS", "listener", "SocketWithoutUpgrade", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "parsed<PERSON><PERSON>", "_transportsByName", "t", "transportName", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "_beforeunloadEventListener", "transport", "_offlineEventListener", "_onClose", "_cookieJar", "createCookieJar", "_open", "createTransport", "EIO", "id", "priorWebsocketSuccess", "setTransport", "_onDrain", "_onPacket", "flush", "onHandshake", "JSON", "_sendPacket", "_resetPingTimeout", "code", "pingInterval", "pingTimeout", "_pingTimeoutTimer", "delay", "upgrading", "_getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "_hasPingExpired", "hasExpired", "msg", "options", "compress", "cleanupAndClose", "waitForUpgrade", "tryAllTransports", "SocketWithUpgrade", "_upgrades", "_probe", "failed", "onTransportOpen", "cleanup", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "o", "map", "DEFAULT_TRANSPORTS", "filter", "withNativeFile", "File", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "num", "newData", "reconstructPacket", "_reconstructPacket", "RESERVED_EVENTS", "PacketType", "Encoder", "replacer", "EVENT", "ACK", "encodeAsString", "encodeAsBinary", "BINARY_EVENT", "BINARY_ACK", "nsp", "stringify", "deconstruction", "unshift", "isObject", "Decoder", "reviver", "add", "reconstructor", "decodeString", "isBinaryEvent", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "destroy", "finishedReconstruction", "reconPack", "binData", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_autoConnect", "disconnected", "subEvents", "subs", "onpacket", "active", "_readyState", "_b", "_c", "_len2", "_key2", "retries", "fromQueue", "volatile", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "isConnected", "notifyOutgoingListeners", "_this", "ackTimeout", "timer", "_len3", "_key3", "with<PERSON><PERSON><PERSON>", "emitWithAck", "_len4", "_key4", "reject", "arg1", "arg2", "_this2", "tryCount", "pending", "_len5", "responseArgs", "_key5", "_drainQueue", "force", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "_clearAcks", "some", "onconnect", "onevent", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "sent", "_len6", "_key6", "emitBuffered", "subDestroy", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "deviation", "floor", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "decoder", "autoConnect", "v", "_reconnection", "skipReconnect", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "_destroy", "_close", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "sourceRoot": ""}