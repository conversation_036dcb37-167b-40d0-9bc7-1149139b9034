const path = require('path');
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const compression = require('compression');
const mongoose = require('mongoose');
const dotenv = require('dotenv');
const routes = require('./src/routes');

// Load environment variables
dotenv.config();

// Create Express app
const app = express();

// Explicitly handle OPTIONS requests globally (often needed for preflight)
app.options('*', cors({ // Enable CORS for all origins for OPTIONS requests
  origin: '*', 
  methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
  allowedHeaders: 'Content-Type,Authorization', // Add any other headers your frontend sends
  optionsSuccessStatus: 204 // Use 204 No Content for OPTIONS
}));

// Middleware
app.use(cors({
  origin: 'http://localhost:3002', // Keep specific origin for other requests
  optionsSuccessStatus: 200 
}));
app.use(helmet());
app.use(compression());
app.use(morgan('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files - using path.join for cross-platform compatibility
app.use(express.static(path.join(__dirname, 'public')));

// API Routes
app.use('/api', routes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'An error occurred on the server',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  retryWrites: true,
  w: 'majority'
})
  .then(() => {
    console.log('Connected to MongoDB');
    
    // Start server
    const PORT = process.env.PORT || 3000;
    app.listen(PORT, '0.0.0.0', () => {
      console.log(`Server running on port ${PORT}`);
    });
  })
  .catch(err => {
    console.error('Failed to connect to MongoDB', err);
    process.exit(1);
  });

module.exports = app;
