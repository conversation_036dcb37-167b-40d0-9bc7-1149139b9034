import { Router } from 'express';

import abTestRoutes from './ab-test';
import accessibilityRoutes from './accessibility';
import aiRoutes from './ai';
import aiTemplateRoutes from './ai-template.routes';
import analyticsRoutes from './analytics.routes';
import authRoutes from './auth.routes';
import awsRoutes from './aws.routes';
import billingRoutes from './billing.routes';
import blockRoutes from './block.routes';
import campaignRoutes from './campaign.routes';
import contactRoutes from './contact.routes';
import exportRoutes from './data-export';
import deliverabilityRoutes from './deliverability';
import integrationRoutes from './integrations';
import interactiveRoutes from './interactive';
import journeyRoutes from './journeys';
import mobilePreviewRoutes from './mobile-preview';
import personalizationRoutes from './personalization';
import recipientRoutes from './recipient.routes';
import scheduleRoutes from './scheduling';
import segmentRoutes from './segments';
import sendTimeRoutes from './send-time';
import templateRoutes from './template-recommendation';
import userRoutes from './user.routes';

const router = Router();

// API routes
router.use('/auth', authRoutes);
router.use('/ai', aiRoutes);
router.use('/ai-templates', aiTemplateRoutes);
router.use('/blocks', blockRoutes);
router.use('/personalization', personalizationRoutes);
router.use('/interactive', interactiveRoutes);
router.use('/send-time', sendTimeRoutes);
router.use('/ab-test', abTestRoutes);
router.use('/segments', segmentRoutes);
router.use('/deliverability', deliverabilityRoutes);
router.use('/journeys', journeyRoutes);
router.use('/integrations', integrationRoutes);
router.use('/mobile-preview', mobilePreviewRoutes);
router.use('/accessibility', accessibilityRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/templates', templateRoutes);
router.use('/schedules', scheduleRoutes);
router.use('/export', exportRoutes);
router.use('/contacts', contactRoutes);
router.use('/domains', awsRoutes);
router.use('/campaigns', campaignRoutes);
router.use('/recipients', recipientRoutes);
router.use('/user', userRoutes);
router.use('/billing', billingRoutes);

export default router;
