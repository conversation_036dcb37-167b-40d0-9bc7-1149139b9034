{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\EmailEditor.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Enhanced EmailEditor component for Driftly Email Generator\n * Provides advanced drag-and-drop interface for template creation and editing\n * Features: Real-time preview, responsive design, advanced block library, undo/redo\n */\n\nimport React, { useCallback, useEffect, useRef, useState } from 'react';\nimport mjml2html from 'mjml-browser';\nimport { useDrag, useDrop } from 'react-dnd';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport api from '../services/api'; // Corrected import path\n// Types - Adjust path as needed\n\nimport BlockEditor from './BlockEditor';\n// Components - Adjust paths as needed\nimport BlockLibrary from './BlockLibrary';\nimport EmailPreview from './EmailPreview';\nimport SaveTemplateModal from './SaveTemplateModal';\nimport Toolbar from './Toolbar';\n\n// Styles - Ensure this is imported in your main application entry point\n// e.g., import '../styles/editor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_API_URL || '/api'; // Use environment variable\n\n// Define Drag Item Types\nconst ItemTypes = {\n  BLOCK: 'block',\n  LIBRARY_BLOCK: 'library_block'\n};\n// Near the top of the file, add a template cache mechanism:\n// Cache for storing generated HTML previews\nconst templateCache = new Map();\n\n// Cache TTL (Time To Live) in milliseconds - 5 minutes\nconst CACHE_TTL = 5 * 60 * 1000;\n\n// Add this function before the EmailEditor component definition\n// Function to generate a cache key based on blocks\nconst generateCacheKey = blocks => {\n  return blocks.map(block => {\n    const {\n      instanceId,\n      content\n    } = block;\n    // Include only essential data in cache key\n    return `${block.blockId || block._id}:${instanceId}:${JSON.stringify(content)}`;\n  }).join('|');\n};\n\n// Function to clear stale cache entries\nconst clearStaleCache = () => {\n  const now = Date.now();\n  templateCache.forEach((entry, key) => {\n    if (now - entry.timestamp > CACHE_TTL) {\n      templateCache.delete(key);\n    }\n  });\n};\n\n// --- EmailEditor Component ---\n\nconst EmailEditor = () => {\n  _s();\n  var _editorBlocks$selecte;\n  const {\n    templateId\n  } = useParams();\n  const navigate = useNavigate();\n\n  // State\n  const [template, setTemplate] = useState({});\n  const [editorBlocks, setEditorBlocks] = useState([]);\n  const [availableBlocks, setAvailableBlocks] = useState([]);\n  const [selectedBlockIndex, setSelectedBlockIndex] = useState(null);\n  const [previewHtml, setPreviewHtml] = useState('');\n  const [previewMode, setPreviewMode] = useState('desktop');\n  const [isSaving, setIsSaving] = useState(false);\n  const [showSaveModal, setShowSaveModal] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [saveError, setSaveError] = useState(null);\n  const [userPreferences, setUserPreferences] = useState(null);\n  const [templateName, setTemplateName] = useState('');\n\n  // Refs\n  const previewIframeRef = useRef(null);\n  const editorCanvasRef = useRef(null);\n\n  // --- Data Fetching --- //\n  useEffect(() => {\n    let isMounted = true;\n    const fetchData = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Fetch available blocks first (always needed)\n        console.log(`Fetching blocks from ${API_URL}/blocks`);\n        // Use the shared api instance with BlockListResponse type\n        const blocksPromise = api.get('/blocks');\n\n        // Fetch user preferences to potentially apply brand colors/fonts later\n        console.log(`Fetching user preferences from ${API_URL}/user/preferences`);\n        // Use the shared api instance with correct inline type\n        const prefsPromise = api.get(`/user/preferences`);\n\n        // Fetch template data if ID exists\n        const templatePromise = templateId\n        // Use the shared api instance with TemplateDetailResponse type\n        ? api.get(`/templates/${templateId}`) : Promise.resolve(null);\n        const [blocksResponse, prefsResponse, templateResponse] = await Promise.all([blocksPromise, prefsPromise, templatePromise]);\n\n        // Access blocks correctly from response.data.data\n        const fetchedAvailableBlocks = blocksResponse.data.data || [];\n        console.log(`Fetched ${fetchedAvailableBlocks.length} available blocks.`);\n        setAvailableBlocks(fetchedAvailableBlocks);\n\n        // Process user preferences\n        const userPreferences = prefsResponse.data.preferences; // Store preferences\n        // TODO: Apply user preferences (e.g., brand colors, fonts)\n\n        if (templateResponse && templateResponse.data.template) {\n          const templateData = templateResponse.data.template; // Correct path now\n          console.log('[EmailEditor] Fetched template data:', JSON.stringify(templateData, null, 2)); // Log fetched data\n\n          // Apply brand colors/fonts from preferences if not set on template\n          setTemplate({\n            ...templateData,\n            brandColors: templateData.brandColors || (userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.brandColors),\n            defaultFonts: templateData.defaultFonts || (userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.defaultFonts)\n          });\n\n          // Populate editor blocks based on blockIds and fetched definitions\n          console.log('[EmailEditor] Attempting to populate blocks. Available block defs:', fetchedAvailableBlocks.length);\n          console.log('[EmailEditor] Template blockIds:', templateData.blockIds);\n          console.log('[EmailEditor] Template blocks data:', templateData.blocks);\n          if (templateData.blockIds && templateData.blockIds.length > 0 && fetchedAvailableBlocks.length > 0) {\n            console.log('[EmailEditor] Conditions met, proceeding to map blockIds.');\n            const populatedBlocks = templateData.blockIds.map((id, index) => {\n              var _templateData$blocks;\n              console.log(`[EmailEditor] Mapping blockId: ${id} at index: ${index}`);\n              // Match ID from templateData.blockIds with fetchedAvailableBlocks\n              const foundBlockDef = fetchedAvailableBlocks.find(b => b.blockId === id || b._id === id); // Check both blockId and _id\n              console.log(`[EmailEditor] ... Definition found for ${id}?`, foundBlockDef ? 'Yes' : 'No');\n              if (!foundBlockDef) {\n                console.warn(`[EmailEditor] Block definition not found for available block matching ID: ${id}. Skipping.`); // Refined warning\n                return null;\n              }\n\n              // Get content for this specific instance from templateData.blocks\n              const instanceBlockData = (_templateData$blocks = templateData.blocks) === null || _templateData$blocks === void 0 ? void 0 : _templateData$blocks[index]; // Get the block data saved for this instance\n              const instanceContent = (instanceBlockData === null || instanceBlockData === void 0 ? void 0 : instanceBlockData.content) || foundBlockDef.content || {}; // Use instance content, fallback to definition's default\n              console.log(`[EmailEditor] ... Instance content for ${id}:`, instanceContent);\n              const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`; // Unique key for React\n\n              // Return the combined block object for the editor state\n              const blockForState = {\n                ...foundBlockDef,\n                // Base definition (MJML, name, category etc.)\n                content: {\n                  ...instanceContent\n                },\n                // Specific content for this instance\n                instanceId // Unique ID for this instance in the editor\n              };\n              console.log(`[EmailEditor] ... Created block object for state for ${id}:`, blockForState);\n              return blockForState;\n            }).filter(b => b !== null);\n            console.log(\"[EmailEditor] Populated blocks array before setting state:\", populatedBlocks);\n            setEditorBlocks(populatedBlocks);\n          } else {\n            var _templateData$blockId;\n            console.log(\"[EmailEditor] Condition for populating blocks NOT met:\", {\n              hasBlockIds: !!templateData.blockIds,\n              blockIdsLength: (_templateData$blockId = templateData.blockIds) === null || _templateData$blockId === void 0 ? void 0 : _templateData$blockId.length,\n              hasFetchedBlocks: !!fetchedAvailableBlocks,\n              fetchedBlocksLength: fetchedAvailableBlocks === null || fetchedAvailableBlocks === void 0 ? void 0 : fetchedAvailableBlocks.length\n            });\n            setEditorBlocks([]); // Sets blocks to empty\n          }\n        } else {\n          // New template: initialize with preferences\n          console.log('Initializing new template state with preferences.');\n          setTemplate({\n            templateName: 'Untitled Template',\n            subject: 'Your Subject Here',\n            brandColors: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.brandColors,\n            defaultFonts: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.defaultFonts,\n            // Initialize other necessary fields if needed\n            userId: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.userId // Important for saving later\n          });\n          setEditorBlocks([]);\n        }\n      } catch (err) {\n        console.error('Error loading editor data:', err);\n        let errorMsg = 'Failed to load editor data.'; // Default message\n\n        if (err.response) {\n          var _err$config, _err$config$url;\n          // Check if it's the template fetch that failed with 404\n          if ((_err$config = err.config) !== null && _err$config !== void 0 && (_err$config$url = _err$config.url) !== null && _err$config$url !== void 0 && _err$config$url.includes(`/templates/${templateId}`) && err.response.status === 404) {\n            errorMsg = `Template with ID ${templateId} not found. It may have been deleted.`;\n            // Optionally, redirect the user or clear the template state\n            // navigate('/templates'); // Example redirect\n            setTemplate({}); // Clear any partial template data\n            setEditorBlocks([]);\n          } else {\n            var _err$response$data, _err$response$data2;\n            // Use the error message from the response if available, otherwise use the generic message\n            errorMsg = ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || ((_err$response$data2 = err.response.data) === null || _err$response$data2 === void 0 ? void 0 : _err$response$data2.message) || err.message || errorMsg;\n          }\n        } else {\n          // Network error or other issue\n          errorMsg = err.message || errorMsg;\n        }\n        setError(errorMsg);\n        // Fallback logic: Check if availableBlocks is empty (meaning block fetch might have also failed)\n        if (availableBlocks.length === 0) {\n          console.warn(\"Falling back to empty block list as blocks couldn't be fetched or list is empty.\");\n          setAvailableBlocks([]); // Ensure it's an empty array if blocks failed\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchData();\n    return () => {\n      isMounted = false;\n    };\n    // eslint-disable-next-line\n  }, [templateId]); // Rerun only when templateId changes\n\n  // --- MJML Generation & Preview Update --- //\n  const generateMjmlFromBlocks = useCallback(blockList => {\n    var _template$brandColors, _template$defaultFont;\n    // If there are no blocks, return early with an empty string\n    if (!blockList.length) return '';\n\n    // Generate a cache key for this block configuration\n    const cacheKey = generateCacheKey(blockList);\n\n    // Check if we have a cached version\n    if (templateCache.has(cacheKey)) {\n      const cached = templateCache.get(cacheKey);\n      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {\n        console.log(\"[EmailEditor] Using cached MJML\");\n        return cached.mjml;\n      }\n    }\n\n    // Periodically clean up old cache entries\n    clearStaleCache();\n\n    // Process blocks in chunks if there are many\n    const chunkSize = 5;\n    let mjmlBodyContent = '';\n    for (let i = 0; i < blockList.length; i += chunkSize) {\n      const chunk = blockList.slice(i, i + chunkSize);\n      const chunkContent = chunk.map(block => {\n        let blockMjml = block.mjml || '';\n        const content = block.content || {};\n\n        // More robust replacement logic\n        Object.keys(content).forEach(key => {\n          const value = content[key];\n          const placeholder = `{{${key}}}`; // Standard placeholder\n\n          // Only process if value exists\n          if (value !== undefined && value !== null) {\n            // Handle arrays specifically (e.g., nav links, social icons)\n            if (Array.isArray(value)) {\n              // Existing array handling code...\n              if (key === 'nav_links' || key === 'navLinks') {\n                const linksHtml = value.map(link => `<mj-navbar-link href=\"${link.url || '#'}\" color=\"#1E3A8A\">${link.name || 'Link'}</mj-navbar-link>`).join('\\n');\n                blockMjml = blockMjml.replace('{{navLinksArea}}', linksHtml);\n              } else if (key === 'social_icons' || key === 'socialLinks') {\n                const iconsHtml = value.map(icon => {\n                  var _icon$platform;\n                  return `<mj-social-element name=\"${((_icon$platform = icon.platform) === null || _icon$platform === void 0 ? void 0 : _icon$platform.toLowerCase()) || 'share'}\" href=\"${icon.url || '#'}\"></mj-social-element>`;\n                }).join('\\n');\n                blockMjml = blockMjml.replace('{{socialIconsArea}}', iconsHtml);\n              }\n            } else {\n              // More efficient single replacement (no array of placeholders)\n              const stringValue = String(value);\n              blockMjml = blockMjml.replaceAll(placeholder, stringValue);\n\n              // Only try alternative formats if there's a good chance they exist\n              if (blockMjml.includes('{{')) {\n                // Convert camelCase to snake_case for placeholder check if needed\n                const camelCasePlaceholder = `{{${key.replace(/_([a-z])/g, g => g[1].toUpperCase())}}}`;\n                const snakeCasePlaceholder = `{{${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)}}}`;\n                blockMjml = blockMjml.replaceAll(camelCasePlaceholder, stringValue);\n                blockMjml = blockMjml.replaceAll(snakeCasePlaceholder, stringValue);\n              }\n            }\n          }\n        });\n\n        // More efficient placeholder cleanup - only if needed\n        if (blockMjml.includes('{{')) {\n          blockMjml = blockMjml.replace(/\\{\\{[\\w.-]+\\}\\}/g, '');\n        }\n        return blockMjml;\n      }).join('\\n');\n      mjmlBodyContent += chunkContent;\n    }\n\n    // Use template state for colors/fonts, falling back to defaults\n    const brandColors = (_template$brandColors = template === null || template === void 0 ? void 0 : template.brandColors) !== null && _template$brandColors !== void 0 ? _template$brandColors : {};\n    const defaultFonts = (_template$defaultFont = template === null || template === void 0 ? void 0 : template.defaultFonts) !== null && _template$defaultFont !== void 0 ? _template$defaultFont : {};\n    const primaryColor = brandColors.primary || '#4F46E5';\n    const backgroundColor = brandColors.background || '#f3f4f6';\n    const textColor = brandColors.text || '#111827';\n    const headingFont = defaultFonts.heading || 'Arial, sans-serif';\n    const bodyFont = defaultFonts.body || 'Arial, sans-serif';\n\n    // Construct the full MJML document\n    const fullMjml = `\n<mjml>\n  <mj-head>\n    <mj-title>${(template === null || template === void 0 ? void 0 : template.subject) || 'Email Template'}</mj-title>\n    <mj-font name=\"Roboto\" href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700\" />\n    <mj-attributes>\n      <mj-all padding=\"0px\" font-family=\"${bodyFont}\" />\n      <mj-text padding=\"10px 25px\" font-size=\"14px\" line-height=\"1.5\" color=\"${textColor}\" />\n      <mj-section padding=\"10px 0\" />\n      <mj-column padding=\"5px\" />\n      <mj-button background-color=\"${primaryColor}\" color=\"#ffffff\" font-weight=\"bold\" border-radius=\"4px\" padding=\"10px 20px\" />\n      <mj-image padding=\"0px\" />\n    </mj-attributes>\n    <mj-style inline=\"inline\">\n      /* Inline styles */\n      a { color: ${primaryColor} !important; text-decoration: none !important; }\n    </mj-style>\n     <mj-style>\n      /* Embedded styles */\n      .hover-link:hover { text-decoration: underline !important; }\n    </mj-style>\n  </mj-head>\n  <mj-body background-color=\"${backgroundColor}\">\n    ${mjmlBodyContent}\n  </mj-body>\n</mjml>`;\n\n    // Store in cache\n    templateCache.set(cacheKey, {\n      mjml: fullMjml,\n      html: '',\n      // Will be populated on first HTML conversion\n      timestamp: Date.now()\n    });\n    return fullMjml;\n  }, [template]);\n  useEffect(() => {\n    const generatePreview = () => {\n      if (!editorBlocks || editorBlocks.length === 0) {\n        setPreviewHtml('<div style=\"display: flex; justify-content: center; align-items: center; height: 100%; color: grey; padding: 20px; text-align: center;\">Drag blocks here to build your email</div>');\n        return;\n      }\n      try {\n        // Generate cache key\n        const cacheKey = generateCacheKey(editorBlocks);\n\n        // Check for valid cached HTML\n        if (templateCache.has(cacheKey)) {\n          const cached = templateCache.get(cacheKey);\n          if (cached && cached.html && Date.now() - cached.timestamp < CACHE_TTL) {\n            console.log(\"[EmailEditor Preview] Using cached HTML\");\n            setPreviewHtml(cached.html);\n            return;\n          }\n        }\n\n        // If we get here, generate a new preview\n        const mjmlString = generateMjmlFromBlocks(editorBlocks);\n        console.log(\"[EmailEditor Preview] Generating fresh HTML preview\");\n\n        // Use a web worker for MJML conversion if possible\n        if (window.Worker) {\n          // Note: You would need to create a separate mjml-worker.js file\n          // This is just demonstrating the concept\n          const workerTimeout = setTimeout(() => {\n            // Fallback if worker takes too long\n            try {\n              const {\n                html,\n                errors\n              } = mjml2html(mjmlString, {\n                validationLevel: 'soft'\n              });\n              setPreviewHtml(html);\n              // Update cache\n              const cacheKey = generateCacheKey(editorBlocks);\n              if (templateCache.has(cacheKey)) {\n                const entry = templateCache.get(cacheKey);\n                if (entry) {\n                  entry.html = html;\n                  entry.timestamp = Date.now();\n                }\n              }\n            } catch (fallbackErr) {\n              console.error('[EmailEditor Preview] Error in fallback conversion:', fallbackErr);\n              setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${(fallbackErr === null || fallbackErr === void 0 ? void 0 : fallbackErr.message) || 'Unknown error'}</div>`);\n            }\n          }, 1000); // 1 second timeout\n\n          // This is just conceptual - actual implementation would need the worker file\n          // const worker = new Worker('/mjml-worker.js');\n          // worker.postMessage(mjmlString);\n          // worker.onmessage = (e) => {\n          //   clearTimeout(workerTimeout);\n          //   const { html, errors } = e.data;\n          //   setPreviewHtml(html);\n          //   // Update cache\n          //   updateHtmlCache(cacheKey, html);\n          // };\n        } else {\n          // Direct conversion when Web Workers aren't available\n          const {\n            html,\n            errors\n          } = mjml2html(mjmlString, {\n            validationLevel: 'soft'\n          });\n          if (errors && errors.length > 0) {\n            console.warn('[EmailEditor Preview] MJML Validation:', errors.length, 'issues');\n          }\n          setPreviewHtml(html);\n\n          // Update cache\n          if (templateCache.has(cacheKey)) {\n            const entry = templateCache.get(cacheKey);\n            if (entry) {\n              entry.html = html;\n              entry.timestamp = Date.now();\n            }\n          }\n        }\n      } catch (err) {\n        console.error('[EmailEditor Preview] Error generating preview:', err);\n        setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${(err === null || err === void 0 ? void 0 : err.message) || 'Unknown error'}</div>`);\n      }\n    };\n\n    // Debounce the preview generation to avoid too many updates\n    const debounceTimeout = setTimeout(generatePreview, 300);\n    return () => clearTimeout(debounceTimeout);\n  }, [editorBlocks, generateMjmlFromBlocks]);\n\n  // --- DND Callbacks --- //\n  const moveBlock = useCallback((dragIndex, hoverIndex) => {\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = [...prevBlocks];\n      const [draggedBlock] = newBlocks.splice(dragIndex, 1);\n      newBlocks.splice(hoverIndex, 0, draggedBlock);\n      return newBlocks;\n    });\n    // Adjust selected index\n    if (selectedBlockIndex === dragIndex) {\n      setSelectedBlockIndex(hoverIndex);\n    } else if (selectedBlockIndex !== null) {\n      if (dragIndex < hoverIndex && selectedBlockIndex > dragIndex && selectedBlockIndex <= hoverIndex) {\n        setSelectedBlockIndex(s => s !== null ? s - 1 : null);\n      } else if (dragIndex > hoverIndex && selectedBlockIndex >= hoverIndex && selectedBlockIndex < dragIndex) {\n        setSelectedBlockIndex(s => s !== null ? s + 1 : null);\n      }\n    }\n  }, [selectedBlockIndex]);\n  const dropBlockFromLibrary = useCallback((block, index) => {\n    // Cast the block to ensure it has all required properties\n    const blockWithRequiredProps = {\n      ...JSON.parse(JSON.stringify(block)),\n      blockId: block.blockId || block._id || `block-${Date.now()}`,\n      // Ensure blockId is never undefined\n      content: block.content ? JSON.parse(JSON.stringify(block.content)) : {},\n      instanceId: `${block.blockId || block._id || 'new'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n    };\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = [...prevBlocks];\n      newBlocks.splice(index, 0, blockWithRequiredProps);\n      return newBlocks;\n    });\n    setSelectedBlockIndex(index); // Select the newly dropped block\n  }, []);\n  const removeBlock = useCallback(index => {\n    setEditorBlocks(prevBlocks => prevBlocks.filter((_, i) => i !== index));\n    if (selectedBlockIndex === index) {\n      setSelectedBlockIndex(null);\n    } else if (selectedBlockIndex !== null && selectedBlockIndex > index) {\n      setSelectedBlockIndex(prevIndex => prevIndex !== null ? prevIndex - 1 : null);\n    }\n  }, [selectedBlockIndex]);\n  const updateBlockContent = useCallback((index, updatedContent) => {\n    setEditorBlocks(prevBlocks => prevBlocks.map((block, i) => i === index ? {\n      ...block,\n      content: {\n        ...(block.content || {}),\n        ...updatedContent\n      }\n    } : block));\n  }, []);\n\n  // --- Save Handler --- //\n  const handleSave = async (templateNameToSave, isPublicStatus = false) => {\n    setIsSaving(true);\n    setSaveError(null);\n    try {\n      const mjml = generateMjmlFromBlocks(editorBlocks);\n      const {\n        html,\n        errors: conversionErrors\n      } = mjml2html(mjml);\n      if (conversionErrors && conversionErrors.length > 0) {\n        console.warn('MJML conversion errors detected during save:', conversionErrors);\n        // Consider not saving if critical errors occurred\n      }\n      const blockIds = editorBlocks.map(b => String(b.blockId || b._id)) // Ensure string ID\n      .filter(Boolean); // Filter out any potential undefined/null\n\n      const payload = {\n        templateId: template === null || template === void 0 ? void 0 : template._id,\n        templateName: templateNameToSave,\n        mjml,\n        html,\n        blockIds,\n        subject: (template === null || template === void 0 ? void 0 : template.subject) || 'Untitled Template',\n        tags: (template === null || template === void 0 ? void 0 : template.tags) || [],\n        isPublic: isPublicStatus,\n        description: (template === null || template === void 0 ? void 0 : template.description) || '',\n        aiPrompt: (template === null || template === void 0 ? void 0 : template.aiPrompt) || '',\n        isAiGenerated: (template === null || template === void 0 ? void 0 : template.isAiGenerated) || false\n      };\n\n      // Add userId if it's a new template - CRITICAL: Ensure req.user is populated in backend\n      // This assumes the backend will get the userId from the authenticated request (authenticateJWT)\n      // if (!payload.templateId) {\n      //   payload.userId = template?.userId; // Might be set from prefs initially\n      // }\n\n      const response = await api.post('/templates/save', payload);\n      const savedTemplate = response.data.template;\n      setTemplate(savedTemplate);\n\n      // Re-sync editor blocks if necessary (e.g., if backend modifies content/IDs)\n      // This part needs careful implementation if backend modifies block content on save\n      if (savedTemplate.blockIds && availableBlocks.length > 0) {\n        const populatedBlocks = savedTemplate.blockIds.map((id, index) => {\n          var _savedTemplate$blocks, _savedTemplate$blocks2;\n          const foundBlockDef = availableBlocks.find(b => b.blockId === id || b._id === id);\n          const instanceContent = ((_savedTemplate$blocks = savedTemplate.blocks) === null || _savedTemplate$blocks === void 0 ? void 0 : (_savedTemplate$blocks2 = _savedTemplate$blocks[index]) === null || _savedTemplate$blocks2 === void 0 ? void 0 : _savedTemplate$blocks2.content) || (foundBlockDef === null || foundBlockDef === void 0 ? void 0 : foundBlockDef.content) || {};\n          const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`;\n          return foundBlockDef ? {\n            ...foundBlockDef,\n            content: {\n              ...instanceContent\n            },\n            instanceId\n          } : null;\n        }).filter(b => b !== null);\n        setEditorBlocks(populatedBlocks);\n      }\n      setShowSaveModal(false);\n      console.log('Template saved successfully!');\n      if (!templateId && savedTemplate._id) {\n        navigate(`/email-editor/${savedTemplate._id}`, {\n          replace: true\n        });\n      }\n      return savedTemplate;\n    } catch (err) {\n      var _err$response, _err$response$data3;\n      console.error('Error saving template:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data3 = _err$response.data) === null || _err$response$data3 === void 0 ? void 0 : _err$response$data3.error) || err.message || 'Failed to save template.';\n      setSaveError(errorMessage);\n      return null;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // --- Drop Target for Canvas --- //\n  const [{\n    isOverCanvas\n  }, drop] = useDrop(() => ({\n    accept: [ItemTypes.BLOCK, ItemTypes.LIBRARY_BLOCK],\n    drop: (item, monitor) => {\n      if (monitor.didDrop()) return;\n      const editorDiv = editorCanvasRef.current;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset || !editorDiv) return;\n      const hoverIndex = findDropIndex(clientOffset.y, editorDiv);\n      if (item.type === ItemTypes.LIBRARY_BLOCK) {\n        dropBlockFromLibrary(item.block, hoverIndex);\n      }\n      // Reordering is handled by DraggableBlock's hover\n    },\n    collect: monitor => ({\n      isOverCanvas: monitor.isOver({\n        shallow: true\n      })\n    })\n  }), [editorBlocks, moveBlock, dropBlockFromLibrary]); // Ensure correct dependencies\n\n  // Helper function to find the correct drop index\n  const findDropIndex = (clientY, container) => {\n    if (clientY === undefined) return editorBlocks.length;\n    const containerRect = container.getBoundingClientRect();\n    const offsetY = clientY - containerRect.top + container.scrollTop;\n    let calculatedIndex = editorBlocks.length;\n    const children = Array.from(container.children);\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (!child.classList || !child.classList.contains('draggable-block')) continue;\n      const childTop = child.offsetTop;\n      const childHeight = child.offsetHeight;\n      const middleY = childTop + childHeight / 2;\n      if (offsetY < middleY) {\n        calculatedIndex = i;\n        break;\n      }\n    }\n    return calculatedIndex;\n  };\n\n  // Cleanup cache when component unmounts\n  useEffect(() => {\n    return () => {\n      // Clear the entire cache when this editor instance is unmounted\n      templateCache.clear();\n    };\n  }, []);\n\n  // Add a cleanup function for memory management\n  const cleanupUnusedResources = useCallback(() => {\n    // Clear any unused thumbnails from memory if browser gets low on memory\n    if ('memory' in performance) {\n      const memoryInfo = performance.memory;\n      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {\n        console.log('[EmailEditor] Memory pressure detected, cleaning up resources');\n        // Force a garbage collection if possible\n        clearStaleCache();\n        // You could also unload any non-visible thumbnails or other large objects\n      }\n    }\n  }, []);\n\n  // Listen for low memory events\n  useEffect(() => {\n    if ('onmemorywarning' in window) {\n      window.addEventListener('memorywarning', cleanupUnusedResources);\n      return () => {\n        window.removeEventListener('memorywarning', cleanupUnusedResources);\n      };\n    }\n  }, [cleanupUnusedResources]);\n\n  // --- Render Logic --- //\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-screen text-gray-600 text-lg\",\n      children: \"Loading Editor...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 689,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Critical error on initial load\n  if (error && !template) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 m-4 text-red-700 bg-red-100 border border-red-400 rounded\",\n      children: [\"Error loading editor configuration: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 694,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"email-editor flex flex-col h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      onSave: () => {\n        setTemplate(prev => ({\n          ...(prev || {}),\n          templateName: (prev === null || prev === void 0 ? void 0 : prev.templateName) || 'Untitled Template'\n        }));\n        setSaveError(null);\n        setShowSaveModal(true);\n      },\n      onPreviewModeChange: setPreviewMode,\n      previewMode: previewMode,\n      isSaving: isSaving\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 699,\n      columnNumber: 7\n    }, this), error && templateId && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 text-sm text-red-700 bg-red-100 border-b border-red-300 text-center\",\n      children: [\"Error: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `editor-content flex flex-1 overflow-hidden`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"block-library-panel w-64 bg-white border-r border-gray-200 flex flex-col overflow-hidden shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"px-4 py-3 text-base font-semibold text-gray-800 border-b border-gray-200 whitespace-nowrap\",\n          children: \"Block Library\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BlockLibrary, {\n          blocks: availableBlocks,\n          onAddBlock: block => dropBlockFromLibrary(block, editorBlocks.length) // Click adds to end\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 719,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: drop,\n        className: `editor-workspace flex-1 flex flex-col overflow-hidden bg-gray-200 ${isOverCanvas ? 'outline-2 outline-dashed outline-indigo-500' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: editorCanvasRef,\n          className: \"blocks-container flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto\",\n          children: editorBlocks.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-blocks flex items-center justify-center h-full text-gray-500 text-center p-8 border-2 border-dashed border-gray-300 rounded-lg min-h-[200px]\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Drag blocks from the library here\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 53\n              }, this), \"or click a block in the library to add it.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 729,\n            columnNumber: 15\n          }, this) : editorBlocks.map((block, index) => /*#__PURE__*/_jsxDEV(DraggableBlock, {\n            // Use unique instanceId\n            block: block,\n            index: index,\n            moveBlock: moveBlock,\n            removeBlock: removeBlock,\n            isSelected: selectedBlockIndex === index,\n            onClick: () => setSelectedBlockIndex(index)\n          }, block.instanceId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"right-panel w-80 md:w-96 lg:w-[500px] border-l border-gray-300 flex flex-col shrink-0 bg-white shadow-lg\",\n        children: selectedBlockIndex === null || editorBlocks.length === 0 ?\n        /*#__PURE__*/\n        // Show Preview when no block is selected OR if editor is empty\n        _jsxDEV(\"div\", {\n          className: \"preview-panel flex-1 flex flex-col overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200\",\n            children: \"Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(EmailPreview, {\n            html: previewHtml,\n            mode: previewMode,\n            iframeRef: previewIframeRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 754,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 17\n        }, this) :\n        /*#__PURE__*/\n        // Show Block Editor when a block is selected\n        _jsxDEV(\"div\", {\n          className: \"block-editor-panel flex-1 flex flex-col overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200 flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Edit: \", ((_editorBlocks$selecte = editorBlocks[selectedBlockIndex]) === null || _editorBlocks$selecte === void 0 ? void 0 : _editorBlocks$selecte.name) || 'Block']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedBlockIndex(null),\n              className: \"text-sm text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100\",\n              title: \"Close Editor\",\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto\",\n            children: editorBlocks[selectedBlockIndex] &&\n            /*#__PURE__*/\n            // Check block exists before rendering\n            _jsxDEV(BlockEditor, {\n              block: editorBlocks[selectedBlockIndex],\n              onUpdate: content => {\n                if (selectedBlockIndex !== null) {\n                  updateBlockContent(selectedBlockIndex, content);\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 762,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 749,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 715,\n      columnNumber: 7\n    }, this), showSaveModal && /*#__PURE__*/_jsxDEV(SaveTemplateModal, {\n      initialName: (template === null || template === void 0 ? void 0 : template.templateName) || 'Untitled Template',\n      onSave: handleSave,\n      onCancel: () => {\n        setShowSaveModal(false);\n        setSaveError(null);\n      },\n      isSaving: isSaving,\n      error: saveError // Pass save error to modal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 793,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 698,\n    columnNumber: 5\n  }, this);\n};\n\n// Draggable Block Component (Internal to EmailEditor)\n_s(EmailEditor, \"a2vAXQo9CXvywYiuHPVbS3zDgvQ=\", false, function () {\n  return [useParams, useNavigate, useDrop];\n});\n_c = EmailEditor;\nconst DraggableBlock = ({\n  block,\n  index,\n  moveBlock,\n  removeBlock,\n  isSelected,\n  onClick\n}) => {\n  _s2();\n  const ref = useRef(null);\n  const [{\n    handlerId\n  }, drop] = useDrop(() => ({\n    accept: ItemTypes.BLOCK,\n    hover: (item, monitor) => {\n      if (!ref.current) return;\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) return;\n      const hoverBoundingRect = ref.current.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n      const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;\n      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;\n      moveBlock(dragIndex, hoverIndex);\n      item.index = hoverIndex; // Mutate monitor item for performance\n    },\n    collect: monitor => ({\n      handlerId: monitor.getHandlerId()\n    })\n  }), [index, moveBlock]); // Dependencies for useDrop hover logic\n\n  const [{\n    isDragging\n  }, drag] = useDrag(() => ({\n    type: ItemTypes.BLOCK,\n    item: {\n      index,\n      id: block.instanceId,\n      type: ItemTypes.BLOCK\n    },\n    // Return item as a function\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [index, block.instanceId]); // Dependencies for useDrag\n\n  drag(drop(ref)); // Combine drag and drop refs\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: ref,\n    \"data-handler-id\": handlerId,\n    className: `draggable-block bg-white border rounded-md mb-4 cursor-move shadow-sm ${isDragging ? 'opacity-40 border-blue-500' : 'hover:border-blue-400 hover:shadow-md'} ${isSelected ? 'border-blue-500 ring-2 ring-blue-300 ring-offset-1' : 'border-gray-200'}` // styles/editor.css\n    ,\n    onClick: onClick,\n    style: {\n      opacity: isDragging ? 0.4 : 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-header flex items-center justify-between px-3 py-1.5 border-b border-gray-200 bg-gray-50 rounded-t-md text-xs\",\n      children: [\" \", /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block-type font-medium text-gray-700 truncate pr-2\",\n        title: block.name,\n        children: [block.name, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-400\",\n          children: [\"(\", block.category, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 110\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"block-actions flex items-center space-x-1\",\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n          onClick: e => {\n            e.stopPropagation();\n            console.log(\"Duplicate block:\", index); // TODO: Implement duplicate functionality\n          },\n          title: \"Duplicate Block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 20\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 872,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"remove-block p-1 text-gray-400 hover:text-red-600 transition-colors\" // styles/editor.css\n          ,\n          onClick: e => {\n            e.stopPropagation();\n            removeBlock(index);\n          },\n          title: \"Remove Block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 897,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 868,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-content-preview p-3 text-sm text-gray-600 bg-white rounded-b-md min-h-[50px]\",\n      children: [(() => {\n        // Display appropriate preview based on block type\n        if (block.blockId === 'header/simple-nav') {\n          return 'Navigation Header';\n        } else if (block.blockId === 'layout/hero') {\n          var _block$content;\n          return ((_block$content = block.content) === null || _block$content === void 0 ? void 0 : _block$content.heroHeadline) || 'Hero Section';\n        } else if (block.blockId === 'content/headline') {\n          var _block$content2;\n          return ((_block$content2 = block.content) === null || _block$content2 === void 0 ? void 0 : _block$content2.headline) || 'Headline';\n        } else if (block.blockId === 'product/grid') {\n          var _block$content3, _block$content4, _block$content5;\n          return 'Product Grid: ' + [(_block$content3 = block.content) === null || _block$content3 === void 0 ? void 0 : _block$content3.prod1_name, (_block$content4 = block.content) === null || _block$content4 === void 0 ? void 0 : _block$content4.prod2_name, (_block$content5 = block.content) === null || _block$content5 === void 0 ? void 0 : _block$content5.prod3_name].filter(Boolean).join(', ');\n        } else if (block.blockId === 'cta/button') {\n          var _block$content6;\n          return ((_block$content6 = block.content) === null || _block$content6 === void 0 ? void 0 : _block$content6.buttonText) || 'Button';\n        } else if (block.blockId === 'footer/standard') {\n          var _block$content7;\n          return (_block$content7 = block.content) !== null && _block$content7 !== void 0 && _block$content7.companyName ? `Footer: ${block.content.companyName}` : 'Standard Footer';\n        } else {\n          var _block$content8, _block$content9, _block$content9$body, _block$content10;\n          // Fallback to original logic\n          return ((_block$content8 = block.content) === null || _block$content8 === void 0 ? void 0 : _block$content8.headline) || ((_block$content9 = block.content) === null || _block$content9 === void 0 ? void 0 : (_block$content9$body = _block$content9.body) === null || _block$content9$body === void 0 ? void 0 : _block$content9$body.substring(0, 50)) + ((_block$content10 = block.content) !== null && _block$content10 !== void 0 && _block$content10.body && block.content.body.length > 50 ? '...' : '') || block.name;\n        }\n      })(), block.thumbnail && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: block.thumbnail,\n        alt: `${block.name} thumbnail`,\n        className: \"mx-auto h-12 mt-2 opacity-75 object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 904,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 861,\n    columnNumber: 5\n  }, this);\n};\n_s2(DraggableBlock, \"Bm9lse0dSUrMRW3+tCDIsw+uDQ8=\", false, function () {\n  return [useDrop, useDrag];\n});\n_c2 = DraggableBlock;\nexport { EmailEditor };\nexport default EmailEditor;\nvar _c, _c2;\n$RefreshReg$(_c, \"EmailEditor\");\n$RefreshReg$(_c2, \"DraggableBlock\");", "map": {"version": 3, "names": ["React", "useCallback", "useEffect", "useRef", "useState", "mjml2html", "useDrag", "useDrop", "useNavigate", "useParams", "api", "BlockEditor", "BlockLibrary", "EmailPreview", "SaveTemplateModal", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "API_URL", "process", "env", "REACT_APP_API_URL", "ItemTypes", "BLOCK", "LIBRARY_BLOCK", "templateCache", "Map", "CACHE_TTL", "generate<PERSON>ache<PERSON>ey", "blocks", "map", "block", "instanceId", "content", "blockId", "_id", "JSON", "stringify", "join", "clearStaleCache", "now", "Date", "for<PERSON>ach", "entry", "key", "timestamp", "delete", "EmailEditor", "_s", "_editorBlocks$selecte", "templateId", "navigate", "template", "setTemplate", "<PERSON><PERSON><PERSON><PERSON>", "setEditorBlocks", "availableBlocks", "setAvailableBlocks", "selectedBlockIndex", "setSelectedBlockIndex", "previewHtml", "setPreviewHtml", "previewMode", "setPreviewMode", "isSaving", "setIsSaving", "showSaveModal", "setShowSaveModal", "isLoading", "setIsLoading", "error", "setError", "saveError", "setSaveError", "userPreferences", "setUserPreferences", "templateName", "setTemplateName", "previewIframeRef", "editorCanvasRef", "isMounted", "fetchData", "console", "log", "blocksPromise", "get", "prefsPromise", "templatePromise", "Promise", "resolve", "blocksResponse", "prefsResponse", "templateResponse", "all", "fetchedAvailableBlocks", "data", "length", "preferences", "templateData", "brandColors", "defaultFonts", "blockIds", "populatedBlocks", "id", "index", "_templateData$blocks", "foundBlockDef", "find", "b", "warn", "instanceBlockData", "instanceContent", "Math", "random", "toString", "substring", "blockForState", "filter", "_templateData$blockId", "hasBlockIds", "blockIdsLength", "hasFetchedBlocks", "fetchedBlocksLength", "subject", "userId", "err", "errorMsg", "response", "_err$config", "_err$config$url", "config", "url", "includes", "status", "_err$response$data", "_err$response$data2", "message", "generateMjmlFromBlocks", "blockList", "_template$brandColors", "_template$defaultFont", "cache<PERSON>ey", "has", "cached", "mjml", "chunkSize", "mjml<PERSON>ody<PERSON><PERSON>nt", "i", "chunk", "slice", "chunkContent", "blockMjml", "Object", "keys", "value", "placeholder", "undefined", "Array", "isArray", "linksHtml", "link", "name", "replace", "iconsHtml", "icon", "_icon$platform", "platform", "toLowerCase", "stringValue", "String", "replaceAll", "camelCasePlaceholder", "g", "toUpperCase", "snakeCasePlaceholder", "letter", "primaryColor", "primary", "backgroundColor", "background", "textColor", "text", "headingFont", "heading", "bodyFont", "body", "fullMjml", "set", "html", "generatePreview", "mjmlString", "window", "Worker", "workerTimeout", "setTimeout", "errors", "validationLevel", "fallbackErr", "debounceTimeout", "clearTimeout", "moveBlock", "dragIndex", "hoverIndex", "prevBlocks", "newBlocks", "<PERSON><PERSON><PERSON>", "splice", "s", "dropBlockFromLibrary", "blockWithRequiredProps", "parse", "removeBlock", "_", "prevIndex", "updateBlockContent", "updatedContent", "handleSave", "templateNameToSave", "isPublicStatus", "conversionErrors", "Boolean", "payload", "tags", "isPublic", "description", "aiPrompt", "isAiGenerated", "post", "savedTemplate", "_savedTemplate$blocks", "_savedTemplate$blocks2", "_err$response", "_err$response$data3", "errorMessage", "isOverCanvas", "drop", "accept", "item", "monitor", "didDrop", "editorD<PERSON>", "current", "clientOffset", "getClientOffset", "findDropIndex", "y", "type", "collect", "isOver", "shallow", "clientY", "container", "containerRect", "getBoundingClientRect", "offsetY", "top", "scrollTop", "calculatedIndex", "children", "from", "child", "classList", "contains", "childTop", "offsetTop", "childHeight", "offsetHeight", "middleY", "clear", "cleanupUnusedResources", "performance", "memoryInfo", "memory", "usedJSHeapSize", "jsHeapSizeLimit", "addEventListener", "removeEventListener", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSave", "prev", "onPreviewModeChange", "onAddBlock", "ref", "DraggableBlock", "isSelected", "onClick", "mode", "iframeRef", "title", "onUpdate", "initialName", "onCancel", "_c", "_s2", "handlerId", "hover", "hoverBoundingRect", "hoverMiddleY", "bottom", "hoverClientY", "getHandlerId", "isDragging", "drag", "style", "opacity", "category", "e", "stopPropagation", "xmlns", "fill", "viewBox", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "_block$content", "heroHeadline", "_block$content2", "headline", "_block$content3", "_block$content4", "_block$content5", "prod1_name", "prod2_name", "prod3_name", "_block$content6", "buttonText", "_block$content7", "companyName", "_block$content8", "_block$content9", "_block$content9$body", "_block$content10", "thumbnail", "src", "alt", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/EmailEditor.tsx"], "sourcesContent": ["/**\n * Enhanced EmailEditor component for Driftly Email Generator\n * Provides advanced drag-and-drop interface for template creation and editing\n * Features: Real-time preview, responsive design, advanced block library, undo/redo\n */\n\nimport React, {\n  useCallback,\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\n\nimport mjml2html from 'mjml-browser';\nimport {\n  DropTargetMonitor,\n  useDrag,\n  useDrop,\n} from 'react-dnd';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\n\nimport api from '../services/api'; // Corrected import path\n// Types - Adjust path as needed\nimport {\n  Block,\n  BlockContent,\n  BlockListResponse,\n  Template,\n  TemplateDetailResponse,\n  TemplateSaveResponse,\n  UserPreference,\n} from '../types/editor';\nimport BlockEditor from './BlockEditor';\n// Components - Adjust paths as needed\nimport BlockLibrary from './BlockLibrary';\nimport EmailPreview from './EmailPreview';\nimport SaveTemplateModal from './SaveTemplateModal';\nimport Toolbar from './Toolbar';\n\n// Styles - Ensure this is imported in your main application entry point\n// e.g., import '../styles/editor.css';\n\nconst API_URL = process.env.REACT_APP_API_URL || '/api'; // Use environment variable\n\n// Define Drag Item Types\nconst ItemTypes = {\n  BLOCK: 'block',\n  LIBRARY_BLOCK: 'library_block'\n};\n\ninterface EditorDragItem {\n  index: number;\n  type: typeof ItemTypes.BLOCK;\n  id: string; // Unique instance ID of the block being dragged\n}\n\ninterface LibraryDragItem {\n  block: Block; // The block definition from the library\n  type: typeof ItemTypes.LIBRARY_BLOCK;\n}\n\n// Near the top of the file, add a template cache mechanism:\n// Cache for storing generated HTML previews\nconst templateCache = new Map<string, {\n  html: string;\n  timestamp: number;\n  mjml: string;\n}>();\n\n// Cache TTL (Time To Live) in milliseconds - 5 minutes\nconst CACHE_TTL = 5 * 60 * 1000;\n\n// Add this function before the EmailEditor component definition\n// Function to generate a cache key based on blocks\nconst generateCacheKey = (blocks: Block[]): string => {\n  return blocks.map(block => {\n    const { instanceId, content } = block;\n    // Include only essential data in cache key\n    return `${block.blockId || block._id}:${instanceId}:${JSON.stringify(content)}`;\n  }).join('|');\n};\n\n// Function to clear stale cache entries\nconst clearStaleCache = () => {\n  const now = Date.now();\n  templateCache.forEach((entry, key) => {\n    if (now - entry.timestamp > CACHE_TTL) {\n      templateCache.delete(key);\n    }\n  });\n};\n\n// --- EmailEditor Component ---\n\nconst EmailEditor: React.FC = () => {\n  const { templateId } = useParams<{ templateId?: string }>();\n  const navigate = useNavigate();\n\n  // State\n  const [template, setTemplate] = useState<Partial<Template>>({});\n  const [editorBlocks, setEditorBlocks] = useState<Block[]>([]);\n  const [availableBlocks, setAvailableBlocks] = useState<Block[]>([]);\n  const [selectedBlockIndex, setSelectedBlockIndex] = useState<number | null>(null);\n  const [previewHtml, setPreviewHtml] = useState<string>('');\n  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');\n  const [isSaving, setIsSaving] = useState<boolean>(false);\n  const [showSaveModal, setShowSaveModal] = useState<boolean>(false);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [saveError, setSaveError] = useState<string | null>(null);\n  const [userPreferences, setUserPreferences] = useState<UserPreference | null>(null);\n  const [templateName, setTemplateName] = useState('');\n\n  // Refs\n  const previewIframeRef = useRef<HTMLIFrameElement>(null);\n  const editorCanvasRef = useRef<HTMLDivElement>(null);\n\n  // --- Data Fetching --- //\n  useEffect(() => {\n    let isMounted = true;\n    const fetchData = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Fetch available blocks first (always needed)\n        console.log(`Fetching blocks from ${API_URL}/blocks`);\n        // Use the shared api instance with BlockListResponse type\n        const blocksPromise = api.get<BlockListResponse>('/blocks');\n\n        // Fetch user preferences to potentially apply brand colors/fonts later\n        console.log(`Fetching user preferences from ${API_URL}/user/preferences`);\n         // Use the shared api instance with correct inline type\n        const prefsPromise = api.get<{ preferences: UserPreference }>(`/user/preferences`);\n\n        // Fetch template data if ID exists\n        const templatePromise = templateId\n          // Use the shared api instance with TemplateDetailResponse type\n          ? api.get<TemplateDetailResponse>(`/templates/${templateId}`)\n          : Promise.resolve(null);\n\n        const [blocksResponse, prefsResponse, templateResponse] = await Promise.all([\n          blocksPromise,\n          prefsPromise,\n          templatePromise\n        ]);\n\n        // Access blocks correctly from response.data.data\n        const fetchedAvailableBlocks = blocksResponse.data.data || [];\n        console.log(`Fetched ${fetchedAvailableBlocks.length} available blocks.`);\n        setAvailableBlocks(fetchedAvailableBlocks);\n\n        // Process user preferences\n        const userPreferences = prefsResponse.data.preferences; // Store preferences\n        // TODO: Apply user preferences (e.g., brand colors, fonts)\n\n        if (templateResponse && templateResponse.data.template) {\n          const templateData = templateResponse.data.template; // Correct path now\n          console.log('[EmailEditor] Fetched template data:', JSON.stringify(templateData, null, 2)); // Log fetched data\n\n          // Apply brand colors/fonts from preferences if not set on template\n          setTemplate({\n              ...templateData,\n              brandColors: templateData.brandColors || userPreferences?.brandColors,\n              defaultFonts: templateData.defaultFonts || userPreferences?.defaultFonts,\n          });\n\n          // Populate editor blocks based on blockIds and fetched definitions\n          console.log('[EmailEditor] Attempting to populate blocks. Available block defs:', fetchedAvailableBlocks.length);\n          console.log('[EmailEditor] Template blockIds:', templateData.blockIds);\n          console.log('[EmailEditor] Template blocks data:', templateData.blocks);\n\n          if (templateData.blockIds && templateData.blockIds.length > 0 && fetchedAvailableBlocks.length > 0) {\n            console.log('[EmailEditor] Conditions met, proceeding to map blockIds.');\n            const populatedBlocks = templateData.blockIds.map((id: string, index: number) => {\n              console.log(`[EmailEditor] Mapping blockId: ${id} at index: ${index}`);\n              // Match ID from templateData.blockIds with fetchedAvailableBlocks\n              const foundBlockDef = fetchedAvailableBlocks.find((b: Block) => b.blockId === id || b._id === id); // Check both blockId and _id\n              console.log(`[EmailEditor] ... Definition found for ${id}?`, foundBlockDef ? 'Yes' : 'No');\n\n              if (!foundBlockDef) {\n                console.warn(`[EmailEditor] Block definition not found for available block matching ID: ${id}. Skipping.`); // Refined warning\n                return null;\n              }\n\n              // Get content for this specific instance from templateData.blocks\n              const instanceBlockData = templateData.blocks?.[index]; // Get the block data saved for this instance\n              const instanceContent = instanceBlockData?.content || foundBlockDef.content || {}; // Use instance content, fallback to definition's default\n              console.log(`[EmailEditor] ... Instance content for ${id}:`, instanceContent);\n\n              const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`; // Unique key for React\n\n              // Return the combined block object for the editor state\n              const blockForState = {\n                  ...foundBlockDef, // Base definition (MJML, name, category etc.)\n                  content: { ...instanceContent }, // Specific content for this instance\n                  instanceId // Unique ID for this instance in the editor\n              };\n              console.log(`[EmailEditor] ... Created block object for state for ${id}:`, blockForState);\n              return blockForState;\n            }).filter(b => b !== null);\n\n            console.log(\"[EmailEditor] Populated blocks array before setting state:\", populatedBlocks);\n            setEditorBlocks(populatedBlocks as Block[]);\n          } else {\n            console.log(\"[EmailEditor] Condition for populating blocks NOT met:\", {\n                hasBlockIds: !!templateData.blockIds,\n                blockIdsLength: templateData.blockIds?.length,\n                hasFetchedBlocks: !!fetchedAvailableBlocks,\n                fetchedBlocksLength: fetchedAvailableBlocks?.length\n            });\n            setEditorBlocks([]); // Sets blocks to empty\n          }\n        } else {\n          // New template: initialize with preferences\n          console.log('Initializing new template state with preferences.');\n          setTemplate({\n            templateName: 'Untitled Template',\n            subject: 'Your Subject Here',\n            brandColors: userPreferences?.brandColors,\n            defaultFonts: userPreferences?.defaultFonts,\n            // Initialize other necessary fields if needed\n            userId: userPreferences?.userId // Important for saving later\n          });\n          setEditorBlocks([]);\n        }\n      } catch (err: any) {\n        console.error('Error loading editor data:', err);\n        let errorMsg = 'Failed to load editor data.'; // Default message\n\n        if (err.response) {\n          // Check if it's the template fetch that failed with 404\n          if (err.config?.url?.includes(`/templates/${templateId}`) && err.response.status === 404) {\n            errorMsg = `Template with ID ${templateId} not found. It may have been deleted.`;\n            // Optionally, redirect the user or clear the template state\n            // navigate('/templates'); // Example redirect\n            setTemplate({}); // Clear any partial template data\n            setEditorBlocks([]);\n          } else {\n            // Use the error message from the response if available, otherwise use the generic message\n            errorMsg = err.response.data?.error || err.response.data?.message || err.message || errorMsg;\n          }\n        } else {\n          // Network error or other issue\n          errorMsg = err.message || errorMsg;\n        }\n\n        setError(errorMsg);\n        // Fallback logic: Check if availableBlocks is empty (meaning block fetch might have also failed)\n        if (availableBlocks.length === 0) {\n            console.warn(\"Falling back to empty block list as blocks couldn't be fetched or list is empty.\");\n            setAvailableBlocks([]); // Ensure it's an empty array if blocks failed\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchData();\n\n    return () => {\n      isMounted = false;\n    };\n  // eslint-disable-next-line\n  }, [templateId]); // Rerun only when templateId changes\n\n  // --- MJML Generation & Preview Update --- //\n  const generateMjmlFromBlocks = useCallback((blockList: Block[]): string => {\n    // If there are no blocks, return early with an empty string\n    if (!blockList.length) return '';\n\n    // Generate a cache key for this block configuration\n    const cacheKey = generateCacheKey(blockList);\n\n    // Check if we have a cached version\n    if (templateCache.has(cacheKey)) {\n      const cached = templateCache.get(cacheKey);\n      if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {\n        console.log(\"[EmailEditor] Using cached MJML\");\n        return cached.mjml;\n      }\n    }\n\n    // Periodically clean up old cache entries\n    clearStaleCache();\n\n    // Process blocks in chunks if there are many\n    const chunkSize = 5;\n    let mjmlBodyContent = '';\n\n    for (let i = 0; i < blockList.length; i += chunkSize) {\n      const chunk = blockList.slice(i, i + chunkSize);\n      const chunkContent = chunk.map(block => {\n        let blockMjml = block.mjml || '';\n        const content = block.content || {};\n\n        // More robust replacement logic\n        Object.keys(content).forEach(key => {\n          const value = content[key];\n          const placeholder = `{{${key}}}`; // Standard placeholder\n\n          // Only process if value exists\n          if (value !== undefined && value !== null) {\n            // Handle arrays specifically (e.g., nav links, social icons)\n            if (Array.isArray(value)) {\n              // Existing array handling code...\n              if (key === 'nav_links' || key === 'navLinks') {\n                const linksHtml = value.map((link: any) =>\n                  `<mj-navbar-link href=\"${link.url || '#'}\" color=\"#1E3A8A\">${link.name || 'Link'}</mj-navbar-link>`\n                ).join('\\n');\n                blockMjml = blockMjml.replace('{{navLinksArea}}', linksHtml);\n              } else if (key === 'social_icons' || key === 'socialLinks') {\n                const iconsHtml = value.map((icon: any) =>\n                  `<mj-social-element name=\"${icon.platform?.toLowerCase() || 'share'}\" href=\"${icon.url || '#'}\"></mj-social-element>`\n                ).join('\\n');\n                blockMjml = blockMjml.replace('{{socialIconsArea}}', iconsHtml);\n              }\n            } else {\n              // More efficient single replacement (no array of placeholders)\n              const stringValue = String(value);\n              blockMjml = blockMjml.replaceAll(placeholder, stringValue);\n\n              // Only try alternative formats if there's a good chance they exist\n              if (blockMjml.includes('{{')) {\n                // Convert camelCase to snake_case for placeholder check if needed\n                const camelCasePlaceholder = `{{${key.replace(/_([a-z])/g, g => g[1].toUpperCase())}}}`;\n                const snakeCasePlaceholder = `{{${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)}}}`;\n\n                blockMjml = blockMjml.replaceAll(camelCasePlaceholder, stringValue);\n                blockMjml = blockMjml.replaceAll(snakeCasePlaceholder, stringValue);\n              }\n            }\n          }\n        });\n\n        // More efficient placeholder cleanup - only if needed\n        if (blockMjml.includes('{{')) {\n          blockMjml = blockMjml.replace(/\\{\\{[\\w.-]+\\}\\}/g, '');\n        }\n\n        return blockMjml;\n      }).join('\\n');\n\n      mjmlBodyContent += chunkContent;\n    }\n\n    // Use template state for colors/fonts, falling back to defaults\n    const brandColors = template?.brandColors ?? {};\n    const defaultFonts = template?.defaultFonts ?? {};\n    const primaryColor = brandColors.primary || '#4F46E5';\n    const backgroundColor = brandColors.background || '#f3f4f6';\n    const textColor = brandColors.text || '#111827';\n    const headingFont = defaultFonts.heading || 'Arial, sans-serif';\n    const bodyFont = defaultFonts.body || 'Arial, sans-serif';\n\n    // Construct the full MJML document\n    const fullMjml = `\n<mjml>\n  <mj-head>\n    <mj-title>${template?.subject || 'Email Template'}</mj-title>\n    <mj-font name=\"Roboto\" href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700\" />\n    <mj-attributes>\n      <mj-all padding=\"0px\" font-family=\"${bodyFont}\" />\n      <mj-text padding=\"10px 25px\" font-size=\"14px\" line-height=\"1.5\" color=\"${textColor}\" />\n      <mj-section padding=\"10px 0\" />\n      <mj-column padding=\"5px\" />\n      <mj-button background-color=\"${primaryColor}\" color=\"#ffffff\" font-weight=\"bold\" border-radius=\"4px\" padding=\"10px 20px\" />\n      <mj-image padding=\"0px\" />\n    </mj-attributes>\n    <mj-style inline=\"inline\">\n      /* Inline styles */\n      a { color: ${primaryColor} !important; text-decoration: none !important; }\n    </mj-style>\n     <mj-style>\n      /* Embedded styles */\n      .hover-link:hover { text-decoration: underline !important; }\n    </mj-style>\n  </mj-head>\n  <mj-body background-color=\"${backgroundColor}\">\n    ${mjmlBodyContent}\n  </mj-body>\n</mjml>`;\n\n    // Store in cache\n    templateCache.set(cacheKey, {\n      mjml: fullMjml,\n      html: '', // Will be populated on first HTML conversion\n      timestamp: Date.now()\n    });\n\n    return fullMjml;\n  }, [template]);\n\n  useEffect(() => {\n    const generatePreview = () => {\n      if (!editorBlocks || editorBlocks.length === 0) {\n        setPreviewHtml('<div style=\"display: flex; justify-content: center; align-items: center; height: 100%; color: grey; padding: 20px; text-align: center;\">Drag blocks here to build your email</div>');\n        return;\n      }\n\n      try {\n        // Generate cache key\n        const cacheKey = generateCacheKey(editorBlocks);\n\n        // Check for valid cached HTML\n        if (templateCache.has(cacheKey)) {\n          const cached = templateCache.get(cacheKey);\n          if (cached && cached.html && (Date.now() - cached.timestamp) < CACHE_TTL) {\n            console.log(\"[EmailEditor Preview] Using cached HTML\");\n            setPreviewHtml(cached.html);\n            return;\n          }\n        }\n\n        // If we get here, generate a new preview\n        const mjmlString = generateMjmlFromBlocks(editorBlocks);\n        console.log(\"[EmailEditor Preview] Generating fresh HTML preview\");\n\n        // Use a web worker for MJML conversion if possible\n        if (window.Worker) {\n          // Note: You would need to create a separate mjml-worker.js file\n          // This is just demonstrating the concept\n          const workerTimeout = setTimeout(() => {\n            // Fallback if worker takes too long\n            try {\n              const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });\n              setPreviewHtml(html);\n              // Update cache\n              const cacheKey = generateCacheKey(editorBlocks);\n              if (templateCache.has(cacheKey)) {\n                const entry = templateCache.get(cacheKey);\n                if (entry) {\n                  entry.html = html;\n                  entry.timestamp = Date.now();\n                }\n              }\n            } catch (fallbackErr: any) {\n              console.error('[EmailEditor Preview] Error in fallback conversion:', fallbackErr);\n              setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${fallbackErr?.message || 'Unknown error'}</div>`);\n            }\n          }, 1000); // 1 second timeout\n\n          // This is just conceptual - actual implementation would need the worker file\n          // const worker = new Worker('/mjml-worker.js');\n          // worker.postMessage(mjmlString);\n          // worker.onmessage = (e) => {\n          //   clearTimeout(workerTimeout);\n          //   const { html, errors } = e.data;\n          //   setPreviewHtml(html);\n          //   // Update cache\n          //   updateHtmlCache(cacheKey, html);\n          // };\n        } else {\n          // Direct conversion when Web Workers aren't available\n          const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });\n          if (errors && errors.length > 0) {\n            console.warn('[EmailEditor Preview] MJML Validation:', errors.length, 'issues');\n          }\n          setPreviewHtml(html);\n\n          // Update cache\n          if (templateCache.has(cacheKey)) {\n            const entry = templateCache.get(cacheKey);\n            if (entry) {\n              entry.html = html;\n              entry.timestamp = Date.now();\n            }\n          }\n        }\n      } catch (err: any) {\n        console.error('[EmailEditor Preview] Error generating preview:', err);\n        setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${err?.message || 'Unknown error'}</div>`);\n      }\n    };\n\n    // Debounce the preview generation to avoid too many updates\n    const debounceTimeout = setTimeout(generatePreview, 300);\n    return () => clearTimeout(debounceTimeout);\n  }, [editorBlocks, generateMjmlFromBlocks]);\n\n  // --- DND Callbacks --- //\n  const moveBlock = useCallback((dragIndex: number, hoverIndex: number) => {\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = [...prevBlocks];\n          const [draggedBlock] = newBlocks.splice(dragIndex, 1);\n          newBlocks.splice(hoverIndex, 0, draggedBlock);\n          return newBlocks;\n      });\n      // Adjust selected index\n      if (selectedBlockIndex === dragIndex) {\n          setSelectedBlockIndex(hoverIndex);\n      } else if (selectedBlockIndex !== null) {\n          if (dragIndex < hoverIndex && selectedBlockIndex > dragIndex && selectedBlockIndex <= hoverIndex) {\n              setSelectedBlockIndex(s => (s !== null ? s - 1 : null));\n          } else if (dragIndex > hoverIndex && selectedBlockIndex >= hoverIndex && selectedBlockIndex < dragIndex) {\n              setSelectedBlockIndex(s => (s !== null ? s + 1 : null));\n          }\n      }\n  }, [selectedBlockIndex]);\n\n  const dropBlockFromLibrary = useCallback((block: any, index: number) => {\n      // Cast the block to ensure it has all required properties\n      const blockWithRequiredProps = {\n          ...JSON.parse(JSON.stringify(block)),\n          blockId: block.blockId || block._id || `block-${Date.now()}`, // Ensure blockId is never undefined\n          content: block.content ? JSON.parse(JSON.stringify(block.content)) : {},\n          instanceId: `${block.blockId || block._id || 'new'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n      };\n\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = [...prevBlocks];\n          newBlocks.splice(index, 0, blockWithRequiredProps as Block);\n          return newBlocks;\n      });\n      setSelectedBlockIndex(index); // Select the newly dropped block\n  }, []);\n\n  const removeBlock = useCallback((index: number) => {\n      setEditorBlocks(prevBlocks => prevBlocks.filter((_, i) => i !== index));\n      if (selectedBlockIndex === index) {\n          setSelectedBlockIndex(null);\n      } else if (selectedBlockIndex !== null && selectedBlockIndex > index) {\n          setSelectedBlockIndex(prevIndex => (prevIndex !== null ? prevIndex - 1 : null));\n      }\n  }, [selectedBlockIndex]);\n\n  const updateBlockContent = useCallback((index: number, updatedContent: Partial<BlockContent>) => {\n      setEditorBlocks(prevBlocks =>\n          prevBlocks.map((block, i) =>\n              i === index\n                  ? { ...block, content: { ...(block.content || {}), ...updatedContent } }\n                  : block\n          )\n      );\n  }, []);\n\n  // --- Save Handler --- //\n  const handleSave = async (templateNameToSave: string, isPublicStatus: boolean = false) => {\n    setIsSaving(true);\n    setSaveError(null);\n    try {\n      const mjml = generateMjmlFromBlocks(editorBlocks);\n      const { html, errors: conversionErrors } = mjml2html(mjml);\n      if (conversionErrors && conversionErrors.length > 0) {\n        console.warn('MJML conversion errors detected during save:', conversionErrors);\n        // Consider not saving if critical errors occurred\n      }\n\n      const blockIds = editorBlocks\n        .map(b => String(b.blockId || b._id)) // Ensure string ID\n        .filter(Boolean); // Filter out any potential undefined/null\n\n      const payload: Partial<Template> & { templateId?: string; blockIds: string[] } = {\n        templateId: template?._id,\n        templateName: templateNameToSave,\n        mjml,\n        html,\n        blockIds,\n        subject: template?.subject || 'Untitled Template',\n        tags: template?.tags || [],\n        isPublic: isPublicStatus,\n        description: template?.description || '',\n        aiPrompt: template?.aiPrompt || '',\n        isAiGenerated: template?.isAiGenerated || false\n      };\n\n      // Add userId if it's a new template - CRITICAL: Ensure req.user is populated in backend\n      // This assumes the backend will get the userId from the authenticated request (authenticateJWT)\n      // if (!payload.templateId) {\n      //   payload.userId = template?.userId; // Might be set from prefs initially\n      // }\n\n      const response = await api.post<TemplateSaveResponse>('/templates/save', payload);\n      const savedTemplate = response.data.template;\n      setTemplate(savedTemplate);\n\n      // Re-sync editor blocks if necessary (e.g., if backend modifies content/IDs)\n      // This part needs careful implementation if backend modifies block content on save\n      if (savedTemplate.blockIds && availableBlocks.length > 0) {\n         const populatedBlocks = savedTemplate.blockIds.map((id, index) => {\n            const foundBlockDef = availableBlocks.find(b => b.blockId === id || b._id === id);\n            const instanceContent = savedTemplate.blocks?.[index]?.content || foundBlockDef?.content || {};\n            const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`;\n            return foundBlockDef ? { ...foundBlockDef, content: { ...instanceContent }, instanceId } : null;\n         }).filter((b) => b !== null) as Block[];\n         setEditorBlocks(populatedBlocks);\n      }\n\n      setShowSaveModal(false);\n      console.log('Template saved successfully!');\n      if (!templateId && savedTemplate._id) {\n        navigate(`/email-editor/${savedTemplate._id}`, { replace: true });\n      }\n      return savedTemplate;\n    } catch (err: any) {\n      console.error('Error saving template:', err);\n      const errorMessage = err.response?.data?.error || err.message || 'Failed to save template.';\n      setSaveError(errorMessage);\n      return null;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // --- Drop Target for Canvas --- //\n  const [{ isOverCanvas }, drop] = useDrop(() => ({\n      accept: [ItemTypes.BLOCK, ItemTypes.LIBRARY_BLOCK],\n      drop: (item: EditorDragItem | LibraryDragItem, monitor) => {\n          if (monitor.didDrop()) return;\n\n          const editorDiv = editorCanvasRef.current;\n          const clientOffset = monitor.getClientOffset();\n          if (!clientOffset || !editorDiv) return;\n\n          const hoverIndex = findDropIndex(clientOffset.y, editorDiv);\n\n          if (item.type === ItemTypes.LIBRARY_BLOCK) {\n            dropBlockFromLibrary((item as LibraryDragItem).block, hoverIndex);\n          }\n          // Reordering is handled by DraggableBlock's hover\n      },\n      collect: monitor => ({\n        isOverCanvas: monitor.isOver({ shallow: true }),\n      }),\n  }), [editorBlocks, moveBlock, dropBlockFromLibrary]); // Ensure correct dependencies\n\n  // Helper function to find the correct drop index\n  const findDropIndex = (clientY: number | undefined, container: HTMLDivElement): number => {\n      if (clientY === undefined) return editorBlocks.length;\n\n      const containerRect = container.getBoundingClientRect();\n      const offsetY = clientY - containerRect.top + container.scrollTop;\n\n      let calculatedIndex = editorBlocks.length;\n      const children = Array.from(container.children) as HTMLElement[];\n\n      for (let i = 0; i < children.length; i++) {\n          const child = children[i];\n          if (!child.classList || !child.classList.contains('draggable-block')) continue;\n\n          const childTop = child.offsetTop;\n          const childHeight = child.offsetHeight;\n          const middleY = childTop + childHeight / 2;\n\n          if (offsetY < middleY) {\n              calculatedIndex = i;\n              break;\n          }\n      }\n      return calculatedIndex;\n  };\n\n  // Cleanup cache when component unmounts\n  useEffect(() => {\n    return () => {\n      // Clear the entire cache when this editor instance is unmounted\n      templateCache.clear();\n    };\n  }, []);\n\n  // Add a cleanup function for memory management\n  const cleanupUnusedResources = useCallback(() => {\n    // Clear any unused thumbnails from memory if browser gets low on memory\n    if ('memory' in performance) {\n      const memoryInfo = (performance as any).memory;\n      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {\n        console.log('[EmailEditor] Memory pressure detected, cleaning up resources');\n        // Force a garbage collection if possible\n        clearStaleCache();\n        // You could also unload any non-visible thumbnails or other large objects\n      }\n    }\n  }, []);\n\n  // Listen for low memory events\n  useEffect(() => {\n    if ('onmemorywarning' in window) {\n      (window as any).addEventListener('memorywarning', cleanupUnusedResources);\n      return () => {\n        (window as any).removeEventListener('memorywarning', cleanupUnusedResources);\n      };\n    }\n  }, [cleanupUnusedResources]);\n\n  // --- Render Logic --- //\n  if (isLoading) {\n    return <div className=\"flex justify-center items-center h-screen text-gray-600 text-lg\">Loading Editor...</div>;\n  }\n\n  // Critical error on initial load\n  if (error && !template) {\n    return <div className=\"p-4 m-4 text-red-700 bg-red-100 border border-red-400 rounded\">Error loading editor configuration: {error}</div>;\n  }\n\n  return (\n    <div className=\"email-editor flex flex-col h-screen bg-gray-100\">\n      <Toolbar\n        onSave={() => {\n            setTemplate(prev => ({ ...(prev || {}), templateName: prev?.templateName || 'Untitled Template' } as Template));\n            setSaveError(null);\n            setShowSaveModal(true);\n        }}\n        onPreviewModeChange={setPreviewMode}\n        previewMode={previewMode}\n        isSaving={isSaving}\n      />\n\n      {/* Display non-critical error (e.g., failed suggestion) */}\n      {error && templateId &&\n          <div className=\"p-2 text-sm text-red-700 bg-red-100 border-b border-red-300 text-center\">Error: {error}</div>\n      }\n\n      <div className={`editor-content flex flex-1 overflow-hidden`}>\n        {/* Block Library Panel */}\n        <div className=\"block-library-panel w-64 bg-white border-r border-gray-200 flex flex-col overflow-hidden shrink-0\">\n          <h3 className=\"px-4 py-3 text-base font-semibold text-gray-800 border-b border-gray-200 whitespace-nowrap\">Block Library</h3>\n          <BlockLibrary\n            blocks={availableBlocks}\n            onAddBlock={(block) => dropBlockFromLibrary(block, editorBlocks.length)} // Click adds to end\n          />\n        </div>\n\n        {/* Editor Workspace / Canvas */}\n        <div ref={drop} className={`editor-workspace flex-1 flex flex-col overflow-hidden bg-gray-200 ${isOverCanvas ? 'outline-2 outline-dashed outline-indigo-500' : ''}`}>\n          <div ref={editorCanvasRef} className=\"blocks-container flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto\">\n            {editorBlocks.length === 0 ? (\n              <div className=\"empty-blocks flex items-center justify-center h-full text-gray-500 text-center p-8 border-2 border-dashed border-gray-300 rounded-lg min-h-[200px]\">\n                <p>Drag blocks from the library here<br/>or click a block in the library to add it.</p>\n              </div>\n            ) : (\n              editorBlocks.map((block, index) => (\n                <DraggableBlock\n                  key={block.instanceId} // Use unique instanceId\n                  block={block}\n                  index={index}\n                  moveBlock={moveBlock}\n                  removeBlock={removeBlock}\n                  isSelected={selectedBlockIndex === index}\n                  onClick={() => setSelectedBlockIndex(index)}\n                />\n              ))\n            )}\n          </div>\n        </div>\n\n        {/* Right Panel (Toggles Preview/Editor) */}\n        <div className=\"right-panel w-80 md:w-96 lg:w-[500px] border-l border-gray-300 flex flex-col shrink-0 bg-white shadow-lg\">\n            {(selectedBlockIndex === null || editorBlocks.length === 0) ? (\n                // Show Preview when no block is selected OR if editor is empty\n                <div className=\"preview-panel flex-1 flex flex-col overflow-hidden\">\n                    <h3 className=\"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200\">Preview</h3>\n                    <EmailPreview\n                        html={previewHtml}\n                        mode={previewMode}\n                        iframeRef={previewIframeRef}\n                    />\n                </div>\n            ) : (\n                // Show Block Editor when a block is selected\n                <div className=\"block-editor-panel flex-1 flex flex-col overflow-hidden\">\n                    <h3 className=\"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200 flex justify-between items-center\">\n                      <span>Edit: {editorBlocks[selectedBlockIndex]?.name || 'Block'}</span>\n                      <button\n                        onClick={() => setSelectedBlockIndex(null)}\n                        className=\"text-sm text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100\"\n                        title=\"Close Editor\"\n                      >\n                        ✕\n                      </button>\n                    </h3>\n                    <div className=\"flex-1 overflow-y-auto\">\n                        {editorBlocks[selectedBlockIndex] && ( // Check block exists before rendering\n                            <BlockEditor\n                                block={editorBlocks[selectedBlockIndex]}\n                                onUpdate={(content) => {\n                                    if (selectedBlockIndex !== null) {\n                                        updateBlockContent(selectedBlockIndex, content);\n                                    }\n                                }}\n                            />\n                        )}\n                    </div>\n                </div>\n            )}\n        </div>\n\n      </div>\n\n      {/* Save Modal */}\n      {showSaveModal && (\n        <SaveTemplateModal\n          initialName={template?.templateName || 'Untitled Template'}\n          onSave={handleSave}\n          onCancel={() => { setShowSaveModal(false); setSaveError(null); }}\n          isSaving={isSaving}\n          error={saveError} // Pass save error to modal\n        />\n      )}\n    </div>\n  );\n};\n\n// Draggable Block Component (Internal to EmailEditor)\ninterface DraggableBlockProps {\n  block: Block;\n  index: number;\n  moveBlock: (dragIndex: number, hoverIndex: number) => void;\n  removeBlock: (index: number) => void;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nconst DraggableBlock: React.FC<DraggableBlockProps> = ({\n  block,\n  index,\n  moveBlock,\n  removeBlock,\n  isSelected,\n  onClick\n}) => {\n  const ref = useRef<HTMLDivElement>(null);\n\n  const [{ handlerId }, drop] = useDrop<EditorDragItem, void, { handlerId: string | symbol | null }>(() => ({\n    accept: ItemTypes.BLOCK,\n    hover: (item: EditorDragItem, monitor: DropTargetMonitor<EditorDragItem, void>) => {\n      if (!ref.current) return;\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) return;\n\n      const hoverBoundingRect = ref.current.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n      const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n\n      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;\n      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;\n\n      moveBlock(dragIndex, hoverIndex);\n      item.index = hoverIndex; // Mutate monitor item for performance\n    },\n    collect: (monitor) => ({\n        handlerId: monitor.getHandlerId(),\n    }),\n  }), [index, moveBlock]); // Dependencies for useDrop hover logic\n\n  const [{ isDragging }, drag] = useDrag(() => ({\n    type: ItemTypes.BLOCK,\n    item: { index, id: block.instanceId, type: ItemTypes.BLOCK }, // Return item as a function\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [index, block.instanceId]); // Dependencies for useDrag\n\n  drag(drop(ref)); // Combine drag and drop refs\n\n  return (\n    <div\n      ref={ref}\n      data-handler-id={handlerId}\n      className={`draggable-block bg-white border rounded-md mb-4 cursor-move shadow-sm ${isDragging ? 'opacity-40 border-blue-500' : 'hover:border-blue-400 hover:shadow-md'} ${isSelected ? 'border-blue-500 ring-2 ring-blue-300 ring-offset-1' : 'border-gray-200'}`} // styles/editor.css\n      onClick={onClick}\n      style={{ opacity: isDragging ? 0.4 : 1 }}\n    >\n      <div className=\"block-header flex items-center justify-between px-3 py-1.5 border-b border-gray-200 bg-gray-50 rounded-t-md text-xs\"> {/* styles/editor.css */}\n        <span className=\"block-type font-medium text-gray-700 truncate pr-2\" title={block.name}>{block.name} <span className=\"text-gray-400\">({block.category})</span></span>\n        <div className=\"block-actions flex items-center space-x-1\"> {/* styles/editor.css */}\n           {/* Duplicate Button Placeholder */}\n           <button\n            type=\"button\"\n            className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\n            onClick={(e) => {\n                e.stopPropagation();\n                console.log(\"Duplicate block:\", index); // TODO: Implement duplicate functionality\n            }}\n            title=\"Duplicate Block\"\n           >\n               {/* Heroicon: duplicate */}\n               <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={2}>\n                   <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n               </svg>\n           </button>\n           {/* Remove Button */}\n          <button\n            type=\"button\"\n            className=\"remove-block p-1 text-gray-400 hover:text-red-600 transition-colors\" // styles/editor.css\n            onClick={(e) => {\n              e.stopPropagation();\n              removeBlock(index);\n            }}\n            title=\"Remove Block\"\n          >\n            {/* Heroicon: x */}\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={2}>\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n      {/* Simplified preview inside the draggable block */}\n      <div className=\"block-content-preview p-3 text-sm text-gray-600 bg-white rounded-b-md min-h-[50px]\">\n        {(() => {\n          // Display appropriate preview based on block type\n          if (block.blockId === 'header/simple-nav') {\n            return 'Navigation Header';\n          } else if (block.blockId === 'layout/hero') {\n            return block.content?.heroHeadline || 'Hero Section';\n          } else if (block.blockId === 'content/headline') {\n            return block.content?.headline || 'Headline';\n          } else if (block.blockId === 'product/grid') {\n            return 'Product Grid: ' + [\n              block.content?.prod1_name,\n              block.content?.prod2_name,\n              block.content?.prod3_name\n            ].filter(Boolean).join(', ');\n          } else if (block.blockId === 'cta/button') {\n            return block.content?.buttonText || 'Button';\n          } else if (block.blockId === 'footer/standard') {\n            return block.content?.companyName ? `Footer: ${block.content.companyName}` : 'Standard Footer';\n          } else {\n            // Fallback to original logic\n            return block.content?.headline ||\n                   block.content?.body?.substring(0, 50) +\n                   (block.content?.body && block.content.body.length > 50 ? '...' : '') ||\n                   block.name;\n          }\n        })()}\n        {block.thumbnail && <img src={block.thumbnail} alt={`${block.name} thumbnail`} className=\"mx-auto h-12 mt-2 opacity-75 object-contain\"/>}\n      </div>\n    </div>\n  );\n};\n\nexport { EmailEditor };\nexport default EmailEditor;"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IACVC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,QAAQ,QACH,OAAO;AAEd,OAAOC,SAAS,MAAM,cAAc;AACpC,SAEEC,OAAO,EACPC,OAAO,QACF,WAAW;AAClB,SACEC,WAAW,EACXC,SAAS,QACJ,kBAAkB;AAEzB,OAAOC,GAAG,MAAM,iBAAiB,CAAC,CAAC;AACnC;;AAUA,OAAOC,WAAW,MAAM,eAAe;AACvC;AACA,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,OAAO,MAAM,WAAW;;AAE/B;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,MAAM,CAAC,CAAC;;AAEzD;AACA,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE;AACjB,CAAC;AAaD;AACA;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAI1B,CAAC;;AAEJ;AACA,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;;AAE/B;AACA;AACA,MAAMC,gBAAgB,GAAIC,MAAe,IAAa;EACpD,OAAOA,MAAM,CAACC,GAAG,CAACC,KAAK,IAAI;IACzB,MAAM;MAAEC,UAAU;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IACrC;IACA,OAAO,GAAGA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAIH,UAAU,IAAII,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,EAAE;EACjF,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;AACd,CAAC;;AAED;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtBf,aAAa,CAACiB,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;IACpC,IAAIJ,GAAG,GAAGG,KAAK,CAACE,SAAS,GAAGlB,SAAS,EAAE;MACrCF,aAAa,CAACqB,MAAM,CAACF,GAAG,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;;AAEA,MAAMG,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAClC,MAAM;IAAEC;EAAW,CAAC,GAAGzC,SAAS,CAA0B,CAAC;EAC3D,MAAM0C,QAAQ,GAAG3C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC4C,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAoB,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAU,EAAE,CAAC;EAC7D,MAAM,CAACoD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrD,QAAQ,CAAU,EAAE,CAAC;EACnE,MAAM,CAACsD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAgB,IAAI,CAAC;EACjF,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAuB,SAAS,CAAC;EAC/E,MAAM,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAU,KAAK,CAAC;EACxD,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAU,IAAI,CAAC;EACzD,MAAM,CAACkE,KAAK,EAAEC,QAAQ,CAAC,GAAGnE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAwB,IAAI,CAAC;EACnF,MAAM,CAACwE,YAAY,EAAEC,eAAe,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM0E,gBAAgB,GAAG3E,MAAM,CAAoB,IAAI,CAAC;EACxD,MAAM4E,eAAe,GAAG5E,MAAM,CAAiB,IAAI,CAAC;;EAEpD;EACAD,SAAS,CAAC,MAAM;IACd,IAAI8E,SAAS,GAAG,IAAI;IACpB,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BZ,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACF;QACAW,OAAO,CAACC,GAAG,CAAC,wBAAwBjE,OAAO,SAAS,CAAC;QACrD;QACA,MAAMkE,aAAa,GAAG1E,GAAG,CAAC2E,GAAG,CAAoB,SAAS,CAAC;;QAE3D;QACAH,OAAO,CAACC,GAAG,CAAC,kCAAkCjE,OAAO,mBAAmB,CAAC;QACxE;QACD,MAAMoE,YAAY,GAAG5E,GAAG,CAAC2E,GAAG,CAAkC,mBAAmB,CAAC;;QAElF;QACA,MAAME,eAAe,GAAGrC;QACtB;QAAA,EACExC,GAAG,CAAC2E,GAAG,CAAyB,cAAcnC,UAAU,EAAE,CAAC,GAC3DsC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;QAEzB,MAAM,CAACC,cAAc,EAAEC,aAAa,EAAEC,gBAAgB,CAAC,GAAG,MAAMJ,OAAO,CAACK,GAAG,CAAC,CAC1ET,aAAa,EACbE,YAAY,EACZC,eAAe,CAChB,CAAC;;QAEF;QACA,MAAMO,sBAAsB,GAAGJ,cAAc,CAACK,IAAI,CAACA,IAAI,IAAI,EAAE;QAC7Db,OAAO,CAACC,GAAG,CAAC,WAAWW,sBAAsB,CAACE,MAAM,oBAAoB,CAAC;QACzEvC,kBAAkB,CAACqC,sBAAsB,CAAC;;QAE1C;QACA,MAAMpB,eAAe,GAAGiB,aAAa,CAACI,IAAI,CAACE,WAAW,CAAC,CAAC;QACxD;;QAEA,IAAIL,gBAAgB,IAAIA,gBAAgB,CAACG,IAAI,CAAC3C,QAAQ,EAAE;UACtD,MAAM8C,YAAY,GAAGN,gBAAgB,CAACG,IAAI,CAAC3C,QAAQ,CAAC,CAAC;UACrD8B,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE/C,IAAI,CAACC,SAAS,CAAC6D,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE5F;UACA7C,WAAW,CAAC;YACR,GAAG6C,YAAY;YACfC,WAAW,EAAED,YAAY,CAACC,WAAW,KAAIzB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyB,WAAW;YACrEC,YAAY,EAAEF,YAAY,CAACE,YAAY,KAAI1B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0B,YAAY;UAC5E,CAAC,CAAC;;UAEF;UACAlB,OAAO,CAACC,GAAG,CAAC,oEAAoE,EAAEW,sBAAsB,CAACE,MAAM,CAAC;UAChHd,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEe,YAAY,CAACG,QAAQ,CAAC;UACtEnB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEe,YAAY,CAACrE,MAAM,CAAC;UAEvE,IAAIqE,YAAY,CAACG,QAAQ,IAAIH,YAAY,CAACG,QAAQ,CAACL,MAAM,GAAG,CAAC,IAAIF,sBAAsB,CAACE,MAAM,GAAG,CAAC,EAAE;YAClGd,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;YACxE,MAAMmB,eAAe,GAAGJ,YAAY,CAACG,QAAQ,CAACvE,GAAG,CAAC,CAACyE,EAAU,EAAEC,KAAa,KAAK;cAAA,IAAAC,oBAAA;cAC/EvB,OAAO,CAACC,GAAG,CAAC,kCAAkCoB,EAAE,cAAcC,KAAK,EAAE,CAAC;cACtE;cACA,MAAME,aAAa,GAAGZ,sBAAsB,CAACa,IAAI,CAAEC,CAAQ,IAAKA,CAAC,CAAC1E,OAAO,KAAKqE,EAAE,IAAIK,CAAC,CAACzE,GAAG,KAAKoE,EAAE,CAAC,CAAC,CAAC;cACnGrB,OAAO,CAACC,GAAG,CAAC,0CAA0CoB,EAAE,GAAG,EAAEG,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC;cAE1F,IAAI,CAACA,aAAa,EAAE;gBAClBxB,OAAO,CAAC2B,IAAI,CAAC,6EAA6EN,EAAE,aAAa,CAAC,CAAC,CAAC;gBAC5G,OAAO,IAAI;cACb;;cAEA;cACA,MAAMO,iBAAiB,IAAAL,oBAAA,GAAGP,YAAY,CAACrE,MAAM,cAAA4E,oBAAA,uBAAnBA,oBAAA,CAAsBD,KAAK,CAAC,CAAC,CAAC;cACxD,MAAMO,eAAe,GAAG,CAAAD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAE7E,OAAO,KAAIyE,aAAa,CAACzE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;cACnFiD,OAAO,CAACC,GAAG,CAAC,0CAA0CoB,EAAE,GAAG,EAAEQ,eAAe,CAAC;cAE7E,MAAM/E,UAAU,GAAG,GAAGuE,EAAE,IAAIC,KAAK,IAAI/D,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIwE,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;cAE9F;cACA,MAAMC,aAAa,GAAG;gBAClB,GAAGV,aAAa;gBAAE;gBAClBzE,OAAO,EAAE;kBAAE,GAAG8E;gBAAgB,CAAC;gBAAE;gBACjC/E,UAAU,CAAC;cACf,CAAC;cACDkD,OAAO,CAACC,GAAG,CAAC,wDAAwDoB,EAAE,GAAG,EAAEa,aAAa,CAAC;cACzF,OAAOA,aAAa;YACtB,CAAC,CAAC,CAACC,MAAM,CAACT,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC;YAE1B1B,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEmB,eAAe,CAAC;YAC1F/C,eAAe,CAAC+C,eAA0B,CAAC;UAC7C,CAAC,MAAM;YAAA,IAAAgB,qBAAA;YACLpC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;cAClEoC,WAAW,EAAE,CAAC,CAACrB,YAAY,CAACG,QAAQ;cACpCmB,cAAc,GAAAF,qBAAA,GAAEpB,YAAY,CAACG,QAAQ,cAAAiB,qBAAA,uBAArBA,qBAAA,CAAuBtB,MAAM;cAC7CyB,gBAAgB,EAAE,CAAC,CAAC3B,sBAAsB;cAC1C4B,mBAAmB,EAAE5B,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEE;YACjD,CAAC,CAAC;YACFzC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;UACvB;QACF,CAAC,MAAM;UACL;UACA2B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChE9B,WAAW,CAAC;YACVuB,YAAY,EAAE,mBAAmB;YACjC+C,OAAO,EAAE,mBAAmB;YAC5BxB,WAAW,EAAEzB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyB,WAAW;YACzCC,YAAY,EAAE1B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0B,YAAY;YAC3C;YACAwB,MAAM,EAAElD,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkD,MAAM,CAAC;UAClC,CAAC,CAAC;UACFrE,eAAe,CAAC,EAAE,CAAC;QACrB;MACF,CAAC,CAAC,OAAOsE,GAAQ,EAAE;QACjB3C,OAAO,CAACZ,KAAK,CAAC,4BAA4B,EAAEuD,GAAG,CAAC;QAChD,IAAIC,QAAQ,GAAG,6BAA6B,CAAC,CAAC;;QAE9C,IAAID,GAAG,CAACE,QAAQ,EAAE;UAAA,IAAAC,WAAA,EAAAC,eAAA;UAChB;UACA,IAAI,CAAAD,WAAA,GAAAH,GAAG,CAACK,MAAM,cAAAF,WAAA,gBAAAC,eAAA,GAAVD,WAAA,CAAYG,GAAG,cAAAF,eAAA,eAAfA,eAAA,CAAiBG,QAAQ,CAAC,cAAclF,UAAU,EAAE,CAAC,IAAI2E,GAAG,CAACE,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;YACxFP,QAAQ,GAAG,oBAAoB5E,UAAU,uCAAuC;YAChF;YACA;YACAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjBE,eAAe,CAAC,EAAE,CAAC;UACrB,CAAC,MAAM;YAAA,IAAA+E,kBAAA,EAAAC,mBAAA;YACL;YACAT,QAAQ,GAAG,EAAAQ,kBAAA,GAAAT,GAAG,CAACE,QAAQ,CAAChC,IAAI,cAAAuC,kBAAA,uBAAjBA,kBAAA,CAAmBhE,KAAK,OAAAiE,mBAAA,GAAIV,GAAG,CAACE,QAAQ,CAAChC,IAAI,cAAAwC,mBAAA,uBAAjBA,mBAAA,CAAmBC,OAAO,KAAIX,GAAG,CAACW,OAAO,IAAIV,QAAQ;UAC9F;QACF,CAAC,MAAM;UACL;UACAA,QAAQ,GAAGD,GAAG,CAACW,OAAO,IAAIV,QAAQ;QACpC;QAEAvD,QAAQ,CAACuD,QAAQ,CAAC;QAClB;QACA,IAAItE,eAAe,CAACwC,MAAM,KAAK,CAAC,EAAE;UAC9Bd,OAAO,CAAC2B,IAAI,CAAC,kFAAkF,CAAC;UAChGpD,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC,SAAS;QACRY,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDY,SAAS,CAAC,CAAC;IAEX,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;IACH;EACA,CAAC,EAAE,CAAC9B,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA,MAAMuF,sBAAsB,GAAGxI,WAAW,CAAEyI,SAAkB,IAAa;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IACzE;IACA,IAAI,CAACF,SAAS,CAAC1C,MAAM,EAAE,OAAO,EAAE;;IAEhC;IACA,MAAM6C,QAAQ,GAAGjH,gBAAgB,CAAC8G,SAAS,CAAC;;IAE5C;IACA,IAAIjH,aAAa,CAACqH,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC/B,MAAME,MAAM,GAAGtH,aAAa,CAAC4D,GAAG,CAACwD,QAAQ,CAAC;MAC1C,IAAIE,MAAM,IAAKtG,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGuG,MAAM,CAAClG,SAAS,GAAIlB,SAAS,EAAE;QACzDuD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO4D,MAAM,CAACC,IAAI;MACpB;IACF;;IAEA;IACAzG,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAM0G,SAAS,GAAG,CAAC;IACnB,IAAIC,eAAe,GAAG,EAAE;IAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAAC1C,MAAM,EAAEmD,CAAC,IAAIF,SAAS,EAAE;MACpD,MAAMG,KAAK,GAAGV,SAAS,CAACW,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC;MAC/C,MAAMK,YAAY,GAAGF,KAAK,CAACtH,GAAG,CAACC,KAAK,IAAI;QACtC,IAAIwH,SAAS,GAAGxH,KAAK,CAACiH,IAAI,IAAI,EAAE;QAChC,MAAM/G,OAAO,GAAGF,KAAK,CAACE,OAAO,IAAI,CAAC,CAAC;;QAEnC;QACAuH,MAAM,CAACC,IAAI,CAACxH,OAAO,CAAC,CAACS,OAAO,CAACE,GAAG,IAAI;UAClC,MAAM8G,KAAK,GAAGzH,OAAO,CAACW,GAAG,CAAC;UAC1B,MAAM+G,WAAW,GAAG,KAAK/G,GAAG,IAAI,CAAC,CAAC;;UAElC;UACA,IAAI8G,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;YACzC;YACA,IAAIG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;cACxB;cACA,IAAI9G,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,UAAU,EAAE;gBAC7C,MAAMmH,SAAS,GAAGL,KAAK,CAAC5H,GAAG,CAAEkI,IAAS,IACpC,yBAAyBA,IAAI,CAAC7B,GAAG,IAAI,GAAG,qBAAqB6B,IAAI,CAACC,IAAI,IAAI,MAAM,mBAClF,CAAC,CAAC3H,IAAI,CAAC,IAAI,CAAC;gBACZiH,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,kBAAkB,EAAEH,SAAS,CAAC;cAC9D,CAAC,MAAM,IAAInH,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,aAAa,EAAE;gBAC1D,MAAMuH,SAAS,GAAGT,KAAK,CAAC5H,GAAG,CAAEsI,IAAS;kBAAA,IAAAC,cAAA;kBAAA,OACpC,4BAA4B,EAAAA,cAAA,GAAAD,IAAI,CAACE,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,KAAI,OAAO,WAAWH,IAAI,CAACjC,GAAG,IAAI,GAAG,wBAAwB;gBAAA,CACvH,CAAC,CAAC7F,IAAI,CAAC,IAAI,CAAC;gBACZiH,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,qBAAqB,EAAEC,SAAS,CAAC;cACjE;YACF,CAAC,MAAM;cACL;cACA,MAAMK,WAAW,GAAGC,MAAM,CAACf,KAAK,CAAC;cACjCH,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACf,WAAW,EAAEa,WAAW,CAAC;;cAE1D;cACA,IAAIjB,SAAS,CAACnB,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC5B;gBACA,MAAMuC,oBAAoB,GAAG,KAAK/H,GAAG,CAACsH,OAAO,CAAC,WAAW,EAAEU,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI;gBACvF,MAAMC,oBAAoB,GAAG,KAAKlI,GAAG,CAACsH,OAAO,CAAC,QAAQ,EAAEa,MAAM,IAAI,IAAIA,MAAM,CAACR,WAAW,CAAC,CAAC,EAAE,CAAC,IAAI;gBAEjGhB,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACC,oBAAoB,EAAEH,WAAW,CAAC;gBACnEjB,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACI,oBAAoB,EAAEN,WAAW,CAAC;cACrE;YACF;UACF;QACF,CAAC,CAAC;;QAEF;QACA,IAAIjB,SAAS,CAACnB,QAAQ,CAAC,IAAI,CAAC,EAAE;UAC5BmB,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QACvD;QAEA,OAAOX,SAAS;MAClB,CAAC,CAAC,CAACjH,IAAI,CAAC,IAAI,CAAC;MAEb4G,eAAe,IAAII,YAAY;IACjC;;IAEA;IACA,MAAMnD,WAAW,IAAAwC,qBAAA,GAAGvF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+C,WAAW,cAAAwC,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;IAC/C,MAAMvC,YAAY,IAAAwC,qBAAA,GAAGxF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgD,YAAY,cAAAwC,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;IACjD,MAAMoC,YAAY,GAAG7E,WAAW,CAAC8E,OAAO,IAAI,SAAS;IACrD,MAAMC,eAAe,GAAG/E,WAAW,CAACgF,UAAU,IAAI,SAAS;IAC3D,MAAMC,SAAS,GAAGjF,WAAW,CAACkF,IAAI,IAAI,SAAS;IAC/C,MAAMC,WAAW,GAAGlF,YAAY,CAACmF,OAAO,IAAI,mBAAmB;IAC/D,MAAMC,QAAQ,GAAGpF,YAAY,CAACqF,IAAI,IAAI,mBAAmB;;IAEzD;IACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA,gBAAgB,CAAAtI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuE,OAAO,KAAI,gBAAgB;AACrD;AACA;AACA,2CAA2C6D,QAAQ;AACnD,+EAA+EJ,SAAS;AACxF;AACA;AACA,qCAAqCJ,YAAY;AACjD;AACA;AACA;AACA;AACA,mBAAmBA,YAAY;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+BE,eAAe;AAC9C,MAAMhC,eAAe;AACrB;AACA,QAAQ;;IAEJ;IACAzH,aAAa,CAACkK,GAAG,CAAC9C,QAAQ,EAAE;MAC1BG,IAAI,EAAE0C,QAAQ;MACdE,IAAI,EAAE,EAAE;MAAE;MACV/I,SAAS,EAAEJ,IAAI,CAACD,GAAG,CAAC;IACtB,CAAC,CAAC;IAEF,OAAOkJ,QAAQ;EACjB,CAAC,EAAE,CAACtI,QAAQ,CAAC,CAAC;EAEdlD,SAAS,CAAC,MAAM;IACd,MAAM2L,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI,CAACvI,YAAY,IAAIA,YAAY,CAAC0C,MAAM,KAAK,CAAC,EAAE;QAC9CnC,cAAc,CAAC,oLAAoL,CAAC;QACpM;MACF;MAEA,IAAI;QACF;QACA,MAAMgF,QAAQ,GAAGjH,gBAAgB,CAAC0B,YAAY,CAAC;;QAE/C;QACA,IAAI7B,aAAa,CAACqH,GAAG,CAACD,QAAQ,CAAC,EAAE;UAC/B,MAAME,MAAM,GAAGtH,aAAa,CAAC4D,GAAG,CAACwD,QAAQ,CAAC;UAC1C,IAAIE,MAAM,IAAIA,MAAM,CAAC6C,IAAI,IAAKnJ,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGuG,MAAM,CAAClG,SAAS,GAAIlB,SAAS,EAAE;YACxEuD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtDtB,cAAc,CAACkF,MAAM,CAAC6C,IAAI,CAAC;YAC3B;UACF;QACF;;QAEA;QACA,MAAME,UAAU,GAAGrD,sBAAsB,CAACnF,YAAY,CAAC;QACvD4B,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;QAElE;QACA,IAAI4G,MAAM,CAACC,MAAM,EAAE;UACjB;UACA;UACA,MAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;YACrC;YACA,IAAI;cACF,MAAM;gBAAEN,IAAI;gBAAEO;cAAO,CAAC,GAAG9L,SAAS,CAACyL,UAAU,EAAE;gBAAEM,eAAe,EAAE;cAAO,CAAC,CAAC;cAC3EvI,cAAc,CAAC+H,IAAI,CAAC;cACpB;cACA,MAAM/C,QAAQ,GAAGjH,gBAAgB,CAAC0B,YAAY,CAAC;cAC/C,IAAI7B,aAAa,CAACqH,GAAG,CAACD,QAAQ,CAAC,EAAE;gBAC/B,MAAMlG,KAAK,GAAGlB,aAAa,CAAC4D,GAAG,CAACwD,QAAQ,CAAC;gBACzC,IAAIlG,KAAK,EAAE;kBACTA,KAAK,CAACiJ,IAAI,GAAGA,IAAI;kBACjBjJ,KAAK,CAACE,SAAS,GAAGJ,IAAI,CAACD,GAAG,CAAC,CAAC;gBAC9B;cACF;YACF,CAAC,CAAC,OAAO6J,WAAgB,EAAE;cACzBnH,OAAO,CAACZ,KAAK,CAAC,qDAAqD,EAAE+H,WAAW,CAAC;cACjFxI,cAAc,CAAC,0DAA0D,CAAAwI,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE7D,OAAO,KAAI,eAAe,QAAQ,CAAC;YAC3H;UACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;UAEV;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,MAAM;UACL;UACA,MAAM;YAAEoD,IAAI;YAAEO;UAAO,CAAC,GAAG9L,SAAS,CAACyL,UAAU,EAAE;YAAEM,eAAe,EAAE;UAAO,CAAC,CAAC;UAC3E,IAAID,MAAM,IAAIA,MAAM,CAACnG,MAAM,GAAG,CAAC,EAAE;YAC/Bd,OAAO,CAAC2B,IAAI,CAAC,wCAAwC,EAAEsF,MAAM,CAACnG,MAAM,EAAE,QAAQ,CAAC;UACjF;UACAnC,cAAc,CAAC+H,IAAI,CAAC;;UAEpB;UACA,IAAInK,aAAa,CAACqH,GAAG,CAACD,QAAQ,CAAC,EAAE;YAC/B,MAAMlG,KAAK,GAAGlB,aAAa,CAAC4D,GAAG,CAACwD,QAAQ,CAAC;YACzC,IAAIlG,KAAK,EAAE;cACTA,KAAK,CAACiJ,IAAI,GAAGA,IAAI;cACjBjJ,KAAK,CAACE,SAAS,GAAGJ,IAAI,CAACD,GAAG,CAAC,CAAC;YAC9B;UACF;QACF;MACF,CAAC,CAAC,OAAOqF,GAAQ,EAAE;QACjB3C,OAAO,CAACZ,KAAK,CAAC,iDAAiD,EAAEuD,GAAG,CAAC;QACrEhE,cAAc,CAAC,0DAA0D,CAAAgE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEW,OAAO,KAAI,eAAe,QAAQ,CAAC;MACnH;IACF,CAAC;;IAED;IACA,MAAM8D,eAAe,GAAGJ,UAAU,CAACL,eAAe,EAAE,GAAG,CAAC;IACxD,OAAO,MAAMU,YAAY,CAACD,eAAe,CAAC;EAC5C,CAAC,EAAE,CAAChJ,YAAY,EAAEmF,sBAAsB,CAAC,CAAC;;EAE1C;EACA,MAAM+D,SAAS,GAAGvM,WAAW,CAAC,CAACwM,SAAiB,EAAEC,UAAkB,KAAK;IACrEnJ,eAAe,CAACoJ,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjC,MAAM,CAACE,YAAY,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACL,SAAS,EAAE,CAAC,CAAC;MACrDG,SAAS,CAACE,MAAM,CAACJ,UAAU,EAAE,CAAC,EAAEG,YAAY,CAAC;MAC7C,OAAOD,SAAS;IACpB,CAAC,CAAC;IACF;IACA,IAAIlJ,kBAAkB,KAAK+I,SAAS,EAAE;MAClC9I,qBAAqB,CAAC+I,UAAU,CAAC;IACrC,CAAC,MAAM,IAAIhJ,kBAAkB,KAAK,IAAI,EAAE;MACpC,IAAI+I,SAAS,GAAGC,UAAU,IAAIhJ,kBAAkB,GAAG+I,SAAS,IAAI/I,kBAAkB,IAAIgJ,UAAU,EAAE;QAC9F/I,qBAAqB,CAACoJ,CAAC,IAAKA,CAAC,KAAK,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAK,CAAC;MAC3D,CAAC,MAAM,IAAIN,SAAS,GAAGC,UAAU,IAAIhJ,kBAAkB,IAAIgJ,UAAU,IAAIhJ,kBAAkB,GAAG+I,SAAS,EAAE;QACrG9I,qBAAqB,CAACoJ,CAAC,IAAKA,CAAC,KAAK,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAK,CAAC;MAC3D;IACJ;EACJ,CAAC,EAAE,CAACrJ,kBAAkB,CAAC,CAAC;EAExB,MAAMsJ,oBAAoB,GAAG/M,WAAW,CAAC,CAAC8B,KAAU,EAAEyE,KAAa,KAAK;IACpE;IACA,MAAMyG,sBAAsB,GAAG;MAC3B,GAAG7K,IAAI,CAAC8K,KAAK,CAAC9K,IAAI,CAACC,SAAS,CAACN,KAAK,CAAC,CAAC;MACpCG,OAAO,EAAEH,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAI,SAASM,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;MAAE;MAC9DP,OAAO,EAAEF,KAAK,CAACE,OAAO,GAAGG,IAAI,CAAC8K,KAAK,CAAC9K,IAAI,CAACC,SAAS,CAACN,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACvED,UAAU,EAAE,GAAGD,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAI,KAAK,IAAIM,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIwE,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;IAC/G,CAAC;IAED5D,eAAe,CAACoJ,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjCC,SAAS,CAACE,MAAM,CAACtG,KAAK,EAAE,CAAC,EAAEyG,sBAA+B,CAAC;MAC3D,OAAOL,SAAS;IACpB,CAAC,CAAC;IACFjJ,qBAAqB,CAAC6C,KAAK,CAAC,CAAC,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2G,WAAW,GAAGlN,WAAW,CAAEuG,KAAa,IAAK;IAC/CjD,eAAe,CAACoJ,UAAU,IAAIA,UAAU,CAACtF,MAAM,CAAC,CAAC+F,CAAC,EAAEjE,CAAC,KAAKA,CAAC,KAAK3C,KAAK,CAAC,CAAC;IACvE,IAAI9C,kBAAkB,KAAK8C,KAAK,EAAE;MAC9B7C,qBAAqB,CAAC,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAID,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,GAAG8C,KAAK,EAAE;MAClE7C,qBAAqB,CAAC0J,SAAS,IAAKA,SAAS,KAAK,IAAI,GAAGA,SAAS,GAAG,CAAC,GAAG,IAAK,CAAC;IACnF;EACJ,CAAC,EAAE,CAAC3J,kBAAkB,CAAC,CAAC;EAExB,MAAM4J,kBAAkB,GAAGrN,WAAW,CAAC,CAACuG,KAAa,EAAE+G,cAAqC,KAAK;IAC7FhK,eAAe,CAACoJ,UAAU,IACtBA,UAAU,CAAC7K,GAAG,CAAC,CAACC,KAAK,EAAEoH,CAAC,KACpBA,CAAC,KAAK3C,KAAK,GACL;MAAE,GAAGzE,KAAK;MAAEE,OAAO,EAAE;QAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,CAAC,CAAC,CAAC;QAAE,GAAGsL;MAAe;IAAE,CAAC,GACtExL,KACV,CACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyL,UAAU,GAAG,MAAAA,CAAOC,kBAA0B,EAAEC,cAAuB,GAAG,KAAK,KAAK;IACxFzJ,WAAW,CAAC,IAAI,CAAC;IACjBQ,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMuE,IAAI,GAAGP,sBAAsB,CAACnF,YAAY,CAAC;MACjD,MAAM;QAAEsI,IAAI;QAAEO,MAAM,EAAEwB;MAAiB,CAAC,GAAGtN,SAAS,CAAC2I,IAAI,CAAC;MAC1D,IAAI2E,gBAAgB,IAAIA,gBAAgB,CAAC3H,MAAM,GAAG,CAAC,EAAE;QACnDd,OAAO,CAAC2B,IAAI,CAAC,8CAA8C,EAAE8G,gBAAgB,CAAC;QAC9E;MACF;MAEA,MAAMtH,QAAQ,GAAG/C,YAAY,CAC1BxB,GAAG,CAAC8E,CAAC,IAAI6D,MAAM,CAAC7D,CAAC,CAAC1E,OAAO,IAAI0E,CAAC,CAACzE,GAAG,CAAC,CAAC,CAAC;MAAA,CACrCkF,MAAM,CAACuG,OAAO,CAAC,CAAC,CAAC;;MAEpB,MAAMC,OAAwE,GAAG;QAC/E3K,UAAU,EAAEE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjB,GAAG;QACzByC,YAAY,EAAE6I,kBAAkB;QAChCzE,IAAI;QACJ4C,IAAI;QACJvF,QAAQ;QACRsB,OAAO,EAAE,CAAAvE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuE,OAAO,KAAI,mBAAmB;QACjDmG,IAAI,EAAE,CAAA1K,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE0K,IAAI,KAAI,EAAE;QAC1BC,QAAQ,EAAEL,cAAc;QACxBM,WAAW,EAAE,CAAA5K,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4K,WAAW,KAAI,EAAE;QACxCC,QAAQ,EAAE,CAAA7K,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6K,QAAQ,KAAI,EAAE;QAClCC,aAAa,EAAE,CAAA9K,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE8K,aAAa,KAAI;MAC5C,CAAC;;MAED;MACA;MACA;MACA;MACA;;MAEA,MAAMnG,QAAQ,GAAG,MAAMrH,GAAG,CAACyN,IAAI,CAAuB,iBAAiB,EAAEN,OAAO,CAAC;MACjF,MAAMO,aAAa,GAAGrG,QAAQ,CAAChC,IAAI,CAAC3C,QAAQ;MAC5CC,WAAW,CAAC+K,aAAa,CAAC;;MAE1B;MACA;MACA,IAAIA,aAAa,CAAC/H,QAAQ,IAAI7C,eAAe,CAACwC,MAAM,GAAG,CAAC,EAAE;QACvD,MAAMM,eAAe,GAAG8H,aAAa,CAAC/H,QAAQ,CAACvE,GAAG,CAAC,CAACyE,EAAE,EAAEC,KAAK,KAAK;UAAA,IAAA6H,qBAAA,EAAAC,sBAAA;UAC/D,MAAM5H,aAAa,GAAGlD,eAAe,CAACmD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1E,OAAO,KAAKqE,EAAE,IAAIK,CAAC,CAACzE,GAAG,KAAKoE,EAAE,CAAC;UACjF,MAAMQ,eAAe,GAAG,EAAAsH,qBAAA,GAAAD,aAAa,CAACvM,MAAM,cAAAwM,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAuB7H,KAAK,CAAC,cAAA8H,sBAAA,uBAA7BA,sBAAA,CAA+BrM,OAAO,MAAIyE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEzE,OAAO,KAAI,CAAC,CAAC;UAC9F,MAAMD,UAAU,GAAG,GAAGuE,EAAE,IAAIC,KAAK,IAAI/D,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIwE,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE;UAC5F,OAAOT,aAAa,GAAG;YAAE,GAAGA,aAAa;YAAEzE,OAAO,EAAE;cAAE,GAAG8E;YAAgB,CAAC;YAAE/E;UAAW,CAAC,GAAG,IAAI;QAClG,CAAC,CAAC,CAACqF,MAAM,CAAET,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAY;QACvCrD,eAAe,CAAC+C,eAAe,CAAC;MACnC;MAEAnC,gBAAgB,CAAC,KAAK,CAAC;MACvBe,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACjC,UAAU,IAAIkL,aAAa,CAACjM,GAAG,EAAE;QACpCgB,QAAQ,CAAC,iBAAiBiL,aAAa,CAACjM,GAAG,EAAE,EAAE;UAAE+H,OAAO,EAAE;QAAK,CAAC,CAAC;MACnE;MACA,OAAOkE,aAAa;IACtB,CAAC,CAAC,OAAOvG,GAAQ,EAAE;MAAA,IAAA0G,aAAA,EAAAC,mBAAA;MACjBtJ,OAAO,CAACZ,KAAK,CAAC,wBAAwB,EAAEuD,GAAG,CAAC;MAC5C,MAAM4G,YAAY,GAAG,EAAAF,aAAA,GAAA1G,GAAG,CAACE,QAAQ,cAAAwG,aAAA,wBAAAC,mBAAA,GAAZD,aAAA,CAAcxI,IAAI,cAAAyI,mBAAA,uBAAlBA,mBAAA,CAAoBlK,KAAK,KAAIuD,GAAG,CAACW,OAAO,IAAI,0BAA0B;MAC3F/D,YAAY,CAACgK,YAAY,CAAC;MAC1B,OAAO,IAAI;IACb,CAAC,SAAS;MACRxK,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM,CAAC;IAAEyK;EAAa,CAAC,EAAEC,IAAI,CAAC,GAAGpO,OAAO,CAAC,OAAO;IAC5CqO,MAAM,EAAE,CAACtN,SAAS,CAACC,KAAK,EAAED,SAAS,CAACE,aAAa,CAAC;IAClDmN,IAAI,EAAEA,CAACE,IAAsC,EAAEC,OAAO,KAAK;MACvD,IAAIA,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE;MAEvB,MAAMC,SAAS,GAAGjK,eAAe,CAACkK,OAAO;MACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACK,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACD,YAAY,IAAI,CAACF,SAAS,EAAE;MAEjC,MAAMtC,UAAU,GAAG0C,aAAa,CAACF,YAAY,CAACG,CAAC,EAAEL,SAAS,CAAC;MAE3D,IAAIH,IAAI,CAACS,IAAI,KAAKhO,SAAS,CAACE,aAAa,EAAE;QACzCwL,oBAAoB,CAAE6B,IAAI,CAAqB9M,KAAK,EAAE2K,UAAU,CAAC;MACnE;MACA;IACJ,CAAC;IACD6C,OAAO,EAAET,OAAO,KAAK;MACnBJ,YAAY,EAAEI,OAAO,CAACU,MAAM,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC;IAChD,CAAC;EACL,CAAC,CAAC,EAAE,CAACnM,YAAY,EAAEkJ,SAAS,EAAEQ,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAMoC,aAAa,GAAGA,CAACM,OAA2B,EAAEC,SAAyB,KAAa;IACtF,IAAID,OAAO,KAAK9F,SAAS,EAAE,OAAOtG,YAAY,CAAC0C,MAAM;IAErD,MAAM4J,aAAa,GAAGD,SAAS,CAACE,qBAAqB,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGJ,OAAO,GAAGE,aAAa,CAACG,GAAG,GAAGJ,SAAS,CAACK,SAAS;IAEjE,IAAIC,eAAe,GAAG3M,YAAY,CAAC0C,MAAM;IACzC,MAAMkK,QAAQ,GAAGrG,KAAK,CAACsG,IAAI,CAACR,SAAS,CAACO,QAAQ,CAAkB;IAEhE,KAAK,IAAI/G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+G,QAAQ,CAAClK,MAAM,EAAEmD,CAAC,EAAE,EAAE;MACtC,MAAMiH,KAAK,GAAGF,QAAQ,CAAC/G,CAAC,CAAC;MACzB,IAAI,CAACiH,KAAK,CAACC,SAAS,IAAI,CAACD,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MAEtE,MAAMC,QAAQ,GAAGH,KAAK,CAACI,SAAS;MAChC,MAAMC,WAAW,GAAGL,KAAK,CAACM,YAAY;MACtC,MAAMC,OAAO,GAAGJ,QAAQ,GAAGE,WAAW,GAAG,CAAC;MAE1C,IAAIX,OAAO,GAAGa,OAAO,EAAE;QACnBV,eAAe,GAAG9G,CAAC;QACnB;MACJ;IACJ;IACA,OAAO8G,eAAe;EAC1B,CAAC;;EAED;EACA/P,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACAuB,aAAa,CAACmP,KAAK,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,sBAAsB,GAAG5Q,WAAW,CAAC,MAAM;IAC/C;IACA,IAAI,QAAQ,IAAI6Q,WAAW,EAAE;MAC3B,MAAMC,UAAU,GAAID,WAAW,CAASE,MAAM;MAC9C,IAAID,UAAU,CAACE,cAAc,GAAGF,UAAU,CAACG,eAAe,GAAG,GAAG,EAAE;QAChEhM,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAC5E;QACA5C,eAAe,CAAC,CAAC;QACjB;MACF;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArC,SAAS,CAAC,MAAM;IACd,IAAI,iBAAiB,IAAI6L,MAAM,EAAE;MAC9BA,MAAM,CAASoF,gBAAgB,CAAC,eAAe,EAAEN,sBAAsB,CAAC;MACzE,OAAO,MAAM;QACV9E,MAAM,CAASqF,mBAAmB,CAAC,eAAe,EAAEP,sBAAsB,CAAC;MAC9E,CAAC;IACH;EACF,CAAC,EAAE,CAACA,sBAAsB,CAAC,CAAC;;EAE5B;EACA,IAAIzM,SAAS,EAAE;IACb,oBAAOnD,OAAA;MAAKoQ,SAAS,EAAC,iEAAiE;MAAAnB,QAAA,EAAC;IAAiB;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjH;;EAEA;EACA,IAAInN,KAAK,IAAI,CAAClB,QAAQ,EAAE;IACtB,oBAAOnC,OAAA;MAAKoQ,SAAS,EAAC,+DAA+D;MAAAnB,QAAA,GAAC,sCAAoC,EAAC5L,KAAK;IAAA;MAAAgN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACzI;EAEA,oBACExQ,OAAA;IAAKoQ,SAAS,EAAC,iDAAiD;IAAAnB,QAAA,gBAC9DjP,OAAA,CAACF,OAAO;MACN2Q,MAAM,EAAEA,CAAA,KAAM;QACVrO,WAAW,CAACsO,IAAI,KAAK;UAAE,IAAIA,IAAI,IAAI,CAAC,CAAC,CAAC;UAAE/M,YAAY,EAAE,CAAA+M,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE/M,YAAY,KAAI;QAAoB,CAAC,CAAa,CAAC;QAC/GH,YAAY,CAAC,IAAI,CAAC;QAClBN,gBAAgB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFyN,mBAAmB,EAAE7N,cAAe;MACpCD,WAAW,EAAEA,WAAY;MACzBE,QAAQ,EAAEA;IAAS;MAAAsN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,EAGDnN,KAAK,IAAIpB,UAAU,iBAChBjC,OAAA;MAAKoQ,SAAS,EAAC,yEAAyE;MAAAnB,QAAA,GAAC,SAAO,EAAC5L,KAAK;IAAA;MAAAgN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGjHxQ,OAAA;MAAKoQ,SAAS,EAAE,4CAA6C;MAAAnB,QAAA,gBAE3DjP,OAAA;QAAKoQ,SAAS,EAAC,mGAAmG;QAAAnB,QAAA,gBAChHjP,OAAA;UAAIoQ,SAAS,EAAC,4FAA4F;UAAAnB,QAAA,EAAC;QAAa;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7HxQ,OAAA,CAACL,YAAY;UACXiB,MAAM,EAAE2B,eAAgB;UACxBqO,UAAU,EAAG9P,KAAK,IAAKiL,oBAAoB,CAACjL,KAAK,EAAEuB,YAAY,CAAC0C,MAAM,CAAE,CAAC;QAAA;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNxQ,OAAA;QAAK6Q,GAAG,EAAEnD,IAAK;QAAC0C,SAAS,EAAE,qEAAqE3C,YAAY,GAAG,6CAA6C,GAAG,EAAE,EAAG;QAAAwB,QAAA,eAClKjP,OAAA;UAAK6Q,GAAG,EAAE/M,eAAgB;UAACsM,SAAS,EAAC,2DAA2D;UAAAnB,QAAA,EAC7F5M,YAAY,CAAC0C,MAAM,KAAK,CAAC,gBACxB/E,OAAA;YAAKoQ,SAAS,EAAC,oJAAoJ;YAAAnB,QAAA,eACjKjP,OAAA;cAAAiP,QAAA,GAAG,mCAAiC,eAAAjP,OAAA;gBAAAqQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,8CAA0C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,GAENnO,YAAY,CAACxB,GAAG,CAAC,CAACC,KAAK,EAAEyE,KAAK,kBAC5BvF,OAAA,CAAC8Q,cAAc;YACU;YACvBhQ,KAAK,EAAEA,KAAM;YACbyE,KAAK,EAAEA,KAAM;YACbgG,SAAS,EAAEA,SAAU;YACrBW,WAAW,EAAEA,WAAY;YACzB6E,UAAU,EAAEtO,kBAAkB,KAAK8C,KAAM;YACzCyL,OAAO,EAAEA,CAAA,KAAMtO,qBAAqB,CAAC6C,KAAK;UAAE,GANvCzE,KAAK,CAACC,UAAU;YAAAsP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOtB,CACF;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxQ,OAAA;QAAKoQ,SAAS,EAAC,0GAA0G;QAAAnB,QAAA,EACnHxM,kBAAkB,KAAK,IAAI,IAAIJ,YAAY,CAAC0C,MAAM,KAAK,CAAC;QAAA;QACtD;QACA/E,OAAA;UAAKoQ,SAAS,EAAC,oDAAoD;UAAAnB,QAAA,gBAC/DjP,OAAA;YAAIoQ,SAAS,EAAC,0EAA0E;YAAAnB,QAAA,EAAC;UAAO;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrGxQ,OAAA,CAACJ,YAAY;YACT+K,IAAI,EAAEhI,WAAY;YAClBsO,IAAI,EAAEpO,WAAY;YAClBqO,SAAS,EAAErN;UAAiB;YAAAwM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;QAAA;QAEN;QACAxQ,OAAA;UAAKoQ,SAAS,EAAC,yDAAyD;UAAAnB,QAAA,gBACpEjP,OAAA;YAAIoQ,SAAS,EAAC,4GAA4G;YAAAnB,QAAA,gBACxHjP,OAAA;cAAAiP,QAAA,GAAM,QAAM,EAAC,EAAAjN,qBAAA,GAAAK,YAAY,CAACI,kBAAkB,CAAC,cAAAT,qBAAA,uBAAhCA,qBAAA,CAAkCgH,IAAI,KAAI,OAAO;YAAA;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtExQ,OAAA;cACEgR,OAAO,EAAEA,CAAA,KAAMtO,qBAAqB,CAAC,IAAI,CAAE;cAC3C0N,SAAS,EAAC,yEAAyE;cACnFe,KAAK,EAAC,cAAc;cAAAlC,QAAA,EACrB;YAED;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACLxQ,OAAA;YAAKoQ,SAAS,EAAC,wBAAwB;YAAAnB,QAAA,EAClC5M,YAAY,CAACI,kBAAkB,CAAC;YAAA;YAAM;YACnCzC,OAAA,CAACN,WAAW;cACRoB,KAAK,EAAEuB,YAAY,CAACI,kBAAkB,CAAE;cACxC2O,QAAQ,EAAGpQ,OAAO,IAAK;gBACnB,IAAIyB,kBAAkB,KAAK,IAAI,EAAE;kBAC7B4J,kBAAkB,CAAC5J,kBAAkB,EAAEzB,OAAO,CAAC;gBACnD;cACJ;YAAE;cAAAqP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,EAGLvN,aAAa,iBACZjD,OAAA,CAACH,iBAAiB;MAChBwR,WAAW,EAAE,CAAAlP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,YAAY,KAAI,mBAAoB;MAC3D8M,MAAM,EAAElE,UAAW;MACnB+E,QAAQ,EAAEA,CAAA,KAAM;QAAEpO,gBAAgB,CAAC,KAAK,CAAC;QAAEM,YAAY,CAAC,IAAI,CAAC;MAAE,CAAE;MACjET,QAAQ,EAAEA,QAAS;MACnBM,KAAK,EAAEE,SAAU,CAAC;IAAA;MAAA8M,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAzO,EAAA,CAnsBMD,WAAqB;EAAA,QACFtC,SAAS,EACfD,WAAW,EA4fKD,OAAO;AAAA;AAAAiS,EAAA,GA9fpCzP,WAAqB;AA6sB3B,MAAMgP,cAA6C,GAAGA,CAAC;EACrDhQ,KAAK;EACLyE,KAAK;EACLgG,SAAS;EACTW,WAAW;EACX6E,UAAU;EACVC;AACF,CAAC,KAAK;EAAAQ,GAAA;EACJ,MAAMX,GAAG,GAAG3R,MAAM,CAAiB,IAAI,CAAC;EAExC,MAAM,CAAC;IAAEuS;EAAU,CAAC,EAAE/D,IAAI,CAAC,GAAGpO,OAAO,CAA8D,OAAO;IACxGqO,MAAM,EAAEtN,SAAS,CAACC,KAAK;IACvBoR,KAAK,EAAEA,CAAC9D,IAAoB,EAAEC,OAAgD,KAAK;MACjF,IAAI,CAACgD,GAAG,CAAC7C,OAAO,EAAE;MAClB,MAAMxC,SAAS,GAAGoC,IAAI,CAACrI,KAAK;MAC5B,MAAMkG,UAAU,GAAGlG,KAAK;MACxB,IAAIiG,SAAS,KAAKC,UAAU,EAAE;MAE9B,MAAMkG,iBAAiB,GAAGd,GAAG,CAAC7C,OAAO,CAACY,qBAAqB,CAAC,CAAC;MAC7D,MAAMgD,YAAY,GAAG,CAACD,iBAAiB,CAACE,MAAM,GAAGF,iBAAiB,CAAC7C,GAAG,IAAI,CAAC;MAC3E,MAAMb,YAAY,GAAGJ,OAAO,CAACK,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACD,YAAY,EAAE;MACnB,MAAM6D,YAAY,GAAG7D,YAAY,CAACG,CAAC,GAAGuD,iBAAiB,CAAC7C,GAAG;MAE3D,IAAItD,SAAS,GAAGC,UAAU,IAAIqG,YAAY,GAAGF,YAAY,EAAE;MAC3D,IAAIpG,SAAS,GAAGC,UAAU,IAAIqG,YAAY,GAAGF,YAAY,EAAE;MAE3DrG,SAAS,CAACC,SAAS,EAAEC,UAAU,CAAC;MAChCmC,IAAI,CAACrI,KAAK,GAAGkG,UAAU,CAAC,CAAC;IAC3B,CAAC;IACD6C,OAAO,EAAGT,OAAO,KAAM;MACnB4D,SAAS,EAAE5D,OAAO,CAACkE,YAAY,CAAC;IACpC,CAAC;EACH,CAAC,CAAC,EAAE,CAACxM,KAAK,EAAEgG,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEzB,MAAM,CAAC;IAAEyG;EAAW,CAAC,EAAEC,IAAI,CAAC,GAAG5S,OAAO,CAAC,OAAO;IAC5CgP,IAAI,EAAEhO,SAAS,CAACC,KAAK;IACrBsN,IAAI,EAAE;MAAErI,KAAK;MAAED,EAAE,EAAExE,KAAK,CAACC,UAAU;MAAEsN,IAAI,EAAEhO,SAAS,CAACC;IAAM,CAAC;IAAE;IAC9DgO,OAAO,EAAGT,OAAO,KAAM;MACrBmE,UAAU,EAAEnE,OAAO,CAACmE,UAAU,CAAC;IACjC,CAAC;EACH,CAAC,CAAC,EAAE,CAACzM,KAAK,EAAEzE,KAAK,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEhCkR,IAAI,CAACvE,IAAI,CAACmD,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEjB,oBACE7Q,OAAA;IACE6Q,GAAG,EAAEA,GAAI;IACT,mBAAiBY,SAAU;IAC3BrB,SAAS,EAAE,yEAAyE4B,UAAU,GAAG,4BAA4B,GAAG,uCAAuC,IAAIjB,UAAU,GAAG,oDAAoD,GAAG,iBAAiB,EAAG,CAAC;IAAA;IACpQC,OAAO,EAAEA,OAAQ;IACjBkB,KAAK,EAAE;MAAEC,OAAO,EAAEH,UAAU,GAAG,GAAG,GAAG;IAAE,CAAE;IAAA/C,QAAA,gBAEzCjP,OAAA;MAAKoQ,SAAS,EAAC,qHAAqH;MAAAnB,QAAA,GAAC,GAAC,eACpIjP,OAAA;QAAMoQ,SAAS,EAAC,oDAAoD;QAACe,KAAK,EAAErQ,KAAK,CAACkI,IAAK;QAAAiG,QAAA,GAAEnO,KAAK,CAACkI,IAAI,EAAC,GAAC,eAAAhJ,OAAA;UAAMoQ,SAAS,EAAC,eAAe;UAAAnB,QAAA,GAAC,GAAC,EAACnO,KAAK,CAACsR,QAAQ,EAAC,GAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrKxQ,OAAA;QAAKoQ,SAAS,EAAC,2CAA2C;QAAAnB,QAAA,GAAC,GAAC,eAEzDjP,OAAA;UACCqO,IAAI,EAAC,QAAQ;UACb+B,SAAS,EAAC,yDAAyD;UACnEY,OAAO,EAAGqB,CAAC,IAAK;YACZA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBrO,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEqB,KAAK,CAAC,CAAC,CAAC;UAC5C,CAAE;UACF4L,KAAK,EAAC,iBAAiB;UAAAlC,QAAA,eAGpBjP,OAAA;YAAKuS,KAAK,EAAC,4BAA4B;YAACnC,SAAS,EAAC,SAAS;YAACoC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAE,CAAE;YAAA1D,QAAA,eAC7HjP,OAAA;cAAM4S,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAuH;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9K;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEVxQ,OAAA;UACEqO,IAAI,EAAC,QAAQ;UACb+B,SAAS,EAAC,qEAAqE,CAAC;UAAA;UAChFY,OAAO,EAAGqB,CAAC,IAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBpG,WAAW,CAAC3G,KAAK,CAAC;UACpB,CAAE;UACF4L,KAAK,EAAC,cAAc;UAAAlC,QAAA,eAGpBjP,OAAA;YAAKuS,KAAK,EAAC,4BAA4B;YAACnC,SAAS,EAAC,SAAS;YAACoC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACC,MAAM,EAAC,cAAc;YAACC,WAAW,EAAE,CAAE;YAAA1D,QAAA,eAC7HjP,OAAA;cAAM4S,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAAsB;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxQ,OAAA;MAAKoQ,SAAS,EAAC,oFAAoF;MAAAnB,QAAA,GAChG,CAAC,MAAM;QACN;QACA,IAAInO,KAAK,CAACG,OAAO,KAAK,mBAAmB,EAAE;UACzC,OAAO,mBAAmB;QAC5B,CAAC,MAAM,IAAIH,KAAK,CAACG,OAAO,KAAK,aAAa,EAAE;UAAA,IAAA8R,cAAA;UAC1C,OAAO,EAAAA,cAAA,GAAAjS,KAAK,CAACE,OAAO,cAAA+R,cAAA,uBAAbA,cAAA,CAAeC,YAAY,KAAI,cAAc;QACtD,CAAC,MAAM,IAAIlS,KAAK,CAACG,OAAO,KAAK,kBAAkB,EAAE;UAAA,IAAAgS,eAAA;UAC/C,OAAO,EAAAA,eAAA,GAAAnS,KAAK,CAACE,OAAO,cAAAiS,eAAA,uBAAbA,eAAA,CAAeC,QAAQ,KAAI,UAAU;QAC9C,CAAC,MAAM,IAAIpS,KAAK,CAACG,OAAO,KAAK,cAAc,EAAE;UAAA,IAAAkS,eAAA,EAAAC,eAAA,EAAAC,eAAA;UAC3C,OAAO,gBAAgB,GAAG,EAAAF,eAAA,GACxBrS,KAAK,CAACE,OAAO,cAAAmS,eAAA,uBAAbA,eAAA,CAAeG,UAAU,GAAAF,eAAA,GACzBtS,KAAK,CAACE,OAAO,cAAAoS,eAAA,uBAAbA,eAAA,CAAeG,UAAU,GAAAF,eAAA,GACzBvS,KAAK,CAACE,OAAO,cAAAqS,eAAA,uBAAbA,eAAA,CAAeG,UAAU,CAC1B,CAACpN,MAAM,CAACuG,OAAO,CAAC,CAACtL,IAAI,CAAC,IAAI,CAAC;QAC9B,CAAC,MAAM,IAAIP,KAAK,CAACG,OAAO,KAAK,YAAY,EAAE;UAAA,IAAAwS,eAAA;UACzC,OAAO,EAAAA,eAAA,GAAA3S,KAAK,CAACE,OAAO,cAAAyS,eAAA,uBAAbA,eAAA,CAAeC,UAAU,KAAI,QAAQ;QAC9C,CAAC,MAAM,IAAI5S,KAAK,CAACG,OAAO,KAAK,iBAAiB,EAAE;UAAA,IAAA0S,eAAA;UAC9C,OAAO,CAAAA,eAAA,GAAA7S,KAAK,CAACE,OAAO,cAAA2S,eAAA,eAAbA,eAAA,CAAeC,WAAW,GAAG,WAAW9S,KAAK,CAACE,OAAO,CAAC4S,WAAW,EAAE,GAAG,iBAAiB;QAChG,CAAC,MAAM;UAAA,IAAAC,eAAA,EAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA;UACL;UACA,OAAO,EAAAH,eAAA,GAAA/S,KAAK,CAACE,OAAO,cAAA6S,eAAA,uBAAbA,eAAA,CAAeX,QAAQ,KACvB,EAAAY,eAAA,GAAAhT,KAAK,CAACE,OAAO,cAAA8S,eAAA,wBAAAC,oBAAA,GAAbD,eAAA,CAAetJ,IAAI,cAAAuJ,oBAAA,uBAAnBA,oBAAA,CAAqB7N,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KACpC,CAAA8N,gBAAA,GAAAlT,KAAK,CAACE,OAAO,cAAAgT,gBAAA,eAAbA,gBAAA,CAAexJ,IAAI,IAAI1J,KAAK,CAACE,OAAO,CAACwJ,IAAI,CAACzF,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC,IACpEjE,KAAK,CAACkI,IAAI;QACnB;MACF,CAAC,EAAE,CAAC,EACHlI,KAAK,CAACmT,SAAS,iBAAIjU,OAAA;QAAKkU,GAAG,EAAEpT,KAAK,CAACmT,SAAU;QAACE,GAAG,EAAE,GAAGrT,KAAK,CAACkI,IAAI,YAAa;QAACoH,SAAS,EAAC;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACgB,GAAA,CAxHIV,cAA6C;EAAA,QAUnBxR,OAAO,EAyBND,OAAO;AAAA;AAAA+U,GAAA,GAnClCtD,cAA6C;AA0HnD,SAAShP,WAAW;AACpB,eAAeA,WAAW;AAAC,IAAAyP,EAAA,EAAA6C,GAAA;AAAAC,YAAA,CAAA9C,EAAA;AAAA8C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}