{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nlet isCallingCanDrag = false;\nlet isCallingIsDragging = false;\nexport class DragSourceMonitorImpl {\n  receiveHandlerId(sourceId) {\n    this.sourceId = sourceId;\n  }\n  getHandlerId() {\n    return this.sourceId;\n  }\n  canDrag() {\n    invariant(!isCallingCanDrag, 'You may not call monitor.canDrag() inside your canDrag() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n    try {\n      isCallingCanDrag = true;\n      return this.internalMonitor.canDragSource(this.sourceId);\n    } finally {\n      isCallingCanDrag = false;\n    }\n  }\n  isDragging() {\n    if (!this.sourceId) {\n      return false;\n    }\n    invariant(!isCallingIsDragging, 'You may not call monitor.isDragging() inside your isDragging() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor');\n    try {\n      isCallingIsDragging = true;\n      return this.internalMonitor.isDraggingSource(this.sourceId);\n    } finally {\n      isCallingIsDragging = false;\n    }\n  }\n  subscribeToStateChange(listener, options) {\n    return this.internalMonitor.subscribeToStateChange(listener, options);\n  }\n  isDraggingSource(sourceId) {\n    return this.internalMonitor.isDraggingSource(sourceId);\n  }\n  isOverTarget(targetId, options) {\n    return this.internalMonitor.isOverTarget(targetId, options);\n  }\n  getTargetIds() {\n    return this.internalMonitor.getTargetIds();\n  }\n  isSourcePublic() {\n    return this.internalMonitor.isSourcePublic();\n  }\n  getSourceId() {\n    return this.internalMonitor.getSourceId();\n  }\n  subscribeToOffsetChange(listener) {\n    return this.internalMonitor.subscribeToOffsetChange(listener);\n  }\n  canDragSource(sourceId) {\n    return this.internalMonitor.canDragSource(sourceId);\n  }\n  canDropOnTarget(targetId) {\n    return this.internalMonitor.canDropOnTarget(targetId);\n  }\n  getItemType() {\n    return this.internalMonitor.getItemType();\n  }\n  getItem() {\n    return this.internalMonitor.getItem();\n  }\n  getDropResult() {\n    return this.internalMonitor.getDropResult();\n  }\n  didDrop() {\n    return this.internalMonitor.didDrop();\n  }\n  getInitialClientOffset() {\n    return this.internalMonitor.getInitialClientOffset();\n  }\n  getInitialSourceClientOffset() {\n    return this.internalMonitor.getInitialSourceClientOffset();\n  }\n  getSourceClientOffset() {\n    return this.internalMonitor.getSourceClientOffset();\n  }\n  getClientOffset() {\n    return this.internalMonitor.getClientOffset();\n  }\n  getDifferenceFromInitialOffset() {\n    return this.internalMonitor.getDifferenceFromInitialOffset();\n  }\n  constructor(manager) {\n    this.sourceId = null;\n    this.internalMonitor = manager.getMonitor();\n  }\n}", "map": {"version": 3, "names": ["invariant", "isCallingCanDrag", "isCallingIsDragging", "DragSourceMonitorImpl", "receiveHandlerId", "sourceId", "getHandlerId", "canDrag", "internalMonitor", "canDragSource", "isDragging", "isDraggingSource", "subscribeToStateChange", "listener", "options", "isOverTarget", "targetId", "getTargetIds", "isSourcePublic", "getSourceId", "subscribeToOffsetChange", "canDropOnTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset", "constructor", "manager", "getMonitor"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd\\src\\internals\\DragSourceMonitorImpl.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type {\n\t<PERSON>agDropManager,\n\tDragDropMonitor,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport type { DragSourceMonitor } from '../types/index.js'\n\nlet isCallingCanDrag = false\nlet isCallingIsDragging = false\n\nexport class DragSourceMonitorImpl implements DragSourceMonitor {\n\tprivate internalMonitor: DragDropMonitor\n\tprivate sourceId: Identifier | null = null\n\n\tpublic constructor(manager: DragDropManager) {\n\t\tthis.internalMonitor = manager.getMonitor()\n\t}\n\n\tpublic receiveHandlerId(sourceId: Identifier | null): void {\n\t\tthis.sourceId = sourceId\n\t}\n\n\tpublic getHandlerId(): Identifier | null {\n\t\treturn this.sourceId\n\t}\n\n\tpublic canDrag(): boolean {\n\t\tinvariant(\n\t\t\t!isCallingCanDrag,\n\t\t\t'You may not call monitor.canDrag() inside your canDrag() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingCanDrag = true\n\t\t\treturn this.internalMonitor.canDragSource(this.sourceId as Identifier)\n\t\t} finally {\n\t\t\tisCallingCanDrag = false\n\t\t}\n\t}\n\n\tpublic isDragging(): boolean {\n\t\tif (!this.sourceId) {\n\t\t\treturn false\n\t\t}\n\t\tinvariant(\n\t\t\t!isCallingIsDragging,\n\t\t\t'You may not call monitor.isDragging() inside your isDragging() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingIsDragging = true\n\t\t\treturn this.internalMonitor.isDraggingSource(this.sourceId)\n\t\t} finally {\n\t\t\tisCallingIsDragging = false\n\t\t}\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: { handlerIds?: Identifier[] },\n\t): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToStateChange(listener, options)\n\t}\n\n\tpublic isDraggingSource(sourceId: Identifier): boolean {\n\t\treturn this.internalMonitor.isDraggingSource(sourceId)\n\t}\n\n\tpublic isOverTarget(\n\t\ttargetId: Identifier,\n\t\toptions?: { shallow: boolean },\n\t): boolean {\n\t\treturn this.internalMonitor.isOverTarget(targetId, options)\n\t}\n\n\tpublic getTargetIds(): Identifier[] {\n\t\treturn this.internalMonitor.getTargetIds()\n\t}\n\n\tpublic isSourcePublic(): boolean | null {\n\t\treturn this.internalMonitor.isSourcePublic()\n\t}\n\n\tpublic getSourceId(): Identifier | null {\n\t\treturn this.internalMonitor.getSourceId()\n\t}\n\n\tpublic subscribeToOffsetChange(listener: Listener): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToOffsetChange(listener)\n\t}\n\n\tpublic canDragSource(sourceId: Identifier): boolean {\n\t\treturn this.internalMonitor.canDragSource(sourceId)\n\t}\n\n\tpublic canDropOnTarget(targetId: Identifier): boolean {\n\t\treturn this.internalMonitor.canDropOnTarget(targetId)\n\t}\n\n\tpublic getItemType(): Identifier | null {\n\t\treturn this.internalMonitor.getItemType()\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.internalMonitor.getItem()\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.internalMonitor.getDropResult()\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.internalMonitor.didDrop()\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialClientOffset()\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialSourceClientOffset()\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getSourceClientOffset()\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getClientOffset()\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getDifferenceFromInitialOffset()\n\t}\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAYhD,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,IAAIC,mBAAmB,GAAG,KAAK;AAE/B,OAAO,MAAMC,qBAAqB;EAQjCC,gBAAuBA,CAACC,QAA2B,EAAQ;IAC1D,IAAI,CAACA,QAAQ,GAAGA,QAAQ;;EAGzBC,YAAmBA,CAAA,EAAsB;IACxC,OAAO,IAAI,CAACD,QAAQ;;EAGrBE,OAAcA,CAAA,EAAY;IACzBP,SAAS,CACR,CAACC,gBAAgB,EACjB,2EAA2E,GAC1E,8EAA8E,CAC/E;IAED,IAAI;MACHA,gBAAgB,GAAG,IAAI;MACvB,OAAO,IAAI,CAACO,eAAe,CAACC,aAAa,CAAC,IAAI,CAACJ,QAAQ,CAAe;KACtE,SAAS;MACTJ,gBAAgB,GAAG,KAAK;;;EAI1BS,UAAiBA,CAAA,EAAY;IAC5B,IAAI,CAAC,IAAI,CAACL,QAAQ,EAAE;MACnB,OAAO,KAAK;;IAEbL,SAAS,CACR,CAACE,mBAAmB,EACpB,iFAAiF,GAChF,8EAA8E,CAC/E;IAED,IAAI;MACHA,mBAAmB,GAAG,IAAI;MAC1B,OAAO,IAAI,CAACM,eAAe,CAACG,gBAAgB,CAAC,IAAI,CAACN,QAAQ,CAAC;KAC3D,SAAS;MACTH,mBAAmB,GAAG,KAAK;;;EAI7BU,sBAA6BA,CAC5BC,QAAkB,EAClBC,OAAuC,EACzB;IACd,OAAO,IAAI,CAACN,eAAe,CAACI,sBAAsB,CAACC,QAAQ,EAAEC,OAAO,CAAC;;EAGtEH,gBAAuBA,CAACN,QAAoB,EAAW;IACtD,OAAO,IAAI,CAACG,eAAe,CAACG,gBAAgB,CAACN,QAAQ,CAAC;;EAGvDU,YAAmBA,CAClBC,QAAoB,EACpBF,OAA8B,EACpB;IACV,OAAO,IAAI,CAACN,eAAe,CAACO,YAAY,CAACC,QAAQ,EAAEF,OAAO,CAAC;;EAG5DG,YAAmBA,CAAA,EAAiB;IACnC,OAAO,IAAI,CAACT,eAAe,CAACS,YAAY,EAAE;;EAG3CC,cAAqBA,CAAA,EAAmB;IACvC,OAAO,IAAI,CAACV,eAAe,CAACU,cAAc,EAAE;;EAG7CC,WAAkBA,CAAA,EAAsB;IACvC,OAAO,IAAI,CAACX,eAAe,CAACW,WAAW,EAAE;;EAG1CC,uBAA8BA,CAACP,QAAkB,EAAe;IAC/D,OAAO,IAAI,CAACL,eAAe,CAACY,uBAAuB,CAACP,QAAQ,CAAC;;EAG9DJ,aAAoBA,CAACJ,QAAoB,EAAW;IACnD,OAAO,IAAI,CAACG,eAAe,CAACC,aAAa,CAACJ,QAAQ,CAAC;;EAGpDgB,eAAsBA,CAACL,QAAoB,EAAW;IACrD,OAAO,IAAI,CAACR,eAAe,CAACa,eAAe,CAACL,QAAQ,CAAC;;EAGtDM,WAAkBA,CAAA,EAAsB;IACvC,OAAO,IAAI,CAACd,eAAe,CAACc,WAAW,EAAE;;EAG1CC,OAAcA,CAAA,EAAQ;IACrB,OAAO,IAAI,CAACf,eAAe,CAACe,OAAO,EAAE;;EAGtCC,aAAoBA,CAAA,EAAQ;IAC3B,OAAO,IAAI,CAAChB,eAAe,CAACgB,aAAa,EAAE;;EAG5CC,OAAcA,CAAA,EAAY;IACzB,OAAO,IAAI,CAACjB,eAAe,CAACiB,OAAO,EAAE;;EAGtCC,sBAA6BA,CAAA,EAAmB;IAC/C,OAAO,IAAI,CAAClB,eAAe,CAACkB,sBAAsB,EAAE;;EAGrDC,4BAAmCA,CAAA,EAAmB;IACrD,OAAO,IAAI,CAACnB,eAAe,CAACmB,4BAA4B,EAAE;;EAG3DC,qBAA4BA,CAAA,EAAmB;IAC9C,OAAO,IAAI,CAACpB,eAAe,CAACoB,qBAAqB,EAAE;;EAGpDC,eAAsBA,CAAA,EAAmB;IACxC,OAAO,IAAI,CAACrB,eAAe,CAACqB,eAAe,EAAE;;EAG9CC,8BAAqCA,CAAA,EAAmB;IACvD,OAAO,IAAI,CAACtB,eAAe,CAACsB,8BAA8B,EAAE;;EAxH7DC,YAAmBC,OAAwB,EAAE;IAF7C,KAAQ3B,QAAQ,GAAsB,IAAI;IAGzC,IAAI,CAACG,eAAe,GAAGwB,OAAO,CAACC,UAAU,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}