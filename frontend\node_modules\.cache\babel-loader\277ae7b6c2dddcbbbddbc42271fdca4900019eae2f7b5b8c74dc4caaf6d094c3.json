{"ast": null, "code": "export class MonotonicInterpolant {\n  interpolate(x) {\n    const {\n      xs,\n      ys,\n      c1s,\n      c2s,\n      c3s\n    } = this;\n    // The rightmost point in the dataset should give an exact result\n    let i = xs.length - 1;\n    if (x === xs[i]) {\n      return ys[i];\n    }\n    // Search for the interval x is in, returning the corresponding y if x is one of the original xs\n    let low = 0;\n    let high = c3s.length - 1;\n    let mid;\n    while (low <= high) {\n      mid = Math.floor(0.5 * (low + high));\n      const xHere = xs[mid];\n      if (xHere < x) {\n        low = mid + 1;\n      } else if (xHere > x) {\n        high = mid - 1;\n      } else {\n        return ys[mid];\n      }\n    }\n    i = Math.max(0, high);\n    // Interpolate\n    const diff = x - xs[i];\n    const diffSq = diff * diff;\n    return ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq;\n  }\n  constructor(xs, ys) {\n    const {\n      length\n    } = xs;\n    // Rearrange xs and ys so that xs is sorted\n    const indexes = [];\n    for (let i = 0; i < length; i++) {\n      indexes.push(i);\n    }\n    indexes.sort((a, b) => xs[a] < xs[b] ? -1 : 1);\n    // Get consecutive differences and slopes\n    const dys = [];\n    const dxs = [];\n    const ms = [];\n    let dx;\n    let dy;\n    for (let i1 = 0; i1 < length - 1; i1++) {\n      dx = xs[i1 + 1] - xs[i1];\n      dy = ys[i1 + 1] - ys[i1];\n      dxs.push(dx);\n      dys.push(dy);\n      ms.push(dy / dx);\n    }\n    // Get degree-1 coefficients\n    const c1s = [ms[0]];\n    for (let i2 = 0; i2 < dxs.length - 1; i2++) {\n      const m2 = ms[i2];\n      const mNext = ms[i2 + 1];\n      if (m2 * mNext <= 0) {\n        c1s.push(0);\n      } else {\n        dx = dxs[i2];\n        const dxNext = dxs[i2 + 1];\n        const common = dx + dxNext;\n        c1s.push(3 * common / ((common + dxNext) / m2 + (common + dx) / mNext));\n      }\n    }\n    c1s.push(ms[ms.length - 1]);\n    // Get degree-2 and degree-3 coefficients\n    const c2s = [];\n    const c3s = [];\n    let m;\n    for (let i3 = 0; i3 < c1s.length - 1; i3++) {\n      m = ms[i3];\n      const c1 = c1s[i3];\n      const invDx = 1 / dxs[i3];\n      const common = c1 + c1s[i3 + 1] - m - m;\n      c2s.push((m - c1 - common) * invDx);\n      c3s.push(common * invDx * invDx);\n    }\n    this.xs = xs;\n    this.ys = ys;\n    this.c1s = c1s;\n    this.c2s = c2s;\n    this.c3s = c3s;\n  }\n}", "map": {"version": 3, "names": ["MonotonicInterpolant", "interpolate", "x", "xs", "ys", "c1s", "c2s", "c3s", "i", "length", "low", "high", "mid", "Math", "floor", "xHere", "max", "diff", "diffSq", "constructor", "indexes", "push", "sort", "a", "b", "dys", "dxs", "ms", "dx", "dy", "i1", "i2", "m2", "mNext", "dxNext", "common", "m", "i3", "c1", "invDx"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd-html5-backend\\src\\MonotonicInterpolant.ts"], "sourcesContent": ["export class MonotonicInterpolant {\n\tprivate xs: any\n\tprivate ys: any\n\tprivate c1s: any\n\tprivate c2s: any\n\tprivate c3s: any\n\n\tpublic constructor(xs: number[], ys: number[]) {\n\t\tconst { length } = xs\n\n\t\t// Rearrange xs and ys so that xs is sorted\n\t\tconst indexes = []\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tindexes.push(i)\n\t\t}\n\t\tindexes.sort((a, b) => ((xs[a] as number) < (xs[b] as number) ? -1 : 1))\n\n\t\t// Get consecutive differences and slopes\n\t\tconst dys = []\n\t\tconst dxs = []\n\t\tconst ms = []\n\t\tlet dx\n\t\tlet dy\n\t\tfor (let i = 0; i < length - 1; i++) {\n\t\t\tdx = (xs[i + 1] as number) - (xs[i] as number)\n\t\t\tdy = (ys[i + 1] as number) - (ys[i] as number)\n\t\t\tdxs.push(dx)\n\t\t\tdys.push(dy)\n\t\t\tms.push(dy / dx)\n\t\t}\n\n\t\t// Get degree-1 coefficients\n\t\tconst c1s = [ms[0]]\n\t\tfor (let i = 0; i < dxs.length - 1; i++) {\n\t\t\tconst m2 = ms[i] as number\n\t\t\tconst mNext = ms[i + 1] as number\n\t\t\tif (m2 * mNext <= 0) {\n\t\t\t\tc1s.push(0)\n\t\t\t} else {\n\t\t\t\tdx = dxs[i] as number\n\t\t\t\tconst dxNext = dxs[i + 1] as number\n\t\t\t\tconst common = dx + dxNext\n\t\t\t\tc1s.push(\n\t\t\t\t\t(3 * common) / ((common + dxNext) / m2 + (common + dx) / mNext),\n\t\t\t\t)\n\t\t\t}\n\t\t}\n\t\tc1s.push(ms[ms.length - 1])\n\n\t\t// Get degree-2 and degree-3 coefficients\n\t\tconst c2s = []\n\t\tconst c3s = []\n\t\tlet m\n\t\tfor (let i = 0; i < c1s.length - 1; i++) {\n\t\t\tm = ms[i] as number\n\t\t\tconst c1 = c1s[i] as number\n\t\t\tconst invDx = 1 / (dxs[i] as number)\n\t\t\tconst common = c1 + (c1s[i + 1] as number) - m - m\n\t\t\tc2s.push((m - c1 - common) * invDx)\n\t\t\tc3s.push(common * invDx * invDx)\n\t\t}\n\n\t\tthis.xs = xs\n\t\tthis.ys = ys\n\t\tthis.c1s = c1s\n\t\tthis.c2s = c2s\n\t\tthis.c3s = c3s\n\t}\n\n\tpublic interpolate(x: number): number {\n\t\tconst { xs, ys, c1s, c2s, c3s } = this\n\n\t\t// The rightmost point in the dataset should give an exact result\n\t\tlet i = xs.length - 1\n\t\tif (x === xs[i]) {\n\t\t\treturn ys[i]\n\t\t}\n\n\t\t// Search for the interval x is in, returning the corresponding y if x is one of the original xs\n\t\tlet low = 0\n\t\tlet high = c3s.length - 1\n\t\tlet mid\n\t\twhile (low <= high) {\n\t\t\tmid = Math.floor(0.5 * (low + high))\n\t\t\tconst xHere = xs[mid]\n\t\t\tif (xHere < x) {\n\t\t\t\tlow = mid + 1\n\t\t\t} else if (xHere > x) {\n\t\t\t\thigh = mid - 1\n\t\t\t} else {\n\t\t\t\treturn ys[mid]\n\t\t\t}\n\t\t}\n\t\ti = Math.max(0, high)\n\n\t\t// Interpolate\n\t\tconst diff = x - xs[i]\n\t\tconst diffSq = diff * diff\n\t\treturn ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq\n\t}\n}\n"], "mappings": "AAAA,OAAO,MAAMA,oBAAoB;EAqEhCC,WAAkBA,CAACC,CAAS,EAAU;IACrC,MAAM;MAAEC,EAAE;MAAEC,EAAE;MAAEC,GAAG;MAAEC,GAAG;MAAEC;IAAG,CAAE,GAAG,IAAI;IAEtC;IACA,IAAIC,CAAC,GAAGL,EAAE,CAACM,MAAM,GAAG,CAAC;IACrB,IAAIP,CAAC,KAAKC,EAAE,CAACK,CAAC,CAAC,EAAE;MAChB,OAAOJ,EAAE,CAACI,CAAC,CAAC;;IAGb;IACA,IAAIE,GAAG,GAAG,CAAC;IACX,IAAIC,IAAI,GAAGJ,GAAG,CAACE,MAAM,GAAG,CAAC;IACzB,IAAIG,GAAG;IACP,OAAOF,GAAG,IAAIC,IAAI,EAAE;MACnBC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,IAAIJ,GAAG,GAAGC,IAAI,CAAC,CAAC;MACpC,MAAMI,KAAK,GAAGZ,EAAE,CAACS,GAAG,CAAC;MACrB,IAAIG,KAAK,GAAGb,CAAC,EAAE;QACdQ,GAAG,GAAGE,GAAG,GAAG,CAAC;OACb,MAAM,IAAIG,KAAK,GAAGb,CAAC,EAAE;QACrBS,IAAI,GAAGC,GAAG,GAAG,CAAC;OACd,MAAM;QACN,OAAOR,EAAE,CAACQ,GAAG,CAAC;;;IAGhBJ,CAAC,GAAGK,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEL,IAAI,CAAC;IAErB;IACA,MAAMM,IAAI,GAAGf,CAAC,GAAGC,EAAE,CAACK,CAAC,CAAC;IACtB,MAAMU,MAAM,GAAGD,IAAI,GAAGA,IAAI;IAC1B,OAAOb,EAAE,CAACI,CAAC,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC,GAAGS,IAAI,GAAGX,GAAG,CAACE,CAAC,CAAC,GAAGU,MAAM,GAAGX,GAAG,CAACC,CAAC,CAAC,GAAGS,IAAI,GAAGC,MAAM;;EA3FxEC,YAAmBhB,EAAY,EAAEC,EAAY,EAAE;IAC9C,MAAM;MAAEK;IAAM,CAAE,GAAGN,EAAE;IAErB;IACA,MAAMiB,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;MAChCY,OAAO,CAACC,IAAI,CAACb,CAAC,CAAC;;IAEhBY,OAAO,CAACE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMrB,EAAG,CAACoB,CAAC,CAAC,GAAepB,EAAE,CAACqB,CAAC,CAAC,GAAc,CAAC,CAAC,GAAG,CAAE,CAAC;IAExE;IACA,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,GAAG,GAAG,EAAE;IACd,MAAMC,EAAE,GAAG,EAAE;IACb,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,KAAK,IAAIC,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGrB,MAAM,GAAG,CAAC,EAAEqB,EAAC,EAAE,EAAE;MACpCF,EAAE,GAAGzB,EAAG,CAAC2B,EAAC,GAAG,CAAC,CAAC,GAAe3B,EAAE,CAAC2B,EAAC,CAAC;MACnCD,EAAE,GAAGzB,EAAG,CAAC0B,EAAC,GAAG,CAAC,CAAC,GAAe1B,EAAE,CAAC0B,EAAC,CAAC;MACnCJ,GAAG,CAACL,IAAI,CAACO,EAAE,CAAC;MACZH,GAAG,CAACJ,IAAI,CAACQ,EAAE,CAAC;MACZF,EAAE,CAACN,IAAI,CAACQ,EAAE,GAAGD,EAAE,CAAC;;IAGjB;IACA,MAAMvB,GAAG,GAAG,CAACsB,EAAE,CAAC,CAAC,CAAC,CAAC;IACnB,KAAK,IAAII,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGL,GAAG,CAACjB,MAAM,GAAG,CAAC,EAAEsB,EAAC,EAAE,EAAE;MACxC,MAAMC,EAAE,GAAGL,EAAE,CAACI,EAAC,CAAC;MAChB,MAAME,KAAK,GAAGN,EAAE,CAACI,EAAC,GAAG,CAAC,CAAC;MACvB,IAAIC,EAAE,GAAGC,KAAK,IAAI,CAAC,EAAE;QACpB5B,GAAG,CAACgB,IAAI,CAAC,CAAC,CAAC;OACX,MAAM;QACNO,EAAE,GAAGF,GAAG,CAACK,EAAC,CAAC;QACX,MAAMG,MAAM,GAAGR,GAAG,CAACK,EAAC,GAAG,CAAC,CAAC;QACzB,MAAMI,MAAM,GAAGP,EAAE,GAAGM,MAAM;QAC1B7B,GAAG,CAACgB,IAAI,CACP,CAAE,GAAGc,MAAM,IAAK,CAACA,MAAM,GAAGD,MAAM,IAAIF,EAAE,GAAG,CAACG,MAAM,GAAGP,EAAE,IAAIK,KAAK,CAAC,CAC/D;;;IAGH5B,GAAG,CAACgB,IAAI,CAACM,EAAE,CAACA,EAAE,CAAClB,MAAM,GAAG,CAAC,CAAC,CAAC;IAE3B;IACA,MAAMH,GAAG,GAAG,EAAE;IACd,MAAMC,GAAG,GAAG,EAAE;IACd,IAAI6B,CAAC;IACL,KAAK,IAAIC,EAAC,GAAG,CAAC,EAAEA,EAAC,GAAGhC,GAAG,CAACI,MAAM,GAAG,CAAC,EAAE4B,EAAC,EAAE,EAAE;MACxCD,CAAC,GAAGT,EAAE,CAACU,EAAC,CAAC;MACT,MAAMC,EAAE,GAAGjC,GAAG,CAACgC,EAAC,CAAC;MACjB,MAAME,KAAK,GAAG,CAAC,GAAIb,GAAG,CAACW,EAAC,CAAC;MACzB,MAAMF,MAAM,GAAGG,EAAE,GAAIjC,GAAG,CAACgC,EAAC,GAAG,CAAC,CAAC,GAAcD,CAAC,GAAGA,CAAC;MAClD9B,GAAG,CAACe,IAAI,CAAC,CAACe,CAAC,GAAGE,EAAE,GAAGH,MAAM,IAAII,KAAK,CAAC;MACnChC,GAAG,CAACc,IAAI,CAACc,MAAM,GAAGI,KAAK,GAAGA,KAAK,CAAC;;IAGjC,IAAI,CAACpC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}