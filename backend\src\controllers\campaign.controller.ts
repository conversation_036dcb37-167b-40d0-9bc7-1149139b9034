import Bull from 'bull';
import {
  NextFunction,
  Request,
  Response,
} from 'express';
import mongoose from 'mongoose';

import CampaignRecipient from '../models/campaign-recipient.model';
import Campaign from '../models/campaign.model';
import Contact from '../models/contact.model';
import User from '../models/user.model';
// Import the shared emailQueue instance
import emailQueue from '../queue';
import { createError } from '../utils/error.util';

// Create a campaign
export const createCampaign = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const {
      name,
      subject,
      fromName,
      fromEmail,
      replyTo,
      htmlContent,
      cssContent,
      scheduledFor,
      recipientList,
      emailContents,
      schedule
    } = req.body;

    const userId = req.user?.id;
    if (!userId) {
      return next(createError(401, 'User not authenticated'));
    }

    // Validate required fields
    if (!name || !subject || !fromName || !htmlContent) {
      return next(createError(400, 'Missing required fields'));
    }
    if (!recipientList || !Array.isArray(recipientList)) {
       return next(createError(400, 'Missing or invalid recipientList'));
    }

    // Validate sequence data if provided
    if (emailContents && !Array.isArray(emailContents)) {
        return next(createError(400, 'Invalid emailContents format'));
    }
    if (schedule && typeof schedule !== 'object') {
        return next(createError(400, 'Invalid schedule format'));
    }
    // Add more specific validation for schedule contents if needed

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Determine base html/css from the first email if sequence exists
    const baseHtmlContent = (emailContents && emailContents.length > 0 && emailContents[0]?.html) 
                             ? emailContents[0].html 
                             : htmlContent; // Use the destructured htmlContent from req.body

    // Similarly for CSS, though less critical for this issue
    const baseCssContent = (emailContents && emailContents.length > 0 && emailContents[0]?.css)
                            ? emailContents[0].css
                            : cssContent;

    if (!baseHtmlContent) {
        return next(createError(400, 'Missing email content (htmlContent or valid emailContents[0].html)'));
    }

    // Create campaign FIRST
    const campaign = new Campaign({
      userId,
      name,
      subject,
      fromName,
      fromEmail: fromEmail || `noreply@${user.domain?.name || 'example.com'}`,
      replyTo: replyTo || fromEmail || `noreply@${user.domain?.name || 'example.com'}`,
      htmlContent: baseHtmlContent, // Store first email content here for reference/single sends
      cssContent: baseCssContent,
      emailContents: emailContents, // Store the full sequence
      schedule: schedule, // Store the schedule intervals
      status: scheduledFor ? 'scheduled' : 'draft',
      scheduledFor: scheduledFor ? new Date(scheduledFor) : null,
      sentCount: 0,
      errorCount: 0
    });

    await campaign.save();
    console.log(`[createCampaign] Saved campaign ${campaign._id}`);

    // --- Process Recipients (Find or Create Contacts) ---
    const validContactIds: mongoose.Types.ObjectId[] = [];
    const emailsToFetch = new Set<string>();

    if (recipientList.length > 0) {
      console.log(`[createCampaign] Processing ${recipientList.length} recipients for campaign ${campaign._id}`);
      for (const recipient of recipientList) {
        if (recipient.id) {
          // If ID is provided (from existing contact selection)
          // Validate it's a valid ObjectId format before adding
          if (mongoose.Types.ObjectId.isValid(recipient.id)) {
             validContactIds.push(new mongoose.Types.ObjectId(recipient.id));
          } else {
             console.warn(`[createCampaign] Invalid ObjectId format for recipient ID: ${recipient.id}`);
          }
        } else if (recipient.email) {
          // If ID is null (manual entry), find or create contact by email
          try {
            let contact = await Contact.findOne({ email: recipient.email, userId: userId });

            if (!contact) {
              // Create new contact if not found
              console.log(`[createCampaign] Contact not found for email ${recipient.email}. Creating new contact.`);
              contact = new Contact({
                userId: userId,
                email: recipient.email,
                firstName: recipient.name?.split(' ')[0] || '',
                lastName: recipient.name?.split(' ').slice(1).join(' ') || '',
                // Add any other default fields for Contact if needed
              });
              await contact.save();
              console.log(`[createCampaign] Created new contact ${contact._id} for email ${recipient.email}`);
            }
            // Assert the type via unknown first, as suggested by TS error TS2352
            validContactIds.push(contact._id as unknown as mongoose.Types.ObjectId); 
          } catch (contactError: any) {
            console.error(`[createCampaign] Error finding/creating contact for email ${recipient.email}:`, contactError.message);
            // Skip this recipient if contact processing fails
          }
        } else {
           console.warn('[createCampaign] Skipping recipient with missing id and email:', recipient);
        }
      }
      console.log(`[createCampaign] Finished processing recipients. Valid contact IDs collected: ${validContactIds.length}`);
    } else {
        console.log(`[createCampaign] No recipients provided in recipientList for campaign ${campaign._id}`);
    }

    // --- Associate Valid Contacts with Campaign --- 
    if (validContactIds.length > 0) {
      try {
         // Ensure IDs are unique before fetching details
         const uniqueValidContactIds = [...new Set(validContactIds.map(id => id.toString()))]
                                         .map(idStr => new mongoose.Types.ObjectId(idStr));

         console.log(`[createCampaign] Associating ${uniqueValidContactIds.length} unique contacts with campaign ${campaign._id}`);

        // Fetch email details for all valid contacts (needed for CampaignRecipient)
        const contactsDetails = await Contact.find({
          _id: { $in: uniqueValidContactIds },
          userId: userId // Double-check ownership
        }).select('_id email');

        if (contactsDetails.length !== uniqueValidContactIds.length) {
           console.warn(`[createCampaign] Mismatch fetching contact details. Expected: ${uniqueValidContactIds.length}, Found: ${contactsDetails.length}`);
           // This might indicate an issue if a contact was deleted between findOrCreate and fetch
        }

        const contactEmailMap = new Map(contactsDetails.map(c => [c._id.toString(), c.email]));

        const campaignRecipientsToCreate = uniqueValidContactIds
            .filter(contactId => contactEmailMap.has(contactId.toString())) // Ensure we have email for the ID
            .map((contactId: mongoose.Types.ObjectId) => ({
              campaignId: campaign._id,
              recipientId: contactId, // This is now always a valid Contact ID
              email: contactEmailMap.get(contactId.toString()), // Get email from fetched details
              userId: userId,
              status: 'pending' // Initial status
            }));

         if (campaignRecipientsToCreate.length > 0) {
            await CampaignRecipient.insertMany(campaignRecipientsToCreate);
            console.log(`[createCampaign] Successfully created ${campaignRecipientsToCreate.length} CampaignRecipient documents for campaign ${campaign._id}`);
         } else {
             console.warn(`[createCampaign] No CampaignRecipient documents were created for campaign ${campaign._id}, potentially due to missing contact details after processing.`);
         }

      } catch (associationError) {
        console.error(`[createCampaign] Error associating contacts/creating CampaignRecipients for campaign ${campaign._id}:`, associationError);
        // Campaign is already created, but association failed. Logged the error.
      }
    }

    // --- Schedule Job (If Applicable) --- 
    if (scheduledFor) {
      const scheduledDate = new Date(scheduledFor);
      const now = new Date();
      const delay = Math.max(0, scheduledDate.getTime() - now.getTime());

      console.log(`[createCampaign] Scheduling job for campaign ${campaign._id}, emailIndex: 0 with delay: ${delay}ms`);
      await emailQueue.add(
        {
          campaignId: campaign._id,
          emailIndex: 0, // Start with the first email
          batchIndex: 0,
          batchSize: 100 
        },
        {
          delay
        }
      );
    }

    res.status(201).json({
      success: true,
      message: 'Campaign created successfully',
      data: {
        campaign // Return the base campaign object
      }
    });
  } catch (error) {
    console.error('[createCampaign] Unhandled error:', error);
    next(error);
  }
};

// Get all campaigns with recipient counts
export const getCampaigns = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return next(createError(401, 'User not authenticated'));
    }
    console.log(`[getCampaigns] Fetching campaigns with counts for userId: ${userId}`);

    // Use aggregation pipeline to get campaigns and their recipient counts
    const campaigns = await Campaign.aggregate([
      {
        $match: { userId: new mongoose.Types.ObjectId(userId) } // Match campaigns for the user
      },
      {
        $lookup: {
          from: 'campaignrecipients', // The name of the CampaignRecipients collection in MongoDB (usually lowercase plural)
          localField: '_id', // Field from the Campaign collection
          foreignField: 'campaignId', // Field from the CampaignRecipient collection
          as: 'recipients' // Name of the new array field to add
        }
      },
      {
        $addFields: {
          // Add a new field 'recipientCountActual' with the size of the 'recipients' array
          recipientCountActual: { $size: '$recipients' }
        }
      },
      {
        $project: {
          recipients: 0 // Optionally remove the large recipients array from the final output
        }
      },
      {
        $sort: { createdAt: -1 } // Sort by creation date
      }
    ]);

    console.log(`[getCampaigns] Found ${campaigns.length} campaigns for userId: ${userId} with counts.`);

    res.status(200).json({
      success: true,
      data: {
        campaigns
      }
    });
  } catch (error) {
    console.error('[getCampaigns] Error fetching campaigns with counts:', error);
    next(error);
  }
};

// Get a campaign by ID with recipient count
export const getCampaign = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // --- Start Debug Logging ---
    console.log(`[getCampaign] Received request for campaign ID: ${id}`);
    console.log(`[getCampaign] User ID from token: ${userId}`);
    // --- End Debug Logging ---

    if (!userId) {
        // Logged above
        return next(createError(401, 'User not authenticated'));
    }
    if (!id || !mongoose.Types.ObjectId.isValid(id)) {
      console.error(`[getCampaign] Invalid Campaign ID format received: ${id}`);
      return next(createError(400, 'Invalid or missing Campaign ID'));
    }

    const campaignObjectId = new mongoose.Types.ObjectId(id);
    const userObjectId = new mongoose.Types.ObjectId(userId);

    // --- Start Debug Logging ---
    console.log(`[getCampaign] Attempting findOne with _id: ${campaignObjectId} and userId: ${userObjectId}`);
    // --- End Debug Logging ---

    // --- Simplified Query --- 
    const campaign = await Campaign.findOne({
      _id: campaignObjectId,
      userId: userObjectId
    });
    // --- End Simplified Query --- 

    if (!campaign) {
       console.log(`[getCampaign] Campaign ${id} not found for user ${userId} using findOne.`);
       return next(createError(404, 'Campaign not found'));
    }

    // --- Get Recipient Count ---
    let recipientCount = 0;
    try {
      recipientCount = await CampaignRecipient.countDocuments({
        campaignId: campaignObjectId,
        // Optional: Add userId check here too for extra safety if CampaignRecipient has it
        // userId: userObjectId 
      });
      console.log(`[getCampaign] Found ${recipientCount} recipients for campaign ${id}.`);
    } catch (countError) {
      console.error(`[getCampaign] Error counting recipients for campaign ${id}:`, countError);
      // Don't fail the request, just return 0 count
    }
    // --- End Get Recipient Count ---

    console.log(`[getCampaign] Found campaign ${id} using findOne.`);

    // --- Explicitly construct the response object ---
    const campaignResponseData = {
      _id: campaign._id,
      userId: campaign.userId,
      name: campaign.name,
      subject: campaign.subject,
      fromName: campaign.fromName,
      fromEmail: campaign.fromEmail,
      replyTo: campaign.replyTo,
      htmlContent: campaign.htmlContent,
      // Include other fields from ICampaign interface as needed by frontend
      status: campaign.status,
      scheduledFor: campaign.scheduledFor,
      startedAt: campaign.startedAt,
      completedAt: campaign.completedAt,
      sentCount: campaign.sentCount,
      openCount: campaign.openCount,
      clickCount: campaign.clickCount,
      bounceCount: campaign.bounceCount,
      complaintCount: campaign.complaintCount,
      openRate: campaign.openRate,
      clickRate: campaign.clickRate,
      mosaicoJson: campaign.mosaicoJson, // Ensure Mosaico JSON is included if it exists
      createdAt: campaign.createdAt,
      updatedAt: campaign.updatedAt,
      // Add the calculated count
      recipientCountActual: recipientCount 
    };
    // --- End explicit construction ---

    res.status(200).json({
      success: true,
      data: {
        campaign: campaignResponseData // Send the manually constructed object
      }
    });
  } catch (error) {
    console.error(`[getCampaign] Error fetching campaign ${req.params.id}:`, error);
    next(error);
  }
};

// Update a campaign
export const updateCampaign = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Extract fields, including mosaico data
    const { mosaicoJson, htmlContent, ...otherUpdateData } = req.body;

    // Find campaign
    const campaign = await Campaign.findOne({ _id: id, userId });
    if (!campaign) {
      return next(createError(404, 'Campaign not found'));
    }

    // Prevent updating campaigns that are already sent/sending
    if (campaign.status === 'sending' || campaign.status === 'completed' || campaign.status === 'sent') {
      return next(createError(400, `Cannot update campaign in status: ${campaign.status}`));
    }

    // Prepare update payload
    const updatePayload: any = { 
        ...otherUpdateData // Include other fields passed in the body
    };
    if (mosaicoJson !== undefined) {
        updatePayload.mosaicoJson = mosaicoJson;
    }
    if (htmlContent !== undefined) {
        updatePayload.htmlContent = htmlContent; // Update compiled HTML
    }
    // Explicitly remove cssContent if it exists in otherUpdateData (it shouldn't)
    delete updatePayload.cssContent;

    // Update campaign using findByIdAndUpdate
    const updatedCampaign = await Campaign.findByIdAndUpdate(
      id,
      { $set: updatePayload },
      { new: true } // Return the updated document
    );

    res.status(200).json({
      success: true,
      message: 'Campaign updated successfully',
      data: {
        campaign: updatedCampaign
      }
    });
  } catch (error) {
    next(error);
  }
};

// Schedule a campaign
export const scheduleCampaign = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;
    const { scheduledFor } = req.body;
    const userId = req.user?.id;

    if (!scheduledFor) {
      return next(createError(400, 'Scheduled date is required'));
    }

    // Validate scheduled date
    const scheduledDate = new Date(scheduledFor);
    const now = new Date();

    if (scheduledDate <= now) {
      return next(createError(400, 'Scheduled date must be in the future'));
    }

    // Find campaign
    const campaign = await Campaign.findOne({ _id: id, userId });
    if (!campaign) {
      return next(createError(404, 'Campaign not found'));
    }

    // Check if campaign is already processing
    if (['sending', 'completed', 'failed', 'cancelled'].includes(campaign.status)) {
      return next(createError(400, `Cannot schedule campaign in '${campaign.status}' status`));
    }

    // Update campaign status and scheduled time
    campaign.status = 'scheduled';
    campaign.scheduledFor = scheduledDate;
    await campaign.save();

    // Remove any existing delayed jobs for this campaign to avoid duplicates
    const delayedJobs = await emailQueue.getJobs(['delayed']);
    for (const job of delayedJobs) {
      if (job.data.campaignId.toString() === id) {
        console.log(`[scheduleCampaign] Removing existing delayed job ${job.id} for campaign ${id}`);
        await job.remove();
      }
    }

    // Create a new job using the IMPORTED emailQueue
    const delay = Math.max(0, scheduledDate.getTime() - now.getTime());
    console.log(`[scheduleCampaign] Scheduling job for campaign ${campaign._id}, emailIndex: 0 with delay: ${delay}ms`);
    await emailQueue.add(
      {
        campaignId: campaign._id,
        emailIndex: 0, // Start with the first email
        batchIndex: 0,
        batchSize: 100
      },
      {
        delay
      }
    );

    res.status(200).json({
      success: true,
      message: 'Campaign scheduled successfully',
      data: {
        campaign
      }
    });
  } catch (error) {
    next(error);
  }
};

// Cancel a scheduled campaign
export const cancelScheduledCampaign = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Find campaign
    const campaign = await Campaign.findOne({ _id: id, userId });
    if (!campaign) {
      return next(createError(404, 'Campaign not found'));
    }

    // Check if campaign is scheduled
    if (campaign.status !== 'scheduled') {
      return next(createError(400, 'Campaign is not scheduled'));
    }

    // Update campaign status to draft and clear schedule
    campaign.status = 'draft'; // Changed from 'cancelled' to 'draft'
    campaign.scheduledFor = undefined;
    await campaign.save();

    // Remove scheduled jobs using the IMPORTED emailQueue
    const jobs = await emailQueue.getJobs(['delayed']);
    for (const job of jobs) {
      // Ensure IDs are compared correctly (e.g., string vs ObjectId)
      if (job.data.campaignId.toString() === id) {
        console.log(`[cancelScheduledCampaign] Removing delayed job ${job.id} for campaign ${id}`);
        await job.remove();
      }
    }

    res.status(200).json({
      success: true,
      message: 'Campaign schedule cancelled successfully',
      data: {
        campaign
      }
    });
  } catch (error) {
    next(error);
  }
};

// Send a campaign immediately
export const sendCampaignNow = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Find campaign
    const campaign = await Campaign.findOne({ _id: id, userId });
    if (!campaign) {
      return next(createError(404, 'Campaign not found'));
    }

    // Check campaign status
    if (['sending', 'completed', 'scheduled'].includes(campaign.status)) {
      return next(createError(400, `Campaign cannot be sent now (status: ${campaign.status})`));
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Check if user has a verified domain
    if (!user.domain || user.domain.status !== 'active') {
      return next(createError(400, 'You need a verified domain to send emails'));
    }

    // Update campaign status immediately before adding job
    campaign.status = 'sending'; // Changed from 'pending' to 'sending'
    campaign.scheduledFor = undefined; // Clear schedule if any
    campaign.startedAt = new Date(); // Mark when sending was initiated
    await campaign.save();

    // Add job to email queue for immediate processing (no delay)
    console.log(`[sendCampaignNow] Adding immediate job for campaign ${campaign._id}, emailIndex: 0`);
    await emailQueue.add({
      campaignId: campaign._id,
      emailIndex: 0, // Start with the first email
      batchIndex: 0,
      batchSize: 100
    });

    res.status(200).json({
      success: true,
      message: 'Campaign sending started successfully',
      data: {
        campaign
      }
    });
  } catch (error) {
    next(error);
  }
};

// Delete a campaign
export const deleteCampaign = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void | Response> => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // Find campaign
    const campaign = await Campaign.findOne({ _id: id, userId });
    if (!campaign) {
      return next(createError(404, 'Campaign not found'));
    }

    const currentStatus = campaign.status;

    // Handle 'sending' or 'active' jobs - Prevent deletion, maybe allow cancellation first
    if (currentStatus === 'sending') {
      // Optionally add more complex logic here if needed
      return next(createError(400, 'Cannot delete a campaign while it is actively sending. Try cancelling first if possible.'));
    }

    // --- Deletion Logic ---
    // Mark as deleted or cancelled first?
    // For simplicity, directly delete if not sending.

    // Remove associated jobs (delayed, waiting, active) - Best effort
    try {
      const jobTypes: Bull.JobStatus[] = ['delayed', 'waiting', 'active'];
      const jobs = await emailQueue.getJobs(jobTypes);
      console.log(`[deleteCampaign] Found ${jobs.length} potential jobs across types: ${jobTypes.join(', ')} for campaign ${id}`);
      let removedJobs = 0;
      for (const job of jobs) {
        if (job.data.campaignId.toString() === id) {
          await job.remove();
          removedJobs++;
          console.log(`[deleteCampaign] Removed job ${job.id} (Type: ${await job.getState()}) for campaign ${id}`);
        }
      }
      if (removedJobs > 0) {
        console.log(`[deleteCampaign] Cleaned up ${removedJobs} associated queue jobs for campaign ${id}.`);
      }
    } catch (jobError) {
      console.error(`[deleteCampaign] Error cleaning up jobs for campaign ${id}:`, jobError);
      // Don't prevent deletion if job cleanup fails, just log it.
    }

    // Delete campaign document
    await Campaign.findByIdAndDelete(id);
    // Optionally delete associated CampaignRecipients
    // await CampaignRecipient.deleteMany({ campaignId: id });

    res.status(200).json({
      success: true,
      message: `Campaign '${currentStatus}' deleted successfully`
    });

  } catch (error) {
    next(error);
  }
};
