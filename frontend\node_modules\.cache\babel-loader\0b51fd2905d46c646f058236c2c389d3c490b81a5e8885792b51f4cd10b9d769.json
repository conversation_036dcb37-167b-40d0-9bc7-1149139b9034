{"ast": null, "code": "import * as t from \"react\";\nimport { env as f } from '../utils/env.js';\nfunction s() {\n  let r = typeof document == \"undefined\";\n  return \"useSyncExternalStore\" in t ? (o => o.useSyncExternalStore)(t)(() => () => {}, () => !1, () => !r) : !1;\n}\nfunction l() {\n  let r = s(),\n    [e, n] = t.useState(f.isHandoffComplete);\n  return e && f.isHandoffComplete === !1 && n(!1), t.useEffect(() => {\n    e !== !0 && n(!0);\n  }, [e]), t.useEffect(() => f.handoff(), []), r ? !1 : e;\n}\nexport { l as useServerHandoffComplete };", "map": {"version": 3, "names": ["t", "env", "f", "s", "r", "document", "o", "useSyncExternalStore", "l", "e", "n", "useState", "isHandoffComplete", "useEffect", "handoff", "useServerHandoffComplete"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/node_modules/@headlessui/react/dist/hooks/use-server-handoff-complete.js"], "sourcesContent": ["import*as t from\"react\";import{env as f}from'../utils/env.js';function s(){let r=typeof document==\"undefined\";return\"useSyncExternalStore\"in t?(o=>o.useSyncExternalStore)(t)(()=>()=>{},()=>!1,()=>!r):!1}function l(){let r=s(),[e,n]=t.useState(f.isHandoffComplete);return e&&f.isHandoffComplete===!1&&n(!1),t.useEffect(()=>{e!==!0&&n(!0)},[e]),t.useEffect(()=>f.handoff(),[]),r?!1:e}export{l as useServerHandoffComplete};\n"], "mappings": "AAAA,OAAM,KAAIA,CAAC,MAAK,OAAO;AAAC,SAAOC,GAAG,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAAC,OAAOC,QAAQ,IAAE,WAAW;EAAC,OAAM,sBAAsB,IAAGL,CAAC,GAAC,CAACM,CAAC,IAAEA,CAAC,CAACC,oBAAoB,EAAEP,CAAC,CAAC,CAAC,MAAI,MAAI,CAAC,CAAC,EAAC,MAAI,CAAC,CAAC,EAAC,MAAI,CAACI,CAAC,CAAC,GAAC,CAAC,CAAC;AAAA;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,IAAIJ,CAAC,GAACD,CAAC,CAAC,CAAC;IAAC,CAACM,CAAC,EAACC,CAAC,CAAC,GAACV,CAAC,CAACW,QAAQ,CAACT,CAAC,CAACU,iBAAiB,CAAC;EAAC,OAAOH,CAAC,IAAEP,CAAC,CAACU,iBAAiB,KAAG,CAAC,CAAC,IAAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAACV,CAAC,CAACa,SAAS,CAAC,MAAI;IAACJ,CAAC,KAAG,CAAC,CAAC,IAAEC,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACD,CAAC,CAAC,CAAC,EAACT,CAAC,CAACa,SAAS,CAAC,MAAIX,CAAC,CAACY,OAAO,CAAC,CAAC,EAAC,EAAE,CAAC,EAACV,CAAC,GAAC,CAAC,CAAC,GAACK,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIO,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}