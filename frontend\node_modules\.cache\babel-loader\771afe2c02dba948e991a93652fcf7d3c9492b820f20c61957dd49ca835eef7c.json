{"ast": null, "code": "// Import styles\nimport'styles/editor.css';// Import GrapesJS CSS (needed by MjmlEditor)\nimport'grapesjs/dist/css/grapes.min.css';import React,{useEffect,useRef,useState}from'react';import Alert from'components/Alert';import Button from'components/Button';import Card from'components/Card';import Input from'components/Input';// import MosaicoEditor from 'components/MosaicoEditor'; // Removed Mosaico\nimport MjmlEditor from'components/MjmlEditor';// Added MJML Editor\nimport{Modal}from'components/Modal';// Added missing Modal import\nimport ScheduleCampaignModal from'components/ScheduleCampaignModal';// Import schedule modal\nimport{useAuth}from'contexts/AuthContext';import{useNavigate,useParams}from'react-router-dom';import{templateRecommendationService}from'services';// Add template service import\nimport{campaignAPI}from'services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CampaignEdit=()=>{var _user$domain3,_user$domain4,_emailContents,_emailContents2;const{id}=useParams();const{user}=useAuth();const navigate=useNavigate();// --- State ---\nconst[campaign,setCampaign]=useState(null);const[loading,setLoading]=useState(true);const[saving,setSaving]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');// Email Content State\nconst[currentEmail,setCurrentEmail]=useState(1);const[activeEmailIndices,setActiveEmailIndices]=useState([]);const[emailContents,setEmailContents]=useState(()=>Array.from({length:10},()=>({mjml:'',html:''})));const editorRef=useRef(null);// Campaign Details State\nconst[campaignName,setCampaignName]=useState('');const[subject,setSubject]=useState('');const[fromName,setFromName]=useState('');const[fromEmail,setFromEmail]=useState('');const[replyTo,setReplyTo]=useState('');// Schedule Modal State\nconst[scheduleModalOpen,setScheduleModalOpen]=useState(false);const[campaignToSchedule,setCampaignToSchedule]=useState(null);// Template Picker State\nconst[showTemplatePicker,setShowTemplatePicker]=useState(false);const[templatesList,setTemplatesList]=useState([]);const[loadingTemplates,setLoadingTemplates]=useState(false);const[selectedTemplate,setSelectedTemplate]=useState(null);// --- Effects ---\n// Fetch campaign and initialize state\nuseEffect(()=>{const fetchCampaign=async()=>{if(!id||id==='undefined'){console.log('Skipping fetchCampaign due to invalid ID');navigate('/campaigns');// Redirect if ID is invalid\nreturn;}console.log('Fetching campaign with ID:',id);setLoading(true);try{const response=await campaignAPI.getCampaign(id);const foundCampaign=response.data.campaign||response.data;if(foundCampaign){var _user$domain;setCampaign(foundCampaign);setCampaignName(foundCampaign.name||'');setSubject(foundCampaign.subject||'');setFromName(foundCampaign.fromName||'');setFromEmail((user===null||user===void 0?void 0:(_user$domain=user.domain)===null||_user$domain===void 0?void 0:_user$domain.status)==='active'?`noreply@${user.domain.name}`:foundCampaign.fromEmail||'');setReplyTo(foundCampaign.replyTo||foundCampaign.fromEmail||'');// Initialize email contents\nconst initialContents=Array.from({length:10},()=>({mjml:'',html:''}));if(foundCampaign.emailContents&&Array.isArray(foundCampaign.emailContents)){foundCampaign.emailContents.forEach((content,index)=>{if(index<initialContents.length){initialContents[index]={mjml:content.mjml||'',html:content.html||''};}});}else if(foundCampaign.htmlContent||foundCampaign.mjmlContent){initialContents[0]={mjml:foundCampaign.mjmlContent||'',html:foundCampaign.htmlContent||''};}setEmailContents(initialContents);setLoading(false);}else{setError('Campaign not found');setLoading(false);}}catch(err){var _err$response,_err$response$data;console.error('Error fetching campaign:',err);setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Failed to fetch campaign');setLoading(false);}};fetchCampaign();},[id,user,navigate]);// Initialize activeEmailIndices based on fetched content\nuseEffect(()=>{const initialActive=[];emailContents.forEach((email,index)=>{if(email.html&&email.html.trim()){initialActive.push(index);}});setActiveEmailIndices(initialActive);},[emailContents]);// Fetch templates when modal opens\nuseEffect(()=>{if(showTemplatePicker){setLoadingTemplates(true);templateRecommendationService.getAllTemplates().then(response=>{if(response.success){setTemplatesList(response.data);}else{setError('Failed to load templates');}}).catch(err=>{console.error('Error loading templates:',err);setError('Failed to load templates. Please try again.');}).finally(()=>{setLoadingTemplates(false);});}},[showTemplatePicker]);// --- Handlers ---\n// Template Picker\nconst handleTemplateSelect=async templateId=>{console.log(`[Debug] Selecting template with ID: ${templateId}`);setLoadingTemplates(true);try{const response=await templateRecommendationService.getTemplateById(templateId);console.log('[Debug] Template API response:',response);if(response.success&&response.template){var _response$template$co;console.log('[Debug] Setting selected template:',{id:response.template.id||response.template._id,name:response.template.name,hasMjml:!!response.template.mjmlContent,hasContent:!!response.template.content,contentLength:((_response$template$co=response.template.content)===null||_response$template$co===void 0?void 0:_response$template$co.length)||0});setSelectedTemplate(response.template);}else{console.error('[Debug] Failed to load template:',response);setError('Failed to load selected template');}}catch(err){console.error('[Debug] Error selecting template:',err);setError('Failed to load selected template');}finally{setLoadingTemplates(false);}};const handleUseTemplate=()=>{console.log('[Debug] Using selected template:',selectedTemplate?{id:selectedTemplate.id||selectedTemplate._id,name:selectedTemplate.name,hasMjml:!!selectedTemplate.mjmlContent,hasContent:!!selectedTemplate.content}:'No template selected');if(selectedTemplate){const updated=[...emailContents];updated[currentEmail-1]={mjml:selectedTemplate.mjmlContent||'',html:selectedTemplate.content||''};console.log('[Debug] Updated email contents:',{emailIndex:currentEmail-1,hasMjml:!!updated[currentEmail-1].mjml,hasHtml:!!updated[currentEmail-1].html,mjmlLength:updated[currentEmail-1].mjml.length,htmlLength:updated[currentEmail-1].html.length});// Save the updated contents to state\nsetEmailContents(updated);// Also add this email to active indices if it contains content\nif(selectedTemplate.content&&selectedTemplate.content.trim()){const currentIndex=currentEmail-1;setActiveEmailIndices(prev=>prev.includes(currentIndex)?prev:[...prev,currentIndex].sort((a,b)=>a-b));}// Close the modal first\nsetSelectedTemplate(null);setShowTemplatePicker(false);// Force a complete re-render of the editor component by changing its key\n// Wait a moment to ensure state updates have completed\nsetTimeout(()=>{// This will force the MjmlEditor to completely remount with the new content\nconst tempKey=Date.now();// Use timestamp as a unique key\n// Temporarily set current email to a value that won't match any existing email\n// to force a complete remount\nsetCurrentEmail(-1);// After a brief delay, restore the current email index\nsetTimeout(()=>{setCurrentEmail(currentEmail);},50);},100);}};// Email Selection/Saving\nconst handleEmailSelect=index=>{setCurrentEmail(index);};const handleMjmlSave=(mjml,html)=>{console.log(`[handleMjmlSave] Saving content for email ${currentEmail}`);const updated=[...emailContents];const currentIndex=currentEmail-1;if(currentIndex>=0&&currentIndex<updated.length){updated[currentIndex]={mjml,html};setEmailContents(updated);// Also manage active state on save\nif(html&&html.trim()){setActiveEmailIndices(prev=>prev.includes(currentIndex)?prev:[...prev,currentIndex].sort((a,b)=>a-b));}else{setActiveEmailIndices(prev=>prev.filter(i=>i!==currentIndex));}}else{console.error(`[handleMjmlSave] Invalid currentEmail index: ${currentEmail}`);}};// Schedule Modal\nconst openScheduleModal=()=>{if(campaign){setCampaignToSchedule({_id:campaign._id,name:campaignName,scheduledFor:campaign.scheduledFor});setScheduleModalOpen(true);}};const handleScheduleSuccess=message=>{setScheduleModalOpen(false);setCampaignToSchedule(null);setSuccess(message);// Refresh campaign data \nif(id){campaignAPI.getCampaign(id).then(response=>setCampaign(response.data.campaign||response.data)).catch(err=>console.error(\"Failed to refresh campaign after schedule:\",err));}};// Main Save/Update Handler\nconst handleUpdateCampaign=async()=>{if(!id){setError('Campaign ID missing');return;}if(!campaignName||!subject||!fromName||!fromEmail){setError('Please fill in all required campaign details.');return;}// Filter active emails with content JUST before sending\nconst activeEmailsToSend=emailContents.filter((email,index)=>activeEmailIndices.includes(index)&&email.html&&email.html.trim());// Note: Validation for *at least one* active email might be needed here too,\n// depending on whether saving content without any active emails is allowed.\n// For now, we proceed even if activeEmailsToSend is empty, just saving details.\ntry{var _user$domain2,_activeEmailsToSend$,_response$data;setSaving(true);setError('');setSuccess('');const emailToUse=(user===null||user===void 0?void 0:(_user$domain2=user.domain)===null||_user$domain2===void 0?void 0:_user$domain2.status)==='active'?`noreply@${user.domain.name}`:fromEmail;const campaignData={name:campaignName,subject,fromName,fromEmail:emailToUse,replyTo:replyTo||emailToUse,// Send the *full* potential list, but backend might only use active ones based on other flags\n// Or, only send the *active* ones. Let's send only active ones for clarity.\nemailContents:activeEmailsToSend.map(e=>({mjml:e.mjml,html:e.html})),// Include htmlContent for potential compatibility if needed by backend update logic\nhtmlContent:((_activeEmailsToSend$=activeEmailsToSend[0])===null||_activeEmailsToSend$===void 0?void 0:_activeEmailsToSend$.html)||''// Note: Scheduling info is NOT sent here, it's handled by the modal/schedule API call.\n};console.log(\"Sending update data (Details & Content Only):\",campaignData);const response=await campaignAPI.updateCampaign(id,campaignData);setSuccess('Campaign details & content updated successfully!');if((_response$data=response.data)!==null&&_response$data!==void 0&&_response$data.campaign||response.data){setCampaign(response.data.campaign||response.data);}setTimeout(()=>setSuccess(''),3000);}catch(err){var _err$response2,_err$response2$data;console.error('Error updating campaign:',err);setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||'Failed to update campaign');}finally{setSaving(false);}};// Template Picker Modal Renderer\nconst renderTemplatePickerModal=()=>/*#__PURE__*/_jsxs(Modal,{isOpen:showTemplatePicker,onClose:()=>setShowTemplatePicker(false),title:\"Choose a Template\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\",children:[loadingTemplates&&!templatesList.length?/*#__PURE__*/_jsx(\"p\",{children:\"Loading templates...\"}):templatesList.map(template=>/*#__PURE__*/_jsxs(Card,{className:`cursor-pointer transition-all ${(selectedTemplate===null||selectedTemplate===void 0?void 0:selectedTemplate.id)===template.id?'ring-2 ring-primary shadow-lg':'hover:shadow-md'}`,onClick:()=>handleTemplateSelect(template.id),children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold mb-2 truncate\",children:template.name}),/*#__PURE__*/_jsx(\"div\",{className:\"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\",children:template.thumbnailUrl?/*#__PURE__*/_jsx(\"img\",{src:template.thumbnailUrl,alt:template.name,className:\"object-contain h-full w-full\"}):/*#__PURE__*/_jsx(\"span\",{children:\"No Preview\"})})]},template.id)),loadingTemplates&&selectedTemplate&&/*#__PURE__*/_jsx(\"p\",{children:\"Loading selected template details...\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 flex justify-end gap-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>{setShowTemplatePicker(false);setSelectedTemplate(null);},children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleUseTemplate,disabled:!selectedTemplate||loadingTemplates,children:\"Use Selected Template\"})]})]});// --- Main Render --- \n// Loading/Error checks\nif(loading&&!campaign){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"})});}if(!campaign&&!loading){return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error||\"Campaign not found or failed to load.\",className:\"mb-6\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>navigate('/campaigns'),children:\"Back to Campaigns\"})]});}if(!campaign)return null;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-2xl font-bold mb-6 text-gray-800 dark:text-white\",children:[\"Edit Campaign: \",campaignName||(campaign===null||campaign===void 0?void 0:campaign.name)]}),/*#__PURE__*/_jsxs(Card,{children:[error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(''),className:\"mb-4\"}),success&&/*#__PURE__*/_jsx(Alert,{type:\"success\",message:success,onClose:()=>setSuccess(''),className:\"mb-4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6 pb-4 border-b dark:border-gray-700 flex flex-wrap items-center justify-between gap-2\",children:[/*#__PURE__*/_jsx(Button,{onClick:handleUpdateCampaign,disabled:saving||loading,size:\"sm\",className:\"btn-primary\",children:saving?'Saving...':'Save Details & Content'}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>navigate(`/campaigns/recipients/${id}`),disabled:saving||loading,children:\"Manage Recipients\"}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:openScheduleModal,disabled:saving||loading,children:(campaign===null||campaign===void 0?void 0:campaign.status)==='scheduled'?'Reschedule':'Schedule Campaign'}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>navigate('/campaigns'),children:\"Back to Dashboard\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-3 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-1 space-y-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"Campaign Details\"}),/*#__PURE__*/_jsx(Input,{id:\"campaignName\",name:\"campaignName\",label:\"Campaign Name\",value:campaignName,onChange:e=>setCampaignName(e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{id:\"subject\",name:\"subject\",label:\"Email Subject\",value:subject,onChange:e=>setSubject(e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{id:\"fromName\",name:\"fromName\",label:\"From Name\",value:fromName,onChange:e=>setFromName(e.target.value),required:true}),/*#__PURE__*/_jsx(Input,{id:\"fromEmail\",name:\"fromEmail\",label:\"From Email\",type:\"email\",value:fromEmail,onChange:e=>setFromEmail(e.target.value),disabled:(user===null||user===void 0?void 0:(_user$domain3=user.domain)===null||_user$domain3===void 0?void 0:_user$domain3.status)==='active',required:true,helpText:(user===null||user===void 0?void 0:(_user$domain4=user.domain)===null||_user$domain4===void 0?void 0:_user$domain4.status)==='active'?`Using verified domain: ${user.domain.name}`:''}),/*#__PURE__*/_jsx(Input,{id:\"replyTo\",name:\"replyTo\",label:\"Reply-To Email (optional)\",type:\"email\",value:replyTo,onChange:e=>setReplyTo(e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"lg:col-span-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-2\",children:\"Email Content\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col lg:flex-row gap-4 h-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full lg:w-1/5 flex flex-col gap-2\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"font-semibold mb-1 text-sm\",children:[\"Select Email (Active: \",activeEmailIndices.length,\"):\"]}),Array.from({length:10}).map((_,idx)=>{var _emailContents$idx;const hasHtmlContent=!!((_emailContents$idx=emailContents[idx])!==null&&_emailContents$idx!==void 0&&_emailContents$idx.html&&emailContents[idx].html.trim());const isSelected=currentEmail===idx+1;const isActive=activeEmailIndices.includes(idx);let buttonVariant=\"secondary\";let customClasses=\"\";let icon=null;if(hasHtmlContent){buttonVariant=isActive?\"primary\":\"secondary\";customClasses=isActive?\"\":\"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\";icon=isActive?/*#__PURE__*/_jsx(\"span\",{className:\"ml-1 text-green-500 dark:text-green-400 font-bold\",children:\"\\u2713\"}):/*#__PURE__*/_jsx(\"span\",{className:\"ml-1 text-gray-500 dark:text-gray-400 font-bold\",children:\"\\u23F8\"});}else{customClasses=\"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\";}const selectionRingClass=isSelected?\"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\":\"\";return/*#__PURE__*/_jsxs(Button,{variant:buttonVariant,size:\"sm\",onClick:()=>{setCurrentEmail(idx+1);if(hasHtmlContent){setActiveEmailIndices(prev=>prev.includes(idx)?prev.filter(i=>i!==idx):[...prev,idx].sort((a,b)=>a-b));}},className:`w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses} px-2 py-1`,title:hasHtmlContent?isActive?\"Click to exclude\":\"Click to include\":\"Click to edit\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"flex-grow truncate text-xs\",children:[\"Email \",idx+1,isActive&&hasHtmlContent?' (Active)':hasHtmlContent?' (Inactive)':'']}),icon]},idx);}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>setShowTemplatePicker(true),className:\"mt-4\",children:\"Use Template\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full lg:w-4/5 flex flex-col\",children:[/*#__PURE__*/_jsx(MjmlEditor,{ref:editorRef,initialMjml:((_emailContents=emailContents[currentEmail-1])===null||_emailContents===void 0?void 0:_emailContents.mjml)||'',initialHtml:((_emailContents2=emailContents[currentEmail-1])===null||_emailContents2===void 0?void 0:_emailContents2.html)||'',onSave:handleMjmlSave,height:\"60vh\"},`email-${currentEmail}-${Date.now()}`),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500 dark:text-gray-400 mt-1\",children:\"Click email on left to edit. Active emails (\\u2713) will be used if campaign is sent/scheduled.\"})]})]})]})]})]}),renderTemplatePickerModal(),/*#__PURE__*/_jsx(ScheduleCampaignModal,{isOpen:scheduleModalOpen,onClose:()=>setScheduleModalOpen(false),campaign:campaignToSchedule,onScheduled:handleScheduleSuccess})]});};export default CampaignEdit;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "Input", "MjmlEditor", "Modal", "ScheduleCampaignModal", "useAuth", "useNavigate", "useParams", "templateRecommendationService", "campaignAPI", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CampaignEdit", "_user$domain3", "_user$domain4", "_emailContents", "_emailContents2", "id", "user", "navigate", "campaign", "setCampaign", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "currentEmail", "setCurrentEmail", "activeEmailIndices", "setActiveEmailIndices", "emailContents", "setEmailContents", "Array", "from", "length", "mjml", "html", "editor<PERSON><PERSON>", "campaignName", "setCampaignName", "subject", "setSubject", "fromName", "setFromName", "fromEmail", "setFromEmail", "replyTo", "setReplyTo", "scheduleModalOpen", "setScheduleModalOpen", "campaignToSchedule", "setCampaignToSchedule", "showTemplatePicker", "setShowTemplatePicker", "templatesList", "setTemplatesList", "loadingTemplates", "setLoadingTemplates", "selectedTemplate", "setSelectedTemplate", "fetchCampaign", "console", "log", "response", "getCampaign", "foundCampaign", "data", "_user$domain", "name", "domain", "status", "initialContents", "isArray", "for<PERSON>ach", "content", "index", "htmlContent", "mjml<PERSON><PERSON><PERSON>", "err", "_err$response", "_err$response$data", "message", "initialActive", "email", "trim", "push", "getAllTemplates", "then", "catch", "finally", "handleTemplateSelect", "templateId", "getTemplateById", "template", "_response$template$co", "_id", "hasMjml", "<PERSON><PERSON><PERSON><PERSON>", "contentLength", "handleUseTemplate", "updated", "emailIndex", "hasHtml", "mjm<PERSON><PERSON><PERSON><PERSON>", "htmlLength", "currentIndex", "prev", "includes", "sort", "a", "b", "setTimeout", "tempKey", "Date", "now", "handleEmailSelect", "handleMjmlSave", "filter", "i", "openScheduleModal", "scheduledFor", "handleScheduleSuccess", "handleUpdateCampaign", "activeEmailsToSend", "_user$domain2", "_activeEmailsToSend$", "_response$data", "emailToUse", "campaignData", "map", "e", "updateCampaign", "_err$response2", "_err$response2$data", "renderTemplatePickerModal", "isOpen", "onClose", "title", "children", "className", "onClick", "thumbnailUrl", "src", "alt", "variant", "disabled", "type", "size", "label", "value", "onChange", "target", "required", "helpText", "_", "idx", "_emailContents$idx", "hasHtmlContent", "isSelected", "isActive", "buttonVariant", "customClasses", "icon", "selectionRingClass", "ref", "initialMjml", "initialHtml", "onSave", "height", "onScheduled"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/campaigns/CampaignEdit.tsx"], "sourcesContent": ["// Import styles\nimport 'styles/editor.css';\n// Import GrapesJS CSS (needed by MjmlEditor)\nimport 'grapesjs/dist/css/grapes.min.css';\n\nimport React, {\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport Input from 'components/Input';\n// import MosaicoEditor from 'components/MosaicoEditor'; // Removed Mosaico\nimport MjmlEditor, {\n  MjmlEditorRef,\n} from 'components/MjmlEditor'; // Added MJML Editor\nimport { Modal } from 'components/Modal'; // Added missing Modal import\nimport ScheduleCampaignModal\n  from 'components/ScheduleCampaignModal'; // Import schedule modal\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\nimport {\n  templateRecommendationService,\n} from 'services'; // Add template service import\nimport { campaignAPI } from 'services/api';\n\nconst CampaignEdit: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // --- State ---\n  const [campaign, setCampaign] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Email Content State\n  const [currentEmail, setCurrentEmail] = useState<number>(1);\n  const [activeEmailIndices, setActiveEmailIndices] = useState<number[]>([]); \n  const [emailContents, setEmailContents] = useState<{ mjml: string; html: string }[]>(() =>\n    Array.from({ length: 10 }, () => ({ mjml: '', html: '' }))\n  );\n  const editorRef = useRef<MjmlEditorRef>(null);\n\n  // Campaign Details State\n  const [campaignName, setCampaignName] = useState('');\n  const [subject, setSubject] = useState('');\n  const [fromName, setFromName] = useState('');\n  const [fromEmail, setFromEmail] = useState('');\n  const [replyTo, setReplyTo] = useState('');\n\n  // Schedule Modal State\n  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);\n  const [campaignToSchedule, setCampaignToSchedule] = useState<any>(null); \n\n  // Template Picker State\n  const [showTemplatePicker, setShowTemplatePicker] = useState(false);\n  const [templatesList, setTemplatesList] = useState<any[]>([]);\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);\n\n  // --- Effects ---\n\n  // Fetch campaign and initialize state\n  useEffect(() => {\n    const fetchCampaign = async () => {\n        if (!id || id === 'undefined') {\n            console.log('Skipping fetchCampaign due to invalid ID');\n            navigate('/campaigns'); // Redirect if ID is invalid\n            return;\n        }\n        console.log('Fetching campaign with ID:', id);\n        setLoading(true); \n        try {\n            const response = await campaignAPI.getCampaign(id);\n            const foundCampaign = response.data.campaign || response.data;\n            if (foundCampaign) {\n                setCampaign(foundCampaign);\n                setCampaignName(foundCampaign.name || '');\n                setSubject(foundCampaign.subject || '');\n                setFromName(foundCampaign.fromName || '');\n                setFromEmail(user?.domain?.status === 'active' ? `noreply@${user.domain.name}` : (foundCampaign.fromEmail || ''));\n                setReplyTo(foundCampaign.replyTo || foundCampaign.fromEmail || '');\n\n                // Initialize email contents\n                const initialContents = Array.from({ length: 10 }, () => ({ mjml: '', html: '' }));\n                if (foundCampaign.emailContents && Array.isArray(foundCampaign.emailContents)) {\n                    foundCampaign.emailContents.forEach((content: any, index: number) => {\n                        if (index < initialContents.length) {\n                            initialContents[index] = {\n                                mjml: content.mjml || '',\n                                html: content.html || ''\n                            };\n                        }\n                    });\n                } else if (foundCampaign.htmlContent || foundCampaign.mjmlContent) {\n                    initialContents[0] = {\n                        mjml: foundCampaign.mjmlContent || '',\n                        html: foundCampaign.htmlContent || ''\n                    };\n                }\n                setEmailContents(initialContents);\n                setLoading(false);\n            } else {\n                setError('Campaign not found');\n                setLoading(false);\n            }\n        } catch (err: any) {\n            console.error('Error fetching campaign:', err);\n            setError(err.response?.data?.message || 'Failed to fetch campaign');\n            setLoading(false);\n        }\n    };\n    fetchCampaign();\n  }, [id, user, navigate]);\n\n  // Initialize activeEmailIndices based on fetched content\n  useEffect(() => {\n    const initialActive: number[] = [];\n    emailContents.forEach((email, index) => {\n      if (email.html && email.html.trim()) {\n        initialActive.push(index);\n      }\n    });\n    setActiveEmailIndices(initialActive);\n  }, [emailContents]); \n\n  // Fetch templates when modal opens\n  useEffect(() => {\n    if (showTemplatePicker) {\n        setLoadingTemplates(true);\n        templateRecommendationService.getAllTemplates()\n            .then(response => {\n                if (response.success) {\n                    setTemplatesList(response.data);\n                } else {\n                    setError('Failed to load templates');\n                }\n            })\n            .catch(err => {\n                console.error('Error loading templates:', err);\n                setError('Failed to load templates. Please try again.');\n            })\n            .finally(() => {\n                setLoadingTemplates(false);\n            });\n    }\n  }, [showTemplatePicker]);\n\n  // --- Handlers ---\n  \n  // Template Picker\n  const handleTemplateSelect = async (templateId: string) => {\n    console.log(`[Debug] Selecting template with ID: ${templateId}`);\n    setLoadingTemplates(true);\n    try {\n        const response = await templateRecommendationService.getTemplateById(templateId);\n        console.log('[Debug] Template API response:', response);\n        if (response.success && response.template) {\n            console.log('[Debug] Setting selected template:', {\n                id: response.template.id || response.template._id,\n                name: response.template.name,\n                hasMjml: !!response.template.mjmlContent,\n                hasContent: !!response.template.content,\n                contentLength: response.template.content?.length || 0\n            });\n            setSelectedTemplate(response.template);\n        } else {\n            console.error('[Debug] Failed to load template:', response);\n            setError('Failed to load selected template');\n        }\n    } catch (err) {\n        console.error('[Debug] Error selecting template:', err);\n        setError('Failed to load selected template');\n    } finally {\n        setLoadingTemplates(false);\n    }\n  };\n  \n  const handleUseTemplate = () => {\n    console.log('[Debug] Using selected template:', selectedTemplate ? {\n        id: selectedTemplate.id || selectedTemplate._id,\n        name: selectedTemplate.name,\n        hasMjml: !!selectedTemplate.mjmlContent,\n        hasContent: !!selectedTemplate.content\n    } : 'No template selected');\n    \n    if (selectedTemplate) {\n        const updated = [...emailContents];\n        updated[currentEmail - 1] = {\n            mjml: selectedTemplate.mjmlContent || '',\n            html: selectedTemplate.content || '',\n        };\n        \n        console.log('[Debug] Updated email contents:', {\n            emailIndex: currentEmail - 1,\n            hasMjml: !!updated[currentEmail - 1].mjml,\n            hasHtml: !!updated[currentEmail - 1].html,\n            mjmlLength: updated[currentEmail - 1].mjml.length,\n            htmlLength: updated[currentEmail - 1].html.length\n        });\n        \n        // Save the updated contents to state\n        setEmailContents(updated);\n        \n        // Also add this email to active indices if it contains content\n        if (selectedTemplate.content && selectedTemplate.content.trim()) {\n            const currentIndex = currentEmail - 1;\n            setActiveEmailIndices(prev => \n                prev.includes(currentIndex) \n                    ? prev \n                    : [...prev, currentIndex].sort((a, b) => a - b)\n            );\n        }\n        \n        // Close the modal first\n        setSelectedTemplate(null);\n        setShowTemplatePicker(false);\n        \n        // Force a complete re-render of the editor component by changing its key\n        // Wait a moment to ensure state updates have completed\n        setTimeout(() => {\n            // This will force the MjmlEditor to completely remount with the new content\n            const tempKey = Date.now(); // Use timestamp as a unique key\n            \n            // Temporarily set current email to a value that won't match any existing email\n            // to force a complete remount\n            setCurrentEmail(-1);\n            \n            // After a brief delay, restore the current email index\n            setTimeout(() => {\n                setCurrentEmail(currentEmail);\n            }, 50);\n        }, 100);\n    }\n  };\n\n  // Email Selection/Saving\n  const handleEmailSelect = (index: number) => {\n      setCurrentEmail(index);\n  };\n  const handleMjmlSave = (mjml: string, html: string) => {\n    console.log(`[handleMjmlSave] Saving content for email ${currentEmail}`);\n    const updated = [...emailContents];\n    const currentIndex = currentEmail - 1;\n    if (currentIndex >= 0 && currentIndex < updated.length) {\n      updated[currentIndex] = { mjml, html };\n      setEmailContents(updated);\n      // Also manage active state on save\n      if (html && html.trim()) {\n        setActiveEmailIndices(prev => prev.includes(currentIndex) ? prev : [...prev, currentIndex].sort((a, b) => a - b));\n      } else {\n        setActiveEmailIndices(prev => prev.filter(i => i !== currentIndex));\n      }\n    } else {\n      console.error(`[handleMjmlSave] Invalid currentEmail index: ${currentEmail}`);\n    }\n  };\n\n  // Schedule Modal\n  const openScheduleModal = () => {\n    if (campaign) {\n      setCampaignToSchedule({\n        _id: campaign._id,\n        name: campaignName,\n        scheduledFor: campaign.scheduledFor \n      });\n      setScheduleModalOpen(true);\n    }\n  };\n  const handleScheduleSuccess = (message: string) => {\n    setScheduleModalOpen(false);\n    setCampaignToSchedule(null);\n    setSuccess(message);\n    // Refresh campaign data \n    if (id) {\n        campaignAPI.getCampaign(id)\n            .then(response => setCampaign(response.data.campaign || response.data))\n            .catch(err => console.error(\"Failed to refresh campaign after schedule:\", err));\n    }\n  };\n\n  // Main Save/Update Handler\n  const handleUpdateCampaign = async () => {\n    if (!id) { setError('Campaign ID missing'); return; }\n    if (!campaignName || !subject || !fromName || !fromEmail) {\n      setError('Please fill in all required campaign details.');\n      return;\n    }\n\n    // Filter active emails with content JUST before sending\n    const activeEmailsToSend = emailContents.filter((email, index) => \n        activeEmailIndices.includes(index) && email.html && email.html.trim()\n    );\n\n    // Note: Validation for *at least one* active email might be needed here too,\n    // depending on whether saving content without any active emails is allowed.\n    // For now, we proceed even if activeEmailsToSend is empty, just saving details.\n\n    try {\n      setSaving(true); setError(''); setSuccess('');\n      const emailToUse = user?.domain?.status === 'active' ? `noreply@${user.domain.name}` : fromEmail;\n      \n      const campaignData: any = {\n        name: campaignName,\n        subject,\n        fromName,\n        fromEmail: emailToUse,\n        replyTo: replyTo || emailToUse,\n        // Send the *full* potential list, but backend might only use active ones based on other flags\n        // Or, only send the *active* ones. Let's send only active ones for clarity.\n        emailContents: activeEmailsToSend.map(e => ({ mjml: e.mjml, html: e.html })),\n        // Include htmlContent for potential compatibility if needed by backend update logic\n        htmlContent: activeEmailsToSend[0]?.html || '', \n        // Note: Scheduling info is NOT sent here, it's handled by the modal/schedule API call.\n      };\n\n      console.log(\"Sending update data (Details & Content Only):\", campaignData);\n      const response = await campaignAPI.updateCampaign(id, campaignData);\n\n      setSuccess('Campaign details & content updated successfully!');\n      if (response.data?.campaign || response.data) {\n        setCampaign(response.data.campaign || response.data);\n      }\n      setTimeout(() => setSuccess(''), 3000);\n\n    } catch (err: any) {\n      console.error('Error updating campaign:', err);\n      setError(err.response?.data?.message || 'Failed to update campaign');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Template Picker Modal Renderer\n  const renderTemplatePickerModal = () => (\n    <Modal\n      isOpen={showTemplatePicker}\n      onClose={() => setShowTemplatePicker(false)}\n      title=\"Choose a Template\"\n    >\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\">\n        {loadingTemplates && !templatesList.length ? (\n          <p>Loading templates...</p>\n        ) : (\n          templatesList.map(template => (\n            <Card\n              key={template.id}\n              className={`cursor-pointer transition-all ${selectedTemplate?.id === template.id ? 'ring-2 ring-primary shadow-lg' : 'hover:shadow-md'}`}\n              onClick={() => handleTemplateSelect(template.id)}\n            >\n              <h4 className=\"font-semibold mb-2 truncate\">{template.name}</h4>\n              <div className=\"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\">\n                 {template.thumbnailUrl ? <img src={template.thumbnailUrl} alt={template.name} className=\"object-contain h-full w-full\"/> : <span>No Preview</span>}\n              </div>\n            </Card>\n          ))\n        )}\n         {loadingTemplates && selectedTemplate && <p>Loading selected template details...</p>}\n      </div>\n      <div className=\"mt-4 flex justify-end gap-2\">\n        <Button variant=\"secondary\" onClick={() => {setShowTemplatePicker(false); setSelectedTemplate(null);}}>Cancel</Button>\n        <Button\n           onClick={handleUseTemplate}\n           disabled={!selectedTemplate || loadingTemplates}\n        >\n          Use Selected Template\n        </Button>\n      </div>\n    </Modal>\n  );\n\n  // --- Main Render --- \n\n  // Loading/Error checks\n  if (loading && !campaign) {\n    return (\n      <div className=\"flex justify-center items-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  if (!campaign && !loading) {\n    return (\n      <div>\n        <Alert\n          type=\"error\"\n          message={error || \"Campaign not found or failed to load.\"}\n          className=\"mb-6\"\n        />\n        <Button onClick={() => navigate('/campaigns')}>\n          Back to Campaigns\n        </Button>\n      </div>\n    );\n  }\n\n  if (!campaign) return null;\n\n  return (\n    <>\n      <h1 className=\"text-2xl font-bold mb-6 text-gray-800 dark:text-white\">Edit Campaign: {campaignName || campaign?.name}</h1>\n      <Card>\n          {error && <Alert type=\"error\" message={error} onClose={() => setError('')} className=\"mb-4\" />}\n          {success && <Alert type=\"success\" message={success} onClose={() => setSuccess('')} className=\"mb-4\" />}\n\n          {/* Action Buttons Area - Restored */} \n          <div className=\"mb-6 pb-4 border-b dark:border-gray-700 flex flex-wrap items-center justify-between gap-2\">\n            <Button onClick={handleUpdateCampaign} disabled={saving || loading} size=\"sm\" className=\"btn-primary\">\n              {saving ? 'Saving...' : 'Save Details & Content'}\n            </Button>\n            <div className=\"flex space-x-2\">\n                <Button variant=\"secondary\" size=\"sm\" onClick={() => navigate(`/campaigns/recipients/${id}`)} disabled={saving || loading}>\n                  Manage Recipients\n                </Button>\n                <Button variant=\"secondary\" size=\"sm\" onClick={openScheduleModal} disabled={saving || loading}>\n                  {campaign?.status === 'scheduled' ? 'Reschedule' : 'Schedule Campaign'}\n                </Button>\n                 <Button variant=\"secondary\" size=\"sm\" onClick={() => navigate('/campaigns')}> \n                  Back to Dashboard\n                </Button>\n            </div>\n          </div>\n\n          {/* Combined Edit Area - Restored */} \n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Column 1: Campaign Details */} \n            <div className=\"lg:col-span-1 space-y-4\">\n              <h3 className=\"text-lg font-semibold mb-2\">Campaign Details</h3>\n              <Input id=\"campaignName\" name=\"campaignName\" label=\"Campaign Name\" value={campaignName} onChange={(e) => setCampaignName(e.target.value)} required />\n              <Input id=\"subject\" name=\"subject\" label=\"Email Subject\" value={subject} onChange={(e) => setSubject(e.target.value)} required />\n              <Input id=\"fromName\" name=\"fromName\" label=\"From Name\" value={fromName} onChange={(e) => setFromName(e.target.value)} required />\n              <Input id=\"fromEmail\" name=\"fromEmail\" label=\"From Email\" type=\"email\" value={fromEmail} onChange={(e) => setFromEmail(e.target.value)} disabled={user?.domain?.status === 'active'} required helpText={user?.domain?.status === 'active' ? `Using verified domain: ${user.domain.name}` : ''} />\n              <Input id=\"replyTo\" name=\"replyTo\" label=\"Reply-To Email (optional)\" type=\"email\" value={replyTo} onChange={(e) => setReplyTo(e.target.value)} />\n            </div>\n\n            {/* Column 2 & 3: Email Content Editor */} \n            <div className=\"lg:col-span-2\">\n               <h3 className=\"text-lg font-semibold mb-2\">Email Content</h3>\n               <div className=\"flex flex-col lg:flex-row gap-4 h-full\">\n                  {/* Email sequence sidebar */} \n                  <div className=\"w-full lg:w-1/5 flex flex-col gap-2\">\n                    <h3 className=\"font-semibold mb-1 text-sm\">Select Email (Active: {activeEmailIndices.length}):</h3>\n                     {/* ... (Mapping logic for email buttons - kept from previous state) ... */} \n                     {Array.from({ length: 10 }).map((_, idx) => {\n                        const hasHtmlContent = !!(emailContents[idx]?.html && emailContents[idx].html.trim());\n                        const isSelected = currentEmail === idx + 1;\n                        const isActive = activeEmailIndices.includes(idx);\n                        let buttonVariant: \"primary\" | \"secondary\" = \"secondary\";\n                        let customClasses = \"\";\n                        let icon = null;\n                        if (hasHtmlContent) {\n                            buttonVariant = isActive ? \"primary\" : \"secondary\";\n                            customClasses = isActive ? \"\" : \"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\";\n                            icon = isActive ? <span className=\"ml-1 text-green-500 dark:text-green-400 font-bold\">✓</span> : <span className=\"ml-1 text-gray-500 dark:text-gray-400 font-bold\">⏸</span>;\n                        } else {\n                           customClasses = \"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\";\n                        }\n                        const selectionRingClass = isSelected ? \"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\" : \"\";\n                        return (\n                            <Button\n                                key={idx} variant={buttonVariant} size=\"sm\"\n                                onClick={() => { setCurrentEmail(idx + 1); if (hasHtmlContent) { setActiveEmailIndices(prev => prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx].sort((a, b) => a - b)); }}}\n                                className={`w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses} px-2 py-1`}\n                                title={hasHtmlContent ? (isActive ? \"Click to exclude\" : \"Click to include\") : \"Click to edit\"}\n                            >\n                                <span className=\"flex-grow truncate text-xs\">Email {idx + 1}{isActive && hasHtmlContent ? ' (Active)' : hasHtmlContent ? ' (Inactive)' : ''}</span>\n                                {icon}\n                            </Button>\n                        );\n                     })}\n                     <Button variant=\"secondary\" size=\"sm\" onClick={() => setShowTemplatePicker(true)} className=\"mt-4\">Use Template</Button>\n                  </div>\n                  {/* Editor */} \n                  <div className=\"w-full lg:w-4/5 flex flex-col\">\n                    <MjmlEditor\n                      key={`email-${currentEmail}-${Date.now()}`} \n                      ref={editorRef}\n                      initialMjml={emailContents[currentEmail - 1]?.mjml || ''}\n                      initialHtml={emailContents[currentEmail - 1]?.html || ''}\n                      onSave={handleMjmlSave}\n                      height=\"60vh\" \n                    />\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">Click email on left to edit. Active emails (✓) will be used if campaign is sent/scheduled.</p>\n                  </div>\n               </div>\n            </div>\n          </div>\n      </Card>\n\n      {/* Modals */} \n      {renderTemplatePickerModal()}\n      <ScheduleCampaignModal\n        isOpen={scheduleModalOpen}\n        onClose={() => setScheduleModalOpen(false)}\n        campaign={campaignToSchedule}\n        onScheduled={handleScheduleSuccess}\n      />\n    </>\n  );\n};\n\nexport default CampaignEdit;\n"], "mappings": "AAAA;AACA,MAAO,mBAAmB,CAC1B;AACA,MAAO,kCAAkC,CAEzC,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,MAAM,CACNC,QAAQ,KACH,OAAO,CAEd,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,IAAI,KAAM,iBAAiB,CAClC,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC;AACA,MAAO,CAAAC,UAAU,KAEV,uBAAuB,CAAE;AAChC,OAASC,KAAK,KAAQ,kBAAkB,CAAE;AAC1C,MAAO,CAAAC,qBAAqB,KACrB,kCAAkC,CAAE;AAC3C,OAASC,OAAO,KAAQ,sBAAsB,CAC9C,OACEC,WAAW,CACXC,SAAS,KACJ,kBAAkB,CACzB,OACEC,6BAA6B,KACxB,UAAU,CAAE;AACnB,OAASC,WAAW,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE3C,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,KAAAC,aAAA,CAAAC,aAAA,CAAAC,cAAA,CAAAC,eAAA,CACnC,KAAM,CAAEC,EAAG,CAAC,CAAGd,SAAS,CAAiB,CAAC,CAC1C,KAAM,CAAEe,IAAK,CAAC,CAAGjB,OAAO,CAAC,CAAC,CAC1B,KAAM,CAAAkB,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACkB,QAAQ,CAAEC,WAAW,CAAC,CAAG5B,QAAQ,CAAM,IAAI,CAAC,CACnD,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC+B,MAAM,CAAEC,SAAS,CAAC,CAAGhC,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAACiC,KAAK,CAAEC,QAAQ,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACA,KAAM,CAACqC,YAAY,CAAEC,eAAe,CAAC,CAAGtC,QAAQ,CAAS,CAAC,CAAC,CAC3D,KAAM,CAACuC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxC,QAAQ,CAAW,EAAE,CAAC,CAC1E,KAAM,CAACyC,aAAa,CAAEC,gBAAgB,CAAC,CAAG1C,QAAQ,CAAmC,IACnF2C,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAE,EAAG,CAAC,CAAE,KAAO,CAAEC,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAC3D,CAAC,CACD,KAAM,CAAAC,SAAS,CAAGjD,MAAM,CAAgB,IAAI,CAAC,CAE7C;AACA,KAAM,CAACkD,YAAY,CAAEC,eAAe,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACmD,OAAO,CAAEC,UAAU,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACqD,QAAQ,CAAEC,WAAW,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuD,SAAS,CAAEC,YAAY,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACyD,OAAO,CAAEC,UAAU,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACA,KAAM,CAAC2D,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5D,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC6D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9D,QAAQ,CAAM,IAAI,CAAC,CAEvE;AACA,KAAM,CAAC+D,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGhE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACiE,aAAa,CAAEC,gBAAgB,CAAC,CAAGlE,QAAQ,CAAQ,EAAE,CAAC,CAC7D,KAAM,CAACmE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACqE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtE,QAAQ,CAAa,IAAI,CAAC,CAE1E;AAEA;AACAF,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyE,aAAa,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CAAC/C,EAAE,EAAIA,EAAE,GAAK,WAAW,CAAE,CAC3BgD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvD/C,QAAQ,CAAC,YAAY,CAAC,CAAE;AACxB,OACJ,CACA8C,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAEjD,EAAE,CAAC,CAC7CM,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACA,KAAM,CAAA4C,QAAQ,CAAG,KAAM,CAAA9D,WAAW,CAAC+D,WAAW,CAACnD,EAAE,CAAC,CAClD,KAAM,CAAAoD,aAAa,CAAGF,QAAQ,CAACG,IAAI,CAAClD,QAAQ,EAAI+C,QAAQ,CAACG,IAAI,CAC7D,GAAID,aAAa,CAAE,KAAAE,YAAA,CACflD,WAAW,CAACgD,aAAa,CAAC,CAC1B1B,eAAe,CAAC0B,aAAa,CAACG,IAAI,EAAI,EAAE,CAAC,CACzC3B,UAAU,CAACwB,aAAa,CAACzB,OAAO,EAAI,EAAE,CAAC,CACvCG,WAAW,CAACsB,aAAa,CAACvB,QAAQ,EAAI,EAAE,CAAC,CACzCG,YAAY,CAAC,CAAA/B,IAAI,SAAJA,IAAI,kBAAAqD,YAAA,CAAJrD,IAAI,CAAEuD,MAAM,UAAAF,YAAA,iBAAZA,YAAA,CAAcG,MAAM,IAAK,QAAQ,CAAG,WAAWxD,IAAI,CAACuD,MAAM,CAACD,IAAI,EAAE,CAAIH,aAAa,CAACrB,SAAS,EAAI,EAAG,CAAC,CACjHG,UAAU,CAACkB,aAAa,CAACnB,OAAO,EAAImB,aAAa,CAACrB,SAAS,EAAI,EAAE,CAAC,CAElE;AACA,KAAM,CAAA2B,eAAe,CAAGvC,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAE,EAAG,CAAC,CAAE,KAAO,CAAEC,IAAI,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAAC,CAAC,CAClF,GAAI6B,aAAa,CAACnC,aAAa,EAAIE,KAAK,CAACwC,OAAO,CAACP,aAAa,CAACnC,aAAa,CAAC,CAAE,CAC3EmC,aAAa,CAACnC,aAAa,CAAC2C,OAAO,CAAC,CAACC,OAAY,CAAEC,KAAa,GAAK,CACjE,GAAIA,KAAK,CAAGJ,eAAe,CAACrC,MAAM,CAAE,CAChCqC,eAAe,CAACI,KAAK,CAAC,CAAG,CACrBxC,IAAI,CAAEuC,OAAO,CAACvC,IAAI,EAAI,EAAE,CACxBC,IAAI,CAAEsC,OAAO,CAACtC,IAAI,EAAI,EAC1B,CAAC,CACL,CACJ,CAAC,CAAC,CACN,CAAC,IAAM,IAAI6B,aAAa,CAACW,WAAW,EAAIX,aAAa,CAACY,WAAW,CAAE,CAC/DN,eAAe,CAAC,CAAC,CAAC,CAAG,CACjBpC,IAAI,CAAE8B,aAAa,CAACY,WAAW,EAAI,EAAE,CACrCzC,IAAI,CAAE6B,aAAa,CAACW,WAAW,EAAI,EACvC,CAAC,CACL,CACA7C,gBAAgB,CAACwC,eAAe,CAAC,CACjCpD,UAAU,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACHI,QAAQ,CAAC,oBAAoB,CAAC,CAC9BJ,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAE,MAAO2D,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACfnB,OAAO,CAACvC,KAAK,CAAC,0BAA0B,CAAEwD,GAAG,CAAC,CAC9CvD,QAAQ,CAAC,EAAAwD,aAAA,CAAAD,GAAG,CAACf,QAAQ,UAAAgB,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcb,IAAI,UAAAc,kBAAA,iBAAlBA,kBAAA,CAAoBC,OAAO,GAAI,0BAA0B,CAAC,CACnE9D,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CACDyC,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAAC/C,EAAE,CAAEC,IAAI,CAAEC,QAAQ,CAAC,CAAC,CAExB;AACA5B,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+F,aAAuB,CAAG,EAAE,CAClCpD,aAAa,CAAC2C,OAAO,CAAC,CAACU,KAAK,CAAER,KAAK,GAAK,CACtC,GAAIQ,KAAK,CAAC/C,IAAI,EAAI+C,KAAK,CAAC/C,IAAI,CAACgD,IAAI,CAAC,CAAC,CAAE,CACnCF,aAAa,CAACG,IAAI,CAACV,KAAK,CAAC,CAC3B,CACF,CAAC,CAAC,CACF9C,qBAAqB,CAACqD,aAAa,CAAC,CACtC,CAAC,CAAE,CAACpD,aAAa,CAAC,CAAC,CAEnB;AACA3C,SAAS,CAAC,IAAM,CACd,GAAIiE,kBAAkB,CAAE,CACpBK,mBAAmB,CAAC,IAAI,CAAC,CACzBzD,6BAA6B,CAACsF,eAAe,CAAC,CAAC,CAC1CC,IAAI,CAACxB,QAAQ,EAAI,CACd,GAAIA,QAAQ,CAACvC,OAAO,CAAE,CAClB+B,gBAAgB,CAACQ,QAAQ,CAACG,IAAI,CAAC,CACnC,CAAC,IAAM,CACH3C,QAAQ,CAAC,0BAA0B,CAAC,CACxC,CACJ,CAAC,CAAC,CACDiE,KAAK,CAACV,GAAG,EAAI,CACVjB,OAAO,CAACvC,KAAK,CAAC,0BAA0B,CAAEwD,GAAG,CAAC,CAC9CvD,QAAQ,CAAC,6CAA6C,CAAC,CAC3D,CAAC,CAAC,CACDkE,OAAO,CAAC,IAAM,CACXhC,mBAAmB,CAAC,KAAK,CAAC,CAC9B,CAAC,CAAC,CACV,CACF,CAAC,CAAE,CAACL,kBAAkB,CAAC,CAAC,CAExB;AAEA;AACA,KAAM,CAAAsC,oBAAoB,CAAG,KAAO,CAAAC,UAAkB,EAAK,CACzD9B,OAAO,CAACC,GAAG,CAAC,uCAAuC6B,UAAU,EAAE,CAAC,CAChElC,mBAAmB,CAAC,IAAI,CAAC,CACzB,GAAI,CACA,KAAM,CAAAM,QAAQ,CAAG,KAAM,CAAA/D,6BAA6B,CAAC4F,eAAe,CAACD,UAAU,CAAC,CAChF9B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEC,QAAQ,CAAC,CACvD,GAAIA,QAAQ,CAACvC,OAAO,EAAIuC,QAAQ,CAAC8B,QAAQ,CAAE,KAAAC,qBAAA,CACvCjC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAE,CAC9CjD,EAAE,CAAEkD,QAAQ,CAAC8B,QAAQ,CAAChF,EAAE,EAAIkD,QAAQ,CAAC8B,QAAQ,CAACE,GAAG,CACjD3B,IAAI,CAAEL,QAAQ,CAAC8B,QAAQ,CAACzB,IAAI,CAC5B4B,OAAO,CAAE,CAAC,CAACjC,QAAQ,CAAC8B,QAAQ,CAAChB,WAAW,CACxCoB,UAAU,CAAE,CAAC,CAAClC,QAAQ,CAAC8B,QAAQ,CAACnB,OAAO,CACvCwB,aAAa,CAAE,EAAAJ,qBAAA,CAAA/B,QAAQ,CAAC8B,QAAQ,CAACnB,OAAO,UAAAoB,qBAAA,iBAAzBA,qBAAA,CAA2B5D,MAAM,GAAI,CACxD,CAAC,CAAC,CACFyB,mBAAmB,CAACI,QAAQ,CAAC8B,QAAQ,CAAC,CAC1C,CAAC,IAAM,CACHhC,OAAO,CAACvC,KAAK,CAAC,kCAAkC,CAAEyC,QAAQ,CAAC,CAC3DxC,QAAQ,CAAC,kCAAkC,CAAC,CAChD,CACJ,CAAE,MAAOuD,GAAG,CAAE,CACVjB,OAAO,CAACvC,KAAK,CAAC,mCAAmC,CAAEwD,GAAG,CAAC,CACvDvD,QAAQ,CAAC,kCAAkC,CAAC,CAChD,CAAC,OAAS,CACNkC,mBAAmB,CAAC,KAAK,CAAC,CAC9B,CACF,CAAC,CAED,KAAM,CAAA0C,iBAAiB,CAAGA,CAAA,GAAM,CAC9BtC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAEJ,gBAAgB,CAAG,CAC/D7C,EAAE,CAAE6C,gBAAgB,CAAC7C,EAAE,EAAI6C,gBAAgB,CAACqC,GAAG,CAC/C3B,IAAI,CAAEV,gBAAgB,CAACU,IAAI,CAC3B4B,OAAO,CAAE,CAAC,CAACtC,gBAAgB,CAACmB,WAAW,CACvCoB,UAAU,CAAE,CAAC,CAACvC,gBAAgB,CAACgB,OACnC,CAAC,CAAG,sBAAsB,CAAC,CAE3B,GAAIhB,gBAAgB,CAAE,CAClB,KAAM,CAAA0C,OAAO,CAAG,CAAC,GAAGtE,aAAa,CAAC,CAClCsE,OAAO,CAAC1E,YAAY,CAAG,CAAC,CAAC,CAAG,CACxBS,IAAI,CAAEuB,gBAAgB,CAACmB,WAAW,EAAI,EAAE,CACxCzC,IAAI,CAAEsB,gBAAgB,CAACgB,OAAO,EAAI,EACtC,CAAC,CAEDb,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAE,CAC3CuC,UAAU,CAAE3E,YAAY,CAAG,CAAC,CAC5BsE,OAAO,CAAE,CAAC,CAACI,OAAO,CAAC1E,YAAY,CAAG,CAAC,CAAC,CAACS,IAAI,CACzCmE,OAAO,CAAE,CAAC,CAACF,OAAO,CAAC1E,YAAY,CAAG,CAAC,CAAC,CAACU,IAAI,CACzCmE,UAAU,CAAEH,OAAO,CAAC1E,YAAY,CAAG,CAAC,CAAC,CAACS,IAAI,CAACD,MAAM,CACjDsE,UAAU,CAAEJ,OAAO,CAAC1E,YAAY,CAAG,CAAC,CAAC,CAACU,IAAI,CAACF,MAC/C,CAAC,CAAC,CAEF;AACAH,gBAAgB,CAACqE,OAAO,CAAC,CAEzB;AACA,GAAI1C,gBAAgB,CAACgB,OAAO,EAAIhB,gBAAgB,CAACgB,OAAO,CAACU,IAAI,CAAC,CAAC,CAAE,CAC7D,KAAM,CAAAqB,YAAY,CAAG/E,YAAY,CAAG,CAAC,CACrCG,qBAAqB,CAAC6E,IAAI,EACtBA,IAAI,CAACC,QAAQ,CAACF,YAAY,CAAC,CACrBC,IAAI,CACJ,CAAC,GAAGA,IAAI,CAAED,YAAY,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAGC,CAAC,CACtD,CAAC,CACL,CAEA;AACAnD,mBAAmB,CAAC,IAAI,CAAC,CACzBN,qBAAqB,CAAC,KAAK,CAAC,CAE5B;AACA;AACA0D,UAAU,CAAC,IAAM,CACb;AACA,KAAM,CAAAC,OAAO,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE;AAE5B;AACA;AACAvF,eAAe,CAAC,CAAC,CAAC,CAAC,CAEnB;AACAoF,UAAU,CAAC,IAAM,CACbpF,eAAe,CAACD,YAAY,CAAC,CACjC,CAAC,CAAE,EAAE,CAAC,CACV,CAAC,CAAE,GAAG,CAAC,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAyF,iBAAiB,CAAIxC,KAAa,EAAK,CACzChD,eAAe,CAACgD,KAAK,CAAC,CAC1B,CAAC,CACD,KAAM,CAAAyC,cAAc,CAAGA,CAACjF,IAAY,CAAEC,IAAY,GAAK,CACrDyB,OAAO,CAACC,GAAG,CAAC,6CAA6CpC,YAAY,EAAE,CAAC,CACxE,KAAM,CAAA0E,OAAO,CAAG,CAAC,GAAGtE,aAAa,CAAC,CAClC,KAAM,CAAA2E,YAAY,CAAG/E,YAAY,CAAG,CAAC,CACrC,GAAI+E,YAAY,EAAI,CAAC,EAAIA,YAAY,CAAGL,OAAO,CAAClE,MAAM,CAAE,CACtDkE,OAAO,CAACK,YAAY,CAAC,CAAG,CAAEtE,IAAI,CAAEC,IAAK,CAAC,CACtCL,gBAAgB,CAACqE,OAAO,CAAC,CACzB;AACA,GAAIhE,IAAI,EAAIA,IAAI,CAACgD,IAAI,CAAC,CAAC,CAAE,CACvBvD,qBAAqB,CAAC6E,IAAI,EAAIA,IAAI,CAACC,QAAQ,CAACF,YAAY,CAAC,CAAGC,IAAI,CAAG,CAAC,GAAGA,IAAI,CAAED,YAAY,CAAC,CAACG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAGC,CAAC,CAAC,CAAC,CACnH,CAAC,IAAM,CACLjF,qBAAqB,CAAC6E,IAAI,EAAIA,IAAI,CAACW,MAAM,CAACC,CAAC,EAAIA,CAAC,GAAKb,YAAY,CAAC,CAAC,CACrE,CACF,CAAC,IAAM,CACL5C,OAAO,CAACvC,KAAK,CAAC,gDAAgDI,YAAY,EAAE,CAAC,CAC/E,CACF,CAAC,CAED;AACA,KAAM,CAAA6F,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAIvG,QAAQ,CAAE,CACZmC,qBAAqB,CAAC,CACpB4C,GAAG,CAAE/E,QAAQ,CAAC+E,GAAG,CACjB3B,IAAI,CAAE9B,YAAY,CAClBkF,YAAY,CAAExG,QAAQ,CAACwG,YACzB,CAAC,CAAC,CACFvE,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CACF,CAAC,CACD,KAAM,CAAAwE,qBAAqB,CAAIxC,OAAe,EAAK,CACjDhC,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,qBAAqB,CAAC,IAAI,CAAC,CAC3B1B,UAAU,CAACwD,OAAO,CAAC,CACnB;AACA,GAAIpE,EAAE,CAAE,CACJZ,WAAW,CAAC+D,WAAW,CAACnD,EAAE,CAAC,CACtB0E,IAAI,CAACxB,QAAQ,EAAI9C,WAAW,CAAC8C,QAAQ,CAACG,IAAI,CAAClD,QAAQ,EAAI+C,QAAQ,CAACG,IAAI,CAAC,CAAC,CACtEsB,KAAK,CAACV,GAAG,EAAIjB,OAAO,CAACvC,KAAK,CAAC,4CAA4C,CAAEwD,GAAG,CAAC,CAAC,CACvF,CACF,CAAC,CAED;AACA,KAAM,CAAA4C,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CAAC7G,EAAE,CAAE,CAAEU,QAAQ,CAAC,qBAAqB,CAAC,CAAE,OAAQ,CACpD,GAAI,CAACe,YAAY,EAAI,CAACE,OAAO,EAAI,CAACE,QAAQ,EAAI,CAACE,SAAS,CAAE,CACxDrB,QAAQ,CAAC,+CAA+C,CAAC,CACzD,OACF,CAEA;AACA,KAAM,CAAAoG,kBAAkB,CAAG7F,aAAa,CAACuF,MAAM,CAAC,CAAClC,KAAK,CAAER,KAAK,GACzD/C,kBAAkB,CAAC+E,QAAQ,CAAChC,KAAK,CAAC,EAAIQ,KAAK,CAAC/C,IAAI,EAAI+C,KAAK,CAAC/C,IAAI,CAACgD,IAAI,CAAC,CACxE,CAAC,CAED;AACA;AACA;AAEA,GAAI,KAAAwC,aAAA,CAAAC,oBAAA,CAAAC,cAAA,CACFzG,SAAS,CAAC,IAAI,CAAC,CAAEE,QAAQ,CAAC,EAAE,CAAC,CAAEE,UAAU,CAAC,EAAE,CAAC,CAC7C,KAAM,CAAAsG,UAAU,CAAG,CAAAjH,IAAI,SAAJA,IAAI,kBAAA8G,aAAA,CAAJ9G,IAAI,CAAEuD,MAAM,UAAAuD,aAAA,iBAAZA,aAAA,CAActD,MAAM,IAAK,QAAQ,CAAG,WAAWxD,IAAI,CAACuD,MAAM,CAACD,IAAI,EAAE,CAAGxB,SAAS,CAEhG,KAAM,CAAAoF,YAAiB,CAAG,CACxB5D,IAAI,CAAE9B,YAAY,CAClBE,OAAO,CACPE,QAAQ,CACRE,SAAS,CAAEmF,UAAU,CACrBjF,OAAO,CAAEA,OAAO,EAAIiF,UAAU,CAC9B;AACA;AACAjG,aAAa,CAAE6F,kBAAkB,CAACM,GAAG,CAACC,CAAC,GAAK,CAAE/F,IAAI,CAAE+F,CAAC,CAAC/F,IAAI,CAAEC,IAAI,CAAE8F,CAAC,CAAC9F,IAAK,CAAC,CAAC,CAAC,CAC5E;AACAwC,WAAW,CAAE,EAAAiD,oBAAA,CAAAF,kBAAkB,CAAC,CAAC,CAAC,UAAAE,oBAAA,iBAArBA,oBAAA,CAAuBzF,IAAI,GAAI,EAC5C;AACF,CAAC,CAEDyB,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAEkE,YAAY,CAAC,CAC1E,KAAM,CAAAjE,QAAQ,CAAG,KAAM,CAAA9D,WAAW,CAACkI,cAAc,CAACtH,EAAE,CAAEmH,YAAY,CAAC,CAEnEvG,UAAU,CAAC,kDAAkD,CAAC,CAC9D,GAAI,CAAAqG,cAAA,CAAA/D,QAAQ,CAACG,IAAI,UAAA4D,cAAA,WAAbA,cAAA,CAAe9G,QAAQ,EAAI+C,QAAQ,CAACG,IAAI,CAAE,CAC5CjD,WAAW,CAAC8C,QAAQ,CAACG,IAAI,CAAClD,QAAQ,EAAI+C,QAAQ,CAACG,IAAI,CAAC,CACtD,CACA6C,UAAU,CAAC,IAAMtF,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CAExC,CAAE,MAAOqD,GAAQ,CAAE,KAAAsD,cAAA,CAAAC,mBAAA,CACjBxE,OAAO,CAACvC,KAAK,CAAC,0BAA0B,CAAEwD,GAAG,CAAC,CAC9CvD,QAAQ,CAAC,EAAA6G,cAAA,CAAAtD,GAAG,CAACf,QAAQ,UAAAqE,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAclE,IAAI,UAAAmE,mBAAA,iBAAlBA,mBAAA,CAAoBpD,OAAO,GAAI,2BAA2B,CAAC,CACtE,CAAC,OAAS,CACR5D,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED;AACA,KAAM,CAAAiH,yBAAyB,CAAGA,CAAA,gBAChCjI,KAAA,CAACV,KAAK,EACJ4I,MAAM,CAAEnF,kBAAmB,CAC3BoF,OAAO,CAAEA,CAAA,GAAMnF,qBAAqB,CAAC,KAAK,CAAE,CAC5CoF,KAAK,CAAC,mBAAmB,CAAAC,QAAA,eAEzBrI,KAAA,QAAKsI,SAAS,CAAC,wEAAwE,CAAAD,QAAA,EACpFlF,gBAAgB,EAAI,CAACF,aAAa,CAACpB,MAAM,cACxC/B,IAAA,MAAAuI,QAAA,CAAG,sBAAoB,CAAG,CAAC,CAE3BpF,aAAa,CAAC2E,GAAG,CAACpC,QAAQ,eACxBxF,KAAA,CAACb,IAAI,EAEHmJ,SAAS,CAAE,iCAAiC,CAAAjF,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE7C,EAAE,IAAKgF,QAAQ,CAAChF,EAAE,CAAG,+BAA+B,CAAG,iBAAiB,EAAG,CACzI+H,OAAO,CAAEA,CAAA,GAAMlD,oBAAoB,CAACG,QAAQ,CAAChF,EAAE,CAAE,CAAA6H,QAAA,eAEjDvI,IAAA,OAAIwI,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAE7C,QAAQ,CAACzB,IAAI,CAAK,CAAC,cAChEjE,IAAA,QAAKwI,SAAS,CAAC,0FAA0F,CAAAD,QAAA,CACrG7C,QAAQ,CAACgD,YAAY,cAAG1I,IAAA,QAAK2I,GAAG,CAAEjD,QAAQ,CAACgD,YAAa,CAACE,GAAG,CAAElD,QAAQ,CAACzB,IAAK,CAACuE,SAAS,CAAC,8BAA8B,CAAC,CAAC,cAAGxI,IAAA,SAAAuI,QAAA,CAAM,YAAU,CAAM,CAAC,CAChJ,CAAC,GAPD7C,QAAQ,CAAChF,EAQV,CACP,CACF,CACC2C,gBAAgB,EAAIE,gBAAgB,eAAIvD,IAAA,MAAAuI,QAAA,CAAG,sCAAoC,CAAG,CAAC,EAClF,CAAC,cACNrI,KAAA,QAAKsI,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1CvI,IAAA,CAACZ,MAAM,EAACyJ,OAAO,CAAC,WAAW,CAACJ,OAAO,CAAEA,CAAA,GAAM,CAACvF,qBAAqB,CAAC,KAAK,CAAC,CAAEM,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAE,CAAA+E,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtHvI,IAAA,CAACZ,MAAM,EACJqJ,OAAO,CAAEzC,iBAAkB,CAC3B8C,QAAQ,CAAE,CAACvF,gBAAgB,EAAIF,gBAAiB,CAAAkF,QAAA,CAClD,uBAED,CAAQ,CAAC,EACN,CAAC,EACD,CACR,CAED;AAEA;AACA,GAAIxH,OAAO,EAAI,CAACF,QAAQ,CAAE,CACxB,mBACEb,IAAA,QAAKwI,SAAS,CAAC,uCAAuC,CAAAD,QAAA,cACpDvI,IAAA,QAAKwI,SAAS,CAAC,0EAA0E,CAAM,CAAC,CAC7F,CAAC,CAEV,CAEA,GAAI,CAAC3H,QAAQ,EAAI,CAACE,OAAO,CAAE,CACzB,mBACEb,KAAA,QAAAqI,QAAA,eACEvI,IAAA,CAACb,KAAK,EACJ4J,IAAI,CAAC,OAAO,CACZjE,OAAO,CAAE3D,KAAK,EAAI,uCAAwC,CAC1DqH,SAAS,CAAC,MAAM,CACjB,CAAC,cACFxI,IAAA,CAACZ,MAAM,EAACqJ,OAAO,CAAEA,CAAA,GAAM7H,QAAQ,CAAC,YAAY,CAAE,CAAA2H,QAAA,CAAC,mBAE/C,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA,GAAI,CAAC1H,QAAQ,CAAE,MAAO,KAAI,CAE1B,mBACEX,KAAA,CAAAE,SAAA,EAAAmI,QAAA,eACErI,KAAA,OAAIsI,SAAS,CAAC,uDAAuD,CAAAD,QAAA,EAAC,iBAAe,CAACpG,YAAY,GAAItB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEoD,IAAI,GAAK,CAAC,cAC1H/D,KAAA,CAACb,IAAI,EAAAkJ,QAAA,EACApH,KAAK,eAAInB,IAAA,CAACb,KAAK,EAAC4J,IAAI,CAAC,OAAO,CAACjE,OAAO,CAAE3D,KAAM,CAACkH,OAAO,CAAEA,CAAA,GAAMjH,QAAQ,CAAC,EAAE,CAAE,CAACoH,SAAS,CAAC,MAAM,CAAE,CAAC,CAC7FnH,OAAO,eAAIrB,IAAA,CAACb,KAAK,EAAC4J,IAAI,CAAC,SAAS,CAACjE,OAAO,CAAEzD,OAAQ,CAACgH,OAAO,CAAEA,CAAA,GAAM/G,UAAU,CAAC,EAAE,CAAE,CAACkH,SAAS,CAAC,MAAM,CAAE,CAAC,cAGtGtI,KAAA,QAAKsI,SAAS,CAAC,2FAA2F,CAAAD,QAAA,eACxGvI,IAAA,CAACZ,MAAM,EAACqJ,OAAO,CAAElB,oBAAqB,CAACuB,QAAQ,CAAE7H,MAAM,EAAIF,OAAQ,CAACiI,IAAI,CAAC,IAAI,CAACR,SAAS,CAAC,aAAa,CAAAD,QAAA,CAClGtH,MAAM,CAAG,WAAW,CAAG,wBAAwB,CAC1C,CAAC,cACTf,KAAA,QAAKsI,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC3BvI,IAAA,CAACZ,MAAM,EAACyJ,OAAO,CAAC,WAAW,CAACG,IAAI,CAAC,IAAI,CAACP,OAAO,CAAEA,CAAA,GAAM7H,QAAQ,CAAC,yBAAyBF,EAAE,EAAE,CAAE,CAACoI,QAAQ,CAAE7H,MAAM,EAAIF,OAAQ,CAAAwH,QAAA,CAAC,mBAE3H,CAAQ,CAAC,cACTvI,IAAA,CAACZ,MAAM,EAACyJ,OAAO,CAAC,WAAW,CAACG,IAAI,CAAC,IAAI,CAACP,OAAO,CAAErB,iBAAkB,CAAC0B,QAAQ,CAAE7H,MAAM,EAAIF,OAAQ,CAAAwH,QAAA,CAC3F,CAAA1H,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsD,MAAM,IAAK,WAAW,CAAG,YAAY,CAAG,mBAAmB,CAChE,CAAC,cACRnE,IAAA,CAACZ,MAAM,EAACyJ,OAAO,CAAC,WAAW,CAACG,IAAI,CAAC,IAAI,CAACP,OAAO,CAAEA,CAAA,GAAM7H,QAAQ,CAAC,YAAY,CAAE,CAAA2H,QAAA,CAAC,mBAE9E,CAAQ,CAAC,EACR,CAAC,EACH,CAAC,cAGNrI,KAAA,QAAKsI,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eAEpDrI,KAAA,QAAKsI,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACtCvI,IAAA,OAAIwI,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAChEvI,IAAA,CAACV,KAAK,EAACoB,EAAE,CAAC,cAAc,CAACuD,IAAI,CAAC,cAAc,CAACgF,KAAK,CAAC,eAAe,CAACC,KAAK,CAAE/G,YAAa,CAACgH,QAAQ,CAAGpB,CAAC,EAAK3F,eAAe,CAAC2F,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE,CAACG,QAAQ,MAAE,CAAC,cACrJrJ,IAAA,CAACV,KAAK,EAACoB,EAAE,CAAC,SAAS,CAACuD,IAAI,CAAC,SAAS,CAACgF,KAAK,CAAC,eAAe,CAACC,KAAK,CAAE7G,OAAQ,CAAC8G,QAAQ,CAAGpB,CAAC,EAAKzF,UAAU,CAACyF,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE,CAACG,QAAQ,MAAE,CAAC,cACjIrJ,IAAA,CAACV,KAAK,EAACoB,EAAE,CAAC,UAAU,CAACuD,IAAI,CAAC,UAAU,CAACgF,KAAK,CAAC,WAAW,CAACC,KAAK,CAAE3G,QAAS,CAAC4G,QAAQ,CAAGpB,CAAC,EAAKvF,WAAW,CAACuF,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE,CAACG,QAAQ,MAAE,CAAC,cACjIrJ,IAAA,CAACV,KAAK,EAACoB,EAAE,CAAC,WAAW,CAACuD,IAAI,CAAC,WAAW,CAACgF,KAAK,CAAC,YAAY,CAACF,IAAI,CAAC,OAAO,CAACG,KAAK,CAAEzG,SAAU,CAAC0G,QAAQ,CAAGpB,CAAC,EAAKrF,YAAY,CAACqF,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE,CAACJ,QAAQ,CAAE,CAAAnI,IAAI,SAAJA,IAAI,kBAAAL,aAAA,CAAJK,IAAI,CAAEuD,MAAM,UAAA5D,aAAA,iBAAZA,aAAA,CAAc6D,MAAM,IAAK,QAAS,CAACkF,QAAQ,MAACC,QAAQ,CAAE,CAAA3I,IAAI,SAAJA,IAAI,kBAAAJ,aAAA,CAAJI,IAAI,CAAEuD,MAAM,UAAA3D,aAAA,iBAAZA,aAAA,CAAc4D,MAAM,IAAK,QAAQ,CAAG,0BAA0BxD,IAAI,CAACuD,MAAM,CAACD,IAAI,EAAE,CAAG,EAAG,CAAE,CAAC,cACjSjE,IAAA,CAACV,KAAK,EAACoB,EAAE,CAAC,SAAS,CAACuD,IAAI,CAAC,SAAS,CAACgF,KAAK,CAAC,2BAA2B,CAACF,IAAI,CAAC,OAAO,CAACG,KAAK,CAAEvG,OAAQ,CAACwG,QAAQ,CAAGpB,CAAC,EAAKnF,UAAU,CAACmF,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE,CAAE,CAAC,EAC9I,CAAC,cAGNhJ,KAAA,QAAKsI,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC3BvI,IAAA,OAAIwI,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,eAAa,CAAI,CAAC,cAC7DrI,KAAA,QAAKsI,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAEpDrI,KAAA,QAAKsI,SAAS,CAAC,qCAAqC,CAAAD,QAAA,eAClDrI,KAAA,OAAIsI,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAC,wBAAsB,CAAC9G,kBAAkB,CAACM,MAAM,CAAC,IAAE,EAAI,CAAC,CAEjGF,KAAK,CAACC,IAAI,CAAC,CAAEC,MAAM,CAAE,EAAG,CAAC,CAAC,CAAC+F,GAAG,CAAC,CAACyB,CAAC,CAAEC,GAAG,GAAK,KAAAC,kBAAA,CACzC,KAAM,CAAAC,cAAc,CAAG,CAAC,EAAE,CAAAD,kBAAA,CAAA9H,aAAa,CAAC6H,GAAG,CAAC,UAAAC,kBAAA,WAAlBA,kBAAA,CAAoBxH,IAAI,EAAIN,aAAa,CAAC6H,GAAG,CAAC,CAACvH,IAAI,CAACgD,IAAI,CAAC,CAAC,CAAC,CACrF,KAAM,CAAA0E,UAAU,CAAGpI,YAAY,GAAKiI,GAAG,CAAG,CAAC,CAC3C,KAAM,CAAAI,QAAQ,CAAGnI,kBAAkB,CAAC+E,QAAQ,CAACgD,GAAG,CAAC,CACjD,GAAI,CAAAK,aAAsC,CAAG,WAAW,CACxD,GAAI,CAAAC,aAAa,CAAG,EAAE,CACtB,GAAI,CAAAC,IAAI,CAAG,IAAI,CACf,GAAIL,cAAc,CAAE,CAChBG,aAAa,CAAGD,QAAQ,CAAG,SAAS,CAAG,WAAW,CAClDE,aAAa,CAAGF,QAAQ,CAAG,EAAE,CAAG,wGAAwG,CACxIG,IAAI,CAAGH,QAAQ,cAAG5J,IAAA,SAAMwI,SAAS,CAAC,mDAAmD,CAAAD,QAAA,CAAC,QAAC,CAAM,CAAC,cAAGvI,IAAA,SAAMwI,SAAS,CAAC,iDAAiD,CAAAD,QAAA,CAAC,QAAC,CAAM,CAAC,CAC/K,CAAC,IAAM,CACJuB,aAAa,CAAG,sEAAsE,CACzF,CACA,KAAM,CAAAE,kBAAkB,CAAGL,UAAU,CAAG,kEAAkE,CAAG,EAAE,CAC/G,mBACIzJ,KAAA,CAACd,MAAM,EACOyJ,OAAO,CAAEgB,aAAc,CAACb,IAAI,CAAC,IAAI,CAC3CP,OAAO,CAAEA,CAAA,GAAM,CAAEjH,eAAe,CAACgI,GAAG,CAAG,CAAC,CAAC,CAAE,GAAIE,cAAc,CAAE,CAAEhI,qBAAqB,CAAC6E,IAAI,EAAIA,IAAI,CAACC,QAAQ,CAACgD,GAAG,CAAC,CAAGjD,IAAI,CAACW,MAAM,CAACC,CAAC,EAAIA,CAAC,GAAKqC,GAAG,CAAC,CAAG,CAAC,GAAGjD,IAAI,CAAEiD,GAAG,CAAC,CAAC/C,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAGC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,CAC5L6B,SAAS,CAAE,sDAAsDwB,kBAAkB,IAAIF,aAAa,YAAa,CACjHxB,KAAK,CAAEoB,cAAc,CAAIE,QAAQ,CAAG,kBAAkB,CAAG,kBAAkB,CAAI,eAAgB,CAAArB,QAAA,eAE/FrI,KAAA,SAAMsI,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAC,QAAM,CAACiB,GAAG,CAAG,CAAC,CAAEI,QAAQ,EAAIF,cAAc,CAAG,WAAW,CAAGA,cAAc,CAAG,aAAa,CAAG,EAAE,EAAO,CAAC,CAClJK,IAAI,GANAP,GAOD,CAAC,CAEhB,CAAC,CAAC,cACFxJ,IAAA,CAACZ,MAAM,EAACyJ,OAAO,CAAC,WAAW,CAACG,IAAI,CAAC,IAAI,CAACP,OAAO,CAAEA,CAAA,GAAMvF,qBAAqB,CAAC,IAAI,CAAE,CAACsF,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,cAAY,CAAQ,CAAC,EACtH,CAAC,cAENrI,KAAA,QAAKsI,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5CvI,IAAA,CAACT,UAAU,EAET0K,GAAG,CAAE/H,SAAU,CACfgI,WAAW,CAAE,EAAA1J,cAAA,CAAAmB,aAAa,CAACJ,YAAY,CAAG,CAAC,CAAC,UAAAf,cAAA,iBAA/BA,cAAA,CAAiCwB,IAAI,GAAI,EAAG,CACzDmI,WAAW,CAAE,EAAA1J,eAAA,CAAAkB,aAAa,CAACJ,YAAY,CAAG,CAAC,CAAC,UAAAd,eAAA,iBAA/BA,eAAA,CAAiCwB,IAAI,GAAI,EAAG,CACzDmI,MAAM,CAAEnD,cAAe,CACvBoD,MAAM,CAAC,MAAM,EALR,SAAS9I,YAAY,IAAIuF,IAAI,CAACC,GAAG,CAAC,CAAC,EAMzC,CAAC,cACF/G,IAAA,MAAGwI,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,iGAA0F,CAAG,CAAC,EACxJ,CAAC,EACJ,CAAC,EACJ,CAAC,EACH,CAAC,EACJ,CAAC,CAGNJ,yBAAyB,CAAC,CAAC,cAC5BnI,IAAA,CAACP,qBAAqB,EACpB2I,MAAM,CAAEvF,iBAAkB,CAC1BwF,OAAO,CAAEA,CAAA,GAAMvF,oBAAoB,CAAC,KAAK,CAAE,CAC3CjC,QAAQ,CAAEkC,kBAAmB,CAC7BuH,WAAW,CAAEhD,qBAAsB,CACpC,CAAC,EACF,CAAC,CAEP,CAAC,CAED,cAAe,CAAAjH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}