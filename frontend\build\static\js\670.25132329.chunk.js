"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[670],{670:(e,t,s)=>{s.r(t),s.d(t,{default:()=>v});var a=s(5043),r=s(8231),n=s(8417),l=s(1411),o=s(9774),i=s(9579),c=s(579);const d=e=>{switch(e.toLowerCase()){case"newsletter":return"from-blue-500 to-indigo-600";case"promotion":return"from-purple-500 to-pink-600";case"welcome":return"from-green-400 to-teal-500";case"announcement":return"from-yellow-400 to-orange-500";case"e-commerce":return"from-red-500 to-rose-600";default:return"from-gray-600 to-gray-700"}},m=()=>(0,c.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-12 h-12 text-white opacity-50",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z"})}),h=()=>(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-12 h-12 text-white opacity-50",children:[(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 6h.008v.008H6V6Z"})]}),x=()=>(0,c.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-12 h-12 text-white opacity-50",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"})}),u=()=>(0,c.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-12 h-12 text-white opacity-50",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535L19.5 17.11c.463-.304.646-.961.463-1.511a20.845 20.845 0 0 0-1.44-4.282m-3.102-.069a18.03 18.03 0 0 0-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18C9.36 16.33 8.5 17.644 8.5 19.5a2.25 2.25 0 0 0 4.5 0c0-1.09-.394-2.068-.998-2.818"})}),p=()=>(0,c.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-12 h-12 text-white opacity-50",children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.106M15.75 5.106l-1.483 7.411M9.75 9.75l4.5 1.5m-4.5-1.5L12 9.75m-2.25 0L9.75 7.5M15 12l-2.25-1.5M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})}),g=()=>(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-12 h-12 text-white opacity-50",children:[(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M11.35 3.836A1.5 1.5 0 0 1 12.183 3h9.634A1.5 1.5 0 0 1 23.317 4.183L19.817 7.683A1.5 1.5 0 0 1 19.183 8H2.817a1.5 1.5 0 0 1-1.317-.817L.683 4.183A1.5 1.5 0 0 1 1.817 3h9.534Z"}),(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2 8v11a1.5 1.5 0 0 0 1.5 1.5h17A1.5 1.5 0 0 0 22 19V8M8 12h8M8 16h4"})]}),f=e=>{switch(e.toLowerCase()){case"newsletter":return(0,c.jsx)(m,{});case"promotion":case"promotional":return(0,c.jsx)(h,{});case"welcome":case"onboarding":return(0,c.jsx)(x,{});case"announcement":return(0,c.jsx)(u,{});case"e-commerce":return(0,c.jsx)(p,{});default:return(0,c.jsx)(g,{})}},v=()=>{const e=(0,r.Zp)(),[t,s]=(0,a.useState)([]),[m,h]=(0,a.useState)({}),[x,u]=(0,a.useState)([]),[p,g]=(0,a.useState)([]),[v,j]=(0,a.useState)(""),[y,w]=(0,a.useState)("system"),[b,k]=(0,a.useState)(!0),[N,C]=(0,a.useState)(null),[A,L]=(0,a.useState)(!1),[M,T]=(0,a.useState)(null),[S,F]=(0,a.useState)([]),[V,H]=(0,a.useState)(!1);(0,a.useEffect)((()=>{z()}),[]),(0,a.useEffect)((()=>{E()}),[t,y]);const z=async()=>{k(!0),C(null);try{console.log("Fetching all user and system templates...");const e=await i.jg.getAllTemplates();e.success&&e.data?s(e.data):(C(e.message||"Failed to fetch templates"),s([]))}catch(a){var e,t;console.error("Error fetching all templates:",a);const r=(null===(e=a.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||a.message||"An unexpected error occurred";C(r),s([])}finally{k(!1)}},E=()=>{if("system"===y){const e=t.filter((e=>e.isSystem)).reduce(((e,t)=>{const s=t.category||"other";return e[s]||(e[s]=[]),e[s].push(t),e}),{});h(e);const s=Object.keys(e);g(s),v&&s.includes(v)||j(s[0]||""),u([])}else{const e=t.filter((e=>!e.isSystem));u(e),h({}),g([]),j("")}},B=t=>{e(`/email-templates/editor/${t}`)},D=()=>{L(!1),T(null)},Z=()=>{H(!1)};let $;if(b)$=(0,c.jsx)("p",{className:"text-white col-span-full text-center py-10",children:"Loading templates..."});else if(N)$=(0,c.jsxs)("p",{className:"text-red-500 col-span-full text-center py-10",children:["Error: ",N]});else if("system"===y){var W;if(0===p.length)$=(0,c.jsx)("p",{className:"text-white col-span-full text-center py-10",children:"No system template categories found."});else if(v&&(null===(W=m[v])||void 0===W?void 0:W.length)>0){$=m[v].map((e=>{const t=d(e.category),s=f(e.category);return(0,c.jsxs)(l.A,{className:"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col",children:[(0,c.jsxs)("div",{className:`relative w-full h-48 bg-gradient-to-br ${t} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`,children:[(0,c.jsx)("div",{className:"mb-2",children:s}),(0,c.jsx)("span",{className:"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md",children:e.name})]}),(0,c.jsxs)("div",{className:"p-4 flex flex-col flex-grow",children:[(0,c.jsx)("p",{className:"text-gray-400 text-sm mb-4 flex-grow",children:e.description||"No description available."}),(0,c.jsx)("div",{className:"mt-auto flex gap-2 justify-end pt-2",children:(0,c.jsx)(n.A,{size:"sm",variant:"secondary",onClick:()=>B(e.id),children:"Use Template"})})]})]},e.id)}))}else $=(0,c.jsxs)("p",{className:"text-white col-span-full text-center py-10",children:["No templates found in the '",v,"' category."]})}else $=0===x.length?(0,c.jsx)("p",{className:"text-white col-span-full text-center py-10",children:"You haven't created or saved any templates yet."}):x.map((t=>{const s=d(t.category),a=f(t.category),r=S.includes(t.id);return(0,c.jsxs)(l.A,{className:"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col",children:[(0,c.jsxs)("div",{className:`relative w-full h-48 bg-gradient-to-br ${s} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`,children:[(0,c.jsx)("div",{className:"absolute top-2 left-2 z-20",children:(0,c.jsx)("input",{type:"checkbox",checked:r,onChange:e=>((e,t)=>{F((s=>t?[...s,e]:s.filter((t=>t!==e))))})(t.id,e.target.checked),className:"h-5 w-5 rounded border-gray-500 text-indigo-600 focus:ring-indigo-500"})}),(0,c.jsx)("div",{className:"mb-2",children:a}),(0,c.jsx)("span",{className:"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md",children:t.name})]}),(0,c.jsxs)("div",{className:"p-4 flex flex-col flex-grow",children:[(0,c.jsx)("p",{className:"text-gray-400 text-sm mb-4 flex-grow",children:t.description||"No description available."}),(0,c.jsxs)("div",{className:"mt-auto flex gap-2 justify-end pt-2 w-full",children:[(0,c.jsx)(n.A,{size:"sm",variant:"secondary",onClick:()=>B(t.id),children:"Use Template"}),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(n.A,{size:"sm",variant:"secondary",onClick:()=>{return s=t.id,void e(`/email-templates/editor/${s}`);var s},children:"Edit"}),(0,c.jsx)(n.A,{size:"sm",variant:"danger",onClick:()=>{return e=t.id,T(e),void L(!0);var e},children:"Delete"})]})]})]})]},t.id)}));return(0,c.jsxs)("div",{className:"p-6 bg-gray-900 min-h-screen text-white",children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,c.jsx)("h1",{className:"text-2xl font-semibold",children:"Email Templates"}),(0,c.jsxs)("div",{className:"flex gap-2",children:["user"===y&&(0,c.jsx)(c.Fragment,{children:S.length>0?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(n.A,{variant:"secondary",onClick:()=>{F([])},children:"Deselect All"}),(0,c.jsxs)(n.A,{variant:"danger",onClick:()=>{0!==S.length&&H(!0)},children:["Delete Selected (",S.length,")"]})]}):(0,c.jsx)(n.A,{variant:"secondary",onClick:()=>{const e=x.map((e=>e.id));F(e)},children:"Select All"})}),(0,c.jsx)(n.A,{variant:"secondary",onClick:()=>e("/ai-template-generator"),children:"AI Generator"}),(0,c.jsx)(n.A,{variant:"primary",onClick:()=>{e("/email-templates/create")},children:"Create New Template"})]})]}),(0,c.jsxs)("div",{className:"flex gap-4 mb-6 border-b border-gray-700 pb-4",children:[(0,c.jsx)("button",{onClick:()=>w("system"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${"system"===y?"bg-indigo-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}\n           `,children:"System Templates"}),(0,c.jsx)("button",{onClick:()=>w("user"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${"user"===y?"bg-indigo-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}\n           `,children:"My Templates"})]}),"system"===y&&!b&&!N&&p.length>0&&(0,c.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:p.map((e=>(0,c.jsx)("button",{onClick:()=>j(e),className:`px-3 py-1 rounded-md text-xs font-medium transition-colors duration-150\n                ${v===e?"bg-indigo-500 text-white":"bg-gray-600 text-gray-200 hover:bg-gray-500"}\n              `,children:e.charAt(0).toUpperCase()+e.slice(1)},e)))}),(0,c.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:$}),(0,c.jsx)(o.a,{isOpen:A,onClose:D,title:"Confirm Deletion",onConfirm:async()=>{if(M)try{await i.jg.deleteTemplate(M),z(),D()}catch(s){var e,t;console.error("Error deleting template:",s),C((null===(e=s.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||s.message||"Failed to delete template"),D()}},confirmText:"Delete",confirmVariant:"danger",children:(0,c.jsx)("p",{children:"Are you sure you want to delete this template? This action cannot be undone."})}),(0,c.jsx)(o.a,{isOpen:V,onClose:Z,title:"Confirm Bulk Deletion",onConfirm:async()=>{if(0!==S.length)try{k(!0);for(const e of S)await i.jg.deleteTemplate(e);F([]),z(),Z()}catch(s){var e,t;console.error("Error deleting templates in bulk:",s),C((null===(e=s.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||s.message||"Failed to delete selected templates"),Z()}finally{k(!1)}},confirmText:"Delete Selected",confirmVariant:"danger",children:(0,c.jsxs)("p",{children:["Are you sure you want to delete ",S.length," selected template(s)? This action cannot be undone."]})})]})}},9774:(e,t,s)=>{s.d(t,{a:()=>i});var a=s(5043),r=s(6018),n=s(6443),l=s(8417),o=s(579);const i=e=>{let{isOpen:t,onClose:s,title:i,children:c,onConfirm:d,confirmText:m="Confirm",confirmVariant:h="primary",cancelText:x="Cancel"}=e;return(0,o.jsx)(r.e,{appear:!0,show:t,as:a.Fragment,children:(0,o.jsxs)(n.lG,{as:"div",className:"relative z-10",onClose:s,children:[(0,o.jsx)(r.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50"})}),(0,o.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,o.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,o.jsx)(r.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,o.jsxs)(n.lG.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all",children:[(0,o.jsx)(n.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-white mb-4",children:i}),(0,o.jsx)("div",{className:"mt-2 text-sm text-gray-300",children:c}),(0,o.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,o.jsx)(l.A,{variant:"secondary",onClick:s,children:x}),d&&(0,o.jsx)(l.A,{variant:h,onClick:d,children:m})]})]})})})})]})})}}}]);
//# sourceMappingURL=670.25132329.chunk.js.map