{"ast": null, "code": "import { useCollector } from './useCollector.js';\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js';\nexport function useMonitorOutput(monitor, collect, onCollect) {\n  const [collected, updateCollected] = useCollector(monitor, collect, onCollect);\n  useIsomorphicLayoutEffect(function subscribeToMonitorStateChange() {\n    const handlerId = monitor.getHandlerId();\n    if (handlerId == null) {\n      return;\n    }\n    return monitor.subscribeToStateChange(updateCollected, {\n      handlerIds: [handlerId]\n    });\n  }, [monitor, updateCollected]);\n  return collected;\n}", "map": {"version": 3, "names": ["useCollector", "useIsomorphicLayoutEffect", "useMonitorOutput", "monitor", "collect", "onCollect", "collected", "updateCollected", "subscribeToMonitorStateChange", "handlerId", "getHandlerId", "subscribeToStateChange", "handlerIds"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd\\src\\hooks\\useMonitorOutput.ts"], "sourcesContent": ["import type { Hand<PERSON><PERSON>anager, MonitorEventEmitter } from '../types/index.js'\nimport { useCollector } from './useCollector.js'\nimport { useIsomorphicLayoutEffect } from './useIsomorphicLayoutEffect.js'\n\nexport function useMonitorOutput<Monitor extends HandlerManager, Collected>(\n\tmonitor: Monitor & MonitorEventEmitter,\n\tcollect: (monitor: Monitor) => Collected,\n\tonCollect?: () => void,\n): Collected {\n\tconst [collected, updateCollected] = useCollector(monitor, collect, onCollect)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction subscribeToMonitorStateChange() {\n\t\t\tconst handlerId = monitor.getHandlerId()\n\t\t\tif (handlerId == null) {\n\t\t\t\treturn\n\t\t\t}\n\t\t\treturn monitor.subscribeToStateChange(updateCollected, {\n\t\t\t\thandlerIds: [handlerId],\n\t\t\t})\n\t\t},\n\t\t[monitor, updateCollected],\n\t)\n\n\treturn collected\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,yBAAyB,QAAQ,gCAAgC;AAE1E,OAAO,SAASC,gBAAgBA,CAC/BC,OAAsC,EACtCC,OAAwC,EACxCC,SAAsB,EACV;EACZ,MAAM,CAACC,SAAS,EAAEC,eAAe,CAAC,GAAGP,YAAY,CAACG,OAAO,EAAEC,OAAO,EAAEC,SAAS,CAAC;EAE9EJ,yBAAyB,CACxB,SAASO,6BAA6BA,CAAA,EAAG;IACxC,MAAMC,SAAS,GAAGN,OAAO,CAACO,YAAY,EAAE;IACxC,IAAID,SAAS,IAAI,IAAI,EAAE;MACtB;;IAED,OAAON,OAAO,CAACQ,sBAAsB,CAACJ,eAAe,EAAE;MACtDK,UAAU,EAAE,CAACH,SAAS;KACtB,CAAC;GACF,EACD,CAACN,OAAO,EAAEI,eAAe,CAAC,CAC1B;EAED,OAAOD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}