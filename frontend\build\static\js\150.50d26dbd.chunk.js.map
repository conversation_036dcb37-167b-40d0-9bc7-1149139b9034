{"version": 3, "file": "static/js/150.50d26dbd.chunk.js", "mappings": "gKAIA,MAkBA,EAlB2BA,KAEvBC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+DAA8DC,SAAA,EAC3EC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uCAAsCC,SAAC,SACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gDAA+CC,SAAC,oBAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,6FAGxCC,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAG,IACHJ,UAAU,qFAAoFC,SAC/F,mB", "sources": ["pages/NotFound.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nimport { Link } from 'react-router-dom';\r\n\r\nconst NotFound: React.FC = () => {\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center h-full text-center\">\r\n      <h1 className=\"text-6xl font-bold text-primary mb-4\">404</h1>\r\n      <h2 className=\"text-2xl font-semibold text-text-primary mb-4\">Page Not Found</h2>\r\n      <p className=\"text-text-secondary mb-8\">\r\n        Oops! The page you are looking for does not exist. It might have been moved or deleted.\r\n      </p>\r\n      <Link\r\n        to=\"/\"\r\n        className=\"px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors\"\r\n      >\r\n        Go Back Home\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotFound; "], "names": ["NotFound", "_jsxs", "className", "children", "_jsx", "Link", "to"], "sourceRoot": ""}