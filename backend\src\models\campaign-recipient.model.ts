import mongoose, {
  Document,
  Schema,
} from 'mongoose';

export interface ICampaignRecipient extends Document {
  campaignId: mongoose.Types.ObjectId;
  recipientId: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  email: string;
  status: 'pending' | 'sent' | 'delivered' | 'opened' | 'clicked' | 'bounced' | 'complained' | 'unsubscribed';
  sentAt?: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  clickedLinks?: { url: string; clickedAt: Date }[];
  bouncedAt?: Date;
  complainedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  lastEmailIndexSent?: number;
}

const campaignRecipientSchema = new Schema<ICampaignRecipient>(
  {
    campaignId: {
      type: Schema.Types.ObjectId,
      ref: 'Campaign',
      required: true
    },
    recipientId: {
      type: Schema.Types.ObjectId,
      ref: 'Contact',
      required: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true
    },
    status: {
      type: String,
      enum: ['pending', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'complained', 'unsubscribed'],
      default: 'pending'
    },
    sentAt: {
      type: Date
    },
    deliveredAt: {
      type: Date
    },
    openedAt: {
      type: Date
    },
    clickedAt: {
      type: Date
    },
    clickedLinks: [{
      url: { type: String, required: true },
      clickedAt: { type: Date, required: true, default: Date.now }
    }],
    bouncedAt: {
      type: Date
    },
    complainedAt: {
      type: Date
    },
    lastEmailIndexSent: {
      type: Number,
      default: -1
    }
  },
  {
    timestamps: true
  }
);

// Create a unique compound index on campaignId and recipientId
campaignRecipientSchema.index({ campaignId: 1, recipientId: 1 }, { unique: true });

const CampaignRecipient = mongoose.model<ICampaignRecipient>('CampaignRecipient', campaignRecipientSchema);

export default CampaignRecipient;
