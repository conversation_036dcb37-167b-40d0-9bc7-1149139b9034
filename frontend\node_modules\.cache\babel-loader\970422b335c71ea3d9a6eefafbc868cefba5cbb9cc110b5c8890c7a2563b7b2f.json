{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Input=_ref=>{let{id,name,type='text',label,placeholder,value,onChange,error,required=false,disabled=false,className='',helpText}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:`mb-4 ${className}`,children:[label&&/*#__PURE__*/_jsxs(\"label\",{htmlFor:id,className:\"form-label\",children:[label,required&&/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500 ml-1\",children:\"*\"})]}),/*#__PURE__*/_jsx(\"input\",{id:id,name:name,type:type,placeholder:placeholder,value:value,onChange:onChange,disabled:disabled,required:required,className:`w-full px-3 py-2 rounded-md shadow-md\n                    bg-gray-900 border border-gray-700 text-white placeholder-gray-400 \n                    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue focus:border-primary-blue \n                    ${error?'border-red-500 focus:ring-red-500':'border-gray-700'} \n                    ${disabled?'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed':''}`}),error&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-red-500\",children:error}),helpText&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-1 text-sm text-gray-500 dark:text-gray-400\",children:helpText})]});};export default Input;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Input", "_ref", "id", "name", "type", "label", "placeholder", "value", "onChange", "error", "required", "disabled", "className", "helpText", "children", "htmlFor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/Input.tsx"], "sourcesContent": ["import React from 'react';\n\nexport interface InputProps {\n  id: string;\n  name: string;\n  type?: string;\n  label?: React.ReactNode;\n  placeholder?: string;\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  error?: string;\n  required?: boolean;\n  disabled?: boolean;\n  className?: string;\n  helpText?: string;\n}\n\nconst Input: React.FC<InputProps> = ({\n  id,\n  name,\n  type = 'text',\n  label,\n  placeholder,\n  value,\n  onChange,\n  error,\n  required = false,\n  disabled = false,\n  className = '',\n  helpText,\n}) => {\n  return (\n    <div className={`mb-4 ${className}`}>\n      {label && (\n        <label htmlFor={id} className=\"form-label\">\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      <input\n        id={id}\n        name={name}\n        type={type}\n        placeholder={placeholder}\n        value={value}\n        onChange={onChange}\n        disabled={disabled}\n        required={required}\n        className={`w-full px-3 py-2 rounded-md shadow-md\n                    bg-gray-900 border border-gray-700 text-white placeholder-gray-400 \n                    focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue focus:border-primary-blue \n                    ${error ? 'border-red-500 focus:ring-red-500' : 'border-gray-700'} \n                    ${disabled ? 'bg-gray-800 border-gray-600 text-gray-500 cursor-not-allowed' : ''}`}\n      />\n      {error && <p className=\"mt-1 text-sm text-red-500\">{error}</p>}\n      {helpText && <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">{helpText}</p>}\n    </div>\n  );\n};\n\nexport default Input;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAiB1B,KAAM,CAAAC,KAA2B,CAAGC,IAAA,EAa9B,IAb+B,CACnCC,EAAE,CACFC,IAAI,CACJC,IAAI,CAAG,MAAM,CACbC,KAAK,CACLC,WAAW,CACXC,KAAK,CACLC,QAAQ,CACRC,KAAK,CACLC,QAAQ,CAAG,KAAK,CAChBC,QAAQ,CAAG,KAAK,CAChBC,SAAS,CAAG,EAAE,CACdC,QACF,CAAC,CAAAZ,IAAA,CACC,mBACEF,KAAA,QAAKa,SAAS,CAAE,QAAQA,SAAS,EAAG,CAAAE,QAAA,EACjCT,KAAK,eACJN,KAAA,UAAOgB,OAAO,CAAEb,EAAG,CAACU,SAAS,CAAC,YAAY,CAAAE,QAAA,EACvCT,KAAK,CACLK,QAAQ,eAAIb,IAAA,SAAMe,SAAS,CAAC,mBAAmB,CAAAE,QAAA,CAAC,GAAC,CAAM,CAAC,EACpD,CACR,cACDjB,IAAA,UACEK,EAAE,CAAEA,EAAG,CACPC,IAAI,CAAEA,IAAK,CACXC,IAAI,CAAEA,IAAK,CACXE,WAAW,CAAEA,WAAY,CACzBC,KAAK,CAAEA,KAAM,CACbC,QAAQ,CAAEA,QAAS,CACnBG,QAAQ,CAAEA,QAAS,CACnBD,QAAQ,CAAEA,QAAS,CACnBE,SAAS,CAAE;AACnB;AACA;AACA,sBAAsBH,KAAK,CAAG,mCAAmC,CAAG,iBAAiB;AACrF,sBAAsBE,QAAQ,CAAG,8DAA8D,CAAG,EAAE,EAAG,CAChG,CAAC,CACDF,KAAK,eAAIZ,IAAA,MAAGe,SAAS,CAAC,2BAA2B,CAAAE,QAAA,CAAEL,KAAK,CAAI,CAAC,CAC7DI,QAAQ,eAAIhB,IAAA,MAAGe,SAAS,CAAC,+CAA+C,CAAAE,QAAA,CAAED,QAAQ,CAAI,CAAC,EACrF,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}