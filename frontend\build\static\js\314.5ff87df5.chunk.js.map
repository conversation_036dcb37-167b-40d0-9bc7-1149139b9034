{"version": 3, "file": "static/js/314.5ff87df5.chunk.js", "mappings": "gMAQA,MAsKA,EAtK0BA,KACxB,MAAOC,EAASC,IAAcC,EAAAA,EAAAA,UAAS,KAChCC,EAASC,IAAcF,EAAAA,EAAAA,UAAS,KAChCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAASC,IAAcN,EAAAA,EAAAA,UAAS,KAChCO,EAASC,IAAcR,EAAAA,EAAAA,WAAS,GAsDvC,OAEES,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCF,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,gBAAeF,UAC5BF,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAACC,MAAM,6BAA4BJ,UACtCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,YAAWF,SAzDnB,CACf,CACEK,SAAU,kBACVC,OAAQ,kJAEV,CACED,SAAU,6BACVC,OAAQ,2MAEV,CACED,SAAU,qCACVC,OAAQ,uKAEV,CACED,SAAU,4BACVC,OAAQ,kLAEV,CACED,SAAU,kDACVC,OAAQ,+JAuCUC,KAAI,CAACC,EAAMC,KACnBR,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,0DAAyDF,SAAA,EAClFF,EAAAA,EAAAA,KAAA,MAAII,UAAU,2BAA0BF,SAAEQ,EAAKH,YAC/CP,EAAAA,EAAAA,KAAA,KAAGI,UAAU,sBAAqBF,SAAEQ,EAAKF,WAFjCG,YASlBR,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAACE,EAAAA,EAAI,CAACC,MAAM,kBAAiBJ,SAAA,CAC1BR,IACCM,EAAAA,EAAAA,KAACY,EAAAA,EAAK,CACJC,KAAK,QACLrB,QAASE,EACToB,QAASA,IAAMnB,EAAS,IACxBS,UAAU,SAIbR,IACCI,EAAAA,EAAAA,KAACY,EAAAA,EAAK,CACJC,KAAK,UACLrB,QAASI,EACTkB,QAASA,IAAMjB,EAAW,IAC1BO,UAAU,UAIdD,EAAAA,EAAAA,MAAA,QAAMY,SAjEKC,UAGnB,GAFAC,EAAEC,iBAEG7B,GAAYG,EAKjB,IACEO,GAAW,GACXJ,EAAS,UAMH,IAAIwB,SAAQC,GAAWC,WAAWD,EAAS,OAEjDvB,EAAW,0EACXP,EAAW,IACXG,EAAW,GACb,CAAE,MAAO6B,GAAW,IAADC,EAAAC,EACjB7B,GAAqB,QAAZ4B,EAAAD,EAAIG,gBAAQ,IAAAF,GAAM,QAANC,EAAZD,EAAcG,YAAI,IAAAF,OAAN,EAAZA,EAAoBhC,UAAW,mCAC1C,CAAC,QACCO,GAAW,EACb,MArBEJ,EAAS,4BAqBX,EAwCqCO,SAAA,EAC3BF,EAAAA,EAAAA,KAAC2B,EAAAA,EAAK,CACJC,GAAG,UACHC,KAAK,UACLC,MAAM,UACNC,MAAO1C,EACP2C,SAAWf,GAAM3B,EAAW2B,EAAEgB,OAAOF,OACrCG,UAAQ,EACR9B,UAAU,UAGZD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOgC,QAAQ,UAAU/B,UAAU,aAAYF,SAAA,CAAC,YACtCF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,eAAcF,SAAC,UAEzCF,EAAAA,EAAAA,KAAA,YACE4B,GAAG,UACHC,KAAK,UACLE,MAAOvC,EACPwC,SAAWf,GAAMxB,EAAWwB,EAAEgB,OAAOF,OACrCG,UAAQ,EACRE,KAAM,EACNhC,UAAU,0BAIdJ,EAAAA,EAAAA,KAACqC,EAAAA,EAAM,CACLxB,KAAK,SACLyB,SAAUxC,EAAQI,SAEjBJ,EAAU,gBAAkB,0BAKnCE,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAACC,MAAM,kBAAkBF,UAAU,OAAMF,UAC5CC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,YAAWF,SAAA,EACvBF,EAAAA,EAAAA,KAAA,MAAAE,UACEC,EAAAA,EAAAA,MAAA,KAAGoC,KAAK,IAAInC,UAAU,qDAAoDF,SAAA,EACxEF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,OAAMF,SAAC,iBAAS,sBAIpCF,EAAAA,EAAAA,KAAA,MAAAE,UACEC,EAAAA,EAAAA,MAAA,KAAGoC,KAAK,IAAInC,UAAU,qDAAoDF,SAAA,EACxEF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,OAAMF,SAAC,iBAAS,uBAIpCF,EAAAA,EAAAA,KAAA,MAAAE,UACEC,EAAAA,EAAAA,MAAA,KAAGoC,KAAK,IAAInC,UAAU,qDAAoDF,SAAA,EACxEF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,OAAMF,SAAC,iBAAS,wBAIpCF,EAAAA,EAAAA,KAAA,MAAAE,UACEC,EAAAA,EAAAA,MAAA,KAAGoC,KAAK,IAAInC,UAAU,qDAAoDF,SAAA,EACxEF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,OAAMF,SAAC,iBAAS,iCAS9C,C", "sources": ["pages/Support.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\n// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from '../components/Card';\nimport Input from '../components/Input';\n\nconst Support: React.FC = () => {\n  const [subject, setSubject] = useState('');\n  const [message, setMessage] = useState('');\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  \n  // FAQ items\n  const faqItems = [\n    {\n      question: 'What is a flow?',\n      answer: 'A flow is a series of emails sent to your contacts. Each flow costs $10 and includes 10 emails that can be sent to up to 1000 recipients each.'\n    },\n    {\n      question: 'How do I verify my domain?',\n      answer: 'Go to the Campaigns section and click \"Register Domain\". Enter your domain name and follow the instructions to add the required DNS records. Once verified, your domain status will update to \"Active\".'\n    },\n    {\n      question: 'How do I create an email campaign?',\n      answer: 'Go to the Campaigns section, create a new campaign, and use the drag-and-drop editor to design your emails. You can then schedule them to be sent to your contacts.'\n    },\n    {\n      question: 'How do I import contacts?',\n      answer: 'Go to the Contacts section and click \"Upload Contacts\". You can upload a CSV file with your contacts\\' information. Make sure it includes at least the email and name columns.'\n    },\n    {\n      question: 'How do I track the performance of my campaigns?',\n      answer: 'Go to the Analytics section to view detailed reports on your campaigns, including open rates, click rates, and more. You can also export this data as CSV.'\n    }\n  ];\n  \n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!subject || !message) {\n      setError('Please fill in all fields');\n      return;\n    }\n    \n    try {\n      setLoading(true);\n      setError('');\n      \n      // This would be an actual API call in the real app\n      // await axios.post('/api/v1/support/request', { subject, message });\n      \n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      setSuccess('Your support request has been submitted. We will get back to you soon.');\n      setSubject('');\n      setMessage('');\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to submit support request');\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  return (\n    // <Layout title=\"Help & Support\">\n    <>\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2\">\n          <Card title=\"Frequently Asked Questions\">\n            <div className=\"space-y-6\">\n              {faqItems.map((item, index) => (\n                <div key={index} className=\"border-b border-gray-700 pb-4 last:border-b-0 last:pb-0\">\n                  <h3 className=\"text-lg font-medium mb-2\">{item.question}</h3>\n                  <p className=\"text-text-secondary\">{item.answer}</p>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </div>\n        \n        <div>\n          <Card title=\"Contact Support\">\n            {error && (\n              <Alert\n                type=\"error\"\n                message={error}\n                onClose={() => setError('')}\n                className=\"mb-4\"\n              />\n            )}\n            \n            {success && (\n              <Alert\n                type=\"success\"\n                message={success}\n                onClose={() => setSuccess('')}\n                className=\"mb-4\"\n              />\n            )}\n            \n            <form onSubmit={handleSubmit}>\n              <Input\n                id=\"subject\"\n                name=\"subject\"\n                label=\"Subject\"\n                value={subject}\n                onChange={(e) => setSubject(e.target.value)}\n                required\n                className=\"mb-4\"\n              />\n              \n              <div className=\"mb-4\">\n                <label htmlFor=\"message\" className=\"form-label\">\n                  Message <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  value={message}\n                  onChange={(e) => setMessage(e.target.value)}\n                  required\n                  rows={6}\n                  className=\"form-input w-full\"\n                ></textarea>\n              </div>\n              \n              <Button\n                type=\"submit\"\n                disabled={loading}\n              >\n                {loading ? 'Submitting...' : 'Submit Request'}\n              </Button>\n            </form>\n          </Card>\n          \n          <Card title=\"Other Resources\" className=\"mt-6\">\n            <ul className=\"space-y-3\">\n              <li>\n                <a href=\"#\" className=\"text-primary hover:text-blue-400 flex items-center\">\n                  <span className=\"mr-2\">📄</span>\n                  Documentation\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-primary hover:text-blue-400 flex items-center\">\n                  <span className=\"mr-2\">📚</span>\n                  Knowledge Base\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-primary hover:text-blue-400 flex items-center\">\n                  <span className=\"mr-2\">📹</span>\n                  Video Tutorials\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-primary hover:text-blue-400 flex items-center\">\n                  <span className=\"mr-2\">🔄</span>\n                  System Status\n                </a>\n              </li>\n            </ul>\n          </Card>\n        </div>\n      </div>\n    </>\n    // </Layout>\n  );\n};\n\nexport default Support;\n"], "names": ["Support", "subject", "setSubject", "useState", "message", "setMessage", "error", "setError", "success", "setSuccess", "loading", "setLoading", "_jsx", "_Fragment", "children", "_jsxs", "className", "Card", "title", "question", "answer", "map", "item", "index", "<PERSON><PERSON>", "type", "onClose", "onSubmit", "async", "e", "preventDefault", "Promise", "resolve", "setTimeout", "err", "_err$response", "_err$response$data", "response", "data", "Input", "id", "name", "label", "value", "onChange", "target", "required", "htmlFor", "rows", "<PERSON><PERSON>", "disabled", "href"], "sourceRoot": ""}