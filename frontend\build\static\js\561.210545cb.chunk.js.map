{"version": 3, "file": "static/js/561.210545cb.chunk.js", "mappings": "8KA0BA,MAAMA,GAAaC,EAAAA,EAAAA,aACjB,CAAAC,EAAkEC,KAAS,IAA1E,YAAEC,EAAc,GAAE,YAAEC,EAAc,GAAE,OAAEC,EAAM,OAAEC,EAAS,QAAQL,EAC9D,MAAMM,GAAYC,EAAAA,EAAAA,QAAuB,MACnCC,GAAeD,EAAAA,EAAAA,QAAsB,MAkT3C,OA/SAE,EAAAA,EAAAA,YAAU,KACR,GAAKH,EAAUI,QAAf,CAGIF,EAAaE,UACfC,QAAQC,IAAI,qDACZJ,EAAaE,QAAQG,UACrBL,EAAaE,QAAU,MAGzBC,QAAQC,IAAI,+CAAgD,CAC1DE,UAAWZ,EACXa,YAAuB,OAAXb,QAAW,IAAXA,OAAW,EAAXA,EAAac,SAAU,EACnCC,UAAWd,EACXe,YAAuB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAaa,SAAU,IAGrC,IACE,MAAMG,EAASC,EAAAA,GAASC,KAAK,CAC3BC,UAAWhB,EAAUI,QACrBa,aAAa,EACblB,OAAQmB,OAAOnB,GACfoB,MAAO,OACPC,gBAAgB,EAChBC,QAAS,CAACC,KACVC,YAAa,CACX,gBAAiB,CAGdC,cAAc,EACdC,aAAa,MAQpB,IAAKZ,EAEH,YADAR,QAAQqB,MAAM,4CAoFhB,IAAIC,EAhFJzB,EAAaE,QAAUS,EAGlBA,EAAOe,SAASC,IAAI,mBACvBxB,QAAQC,IAAI,0DACZO,EAAOe,SAASE,IAAI,gBAAiB,CACnCC,IAAMlB,IACJ,MAAMmB,EAAOnB,EAAOoB,UAEpB,MAAO,CACLD,KAAMA,EACNE,KAAMF,EACP,KAKFnB,EAAOe,SAASC,IAAI,kBACvBxB,QAAQC,IAAI,yDACZO,EAAOe,SAASE,IAAI,eAAgB,CAClCC,IAAMlB,GACGA,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,kBAMhDC,YAAW,KACT,GAAKnC,EAAaE,QAMlB,GAAKF,EAAaE,QAAQkC,cAK1B,IAEE,GAAI1C,EAAa,CACfS,QAAQC,IAAI,qCAAsCV,EAAY2C,UAAU,EAAG,KAAO,OAClF,IACErC,EAAaE,QAAQkC,cAAc1C,GACnCS,QAAQC,IAAI,gDACd,CAAE,MAAOkC,GACPnC,QAAQqB,MAAM,2CAA4Cc,GAEtD3C,IACFQ,QAAQC,IAAI,qDACZJ,EAAaE,QAAQkC,cAAczC,GACnCQ,QAAQC,IAAI,qDAEhB,CACF,MAAWT,GACTQ,QAAQC,IAAI,yDAA0DT,EAAY0C,UAAU,EAAG,KAAO,OACtGrC,EAAaE,QAAQkC,cAAczC,GACnCQ,QAAQC,IAAI,mDAGZD,QAAQC,IAAI,8DACZJ,EAAaE,QAAQkC,cAAc,oVAYvC,CAAE,MAAOZ,GACPrB,QAAQqB,MAAM,+CAAgDA,EAChE,MAzCErB,QAAQqB,MAAM,oEANdrB,QAAQqB,MAAM,2DA+ChB,GACC,KAIH,IAAIe,GAAW,EAmEf,OAhEA5B,EAAO6B,GAAG,uBAAuB,KAC3B5C,GAAUe,IAAW4B,IAEnBd,GAAagB,aAAahB,GAG9BA,EAAcU,YAAW,KACvB,IACEI,GAAW,EAEX,IAAIG,EAAY,GACZC,EAAY,GAEhB,IAEE,MAAMC,EAAWjC,EAAOkC,WAAW,iBAC/BD,GAAgC,kBAAbA,IACrBF,EAAYE,EAASd,MAAQ,GAC7Ba,EAAYC,EAASZ,MAAQ,GAC7B7B,QAAQC,IAAI,qDAEhB,CAAE,MAAO0C,GACN3C,QAAQ4C,KAAK,0DAA2DD,EAC3E,CAGA,IAAKJ,IAAcC,EAAW,CAC1BD,EAAY/B,EAAOoB,WAAa,GAChC,IAEEY,EAAYhC,EAAOkC,WAAW,iBAAmB,EACnD,CAAE,MAAMG,GACL7C,QAAQ4C,KAAK,iCAAkCC,EAClD,CAEKL,IACHA,EAAYhC,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,IAEpE/B,QAAQC,IAAI,uDAChB,CAGA,IAAKsC,EAAUO,OAGb,OAFA9C,QAAQC,IAAI,4DACZmC,GAAW,GAIbpC,QAAQC,IAAI,6CAGZR,EAAO8C,EAAWC,GAClBxC,QAAQC,IAAI,yCAEf,CAAE,MAAOoB,GACLrB,QAAQqB,MAAM,uCAAwCA,EAC1D,CAAC,QACGe,GAAW,CACf,IACE,KACL,IAIK,KAEL,GADId,GAAagB,aAAahB,GAC1BzB,EAAaE,QAAS,CAEvB,IACEF,EAAaE,QAAQG,SACvB,CAAE,MAAO6C,GACP/C,QAAQqB,MAAM,4CAA6C0B,EAC7D,CACAlD,EAAaE,QAAU,IAC1B,EAEJ,CAAE,MAAOiD,GACPhD,QAAQqB,MAAM,4DAA6D2B,EAC7E,CA5M8B,CA4M9B,GACC,CAACzD,EAAaC,EAAaE,EAAQD,KAGtCwD,EAAAA,EAAAA,qBAAoB3D,GAAK,MACvB4D,KAAMC,UACJ,IAAIC,EAAgB,CAAEzB,KAAM,GAAIE,KAAM,IACtC,GAAIhC,EAAaE,QACd,IAEI,MAAMS,EAASX,EAAaE,QAC5B,IAAIS,EAAOe,SAASC,IAAI,iBAetB,MAAM,IAAI6B,MAAM,uCAfwB,CAExC,MAAMC,EAAS9C,EAAOkC,WAAW,iBAEjC,KAAIY,GAA4B,kBAAXA,GAAuB,SAAUA,GAAU,SAAUA,GAOvE,MAFAtD,QAAQ4C,KAAK,6EAEP,IAAIS,MAAM,yCANjBD,EAAczB,KAAO2B,EAAO3B,MAAQ,GACpCyB,EAAcvB,KAAOyB,EAAOzB,MAAQ,GACpC7B,QAAQC,IAAI,qDAAsD,CAAE0B,KAAMyB,EAAczB,KAAKO,UAAU,EAAE,IAAI,MAAOL,KAAMuB,EAAcvB,KAAKK,UAAU,EAAE,IAAI,OAMjK,CAIJ,CAAE,MAAOS,GACL3C,QAAQ4C,KAAK,+DAAgED,GAC7E,IAEE,MAAMnC,EAASX,EAAaE,QACtBwD,EAAU/C,EAAOoB,WAAa,GACpC,IAAI4B,EAAgB,GAIlBA,EADEhD,EAAOe,SAASC,IAAI,gBACNhB,EAAOkC,WAAW,iBAAmB,GAGrClC,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,GAGpEwB,GAAWC,GACXJ,EAAczB,KAAO4B,EACrBH,EAAcvB,KAAO2B,GAAiBD,EACtCvD,QAAQC,IAAI,oEAEZD,QAAQqB,MAAM,0EAEpB,CAAE,MAAOoC,GACNzD,QAAQqB,MAAM,8DAA+DoC,EAChF,CACJ,MAEDzD,QAAQqB,MAAM,oDAOhB,SAHM,IAAIqC,SAAQC,GAAW3B,WAAW2B,EAAS,OAG7C9D,EAAaE,UAAYqD,EAAcvB,KAAKiB,OAAQ,CACpD9C,QAAQC,IAAI,8DACZ,IACK,MAAMO,EAASX,EAAaE,QAE5B,IAAI6D,EAAyB,GACzBpD,EAAOe,SAASC,IAAI,kBACtBoC,EAAyBpD,EAAOkC,WAAW,iBAGxCkB,IACHA,EAAyBpD,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,IAG7E6B,EAAuBd,QACvB9C,QAAQC,IAAI,8DACZmD,EAAcvB,KAAO+B,IAEtB5D,QAAQC,IAAI,4DAERmD,EAAczB,OAASyB,EAAcvB,OACvCuB,EAAcvB,KAAOuB,EAAczB,MAG7C,CAAE,MAAOkC,GACL7D,QAAQqB,MAAM,iEAAkEwC,EACpF,CACJ,CAGA,OAAOT,CAAa,EAErBU,UAAWA,IAAMjE,EAAaE,aAG1BgE,EAAAA,EAAAA,KAAA,OAAKzE,IAAKK,EAAWqE,MAAO,CAAEtE,OAAQA,IAAY,IAK7DP,EAAW8E,YAAc,aAEzB,S,2GC/TA,MAqHA,EArHoE5E,IAK7D,IAL8D,OACnE6E,EAAM,QACNC,EAAO,SACPC,EAAQ,YACRC,GACDhF,EACC,MAAOiF,EAAmBC,IAAwBC,EAAAA,EAAAA,UAAiB,KAC5DC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCnD,EAAOsD,IAAYH,EAAAA,EAAAA,UAAS,KAGnC1E,EAAAA,EAAAA,YAAU,KACR,GAAIoE,GAAUE,EAAU,CACtB,IAAIQ,EAAkB,GACtB,GAAIR,EAASS,aACX,IAEE,MAAMC,EAAe,IAAIC,KAAKX,EAASS,cAEvC,GAAKG,MAAMF,EAAaG,WAatBjF,QAAQ4C,KAAK,kDAAmDwB,EAASS,kBAbvC,CAKlC,MAAMK,EAAOJ,EAAaK,cACpBC,GAASN,EAAaO,WAAa,GAAGC,WAAWC,SAAS,EAAG,KAC7DC,EAAMV,EAAaW,UAAUH,WAAWC,SAAS,EAAG,KACpDG,EAAQZ,EAAaa,WAAWL,WAAWC,SAAS,EAAG,KAE7DX,EAAkB,GAAGM,KAAQE,KAASI,KAAOE,KAD7BZ,EAAac,aAAaN,WAAWC,SAAS,EAAG,OAEjEvF,QAAQC,IAAI,4CAA4CmE,EAASS,8BAA8BD,IACjG,CAGF,CAAE,MAAOzC,GACPnC,QAAQqB,MAAM,wDAAyDc,EACzE,CAIF,IAAKyC,EAAiB,CACpB,MAAMiB,EAAc,IAAId,KAAKA,KAAKe,MAAQ,MAO1ClB,EAAkB,GALLiB,EAAYV,kBACVU,EAAYR,WAAa,GAAGC,WAAWC,SAAS,EAAG,QACtDM,EAAYJ,UAAUH,WAAWC,SAAS,EAAG,QAC3CM,EAAYF,WAAWL,WAAWC,SAAS,EAAG,QAC5CM,EAAYD,aAAaN,WAAWC,SAAS,EAAG,OAEhEvF,QAAQC,IAAI,wDAAwD2E,IACtE,CAEAL,EAAqBK,GACrBD,EAAS,GACX,IACC,CAACT,EAAQE,IAEZ,MAqBM2B,EAAcA,KAClBrB,GAAW,GACXC,EAAS,IACTJ,EAAqB,IACrBJ,GAAS,EAGX,OACE6B,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CAAC/B,OAAQA,EAAQC,QAAS4B,EAAaG,MAAO,uBAA8B,OAAR9B,QAAQ,IAARA,OAAQ,EAARA,EAAU+B,OAAQ,KAAKC,SAAA,EAC/FJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,YAAWD,SAAA,CACvB/E,IAAS0C,EAAAA,EAAAA,KAACuC,EAAAA,EAAK,CAACC,KAAK,QAAQC,QAASnF,EAAO8C,QAASA,IAAMQ,EAAS,OAEtEZ,EAAAA,EAAAA,KAAA,KAAGsC,UAAU,sBAAqBD,SAAC,8DAEnCrC,EAAAA,EAAAA,KAAC0C,EAAAA,EAAK,CACJC,GAAG,yBACHP,KAAK,yBACLI,KAAK,iBACLI,MAAOrC,EACPsC,SAAWzE,GAAMoC,EAAqBpC,EAAE0E,OAAOF,OAC/CG,MAAM,wBACNC,UAAQ,EACRV,UAAU,YAEZtC,EAAAA,EAAAA,KAAA,KAAGsC,UAAU,iDAAgDD,SAAC,0CAGhEJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,8BAA6BD,SAAA,EAC1CrC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASnB,EAAaoB,SAAU1C,EAAQ2B,SAAC,YAGrErC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACE,QApDS/D,UACrB,GAAKiB,GAAaE,EAAlB,CAKAI,GAAW,GACXC,EAAS,IACT,IACE,MAAMyC,EAAoB,IAAIrC,KAAKT,GAAmB+C,oBAChDC,EAAAA,EAAYC,iBAAiBnD,EAASoD,IAAKJ,GACjD/C,EAAY,aAAaD,EAAS+B,oCAAoC,IAAIpB,KAAKT,GAAmBmD,oBACpG,CAAE,MAAOC,GAAW,IAADC,EAAAC,EACjB5H,QAAQqB,MAAM,6BAA8BqG,GAC5C/C,GAAqB,QAAZgD,EAAAD,EAAIG,gBAAQ,IAAAF,GAAM,QAANC,EAAZD,EAAcG,YAAI,IAAAF,OAAN,EAAZA,EAAoBpB,UAAW,+BAC1C,CAAC,QACC9B,GAAW,EACb,CAbA,MAFEC,EAAS,uCAeX,EAmCqCwC,SAAU1C,IAAYH,EAAkB8B,SACtE3B,EAAU,gBAAkB,2BAG3B,C,+MCxGZ,MA+dA,EA/d+BsD,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EACnC,MAAM,GAAEzB,IAAO0B,EAAAA,EAAAA,MACT,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,OAGVpE,EAAUqE,IAAejE,EAAAA,EAAAA,UAAc,OACvCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCkE,EAAQC,IAAanE,EAAAA,EAAAA,WAAS,IAC9BnD,EAAOsD,IAAYH,EAAAA,EAAAA,UAAS,KAC5BoE,EAASC,IAAcrE,EAAAA,EAAAA,UAAS,KAGhCsE,EAAcC,IAAmBvE,EAAAA,EAAAA,UAAiB,IAClDwE,EAAoBC,IAAyBzE,EAAAA,EAAAA,UAAmB,KAChE0E,EAAeC,IAAoB3E,EAAAA,EAAAA,WAA2C,IACnF4E,MAAMC,KAAK,CAAEhJ,OAAQ,KAAM,MAASsB,KAAM,GAAIE,KAAM,SAEhDlC,GAAYC,EAAAA,EAAAA,QAAsB,OAGjC0J,EAAcC,IAAmB/E,EAAAA,EAAAA,UAAS,KAC1CgF,EAASC,IAAcjF,EAAAA,EAAAA,UAAS,KAChCkF,EAAUC,IAAenF,EAAAA,EAAAA,UAAS,KAClCoF,EAAWC,IAAgBrF,EAAAA,EAAAA,UAAS,KACpCsF,EAASC,IAAcvF,EAAAA,EAAAA,UAAS,KAGhCwF,EAAmBC,IAAwBzF,EAAAA,EAAAA,WAAS,IACpD0F,EAAoBC,IAAyB3F,EAAAA,EAAAA,UAAc,OAG3D4F,EAAoBC,IAAyB7F,EAAAA,EAAAA,WAAS,IACtD8F,EAAeC,KAAoB/F,EAAAA,EAAAA,UAAgB,KACnDgG,GAAkBC,KAAuBjG,EAAAA,EAAAA,WAAS,IAClDkG,GAAkBC,KAAuBnG,EAAAA,EAAAA,UAAqB,OAKrE1E,EAAAA,EAAAA,YAAU,KACcqD,WAClB,IAAKuD,GAAa,cAAPA,EAGP,OAFA1G,QAAQC,IAAI,iDACZsI,EAAS,cAGbvI,QAAQC,IAAI,6BAA8ByG,GAC1ChC,GAAW,GACX,IACI,MAAMmD,QAAiBP,EAAAA,EAAYsD,YAAYlE,GACzCmE,EAAgBhD,EAASC,KAAK1D,UAAYyD,EAASC,KACzD,GAAI+C,EAAe,CAAC,IAADC,EACfrC,EAAYoC,GACZtB,EAAgBsB,EAAc1E,MAAQ,IACtCsD,EAAWoB,EAAcrB,SAAW,IACpCG,EAAYkB,EAAcnB,UAAY,IACtCG,EAAsC,YAArB,OAAJxB,QAAI,IAAJA,GAAY,QAARyC,EAAJzC,EAAM0C,cAAM,IAAAD,OAAR,EAAJA,EAAcE,QAAsB,WAAW3C,EAAK0C,OAAO5E,OAAU0E,EAAcjB,WAAa,IAC7GG,EAAWc,EAAcf,SAAWe,EAAcjB,WAAa,IAG/D,MAAMqB,EAAkB7B,MAAMC,KAAK,CAAEhJ,OAAQ,KAAM,MAASsB,KAAM,GAAIE,KAAM,OACxEgJ,EAAc3B,eAAiBE,MAAM8B,QAAQL,EAAc3B,eAC3D2B,EAAc3B,cAAciC,SAAQ,CAACC,EAAcC,KAC3CA,EAAQJ,EAAgB5K,SACxB4K,EAAgBI,GAAS,CACrB1J,KAAMyJ,EAAQzJ,MAAQ,GACtBE,KAAMuJ,EAAQvJ,MAAQ,IAE9B,KAEGgJ,EAAcS,aAAeT,EAAcU,eAClDN,EAAgB,GAAK,CACjBtJ,KAAMkJ,EAAcU,aAAe,GACnC1J,KAAMgJ,EAAcS,aAAe,KAG3CnC,EAAiB8B,GACjBvG,GAAW,EACf,MACIC,EAAS,sBACTD,GAAW,EAEnB,CAAE,MAAOgD,GAAW,IAADC,EAAAC,EACf5H,QAAQqB,MAAM,2BAA4BqG,GAC1C/C,GAAqB,QAAZgD,EAAAD,EAAIG,gBAAQ,IAAAF,GAAM,QAANC,EAAZD,EAAcG,YAAI,IAAAF,OAAN,EAAZA,EAAoBpB,UAAW,4BACxC9B,GAAW,EACf,GAEJ8G,EAAe,GACd,CAAC9E,EAAI2B,EAAME,KAGdzI,EAAAA,EAAAA,YAAU,KACR,MAAM2L,EAA0B,GAChCvC,EAAciC,SAAQ,CAACO,EAAOL,KACxBK,EAAM7J,MAAQ6J,EAAM7J,KAAKiB,QAC3B2I,EAAcE,KAAKN,EACrB,IAEFpC,EAAsBwC,EAAc,GACnC,CAACvC,KAGJpJ,EAAAA,EAAAA,YAAU,KACJsK,IACAK,IAAoB,GACpBmB,EAAAA,GAA8BC,kBACzBC,MAAKjE,IACEA,EAASe,QACT2B,GAAiB1C,EAASC,MAE1BnD,EAAS,2BACb,IAEHoH,OAAMrE,IACH1H,QAAQqB,MAAM,2BAA4BqG,GAC1C/C,EAAS,8CAA8C,IAE1DqH,SAAQ,KACLvB,IAAoB,EAAM,IAEtC,GACC,CAACL,IAKJ,MA2BM6B,GAAoBA,KAQxB,GAPAjM,QAAQC,IAAI,mCAAoCyK,GAAmB,CAC/DhE,GAAIgE,GAAiBhE,IAAMgE,GAAiBlD,IAC5CrB,KAAMuE,GAAiBvE,KACvBhG,UAAWuK,GAAiBa,YAC5BW,aAAcxB,GAAiBU,SAC/B,wBAEAV,GAAkB,CAClB,MAAMyB,EAAU,IAAIjD,GAkBpB,GAjBAiD,EAAQrD,EAAe,GAAK,CACxBnH,KAAM+I,GAAiBa,aAAe,GACtC1J,KAAM6I,GAAiBU,SAAW,IAGtCpL,QAAQC,IAAI,kCAAmC,CAC3CmM,WAAYtD,EAAe,EAC3B3I,UAAWgM,EAAQrD,EAAe,GAAGnH,KACrCrB,UAAW6L,EAAQrD,EAAe,GAAGjH,KACrCzB,WAAY+L,EAAQrD,EAAe,GAAGnH,KAAKtB,OAC3CE,WAAY4L,EAAQrD,EAAe,GAAGjH,KAAKxB,SAI/C8I,EAAiBgD,GAGbzB,GAAiBU,SAAWV,GAAiBU,QAAQtI,OAAQ,CAC7D,MAAMuJ,EAAevD,EAAe,EACpCG,GAAsBqD,GAClBA,EAAKC,SAASF,GACRC,EACA,IAAIA,EAAMD,GAAcG,MAAK,CAACC,EAAGC,IAAMD,EAAIC,KAEzD,CAGA/B,GAAoB,MACpBN,GAAsB,GAItBrI,YAAW,KAES+C,KAAKe,MAIrBiD,GAAiB,GAGjB/G,YAAW,KACP+G,EAAgBD,EAAa,GAC9B,GAAG,GACP,IACP,GA6IF,OAAIrE,IAAYL,GAEZL,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,wCAAuCD,UACpDrC,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,+EAKhBjC,GAAaK,EAebL,GAGH4B,EAAAA,EAAAA,MAAA2G,EAAAA,SAAA,CAAAvG,SAAA,EACEJ,EAAAA,EAAAA,MAAA,MAAIK,UAAU,wDAAuDD,SAAA,CAAC,kBAAgBkD,IAAwB,OAARlF,QAAQ,IAARA,OAAQ,EAARA,EAAU+B,UAChHH,EAAAA,EAAAA,MAAC4G,EAAAA,EAAI,CAAAxG,SAAA,CACA/E,IAAS0C,EAAAA,EAAAA,KAACuC,EAAAA,EAAK,CAACC,KAAK,QAAQC,QAASnF,EAAO8C,QAASA,IAAMQ,EAAS,IAAK0B,UAAU,SACpFuC,IAAW7E,EAAAA,EAAAA,KAACuC,EAAAA,EAAK,CAACC,KAAK,UAAUC,QAASoC,EAASzE,QAASA,IAAM0E,EAAW,IAAKxC,UAAU,UAG7FL,EAAAA,EAAAA,MAAA,OAAKK,UAAU,4FAA2FD,SAAA,EACxGrC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACE,QA9HW/D,UAC3B,IAAKuD,EAAuC,YAAjC/B,EAAS,uBACpB,IAAK2E,IAAiBE,IAAYE,IAAaE,EAE7C,YADAjF,EAAS,iDAKX,MAAMkI,EAAqB3D,EAAc4D,QAAO,CAACpB,EAAOL,IACpDrC,EAAmBuD,SAASlB,IAAUK,EAAM7J,MAAQ6J,EAAM7J,KAAKiB,SAOnE,IAAK,IAADiK,EAAAC,EAAAC,EACFtE,GAAU,GAAOhE,EAAS,IAAKkE,EAAW,IAC1C,MAAMqE,EAAsC,YAArB,OAAJ7E,QAAI,IAAJA,GAAY,QAAR0E,EAAJ1E,EAAM0C,cAAM,IAAAgC,OAAR,EAAJA,EAAc/B,QAAsB,WAAW3C,EAAK0C,OAAO5E,OAASyD,EAEjFuD,EAAoB,CACxBhH,KAAMmD,EACNE,UACAE,WACAE,UAAWsD,EACXpD,QAASA,GAAWoD,EAGpBhE,cAAe2D,EAAmBO,KAAIjL,IAAC,CAAOR,KAAMQ,EAAER,KAAME,KAAMM,EAAEN,SAEpEyJ,aAAkC,QAArB0B,EAAAH,EAAmB,UAAE,IAAAG,OAAA,EAArBA,EAAuBnL,OAAQ,IAI9C7B,QAAQC,IAAI,gDAAiDkN,GAC7D,MAAMtF,QAAiBP,EAAAA,EAAY+F,eAAe3G,EAAIyG,GAEtDtE,EAAW,qDACM,QAAboE,EAAApF,EAASC,YAAI,IAAAmF,GAAbA,EAAe7I,UAAYyD,EAASC,OACtCW,EAAYZ,EAASC,KAAK1D,UAAYyD,EAASC,MAEjD9F,YAAW,IAAM6G,EAAW,KAAK,IAEnC,CAAE,MAAOnB,GAAW,IAAD4F,EAAAC,EACjBvN,QAAQqB,MAAM,2BAA4BqG,GAC1C/C,GAAqB,QAAZ2I,EAAA5F,EAAIG,gBAAQ,IAAAyF,GAAM,QAANC,EAAZD,EAAcxF,YAAI,IAAAyF,OAAN,EAAZA,EAAoB/G,UAAW,4BAC1C,CAAC,QACCmC,GAAU,EACZ,GA8E+CxB,SAAUuB,GAAUjE,EAAS+I,KAAK,KAAKnH,UAAU,cAAaD,SAClGsC,EAAS,YAAc,4BAE1B1C,EAAAA,EAAAA,MAAA,OAAKK,UAAU,iBAAgBD,SAAA,EAC3BrC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,YAAYuG,KAAK,KAAKtG,QAASA,IAAMqB,EAAS,yBAAyB7B,KAAOS,SAAUuB,GAAUjE,EAAQ2B,SAAC,uBAG3HrC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,YAAYuG,KAAK,KAAKtG,QA5J1BuG,KACpBrJ,IACF+F,EAAsB,CACpB3C,IAAKpD,EAASoD,IACdrB,KAAMmD,EACNzE,aAAcT,EAASS,eAEzBoF,GAAqB,GACvB,EAoJ8E9C,SAAUuB,GAAUjE,EAAQ2B,SACtE,eAAb,OAARhC,QAAQ,IAARA,OAAQ,EAARA,EAAU4G,QAAyB,aAAe,uBAEpDjH,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,YAAYuG,KAAK,KAAKtG,QAASA,IAAMqB,EAAS,cAAcnC,SAAC,6BAOpFJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,wCAAuCD,SAAA,EAEpDJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,0BAAyBD,SAAA,EACtCrC,EAAAA,EAAAA,KAAA,MAAIsC,UAAU,6BAA4BD,SAAC,sBAC3CrC,EAAAA,EAAAA,KAAC0C,EAAAA,EAAK,CAACC,GAAG,eAAeP,KAAK,eAAeW,MAAM,gBAAgBH,MAAO2C,EAAc1C,SAAWzE,GAAMoH,EAAgBpH,EAAE0E,OAAOF,OAAQI,UAAQ,KAClJhD,EAAAA,EAAAA,KAAC0C,EAAAA,EAAK,CAACC,GAAG,UAAUP,KAAK,UAAUW,MAAM,gBAAgBH,MAAO6C,EAAS5C,SAAWzE,GAAMsH,EAAWtH,EAAE0E,OAAOF,OAAQI,UAAQ,KAC9HhD,EAAAA,EAAAA,KAAC0C,EAAAA,EAAK,CAACC,GAAG,WAAWP,KAAK,WAAWW,MAAM,YAAYH,MAAO+C,EAAU9C,SAAWzE,GAAMwH,EAAYxH,EAAE0E,OAAOF,OAAQI,UAAQ,KAC9HhD,EAAAA,EAAAA,KAAC0C,EAAAA,EAAK,CAACC,GAAG,YAAYP,KAAK,YAAYW,MAAM,aAAaP,KAAK,QAAQI,MAAOiD,EAAWhD,SAAWzE,GAAM0H,EAAa1H,EAAE0E,OAAOF,OAAQQ,SAAmC,YAArB,OAAJkB,QAAI,IAAJA,GAAY,QAARL,EAAJK,EAAM0C,cAAM,IAAA/C,OAAR,EAAJA,EAAcgD,QAAqBjE,UAAQ,EAAC2G,SAAmC,YAArB,OAAJrF,QAAI,IAAJA,GAAY,QAARJ,EAAJI,EAAM0C,cAAM,IAAA9C,OAAR,EAAJA,EAAc+C,QAAsB,0BAA0B3C,EAAK0C,OAAO5E,OAAS,MAC3RpC,EAAAA,EAAAA,KAAC0C,EAAAA,EAAK,CAACC,GAAG,UAAUP,KAAK,UAAUW,MAAM,4BAA4BP,KAAK,QAAQI,MAAOmD,EAASlD,SAAWzE,GAAM4H,EAAW5H,EAAE0E,OAAOF,aAIzIX,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gBAAeD,SAAA,EAC3BrC,EAAAA,EAAAA,KAAA,MAAIsC,UAAU,6BAA4BD,SAAC,mBAC3CJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yCAAwCD,SAAA,EAEpDJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,sCAAqCD,SAAA,EAClDJ,EAAAA,EAAAA,MAAA,MAAIK,UAAU,6BAA4BD,SAAA,CAAC,yBAAuB4C,EAAmB3I,OAAO,QAE1F+I,MAAMC,KAAK,CAAEhJ,OAAQ,KAAM+M,KAAI,CAACO,EAAGC,KAAS,IAADC,EACzC,MAAMC,IAAsC,QAAlBD,EAAA3E,EAAc0E,UAAI,IAAAC,IAAlBA,EAAoBhM,OAAQqH,EAAc0E,GAAK/L,KAAKiB,QACxEiL,EAAajF,IAAiB8E,EAAM,EACpCI,EAAWhF,EAAmBuD,SAASqB,GAC7C,IAAIK,EAAyC,YACzCC,EAAgB,GAChBC,EAAO,KACPL,GACAG,EAAgBD,EAAW,UAAY,YACvCE,EAAgBF,EAAW,GAAK,yGAChCG,EAAOH,GAAWjK,EAAAA,EAAAA,KAAA,QAAMsC,UAAU,oDAAmDD,SAAC,YAAWrC,EAAAA,EAAAA,KAAA,QAAMsC,UAAU,kDAAiDD,SAAC,YAEpK8H,EAAgB,uEAEnB,MAAME,EAAqBL,EAAa,mEAAqE,GAC7G,OACI/H,EAAAA,EAAAA,MAACgB,EAAAA,EAAM,CACOC,QAASgH,EAAeT,KAAK,KACvCtG,QAASA,KAAQ6B,EAAgB6E,EAAM,GAAQE,GAAkB7E,GAAsBqD,GAAQA,EAAKC,SAASqB,GAAOtB,EAAKQ,QAAOuB,GAAKA,IAAMT,IAAO,IAAItB,EAAMsB,GAAKpB,MAAK,CAACC,EAAGC,IAAMD,EAAIC,KAAK,EACzLrG,UAAW,sDAAsD+H,KAAsBF,cACvFhI,MAAO4H,EAAkBE,EAAW,mBAAqB,mBAAsB,gBAAgB5H,SAAA,EAE/FJ,EAAAA,EAAAA,MAAA,QAAMK,UAAU,6BAA4BD,SAAA,CAAC,SAAOwH,EAAM,EAAGI,GAAYF,EAAiB,YAAcA,EAAiB,cAAgB,MACxIK,IANIP,EAOA,KAGhB7J,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,YAAYuG,KAAK,KAAKtG,QAASA,IAAMmD,GAAsB,GAAOhE,UAAU,OAAMD,SAAC,qBAGtGJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,gCAA+BD,SAAA,EAC5CrC,EAAAA,EAAAA,KAAC5E,EAAAA,EAAU,CAETG,IAAKK,EACLJ,aAA4C,QAA/B2I,EAAAgB,EAAcJ,EAAe,UAAE,IAAAZ,OAAA,EAA/BA,EAAiCvG,OAAQ,GACtDnC,aAA4C,QAA/B2I,EAAAe,EAAcJ,EAAe,UAAE,IAAAX,OAAA,EAA/BA,EAAiCtG,OAAQ,GACtDpC,OAhPG6O,CAAC3M,EAAcE,KACpC7B,QAAQC,IAAI,6CAA6C6I,KACzD,MAAMqD,EAAU,IAAIjD,GACdmD,EAAevD,EAAe,EAChCuD,GAAgB,GAAKA,EAAeF,EAAQ9L,QAC9C8L,EAAQE,GAAgB,CAAE1K,OAAME,QAChCsH,EAAiBgD,GAEbtK,GAAQA,EAAKiB,OACfmG,GAAsBqD,GAAQA,EAAKC,SAASF,GAAgBC,EAAO,IAAIA,EAAMD,GAAcG,MAAK,CAACC,EAAGC,IAAMD,EAAIC,MAE9GzD,GAAsBqD,GAAQA,EAAKQ,QAAOuB,GAAKA,IAAMhC,OAGvDrM,QAAQqB,MAAM,gDAAgDyH,IAChE,EAkOkBpJ,OAAO,QALF,SAASoJ,KAAgB/D,KAAKe,UAOrC/B,EAAAA,EAAAA,KAAA,KAAGsC,UAAU,gDAA+CD,SAAC,oHApJ7EJ,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CACJ/B,OAAQkG,EACRjG,QAASA,IAAMkG,GAAsB,GACrCnE,MAAM,oBAAmBE,SAAA,EAEzBJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yEAAwED,SAAA,CACpFoE,KAAqBF,EAAcjK,QAClC0D,EAAAA,EAAAA,KAAA,KAAAqC,SAAG,yBAEHkE,EAAc8C,KAAImB,IAChBvI,EAAAA,EAAAA,MAAC4G,EAAAA,EAAI,CAEHvG,UAAW,mCAAiD,OAAhBqE,SAAgB,IAAhBA,QAAgB,EAAhBA,GAAkBhE,MAAO6H,EAAS7H,GAAK,gCAAkC,mBACrHQ,QAASA,IArMQ/D,WAC3BnD,QAAQC,IAAI,uCAAuCuO,KACnD/D,IAAoB,GACpB,IACI,MAAM5C,QAAiB+D,EAAAA,GAA8B6C,gBAAgBD,GAEzB,IAADE,EAD3C1O,QAAQC,IAAI,iCAAkC4H,GAC1CA,EAASe,SAAWf,EAAS0G,UAC7BvO,QAAQC,IAAI,qCAAsC,CAC9CyG,GAAImB,EAAS0G,SAAS7H,IAAMmB,EAAS0G,SAAS/G,IAC9CrB,KAAM0B,EAAS0G,SAASpI,KACxBhG,UAAW0H,EAAS0G,SAAShD,YAC7BW,aAAcrE,EAAS0G,SAASnD,QAChCuD,eAAwC,QAAzBD,EAAA7G,EAAS0G,SAASnD,eAAO,IAAAsD,OAAA,EAAzBA,EAA2BrO,SAAU,IAExDsK,GAAoB9C,EAAS0G,YAE7BvO,QAAQqB,MAAM,mCAAoCwG,GAClDlD,EAAS,oCAEjB,CAAE,MAAO+C,GACL1H,QAAQqB,MAAM,oCAAqCqG,GACnD/C,EAAS,mCACb,CAAC,QACG8F,IAAoB,EACxB,GA6KyBmE,CAAqBL,EAAS7H,IAAIN,SAAA,EAEjDrC,EAAAA,EAAAA,KAAA,MAAIsC,UAAU,8BAA6BD,SAAEmI,EAASpI,QACtDpC,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,2FAA0FD,SACrGmI,EAASM,cAAe9K,EAAAA,EAAAA,KAAA,OAAK+K,IAAKP,EAASM,aAAcE,IAAKR,EAASpI,KAAME,UAAU,kCAAmCtC,EAAAA,EAAAA,KAAA,QAAAqC,SAAM,mBAN/HmI,EAAS7H,MAWlB8D,IAAoBE,KAAoB3G,EAAAA,EAAAA,KAAA,KAAAqC,SAAG,6CAE/CJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,8BAA6BD,SAAA,EAC1CrC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASA,KAAOmD,GAAsB,GAAQM,GAAoB,KAAK,EAAGvE,SAAC,YACvGrC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CACJE,QAAS+E,GACT9E,UAAWuD,IAAoBF,GAAiBpE,SAClD,iCAgIHrC,EAAAA,EAAAA,KAACiL,EAAAA,EAAqB,CACpB9K,OAAQ8F,EACR7F,QAASA,IAAM8F,GAAqB,GACpC7F,SAAU8F,EACV7F,YAnOyBmC,IAC7ByD,GAAqB,GACrBE,EAAsB,MACtBtB,EAAWrC,GAEPE,GACAY,EAAAA,EAAYsD,YAAYlE,GACnBoF,MAAKjE,GAAYY,EAAYZ,EAASC,KAAK1D,UAAYyD,EAASC,QAChEiE,OAAMrE,GAAO1H,QAAQqB,MAAM,6CAA8CqG,IAClF,OAuHoB,MAblB1B,EAAAA,EAAAA,MAAA,OAAAI,SAAA,EACErC,EAAAA,EAAAA,KAACuC,EAAAA,EAAK,CACJC,KAAK,QACLC,QAASnF,GAAS,wCAClBgF,UAAU,UAEZtC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACE,QAASA,IAAMqB,EAAS,cAAcnC,SAAC,wBA4GhD,C,uFCveA,MAAMH,EAA8B5G,IASpC,IATqC,OAC1C6E,EAAM,QACNC,EAAO,MACP+B,EAAK,SACLE,EAAQ,UACR6I,EAAS,YACTC,EAAc,UAAS,eACvBC,EAAiB,UAAS,WAC1BC,EAAa,UACd/P,EACC,OACE0E,EAAAA,EAAAA,KAACsL,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMrL,EAAQsL,GAAIC,EAAAA,SAASrJ,UAC5CJ,EAAAA,EAAAA,MAAC0J,EAAAA,GAAM,CAACF,GAAG,MAAMnJ,UAAU,gBAAgBlC,QAASA,EAAQiC,SAAA,EAC1DrC,EAAAA,EAAAA,KAACsL,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW7J,UAEnBrC,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,4CAGjBtC,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,gCAA+BD,UAC5CrC,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,8DAA6DD,UAC1ErC,EAAAA,EAAAA,KAACsL,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB7J,UAE5BJ,EAAAA,EAAAA,MAAC0J,EAAAA,GAAOQ,MAAK,CAAC7J,UAAU,uHAAsHD,SAAA,EAC5IrC,EAAAA,EAAAA,KAAC2L,EAAAA,GAAOS,MAAK,CACXX,GAAG,KACHnJ,UAAU,gDAA+CD,SAExDF,KAEHnC,EAAAA,EAAAA,KAAA,OAAKsC,UAAU,6BAA4BD,SACxCA,KAGHJ,EAAAA,EAAAA,MAAA,OAAKK,UAAU,kCAAiCD,SAAA,EAC9CrC,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAAS/C,EAAQiC,SAC1CgJ,IAEFH,IACClL,EAAAA,EAAAA,KAACiD,EAAAA,EAAM,CAACC,QAASkI,EAAgBjI,QAAS+H,EAAU7I,SACjD8I,oBASN,C", "sources": ["components/MjmlEditor.tsx", "components/ScheduleCampaignModal.tsx", "pages/campaigns/CampaignEdit.tsx", "components/Modal.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n// @ts-ignore - grapesjs-mjml lacks official types\r\nimport grapesjsMjml from 'grapesjs-mjml';\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface MjmlEditorRef {\r\n  save: () => Promise<{ mjml: string; html: string }>;\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface MjmlEditorProps {\r\n  initialMjml?: string;\r\n  initialHtml?: string; // Added to potentially load HTML if MJML is missing\r\n  onSave?: (mjml: string, html: string) => void;\r\n  height?: string | number;\r\n}\r\n\r\nconst MjmlEditor = forwardRef<MjmlEditorRef, MjmlEditorProps>(\r\n  ({ initialMjml = '', initialHtml = '', onSave, height = '70vh' }, ref) => {\r\n    const editorRef = useRef<HTMLDivElement>(null);\r\n    const grapesEditor = useRef<Editor | null>(null);\r\n\r\n    // Initialize GrapesJS Editor\r\n    useEffect(() => {\r\n      if (!editorRef.current) return; // Check for DOM element\r\n      \r\n      // Always destroy and recreate the editor to ensure consistent behavior\r\n      if (grapesEditor.current) {\r\n        console.log(\"[MjmlEditor] Cleaning up previous editor instance\");\r\n        grapesEditor.current.destroy();\r\n        grapesEditor.current = null;\r\n      }\r\n\r\n      console.log(\"[MjmlEditor] Initializing editor with props:\", {\r\n        hasMjml: !!initialMjml,\r\n        mjmlLength: initialMjml?.length || 0,\r\n        hasHtml: !!initialHtml,\r\n        htmlLength: initialHtml?.length || 0\r\n      });\r\n\r\n      try {\r\n        const editor = grapesjs.init({\r\n          container: editorRef.current,\r\n          fromElement: false, // Don't load from existing HTML/CSS in the container\r\n          height: String(height),\r\n          width: 'auto',\r\n          storageManager: false, // Disable default storage manager\r\n          plugins: [grapesjsMjml],\r\n          pluginsOpts: {\r\n            'grapesjs-mjml': {\r\n              // MJML plugin options (optional)\r\n              // columnsPadding: '0px',\r\n               useXmlParser: true, // Use the faster XML parser\r\n               resetBlocks: false, // Try keeping default GrapesJS blocks\r\n               // ... other options\r\n            }\r\n          },\r\n          // Optional: Configure panels, blocks, styles etc.\r\n        });\r\n\r\n        // Make sure the editor was initialized properly\r\n        if (!editor) {\r\n          console.error(\"[MjmlEditor] Failed to initialize editor\");\r\n          return;\r\n        }\r\n\r\n        grapesEditor.current = editor;\r\n\r\n        // Register missing commands that are expected by the editor\r\n        if (!editor.Commands.has('mjml-get-code')) {\r\n          console.log(\"[MjmlEditor] Registering missing mjml-get-code command\");\r\n          editor.Commands.add('mjml-get-code', {\r\n            run: (editor) => {\r\n              const mjml = editor.getHtml();\r\n              // Simple implementation for missing command\r\n              return { \r\n                mjml: mjml,\r\n                html: mjml // We'll process this later if needed\r\n              };\r\n            }\r\n          });\r\n        }\r\n\r\n        if (!editor.Commands.has('gjs-get-html')) {\r\n          console.log(\"[MjmlEditor] Registering missing gjs-get-html command\");\r\n          editor.Commands.add('gjs-get-html', {\r\n            run: (editor) => {\r\n              return editor.getHtml({ component: editor.getWrapper() });\r\n            }\r\n          });\r\n        }\r\n\r\n        // Use a small timeout to ensure editor is fully initialized\r\n        setTimeout(() => {\r\n          if (!grapesEditor.current) {\r\n            console.error(\"[MjmlEditor] Editor instance not available after timeout\");\r\n            return;\r\n          }\r\n          \r\n          // Verify the editor's components API is available\r\n          if (!grapesEditor.current.setComponents) {\r\n            console.error(\"[MjmlEditor] Editor's setComponents method is not available\");\r\n            return;\r\n          }\r\n          \r\n          try {\r\n            // Load initial content\r\n            if (initialMjml) {\r\n              console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\r\n              try {\r\n                grapesEditor.current.setComponents(initialMjml); // Use setComponents for MJML\r\n                console.log(\"[MjmlEditor] Successfully loaded MJML content\");\r\n              } catch (e) {\r\n                console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\r\n                // Fallback to HTML if MJML fails?\r\n                if (initialHtml) {\r\n                  console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\r\n                  grapesEditor.current.setComponents(initialHtml); // Use setComponents for HTML as well\r\n                  console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\r\n                }\r\n              }\r\n            } else if (initialHtml) {\r\n              console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\r\n              grapesEditor.current.setComponents(initialHtml);\r\n              console.log(\"[MjmlEditor] Successfully loaded HTML content\");\r\n            } else {\r\n              // Load default MJML template if nothing is provided\r\n              console.log(\"[MjmlEditor] No content provided, loading default template\");\r\n              grapesEditor.current.setComponents(`\r\n                <mjml>\r\n                  <mj-body>\r\n                    <mj-section>\r\n                      <mj-column>\r\n                        <mj-text>Start designing your email!</mj-text>\r\n                      </mj-column>\r\n                    </mj-section>\r\n                  </mj-body>\r\n                </mjml>\r\n              `);\r\n            }\r\n          } catch (error) {\r\n            console.error(\"[MjmlEditor] Error in content loading phase:\", error);\r\n          }\r\n        }, 100);\r\n\r\n        // Declare timeout variable in outer scope so it's accessible in cleanup function\r\n        let saveTimeout: NodeJS.Timeout | undefined;\r\n        let isSaving = false; // Flag to prevent multiple simultaneous save operations\r\n        \r\n        // Attach save listener with debounce\r\n        editor.on('change:changesCount', () => {\r\n          if (onSave && editor && !isSaving) { // Only proceed if not already saving\r\n            // Clear existing timeout\r\n            if (saveTimeout) clearTimeout(saveTimeout);\r\n            \r\n            // Set a new timeout\r\n            saveTimeout = setTimeout(() => { // Assign to the outer scope variable\r\n              try {\r\n                isSaving = true; // Set saving flag\r\n                // Simplify code retrieval: Prioritize commands, fallback to getHtml()\r\n                let finalMjml = '';\r\n                let finalHtml = '';\r\n\r\n                try {\r\n                  // Try the specific mjml command first\r\n                  const mjmlCode = editor.runCommand('mjml-get-code');\r\n                  if (mjmlCode && typeof mjmlCode === 'object') { // Command should return object {mjml, html}\r\n                    finalMjml = mjmlCode.mjml || '';\r\n                    finalHtml = mjmlCode.html || '';\r\n                    console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\r\n                  }\r\n                } catch (cmdErr) {\r\n                   console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\r\n                }\r\n\r\n                // If command failed or didn't return expected structure, use fallbacks\r\n                if (!finalMjml && !finalHtml) {\r\n                    finalMjml = editor.getHtml() || ''; // Often gets MJML\r\n                    try {\r\n                      // Try getting HTML via command\r\n                      finalHtml = editor.runCommand('gjs-get-html') || ''; \r\n                    } catch(htmlCmdErr) {\r\n                       console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\r\n                    }\r\n                    // As a last resort for HTML, maybe just use the component HTML (less reliable)\r\n                    if (!finalHtml) {\r\n                      finalHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                    }\r\n                    console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\r\n                }\r\n                \r\n                // Don't save if we have no content, prevents potential refresh cycles\r\n                if (!finalMjml.trim()) {\r\n                  console.log(\"[MjmlEditor] No MJML content to save, skipping save\");\r\n                  isSaving = false;\r\n                  return;\r\n                }\r\n                \r\n                console.log(\"[MjmlEditor] Attempting to call onSave...\");\r\n                // Call onSave as long as the editor instance exists and the prop was passed\r\n                // Even if mjml/html strings are empty, let the parent decide what to do\r\n                onSave(finalMjml, finalHtml);\r\n                console.log(\"[MjmlEditor] onSave callback executed.\");\r\n\r\n             } catch (error) {\r\n                 console.error(\"Error during editor change listener:\", error);\r\n             } finally {\r\n                 isSaving = false; // Reset flag whether save succeeded or failed\r\n             }\r\n            }, 500); // 500ms debounce\r\n          }\r\n        });\r\n\r\n        // Return cleanup function\r\n        return () => {\r\n          if (saveTimeout) clearTimeout(saveTimeout); // Now accessible here\r\n          if (grapesEditor.current) {\r\n             // Clean up panels, commands, etc. specific to this instance if necessary\r\n             try {\r\n               grapesEditor.current.destroy();\r\n             } catch (destroyError) {\r\n               console.error(\"[MjmlEditor] Error during editor cleanup:\", destroyError);\r\n             }\r\n             grapesEditor.current = null;\r\n          }\r\n        };\r\n      } catch (initError) {\r\n        console.error(\"[MjmlEditor] Critical error during editor initialization:\", initError);\r\n      }\r\n    }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\r\n\r\n    // Expose save method via ref\r\n    useImperativeHandle(ref, () => ({\r\n      save: async () => {\r\n        let generatedCode = { mjml: '', html: '' }; // Initialize with empty strings\r\n        if (grapesEditor.current) {\r\n           try {\r\n               // Check if command exists first to avoid warnings\r\n               const editor = grapesEditor.current;\r\n               if (editor.Commands.has('mjml-get-code')) {\r\n                 // Try the primary command\r\n                 const result = editor.runCommand('mjml-get-code');\r\n                 // Check if the command returned the expected object structure\r\n                 if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\r\n                   generatedCode.mjml = result.mjml || '';\r\n                   generatedCode.html = result.html || '';\r\n                   console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", { mjml: generatedCode.mjml.substring(0,50)+'...', html: generatedCode.html.substring(0,50)+'...' });\r\n                 } else {\r\n                    console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\r\n                    // Throw an error to trigger the catch block for fallback logic\r\n                    throw new Error(\"Command returned unexpected structure\"); \r\n                 }\r\n               } else {\r\n                 // Command doesn't exist, go straight to fallback\r\n                 throw new Error(\"mjml-get-code command not available\");\r\n               }\r\n           } catch (cmdErr) {\r\n               console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\r\n               try {\r\n                 // Fallback attempts\r\n                 const editor = grapesEditor.current;\r\n                 const rawMjml = editor.getHtml() || '';\r\n                 let generatedHtml = '';\r\n                 \r\n                 // Try gjs-get-html only if it exists\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   generatedHtml = editor.runCommand('gjs-get-html') || '';\r\n                 } else {\r\n                   // Direct fallback to component HTML\r\n                   generatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                  \r\n                 if (rawMjml || generatedHtml) { // Use if *either* fallback worked\r\n                     generatedCode.mjml = rawMjml;\r\n                     generatedCode.html = generatedHtml || rawMjml; // Use MJML as HTML if no HTML generated\r\n                     console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\r\n                 } else {\r\n                     console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\r\n                 }\r\n               } catch (fallbackErr) {\r\n                  console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\r\n               }\r\n           }\r\n        } else {\r\n          console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\r\n        }\r\n\r\n        // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\r\n        await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\r\n        \r\n        // Re-fetch the HTML specifically after the delay, as it might have updated\r\n        if (grapesEditor.current && !generatedCode.html.trim()) { // Only re-fetch if HTML was initially empty\r\n            console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\r\n            try {\r\n                 const editor = grapesEditor.current;\r\n                 \r\n                 let potentiallyUpdatedHtml = '';\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   potentiallyUpdatedHtml = editor.runCommand('gjs-get-html');\r\n                 }\r\n                 \r\n                 if (!potentiallyUpdatedHtml) {\r\n                   potentiallyUpdatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                 \r\n                 if (potentiallyUpdatedHtml.trim()) {\r\n                     console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\r\n                     generatedCode.html = potentiallyUpdatedHtml;\r\n                 } else {\r\n                    console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\r\n                    // If still no HTML but we have MJML, use that\r\n                    if (generatedCode.mjml && !generatedCode.html) {\r\n                      generatedCode.html = generatedCode.mjml;\r\n                    }\r\n                 }\r\n            } catch (refetchErr) {\r\n                console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\r\n            }\r\n        }\r\n\r\n        // ALWAYS return the potentially updated generatedCode object\r\n        return generatedCode; \r\n      },\r\n       getEditor: () => grapesEditor.current,\r\n    }));\r\n\r\n    return <div ref={editorRef} style={{ height: height }} />;\r\n  }\r\n);\r\n\r\n// Assign display name for debugging\r\nMjmlEditor.displayName = 'MjmlEditor';\r\n\r\nexport default MjmlEditor;", "import React, {\r\n  useEffect,\r\n  useState,\r\n} from 'react';\r\n\r\nimport { campaignAPI } from 'services/api';\r\n\r\nimport Alert from './Alert';\r\nimport Button from './Button';\r\nimport Input from './Input';\r\nimport { Modal } from './Modal';\r\n\r\ninterface ScheduleCampaignModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  campaign: { \r\n    _id: string; \r\n    name: string; \r\n    scheduledFor?: string; // Add optional field\r\n  } | null; // Basic campaign info needed\r\n  onScheduled: (message: string) => void; // Callback on successful schedule\r\n}\r\n\r\nconst ScheduleCampaignModal: React.FC<ScheduleCampaignModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  campaign,\r\n  onScheduled,\r\n}) => {\r\n  const [scheduledDateTime, setScheduledDateTime] = useState<string>('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n\r\n  // Set default schedule time when modal opens or campaign changes\r\n  useEffect(() => {\r\n    if (isOpen && campaign) {\r\n      let initialDateTime = '';\r\n      if (campaign.scheduledFor) {\r\n        try {\r\n          // Attempt to parse the existing schedule time\r\n          const existingDate = new Date(campaign.scheduledFor);\r\n          // Check if the date is valid\r\n          if (!isNaN(existingDate.getTime())) {\r\n             // Format for datetime-local input (YYYY-MM-DDTHH:mm)\r\n             // Important: Adjust for local timezone offset if needed, \r\n             // otherwise this might display UTC time in the input.\r\n             // A simpler approach is to format based on local components.\r\n            const year = existingDate.getFullYear();\r\n            const month = (existingDate.getMonth() + 1).toString().padStart(2, '0'); // months are 0-indexed\r\n            const day = existingDate.getDate().toString().padStart(2, '0');\r\n            const hours = existingDate.getHours().toString().padStart(2, '0');\r\n            const minutes = existingDate.getMinutes().toString().padStart(2, '0');\r\n            initialDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;\r\n            console.log(`[ScheduleModal] Using existing schedule: ${campaign.scheduledFor} -> Formatted: ${initialDateTime}`);\r\n          } else {\r\n            console.warn('[ScheduleModal] Invalid existing schedule date:', campaign.scheduledFor);\r\n          }\r\n        } catch (e) {\r\n          console.error('[ScheduleModal] Error parsing existing schedule date:', e);\r\n        }\r\n      }\r\n      \r\n      // If no valid existing schedule, default to 1 hour from now in local time\r\n      if (!initialDateTime) {\r\n        const defaultDate = new Date(Date.now() + 60 * 60 * 1000);\r\n        // Format the default local time correctly for the input\r\n        const year = defaultDate.getFullYear();\r\n        const month = (defaultDate.getMonth() + 1).toString().padStart(2, '0'); \r\n        const day = defaultDate.getDate().toString().padStart(2, '0');\r\n        const hours = defaultDate.getHours().toString().padStart(2, '0');\r\n        const minutes = defaultDate.getMinutes().toString().padStart(2, '0');\r\n        initialDateTime = `${year}-${month}-${day}T${hours}:${minutes}`; \r\n        console.log(`[ScheduleModal] Setting default local schedule time: ${initialDateTime}`);\r\n      }\r\n      \r\n      setScheduledDateTime(initialDateTime);\r\n      setError(''); // Clear previous errors\r\n    }\r\n  }, [isOpen, campaign]);\r\n\r\n  const handleSchedule = async () => {\r\n    if (!campaign || !scheduledDateTime) {\r\n      setError('Please select a valid date and time.');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    try {\r\n      const scheduleTimestamp = new Date(scheduledDateTime).toISOString();\r\n      await campaignAPI.scheduleCampaign(campaign._id, scheduleTimestamp);\r\n      onScheduled(`Campaign \"${campaign.name}\" scheduled successfully for ${new Date(scheduledDateTime).toLocaleString()}.`);\r\n    } catch (err: any) {\r\n      console.error('Error scheduling campaign:', err);\r\n      setError(err.response?.data?.message || 'Failed to schedule campaign.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle modal close - reset state\r\n  const handleClose = () => {\r\n    setLoading(false);\r\n    setError('');\r\n    setScheduledDateTime('');\r\n    onClose(); // Call the parent's close handler\r\n  };\r\n\r\n  return (\r\n    <Modal isOpen={isOpen} onClose={handleClose} title={`Schedule Campaign: ${campaign?.name || ''}`}>\r\n      <div className=\"space-y-4\">\r\n        {error && <Alert type=\"error\" message={error} onClose={() => setError('')} />}\r\n        \r\n        <p className='text-text-secondary'>Select the date and time to start sending this campaign.</p>\r\n\r\n        <Input\r\n          id=\"scheduledDateTimeModal\"\r\n          name=\"scheduledDateTimeModal\"\r\n          type=\"datetime-local\"\r\n          value={scheduledDateTime}\r\n          onChange={(e) => setScheduledDateTime(e.target.value)}\r\n          label=\"Scheduled Date & Time\"\r\n          required\r\n          className=\"w-full\" // Use full width inside modal\r\n        />\r\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 -mt-2\">Your local timezone will be used.</p>\r\n        \r\n      </div>\r\n      <div className=\"mt-6 flex justify-end gap-3\">\r\n        <Button variant=\"secondary\" onClick={handleClose} disabled={loading}>\r\n          Cancel\r\n        </Button>\r\n        <Button onClick={handleSchedule} disabled={loading || !scheduledDateTime}>\r\n          {loading ? 'Scheduling...' : 'Schedule Campaign'}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default ScheduleCampaignModal; ", "// Import styles\nimport 'styles/editor.css';\n// Import GrapesJS CSS (needed by MjmlEditor)\nimport 'grapesjs/dist/css/grapes.min.css';\n\nimport React, {\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport Input from 'components/Input';\n// import MosaicoEditor from 'components/MosaicoEditor'; // Removed Mosaico\nimport MjmlEditor, {\n  MjmlEditorRef,\n} from 'components/MjmlEditor'; // Added MJML Editor\nimport { Modal } from 'components/Modal'; // Added missing Modal import\nimport ScheduleCampaignModal\n  from 'components/ScheduleCampaignModal'; // Import schedule modal\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\nimport {\n  templateRecommendationService,\n} from 'services'; // Add template service import\nimport { campaignAPI } from 'services/api';\n\nconst CampaignEdit: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // --- State ---\n  const [campaign, setCampaign] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Email Content State\n  const [currentEmail, setCurrentEmail] = useState<number>(1);\n  const [activeEmailIndices, setActiveEmailIndices] = useState<number[]>([]); \n  const [emailContents, setEmailContents] = useState<{ mjml: string; html: string }[]>(() =>\n    Array.from({ length: 10 }, () => ({ mjml: '', html: '' }))\n  );\n  const editorRef = useRef<MjmlEditorRef>(null);\n\n  // Campaign Details State\n  const [campaignName, setCampaignName] = useState('');\n  const [subject, setSubject] = useState('');\n  const [fromName, setFromName] = useState('');\n  const [fromEmail, setFromEmail] = useState('');\n  const [replyTo, setReplyTo] = useState('');\n\n  // Schedule Modal State\n  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);\n  const [campaignToSchedule, setCampaignToSchedule] = useState<any>(null); \n\n  // Template Picker State\n  const [showTemplatePicker, setShowTemplatePicker] = useState(false);\n  const [templatesList, setTemplatesList] = useState<any[]>([]);\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);\n\n  // --- Effects ---\n\n  // Fetch campaign and initialize state\n  useEffect(() => {\n    const fetchCampaign = async () => {\n        if (!id || id === 'undefined') {\n            console.log('Skipping fetchCampaign due to invalid ID');\n            navigate('/campaigns'); // Redirect if ID is invalid\n            return;\n        }\n        console.log('Fetching campaign with ID:', id);\n        setLoading(true); \n        try {\n            const response = await campaignAPI.getCampaign(id);\n            const foundCampaign = response.data.campaign || response.data;\n            if (foundCampaign) {\n                setCampaign(foundCampaign);\n                setCampaignName(foundCampaign.name || '');\n                setSubject(foundCampaign.subject || '');\n                setFromName(foundCampaign.fromName || '');\n                setFromEmail(user?.domain?.status === 'active' ? `noreply@${user.domain.name}` : (foundCampaign.fromEmail || ''));\n                setReplyTo(foundCampaign.replyTo || foundCampaign.fromEmail || '');\n\n                // Initialize email contents\n                const initialContents = Array.from({ length: 10 }, () => ({ mjml: '', html: '' }));\n                if (foundCampaign.emailContents && Array.isArray(foundCampaign.emailContents)) {\n                    foundCampaign.emailContents.forEach((content: any, index: number) => {\n                        if (index < initialContents.length) {\n                            initialContents[index] = {\n                                mjml: content.mjml || '',\n                                html: content.html || ''\n                            };\n                        }\n                    });\n                } else if (foundCampaign.htmlContent || foundCampaign.mjmlContent) {\n                    initialContents[0] = {\n                        mjml: foundCampaign.mjmlContent || '',\n                        html: foundCampaign.htmlContent || ''\n                    };\n                }\n                setEmailContents(initialContents);\n                setLoading(false);\n            } else {\n                setError('Campaign not found');\n                setLoading(false);\n            }\n        } catch (err: any) {\n            console.error('Error fetching campaign:', err);\n            setError(err.response?.data?.message || 'Failed to fetch campaign');\n            setLoading(false);\n        }\n    };\n    fetchCampaign();\n  }, [id, user, navigate]);\n\n  // Initialize activeEmailIndices based on fetched content\n  useEffect(() => {\n    const initialActive: number[] = [];\n    emailContents.forEach((email, index) => {\n      if (email.html && email.html.trim()) {\n        initialActive.push(index);\n      }\n    });\n    setActiveEmailIndices(initialActive);\n  }, [emailContents]); \n\n  // Fetch templates when modal opens\n  useEffect(() => {\n    if (showTemplatePicker) {\n        setLoadingTemplates(true);\n        templateRecommendationService.getAllTemplates()\n            .then(response => {\n                if (response.success) {\n                    setTemplatesList(response.data);\n                } else {\n                    setError('Failed to load templates');\n                }\n            })\n            .catch(err => {\n                console.error('Error loading templates:', err);\n                setError('Failed to load templates. Please try again.');\n            })\n            .finally(() => {\n                setLoadingTemplates(false);\n            });\n    }\n  }, [showTemplatePicker]);\n\n  // --- Handlers ---\n  \n  // Template Picker\n  const handleTemplateSelect = async (templateId: string) => {\n    console.log(`[Debug] Selecting template with ID: ${templateId}`);\n    setLoadingTemplates(true);\n    try {\n        const response = await templateRecommendationService.getTemplateById(templateId);\n        console.log('[Debug] Template API response:', response);\n        if (response.success && response.template) {\n            console.log('[Debug] Setting selected template:', {\n                id: response.template.id || response.template._id,\n                name: response.template.name,\n                hasMjml: !!response.template.mjmlContent,\n                hasContent: !!response.template.content,\n                contentLength: response.template.content?.length || 0\n            });\n            setSelectedTemplate(response.template);\n        } else {\n            console.error('[Debug] Failed to load template:', response);\n            setError('Failed to load selected template');\n        }\n    } catch (err) {\n        console.error('[Debug] Error selecting template:', err);\n        setError('Failed to load selected template');\n    } finally {\n        setLoadingTemplates(false);\n    }\n  };\n  \n  const handleUseTemplate = () => {\n    console.log('[Debug] Using selected template:', selectedTemplate ? {\n        id: selectedTemplate.id || selectedTemplate._id,\n        name: selectedTemplate.name,\n        hasMjml: !!selectedTemplate.mjmlContent,\n        hasContent: !!selectedTemplate.content\n    } : 'No template selected');\n    \n    if (selectedTemplate) {\n        const updated = [...emailContents];\n        updated[currentEmail - 1] = {\n            mjml: selectedTemplate.mjmlContent || '',\n            html: selectedTemplate.content || '',\n        };\n        \n        console.log('[Debug] Updated email contents:', {\n            emailIndex: currentEmail - 1,\n            hasMjml: !!updated[currentEmail - 1].mjml,\n            hasHtml: !!updated[currentEmail - 1].html,\n            mjmlLength: updated[currentEmail - 1].mjml.length,\n            htmlLength: updated[currentEmail - 1].html.length\n        });\n        \n        // Save the updated contents to state\n        setEmailContents(updated);\n        \n        // Also add this email to active indices if it contains content\n        if (selectedTemplate.content && selectedTemplate.content.trim()) {\n            const currentIndex = currentEmail - 1;\n            setActiveEmailIndices(prev => \n                prev.includes(currentIndex) \n                    ? prev \n                    : [...prev, currentIndex].sort((a, b) => a - b)\n            );\n        }\n        \n        // Close the modal first\n        setSelectedTemplate(null);\n        setShowTemplatePicker(false);\n        \n        // Force a complete re-render of the editor component by changing its key\n        // Wait a moment to ensure state updates have completed\n        setTimeout(() => {\n            // This will force the MjmlEditor to completely remount with the new content\n            const tempKey = Date.now(); // Use timestamp as a unique key\n            \n            // Temporarily set current email to a value that won't match any existing email\n            // to force a complete remount\n            setCurrentEmail(-1);\n            \n            // After a brief delay, restore the current email index\n            setTimeout(() => {\n                setCurrentEmail(currentEmail);\n            }, 50);\n        }, 100);\n    }\n  };\n\n  // Email Selection/Saving\n  const handleEmailSelect = (index: number) => {\n      setCurrentEmail(index);\n  };\n  const handleMjmlSave = (mjml: string, html: string) => {\n    console.log(`[handleMjmlSave] Saving content for email ${currentEmail}`);\n    const updated = [...emailContents];\n    const currentIndex = currentEmail - 1;\n    if (currentIndex >= 0 && currentIndex < updated.length) {\n      updated[currentIndex] = { mjml, html };\n      setEmailContents(updated);\n      // Also manage active state on save\n      if (html && html.trim()) {\n        setActiveEmailIndices(prev => prev.includes(currentIndex) ? prev : [...prev, currentIndex].sort((a, b) => a - b));\n      } else {\n        setActiveEmailIndices(prev => prev.filter(i => i !== currentIndex));\n      }\n    } else {\n      console.error(`[handleMjmlSave] Invalid currentEmail index: ${currentEmail}`);\n    }\n  };\n\n  // Schedule Modal\n  const openScheduleModal = () => {\n    if (campaign) {\n      setCampaignToSchedule({\n        _id: campaign._id,\n        name: campaignName,\n        scheduledFor: campaign.scheduledFor \n      });\n      setScheduleModalOpen(true);\n    }\n  };\n  const handleScheduleSuccess = (message: string) => {\n    setScheduleModalOpen(false);\n    setCampaignToSchedule(null);\n    setSuccess(message);\n    // Refresh campaign data \n    if (id) {\n        campaignAPI.getCampaign(id)\n            .then(response => setCampaign(response.data.campaign || response.data))\n            .catch(err => console.error(\"Failed to refresh campaign after schedule:\", err));\n    }\n  };\n\n  // Main Save/Update Handler\n  const handleUpdateCampaign = async () => {\n    if (!id) { setError('Campaign ID missing'); return; }\n    if (!campaignName || !subject || !fromName || !fromEmail) {\n      setError('Please fill in all required campaign details.');\n      return;\n    }\n\n    // Filter active emails with content JUST before sending\n    const activeEmailsToSend = emailContents.filter((email, index) => \n        activeEmailIndices.includes(index) && email.html && email.html.trim()\n    );\n\n    // Note: Validation for *at least one* active email might be needed here too,\n    // depending on whether saving content without any active emails is allowed.\n    // For now, we proceed even if activeEmailsToSend is empty, just saving details.\n\n    try {\n      setSaving(true); setError(''); setSuccess('');\n      const emailToUse = user?.domain?.status === 'active' ? `noreply@${user.domain.name}` : fromEmail;\n      \n      const campaignData: any = {\n        name: campaignName,\n        subject,\n        fromName,\n        fromEmail: emailToUse,\n        replyTo: replyTo || emailToUse,\n        // Send the *full* potential list, but backend might only use active ones based on other flags\n        // Or, only send the *active* ones. Let's send only active ones for clarity.\n        emailContents: activeEmailsToSend.map(e => ({ mjml: e.mjml, html: e.html })),\n        // Include htmlContent for potential compatibility if needed by backend update logic\n        htmlContent: activeEmailsToSend[0]?.html || '', \n        // Note: Scheduling info is NOT sent here, it's handled by the modal/schedule API call.\n      };\n\n      console.log(\"Sending update data (Details & Content Only):\", campaignData);\n      const response = await campaignAPI.updateCampaign(id, campaignData);\n\n      setSuccess('Campaign details & content updated successfully!');\n      if (response.data?.campaign || response.data) {\n        setCampaign(response.data.campaign || response.data);\n      }\n      setTimeout(() => setSuccess(''), 3000);\n\n    } catch (err: any) {\n      console.error('Error updating campaign:', err);\n      setError(err.response?.data?.message || 'Failed to update campaign');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Template Picker Modal Renderer\n  const renderTemplatePickerModal = () => (\n    <Modal\n      isOpen={showTemplatePicker}\n      onClose={() => setShowTemplatePicker(false)}\n      title=\"Choose a Template\"\n    >\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\">\n        {loadingTemplates && !templatesList.length ? (\n          <p>Loading templates...</p>\n        ) : (\n          templatesList.map(template => (\n            <Card\n              key={template.id}\n              className={`cursor-pointer transition-all ${selectedTemplate?.id === template.id ? 'ring-2 ring-primary shadow-lg' : 'hover:shadow-md'}`}\n              onClick={() => handleTemplateSelect(template.id)}\n            >\n              <h4 className=\"font-semibold mb-2 truncate\">{template.name}</h4>\n              <div className=\"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\">\n                 {template.thumbnailUrl ? <img src={template.thumbnailUrl} alt={template.name} className=\"object-contain h-full w-full\"/> : <span>No Preview</span>}\n              </div>\n            </Card>\n          ))\n        )}\n         {loadingTemplates && selectedTemplate && <p>Loading selected template details...</p>}\n      </div>\n      <div className=\"mt-4 flex justify-end gap-2\">\n        <Button variant=\"secondary\" onClick={() => {setShowTemplatePicker(false); setSelectedTemplate(null);}}>Cancel</Button>\n        <Button\n           onClick={handleUseTemplate}\n           disabled={!selectedTemplate || loadingTemplates}\n        >\n          Use Selected Template\n        </Button>\n      </div>\n    </Modal>\n  );\n\n  // --- Main Render --- \n\n  // Loading/Error checks\n  if (loading && !campaign) {\n    return (\n      <div className=\"flex justify-center items-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  if (!campaign && !loading) {\n    return (\n      <div>\n        <Alert\n          type=\"error\"\n          message={error || \"Campaign not found or failed to load.\"}\n          className=\"mb-6\"\n        />\n        <Button onClick={() => navigate('/campaigns')}>\n          Back to Campaigns\n        </Button>\n      </div>\n    );\n  }\n\n  if (!campaign) return null;\n\n  return (\n    <>\n      <h1 className=\"text-2xl font-bold mb-6 text-gray-800 dark:text-white\">Edit Campaign: {campaignName || campaign?.name}</h1>\n      <Card>\n          {error && <Alert type=\"error\" message={error} onClose={() => setError('')} className=\"mb-4\" />}\n          {success && <Alert type=\"success\" message={success} onClose={() => setSuccess('')} className=\"mb-4\" />}\n\n          {/* Action Buttons Area - Restored */} \n          <div className=\"mb-6 pb-4 border-b dark:border-gray-700 flex flex-wrap items-center justify-between gap-2\">\n            <Button onClick={handleUpdateCampaign} disabled={saving || loading} size=\"sm\" className=\"btn-primary\">\n              {saving ? 'Saving...' : 'Save Details & Content'}\n            </Button>\n            <div className=\"flex space-x-2\">\n                <Button variant=\"secondary\" size=\"sm\" onClick={() => navigate(`/campaigns/recipients/${id}`)} disabled={saving || loading}>\n                  Manage Recipients\n                </Button>\n                <Button variant=\"secondary\" size=\"sm\" onClick={openScheduleModal} disabled={saving || loading}>\n                  {campaign?.status === 'scheduled' ? 'Reschedule' : 'Schedule Campaign'}\n                </Button>\n                 <Button variant=\"secondary\" size=\"sm\" onClick={() => navigate('/campaigns')}> \n                  Back to Dashboard\n                </Button>\n            </div>\n          </div>\n\n          {/* Combined Edit Area - Restored */} \n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            {/* Column 1: Campaign Details */} \n            <div className=\"lg:col-span-1 space-y-4\">\n              <h3 className=\"text-lg font-semibold mb-2\">Campaign Details</h3>\n              <Input id=\"campaignName\" name=\"campaignName\" label=\"Campaign Name\" value={campaignName} onChange={(e) => setCampaignName(e.target.value)} required />\n              <Input id=\"subject\" name=\"subject\" label=\"Email Subject\" value={subject} onChange={(e) => setSubject(e.target.value)} required />\n              <Input id=\"fromName\" name=\"fromName\" label=\"From Name\" value={fromName} onChange={(e) => setFromName(e.target.value)} required />\n              <Input id=\"fromEmail\" name=\"fromEmail\" label=\"From Email\" type=\"email\" value={fromEmail} onChange={(e) => setFromEmail(e.target.value)} disabled={user?.domain?.status === 'active'} required helpText={user?.domain?.status === 'active' ? `Using verified domain: ${user.domain.name}` : ''} />\n              <Input id=\"replyTo\" name=\"replyTo\" label=\"Reply-To Email (optional)\" type=\"email\" value={replyTo} onChange={(e) => setReplyTo(e.target.value)} />\n            </div>\n\n            {/* Column 2 & 3: Email Content Editor */} \n            <div className=\"lg:col-span-2\">\n               <h3 className=\"text-lg font-semibold mb-2\">Email Content</h3>\n               <div className=\"flex flex-col lg:flex-row gap-4 h-full\">\n                  {/* Email sequence sidebar */} \n                  <div className=\"w-full lg:w-1/5 flex flex-col gap-2\">\n                    <h3 className=\"font-semibold mb-1 text-sm\">Select Email (Active: {activeEmailIndices.length}):</h3>\n                     {/* ... (Mapping logic for email buttons - kept from previous state) ... */} \n                     {Array.from({ length: 10 }).map((_, idx) => {\n                        const hasHtmlContent = !!(emailContents[idx]?.html && emailContents[idx].html.trim());\n                        const isSelected = currentEmail === idx + 1;\n                        const isActive = activeEmailIndices.includes(idx);\n                        let buttonVariant: \"primary\" | \"secondary\" = \"secondary\";\n                        let customClasses = \"\";\n                        let icon = null;\n                        if (hasHtmlContent) {\n                            buttonVariant = isActive ? \"primary\" : \"secondary\";\n                            customClasses = isActive ? \"\" : \"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\";\n                            icon = isActive ? <span className=\"ml-1 text-green-500 dark:text-green-400 font-bold\">✓</span> : <span className=\"ml-1 text-gray-500 dark:text-gray-400 font-bold\">⏸</span>;\n                        } else {\n                           customClasses = \"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\";\n                        }\n                        const selectionRingClass = isSelected ? \"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\" : \"\";\n                        return (\n                            <Button\n                                key={idx} variant={buttonVariant} size=\"sm\"\n                                onClick={() => { setCurrentEmail(idx + 1); if (hasHtmlContent) { setActiveEmailIndices(prev => prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx].sort((a, b) => a - b)); }}}\n                                className={`w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses} px-2 py-1`}\n                                title={hasHtmlContent ? (isActive ? \"Click to exclude\" : \"Click to include\") : \"Click to edit\"}\n                            >\n                                <span className=\"flex-grow truncate text-xs\">Email {idx + 1}{isActive && hasHtmlContent ? ' (Active)' : hasHtmlContent ? ' (Inactive)' : ''}</span>\n                                {icon}\n                            </Button>\n                        );\n                     })}\n                     <Button variant=\"secondary\" size=\"sm\" onClick={() => setShowTemplatePicker(true)} className=\"mt-4\">Use Template</Button>\n                  </div>\n                  {/* Editor */} \n                  <div className=\"w-full lg:w-4/5 flex flex-col\">\n                    <MjmlEditor\n                      key={`email-${currentEmail}-${Date.now()}`} \n                      ref={editorRef}\n                      initialMjml={emailContents[currentEmail - 1]?.mjml || ''}\n                      initialHtml={emailContents[currentEmail - 1]?.html || ''}\n                      onSave={handleMjmlSave}\n                      height=\"60vh\" \n                    />\n                    <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">Click email on left to edit. Active emails (✓) will be used if campaign is sent/scheduled.</p>\n                  </div>\n               </div>\n            </div>\n          </div>\n      </Card>\n\n      {/* Modals */} \n      {renderTemplatePickerModal()}\n      <ScheduleCampaignModal\n        isOpen={scheduleModalOpen}\n        onClose={() => setScheduleModalOpen(false)}\n        campaign={campaignToSchedule}\n        onScheduled={handleScheduleSuccess}\n      />\n    </>\n  );\n};\n\nexport default CampaignEdit;\n", "import React, { Fragment } from 'react';\r\n\r\nimport {\r\n  Dialog,\r\n  Transition,\r\n} from '@headlessui/react';\r\n\r\nimport Button from './Button'; // Assuming Button component exists\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: React.ReactNode;\r\n  onConfirm?: () => void;\r\n  confirmText?: string;\r\n  confirmVariant?: 'primary' | 'secondary' | 'danger';\r\n  cancelText?: string;\r\n}\r\n\r\nexport const Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  onConfirm,\r\n  confirmText = 'Confirm',\r\n  confirmVariant = 'primary',\r\n  cancelText = 'Cancel',\r\n}) => {\r\n  return (\r\n    <Transition appear show={isOpen} as={Fragment}>\r\n      <Dialog as=\"div\" className=\"relative z-10\" onClose={onClose}>\r\n        <Transition.Child\r\n          as={Fragment}\r\n          enter=\"ease-out duration-300\"\r\n          enterFrom=\"opacity-0\"\r\n          enterTo=\"opacity-100\"\r\n          leave=\"ease-in duration-200\"\r\n          leaveFrom=\"opacity-100\"\r\n          leaveTo=\"opacity-0\"\r\n        >\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" />\r\n        </Transition.Child>\r\n\r\n        <div className=\"fixed inset-0 overflow-y-auto\">\r\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n            <Transition.Child\r\n              as={Fragment}\r\n              enter=\"ease-out duration-300\"\r\n              enterFrom=\"opacity-0 scale-95\"\r\n              enterTo=\"opacity-100 scale-100\"\r\n              leave=\"ease-in duration-200\"\r\n              leaveFrom=\"opacity-100 scale-100\"\r\n              leaveTo=\"opacity-0 scale-95\"\r\n            >\r\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all\">\r\n                <Dialog.Title\r\n                  as=\"h3\"\r\n                  className=\"text-lg font-medium leading-6 text-white mb-4\"\r\n                >\r\n                  {title}\r\n                </Dialog.Title>\r\n                <div className=\"mt-2 text-sm text-gray-300\">\r\n                  {children}\r\n                </div>\r\n\r\n                <div className=\"mt-6 flex justify-end space-x-3\">\r\n                  <Button variant=\"secondary\" onClick={onClose}>\r\n                    {cancelText}\r\n                  </Button>\r\n                  {onConfirm && (\r\n                    <Button variant={confirmVariant} onClick={onConfirm}>\r\n                      {confirmText}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </Dialog.Panel>\r\n            </Transition.Child>\r\n          </div>\r\n        </div>\r\n      </Dialog>\r\n    </Transition>\r\n  );\r\n}; "], "names": ["MjmlEditor", "forwardRef", "_ref", "ref", "initialMjml", "initialHtml", "onSave", "height", "editor<PERSON><PERSON>", "useRef", "grapesEditor", "useEffect", "current", "console", "log", "destroy", "hasMjml", "mjm<PERSON><PERSON><PERSON><PERSON>", "length", "hasHtml", "htmlLength", "editor", "<PERSON><PERSON><PERSON>", "init", "container", "fromElement", "String", "width", "storageManager", "plugins", "grapesjsMjml", "pluginsOpts", "useXmlParser", "resetBlocks", "error", "saveTimeout", "Commands", "has", "add", "run", "mjml", "getHtml", "html", "component", "getWrapper", "setTimeout", "setComponents", "substring", "e", "isSaving", "on", "clearTimeout", "finalMjml", "finalHtml", "mjmlCode", "runCommand", "cmdErr", "warn", "htmlCmdErr", "trim", "destroyError", "initError", "useImperativeHandle", "save", "async", "generatedCode", "Error", "result", "rawMjml", "generatedHtml", "fallbackErr", "Promise", "resolve", "potentiallyUpdatedHtml", "refetchErr", "getEditor", "_jsx", "style", "displayName", "isOpen", "onClose", "campaign", "onScheduled", "scheduledDateTime", "setScheduledDateTime", "useState", "loading", "setLoading", "setError", "initialDateTime", "scheduledFor", "existingDate", "Date", "isNaN", "getTime", "year", "getFullYear", "month", "getMonth", "toString", "padStart", "day", "getDate", "hours", "getHours", "getMinutes", "defaultDate", "now", "handleClose", "_jsxs", "Modal", "title", "name", "children", "className", "<PERSON><PERSON>", "type", "message", "Input", "id", "value", "onChange", "target", "label", "required", "<PERSON><PERSON>", "variant", "onClick", "disabled", "scheduleTimestamp", "toISOString", "campaignAPI", "scheduleCampaign", "_id", "toLocaleString", "err", "_err$response", "_err$response$data", "response", "data", "CampaignEdit", "_user$domain3", "_user$domain4", "_emailContents", "_emailContents2", "useParams", "user", "useAuth", "navigate", "useNavigate", "setCampaign", "saving", "setSaving", "success", "setSuccess", "currentEmail", "setCurrentEmail", "activeEmailIndices", "setActiveEmailIndices", "emailContents", "setEmailContents", "Array", "from", "campaignName", "setCampaignName", "subject", "setSubject", "fromName", "setFromName", "fromEmail", "setFromEmail", "replyTo", "setReplyTo", "scheduleModalOpen", "setScheduleModalOpen", "campaignToSchedule", "setCampaignToSchedule", "showTemplatePicker", "setShowTemplatePicker", "templatesList", "setTemplatesList", "loadingTemplates", "setLoadingTemplates", "selectedTemplate", "setSelectedTemplate", "getCampaign", "foundCampaign", "_user$domain", "domain", "status", "initialContents", "isArray", "for<PERSON>ach", "content", "index", "htmlContent", "mjml<PERSON><PERSON><PERSON>", "fetchCampaign", "initialActive", "email", "push", "templateRecommendationService", "getAllTemplates", "then", "catch", "finally", "handleUseTemplate", "<PERSON><PERSON><PERSON><PERSON>", "updated", "emailIndex", "currentIndex", "prev", "includes", "sort", "a", "b", "_Fragment", "Card", "activeEmailsToSend", "filter", "_user$domain2", "_activeEmailsToSend$", "_response$data", "emailToUse", "campaignData", "map", "updateCampaign", "_err$response2", "_err$response2$data", "size", "openScheduleModal", "helpText", "_", "idx", "_emailContents$idx", "hasHtmlContent", "isSelected", "isActive", "buttonVariant", "customClasses", "icon", "selectionRingClass", "i", "handleMjmlSave", "template", "templateId", "getTemplateById", "_response$template$co", "contentLength", "handleTemplateSelect", "thumbnailUrl", "src", "alt", "ScheduleCampaignModal", "onConfirm", "confirmText", "confirmVariant", "cancelText", "Transition", "appear", "show", "as", "Fragment", "Dialog", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Panel", "Title"], "sourceRoot": ""}