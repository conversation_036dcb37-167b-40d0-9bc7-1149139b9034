{"version": 3, "file": "static/js/410.a874cfbb.chunk.js", "mappings": "qLAeA,MAkDA,EAlDgDA,IAAkB,IAAjB,QAAEC,GAASD,EAC1D,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uEAAsEC,SAAA,CAClFH,EAAQI,OAAQC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,OAAMC,SAAEH,EAAQI,OAC7CJ,EAAQM,eAAiBN,EAAQI,OAAQH,EAAAA,EAAAA,MAAA,KAAGC,UAAU,OAAMC,SAAA,CAAC,SAAG,IAAII,MAAOC,cAAc,IAAER,EAAQM,aAAa,6BAEjHL,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CACGH,EAAQS,cACPR,EAAAA,EAAAA,MAAAS,EAAAA,SAAA,CAAAP,SAAA,EAAEE,EAAAA,EAAAA,KAAA,KAAGM,KAAMX,EAAQS,YAAaG,OAAO,SAASC,IAAI,sBAAsBX,UAAU,gCAA+BC,SAAC,YAAW,SAChIH,EAAQc,kBACPT,EAAAA,EAAAA,KAAA,KAAGM,KAAMX,EAAQc,gBAAiBF,OAAO,SAASC,IAAI,sBAAsBX,UAAU,gCAA+BC,SAAC,mBAGzHH,EAAQe,cAAgBf,EAAQe,aAAaC,OAAS,IACnDX,EAAAA,EAAAA,KAAA,OAAKH,UAAU,OAAMC,SAChBH,EAAQe,aAAaE,KAAI,CAACC,EAAkBC,IAErB,kBAATD,GAA8B,OAATA,GAAiB,aAAcA,GAAQ,QAASA,GAE1EjB,EAAAA,EAAAA,MAAA,KAEIU,KAAMO,EAAKE,KAAO,IAClBR,OAAO,SACPC,IAAI,sBACJX,UAAU,6DAA4DC,SAAA,CAErEe,EAAKG,SAAS,MANV,GAAGH,EAAKG,YAAYF,KAWV,kBAATD,GAEVjB,EAAAA,EAAAA,MAAA,KAEIU,KAAM,IAAIO,IACVhB,UAAU,6DAA4DC,SAAA,CAErEe,EAAK,MAJD,GAAGA,KAAQC,KAQnB,WAIf,EC7BV,EApB4CpB,IAAkB,IAAjB,QAAEC,GAASD,EACtD,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kEAAiEC,SAAA,CAC7EH,EAAQsB,oBAAqBrB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BC,SAAA,CAAC,WAASH,EAAQsB,kBAAkB,OAC1GtB,EAAQuB,WAAYlB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,uCAAsCC,SAAEH,EAAQuB,WAClFvB,EAAQwB,OAAQnB,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBAAoBC,SAAEH,EAAQwB,OAC3DxB,EAAQyB,WACPpB,EAAAA,EAAAA,KAAA,KACEM,KAAMX,EAAQ0B,SAAW,IACzBd,OAAO,SACPC,IAAI,sBACJX,UAAU,4FAA2FC,SAEpGH,EAAQyB,aAGT,ECyBV,EAvCkD1B,IAAkB,IAAjB,QAAEC,GAASD,EAC5D,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wFAAuFC,SAAA,CAEnGH,EAAQ2B,WACPtB,EAAAA,EAAAA,KAAA,OACEuB,IAAK5B,EAAQ2B,SACbE,IAAI,OACJ3B,UAAU,+CACV4B,QAAUC,IAASA,EAAEnB,OAA4BoB,MAAMC,QAAU,MAAM,KAGzEjC,EAAQ2B,WAAYtB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBC,SAAC,wBAG7DE,EAAAA,EAAAA,KAAA,OAAAF,UACEE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iBAAgBC,SAC3BH,EAAQkC,WAAalC,EAAQkC,UAAUjB,KAAI,CAACC,EAAMC,KACjD,MAAMgB,EAAuB,kBAATjB,EAAoBA,EAAKiB,KAAOjB,EAC9CE,EAAsB,kBAATF,EAAoBA,EAAKE,IAAM,IAClD,OACEf,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAAA,KACEM,KAAMS,EACNR,OAAO,SACPC,IAAI,sBACJX,UAAU,4DAA2DC,SAEpEgC,KAPIhB,EASJ,UAKT,ECoBV,EA3CqDpB,IAAkB,IAAjB,QAAEC,GAASD,EAC/D,MAAMqC,EAAmBpC,EAAQqC,WAAarC,EAAQsC,UAAY,CAACtC,GAAW,IAE9E,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sDAAqDC,SAAA,CACjEH,EAAQuB,WAAYlB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2CAA0CC,SAAEH,EAAQuB,YACvFlB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWC,SACvBiC,EAAiBnB,KAAI,CAACsB,EAASpB,KAC9BlB,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,iGAAgGC,SAAA,CAExHoC,EAAQC,YACPnC,EAAAA,EAAAA,KAAA,OACEuB,IAAKW,EAAQC,UACbX,IAAKU,EAAQD,WAAa,gBAC1BpC,UAAU,wFACV4B,QAAUC,IAASA,EAAEnB,OAA4BoB,MAAMC,QAAU,MAAM,KAG3EhC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qCAAoCC,SAAA,CAChDoC,EAAQD,YAAajC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BC,SAAEoC,EAAQD,aACzEC,EAAQE,MAAQF,EAAQG,QACxBzC,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BC,SAAA,CACtCoC,EAAQE,KAAMF,EAAQE,MAAQF,EAAQG,MAAQ,KAAMH,EAAQG,QAGhEH,EAAQd,WACPpB,EAAAA,EAAAA,KAAA,KACEM,KAAM4B,EAAQb,SAAW,IACzBd,OAAO,SACPC,IAAI,sBACJX,UAAU,sGAAqGC,SAE9GoC,EAAQd,gBAxBPN,SA+BV,EC9BV,EAzBoEpB,IAAkB,IAAjB,QAAEC,GAASD,EAC9E,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kFAAiFC,SAAA,CAC7FH,EAAQ2C,QACP1C,EAAAA,EAAAA,MAAA,cAAYC,UAAU,oCAAmCC,SAAA,CAAC,KACrDH,EAAQ2C,MAAM,QAGpB3C,EAAQ4C,SACP3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,CACxDH,EAAQ6C,mBACPxC,EAAAA,EAAAA,KAAA,OACEuB,IAAK5B,EAAQ6C,iBACbhB,IAAK7B,EAAQ4C,OACb1C,UAAU,oCACV4B,QAAUC,IAASA,EAAEnB,OAA4BoB,MAAMC,QAAU,MAAM,KAG3EhC,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mDAAkDC,SAAA,CAAC,KAAGH,EAAQ4C,eAG9E,ECVV,EAV4C7C,IAAwB,IAAvB,QAAEC,EAAO,KAAE8C,GAAM/C,EAE5D,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sDAAqDC,SAAA,CACjEH,EAAQuB,WAAYlB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2CAA0CC,SAAEH,EAAQuB,WACtFvB,EAAQwB,OAAQnB,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gBAAeC,SAAEH,EAAQwB,SACnD,E,cCgBV,MAyPA,EAzPqCuB,KACnC,MAAOC,EAAQC,IAAaC,EAAAA,EAAAA,UAAiB,KACtCC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAiB,UAChDG,EAAkBC,IAAuBJ,EAAAA,EAAAA,UAAmC,OAC5EK,EAASC,IAAcN,EAAAA,EAAAA,WAAkB,IACzCO,EAAOC,IAAYR,EAAAA,EAAAA,UAAwB,OAC3CS,EAASC,IAAcV,EAAAA,EAAAA,UAAgB,KACvCW,EAAaC,IAAkBZ,EAAAA,EAAAA,WAAkB,IAGxDa,EAAAA,EAAAA,YAAU,KACaC,WACnB,IACE,MAAMC,QAAiBC,EAAAA,GAAiBC,aACpCF,EAASG,SACXR,EAAWK,EAASI,KAExB,CAAE,MAAOC,GACPC,QAAQd,MAAM,0BAA2Ba,EAC3C,GAGFE,EAAc,GACb,IA6EH,OACEvE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2CAA0CC,SAAC,0BACzDE,EAAAA,EAAAA,KAACoE,EAAAA,EAAM,CAACC,QAASA,IAAMZ,GAAgBD,GAAcc,QAAQ,YAAWxE,SACpE0D,EAAc,eAAiB,qBAIrCxD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2BAA0BC,SAAC,+DAIxCE,EAAAA,EAAAA,KAACuE,EAAAA,EAAI,CAAC1E,UAAU,OAAMC,UACpBF,EAAAA,EAAAA,MAAA,QAAM4E,SAxFSb,UAGnB,GAFAjC,EAAE+C,iBAEG9B,EAAO+B,OAAZ,CAKAvB,GAAW,GACXE,EAAS,MACTJ,EAAoB,MAEpB,IACE,MAAMW,QAAiBC,EAAAA,GAAiBc,gBAAgBhC,EAAQG,GAE5Dc,EAASG,SAAWH,EAASI,MAAQJ,EAASI,KAAKrE,SAErDsD,EAAoBW,EAASI,KAAKrE,SAElC4D,GAAYqB,GAAgB,CAC1B,IAAKhB,EAASI,KAAKrE,QAASkF,GAAIjB,EAASI,KAAKa,GAAIlC,SAAQF,KAAMK,EAAagC,WAAW,IAAI5E,MAAO6E,kBAChGH,MAGLvB,EAASO,EAASoB,SAAW,6DAEjC,CAAE,MAAOf,GACPZ,EAASY,EAAIe,SAAW,6CAC1B,CAAC,QACC7B,GAAW,EACb,CAxBA,MAFEE,EAAS,wBA0BX,EA0DkCxD,UAAW,kBAAiBqD,EAAU,iCAAmC,IAAKpD,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,SAAOiF,QAAQ,cAAcpF,UAAU,qDAAoDC,SAAC,kBAG5FF,EAAAA,EAAAA,MAAA,UACEiF,GAAG,cACHK,MAAOpC,EACPqC,SAAWzD,GAAMqB,EAAerB,EAAEnB,OAAO2E,OACzCrF,UAAU,wLAAuLC,SAAA,EAEjME,EAAAA,EAAAA,KAAA,UAAQkF,MAAM,QAAOpF,SAAC,gBACtBE,EAAAA,EAAAA,KAAA,UAAQkF,MAAM,UAASpF,SAAC,kBACxBE,EAAAA,EAAAA,KAAA,UAAQkF,MAAM,WAAUpF,SAAC,cACzBE,EAAAA,EAAAA,KAAA,UAAQkF,MAAM,MAAKpF,SAAC,oBACpBE,EAAAA,EAAAA,KAAA,UAAQkF,MAAM,sBAAqBpF,SAAC,+BAIxCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,SAAOiF,QAAQ,SAASpF,UAAU,qDAAoDC,SAAC,YAGvFE,EAAAA,EAAAA,KAAA,YACE6E,GAAG,SACHO,KAAM,EACNF,MAAOvC,EACPwC,SAAWzD,GAAMkB,EAAUlB,EAAEnB,OAAO2E,OACpCG,YAAY,wCACZxF,UAAU,uLAIbuD,IACCpD,EAAAA,EAAAA,KAACsF,EAAAA,EAAK,CAAC7C,KAAK,QAAQuC,QAAS5B,EAAOmC,QAASA,IAAMlC,EAAS,SAG9DrD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mBAAkBC,UAC/BE,EAAAA,EAAAA,KAACoE,EAAAA,EAAM,CACL3B,KAAK,SACL+C,SAAUtC,EAAQpD,SAEjBoD,EAAU,gBAAkB,4BAOpCA,IACCtD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uCAAsCC,SAAA,EACnDE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAgB,KAC/BG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2BAA0BC,SAAC,uCAAsC,OAIjF0D,GAAeF,EAAQ3C,OAAS,IAC/BX,EAAAA,EAAAA,KAACuE,EAAAA,EAAI,CAAC1E,UAAU,OAAMC,UACpBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,6CAA4CC,SAAC,wBAC3DE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0CAAyCC,SACrDwD,EAAQ1C,KAAI,CAAC6E,EAAM3E,KAClBlB,EAAAA,EAAAA,MAAA,OAEEC,UAAU,yEACVwE,QAASA,IA9FQoB,KAE3BA,EAAKzC,kBAAqD,kBAA1ByC,EAAKzC,iBACtCC,EAAoBwC,EAAKzC,mBAGxBkB,QAAQwB,KAAK,sEAAuED,GAEpFxC,EAAoB,OAExBL,EAAU6C,EAAK9C,QAAU,IACzBI,EAAe0C,EAAK3C,aAAe,SACnCW,GAAe,EAAM,EAkFQkC,CAAwBF,GAAM3F,SAAA,EAE7CE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iDAAgDC,SAC1D2F,EAAK9C,QAAU,qBAAqB7B,EAAQ,OAE/Cd,EAAAA,EAAAA,KAAA,KAAGH,UAAU,8BAA6BC,SACvC,IAAII,KAAKuF,EAAKX,WAAWc,qBARvBH,EAAKZ,IAAM/D,aAiB1BoC,GAAWF,IACXhD,EAAAA,EAAAA,KAACuE,EAAAA,EAAI,CAAAzE,UACFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wCAAuCC,SAAA,CAAC,sBAAoBkD,EAAiB6C,SAAW,iBACtG7F,EAAAA,EAAAA,KAACoE,EAAAA,EAAM,CACLC,QA5IYV,UACxB,GAAKX,EAEL,IAAK,IAAD8C,EAGF,MAAMC,EAA2E,QAAlED,EAAGxC,EAAQ0C,MAAKP,GAAQA,EAAKI,UAAY7C,EAAiB6C,iBAAQ,IAAAC,OAAA,EAA/DA,EAAiEjB,GAEnF,IAAKkB,EAEH,YADA7B,QAAQd,MAAM,2DAIhB,MAAMtB,EAAOa,EAAOsD,UAAU,EAAG,IAAM,YACjCpC,EAAAA,GAAiBqC,YAAYH,EAAWjE,GAG9CqE,MAAM,iBACR,CAAE,MAAOlC,GACPC,QAAQd,MAAM,wBAAyBa,GACvCkC,MAAM,0BACR,GAwHY7B,QAAQ,UACR8B,KAAK,KAAItG,SACV,qBAIHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iDAAgDC,SAAA,EAC7DE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DC,SAAC,kBACxEkD,EAAiBqD,QAAUrD,EAAiBqD,OAAO1F,OAAS,EAC3DqC,EAAiBqD,OAAOzF,KAAI,CAAC0F,EAAcxF,KACzC,OAAQwF,EAAM7D,MACZ,IAAK,gBACL,IAAK,iBACH,OAAOzC,EAAAA,EAAAA,KAACuG,EAAS,CAAgC5G,QAAS2G,EAAM3G,SAAzC,GAAG2G,EAAM7D,QAAQ3B,KAC1C,IAAK,wBACL,IAAK,2BACL,IAAK,kBACH,OAAOd,EAAAA,EAAAA,KAACwG,EAAS,CAAgC7G,QAAS2G,EAAM3G,QAAS8C,KAAM6D,EAAM7D,MAA9D,GAAG6D,EAAM7D,QAAQ3B,KAC1C,IAAK,eACH,OAAOd,EAAAA,EAAAA,KAACyG,EAAgB,CAAgC9G,QAAS2G,EAAM3G,SAAzC,GAAG2G,EAAM7D,QAAQ3B,KACjD,IAAK,gBACH,OAAOd,EAAAA,EAAAA,KAAC0G,EAAW,CAAgC/G,QAAS2G,EAAM3G,SAAzC,GAAG2G,EAAM7D,QAAQ3B,KAC5C,IAAK,kBACH,OAAOd,EAAAA,EAAAA,KAAC2G,EAAY,CAAgChH,QAAS2G,EAAM3G,SAAzC,GAAG2G,EAAM7D,QAAQ3B,KAC7C,IAAK,oBACH,OAAOd,EAAAA,EAAAA,KAAC4G,EAAqB,CAAgCjH,QAAS2G,EAAM3G,SAAzC,GAAG2G,EAAM7D,QAAQ3B,KACtD,QACE,OACElB,EAAAA,EAAAA,MAAA,OAA8BC,UAAU,mEAAkEC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,4BAA2BC,SAAA,CAAC,2BAAyBwG,EAAM7D,SACxEzC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAwB8B,MAAO,CAAEkF,WAAY,WAAYC,UAAW,aAAchH,SAC9FiH,KAAKC,UAAUV,EAAM3G,SAAW,CAAC,EAAG,KAAM,OAHrC,WAAWmB,KAO3B,KAGFd,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBAAsBC,SAAC,mCAM1C,C", "sources": ["components/emailBlocks/FooterBlock.tsx", "components/emailBlocks/HeroBlock.tsx", "components/emailBlocks/LogoNavBlock.tsx", "components/emailBlocks/ProductCardBlock.tsx", "components/emailBlocks/QuoteTestimonialBlock.tsx", "components/emailBlocks/TextBlock.tsx", "pages/AIContentGenerator.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\n// Define a type for social links which could be string or object\r\ntype SocialLink = string | { platform: string; url: string };\r\n\r\ninterface FooterBlockProps {\r\n  content: {\r\n    text?: string;\r\n    contact_url?: string;\r\n    unsubscribe_url?: string;\r\n    company_name?: string; // Added based on earlier mock data\r\n    social_links?: SocialLink[]; // Use the new type\r\n  };\r\n}\r\n\r\nconst FooterBlock: React.FC<FooterBlockProps> = ({ content }) => {\r\n  return (\r\n    <div className=\"mt-6 pt-4 border-t border-gray-700 text-center text-xs text-gray-400\">\r\n      {content.text && <p className=\"mb-2\">{content.text}</p>}\r\n      {content.company_name && !content.text && <p className=\"mb-2\">© {new Date().getFullYear()} {content.company_name}. All rights reserved.</p>} \r\n      \r\n      <p>\r\n        {content.contact_url && \r\n          <><a href={content.contact_url} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-gray-200\">Contact</a> | </>}\r\n        {content.unsubscribe_url && \r\n          <a href={content.unsubscribe_url} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-gray-200\">Unsubscribe</a>}\r\n      </p>\r\n      \r\n      {content.social_links && content.social_links.length > 0 && (\r\n          <div className=\"mt-2\">\r\n              {content.social_links.map((link: SocialLink, index: number) => {\r\n                  // Check if link is an object with platform and url\r\n                  if (typeof link === 'object' && link !== null && 'platform' in link && 'url' in link) {\r\n                    return (\r\n                        <a \r\n                            key={`${link.platform}-${index}`} \r\n                            href={link.url || '#'} \r\n                            target=\"_blank\" \r\n                            rel=\"noopener noreferrer\" \r\n                            className=\"inline-block mx-1 underline hover:text-gray-200 capitalize\"\r\n                        >\r\n                            {link.platform} {/* Display platform name */}\r\n                        </a>\r\n                    );\r\n                  } \r\n                  // Otherwise, assume it's a string (fallback)\r\n                  else if (typeof link === 'string') {\r\n                    return (\r\n                        <a \r\n                            key={`${link}-${index}`} \r\n                            href={`#${link}`} // Simple href if only string is provided\r\n                            className=\"inline-block mx-1 underline hover:text-gray-200 capitalize\"\r\n                        >\r\n                            {link} {/* Display the string directly */}\r\n                        </a>\r\n                    );\r\n                  }\r\n                  return null; // Skip rendering if format is unexpected\r\n              })}\r\n          </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FooterBlock; ", "import React from 'react';\r\n\r\ninterface HeroBlockProps {\r\n  content: {\r\n    headline?: string;\r\n    body?: string;\r\n    cta_text?: string;\r\n    cta_url?: string;\r\n    image_description?: string; // We won't render the image itself for now\r\n  };\r\n}\r\n\r\nconst HeroBlock: React.FC<HeroBlockProps> = ({ content }) => {\r\n  return (\r\n    <div className=\"mb-4 p-4 border border-gray-600 rounded bg-gray-800 text-center\">\r\n      {content.image_description && <p className=\"text-xs text-gray-400 mb-2\">(Image: {content.image_description})</p>}\r\n      {content.headline && <h2 className=\"text-xl font-bold mb-2 text-blue-400\">{content.headline}</h2>}\r\n      {content.body && <p className=\"mb-3 text-gray-300\">{content.body}</p>}\r\n      {content.cta_text && (\r\n        <a \r\n          href={content.cta_url || '#'} \r\n          target=\"_blank\" \r\n          rel=\"noopener noreferrer\"\r\n          className=\"inline-block bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors\"\r\n        >\r\n          {content.cta_text}\r\n        </a>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HeroBlock; ", "import React from 'react';\r\n\r\ninterface NavLink {\r\n  name: string;\r\n  url: string;\r\n}\r\n\r\ninterface LogoNavBlockProps {\r\n  content: {\r\n    logo_url?: string;\r\n    nav_links?: (NavLink | string)[]; // Can be objects or simple strings\r\n  };\r\n}\r\n\r\nconst LogoNavBlock: React.FC<LogoNavBlockProps> = ({ content }) => {\r\n  return (\r\n    <div className=\"mb-4 p-4 border border-gray-600 rounded bg-gray-800 flex items-center justify-between\">\r\n      {/* Logo */}\r\n      {content.logo_url && (\r\n        <img \r\n          src={content.logo_url} \r\n          alt=\"Logo\" \r\n          className=\"h-8 w-auto max-w-[100px] object-contain mr-4\"\r\n          onError={(e) => { (e.target as HTMLImageElement).style.display = 'none'; }}\r\n        />\r\n      )}\r\n      {!content.logo_url && <div className=\"text-gray-400 text-sm\">(No Logo Provided)</div>}\r\n\r\n      {/* Navigation Links */}\r\n      <nav>\r\n        <ul className=\"flex space-x-4\">\r\n          {content.nav_links && content.nav_links.map((link, index) => {\r\n            const name = typeof link === 'object' ? link.name : link;\r\n            const url = typeof link === 'object' ? link.url : '#';\r\n            return (\r\n              <li key={index}>\r\n                <a \r\n                  href={url}\r\n                  target=\"_blank\" \r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-sm text-blue-400 hover:text-blue-300 hover:underline\"\r\n                >\r\n                  {name}\r\n                </a>\r\n              </li>\r\n            );\r\n          })}\r\n        </ul>\r\n      </nav>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LogoNavBlock; ", "import React from 'react';\r\n\r\ninterface ProductCardProps {\r\n  content: {\r\n    headline?: string; // Optional headline for the section\r\n    products?: Product[]; // Array of products/items (like shows)\r\n    // Handle single product scenario as well\r\n    show_name?: string; \r\n    date?: string;\r\n    time?: string;\r\n    image_url?: string; // Not rendering image for now\r\n    cta_text?: string;\r\n    cta_url?: string;\r\n  };\r\n}\r\n\r\ninterface Product {\r\n  show_name?: string;\r\n  date?: string;\r\n  time?: string;\r\n  image_url?: string;\r\n  cta_text?: string;\r\n  cta_url?: string;\r\n  // Add other potential product fields if needed\r\n}\r\n\r\nconst ProductCardBlock: React.FC<ProductCardProps> = ({ content }) => {\r\n  const productsToRender = content.products || (content.show_name ? [content] : []); // Handle both array and single product structure\r\n\r\n  return (\r\n    <div className=\"mb-4 p-4 border border-gray-600 rounded bg-gray-800\">\r\n      {content.headline && <h3 className=\"text-lg font-semibold mb-3 text-gray-100\">{content.headline}</h3>}\r\n      <div className=\"space-y-3\">\r\n        {productsToRender.map((product, index) => (\r\n          <div key={index} className=\"p-3 border border-gray-700 rounded bg-gray-700/50 flex flex-col sm:flex-row gap-3 items-center\">\r\n            {/* Render Image */} \r\n            {product.image_url && (\r\n              <img \r\n                src={product.image_url} \r\n                alt={product.show_name || 'Product Image'} \r\n                className=\"w-full sm:w-24 h-auto sm:h-24 object-cover rounded mb-2 sm:mb-0 sm:mr-3 flex-shrink-0\" \r\n                onError={(e) => { (e.target as HTMLImageElement).style.display = 'none'; /* Hide broken images */ }} \r\n              />\r\n            )}\r\n            <div className=\"flex-grow text-center sm:text-left\">\r\n              {product.show_name && <h4 className=\"font-semibold text-gray-200\">{product.show_name}</h4>}\r\n              {(product.date || product.time) && (\r\n                <p className=\"text-sm text-gray-300 mb-2\">\r\n                  {product.date}{product.date && product.time && ', '}{product.time}\r\n                </p>\r\n              )}\r\n              {product.cta_text && (\r\n                <a \r\n                  href={product.cta_url || '#'} \r\n                  target=\"_blank\" \r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"inline-block bg-green-600 text-white text-sm py-1 px-3 rounded hover:bg-green-700 transition-colors\"\r\n                >\r\n                  {product.cta_text}\r\n                </a>\r\n              )}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProductCardBlock; ", "import React from 'react';\r\n\r\ninterface QuoteTestimonialBlockProps {\r\n  content: {\r\n    quote?: string;\r\n    author?: string;\r\n    author_image_url?: string; // Optional author image\r\n  };\r\n}\r\n\r\nconst QuoteTestimonialBlock: React.FC<QuoteTestimonialBlockProps> = ({ content }) => {\r\n  return (\r\n    <div className=\"mb-4 p-4 border border-dashed border-purple-400 rounded bg-gray-800 text-center\">\r\n      {content.quote && (\r\n        <blockquote className=\"text-lg italic text-gray-200 mb-3\">\r\n          \" {content.quote} \"\r\n        </blockquote>\r\n      )}\r\n      {content.author && (\r\n        <div className=\"flex items-center justify-center space-x-2\">\r\n          {content.author_image_url && (\r\n            <img \r\n              src={content.author_image_url} \r\n              alt={content.author} \r\n              className=\"w-8 h-8 rounded-full object-cover\"\r\n              onError={(e) => { (e.target as HTMLImageElement).style.display = 'none'; }}\r\n            />\r\n          )}\r\n          <cite className=\"text-sm font-semibold text-purple-300 not-italic\">- {content.author}</cite>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuoteTestimonialBlock; ", "import React from 'react';\r\n\r\ninterface TextBlockProps {\r\n  content: {\r\n    headline?: string;\r\n    body?: string;\r\n    // Could add other simple text fields if needed for different block types\r\n  };\r\n  type: string; // Pass type to potentially style differently\r\n}\r\n\r\nconst TextBlock: React.FC<TextBlockProps> = ({ content, type }) => {\r\n  // Basic rendering, can be expanded\r\n  return (\r\n    <div className=\"mb-4 p-4 border border-gray-600 rounded bg-gray-800\">\r\n      {content.headline && <h3 className=\"text-lg font-semibold mb-1 text-gray-100\">{content.headline}</h3>}\r\n      {content.body && <p className=\"text-gray-300\">{content.body}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TextBlock; ", "import './spinner.css'; // Import CSS for spinner\n\nimport React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\nimport FooterBlock from '../components/emailBlocks/FooterBlock';\nimport HeroBlock from '../components/emailBlocks/HeroBlock';\nimport LogoNavBlock from '../components/emailBlocks/LogoNavBlock';\nimport ProductCardBlock from '../components/emailBlocks/ProductCardBlock';\nimport QuoteTestimonialBlock\n  from '../components/emailBlocks/QuoteTestimonialBlock';\nimport TextBlock from '../components/emailBlocks/TextBlock';\nimport { aiContentService } from '../services';\n\n// Define an interface for the AI-generated template structure\ninterface GeneratedTemplate {\n  subject: string;\n  blocks: Block[];\n  // Add other potential fields like tone, color_scheme if needed\n}\n\n// Define a general Block interface\ninterface Block {\n  type: string;\n  category?: string;\n  content: any; // Use 'any' for now, can be refined\n}\n\nconst AIContentGenerator: React.FC = () => {\n  const [prompt, setPrompt] = useState<string>('');\n  const [contentType, setContentType] = useState<string>('email');\n  const [generatedContent, setGeneratedContent] = useState<GeneratedTemplate | null>(null);\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [history, setHistory] = useState<any[]>([]);\n  const [showHistory, setShowHistory] = useState<boolean>(false);\n\n  // Fetch content generation history on component mount\n  useEffect(() => {\n    const fetchHistory = async () => {\n      try {\n        const response = await aiContentService.getHistory();\n        if (response.success) {\n          setHistory(response.data);\n        }\n      } catch (err) {\n        console.error('Error fetching history:', err);\n      }\n    };\n\n    fetchHistory();\n  }, []);\n\n  // Handle form submission\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!prompt.trim()) {\n      setError('Please enter a prompt');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    setGeneratedContent(null); // Clear previous content\n\n    try {\n      const response = await aiContentService.generateContent(prompt, contentType);\n\n      if (response.success && response.data && response.data.content) {\n        // Set the content object from the response\n        setGeneratedContent(response.data.content);\n        // Add the full response data (including id and content) to history\n        setHistory((prevHistory) => [\n          { ...response.data.content, id: response.data.id, prompt, type: contentType, createdAt: new Date().toISOString() }, \n          ...prevHistory\n        ]); \n      } else {\n        setError(response.message || 'Failed to generate content - unexpected response structure');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while generating content');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle saving content\n  const handleSaveContent = async () => {\n    if (!generatedContent) return;\n\n    try {\n      // Find the corresponding history item to get its ID\n      // This assumes the latest generated item is the first one in history\n      const contentId = history.find(item => item.subject === generatedContent.subject)?.id; \n      // TODO: A more robust way to link generated content to its history ID might be needed\n      if (!contentId) {\n        console.error(\"Could not find ID for the generated content in history.\");\n        return;\n      }\n\n      const name = prompt.substring(0, 30) + '...'; // Use current prompt for name\n      await aiContentService.saveContent(contentId, name);\n\n      // Show success message or update UI\n      alert('Content saved!'); // Simple alert for now\n    } catch (err) {\n      console.error('Error saving content:', err);\n      alert('Failed to save content.'); // Simple alert for now\n    }\n  };\n\n  // Handle selecting content from history\n  const handleSelectFromHistory = (item: any) => {\n    // Expect item.generatedContent to hold the object structure\n    if (item.generatedContent && typeof item.generatedContent === 'object') {\n       setGeneratedContent(item.generatedContent);\n    } else {\n        // Handle cases where history item might have old string format or is missing data\n        console.warn('Selected history item does not have the expected content structure:', item);\n        // Optionally try to reconstruct or clear the state\n        setGeneratedContent(null); \n    }\n    setPrompt(item.prompt || '');\n    setContentType(item.contentType || 'email'); // Use contentType from history item\n    setShowHistory(false);\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-semibold text-text-primary\">AI Content Generator</h1>\n        <Button onClick={() => setShowHistory(!showHistory)} variant=\"secondary\">\n           {showHistory ? 'Hide History' : 'Show History'}\n        </Button>\n      </div>\n      \n      <p className=\"text-text-secondary mb-6\">\n          Generate email content, subject lines, and more using AI.\n      </p>\n\n      <Card className=\"mb-6\">\n        <form onSubmit={handleSubmit} className={`p-6 space-y-4 ${loading ? 'opacity-70 pointer-events-none' : ''}`}>\n          <div>\n            <label htmlFor=\"contentType\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n              Content Type\n            </label>\n            <select\n              id=\"contentType\"\n              value={contentType}\n              onChange={(e) => setContentType(e.target.value)}\n              className=\"mt-1 block w-full pl-3 pr-10 py-2 text-base bg-secondary-bg border border-gray-600 text-text-primary focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md\"\n            >\n              <option value=\"email\">Email Body</option>\n              <option value=\"subject\">Subject Line</option>\n              <option value=\"headline\">Headline</option>\n              <option value=\"cta\">Call to Action</option>\n              <option value=\"product_description\">Product Description</option>\n            </select>\n          </div>\n\n          <div>\n            <label htmlFor=\"prompt\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n              Prompt\n            </label>\n            <textarea\n              id=\"prompt\"\n              rows={4}\n              value={prompt}\n              onChange={(e) => setPrompt(e.target.value)}\n              placeholder=\"Describe what you want to generate...\"\n              className=\"mt-1 block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 text-text-primary focus:outline-none focus:ring-primary focus:border-primary sm:text-sm\"\n            />\n          </div>\n\n          {error && (\n            <Alert type=\"error\" message={error} onClose={() => setError(null)} />\n          )}\n\n          <div className=\"flex justify-end\">\n            <Button\n              type=\"submit\"\n              disabled={loading}\n            >\n              {loading ? 'Generating...' : 'Generate Content'}\n            </Button>\n          </div>\n        </form>\n      </Card>\n\n      {/* Loading Indicator */} \n      {loading && (\n        <div className=\"flex justify-center items-center p-6\">\n          <div className=\"spinner\"></div> {/* Spinner element */}\n          <p className=\"ml-3 text-text-secondary\">Generating content, please wait...</p> {/* Updated Text */}\n        </div>\n      )}\n\n      {showHistory && history.length > 0 && (\n        <Card className=\"mb-6\">\n          <div className=\"p-6\">\n            <h2 className=\"text-lg font-medium text-text-primary mb-4\">Generation History</h2>\n            <div className=\"space-y-3 max-h-60 overflow-y-auto pr-2\">\n              {history.map((item, index) => (\n                <div\n                  key={item.id || index}\n                  className=\"p-3 border border-gray-700 rounded-md hover:bg-gray-700 cursor-pointer\"\n                  onClick={() => handleSelectFromHistory(item)}\n                >\n                  <p className=\"text-sm font-medium text-text-primary truncate\">\n                    {item.prompt || `Generated content ${index + 1}`}\n                  </p>\n                  <p className=\"text-xs text-text-secondary\">\n                    {new Date(item.createdAt).toLocaleString()}\n                  </p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </Card>\n      )}\n\n      {!loading && generatedContent && (\n        <Card>\n           <div className=\"p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-lg font-medium text-text-primary\">Generated Content: {generatedContent.subject || 'No Subject'}</h2>\n              <Button\n                onClick={handleSaveContent}\n                variant=\"primary\"\n                size=\"sm\"\n              >\n                Save Content\n              </Button>\n            </div>\n            <div className=\"border border-gray-700 rounded p-4 bg-gray-900\">\n              <h3 className=\"text-md font-semibold mb-3 border-b border-gray-600 pb-2\">Email Preview</h3>\n              {generatedContent.blocks && generatedContent.blocks.length > 0 ? (\n                generatedContent.blocks.map((block: Block, index: number) => {\n                  switch (block.type) {\n                    case 'hero_with_cta':\n                    case 'full_width_cta':\n                      return <HeroBlock key={`${block.type}-${index}`} content={block.content} />;\n                    case 'two_column_text_image': \n                    case 'headline_supporting_text':\n                    case 'text_with_video':\n                      return <TextBlock key={`${block.type}-${index}`} content={block.content} type={block.type} />;\n                    case 'product_card':\n                      return <ProductCardBlock key={`${block.type}-${index}`} content={block.content} />;\n                    case 'simple_footer':\n                      return <FooterBlock key={`${block.type}-${index}`} content={block.content} />;\n                    case 'simple_logo_nav':\n                      return <LogoNavBlock key={`${block.type}-${index}`} content={block.content} />;\n                    case 'quote_testimonial':\n                      return <QuoteTestimonialBlock key={`${block.type}-${index}`} content={block.content} />;\n                    default:\n                      return (\n                        <div key={`unknown-${index}`} className=\"mb-4 p-2 border border-dashed border-red-400 rounded bg-gray-800\">\n                          <p className=\"text-xs text-red-300 mb-1\">Unsupported Block Type: {block.type}</p>\n                          <pre className=\"text-xs text-gray-400\" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>\n                            {JSON.stringify(block.content || {}, null, 2)}\n                          </pre>\n                        </div>\n                      );\n                  }\n                })\n              ) : (\n                <p className=\"text-gray-400 italic\">No blocks generated.</p>\n              )}\n            </div>\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default AIContentGenerator;\n"], "names": ["_ref", "content", "_jsxs", "className", "children", "text", "_jsx", "company_name", "Date", "getFullYear", "contact_url", "_Fragment", "href", "target", "rel", "unsubscribe_url", "social_links", "length", "map", "link", "index", "url", "platform", "image_description", "headline", "body", "cta_text", "cta_url", "logo_url", "src", "alt", "onError", "e", "style", "display", "nav_links", "name", "productsToRender", "products", "show_name", "product", "image_url", "date", "time", "quote", "author", "author_image_url", "type", "AIContentGenerator", "prompt", "setPrompt", "useState", "contentType", "setContentType", "generatedContent", "setGeneratedContent", "loading", "setLoading", "error", "setError", "history", "setHistory", "showHistory", "setShowHistory", "useEffect", "async", "response", "aiContentService", "getHistory", "success", "data", "err", "console", "fetchHistory", "<PERSON><PERSON>", "onClick", "variant", "Card", "onSubmit", "preventDefault", "trim", "generateContent", "prevHistory", "id", "createdAt", "toISOString", "message", "htmlFor", "value", "onChange", "rows", "placeholder", "<PERSON><PERSON>", "onClose", "disabled", "item", "warn", "handleSelectFromHistory", "toLocaleString", "subject", "_history$find", "contentId", "find", "substring", "saveContent", "alert", "size", "blocks", "block", "HeroBlock", "TextBlock", "ProductCardBlock", "<PERSON><PERSON><PERSON><PERSON>", "LogoNavBlock", "QuoteTestimonialBlock", "whiteSpace", "wordBreak", "JSON", "stringify"], "sourceRoot": ""}