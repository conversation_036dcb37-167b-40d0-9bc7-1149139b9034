{"ast": null, "code": "export const strictEquality = (a, b) => a === b;\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */\nexport function areCoordsEqual(offsetA, offsetB) {\n  if (!offsetA && !offsetB) {\n    return true;\n  } else if (!offsetA || !offsetB) {\n    return false;\n  } else {\n    return offsetA.x === offsetB.x && offsetA.y === offsetB.y;\n  }\n}\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */\nexport function areArraysEqual(a, b) {\n  let isEqual = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : strictEquality;\n  if (a.length !== b.length) {\n    return false;\n  }\n  for (let i = 0; i < a.length; ++i) {\n    if (!isEqual(a[i], b[i])) {\n      return false;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["strictEquality", "a", "b", "areCoordsEqual", "offsetA", "offsetB", "x", "y", "areArraysEqual", "isEqual", "arguments", "length", "undefined", "i"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\dnd-core\\src\\utils\\equality.ts"], "sourcesContent": ["import type { XYCoord } from '../interfaces.js'\n\nexport type EqualityCheck<T> = (a: T, b: T) => boolean\nexport const strictEquality = <T>(a: T, b: T): boolean => a === b\n\n/**\n * Determine if two cartesian coordinate offsets are equal\n * @param offsetA\n * @param offsetB\n */\nexport function areCoordsEqual(\n\toffsetA: XYCoord | null | undefined,\n\toffsetB: XYCoord | null | undefined,\n): boolean {\n\tif (!offsetA && !offsetB) {\n\t\treturn true\n\t} else if (!offsetA || !offsetB) {\n\t\treturn false\n\t} else {\n\t\treturn offsetA.x === offsetB.x && offsetA.y === offsetB.y\n\t}\n}\n\n/**\n * Determines if two arrays of items are equal\n * @param a The first array of items\n * @param b The second array of items\n */\nexport function areArraysEqual<T>(\n\ta: T[],\n\tb: T[],\n\tisEqual: EqualityCheck<T> = strictEquality,\n): boolean {\n\tif (a.length !== b.length) {\n\t\treturn false\n\t}\n\tfor (let i = 0; i < a.length; ++i) {\n\t\tif (!isEqual(a[i] as T, b[i] as T)) {\n\t\t\treturn false\n\t\t}\n\t}\n\treturn true\n}\n"], "mappings": "AAGA,OAAO,MAAMA,cAAc,GAAGA,CAAIC,CAAI,EAAEC,CAAI,KAAcD,CAAC,KAAKC,CAAC;AAEjE;;;;;AAKA,OAAO,SAASC,cAAcA,CAC7BC,OAAmC,EACnCC,OAAmC,EACzB;EACV,IAAI,CAACD,OAAO,IAAI,CAACC,OAAO,EAAE;IACzB,OAAO,IAAI;GACX,MAAM,IAAI,CAACD,OAAO,IAAI,CAACC,OAAO,EAAE;IAChC,OAAO,KAAK;GACZ,MAAM;IACN,OAAOD,OAAO,CAACE,CAAC,KAAKD,OAAO,CAACC,CAAC,IAAIF,OAAO,CAACG,CAAC,KAAKF,OAAO,CAACE,CAAC;;;AAI3D;;;;;AAKA,OAAO,SAASC,cAAcA,CAC7BP,CAAM,EACNC,CAAM,EAEI;EAAA,IADVO,OAAyB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGV,cAAc;EAE1C,IAAIC,CAAC,CAACU,MAAM,KAAKT,CAAC,CAACS,MAAM,EAAE;IAC1B,OAAO,KAAK;;EAEb,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,CAAC,CAACU,MAAM,EAAE,EAAEE,CAAC,EAAE;IAClC,IAAI,CAACJ,OAAO,CAACR,CAAC,CAACY,CAAC,CAAC,EAAOX,CAAC,CAACW,CAAC,CAAC,CAAM,EAAE;MACnC,OAAO,KAAK;;;EAGd,OAAO,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}