{"version": 3, "file": "static/js/670.25132329.chunk.js", "mappings": "yMAqCA,MAAMA,EAA0BC,IAC9B,OAAQA,EAASC,eACf,IAAK,aACH,MAAO,8BACT,IAAK,YACH,MAAO,8BACT,IAAK,UACH,MAAO,6BACT,IAAK,eACH,MAAO,gCACT,IAAK,aACH,MAAO,2BACT,QACE,MAAO,4BACX,EAIIC,EAAiBA,KACrBC,EAAAA,EAAAA,KAAA,OAAKC,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YAAYC,YAAa,IAAKC,OAAO,eAAeC,UAAU,kCAAiCC,UACzJP,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,6RAInDC,EAAgBA,KACpBC,EAAAA,EAAAA,MAAA,OAAKX,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YAAYC,YAAa,IAAKC,OAAO,eAAeC,UAAU,kCAAiCC,SAAA,EACzJP,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,mNACrDV,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,2BAInDG,EAAcA,KAClBb,EAAAA,EAAAA,KAAA,OAAKC,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YAAYC,YAAa,IAAKC,OAAO,eAAeC,UAAU,kCAAiCC,UACzJP,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,iSAInDI,EAAmBA,KACvBd,EAAAA,EAAAA,KAAA,OAAKC,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YAAYC,YAAa,IAAKC,OAAO,eAAeC,UAAU,kCAAiCC,UACzJP,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,+iBAInDK,EAAgBA,KACpBf,EAAAA,EAAAA,KAAA,OAAKC,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YAAYC,YAAa,IAAKC,OAAO,eAAeC,UAAU,kCAAiCC,UACzJP,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,4TAKnDM,EAAsBA,KAC1BJ,EAAAA,EAAAA,MAAA,OAAKX,MAAM,6BAA6BC,KAAK,OAAOC,QAAQ,YAAYC,YAAa,IAAKC,OAAO,eAAeC,UAAU,kCAAiCC,SAAA,EACzJP,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,qLACrDV,EAAAA,EAAAA,KAAA,QAAMQ,cAAc,QAAQC,eAAe,QAAQC,EAAE,4EAKnDO,EAAsBpB,IAC1B,OAAQA,EAASC,eACf,IAAK,aACH,OAAOE,EAAAA,EAAAA,KAACD,EAAc,IACxB,IAAK,YACL,IAAK,cACH,OAAOC,EAAAA,EAAAA,KAACW,EAAa,IACvB,IAAK,UACL,IAAK,aACH,OAAOX,EAAAA,EAAAA,KAACa,EAAW,IACrB,IAAK,eACH,OAAOb,EAAAA,EAAAA,KAACc,EAAgB,IAC1B,IAAK,aACH,OAAOd,EAAAA,EAAAA,KAACe,EAAa,IAKvB,QACE,OAAOf,EAAAA,EAAAA,KAACgB,EAAmB,IAC/B,EAiXF,EA5WoDE,KAClD,MAAMC,GAAWC,EAAAA,EAAAA,OAGVC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAqB,KACtDC,EAAsBC,IAA2BF,EAAAA,EAAAA,UAA+B,CAAC,IACjFG,EAAeC,IAAoBJ,EAAAA,EAAAA,UAAqB,KACxDK,EAAYC,IAAiBN,EAAAA,EAAAA,UAAmB,KAChDO,EAAkBC,IAAuBR,EAAAA,EAAAA,UAAiB,KAC1DS,EAAUC,IAAeV,EAAAA,EAAAA,UAA4B,WACrDW,EAASC,IAAcZ,EAAAA,EAAAA,WAAkB,IACzCa,EAAOC,IAAYd,EAAAA,EAAAA,UAAwB,OAE3Ce,EAAmBC,IAAwBhB,EAAAA,EAAAA,WAAS,IACpDiB,EAAoBC,IAAyBlB,EAAAA,EAAAA,UAAwB,OAErEmB,EAAmBC,IAAwBpB,EAAAA,EAAAA,UAAmB,KAC9DqB,EAAwBC,IAA6BtB,EAAAA,EAAAA,WAAS,IAErEuB,EAAAA,EAAAA,YAAU,KACRC,GAAmB,GAClB,KAGHD,EAAAA,EAAAA,YAAU,KACRE,GAAkB,GACjB,CAAC3B,EAAcW,IAElB,MAAMe,EAAoBE,UACxBd,GAAW,GACXE,EAAS,MACT,IACEa,QAAQC,IAAI,6CAEZ,MAAMC,QAAiBC,EAAAA,GAA8BC,kBAEjDF,EAASG,SAAWH,EAASI,KAC/BlC,EAAgB8B,EAASI,OAEzBnB,EAASe,EAASK,SAAW,6BAC7BnC,EAAgB,IAEpB,CAAE,MAAOoC,GAAW,IAADC,EAAAC,EACjBV,QAAQd,MAAM,gCAAiCsB,GAC/C,MAAMD,GAAsB,QAAZE,EAAAD,EAAIN,gBAAQ,IAAAO,GAAM,QAANC,EAAZD,EAAcH,YAAI,IAAAI,OAAN,EAAZA,EAAoBH,UAAWC,EAAID,SAAW,+BAC9DpB,EAASoB,GACTnC,EAAgB,GAClB,CAAC,QACCa,GAAW,EACb,GAIIa,EAAmBA,KACvB,GAAiB,WAAbhB,EAAuB,CACzB,MACM6B,EADkBxC,EAAayC,QAAOC,GAAKA,EAAEC,WACfC,QAAO,CAACC,EAA2BC,KACrE,MAAMtE,EAAWsE,EAAStE,UAAY,QAKtC,OAJKqE,EAAIrE,KACPqE,EAAIrE,GAAY,IAElBqE,EAAIrE,GAAUuE,KAAKD,GACZD,CAAG,GACT,CAAC,GACJzC,EAAwBoC,GACxB,MAAMQ,EAAoBC,OAAOC,KAAKV,GACtChC,EAAcwC,GAETvC,GAAqBuC,EAAkBG,SAAS1C,IAClDC,EAAoBsC,EAAkB,IAAM,IAE/C1C,EAAiB,GACnB,KAAO,CAGL,MAAM8C,EAAqBpD,EAAayC,QAAOC,IAAMA,EAAEC,WACvDrC,EAAiB8C,GACjBhD,EAAwB,CAAC,GACzBI,EAAc,IACdE,EAAoB,GACtB,GAII2C,EAAqBC,IACzBxD,EAAS,2BAA2BwD,IAAa,EAiC7CC,EAAmBA,KACvBrC,GAAqB,GACrBE,EAAsB,KAAK,EA0CvBoC,EAAwBA,KAC5BhC,GAA0B,EAAM,EAelC,IAAIiC,EACJ,GAAI5C,EACF4C,GAAkB9E,EAAAA,EAAAA,KAAA,KAAGM,UAAU,6CAA4CC,SAAC,8BACvE,GAAI6B,EACT0C,GAAkBlE,EAAAA,EAAAA,MAAA,KAAGN,UAAU,+CAA8CC,SAAA,CAAC,UAAQ6B,UACjF,GAAiB,WAAbJ,EAAuB,CAAC,IAAD+C,EAC9B,GAA0B,IAAtBnD,EAAWoD,OACXF,GAAkB9E,EAAAA,EAAAA,KAAA,KAAGM,UAAU,6CAA4CC,SAAC,8CACzE,GAAIuB,IAA0D,QAAtCiD,EAAAvD,EAAqBM,UAAiB,IAAAiD,OAAA,EAAtCA,EAAwCC,QAAS,EAAG,CAE/EF,EAD2BtD,EAAqBM,GACXmD,KAAKd,IAExC,MAAMe,EAAgBtF,EAAuBuE,EAAStE,UAChDsF,EAAelE,EAAmBkD,EAAStE,UAGjD,OACEe,EAAAA,EAAAA,MAACwE,EAAAA,EAAI,CAAmB9E,UAAU,6EAA4EC,SAAA,EAC5GK,EAAAA,EAAAA,MAAA,OAAKN,UAAW,0CAA0C4E,wGAAoH3E,SAAA,EAC5KP,EAAAA,EAAAA,KAAA,OAAKM,UAAU,OAAMC,SAAE4E,KACvBnF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,uEAAsEC,SAAE4D,EAASkB,WAEnGzE,EAAAA,EAAAA,MAAA,OAAKN,UAAU,8BAA6BC,SAAA,EAC1CP,EAAAA,EAAAA,KAAA,KAAGM,UAAU,uCAAsCC,SAAE4D,EAASmB,aAAe,+BAC7EtF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,sCAAqCC,UAClDP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACC,KAAK,KAAKC,QAAQ,YAAYC,QAASA,IAAMhB,EAAkBP,EAASwB,IAAIpF,SAAC,wBARhF4D,EAASwB,GAYb,GAGf,MACIb,GAAkBlE,EAAAA,EAAAA,MAAA,KAAGN,UAAU,6CAA4CC,SAAA,CAAC,8BAA4BuB,EAAiB,gBAEjI,MAEQgD,EADyB,IAAzBpD,EAAcsD,QACIhF,EAAAA,EAAAA,KAAA,KAAGM,UAAU,6CAA4CC,SAAC,oDAE1DmB,EAAcuD,KAAKd,IAEnC,MACMe,EAAgBtF,EAAuBuE,EAAStE,UAChDsF,EAAelE,EAAmBkD,EAAStE,UAC3C+F,EAAalD,EAAkB8B,SAASL,EAASwB,IAEvD,OACE/E,EAAAA,EAAAA,MAACwE,EAAAA,EAAI,CAAmB9E,UAAU,6EAA4EC,SAAA,EAC5GK,EAAAA,EAAAA,MAAA,OAAKN,UAAW,0CAA0C4E,wGAAoH3E,SAAA,EAE5KP,EAAAA,EAAAA,KAAA,OAAKM,UAAU,6BAA4BC,UACzCP,EAAAA,EAAAA,KAAA,SACE6F,KAAK,WACLC,QAASF,EACTG,SAAWC,GA5GFC,EAACtB,EAAoBiB,KAChDjD,GAAqBuD,GACnBN,EACI,IAAIM,EAAMvB,GACVuB,EAAKpC,QAAO6B,GAAMA,IAAOhB,KAC9B,EAuGkCsB,CAAqB9B,EAASwB,GAAIK,EAAEG,OAAOL,SAC5DxF,UAAU,6EAGdN,EAAAA,EAAAA,KAAA,OAAKM,UAAU,OAAMC,SAAE4E,KACvBnF,EAAAA,EAAAA,KAAA,QAAMM,UAAU,uEAAsEC,SAAE4D,EAASkB,WAEnGzE,EAAAA,EAAAA,MAAA,OAAKN,UAAU,8BAA6BC,SAAA,EAC1CP,EAAAA,EAAAA,KAAA,KAAGM,UAAU,uCAAsCC,SAAE4D,EAASmB,aAAe,+BAC7E1E,EAAAA,EAAAA,MAAA,OAAKN,UAAU,6CAA4CC,SAAA,EACzDP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACC,KAAK,KAAKC,QAAQ,YAAYC,QAASA,IAAMhB,EAAkBP,EAASwB,IAAIpF,SAAC,kBAEnFK,EAAAA,EAAAA,MAAAwF,EAAAA,SAAA,CAAA7F,SAAA,EACEP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACC,KAAK,KAAKC,QAAQ,YAAYC,QAASA,KAAMW,OA3J/C1B,EA2JkER,EAASwB,QA1JrGxE,EAAS,2BAA2BwD,KADVA,KA2J+E,EAAApE,SAAC,UACtFP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACC,KAAK,KAAKC,QAAQ,SAASC,QAASA,KAAMY,OAvJ1C3B,EAuJ+DR,EAASwB,GAtJpGlD,EAAsBkC,QACtBpC,GAAqB,GAFOoC,KAuJ4E,EAAApE,SAAC,sBArBpF4D,EAASwB,GA0Bb,IAMnB,OACE/E,EAAAA,EAAAA,MAAA,OAAKN,UAAU,0CAAyCC,SAAA,EACtDK,EAAAA,EAAAA,MAAA,OAAKN,UAAU,yCAAwCC,SAAA,EACrDP,EAAAA,EAAAA,KAAA,MAAIM,UAAU,yBAAwBC,SAAC,qBACvCK,EAAAA,EAAAA,MAAA,OAAKN,UAAU,aAAYC,SAAA,CACX,SAAbyB,IACChC,EAAAA,EAAAA,KAAAoG,EAAAA,SAAA,CAAA7F,SACGmC,EAAkBsC,OAAS,GAC1BpE,EAAAA,EAAAA,MAAAwF,EAAAA,SAAA,CAAA7F,SAAA,EACEP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACE,QAAQ,YAAYC,QA7Ffa,KAC3B5D,EAAqB,GAAG,EA4FgDpC,SAAC,kBAG3DK,EAAAA,EAAAA,MAAC2E,EAAAA,EAAM,CAACE,QAAQ,SAASC,QAxIhBc,KACU,IAA7B9D,EAAkBsC,QACtBnC,GAA0B,EAAK,EAsIkCtC,SAAA,CAAC,oBAChCmC,EAAkBsC,OAAO,WAI/ChF,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACE,QAAQ,YAAYC,QA3Gfe,KACzB,MAAMC,EAAMhF,EAAcuD,KAAId,GAAYA,EAASwB,KACnDhD,EAAqB+D,EAAI,EAyG2CnG,SAAC,kBAM/DP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACE,QAAQ,YAAYC,QAASA,IAAMvE,EAAS,0BAA0BZ,SAAC,kBAG/EP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACE,QAAQ,UAAUC,QApMLiB,KAC3BxF,EAAS,0BAA0B,EAmM2BZ,SAAC,+BAO7DK,EAAAA,EAAAA,MAAA,OAAKN,UAAU,gDAA+CC,SAAA,EAC3DP,EAAAA,EAAAA,KAAA,UACE0F,QAASA,IAAMzD,EAAY,UAC3B3B,UAAW,yFACM,WAAb0B,EACE,2BACA,6DACJzB,SACH,sBAGSP,EAAAA,EAAAA,KAAA,UACR0F,QAASA,IAAMzD,EAAY,QAC3B3B,UAAW,yFACM,SAAb0B,EACE,2BACA,6DACJzB,SACH,oBAMU,WAAbyB,IAA0BE,IAAYE,GAASR,EAAWoD,OAAS,IAClEhF,EAAAA,EAAAA,KAAA,OAAKM,UAAU,4BAA2BC,SACvCqB,EAAWqD,KAAIpF,IACdG,EAAAA,EAAAA,KAAA,UAEE0F,QAASA,IAAM3D,EAAoBlC,GACnCS,UAAW,4FACPwB,IAAqBjC,EACnB,2BACA,gEACJU,SAEDV,EAAS+G,OAAO,GAAGC,cAAgBhH,EAASiH,MAAM,IAR9CjH,QAebG,EAAAA,EAAAA,KAAA,OAAKM,UAAU,sEAAqEC,SACjFuE,KAIH9E,EAAAA,EAAAA,KAAC+G,EAAAA,EAAK,CACJC,OAAQ1E,EACR2E,QAASrC,EACTsC,MAAM,mBACNC,UAjPwBlE,UAC5B,GAAKT,EAEL,UACQa,EAAAA,GAA8B+D,eAAe5E,GAEnDO,IACA6B,GACF,CAAE,MAAOlB,GAAW,IAAD2D,EAAAC,EACjBpE,QAAQd,MAAM,2BAA4BsB,GAC1CrB,GAAqB,QAAZgF,EAAA3D,EAAIN,gBAAQ,IAAAiE,GAAM,QAANC,EAAZD,EAAc7D,YAAI,IAAA8D,OAAN,EAAZA,EAAoB7D,UAAWC,EAAID,SAAW,6BAEvDmB,GACF,GAqOI2C,YAAY,SACZC,eAAe,SAAQjH,UAEvBP,EAAAA,EAAAA,KAAA,KAAAO,SAAG,oFAILP,EAAAA,EAAAA,KAAC+G,EAAAA,EAAK,CACJC,OAAQpE,EACRqE,QAASpC,EACTqC,MAAM,wBACNC,UAvNoBlE,UACxB,GAAiC,IAA7BP,EAAkBsC,OAEtB,IACE7C,GAAW,GAEX,IAAK,MAAMwD,KAAMjD,QACTW,EAAAA,GAA8B+D,eAAezB,GAGrDhD,EAAqB,IACrBI,IACA8B,GACF,CAAE,MAAOnB,GAAW,IAAD+D,EAAAC,EACjBxE,QAAQd,MAAM,oCAAqCsB,GACnDrB,GAAqB,QAAZoF,EAAA/D,EAAIN,gBAAQ,IAAAqE,GAAM,QAANC,EAAZD,EAAcjE,YAAI,IAAAkE,OAAN,EAAZA,EAAoBjE,UAAWC,EAAID,SAAW,uCACvDoB,GACF,CAAC,QACC1C,GAAW,EACb,GAqMIoF,YAAY,kBACZC,eAAe,SAAQjH,UAEvBK,EAAAA,EAAAA,MAAA,KAAAL,SAAA,CAAG,mCAAiCmC,EAAkBsC,OAAO,8DAE3D,C,uFC5cH,MAAM+B,EAA8BY,IASpC,IATqC,OAC1CX,EAAM,QACNC,EAAO,MACPC,EAAK,SACL3G,EAAQ,UACR4G,EAAS,YACTI,EAAc,UAAS,eACvBC,EAAiB,UAAS,WAC1BI,EAAa,UACdD,EACC,OACE3H,EAAAA,EAAAA,KAAC6H,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMf,EAAQgB,GAAIC,EAAAA,SAAS1H,UAC5CK,EAAAA,EAAAA,MAACsH,EAAAA,GAAM,CAACF,GAAG,MAAM1H,UAAU,gBAAgB2G,QAASA,EAAQ1G,SAAA,EAC1DP,EAAAA,EAAAA,KAAC6H,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAWlI,UAEnBP,EAAAA,EAAAA,KAAA,OAAKM,UAAU,4CAGjBN,EAAAA,EAAAA,KAAA,OAAKM,UAAU,gCAA+BC,UAC5CP,EAAAA,EAAAA,KAAA,OAAKM,UAAU,8DAA6DC,UAC1EP,EAAAA,EAAAA,KAAC6H,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoBlI,UAE5BK,EAAAA,EAAAA,MAACsH,EAAAA,GAAOQ,MAAK,CAACpI,UAAU,uHAAsHC,SAAA,EAC5IP,EAAAA,EAAAA,KAACkI,EAAAA,GAAOS,MAAK,CACXX,GAAG,KACH1H,UAAU,gDAA+CC,SAExD2G,KAEHlH,EAAAA,EAAAA,KAAA,OAAKM,UAAU,6BAA4BC,SACxCA,KAGHK,EAAAA,EAAAA,MAAA,OAAKN,UAAU,kCAAiCC,SAAA,EAC9CP,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACE,QAAQ,YAAYC,QAASuB,EAAQ1G,SAC1CqH,IAEFT,IACCnH,EAAAA,EAAAA,KAACuF,EAAAA,EAAM,CAACE,QAAS+B,EAAgB9B,QAASyB,EAAU5G,SACjDgH,oBASN,C", "sources": ["pages/Templates.tsx", "components/Modal.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport { useNavigate } from 'react-router-dom';\n\nimport Button from '../components/Button';\n// import axios from 'axios'; // Remove axios if only using mock data\nimport Card from '../components/Card';\nimport { Modal } from '../components/Modal'; // Use named import\n// Import the service\nimport { templateRecommendationService } from '../services';\n\ninterface Template {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  thumbnail: string;\n  userId?: string;\n  isSystem: boolean;\n  createdAt: string;\n  performance?: {\n    opens: number;\n    clicks: number;\n    conversions: number;\n    usageCount: number;\n  };\n}\n\n// Type for categorized templates\ninterface CategorizedTemplates {\n  [key: string]: Template[];\n}\n\n// Helper function to get gradient class based on category\nconst getGradientForCategory = (category: string): string => {\n  switch (category.toLowerCase()) {\n    case 'newsletter':\n      return 'from-blue-500 to-indigo-600';\n    case 'promotion':\n      return 'from-purple-500 to-pink-600';\n    case 'welcome':\n      return 'from-green-400 to-teal-500';\n    case 'announcement':\n      return 'from-yellow-400 to-orange-500';\n    case 'e-commerce':\n      return 'from-red-500 to-rose-600';\n    default:\n      return 'from-gray-600 to-gray-700'; // Default fallback\n  }\n};\n\n// --- SVG Icons (can be moved to a separate file later) ---\nconst NewsletterIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z\" />\n  </svg>\n);\n\nconst PromotionIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z\" />\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6Z\" />\n  </svg>\n);\n\nconst WelcomeIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n  </svg>\n);\n\nconst AnnouncementIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535L19.5 17.11c.463-.304.646-.961.463-1.511a20.845 20.845 0 0 0-1.44-4.282m-3.102-.069a18.03 18.03 0 0 0-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18C9.36 16.33 8.5 17.644 8.5 19.5a2.25 2.25 0 0 0 4.5 0c0-1.09-.394-2.068-.998-2.818\" />\n  </svg>\n);\n\nconst EcommerceIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.106M15.75 5.106l-1.483 7.411M9.75 9.75l4.5 1.5m-4.5-1.5L12 9.75m-2.25 0L9.75 7.5M15 12l-2.25-1.5M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n  </svg>\n);\n\n// Default/Fallback Icon\nconst DefaultTemplateIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M11.35 3.836A1.5 1.5 0 0 1 12.183 3h9.634A1.5 1.5 0 0 1 23.317 4.183L19.817 7.683A1.5 1.5 0 0 1 19.183 8H2.817a1.5 1.5 0 0 1-1.317-.817L.683 4.183A1.5 1.5 0 0 1 1.817 3h9.534Z\" />\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2 8v11a1.5 1.5 0 0 0 1.5 1.5h17A1.5 1.5 0 0 0 22 19V8M8 12h8M8 16h4\" />\n  </svg>\n);\n\n// --- Helper function to get icon based on category ---\nconst getIconForCategory = (category: string): JSX.Element => {\n  switch (category.toLowerCase()) {\n    case 'newsletter':\n      return <NewsletterIcon />;\n    case 'promotion':\n    case 'promotional': // Added alias\n      return <PromotionIcon />;\n    case 'welcome':\n    case 'onboarding': // Added alias\n      return <WelcomeIcon />;\n    case 'announcement':\n      return <AnnouncementIcon />;\n    case 'e-commerce':\n      return <EcommerceIcon />;\n    case 'event':\n    case 'follow-up':\n    case 'transactional':\n    case 'marketing': // Catch-all for other generated types\n    default:\n      return <DefaultTemplateIcon />;\n  }\n};\n\ninterface TemplatesPageProps {}\n\nconst TemplatesPage: React.FC<TemplatesPageProps> = () => {\n  const navigate = useNavigate();\n\n  // --- Updated State ---\n  const [allTemplates, setAllTemplates] = useState<Template[]>([]); // Store all fetched templates\n  const [categorizedTemplates, setCategorizedTemplates] = useState<CategorizedTemplates>({});\n  const [userTemplates, setUserTemplates] = useState<Template[]>([]);\n  const [categories, setCategories] = useState<string[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [viewMode, setViewMode] = useState<'system' | 'user'>('system'); // 'system' or 'user'\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  // State for delete confirmation modal\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\n  const [templateToDeleteId, setTemplateToDeleteId] = useState<string | null>(null);\n  // State for bulk delete\n  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);\n  const [isMultiDeleteModalOpen, setIsMultiDeleteModalOpen] = useState(false);\n\n  useEffect(() => {\n    fetchAllTemplates();\n  }, []);\n\n  // Process templates whenever allTemplates or viewMode changes\n  useEffect(() => {\n    processTemplates();\n  }, [allTemplates, viewMode]);\n\n  const fetchAllTemplates = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Fetching all user and system templates...\");\n      // Use getAllTemplates which fetches user's + system\n      const response = await templateRecommendationService.getAllTemplates();\n\n      if (response.success && response.data) {\n        setAllTemplates(response.data); // Store the combined list\n      } else {\n        setError(response.message || 'Failed to fetch templates');\n        setAllTemplates([]); // Clear on error\n      }\n    } catch (err: any) {\n      console.error('Error fetching all templates:', err);\n      const message = err.response?.data?.message || err.message || 'An unexpected error occurred';\n      setError(message);\n      setAllTemplates([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Separate function to process templates based on viewMode\n  const processTemplates = () => {\n    if (viewMode === 'system') {\n      const systemTemplates = allTemplates.filter(t => t.isSystem);\n      const categorized = systemTemplates.reduce((acc: CategorizedTemplates, template) => {\n        const category = template.category || 'other';\n        if (!acc[category]) {\n          acc[category] = [];\n        }\n        acc[category].push(template);\n        return acc;\n      }, {});\n      setCategorizedTemplates(categorized);\n      const fetchedCategories = Object.keys(categorized);\n      setCategories(fetchedCategories);\n      // Set selected category only if it's not already set or valid\n      if (!selectedCategory || !fetchedCategories.includes(selectedCategory)) {\n         setSelectedCategory(fetchedCategories[0] || '');\n      }\n      setUserTemplates([]); // Clear user templates\n    } else { // viewMode === 'user'\n      // Filter for user-owned templates (isSystem is false)\n      // Note: Backend's getUserTemplates already filters by userId, but double-checking isSystem is safe.\n      const userOwnedTemplates = allTemplates.filter(t => !t.isSystem);\n      setUserTemplates(userOwnedTemplates);\n      setCategorizedTemplates({}); // Clear system templates\n      setCategories([]);\n      setSelectedCategory('');\n    }\n  };\n\n  // --- Keep Navigation Handlers ---\n  const handleUseTemplate = (templateId: string) => {\n    navigate(`/email-templates/editor/${templateId}`);\n  };\n  const handleCreateTemplate = () => {\n    navigate('/email-templates/create');\n  };\n  const handleEditTemplate = (templateId: string) => {\n    navigate(`/email-templates/editor/${templateId}`);\n  };\n\n  // Handler for deleting a template - Opens the modal\n  const handleDeleteTemplate = (templateId: string) => {\n    setTemplateToDeleteId(templateId);\n    setIsDeleteModalOpen(true);\n  };\n\n  // Function to actually perform the deletion (called from modal confirm)\n  const confirmDeleteTemplate = async () => {\n    if (!templateToDeleteId) return; // Should not happen, but safeguard\n\n    try {\n      await templateRecommendationService.deleteTemplate(templateToDeleteId);\n      // Refresh templates after deletion\n      fetchAllTemplates();\n      closeDeleteModal(); // Close modal on success\n    } catch (err: any) {\n      console.error('Error deleting template:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to delete template');\n      // Optionally keep modal open on error or display error within modal/toast\n      closeDeleteModal(); // Close modal even on error for now\n    }\n  };\n\n  // Function to close the modal\n  const closeDeleteModal = () => {\n    setIsDeleteModalOpen(false);\n    setTemplateToDeleteId(null);\n  };\n\n  // Function to handle checkbox selection\n  const handleTemplateSelect = (templateId: string, isSelected: boolean) => {\n    setSelectedTemplates(prev => \n      isSelected \n        ? [...prev, templateId] \n        : prev.filter(id => id !== templateId)\n    );\n  };\n\n  // Function to handle bulk delete\n  const handleBulkDelete = () => {\n    if (selectedTemplates.length === 0) return;\n    setIsMultiDeleteModalOpen(true);\n  };\n\n  // Function to confirm bulk delete\n  const confirmBulkDelete = async () => {\n    if (selectedTemplates.length === 0) return;\n    \n    try {\n      setLoading(true);\n      // Process delete operations in sequence\n      for (const id of selectedTemplates) {\n        await templateRecommendationService.deleteTemplate(id);\n      }\n      // Clear selection and refresh templates\n      setSelectedTemplates([]);\n      fetchAllTemplates();\n      closeMultiDeleteModal();\n    } catch (err: any) {\n      console.error('Error deleting templates in bulk:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to delete selected templates');\n      closeMultiDeleteModal();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to close multi-delete modal\n  const closeMultiDeleteModal = () => {\n    setIsMultiDeleteModalOpen(false);\n  };\n\n  // Function to select all user templates\n  const selectAllTemplates = () => {\n    const ids = userTemplates.map(template => template.id);\n    setSelectedTemplates(ids);\n  };\n\n  // Function to deselect all templates\n  const deselectAllTemplates = () => {\n    setSelectedTemplates([]);\n  };\n\n  // --- Render Logic ---\n  let contentToRender;\n  if (loading) {\n    contentToRender = <p className=\"text-white col-span-full text-center py-10\">Loading templates...</p>;\n  } else if (error) {\n    contentToRender = <p className=\"text-red-500 col-span-full text-center py-10\">Error: {error}</p>;\n  } else if (viewMode === 'system') {\n      if (categories.length === 0) {\n          contentToRender = <p className=\"text-white col-span-full text-center py-10\">No system template categories found.</p>;\n      } else if (selectedCategory && categorizedTemplates[selectedCategory]?.length > 0) {\n          const templatesToDisplay = categorizedTemplates[selectedCategory];\n          contentToRender = templatesToDisplay.map((template) => {\n             // ... Same Card rendering logic as before ...\n            const gradientClass = getGradientForCategory(template.category);\n            const CategoryIcon = getIconForCategory(template.category);\n            // System templates are never directly editable (copy-on-edit happens)\n            const canEdit = false;\n            return (\n              <Card key={template.id} className=\"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col\">\n                <div className={`relative w-full h-48 bg-gradient-to-br ${gradientClass} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`}>\n                  <div className=\"mb-2\">{CategoryIcon}</div>\n                  <span className=\"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md\">{template.name}</span>\n                </div>\n                <div className=\"p-4 flex flex-col flex-grow\">\n                  <p className=\"text-gray-400 text-sm mb-4 flex-grow\">{template.description || 'No description available.'}</p>\n                  <div className=\"mt-auto flex gap-2 justify-end pt-2\">\n                    <Button size=\"sm\" variant=\"secondary\" onClick={() => handleUseTemplate(template.id)}>Use Template</Button>\n                    {/* Edit button hidden for system templates in this view */}\n                  </div>\n                </div>\n              </Card>\n            );\n          });\n      } else {\n          contentToRender = <p className=\"text-white col-span-full text-center py-10\">No templates found in the '{selectedCategory}' category.</p>;\n      }\n  } else { // viewMode === 'user'\n      if (userTemplates.length === 0) {\n          contentToRender = <p className=\"text-white col-span-full text-center py-10\">You haven't created or saved any templates yet.</p>;\n      } else {\n          contentToRender = userTemplates.map((template) => {\n            // User templates ARE editable\n            const canEdit = true;\n            const gradientClass = getGradientForCategory(template.category);\n            const CategoryIcon = getIconForCategory(template.category);\n            const isSelected = selectedTemplates.includes(template.id);\n            \n            return (\n              <Card key={template.id} className=\"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col\">\n                <div className={`relative w-full h-48 bg-gradient-to-br ${gradientClass} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`}>\n                  {/* Add checkbox for selection in user templates view */}\n                  <div className=\"absolute top-2 left-2 z-20\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={(e) => handleTemplateSelect(template.id, e.target.checked)}\n                      className=\"h-5 w-5 rounded border-gray-500 text-indigo-600 focus:ring-indigo-500\"\n                    />\n                  </div>\n                  <div className=\"mb-2\">{CategoryIcon}</div>\n                  <span className=\"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md\">{template.name}</span>\n                </div>\n                <div className=\"p-4 flex flex-col flex-grow\">\n                  <p className=\"text-gray-400 text-sm mb-4 flex-grow\">{template.description || 'No description available.'}</p>\n                  <div className=\"mt-auto flex gap-2 justify-end pt-2 w-full\">\n                    <Button size=\"sm\" variant=\"secondary\" onClick={() => handleUseTemplate(template.id)}>Use Template</Button>\n                    {canEdit && (\n                      <>\n                        <Button size=\"sm\" variant=\"secondary\" onClick={() => handleEditTemplate(template.id)}>Edit</Button>\n                        <Button size=\"sm\" variant=\"danger\" onClick={() => handleDeleteTemplate(template.id)}>Delete</Button>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </Card>\n            );\n          });\n      }\n  }\n\n  return (\n    <div className=\"p-6 bg-gray-900 min-h-screen text-white\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-semibold\">Email Templates</h1>\n        <div className=\"flex gap-2\">\n          {viewMode === 'user' && (\n            <>\n              {selectedTemplates.length > 0 ? (\n                <>\n                  <Button variant=\"secondary\" onClick={deselectAllTemplates}>\n                    Deselect All\n                  </Button>\n                  <Button variant=\"danger\" onClick={handleBulkDelete}>\n                    Delete Selected ({selectedTemplates.length})\n                  </Button>\n                </>\n              ) : (\n                <Button variant=\"secondary\" onClick={selectAllTemplates}>\n                  Select All\n                </Button>\n              )}\n            </>\n          )}\n          <Button variant=\"secondary\" onClick={() => navigate('/ai-template-generator')}>\n            AI Generator\n          </Button>\n          <Button variant=\"primary\" onClick={handleCreateTemplate}>\n            Create New Template\n          </Button>\n        </div>\n      </div>\n\n      {/* View Mode Selector */}\n      <div className=\"flex gap-4 mb-6 border-b border-gray-700 pb-4\">\n         <button\n           onClick={() => setViewMode('system')}\n           className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${viewMode === 'system'\n               ? 'bg-indigo-600 text-white'\n               : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}\n           `}\n         >\n           System Templates\n         </button>\n                   <button\n           onClick={() => setViewMode('user')}\n           className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${viewMode === 'user'\n               ? 'bg-indigo-600 text-white'\n               : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}\n           `}\n         >\n           My Templates\n                    </button>\n                </div>\n\n      {/* Category Selector (Only show for System view) */}\n      {viewMode === 'system' && !loading && !error && categories.length > 0 && (\n        <div className=\"flex flex-wrap gap-2 mb-6\">\n          {categories.map(category => (\n            <button\n              key={category}\n              onClick={() => setSelectedCategory(category)}\n              className={`px-3 py-1 rounded-md text-xs font-medium transition-colors duration-150\n                ${selectedCategory === category\n                  ? 'bg-indigo-500 text-white'\n                  : 'bg-gray-600 text-gray-200 hover:bg-gray-500'}\n              `}\n            >\n              {category.charAt(0).toUpperCase() + category.slice(1)}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Template Grid */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n        {contentToRender}\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      <Modal\n        isOpen={isDeleteModalOpen}\n        onClose={closeDeleteModal}\n        title=\"Confirm Deletion\"\n        onConfirm={confirmDeleteTemplate}\n        confirmText=\"Delete\"\n        confirmVariant=\"danger\"\n      >\n        <p>Are you sure you want to delete this template? This action cannot be undone.</p>\n      </Modal>\n\n      {/* Bulk Delete Confirmation Modal */}\n      <Modal\n        isOpen={isMultiDeleteModalOpen}\n        onClose={closeMultiDeleteModal}\n        title=\"Confirm Bulk Deletion\"\n        onConfirm={confirmBulkDelete}\n        confirmText=\"Delete Selected\"\n        confirmVariant=\"danger\"\n      >\n        <p>Are you sure you want to delete {selectedTemplates.length} selected template(s)? This action cannot be undone.</p>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TemplatesPage;\n", "import React, { Fragment } from 'react';\r\n\r\nimport {\r\n  Dialog,\r\n  Transition,\r\n} from '@headlessui/react';\r\n\r\nimport Button from './Button'; // Assuming Button component exists\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: React.ReactNode;\r\n  onConfirm?: () => void;\r\n  confirmText?: string;\r\n  confirmVariant?: 'primary' | 'secondary' | 'danger';\r\n  cancelText?: string;\r\n}\r\n\r\nexport const Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  onConfirm,\r\n  confirmText = 'Confirm',\r\n  confirmVariant = 'primary',\r\n  cancelText = 'Cancel',\r\n}) => {\r\n  return (\r\n    <Transition appear show={isOpen} as={Fragment}>\r\n      <Dialog as=\"div\" className=\"relative z-10\" onClose={onClose}>\r\n        <Transition.Child\r\n          as={Fragment}\r\n          enter=\"ease-out duration-300\"\r\n          enterFrom=\"opacity-0\"\r\n          enterTo=\"opacity-100\"\r\n          leave=\"ease-in duration-200\"\r\n          leaveFrom=\"opacity-100\"\r\n          leaveTo=\"opacity-0\"\r\n        >\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" />\r\n        </Transition.Child>\r\n\r\n        <div className=\"fixed inset-0 overflow-y-auto\">\r\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n            <Transition.Child\r\n              as={Fragment}\r\n              enter=\"ease-out duration-300\"\r\n              enterFrom=\"opacity-0 scale-95\"\r\n              enterTo=\"opacity-100 scale-100\"\r\n              leave=\"ease-in duration-200\"\r\n              leaveFrom=\"opacity-100 scale-100\"\r\n              leaveTo=\"opacity-0 scale-95\"\r\n            >\r\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all\">\r\n                <Dialog.Title\r\n                  as=\"h3\"\r\n                  className=\"text-lg font-medium leading-6 text-white mb-4\"\r\n                >\r\n                  {title}\r\n                </Dialog.Title>\r\n                <div className=\"mt-2 text-sm text-gray-300\">\r\n                  {children}\r\n                </div>\r\n\r\n                <div className=\"mt-6 flex justify-end space-x-3\">\r\n                  <Button variant=\"secondary\" onClick={onClose}>\r\n                    {cancelText}\r\n                  </Button>\r\n                  {onConfirm && (\r\n                    <Button variant={confirmVariant} onClick={onConfirm}>\r\n                      {confirmText}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </Dialog.Panel>\r\n            </Transition.Child>\r\n          </div>\r\n        </div>\r\n      </Dialog>\r\n    </Transition>\r\n  );\r\n}; "], "names": ["getGradientForCategory", "category", "toLowerCase", "NewsletterIcon", "_jsx", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "className", "children", "strokeLinecap", "strokeLinejoin", "d", "PromotionIcon", "_jsxs", "WelcomeIcon", "AnnouncementIcon", "EcommerceIcon", "DefaultTemplateIcon", "getIconForCategory", "TemplatesPage", "navigate", "useNavigate", "allTemplates", "setAllTemplates", "useState", "categorizedTemplates", "setCategorizedTemplates", "userTemplates", "setUserTemplates", "categories", "setCategories", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "viewMode", "setViewMode", "loading", "setLoading", "error", "setError", "isDeleteModalOpen", "setIsDeleteModalOpen", "templateToDeleteId", "setTemplateToDeleteId", "selectedTemplates", "setSelectedTemplates", "isMultiDeleteModalOpen", "setIsMultiDeleteModalOpen", "useEffect", "fetchAllTemplates", "processTemplates", "async", "console", "log", "response", "templateRecommendationService", "getAllTemplates", "success", "data", "message", "err", "_err$response", "_err$response$data", "categorized", "filter", "t", "isSystem", "reduce", "acc", "template", "push", "fetchedCategories", "Object", "keys", "includes", "userOwnedTemplates", "handleUseTemplate", "templateId", "closeDeleteModal", "closeMultiDeleteModal", "contentToRender", "_categorizedTemplates", "length", "map", "gradientClass", "CategoryIcon", "Card", "name", "description", "<PERSON><PERSON>", "size", "variant", "onClick", "id", "isSelected", "type", "checked", "onChange", "e", "handleTemplateSelect", "prev", "target", "_Fragment", "handleEditTemplate", "handleDeleteTemplate", "deselectAllTemplates", "handleBulkDelete", "selectAllTemplates", "ids", "handleCreateTemplate", "char<PERSON>t", "toUpperCase", "slice", "Modal", "isOpen", "onClose", "title", "onConfirm", "deleteTemplate", "_err$response2", "_err$response2$data", "confirmText", "confirmVariant", "_err$response3", "_err$response3$data", "_ref", "cancelText", "Transition", "appear", "show", "as", "Fragment", "Dialog", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Panel", "Title"], "sourceRoot": ""}