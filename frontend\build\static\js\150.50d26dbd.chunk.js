"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[150],{7150:(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});s(5043);var r=s(8231),o=s(579);const a=()=>(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[(0,o.jsx)("h1",{className:"text-6xl font-bold text-primary mb-4",children:"404"}),(0,o.jsx)("h2",{className:"text-2xl font-semibold text-text-primary mb-4",children:"Page Not Found"}),(0,o.jsx)("p",{className:"text-text-secondary mb-8",children:"Oops! The page you are looking for does not exist. It might have been moved or deleted."}),(0,o.jsx)(r.N_,{to:"/",className:"px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors",children:"Go Back Home"})]})}}]);
//# sourceMappingURL=150.50d26dbd.chunk.js.map