{"ast": null, "code": "export var HandlerRole;\n(function (HandlerRole) {\n  HandlerRole[\"SOURCE\"] = \"SOURCE\";\n  HandlerRole[\"TARGET\"] = \"TARGET\";\n})(HandlerRole || (HandlerRole = {}));", "map": {"version": 3, "names": ["HandlerRole"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\dnd-core\\src\\interfaces.ts"], "sourcesContent": ["export type Identifier = string | symbol\nexport type SourceType = Identifier\nexport type TargetType = Identifier | Identifier[]\nexport type Unsubscribe = () => void\nexport type Listener = () => void\n\nexport interface XYCoord {\n\tx: number\n\ty: number\n}\n\nexport enum HandlerRole {\n\tSOURCE = 'SOURCE',\n\tTARGET = 'TARGET',\n}\n\nexport interface Backend {\n\tsetup(): void\n\tteardown(): void\n\tconnectDragSource(sourceId: any, node?: any, options?: any): Unsubscribe\n\tconnectDragPreview(sourceId: any, node?: any, options?: any): Unsubscribe\n\tconnectDropTarget(targetId: any, node?: any, options?: any): Unsubscribe\n\tprofile(): Record<string, number>\n}\n\nexport interface DragDropMonitor {\n\tsubscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: {\n\t\t\thandlerIds?: Identifier[]\n\t\t},\n\t): Unsubscribe\n\tsubscribeToOffsetChange(listener: Listener): Unsubscribe\n\tcanDragSource(sourceId: Identifier | undefined): boolean\n\tcanDropOnTarget(targetId: Identifier | undefined): boolean\n\n\t/**\n\t * Returns true if a drag operation is in progress, and either the owner initiated the drag, or its isDragging()\n\t * is defined and returns true.\n\t */\n\tisDragging(): boolean\n\tisDraggingSource(sourceId: Identifier | undefined): boolean\n\tisOverTarget(\n\t\ttargetId: Identifier | undefined,\n\t\toptions?: {\n\t\t\tshallow?: boolean\n\t\t},\n\t): boolean\n\n\t/**\n\t * Returns a string or a symbol identifying the type of the current dragged item. Returns null if no item is being dragged.\n\t */\n\tgetItemType(): Identifier | null\n\n\t/**\n\t * Returns a plain object representing the currently dragged item. Every drag source must specify it by returning an object\n\t * from its beginDrag() method. Returns null if no item is being dragged.\n\t */\n\tgetItem(): any\n\tgetSourceId(): Identifier | null\n\tgetTargetIds(): Identifier[]\n\t/**\n\t * Returns a plain object representing the last recorded drop result. The drop targets may optionally specify it by returning an\n\t * object from their drop() methods. When a chain of drop() is dispatched for the nested targets, bottom up, any parent that\n\t * explicitly returns its own result from drop() overrides the child drop result previously set by the child. Returns null if\n\t * called outside endDrag().\n\t */\n\tgetDropResult(): any\n\t/**\n\t * Returns true if some drop target has handled the drop event, false otherwise. Even if a target did not return a drop result,\n\t * didDrop() returns true. Use it inside endDrag() to test whether any drop target has handled the drop. Returns false if called\n\t * outside endDrag().\n\t */\n\tdidDrop(): boolean\n\tisSourcePublic(): boolean | null\n\t/**\n\t * Returns the { x, y } client offset of the pointer at the time when the current drag operation has started.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetInitialClientOffset(): XYCoord | null\n\t/**\n\t * Returns the { x, y } client offset of the drag source component's root DOM node at the time when the current drag\n\t * operation has started. Returns null if no item is being dragged.\n\t */\n\tgetInitialSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the last recorded { x, y } client offset of the pointer while a drag operation is in progress.\n\t * Returns null if no item is being dragged.\n\t */\n\tgetClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the projected { x, y } client offset of the drag source component's root DOM node, based on its position at the time\n\t * when the current drag operation has started, and the movement difference. Returns null if no item is being dragged.\n\t */\n\tgetSourceClientOffset(): XYCoord | null\n\n\t/**\n\t * Returns the { x, y } difference between the last recorded client offset of the pointer and the client offset when the current\n\t * drag operation has started. Returns null if no item is being dragged.\n\t */\n\tgetDifferenceFromInitialOffset(): XYCoord | null\n}\n\nexport interface HandlerRegistry {\n\taddSource(type: SourceType, source: DragSource): Identifier\n\taddTarget(type: TargetType, target: DropTarget): Identifier\n\tcontainsHandler(handler: DragSource | DropTarget): boolean\n\tgetSource(sourceId: Identifier, includePinned?: boolean): DragSource\n\tgetSourceType(sourceId: Identifier): SourceType\n\tgetTargetType(targetId: Identifier): TargetType\n\tgetTarget(targetId: Identifier): DropTarget\n\tisSourceId(handlerId: Identifier): boolean\n\tisTargetId(handlerId: Identifier): boolean\n\tremoveSource(sourceId: Identifier): void\n\tremoveTarget(targetId: Identifier): void\n\tpinSource(sourceId: Identifier): void\n\tunpinSource(): void\n}\n\nexport interface Action<Payload> {\n\ttype: Identifier\n\tpayload: Payload\n}\nexport interface SentinelAction {\n\ttype: Identifier\n}\n\nexport type ActionCreator<Payload> = (args: any[]) => Action<Payload>\n\nexport interface BeginDragOptions {\n\tpublishSource?: boolean\n\tclientOffset?: XYCoord\n\tgetSourceClientOffset?: (sourceId: Identifier | undefined) => XYCoord\n}\n\nexport interface InitCoordsPayload {\n\tclientOffset: XYCoord | null\n\tsourceClientOffset: XYCoord | null\n}\n\nexport interface BeginDragPayload {\n\titemType: Identifier\n\titem: any\n\tsourceId: Identifier\n\tclientOffset: XYCoord | null\n\tsourceClientOffset: XYCoord | null\n\tisSourcePublic: boolean\n}\n\nexport interface HoverPayload {\n\ttargetIds: Identifier[]\n\tclientOffset: XYCoord | null\n}\n\nexport interface HoverOptions {\n\tclientOffset?: XYCoord\n}\n\nexport interface DropPayload {\n\tdropResult: any\n}\n\nexport interface TargetIdPayload {\n\ttargetId: Identifier\n}\n\nexport interface SourceIdPayload {\n\tsourceId: Identifier\n}\n\nexport interface DragDropActions {\n\tbeginDrag(\n\t\tsourceIds?: Identifier[],\n\t\toptions?: any,\n\t): Action<BeginDragPayload> | undefined\n\tpublishDragSource(): SentinelAction | undefined\n\thover(targetIds: Identifier[], options?: any): Action<HoverPayload>\n\tdrop(options?: any): void\n\tendDrag(): SentinelAction\n}\n\nexport interface DragDropManager {\n\tgetMonitor(): DragDropMonitor\n\tgetBackend(): Backend\n\tgetRegistry(): HandlerRegistry\n\tgetActions(): DragDropActions\n\tdispatch(action: any): void\n}\n\nexport type BackendFactory = (\n\tmanager: DragDropManager,\n\tglobalContext?: any,\n\tconfiguration?: any,\n) => Backend\n\nexport interface DragSource {\n\tbeginDrag(monitor: DragDropMonitor, targetId: Identifier): void\n\tendDrag(monitor: DragDropMonitor, targetId: Identifier): void\n\tcanDrag(monitor: DragDropMonitor, targetId: Identifier): boolean\n\tisDragging(monitor: DragDropMonitor, targetId: Identifier): boolean\n}\n\nexport interface DropTarget {\n\tcanDrop(monitor: DragDropMonitor, targetId: Identifier): boolean\n\thover(monitor: DragDropMonitor, targetId: Identifier): void\n\tdrop(monitor: DragDropMonitor, targetId: Identifier): any\n}\n"], "mappings": "AAAA,WAWOA,WAGN;WAHWA,WAAW;EAAXA,WAAW,CACtB,QAAM,IAAN,QAAM;EADKA,WAAW,CAEtB,QAAM,IAAN,QAAM;GAFKA,WAAW,KAAXA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}