/**
 * AI Template Routes for Driftly
 * Handles API endpoints for AI-powered template generation
 */

import express, {
  NextFunction,
  Request,
  Response,
} from 'express';
import fs from 'fs'; // <-- Import fs module
import mjml2html from 'mjml'; // Ensure mjml library is imported
import path from 'path'; // <-- Import path module

import { authenticate } from '../middleware/auth.middleware';
import Template from '../models/template.model';
import UserPreference, { IUserPreference } from '../models/UserPreference';
import aiTemplateService from '../services/ai-template.service';
import { createError } from '../utils/error.util';

const router = express.Router();

// Map AI service block types to system blockIds
const blockTypeMap: Record<string, string> = {
  // Header types
  'simple_logo_nav': 'header/simple-nav',
  
  // Content types
  'headline_supporting_text': 'content/headline',
  'two_column_text_image': 'layout/two-column',
  'three_column_layout': 'layout/three-column',
  'hero_section': 'layout/hero',
  'text_block': 'content/text',
  'image_block': 'content/image',
  'feature_block': 'content/feature',
  'quote_testimonial': 'content/testimonial',
  
  // CTA types
  'full_width_cta': 'cta/button',
  'button_cta': 'cta/button',
  
  // Product types
  'product_single': 'product/card',
  'product_grid': 'product/grid',
  
  // Misc types
  'spacer': 'misc/spacer',
  'divider': 'misc/divider',
  
  // Footer types
  'standard_footer': 'footer/standard'
};

// Helper function to safely get brand colors with defaults
function getBrandColors(prefs: IUserPreference | null): { primary: string, secondary: string, accent: string, background: string, text: string } {
    const defaults = { primary: '#4F46E5', secondary: '#10B981', accent: '#F59E0B', background: '#FFFFFF', text: '#333333' };
    if (!prefs || !prefs.brandColors) return defaults;
    return {
        primary: prefs.brandColors.primary || defaults.primary,
        secondary: prefs.brandColors.secondary || defaults.secondary,
        accent: prefs.brandColors.accent || defaults.accent,
        background: prefs.brandColors.background || defaults.background,
        text: prefs.brandColors.text || defaults.text,
    };
}

// Top-tier MJML generation function
function generateMjmlForBlock(block: any, brandColors: ReturnType<typeof getBrandColors>): string {
  const content = block.content || {};
  const blockId = block.blockId || '';
  let blockMjml = '';

  // Use mj-wrapper for consistent section background and padding control
  // Use distinct padding for different block types if needed, or keep consistent
  blockMjml += `<mj-wrapper background-color="${blockId.startsWith('footer/') ? '#f1f1f1' : brandColors.background}" padding="15px 5px">`;

  switch (blockId) {
    case 'header/centered-logo':
        blockMjml += '<mj-section padding="10px 0">';
        blockMjml += '<mj-column>';
        blockMjml += `<mj-image width="180px" src="${content.logoUrl || 'https://via.placeholder.com/180x50?text=Logo'}" alt="${content.logoAlt || 'Company Logo'}" align="center"></mj-image>`;
        blockMjml += '</mj-column>';
        blockMjml += '</mj-section>';
        break;
    case 'header/simple-nav':
      blockMjml += '<mj-section padding="10px 25px">';
      blockMjml += '<mj-group vertical-align="middle">';
      blockMjml += '<mj-column width="40%" padding="0">';
      if (content.logoUrl) blockMjml += `<mj-image width="140px" align="left" src="${content.logoUrl}" alt="${content.logoAlt || 'Logo'}" padding="0"></mj-image>`;
      blockMjml += '</mj-column>';
      blockMjml += '<mj-column width="60%" padding="0">';
      // Dynamically generate nav links
      if (content.nav_links && Array.isArray(content.nav_links)) {
        blockMjml += '<mj-navbar base-url="https://example.com" hamburger="hamburger" ico-color="${brandColors.primary}">';
        content.nav_links.forEach((link: {name?: string, linkText?: string, url?: string, linkUrl?: string}) => {
          const text = link.name || link.linkText;
          const url = link.url || link.linkUrl;
          if (text && url) {
            blockMjml += `<mj-navbar-link href="${url}" color="${brandColors.primary}" font-weight="bold">${text}</mj-navbar-link>`;
          }
        });
        blockMjml += '</mj-navbar>';
      } else {
         blockMjml += '<mj-text align="right">Navigation</mj-text>'; // Fallback
      }
      blockMjml += '</mj-column>';
      blockMjml += '</mj-group>';
      blockMjml += '</mj-section>';
      break;

    case 'layout/hero':
      blockMjml += `<mj-hero mode="fluid-height" background-height="400px" background-url="${content.heroImageUrl || 'https://via.placeholder.com/700x400?text=Hero+Image'}" background-color="#2a3448" padding="50px 20px">`;
      if (content.heroHeadline) blockMjml += `<mj-text align="center" color="#ffffff" font-size="38px" font-weight="bold" line-height="46px" padding-bottom="10px">${content.heroHeadline}</mj-text>`;
      if (content.heroSubtext) blockMjml += `<mj-text align="center" color="#f0f0f0" font-size="18px" line-height="1.5" padding-top="5px">${content.heroSubtext}</mj-text>`;
      if (content.heroButtonText) blockMjml += `<mj-button href="${content.heroButtonUrl || '#'}" background-color="${brandColors.primary}" color="#ffffff" font-size="16px" font-weight="bold" border-radius="5px" padding="25px 0 0 0" inner-padding="15px 30px">${content.heroButtonText}</mj-button>`;
      blockMjml += `</mj-hero>`;
      break;

    case 'content/headline':
      blockMjml += `<mj-section padding="15px 0 5px 0"><mj-column><mj-text align="center" font-size="30px" font-weight="bold" color="${brandColors.text}" padding-bottom="5px">${content.headline || 'Headline'}</mj-text></mj-column></mj-section>`;
      break;

    case 'content/text':
      blockMjml += `<mj-section padding="5px 20px 15px 20px"><mj-column><mj-text align="left" color="${brandColors.text}" line-height="1.7" font-size="16px">${content.text || content.body || 'Text content goes here. Provide specific details and value to the reader.'}</mj-text></mj-column></mj-section>`;
      break;

    case 'content/image':
      blockMjml += `<mj-section padding="10px 0"><mj-column><mj-image css-class="smooth-image" src="${content.imageUrl || content.image_url || 'https://via.placeholder.com/600x300?text=Image'}" alt="${content.imageAlt || content.image_alt || 'Descriptive Alt Text'}" padding="0" fluid-on-mobile="true" /></mj-column></mj-section>`;
      break;

    case 'layout/two-column':
      blockMjml += '<mj-section padding="10px 0">'; // Add section padding
      blockMjml += '<mj-column vertical-align="top" padding="0 5px">'; // Add padding between cols
      if (content.column1_imageUrl) blockMjml += `<mj-image css-class="smooth-image" src="${content.column1_imageUrl}" alt="${content.column1_imageAlt || 'Image 1'}" padding-bottom="15px" fluid-on-mobile="true" />`;
      if (content.column1_headline) blockMjml += `<mj-text font-size="18px" font-weight="bold" color="${brandColors.text}" padding-bottom="5px">${content.column1_headline}</mj-text>`;
      if (content.column1_text) blockMjml += `<mj-text color="${brandColors.text}" line-height="1.6">${content.column1_text}</mj-text>`;
      blockMjml += '</mj-column>';
      blockMjml += '<mj-column vertical-align="top" padding="0 5px">';
      if (content.column2_imageUrl) blockMjml += `<mj-image css-class="smooth-image" src="${content.column2_imageUrl}" alt="${content.column2_imageAlt || 'Image 2'}" padding-bottom="15px" fluid-on-mobile="true" />`;
      if (content.column2_headline) blockMjml += `<mj-text font-size="18px" font-weight="bold" color="${brandColors.text}" padding-bottom="5px">${content.column2_headline}</mj-text>`;
      if (content.column2_text) blockMjml += `<mj-text color="${brandColors.text}" line-height="1.6">${content.column2_text}</mj-text>`;
      blockMjml += '</mj-column>';
      blockMjml += '</mj-section>';
      break;

     case 'layout/three-column':
      blockMjml += '<mj-section text-align="center" padding="10px 0">';
      ['1', '2', '3'].forEach(num => {
         blockMjml += '<mj-column vertical-align="top" padding="0 5px">';
         if (content[`col${num}_imageUrl`]) blockMjml += `<mj-image css-class="smooth-image" width="180px" src="${content[`col${num}_imageUrl`]}" alt="${content[`col${num}_imageAlt`] || `Image ${num}`}" padding-bottom="10px" />`;
         if (content[`col${num}_headline`]) blockMjml += `<mj-text font-weight="bold" font-size="16px" color="${brandColors.text}" padding-bottom="5px">${content[`col${num}_headline`]}</mj-text>`;
         if (content[`col${num}_text`]) blockMjml += `<mj-text font-size="14px" line-height="1.5" color="${brandColors.text}">${content[`col${num}_text`]}</mj-text>`;
         if (content[`col${num}_buttonText`]) blockMjml += `<mj-button href="${content[`col${num}_buttonUrl`] || '#'}" background-color="${brandColors.secondary}" color="#ffffff" font-size="14px" border-radius="4px" padding="15px 0 0 0">${content[`col${num}_buttonText`]}</mj-button>`;
         blockMjml += '</mj-column>';
      });
      blockMjml += '</mj-section>';
      break;

    case 'cta/button':
      blockMjml += `<mj-section padding="15px 0 20px 0"><mj-column><mj-button href="${content.buttonUrl || content.cta_url || '#'}" background-color="${brandColors.primary}" color="#ffffff" font-size="18px" font-weight="bold" border-radius="5px" padding="15px 35px" inner-padding="18px 35px">${content.buttonText || content.cta_text || 'Click Here'}</mj-button></mj-column></mj-section>`;
      break;

    case 'content/testimonial':
        blockMjml += `<mj-section background-color="#f8f8f8" border-radius="8px" padding="25px">`;
        blockMjml += '<mj-column width="20%" vertical-align="middle">';
        blockMjml += `<mj-image width="80px" border-radius="50%" src="${content.avatarUrl || 'https://via.placeholder.com/80x80?text=Avatar'}" alt="${content.avatarAlt || 'Avatar'}" padding="0"></mj-image>`;
        blockMjml += '</mj-column>';
        blockMjml += '<mj-column width="80%" vertical-align="middle">';
        blockMjml += `<mj-text font-style="italic" font-size="16px" color="#444444" padding-bottom="10px">"${content.quote || 'This product is amazing! Customer service was excellent.'}"</mj-text>`;
        blockMjml += `<mj-text align="left" font-weight="bold" color="${brandColors.text}">- ${content.authorName || 'Satisfied Customer'}${content.authorTitle ? ', ' + content.authorTitle : ''}</mj-text>`;
        blockMjml += '</mj-column>';
        blockMjml += `</mj-section>`;
        break;

    case 'footer/standard':
      // Use mj-wrapper for footer background
      blockMjml = `<mj-wrapper background-color="#f1f1f1" padding="30px 10px 20px 10px" border-top="1px solid #dddddd">`;
      blockMjml += '<mj-section>';
      blockMjml += '<mj-column>';
      if (content.companyName) blockMjml += `<mj-text align="center" font-size="14px" font-weight="bold" color="#555555" padding-bottom="5px">${content.companyName}</mj-text>`;
      if (content.companyAddress) blockMjml += `<mj-text align="center" font-size="12px" color="#888888" line-height="1.6">${content.companyAddress}</mj-text>`;
      blockMjml += `<mj-text align="center" font-size="12px" color="#888888" padding="10px 0">
        <a href="${content.unsubscribeUrl || '#'}" style="color:#888888; text-decoration:underline;">Unsubscribe</a> | <a href="${content.privacyUrl || '#'}" style="color:#888888; text-decoration:underline;">Privacy Policy</a>
      </mj-text>`;
      // Update social icons logic to use AI-provided keys
      if (content.social_icons && Array.isArray(content.social_icons) && content.social_icons.length > 0) {
          blockMjml += `<mj-social font-size="12px" icon-size="24px" mode="horizontal" align="center" padding-top="10px" icon-padding="0 8px">`;
          content.social_icons.forEach((icon: { platform?: string, iconLink?: string, iconUrl?: string, iconAlt?: string }) => {
              const url = icon.iconLink; // Use iconLink from AI
              // Try to guess platform from URL for standard icons, or use alt text
              let platform = icon.platform;
              if (!platform && url) {
                  if (url.includes('facebook.com')) platform = 'facebook';
                  else if (url.includes('twitter.com')) platform = 'twitter';
                  else if (url.includes('instagram.com')) platform = 'instagram';
                  else if (url.includes('linkedin.com')) platform = 'linkedin';
                  // Add more platform detections if needed
              }
              platform = platform?.toLowerCase();
              const supportedPlatforms = ['facebook', 'twitter', 'instagram', 'linkedin', 'youtube', 'pinterest', 'github', 'google', 'snapchat', 'vimeo', 'medium', 'web'];

              if (url) { // Only add if there is a URL
                  if (platform && supportedPlatforms.includes(platform)) {
                      blockMjml += `<mj-social-element name="${platform}" href="${url}" background-color="#A9A9A9"></mj-social-element>`;
                  } else if (icon.iconUrl) {
                      // Fallback using the provided iconUrl if platform is unknown/unsupported
                      blockMjml += `<mj-social-element name="${icon.iconAlt || 'link'}" src="${icon.iconUrl}" href="${url}" background-color="#A9A9A9"></mj-social-element>`;
                  } else {
                       // Basic link if no platform match and no iconUrl
                       // blockMjml += `<mj-text align="center" font-size="12px"><a href="${url}">${icon.iconAlt || url}</a></mj-text>`; 
                  }
              }
          });
          blockMjml += `</mj-social>`;
      }
      blockMjml += '</mj-column>';
      blockMjml += '</mj-section>';
      // Closing mj-wrapper is handled below
      break;

    case 'misc/divider':
      blockMjml += `<mj-section padding="15px 0"><mj-column><mj-divider border-color="#cccccc" border-width="1px" padding="0" /></mj-column></mj-section>`;
      break;
    case 'misc/spacer':
      blockMjml += `<mj-section padding="0"><mj-column><mj-spacer height="${content.spacerHeight || 20}px" /></mj-column></mj-section>`;
      break;

    // Add cases for other blocks like product/card, product/grid, content/video, etc.
    case 'product/card': // Example implementation
        blockMjml += '<mj-section padding="10px 0">';
        blockMjml += '<mj-column width="40%" vertical-align="middle">';
        blockMjml += `<mj-image css-class="smooth-image" src="${content.productImageUrl || 'https://via.placeholder.com/200x200?text=Product'}" alt="${content.productImageAlt || 'Product Image'}" padding="0"></mj-image>`;
        blockMjml += '</mj-column>';
        blockMjml += '<mj-column width="60%" vertical-align="middle">';
        blockMjml += `<mj-text font-size="20px" font-weight="bold" color="${brandColors.text}" padding-bottom="5px">${content.productName || 'Product Name'}</mj-text>`;
        if (content.productDescription) blockMjml += `<mj-text font-size="14px" color="#555555" padding-bottom="10px">${content.productDescription}</mj-text>`;
        if (content.productPrice) blockMjml += `<mj-text font-size="18px" font-weight="bold" color="${brandColors.primary}" padding-bottom="10px">${content.productPrice}</mj-text>`;
        blockMjml += `<mj-button href="${content.productUrl || '#'}" background-color="${brandColors.secondary}" color="#ffffff" font-size="14px">${content.productButtonText || 'View Product'}</mj-button>`;
        blockMjml += '</mj-column>';
        blockMjml += '</mj-section>';
        break;

    default: // Fallback for unknown blocks
      blockMjml += `<mj-section><mj-column><mj-text color="red">Unsupported block: ${blockId}<br/><pre>${JSON.stringify(content, null, 2)}</pre></mj-text></mj-column></mj-section>`;
  }

  blockMjml += '</mj-wrapper>';
  return blockMjml;
}

/**
 * Generate email template from AI service
 * POST /api/v1/ai-templates/generate
 */
router.post('/generate', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  // --- ADDED VERY SPECIFIC LOG --- 
  console.log(`
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
>>> ENTERING /generate ROUTE HANDLER <<<
Timestamp: ${new Date().toISOString()}
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
`);
  try {
    const { prompt } = req.body;
    const userId = req.user?.id;

    if (!prompt || !userId) {
      return next(createError(400, 'Prompt and user authentication are required'));
    }

    console.log(`[AI Generate] Received prompt: "${prompt}" for user ${userId}`);

    // Generate structured template data from AI service
    const result = await aiTemplateService.generateTemplate(prompt, userId);
    const aiTemplateData = result.template; // Contains subject, blocks array

    // Fetch user preferences to use for styling
    const userPrefs = await UserPreference.findOne({ userId }).lean<IUserPreference>();
    const brandColors = getBrandColors(userPrefs); // Get brand colors

    if (!aiTemplateData || !aiTemplateData.blocks || !aiTemplateData.subject) {
        console.error('[AI Generate] AI service did not return expected structure:', aiTemplateData);
        return next(createError(500, 'AI service returned invalid template structure.'));
    }

    console.log(`[AI Generate] Received ${aiTemplateData.blocks.length} blocks from AI Service.`);
    const aiJsonString = JSON.stringify(aiTemplateData, null, 2); 
    console.log('[AI Generate Route] Parsed JSON received from AI Service:', aiJsonString);

    // --- Save AI Response to File (Simplified Path) --- 
    try {
        // Try writing to the current execution directory first (likely backend/dist/routes/)
        const logFilePath = path.join(__dirname, 'ai_service_last_response.json'); 
        fs.writeFileSync(logFilePath, aiJsonString, 'utf8');
        console.log(`[AI Generate Route] Attempted to save AI response to: ${logFilePath}`);
    } catch (writeError) {
        console.error('[AI Generate Route] Error writing AI response to file:', writeError);
    }
    // --- End Save AI Response --- 

    // --- Generate MJML from AI Blocks --- 
    let mjmlString = `
<mjml>
  <mj-head>
    <mj-title>${aiTemplateData.subject || 'AI Generated Email'}</mj-title>
    <mj-font name="Inter" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" />
    <mj-attributes>
      <mj-all font-family="Inter, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"" />
      <mj-text font-size="16px" line-height="1.6" color="${brandColors.text}" padding="5px 15px" />
      <mj-section padding="10px 0" />
      <mj-wrapper padding="0px" background-color="${brandColors.background}"/>
      <mj-column padding="5px" />
      <mj-button background-color="${brandColors.primary}" color="#ffffff" font-weight="bold" border-radius="5px" padding="10px 25px" font-size="16px" inner-padding="12px 25px" />
      <mj-divider border-color="#dddddd" border-width="1px" padding="15px 0" />
      <mj-image padding="0px" border-radius="4px" /> 
      <mj-hero padding="50px 0" />
    </mj-attributes>
    <mj-style inline="inline">
      a { color: ${brandColors.primary}; text-decoration: underline; }
      a.button { text-decoration: none; }
      h1, h2, h3 { color: ${brandColors.text}; font-weight: bold; }
    </mj-style>
    <mj-style>
      .smooth-image { border-radius: 8px; }
      .shadow { box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
      /* Add other global non-inline styles here */
    </mj-style>
  </mj-head>
  <mj-body background-color="#eaedf1"> /* Use a light grey background for the body */
`;

    aiTemplateData.blocks.forEach((block: any) => {
      mjmlString += generateMjmlForBlock(block, brandColors); // Pass brand colors
    });

    mjmlString += `
  </mj-body>
</mjml>`;
    // --- End MJML Generation ---

    // --- Convert MJML to HTML --- 
    let generatedHtml = '';
    try {
        const conversionResult = mjml2html(mjmlString, { 
            validationLevel: 'soft', // Use 'soft' to allow minor issues but still render
            minify: false, // Disable minification for easier debugging if needed
            beautify: true // Make output HTML readable
        });
        generatedHtml = conversionResult.html;
        if (conversionResult.errors && conversionResult.errors.length > 0) {
            console.warn('[AI Generate] MJML Conversion Warnings:', JSON.stringify(conversionResult.errors, null, 2));
        }
    } catch (mjmlError: any) {
        console.error('[AI Generate] MJML Conversion Failed:', mjmlError.message, mjmlError.errors);
        // Try to provide more context if possible
        console.error('[AI Generate] Failing MJML Input:\n', mjmlString.substring(0, 2000)); // Log beginning of MJML
        return next(createError(500, `Failed to convert generated MJML to HTML. ${mjmlError.message}`));
    }
    // --- End HTML Conversion ---

    // --- Map AI Blocks to System Block IDs (If needed for other features) ---
    const finalBlockIds = aiTemplateData.blocks.map((block: any) => {
      const mappedId = blockTypeMap[`${block.category}/${block.type}`] || block.blockId;
      if (!mappedId) {
          console.warn(`[AI Generate] Could not map AI block type: ${block.category}/${block.type}`);
      }
      return mappedId;
    }).filter(Boolean);

    // --- Save Template --- 
    console.log(`[AI Generate] Saving new template with subject: ${aiTemplateData.subject}`);
    const templateNameValue = `AI Generated: ${aiTemplateData.subject}`;

    const newTemplate = new Template({
      userId,
      name: templateNameValue,
      templateName: templateNameValue,
      description: `Generated from prompt: ${prompt}`,
      content: generatedHtml, // Store the final generated HTML
      mosaicoJson: null, 
      subject: aiTemplateData.subject,
      isAiGenerated: true,
      aiPrompt: prompt,
      blockIds: finalBlockIds,
      isPublic: false, 
      tags: ['ai-generated']
    });

    await newTemplate.save();

    console.log(`[AI Generate] Saved template with ID: ${newTemplate._id}. Sending response.`);

    res.status(201).json({
      success: true,
      data: {
        template: newTemplate,
      }
    });

  } catch (error) {
    console.error('[AI Template Routes] Error generating template:', error);
    next(error);
  }
});

/**
 * Get AI content suggestions
 * POST /api/v1/ai-templates/suggest
 */
router.post('/suggest', authenticate, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { content, suggestionType } = req.body;

    if (!content || !suggestionType) {
      return next(createError(400, 'Content and suggestion type are required'));
    }

    if (!['headline', 'body', 'cta'].includes(suggestionType)) {
      return next(createError(400, 'Invalid suggestion type'));
    }

    // Get suggestions from AI service
    const suggestions = await aiTemplateService.getSuggestions(content, suggestionType);

    res.json({
      success: true,
      data: {
        suggestions
      }
    });
  } catch (error) {
    console.error('[AI Template Routes] Error getting suggestions:', error);
    next(error);
  }
});

export default router;
