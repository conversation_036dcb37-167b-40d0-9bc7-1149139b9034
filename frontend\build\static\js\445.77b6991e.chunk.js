"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[445],{9445:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var a=s(5043),n=s(9291),i=s(8417),l=s(1411),r=s(9579),o=s(579);const c=()=>{const[e,t]=(0,a.useState)([]),[s,c]=(0,a.useState)(!0),[d,m]=(0,a.useState)(null),[x,u]=(0,a.useState)(null),[g,p]=(0,a.useState)(null),[h,y]=(0,a.useState)(""),[f,b]=(0,a.useState)([]),[j,N]=(0,a.useState)(null);(0,a.useEffect)((()=>{(async()=>{c(!0),m(null);try{const[e,s]=await Promise.all([r.Zg.getUserSegments(),r.DZ.getOptimalTimes()]);e.success?b(Array.isArray(e.data)?e.data:[]):m((t=>t?`${t}; ${e.message}`:e.message||"Failed to fetch segments")),s.success?t(Array.isArray(s.data)?s.data:[]):(m((e=>e?`${e}; ${s.message}`:s.message||"Failed to fetch optimal times")),t([]))}catch(e){m(e.message||"An error occurred while fetching initial data")}finally{c(!1)}})()}),[]),(0,a.useEffect)((()=>{if(!x||"running"!==g)return j&&clearInterval(j),void N(null);const e=async()=>{try{console.log(`Polling job status for ${x.id}...`);const e=await r.DZ.getJobStatus(x.id);if(e.success){const s=e.data.status;if(p(s),console.log(`Job status: ${s}`),"completed"===s||"failed"===s)if(j&&clearInterval(j),N(null),u(null),"completed"===s){console.log("Job completed, fetching updated times...");const e=await r.DZ.getOptimalTimes();e.success?t(Array.isArray(e.data)?e.data:[]):m("Failed to fetch updated optimal times after job completion.")}else m("Optimization job failed. Please check logs or try again.")}else console.error("Failed to poll job status:",e.message)}catch(e){console.error("Error polling job status:",e)}};if(!j){console.log("Starting job polling...");const t=setInterval(e,5e3);N(t)}return()=>{j&&(console.log("Clearing job polling interval."),clearInterval(j))}}),[x,g]);const v=e.reduce(((e,t)=>{const s=t.dayOfWeek||"Unknown";return e[s]||(e[s]=[]),e[s].push(t),e}),{});return(0,o.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,o.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,o.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"Predictive Send-Time Optimization"})}),(0,o.jsx)("p",{className:"text-text-secondary mb-6",children:"Optimize email delivery times based on recipient engagement patterns."}),d&&(0,o.jsx)(n.A,{type:"error",message:d,onClose:()=>m(null),className:"mb-6"}),(0,o.jsx)(l.A,{className:"mb-6",children:(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-text-primary mb-4",children:"Run Optimization"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"segmentId",className:"block text-sm font-medium text-text-secondary mb-1",children:"Segment (Optional)"}),(0,o.jsxs)("select",{id:"segmentId",name:"segmentId",value:h,onChange:e=>y(e.target.value),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:[(0,o.jsx)("option",{value:"",children:"All Recipients"}),f.map((e=>(0,o.jsx)("option",{value:e.id,children:e.name},e.id)))]}),(0,o.jsx)("p",{className:"mt-1 text-xs text-text-secondary",children:"Select a segment to optimize for, or leave blank for all."})]}),(0,o.jsx)("div",{className:"flex items-end",children:(0,o.jsx)(i.A,{onClick:async()=>{m(null),p(null),u(null),c(!0);try{const e=await r.DZ.runBulkOptimization(h||void 0);e.success?(u(e.data),p("running"),console.log("Optimization job started:",e.data)):m(e.message||"Failed to start optimization job")}catch(e){m(e.message||"An error occurred while starting optimization")}finally{c(!1)}},disabled:s||"running"===g,className:"w-full sm:w-auto",children:"running"===g?"Optimization Running...":s?"Starting...":"Run Optimization"})})]}),"running"===g&&(0,o.jsx)(n.A,{type:"info",message:`Optimization in progress. Status: ${g}. This may take a few minutes...`,className:"mt-4"})]})}),(0,o.jsx)(l.A,{children:(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-text-primary mb-4",children:"Optimal Send Times"}),s&&0===e.length?(0,o.jsx)("div",{className:"flex justify-center py-8",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):0===e.length?(0,o.jsx)("p",{className:"text-sm text-text-secondary text-center py-4",children:"No optimal send times available. Run an optimization to generate recommendations."}):(0,o.jsx)("div",{className:"space-y-6",children:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"].map((e=>{const t=v[e]||[];return 0===t.length?null:(0,o.jsxs)("div",{className:"border-t border-gray-700 pt-4",children:[(0,o.jsx)("h3",{className:"text-md font-medium text-text-primary mb-3",children:e}),(0,o.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:t.map(((e,t)=>(0,o.jsxs)("div",{className:"bg-secondary-bg p-4 rounded-lg shadow-md",children:[(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsx)("div",{className:"text-lg font-semibold text-primary",children:e.time||"Time N/A"}),(0,o.jsx)("div",{className:"px-2 py-0.5 rounded-full text-xs font-medium capitalize "+("high"===e.confidence?"bg-green-800 text-green-100":"medium"===e.confidence?"bg-yellow-800 text-yellow-100":"low"===e.confidence?"bg-red-800 text-red-100":"bg-gray-700 text-gray-300"),children:e.confidence||"unknown"})]}),(0,o.jsx)("div",{className:"text-sm text-text-secondary mb-1",children:e.segment?`Segment: ${e.segment}`:"All Recipients"}),(0,o.jsxs)("div",{className:"text-sm text-text-secondary mb-2",children:["Based on ",e.dataPoints||"?"," data points"]}),(0,o.jsxs)("div",{className:"text-sm",children:[(0,o.jsx)("span",{className:"text-text-secondary",children:"Est. Open Rate: "}),(0,o.jsx)("span",{className:"font-medium text-text-primary",children:e.expectedOpenRate||"N/A"})]})]},t)))})]},e)}))})]})})]})}}}]);
//# sourceMappingURL=445.77b6991e.chunk.js.map