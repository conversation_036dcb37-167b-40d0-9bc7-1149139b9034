"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[443],{551:(e,t,n)=>{function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}n.d(t,{_:()=>r})},766:(e,t,n)=>{n.d(t,{_:()=>l});var r=Object.defineProperty,o=(e,t,n)=>(((e,t,n)=>{t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let l=new class{constructor(){o(this,"current",this.detect()),o(this,"handoffState","pending"),o(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}},3057:(e,t,n)=>{n.d(t,{L:()=>l});var r=n(5043),o=n(8518);function l(){let[e]=(0,r.useState)(o.e);return(0,r.useEffect)((()=>()=>e.dispose()),[e]),e}},3820:(e,t,n)=>{function r(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}n.d(t,{x:()=>r})},3880:(e,t,n)=>{function r(e,t){if(e in t){let r=t[e];for(var n=arguments.length,o=new Array(n>2?n-2:0),l=2;l<n;l++)o[l-2]=arguments[l];return"function"==typeof r?r(...o):r}let i=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(i,r),i}n.d(t,{Y:()=>r})},4347:(e,t,n)=>{n.d(t,{Y:()=>l});var r=n(5043),o=n(7248);function l(e){let t=(0,r.useRef)(e);return(0,o.s)((()=>{t.current=e}),[e]),t}},4521:(e,t,n)=>{n.d(t,{a:()=>l});var r=n(5043),o=n(7248);function l(){let e=(0,r.useRef)(!1);return(0,o.s)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},5641:(e,t,n)=>{n.d(t,{Ac:()=>a,Ci:()=>c,FX:()=>p,mK:()=>s,oE:()=>v});var r,o,l=n(5043),i=n(3820),u=n(3880),a=((o=a||{})[o.None=0]="None",o[o.RenderStrategy=1]="RenderStrategy",o[o.Static=2]="Static",o),s=((r=s||{})[r.Unmount=0]="Unmount",r[r.Hidden=1]="Hidden",r);function c(){let e=function(){let e=(0,l.useRef)([]),t=(0,l.useCallback)((t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)}),[]);return function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];if(!r.every((e=>null==e)))return e.current=r,t}}();return(0,l.useCallback)((t=>function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:o,features:l,visible:i=!0,name:a,mergeRefs:s}=e;s=null!=s?s:f;let c=m(n,t);if(i)return d(c,r,o,a,s);let p=null!=l?l:0;if(2&p){let{static:e=!1,...t}=c;if(e)return d(t,r,o,a,s)}if(1&p){let{unmount:e=!0,...t}=c;return(0,u.Y)(e?0:1,{0:()=>null,1:()=>d({...t,hidden:!0,style:{display:"none"}},r,o,a,s)})}return d(c,r,o,a,s)}({mergeRefs:e,...t})),[e])}function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0,{as:u=n,children:a,refName:s="ref",...c}=h(e,["unmount","static"]),d=void 0!==e.ref?{[s]:e.ref}:{},f="function"==typeof a?a(t):a;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(t)),c["aria-labelledby"]&&c["aria-labelledby"]===c.id&&(c["aria-labelledby"]=void 0);let p={};if(t){let e=!1,n=[];for(let[r,o]of Object.entries(t))"boolean"==typeof o&&(e=!0),!0===o&&n.push(r.replace(/([A-Z])/g,(e=>`-${e.toLowerCase()}`)));if(e){p["data-headlessui-state"]=n.join(" ");for(let e of n)p[`data-${e}`]=""}}if(u===l.Fragment&&(Object.keys(v(c)).length>0||Object.keys(v(p)).length>0)){if((0,l.isValidElement)(f)&&!(Array.isArray(f)&&f.length>1)){let e=f.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){return(0,i.x)(t(...arguments),c.className)}:(0,i.x)(t,c.className),r=n?{className:n}:{},u=m(f.props,v(h(c,["ref"])));for(let o in p)o in u&&delete p[o];return(0,l.cloneElement)(f,Object.assign({},u,p,d,{ref:o(g(f),d.ref)},r))}if(Object.keys(v(c)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(v(c)).concat(Object.keys(v(p))).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"))}return(0,l.createElement)(u,Object.assign({},h(c,["ref"]),u!==l.Fragment&&d,u!==l.Fragment&&p),f)}function f(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every((e=>null==e))?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function m(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},o={};for(let l of t)for(let e in l)e.startsWith("on")&&"function"==typeof l[e]?(null!=o[e]||(o[e]=[]),o[e].push(l[e])):r[e]=l[e];if(r.disabled||r["aria-disabled"])for(let l in o)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(l)&&(o[l]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let l in o)Object.assign(r,{[l](e){let t=o[l];for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(let o of t){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;o(e,...r)}}});return r}function p(e){var t;return Object.assign((0,l.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function v(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function h(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function g(e){return l.version.split(".")[0]>="19"?e.props.ref:e.ref}},5962:(e,t,n)=>{n.d(t,{_:()=>l});var r=n(5043),o=n(4347);let l=function(e){let t=(0,o.Y)(e);return r.useCallback((function(){return t.current(...arguments)}),[t])}},6018:(e,t,n)=>{n.d(t,{e:()=>k,_:()=>x});var r,o,l=n(5043),i=n(3057),u=n(5962),a=n(4521),s=n(7248),c=n(4347),d=n(6170),f=n(9358),m=n(8518);"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&"test"===(null==(r=null==process?void 0:{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_AI_SERVICE_URL:"http://localhost:5000",REACT_APP_API_URL:"https://driftly-ab-175086977.us-east-2.elb.amazonaws.com/api/v1"})?void 0:r.NODE_ENV)&&"undefined"==typeof(null==(o=null==Element?void 0:Element.prototype)?void 0:o.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join("\n")),[]});var p,v=((p=v||{})[p.None=0]="None",p[p.Closed=1]="Closed",p[p.Enter=2]="Enter",p[p.Leave=4]="Leave",p);function h(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}function g(e,t,n,r){let[o,u]=(0,l.useState)(n),{hasFlag:a,addFlag:c,removeFlag:d}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,l.useState)(e),r=(0,l.useCallback)((e=>n(e)),[t]),o=(0,l.useCallback)((e=>n((t=>t|e))),[t]),i=(0,l.useCallback)((e=>(t&e)===e),[t]),u=(0,l.useCallback)((e=>n((t=>t&~e))),[n]),a=(0,l.useCallback)((e=>n((t=>t^e))),[n]);return{flags:t,setFlag:r,addFlag:o,hasFlag:i,removeFlag:u,toggleFlag:a}}(e&&o?3:0),f=(0,l.useRef)(!1),p=(0,l.useRef)(!1),v=(0,i.L)();return(0,s.s)((()=>{var o;if(e)return n&&u(!0),t?(null==(o=null==r?void 0:r.start)||o.call(r,n),function(e,t){let{prepare:n,run:r,done:o,inFlight:l}=t,i=(0,m.e)();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return void r();let o=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=o}(e,{prepare:n,inFlight:l}),i.nextFrame((()=>{r(),i.requestAnimationFrame((()=>{i.add(function(e,t){var n,r;let o=(0,m.e)();if(!e)return o.dispose;let l=!1;o.add((()=>{l=!0}));let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter((e=>e instanceof CSSTransition)))?r:[];return 0===i.length?(t(),o.dispose):(Promise.allSettled(i.map((e=>e.finished))).then((()=>{l||t()})),o.dispose)}(e,o))}))})),i.dispose}(t,{inFlight:f,prepare(){p.current?p.current=!1:p.current=f.current,f.current=!0,!p.current&&(n?(c(3),d(4)):(c(4),d(2)))},run(){p.current?n?(d(3),c(4)):(d(4),c(3)):n?d(1):c(1)},done(){var e;p.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(f.current=!1,d(7),n||u(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})):void(n&&c(3))}),[e,n,t,v]),e?[o,{closed:a(1),enter:a(2),leave:a(4),transition:a(2)||a(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}var E=n(6794),w=n(3820),b=n(3880),y=n(5641);function F(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:O)!==l.Fragment||1===l.Children.count(e.children)}let C=(0,l.createContext)(null);C.displayName="TransitionContext";var P=(e=>(e.Visible="visible",e.Hidden="hidden",e))(P||{});let T=(0,l.createContext)(null);function S(e){return"children"in e?S(e.children):e.current.filter((e=>{let{el:t}=e;return null!==t.current})).filter((e=>{let{state:t}=e;return"visible"===t})).length>0}function A(e,t){let n=(0,c.Y)(e),r=(0,l.useRef)([]),o=(0,a.a)(),s=(0,i.L)(),d=(0,u._)((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:y.mK.Hidden,l=r.current.findIndex((t=>{let{el:n}=t;return n===e}));-1!==l&&((0,b.Y)(t,{[y.mK.Unmount](){r.current.splice(l,1)},[y.mK.Hidden](){r.current[l].state="hidden"}}),s.microTask((()=>{var e;!S(r)&&o.current&&(null==(e=n.current)||e.call(n))})))})),f=(0,u._)((e=>{let t=r.current.find((t=>{let{el:n}=t;return n===e}));return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,y.mK.Unmount)})),m=(0,l.useRef)([]),p=(0,l.useRef)(Promise.resolve()),v=(0,l.useRef)({enter:[],leave:[]}),h=(0,u._)(((e,n,r)=>{m.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter((t=>{let[n]=t;return n!==e}))),null==t||t.chains.current[n].push([e,new Promise((e=>{m.current.push(e)}))]),null==t||t.chains.current[n].push([e,new Promise((e=>{Promise.all(v.current[n].map((e=>{let[t,n]=e;return n}))).then((()=>e()))}))]),"enter"===n?p.current=p.current.then((()=>null==t?void 0:t.wait.current)).then((()=>r(n))):r(n)})),g=(0,u._)(((e,t,n)=>{Promise.all(v.current[t].splice(0).map((e=>{let[t,n]=e;return n}))).then((()=>{var e;null==(e=m.current.shift())||e()})).then((()=>n(t)))}));return(0,l.useMemo)((()=>({children:r,register:f,unregister:d,onStart:h,onStop:g,wait:p,chains:v})),[f,d,r,h,g,v,p])}T.displayName="NestingContext";let O=l.Fragment,L=y.Ac.RenderStrategy;let R=(0,y.FX)((function(e,t){let{show:n,appear:r=!1,unmount:o=!0,...i}=e,a=(0,l.useRef)(null),c=F(e),m=(0,f.P)(...c?[a,t]:null===t?[]:[t]);(0,d.g)();let p=(0,E.O_)();if(void 0===n&&null!==p&&(n=(p&E.Uw.Open)===E.Uw.Open),void 0===n)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[v,h]=(0,l.useState)(n?"visible":"hidden"),g=A((()=>{n||h("hidden")})),[w,b]=(0,l.useState)(!0),P=(0,l.useRef)([n]);(0,s.s)((()=>{!1!==w&&P.current[P.current.length-1]!==n&&(P.current.push(n),b(!1))}),[P,n]);let O=(0,l.useMemo)((()=>({show:n,appear:r,initial:w})),[n,r,w]);(0,s.s)((()=>{n?h("visible"):!S(g)&&null!==a.current&&h("hidden")}),[n,g]);let R={unmount:o},x=(0,u._)((()=>{var t;w&&b(!1),null==(t=e.beforeEnter)||t.call(e)})),k=(0,u._)((()=>{var t;w&&b(!1),null==(t=e.beforeLeave)||t.call(e)})),N=(0,y.Ci)();return l.createElement(T.Provider,{value:g},l.createElement(C.Provider,{value:O},N({ourProps:{...R,as:l.Fragment,children:l.createElement(_,{ref:m,...R,...i,beforeEnter:x,beforeLeave:k})},theirProps:{},defaultTag:l.Fragment,features:L,visible:"visible"===v,name:"Transition"})))})),_=(0,y.FX)((function(e,t){var n,r;let{transition:o=!0,beforeEnter:i,afterEnter:a,beforeLeave:c,afterLeave:m,enter:p,enterFrom:v,enterTo:P,entered:R,leave:_,leaveFrom:x,leaveTo:k,...N}=e,[M,D]=(0,l.useState)(null),I=(0,l.useRef)(null),H=F(e),j=(0,f.P)(...H?[I,t,D]:null===t?[]:[t]),U=null==(n=N.unmount)||n?y.mK.Unmount:y.mK.Hidden,{show:Y,appear:$,initial:X}=function(){let e=(0,l.useContext)(C);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[K,W]=(0,l.useState)(Y?"visible":"hidden"),V=function(){let e=(0,l.useContext)(T);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:B,unregister:q}=V;(0,s.s)((()=>B(I)),[B,I]),(0,s.s)((()=>{if(U===y.mK.Hidden&&I.current)return Y&&"visible"!==K?void W("visible"):(0,b.Y)(K,{hidden:()=>q(I),visible:()=>B(I)})}),[K,I,B,q,Y,U]);let G=(0,d.g)();(0,s.s)((()=>{if(H&&G&&"visible"===K&&null===I.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[I,K,G,H]);let z=X&&!$,Z=$&&Y&&X,J=(0,l.useRef)(!1),Q=A((()=>{J.current||(W("hidden"),q(I))}),V),ee=(0,u._)((e=>{J.current=!0;let t=e?"enter":"leave";Q.onStart(I,t,(e=>{"enter"===e?null==i||i():"leave"===e&&(null==c||c())}))})),te=(0,u._)((e=>{let t=e?"enter":"leave";J.current=!1,Q.onStop(I,t,(e=>{"enter"===e?null==a||a():"leave"===e&&(null==m||m())})),"leave"===t&&!S(Q)&&(W("hidden"),q(I))}));(0,l.useEffect)((()=>{H&&o||(ee(Y),te(Y))}),[Y,H,o]);let ne=!(!o||!H||!G||z),[,re]=g(ne,M,Y,{start:ee,end:te}),oe=(0,y.oE)({ref:j,className:(null==(r=(0,w.x)(N.className,Z&&p,Z&&v,re.enter&&p,re.enter&&re.closed&&v,re.enter&&!re.closed&&P,re.leave&&_,re.leave&&!re.closed&&x,re.leave&&re.closed&&k,!re.transition&&Y&&R))?void 0:r.trim())||void 0,...h(re)}),le=0;"visible"===K&&(le|=E.Uw.Open),"hidden"===K&&(le|=E.Uw.Closed),Y&&"hidden"===K&&(le|=E.Uw.Opening),!Y&&"visible"===K&&(le|=E.Uw.Closing);let ie=(0,y.Ci)();return l.createElement(T.Provider,{value:Q},l.createElement(E.El,{value:le},ie({ourProps:oe,theirProps:N,defaultTag:O,features:L,visible:"visible"===K,name:"Transition.Child"})))})),x=(0,y.FX)((function(e,t){let n=null!==(0,l.useContext)(C),r=null!==(0,E.O_)();return l.createElement(l.Fragment,null,!n&&r?l.createElement(R,{ref:t,...e}):l.createElement(_,{ref:t,...e}))})),k=Object.assign(R,{Child:x,Root:R})},6170:(e,t,n)=>{var r;n.d(t,{g:()=>i});var o=n(5043),l=n(766);function i(){let e=function(){let e="undefined"==typeof document;return(r||(r=n.t(o,2))).useSyncExternalStore((()=>()=>{}),(()=>!1),(()=>!e))}(),[t,i]=o.useState(l._.isHandoffComplete);return t&&!1===l._.isHandoffComplete&&i(!1),o.useEffect((()=>{!0!==t&&i(!0)}),[t]),o.useEffect((()=>l._.handoff()),[]),!e&&t}},6443:(e,t,n)=>{n.d(t,{lG:()=>Ve});var r=n(5043),o=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(o||{}),l=n(4347);function i(e,t,n,o){let i=(0,l.Y)(n);(0,r.useEffect)((()=>{function n(e){i.current(e)}return(e=null!=e?e:window).addEventListener(t,n,o),()=>e.removeEventListener(t,n,o)}),[e,t,o])}class u extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}function a(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var o=arguments.length,l=new Array(o>1?o-1:0),i=1;i<o;i++)l[i-1]=arguments[i];let u=t[e].call(n,...l);u&&(n=u,r.forEach((e=>e())))}}}var s=n(7248);function c(e){return(0,r.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}let d=new u((()=>a((()=>[]),{ADD(e){return this.includes(e)?this:[...this,e]},REMOVE(e){let t=this.indexOf(e);if(-1===t)return this;let n=this.slice();return n.splice(t,1),n}})));function f(e,t){let n=d.get(t),o=(0,r.useId)(),l=c(n);if((0,s.s)((()=>{if(e)return n.dispatch("ADD",o),()=>n.dispatch("REMOVE",o)}),[n,e]),!e)return!1;let i=l.indexOf(o),u=l.length;return-1===i&&(i=u,u+=1),i===u-1}var m=n(5962),p=n(8518),v=n(766);function h(e){var t,n;return v._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let g=new Map,E=new Map;function w(e){var t;let n=null!=(t=E.get(e))?t:0;return E.set(e,n+1),0!==n||(g.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>b(e)}function b(e){var t;let n=null!=(t=E.get(e))?t:1;if(1===n?E.delete(e):E.set(e,n-1),1!==n)return;let r=g.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,g.delete(e))}function y(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=f(e,"inert-others");(0,s.s)((()=>{var e,o;if(!r)return;let l=(0,p.e)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(w(t));let i=null!=(o=null==t?void 0:t())?o:[];for(let t of i){if(!t)continue;let e=h(t);if(!e)continue;let n=t.parentElement;for(;n&&n!==e.body;){for(let e of n.children)i.some((t=>e.contains(t)))||l.add(w(e));n=n.parentElement}}return l.dispose}),[r,t,n])}var F=n(3880);let C=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(","),P=["[data-autofocus]"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var T,S=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(S||{}),A=((T=A||{})[T.Error=0]="Error",T[T.Overflow=1]="Overflow",T[T.Success=2]="Success",T[T.Underflow=3]="Underflow",T),O=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(O||{});function L(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(C)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}var R=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(R||{});function _(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;var n;return e!==(null==(n=h(e))?void 0:n.body)&&(0,F.Y)(t,{0:()=>e.matches(C),1(){let t=e;for(;null!==t;){if(t.matches(C))return!0;t=t.parentElement}return!1}})}var x=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(x||{});function k(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let N=["textarea","input"].join(",");function M(e,t){let{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,i=Array.isArray(e)?n?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort(((e,n)=>{let r=t(e),o=t(n);if(null===r||null===o)return 0;let l=r.compareDocumentPosition(o);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(P)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e):L(e);o.length>0&&i.length>1&&(i=i.filter((e=>!o.some((t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))))),r=null!=r?r:l.activeElement;let u,a=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,i.indexOf(r))-1;if(4&t)return Math.max(0,i.indexOf(r))+1;if(8&t)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=i.length;do{if(d>=f||d+f<=0)return 0;let e=s+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}u=i[e],null==u||u.focus(c),d+=a}while(u!==l.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,N))&&n}(u)&&u.select(),2}function D(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function I(){return D()||/Android/gi.test(window.navigator.userAgent)}function H(e,t,n,o){let i=(0,l.Y)(n);(0,r.useEffect)((()=>{if(e)return document.addEventListener(t,n,o),()=>document.removeEventListener(t,n,o);function n(e){i.current(e)}}),[e,t,o])}function j(e,t,n,o){let i=(0,l.Y)(n);(0,r.useEffect)((()=>{if(e)return window.addEventListener(t,n,o),()=>window.removeEventListener(t,n,o);function n(e){i.current(e)}}),[e,t,o])}function U(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)((()=>h(...t)),[...t])}var Y=n(5641);var $=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))($||{});let X=(0,Y.FX)((function(e,t){var n;let{features:r=1,...o}=e,l={ref:t,"aria-hidden":2===(2&r)||(null!=(n=o["aria-hidden"])?n:void 0),hidden:4===(4&r)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...4===(4&r)&&2!==(2&r)&&{display:"none"}}};return(0,Y.Ci)()({ourProps:l,theirProps:o,slot:{},defaultTag:"span",name:"Hidden"})}));let K=(0,r.createContext)(null);function W(e){let{children:t,node:n}=e,[o,l]=(0,r.useState)(null),i=V(null!=n?n:o);return r.createElement(K.Provider,{value:i},t,null===i&&r.createElement(X,{features:$.Hidden,ref:e=>{var t,n;if(e)for(let r of null!=(n=null==(t=h(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&r instanceof HTMLElement&&null!=r&&r.contains(e)){l(r);break}}}))}function V(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;var t;return null!=(t=(0,r.useContext)(K))?t:e}function B(){let e;return{before(t){let{doc:n}=t;var r;let o=n.documentElement,l=null!=(r=n.defaultView)?r:window;e=Math.max(0,l.innerWidth-o.clientWidth)},after(t){let{doc:n,d:r}=t,o=n.documentElement,l=Math.max(0,o.clientWidth-o.offsetWidth),i=Math.max(0,e-l);r.style(o,"paddingRight",`${i}px`)}}}function q(){return D()?{before(e){let{doc:t,d:n,meta:r}=e;function o(e){return r.containers.flatMap((e=>e())).some((t=>t.contains(e)))}n.microTask((()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=(0,p.e)();e.style(t.documentElement,"scrollBehavior","auto"),n.add((()=>n.microTask((()=>e.dispose()))))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,l=null;n.addEventListener(t,"click",(e=>{if(e.target instanceof HTMLElement)try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),i=t.querySelector(r);i&&!o(i)&&(l=i)}catch{}}),!0),n.addEventListener(t,"touchstart",(e=>{if(e.target instanceof HTMLElement)if(o(e.target)){let t=e.target;for(;t.parentElement&&o(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")})),n.addEventListener(t,"touchmove",(e=>{if(e.target instanceof HTMLElement){if("INPUT"===e.target.tagName)return;if(o(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}),{passive:!1}),n.add((()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;r!==t&&window.scrollTo(0,r),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)}))}))}}:{}}function G(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let z=a((()=>new Map),{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,p.e)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let{doc:t,d:n,meta:r}=e,o={doc:t,d:n,meta:G(r)},l=[q(),B(),{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];l.forEach((e=>{let{before:t}=e;return null==t?void 0:t(o)})),l.forEach((e=>{let{after:t}=e;return null==t?void 0:t(o)}))},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});function Z(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];!function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=c(z),o=t?r.get(t):void 0,l=!!o&&o.count>0;(0,s.s)((()=>{if(t&&e)return z.dispatch("PUSH",t,n),()=>z.dispatch("POP",t,n)}),[e,t])}(f(e,"scroll-lock"),t,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}}))}z.subscribe((()=>{let e=z.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&z.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&z.dispatch("TEARDOWN",n)}}));var J=n(6170),Q=n(9358);let ee=(0,r.createContext)((()=>{}));function te(e){let{value:t,children:n}=e;return r.createElement(ee.Provider,{value:t},n)}var ne=n(6794);let re=(0,r.createContext)(!1);function oe(e){return r.createElement(re.Provider,{value:e.force},e.children)}let le=(0,r.createContext)(void 0);let ie=(0,r.createContext)(null);function ue(){let e=(0,r.useContext)(ie);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,ue),e}return e}ie.displayName="DescriptionContext";let ae=(0,Y.FX)((function(e,t){let n=(0,r.useId)(),o=(0,r.useContext)(le),{id:l=`headlessui-description-${n}`,...i}=e,u=ue(),a=(0,Q.P)(t);(0,s.s)((()=>u.register(l)),[l,u.register]);let c=o||!1,d=(0,r.useMemo)((()=>({...u.slot,disabled:c})),[u.slot,c]),f={ref:a,...u.props,id:l};return(0,Y.Ci)()({ourProps:f,theirProps:i,slot:d,defaultTag:"p",name:u.name||"Description"})})),se=Object.assign(ae,{});var ce=n(3057),de=n(4521),fe=n(551);function me(e){let t=(0,m._)(e),n=(0,r.useRef)(!1);(0,r.useEffect)((()=>(n.current=!1,()=>{n.current=!0,(0,fe._)((()=>{n.current&&t()}))})),[t])}var pe=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(pe||{});function ve(e,t){let n=(0,r.useRef)([]),o=(0,m._)(e);(0,r.useEffect)((()=>{let e=[...n.current];for(let[r,l]of t.entries())if(n.current[r]!==l){let r=o(t,e);return n.current=t,r}}),[o,...t])}let he=[];function ge(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)n.current instanceof HTMLElement&&t.add(n.current);return t}!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}((()=>{function e(e){if(!(e.target instanceof HTMLElement)||e.target===document.body||he[0]===e.target)return;let t=e.target;t=t.closest(C),he.unshift(null!=t?t:e.target),he=he.filter((e=>null!=e&&e.isConnected)),he.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})}));var Ee=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(Ee||{});let we=(0,Y.FX)((function(e,t){let n=(0,r.useRef)(null),o=(0,Q.P)(n,t),{initialFocus:l,initialFocusFallback:u,containers:a,features:s=15,...c}=e;(0,J.g)()||(s=0);let d=U(n);!function(e,t){let{ownerDocument:n}=t,o=!!(8&e),l=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=(0,r.useRef)(he.slice());return ve(((e,n)=>{let[r]=e,[o]=n;!0===o&&!1===r&&(0,fe._)((()=>{t.current.splice(0)})),!1===o&&!0===r&&(t.current=he.slice())}),[e,he,t]),(0,m._)((()=>{var e;return null!=(e=t.current.find((e=>null!=e&&e.isConnected)))?e:null}))}(o);ve((()=>{o||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&k(l())}),[o]),me((()=>{o&&k(l())}))}(s,{ownerDocument:d});let p=function(e,t){let{ownerDocument:n,container:o,initialFocus:l,initialFocusFallback:i}=t,u=(0,r.useRef)(null),a=f(!!(1&e),"focus-trap#initial-focus"),s=(0,de.a)();return ve((()=>{if(0===e)return;if(!a)return void(null!=i&&i.current&&k(i.current));let t=o.current;t&&(0,fe._)((()=>{if(!s.current)return;let r=null==n?void 0:n.activeElement;if(null!=l&&l.current){if((null==l?void 0:l.current)===r)return void(u.current=r)}else if(t.contains(r))return void(u.current=r);if(null!=l&&l.current)k(l.current);else{if(16&e){if(M(t,S.First|S.AutoFocus)!==A.Error)return}else if(M(t,S.First)!==A.Error)return;if(null!=i&&i.current&&(k(i.current),(null==n?void 0:n.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}u.current=null==n?void 0:n.activeElement}))}),[i,a,e]),u}(s,{ownerDocument:d,container:n,initialFocus:l,initialFocusFallback:u});!function(e,t){let{ownerDocument:n,container:r,containers:o,previousActiveElement:l}=t,u=(0,de.a)(),a=!!(4&e);i(null==n?void 0:n.defaultView,"focus",(e=>{if(!a||!u.current)return;let t=ge(o);r.current instanceof HTMLElement&&t.add(r.current);let n=l.current;if(!n)return;let i=e.target;i&&i instanceof HTMLElement?ye(t,i)?(l.current=i,k(i)):(e.preventDefault(),e.stopPropagation(),k(n)):k(l.current)}),!0)}(s,{ownerDocument:d,container:n,containers:a,previousActiveElement:p});let v=function(){let e=(0,r.useRef)(0);return j(!0,"keydown",(t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)}),!0),e}(),h=(0,m._)((e=>{let t=n.current;t&&(0,F.Y)(v.current,{[pe.Forwards]:()=>{M(t,S.First,{skipElements:[e.relatedTarget,u]})},[pe.Backwards]:()=>{M(t,S.Last,{skipElements:[e.relatedTarget,u]})}})})),g=f(!!(2&s),"focus-trap#tab-lock"),E=(0,ce.L)(),w=(0,r.useRef)(!1),b={ref:o,onKeyDown(e){"Tab"==e.key&&(w.current=!0,E.requestAnimationFrame((()=>{w.current=!1})))},onBlur(e){if(!(4&s))return;let t=ge(a);n.current instanceof HTMLElement&&t.add(n.current);let r=e.relatedTarget;r instanceof HTMLElement&&"true"!==r.dataset.headlessuiFocusGuard&&(ye(t,r)||(w.current?M(n.current,(0,F.Y)(v.current,{[pe.Forwards]:()=>S.Next,[pe.Backwards]:()=>S.Previous})|S.WrapAround,{relativeTo:e.target}):e.target instanceof HTMLElement&&k(e.target)))}},y=(0,Y.Ci)();return r.createElement(r.Fragment,null,g&&r.createElement(X,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:$.Focusable}),y({ourProps:b,theirProps:c,defaultTag:"div",name:"FocusTrap"}),g&&r.createElement(X,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:h,features:$.Focusable}))})),be=Object.assign(we,{features:Ee});function ye(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var Fe=n(7950);function Ce(e){let t=(0,r.useContext)(re),n=(0,r.useContext)(Ae),[o,l]=(0,r.useState)((()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(v._.isServer)return null;let o=null==e?void 0:e.getElementById("headlessui-portal-root");if(o)return o;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)}));return(0,r.useEffect)((()=>{null!==o&&(null!=e&&e.body.contains(o)||null==e||e.body.appendChild(o))}),[o,e]),(0,r.useEffect)((()=>{t||null!==n&&l(n.current)}),[n,l,t]),o}let Pe=r.Fragment,Te=(0,Y.FX)((function(e,t){let{ownerDocument:n=null,...o}=e,l=(0,r.useRef)(null),i=(0,Q.P)((0,Q.a)((e=>{l.current=e})),t),u=U(l),a=null!=n?n:u,c=Ce(a),[d]=(0,r.useState)((()=>{var e;return v._.isServer?null:null!=(e=null==a?void 0:a.createElement("div"))?e:null})),f=(0,r.useContext)(Oe),m=(0,J.g)();(0,s.s)((()=>{!c||!d||c.contains(d)||(d.setAttribute("data-headlessui-portal",""),c.appendChild(d))}),[c,d]),(0,s.s)((()=>{if(d&&f)return f.register(d)}),[f,d]),me((()=>{var e;!c||!d||(d instanceof Node&&c.contains(d)&&c.removeChild(d),c.childNodes.length<=0&&(null==(e=c.parentElement)||e.removeChild(c)))}));let p=(0,Y.Ci)();return m&&c&&d?(0,Fe.createPortal)(p({ourProps:{ref:i},theirProps:o,slot:{},defaultTag:Pe,name:"Portal"}),d):null}));let Se=r.Fragment,Ae=(0,r.createContext)(null);let Oe=(0,r.createContext)(null);function Le(){let e=(0,r.useContext)(Oe),t=(0,r.useRef)([]),n=(0,m._)((n=>(t.current.push(n),e&&e.register(n),()=>o(n)))),o=(0,m._)((n=>{let r=t.current.indexOf(n);-1!==r&&t.current.splice(r,1),e&&e.unregister(n)})),l=(0,r.useMemo)((()=>({register:n,unregister:o,portals:t})),[n,o,t]);return[t,(0,r.useMemo)((()=>function(e){let{children:t}=e;return r.createElement(Oe.Provider,{value:l},t)}),[l])]}let Re=(0,Y.FX)((function(e,t){let n=(0,Q.P)(t),{enabled:o=!0,ownerDocument:l,...i}=e,u=(0,Y.Ci)();return o?r.createElement(Te,{...i,ownerDocument:l,ref:n}):u({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:Pe,name:"Portal"})})),_e=(0,Y.FX)((function(e,t){let{target:n,...o}=e,l={ref:(0,Q.P)(t)},i=(0,Y.Ci)();return r.createElement(Ae.Provider,{value:n},i({ourProps:l,theirProps:o,defaultTag:Se,name:"Popover.Group"}))})),xe=Object.assign(Re,{Group:_e});var ke=n(6018),Ne=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Ne||{}),Me=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(Me||{});let De={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},Ie=(0,r.createContext)(null);function He(e){let t=(0,r.useContext)(Ie);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,He),t}return t}function je(e,t){return(0,F.Y)(t.type,De,e,t)}Ie.displayName="DialogContext";let Ue=(0,Y.FX)((function(e,t){let n=(0,r.useId)(),{id:u=`headlessui-dialog-${n}`,open:a,onClose:c,initialFocus:d,role:v="dialog",autoFocus:h=!0,__demoMode:g=!1,unmount:E=!1,...w}=e,b=(0,r.useRef)(!1);v="dialog"===v||"alertdialog"===v?v:(b.current||(b.current=!0,console.warn(`Invalid role [${v}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let F=(0,ne.O_)();void 0===a&&null!==F&&(a=(F&ne.Uw.Open)===ne.Uw.Open);let C=(0,r.useRef)(null),P=(0,Q.P)(C,t),T=U(C),S=a?0:1,[A,O]=(0,r.useReducer)(je,{titleId:null,descriptionId:null,panelRef:(0,r.createRef)()}),L=(0,m._)((()=>c(!1))),x=(0,m._)((e=>O({type:0,id:e}))),k=!!(0,J.g)()&&0===S,[N,M]=Le(),D={get current(){var e;return null!=(e=A.panelRef.current)?e:C.current}},$=V(),{resolveContainers:X}=function(){let{defaultContainers:e=[],portals:t,mainTreeNode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=U(n),o=(0,m._)((()=>{var o,l;let i=[];for(let t of e)null!==t&&(t instanceof HTMLElement?i.push(t):"current"in t&&t.current instanceof HTMLElement&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(o=null==r?void 0:r.querySelectorAll("html > *, body > *"))?o:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(l=null==n?void 0:n.getRootNode())?void 0:l.host))||i.some((t=>e.contains(t)))||i.push(e));return i}));return{resolveContainers:o,contains:(0,m._)((e=>o().some((t=>t.contains(e)))))}}({mainTreeNode:$,portals:N,defaultContainers:[D]}),K=null!==F&&(F&ne.Uw.Closing)===ne.Uw.Closing;y(!g&&!K&&k,{allowed:(0,m._)((()=>{var e,t;return[null!=(t=null==(e=C.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]})),disallowed:(0,m._)((()=>{var e;return[null!=(e=null==$?void 0:$.closest("body > *:not(#headlessui-portal-root)"))?e:null]}))}),function(e,t,n){let o=f(e,"outside-click"),i=(0,l.Y)(n),u=(0,r.useCallback)((function(e,n){if(e.defaultPrevented)return;let r=n(e);if(null===r||!r.getRootNode().contains(r)||!r.isConnected)return;let o=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(t);for(let t of o)if(null!==t&&(t.contains(r)||e.composed&&e.composedPath().includes(t)))return;return!_(r,R.Loose)&&-1!==r.tabIndex&&e.preventDefault(),i.current(e,r)}),[i,t]),a=(0,r.useRef)(null);H(o,"pointerdown",(e=>{var t,n;a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target}),!0),H(o,"mousedown",(e=>{var t,n;a.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target}),!0),H(o,"click",(e=>{I()||a.current&&(u(e,(()=>a.current)),a.current=null)}),!0);let s=(0,r.useRef)({x:0,y:0});H(o,"touchstart",(e=>{s.current.x=e.touches[0].clientX,s.current.y=e.touches[0].clientY}),!0),H(o,"touchend",(e=>{let t=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY;if(!(Math.abs(t-s.current.x)>=30||Math.abs(n-s.current.y)>=30))return u(e,(()=>e.target instanceof HTMLElement?e.target:null))}),!0),j(o,"blur",(e=>u(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}(k,X,(e=>{e.preventDefault(),L()})),function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"!=typeof document?document.defaultView:null,n=arguments.length>2?arguments[2]:void 0,r=f(e,"escape");i(t,"keydown",(e=>{r&&(e.defaultPrevented||e.key===o.Escape&&n(e))}))}(k,null==T?void 0:T.defaultView,(e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),L()})),Z(!g&&!K&&k,T,X),function(e,t,n){let o=(0,l.Y)((e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&n()}));(0,r.useEffect)((()=>{if(!e)return;let n=null===t?null:t instanceof HTMLElement?t:t.current;if(!n)return;let r=(0,p.e)();if("undefined"!=typeof ResizeObserver){let e=new ResizeObserver((()=>o.current(n)));e.observe(n),r.add((()=>e.disconnect()))}if("undefined"!=typeof IntersectionObserver){let e=new IntersectionObserver((()=>o.current(n)));e.observe(n),r.add((()=>e.disconnect()))}return()=>r.dispose()}),[t,o,e])}(k,C,L);let[W,B]=function(){let[e,t]=(0,r.useState)([]);return[e.length>0?e.join(" "):void 0,(0,r.useMemo)((()=>function(e){let n=(0,m._)((e=>(t((t=>[...t,e])),()=>t((t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))))),o=(0,r.useMemo)((()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value})),[n,e.slot,e.name,e.props,e.value]);return r.createElement(ie.Provider,{value:o},e.children)}),[t])]}(),q=(0,r.useMemo)((()=>[{dialogState:S,close:L,setTitleId:x,unmount:E},A]),[S,A,L,x,E]),G=(0,r.useMemo)((()=>({open:0===S})),[S]),z={ref:P,id:u,role:v,tabIndex:-1,"aria-modal":g?void 0:0===S||void 0,"aria-labelledby":A.titleId,"aria-describedby":W,unmount:E},ee=!function(){var e;let[t]=(0,r.useState)((()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null)),[n,o]=(0,r.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,s.s)((()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){o(e.matches)}}),[t]),n}(),re=Ee.None;k&&!g&&(re|=Ee.RestoreFocus,re|=Ee.TabLock,h&&(re|=Ee.AutoFocus),ee&&(re|=Ee.InitialFocus));let le=(0,Y.Ci)();return r.createElement(ne.$x,null,r.createElement(oe,{force:!0},r.createElement(xe,null,r.createElement(Ie.Provider,{value:q},r.createElement(_e,{target:C},r.createElement(oe,{force:!1},r.createElement(B,{slot:G},r.createElement(M,null,r.createElement(be,{initialFocus:d,initialFocusFallback:C,containers:X,features:re},r.createElement(te,{value:L},le({ourProps:z,theirProps:w,slot:G,defaultTag:Ye,features:$e,visible:0===S,name:"Dialog"})))))))))))})),Ye="div",$e=Y.Ac.RenderStrategy|Y.Ac.Static;let Xe=(0,Y.FX)((function(e,t){let{transition:n=!1,open:o,...l}=e,i=(0,ne.O_)(),u=e.hasOwnProperty("open")||null!==i,a=e.hasOwnProperty("onClose");if(!u&&!a)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!u)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!a)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&"boolean"!=typeof e.open)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return void 0===o&&!n||l.static?r.createElement(W,null,r.createElement(Ue,{ref:t,open:o,...l})):r.createElement(W,null,r.createElement(ke.e,{show:o,transition:n,unmount:l.unmount},r.createElement(Ue,{ref:t,...l})))})),Ke=(0,Y.FX)((function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-dialog-panel-${n}`,transition:l=!1,...i}=e,[{dialogState:u,unmount:a},s]=He("Dialog.Panel"),c=(0,Q.P)(t,s.panelRef),d=(0,r.useMemo)((()=>({open:0===u})),[u]),f=(0,m._)((e=>{e.stopPropagation()})),p={ref:c,id:o,onClick:f},v=l?ke._:r.Fragment,h=l?{unmount:a}:{},g=(0,Y.Ci)();return r.createElement(v,{...h},g({ourProps:p,theirProps:i,slot:d,defaultTag:"div",name:"Dialog.Panel"}))})),We=((0,Y.FX)((function(e,t){let{transition:n=!1,...o}=e,[{dialogState:l,unmount:i}]=He("Dialog.Backdrop"),u=(0,r.useMemo)((()=>({open:0===l})),[l]),a={ref:t,"aria-hidden":!0},s=n?ke._:r.Fragment,c=n?{unmount:i}:{},d=(0,Y.Ci)();return r.createElement(s,{...c},d({ourProps:a,theirProps:o,slot:u,defaultTag:"div",name:"Dialog.Backdrop"}))})),(0,Y.FX)((function(e,t){let n=(0,r.useId)(),{id:o=`headlessui-dialog-title-${n}`,...l}=e,[{dialogState:i,setTitleId:u}]=He("Dialog.Title"),a=(0,Q.P)(t);(0,r.useEffect)((()=>(u(o),()=>u(null))),[o,u]);let s=(0,r.useMemo)((()=>({open:0===i})),[i]),c={ref:a,id:o};return(0,Y.Ci)()({ourProps:c,theirProps:l,slot:s,defaultTag:"h2",name:"Dialog.Title"})}))),Ve=Object.assign(Xe,{Panel:Ke,Title:We,Description:se})},6794:(e,t,n)=>{n.d(t,{$x:()=>s,El:()=>a,O_:()=>u,Uw:()=>i});var r=n(5043);let o=(0,r.createContext)(null);o.displayName="OpenClosedContext";var l,i=((l=i||{})[l.Open=1]="Open",l[l.Closed=2]="Closed",l[l.Closing=4]="Closing",l[l.Opening=8]="Opening",l);function u(){return(0,r.useContext)(o)}function a(e){let{value:t,children:n}=e;return r.createElement(o.Provider,{value:t},n)}function s(e){let{children:t}=e;return r.createElement(o.Provider,{value:null},t)}},7248:(e,t,n)=>{n.d(t,{s:()=>l});var r=n(5043),o=n(766);let l=(e,t)=>{o._.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},8518:(e,t,n)=>{n.d(t,{e:()=>o});var r=n(551);function o(){let e=[],t={addEventListener:(e,n,r,o)=>(e.addEventListener(n,r,o),t.add((()=>e.removeEventListener(n,r,o)))),requestAnimationFrame(){let e=requestAnimationFrame(...arguments);return t.add((()=>cancelAnimationFrame(e)))},nextFrame(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.requestAnimationFrame((()=>t.requestAnimationFrame(...n)))},setTimeout(){let e=setTimeout(...arguments);return t.add((()=>clearTimeout(e)))},microTask(){for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];let l={current:!0};return(0,r._)((()=>{l.current&&n[0]()})),t.add((()=>{l.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=o();return e(t),this.add((()=>t.dispose()))},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}},9358:(e,t,n)=>{n.d(t,{P:()=>u,a:()=>i});var r=n(5043),o=n(5962);let l=Symbol();function i(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Object.assign(e,{[l]:t})}function u(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];let i=(0,r.useRef)(t);(0,r.useEffect)((()=>{i.current=t}),[t]);let u=(0,o._)((e=>{for(let t of i.current)null!=t&&("function"==typeof t?t(e):t.current=e)}));return t.every((e=>null==e||(null==e?void 0:e[l])))?void 0:u}}}]);
//# sourceMappingURL=443.e8ca9758.chunk.js.map