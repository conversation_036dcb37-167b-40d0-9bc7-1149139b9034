{"version": 3, "file": "static/js/372.500245d5.chunk.js", "mappings": "0MA+BA,MA8WA,EA9WsCA,KACpC,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAA+B,KACxDC,EAASC,IAAcF,EAAAA,EAAAA,WAAkB,IACzCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAC3CK,EAAiBC,IAAsBN,EAAAA,EAAAA,UAAoC,OAC3EO,EAAkBC,IAAuBR,EAAAA,EAAAA,UAAqB,OAG9DS,EAAYC,IAAiBV,EAAAA,EAAAA,UAAS,CAC3CW,KAAM,SACNC,QAAS,GACTC,SAAU,CACRC,gBAAiB,UACjBC,UAAW,UACXC,aAAc,IACdC,KAAM,SACNC,UAAW,WAKRC,EAAaC,IAAkBpB,EAAAA,EAAAA,WAAkB,IAGxDqB,EAAAA,EAAAA,YAAU,KACcC,WACpBpB,GAAW,GACXE,EAAS,MACT,IACE,MAAMmB,QAAiBC,EAAAA,GAAmBC,cACtCF,EAASG,QACX3B,EAAYwB,EAASI,MAErBvB,EAASmB,EAASK,SAAW,uCAEjC,CAAE,MAAOC,GACPzB,EAASyB,EAAID,SAAW,4CAC1B,CAAC,QACC1B,GAAW,EACb,GAGF4B,EAAe,GACd,KAGHT,EAAAA,EAAAA,YAAU,KACR,IAAKhB,EAED,YADAG,EAAoB,MAIMc,WAC5B,IACE,MAAMC,QAAiBC,EAAAA,GAAmBO,oBAAoB1B,EAAgB2B,IAC1ET,EAASG,QACXlB,EAAoBe,EAASI,OAE1BM,QAAQ9B,MAAM,qCAAsCoB,EAASK,SAC7DpB,EAAoB,MAE3B,CAAE,MAAOqB,GACPI,QAAQ9B,MAAM,oCAAqC0B,GACnDrB,EAAoB,KACtB,GAGF0B,EAAuB,GACtB,CAAC7B,IA4HJ,OAEI8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACpDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SAAC,kCAG5DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,gGAIvClC,IACEmC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAAC5B,KAAK,QAAQiB,QAASzB,EAAOqC,QAASA,IAAMpC,EAAS,MAAOgC,UAAU,UAGhFD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,CAAC,KAErDF,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACL,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,wBACtFF,EAAAA,EAAAA,MAAA,QAAMO,SA5IYpB,UAG1B,GAFAqB,EAAEC,iBAEGnC,EAAWG,QAAQiC,OAAxB,CAKA3C,GAAW,GACXE,EAAS,MAET,IACE,MAAMmB,QAAiBC,EAAAA,GAAmBsB,cACxCrC,EAAWE,KACXF,EAAWG,QACXH,EAAWI,UAGTU,EAASG,SAEX3B,EAAY,IAAID,EAAUyB,EAASI,OAEnCjB,EAAc,CACZC,KAAM,SACNC,QAAS,GACTC,SAAU,CACRC,gBAAiB,UACjBC,UAAW,UACXC,aAAc,IACdC,KAAM,SACNC,UAAW,WAIfd,EAASmB,EAASK,SAAW,uCAEjC,CAAE,MAAOC,GACPzB,EAASyB,EAAID,SAAW,2CAC1B,CAAC,QACC1B,GAAW,EACb,CAlCA,MAFEE,EAAS,8BAoCX,EAoG6CgC,UAAU,gBAAeC,SAAA,EAE5DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAOS,QAAQ,cAAcX,UAAU,qDAAoDC,SAAC,kBAC5FF,EAAAA,EAAAA,MAAA,UACGH,GAAG,cACHgB,KAAK,cACLC,MAAOxC,EAAWE,KAClBuC,SAAWP,GAAsCjC,EAAc,IAAKD,EAAYE,KAAMgC,EAAEQ,OAAOF,QAC/Fb,UAAU,6KAA4KC,SAAA,EAEpLC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,SAAQZ,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,YAAWZ,SAAC,qBAC1BC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,OAAMZ,SAAC,gBAI9BC,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACHC,MAAM,UACNrB,GAAG,iBACHgB,KAAK,iBACLC,MAAOxC,EAAWG,QAClBsC,SAAWP,GAAqCjC,EAAc,IAAKD,EAAYG,QAAS+B,EAAEQ,OAAOF,QACjGK,YAAiC,WAApB7C,EAAWE,KAAoB,aAAmC,cAApBF,EAAWE,KAAuB,eAAiB,qBAC9G4C,UAAQ,KAIVjB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,cAG7DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAOS,QAAQ,kBAAkBX,UAAU,qDAAoDC,SAAC,sBAChGC,EAAAA,EAAAA,KAAA,SACG3B,KAAK,QACLqB,GAAG,kBACHgB,KAAK,kBACLC,MAAOxC,EAAWI,SAASC,gBAC3BoC,SAAWP,GAAqCjC,EAAc,IAAKD,EAAYI,SAAU,IAAKJ,EAAWI,SAAUC,gBAAiB6B,EAAEQ,OAAOF,SAC7Ib,UAAU,qGAGfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,SAAOS,QAAQ,YAAYX,UAAU,qDAAoDC,SAAC,gBAC1FC,EAAAA,EAAAA,KAAA,SACG3B,KAAK,QACLqB,GAAG,YACHgB,KAAK,YACLC,MAAOxC,EAAWI,SAASE,UAC3BmC,SAAWP,GAAqCjC,EAAc,IAAKD,EAAYI,SAAU,IAAKJ,EAAWI,SAAUE,UAAW4B,EAAEQ,OAAOF,SACvIb,UAAU,qGAGhBE,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACLC,MAAM,qBACNrB,GAAG,eACHgB,KAAK,eACLrC,KAAK,SACLsC,MAAOxC,EAAWI,SAASG,aAC3BkC,SAAWP,GAAqCjC,EAAc,IAAKD,EAAYI,SAAU,IAAKJ,EAAWI,SAAUG,aAAc2B,EAAEQ,OAAOF,YAG3Id,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,SAAOS,QAAQ,OAAOX,UAAU,qDAAoDC,SAAC,UACrFF,EAAAA,EAAAA,MAAA,UACGH,GAAG,OACHgB,KAAK,OACLC,MAAOxC,EAAWI,SAASI,KAC3BiC,SAAWP,GAAsCjC,EAAc,IAAKD,EAAYI,SAAU,IAAKJ,EAAWI,SAAUI,KAAM0B,EAAEQ,OAAOF,SACnIb,UAAU,6KAA4KC,SAAA,EAEtLC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,QAAOZ,SAAC,WACtBC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,SAAQZ,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,QAAOZ,SAAC,iBAG3BF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,SAAOS,QAAQ,YAAYX,UAAU,qDAAoDC,SAAC,eAC1FF,EAAAA,EAAAA,MAAA,UACGH,GAAG,YACHgB,KAAK,YACLC,MAAOxC,EAAWI,SAASK,UAC3BgC,SAAWP,GAAsCjC,EAAc,IAAKD,EAAYI,SAAU,IAAKJ,EAAWI,SAAUK,UAAWyB,EAAEQ,OAAOF,SACxIb,UAAU,6KAA4KC,SAAA,EAEtLC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,OAAMZ,SAAC,UACrBC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,QAAOZ,SAAC,WACtBC,EAAAA,EAAAA,KAAA,UAAQW,MAAM,SAAQZ,SAAC,kBAKjCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,KAACkB,EAAAA,EAAM,CAAC7C,KAAK,SAAS8C,SAAUxD,EAAQoC,SACrCpC,EAAU,cAAgB,4BAOpCkC,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACL,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,sBACpFpC,GAA+B,IAApBH,EAAS4D,QACjBpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,wBACjC,IAApBvC,EAAS4D,QACTpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,0CAEtDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oDAAmDC,SAAA,CAAC,IAC/DvC,EAAS6D,KAAKC,IACbzB,EAAAA,EAAAA,MAAA,MAEEC,UAAW,0CAAuD,OAAf/B,QAAe,IAAfA,OAAe,EAAfA,EAAiB2B,MAAO4B,EAAQ5B,GAAK,cAAgB,IACxG6B,QAASA,IAjNGD,KAC9BtD,EAAmBsD,EAAQ,EAgNME,CAAuBF,GAASvB,SAAA,EAE9CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iDAAgDC,SAAEuB,EAAQhD,WACvEuB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,8BAA6BC,SAAA,CAAC,SAAOuB,EAAQjD,UALtDiD,EAAQ5B,aAazBG,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACL,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,yBACrFC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACjBhC,GACC8B,EAAAA,EAAAA,MAAA4B,EAAAA,SAAA,CAAA1B,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,kBAC7DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0GAAyGC,SA7N3G2B,KAC5B,IAAKA,EAAiB,OAAO,KAE7B,MAAM,KAAErD,EAAI,QAAEC,EAAO,SAAEC,GAAamD,EAoB9BC,EAAiB,IAlBiB,CACtCnD,gBAAiBD,EAASC,iBAAmB,UAC7CoD,MAAOrD,EAASE,WAAa,UAC7BC,aAAc,GAAGH,EAASG,cAAgB,MAC1CmD,QAA2B,UAAlBtD,EAASI,KAAmB,UAA8B,UAAlBJ,EAASI,KAAmB,YAAc,WAC3FmD,OAAQ,OACRC,OAAQ,UACRC,WAAY,OACZC,WAAY,oBAIsD,UAAvB1D,EAASK,UAClD,CAAEA,UAAW,qBACU,WAAvBL,EAASK,UACP,CAAEA,UAAW,sBACb,CAAC,GAIP,OAAQP,GACN,IAAK,SACH,OACE2B,EAAAA,EAAAA,KAAA,UAAQkC,MAAOP,EAAe5B,SAC3BzB,IAGP,IAAK,YACH,OACEuB,EAAAA,EAAAA,MAAA,OAAKqC,MAAO,CAAEC,UAAW,SAAUN,QAAS,OAAQC,OAAQ,oBAAqBpD,aAAc,MAAOkD,MAAOrD,EAASE,WAAYsB,SAAA,EAChIC,EAAAA,EAAAA,KAAA,OAAKkC,MAAO,CAAEE,SAAU,OAAQC,aAAc,OAAQtC,SAAEzB,KACxDuB,EAAAA,EAAAA,MAAA,OAAKqC,MAAO,CAAEI,QAAS,OAAQC,eAAgB,SAAUC,IAAK,QAASzC,SAAA,EACrEF,EAAAA,EAAAA,MAAA,OAAKqC,MAAO,CAAEL,QAAS,MAAOrD,gBAAiBD,EAASC,gBAAiBoD,MAAOrD,EAASE,UAAWC,aAAc,GAAGH,EAASG,iBAAkB+D,SAAU,QAAS1C,SAAA,CAAC,OAC/JC,EAAAA,EAAAA,KAAA,QAAMkC,MAAO,CAACE,SAAU,OAAQE,QAAS,SAASvC,SAAC,aAExDF,EAAAA,EAAAA,MAAA,OAAKqC,MAAO,CAAEL,QAAS,MAAOrD,gBAAiBD,EAASC,gBAAiBoD,MAAOrD,EAASE,UAAWC,aAAc,GAAGH,EAASG,iBAAkB+D,SAAU,QAAS1C,SAAA,CAAC,OAC/JC,EAAAA,EAAAA,KAAA,QAAMkC,MAAO,CAACE,SAAU,OAAQE,QAAS,SAASvC,SAAC,cAExDF,EAAAA,EAAAA,MAAA,OAAKqC,MAAO,CAAEL,QAAS,MAAOrD,gBAAiBD,EAASC,gBAAiBoD,MAAOrD,EAASE,UAAWC,aAAc,GAAGH,EAASG,iBAAkB+D,SAAU,QAAS1C,SAAA,CAAC,OAC/JC,EAAAA,EAAAA,KAAA,QAAMkC,MAAO,CAACE,SAAU,OAAQE,QAAS,SAASvC,SAAC,kBAKhE,IAAK,OACH,OACEF,EAAAA,EAAAA,MAAA,OAAKqC,MAAO,CAAEJ,OAAQ,oBAAqBpD,aAAc,MAAOmD,QAAS,OAAQD,MAAOrD,EAASE,WAAYsB,SAAA,EAC3GC,EAAAA,EAAAA,KAAA,OAAKkC,MAAO,CAAEE,SAAU,OAAQJ,WAAY,OAAQK,aAAc,QAAStC,SAAEzB,KAC7E0B,EAAAA,EAAAA,KAAA,OAAKkC,MAAO,CAAEI,QAAS,OAAQI,cAAe,SAAUF,IAAK,OAAQzC,SAClE,CAAC,WAAY,WAAY,YAAYsB,KAAI,CAACsB,EAAQC,KACjD/C,EAAAA,EAAAA,MAAA,OAAiBqC,MAAO,CAAEI,QAAS,OAAQO,WAAY,SAAUL,IAAK,OAAQzC,SAAA,EAC1EC,EAAAA,EAAAA,KAAA,SAAO3B,KAAK,QAAQqB,GAAI,eAAekD,IAASlC,KAAM,QAAQgB,EAAgBhC,KAAMyB,UAAQ,KAC5FnB,EAAAA,EAAAA,KAAA,SAAOS,QAAS,eAAemC,IAAQ7C,SAAE4C,MAFnCC,QAMd5C,EAAAA,EAAAA,KAAA,UAAQkC,MAAO,IAAKP,EAAgBmB,UAAW,OAAQV,SAAU,QAAUjB,UAAQ,EAAApB,SAAC,cAK1F,QACE,OAAOC,EAAAA,EAAAA,KAAA,OAAAD,SAAMzB,IACjB,EA0JmByE,CAAqBhF,MAEzB8B,EAAAA,EAAAA,MAAA,MAAIC,UAAU,+CAA8CC,SAAA,CAAC,kBAAgBhC,EAAgBO,QAAQ,OACnGL,GACG4B,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wCAAuCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAQ9B,EAAiB+E,OAAS,MACtCnD,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,wBAAsB9B,EAAiBgF,cAAgB,MAC3DpD,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,oBAAkB9B,EAAiBiF,gBAAkB,EAAE,WAG/DlD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAoCC,SAAC,6BAIvDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCAAwCC,SAAC,4EAMpE,C", "sources": ["pages/InteractiveElements.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  FormEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button'; // Assuming components exist\nimport Card from '../components/Card';\nimport Input from '../components/Input';\n// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport { interactiveService } from '../services';\n\n// Define interfaces if needed\ninterface ElementSettings {\n  backgroundColor: string;\n  textColor: string;\n  borderRadius: string;\n  size: 'small' | 'medium' | 'large';\n  animation: 'none' | 'pulse' | 'bounce';\n}\n\ninterface InteractiveElement {\n  id: string;\n  type: string;\n  content: string;\n  settings: ElementSettings;\n  // Add other properties if they exist\n}\n\nconst InteractiveElements: React.FC = () => {\n  const [elements, setElements] = useState<InteractiveElement[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedElement, setSelectedElement] = useState<InteractiveElement | null>(null);\n  const [elementAnalytics, setElementAnalytics] = useState<any | null>(null); // Keep any for now or define specific analytics type\n  \n  // New element form state\n  const [newElement, setNewElement] = useState({\n    type: 'button',\n    content: '',\n    settings: {\n      backgroundColor: '#3b82f6',\n      textColor: '#ffffff',\n      borderRadius: '4',\n      size: 'medium',\n      animation: 'none'\n    } as ElementSettings\n  });\n  \n  // Preview state\n  const [showPreview, setShowPreview] = useState<boolean>(false);\n\n  // Fetch elements on component mount\n  useEffect(() => {\n    const fetchElements = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const response = await interactiveService.getElements();\n        if (response.success) {\n          setElements(response.data);\n        } else {\n          setError(response.message || 'Failed to fetch interactive elements');\n        }\n      } catch (err: any) {\n        setError(err.message || 'An error occurred while fetching elements');\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    fetchElements();\n  }, []);\n  \n  // Fetch element analytics when an element is selected\n  useEffect(() => {\n    if (!selectedElement) {\n        setElementAnalytics(null);\n        return;\n    }\n    \n    const fetchElementAnalytics = async () => {\n      try {\n        const response = await interactiveService.getElementAnalytics(selectedElement.id);\n        if (response.success) {\n          setElementAnalytics(response.data);\n        } else {\n             console.error('Failed to fetch element analytics:', response.message);\n             setElementAnalytics(null);\n        }\n      } catch (err) {\n        console.error('Error fetching element analytics:', err);\n        setElementAnalytics(null);\n      }\n    };\n    \n    fetchElementAnalytics();\n  }, [selectedElement]);\n  \n  // Handle creating a new element\n  const handleCreateElement = async (e: FormEvent) => {\n    e.preventDefault();\n    \n    if (!newElement.content.trim()) {\n      setError('Element content is required');\n      return;\n    }\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await interactiveService.createElement(\n        newElement.type,\n        newElement.content,\n        newElement.settings\n      );\n      \n      if (response.success) {\n        // Add new element to state\n        setElements([...elements, response.data]);\n        // Reset form\n        setNewElement({\n          type: 'button',\n          content: '',\n          settings: {\n            backgroundColor: '#3b82f6',\n            textColor: '#ffffff',\n            borderRadius: '4',\n            size: 'medium',\n            animation: 'none'\n          } as ElementSettings\n        });\n      } else {\n        setError(response.message || 'Failed to create interactive element');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating element');\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  // Handle element selection\n  const handleElementSelection = (element: InteractiveElement) => {\n    setSelectedElement(element);\n  };\n  \n  // Render element preview based on type and settings\n  const renderElementPreview = (elementToRender: InteractiveElement | null) => {\n    if (!elementToRender) return null;\n    \n    const { type, content, settings } = elementToRender;\n    \n    const baseStyles: React.CSSProperties = {\n      backgroundColor: settings.backgroundColor || '#3b82f6',\n      color: settings.textColor || '#ffffff',\n      borderRadius: `${settings.borderRadius || 4}px`,\n      padding: settings.size === 'small' ? '4px 8px' : settings.size === 'large' ? '12px 24px' : '8px 16px',\n      border: 'none',\n      cursor: 'pointer',\n      fontWeight: 'bold',\n      transition: 'all 0.3s ease'\n    };\n    \n    // Add animation styles\n    const animationStyles: React.CSSProperties = settings.animation === 'pulse' \n      ? { animation: 'pulse 2s infinite' }\n      : settings.animation === 'bounce' \n        ? { animation: 'bounce 1s infinite' }\n        : {};\n    \n    const combinedStyles = { ...baseStyles, ...animationStyles };\n    \n    switch (type) {\n      case 'button':\n        return (\n          <button style={combinedStyles}>\n            {content}\n          </button>\n        );\n      case 'countdown':\n        return (\n          <div style={{ textAlign: 'center', padding: '10px', border: '1px solid #4b5563', borderRadius: '4px', color: settings.textColor }}>\n            <div style={{ fontSize: '14px', marginBottom: '5px' }}>{content}</div>\n            <div style={{ display: 'flex', justifyContent: 'center', gap: '10px' }}>\n              <div style={{ padding: '8px', backgroundColor: settings.backgroundColor, color: settings.textColor, borderRadius: `${settings.borderRadius}px`, minWidth: '40px' }}>\n                00 <span style={{fontSize: '10px', display: 'block'}}>Days</span>\n              </div>\n              <div style={{ padding: '8px', backgroundColor: settings.backgroundColor, color: settings.textColor, borderRadius: `${settings.borderRadius}px`, minWidth: '40px' }}>\n                00 <span style={{fontSize: '10px', display: 'block'}}>Hours</span>\n              </div>\n              <div style={{ padding: '8px', backgroundColor: settings.backgroundColor, color: settings.textColor, borderRadius: `${settings.borderRadius}px`, minWidth: '40px' }}>\n                00 <span style={{fontSize: '10px', display: 'block'}}>Mins</span>\n              </div>\n            </div>\n          </div>\n        );\n      case 'poll':\n        return (\n          <div style={{ border: '1px solid #4b5563', borderRadius: '4px', padding: '10px', color: settings.textColor }}>\n            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '10px' }}>{content}</div>\n            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n              {['Option 1', 'Option 2', 'Option 3'].map((option, index) => (\n                <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                    <input type=\"radio\" id={`poll-option-${index}`} name={`poll-${elementToRender.id}`} disabled />\n                    <label htmlFor={`poll-option-${index}`}>{option}</label>\n                </div>\n              ))}\n            </div>\n            <button style={{ ...combinedStyles, marginTop: '10px', fontSize: '14px' }} disabled>\n              Submit\n            </button>\n          </div>\n        );\n      default:\n        return <div>{content}</div>;\n    }\n  };\n\n  // Remove Sidebar wrapper\n  return (\n    // <Sidebar> // Removed\n      <div className=\"container mx-auto px-4 py-6\"> \n        <div className=\"flex justify-between items-center mb-6\">\n           <h1 className=\"text-2xl font-semibold text-text-primary\">Interactive Email Elements</h1>\n           {/* Add button or other actions if needed */}\n        </div>\n        <p className=\"text-text-secondary mb-6\">\n            Create and manage interactive elements like buttons, polls, and countdowns for your emails.\n        </p>\n        \n        {error && (\n           <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n        )}\n        \n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\"> {/* Adjust grid layout */} \n          {/* Create new element form - Column 1 */} \n          <Card className=\"lg:col-span-1\"> \n             <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Create New Element</h2>\n            <form onSubmit={handleCreateElement} className=\"p-4 space-y-4\">\n              {/* Use standard select element */}\n              <div>\n                  <label htmlFor=\"elementType\" className=\"block text-sm font-medium text-text-secondary mb-1\">Element Type</label>\n                  <select\n                     id=\"elementType\"\n                     name=\"elementType\"\n                     value={newElement.type}\n                     onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewElement({ ...newElement, type: e.target.value })}\n                     className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                  >\n                       <option value=\"button\">Button</option>\n                       <option value=\"countdown\">Countdown Timer</option>\n                       <option value=\"poll\">Poll</option>\n                  </select>\n              </div>\n              \n              <Input\n                 label=\"Content\"\n                 id=\"elementContent\"\n                 name=\"elementContent\"\n                 value={newElement.content}\n                 onChange={(e: ChangeEvent<HTMLInputElement>) => setNewElement({ ...newElement, content: e.target.value })}\n                 placeholder={newElement.type === 'button' ? 'Click Here' : newElement.type === 'countdown' ? 'Sale Ends In' : 'What do you think?'}\n                 required\n              />\n              \n              {/* --- Settings Sub-form --- */} \n               <h3 className=\"text-md font-medium text-text-secondary pt-2\">Settings</h3>\n               \n               {/* Use standard input type=\"color\" */}\n               <div>\n                   <label htmlFor=\"backgroundColor\" className=\"block text-sm font-medium text-text-secondary mb-1\">Background Color</label>\n                   <input\n                      type=\"color\"\n                      id=\"backgroundColor\"\n                      name=\"backgroundColor\"\n                      value={newElement.settings.backgroundColor}\n                      onChange={(e: ChangeEvent<HTMLInputElement>) => setNewElement({ ...newElement, settings: { ...newElement.settings, backgroundColor: e.target.value } })}\n                      className=\"block w-full h-10 px-1 py-1 border border-gray-600 rounded-md cursor-pointer bg-secondary-bg\"\n                   />\n                </div>\n                 <div>\n                    <label htmlFor=\"textColor\" className=\"block text-sm font-medium text-text-secondary mb-1\">Text Color</label>\n                    <input\n                       type=\"color\"\n                       id=\"textColor\"\n                       name=\"textColor\"\n                       value={newElement.settings.textColor}\n                       onChange={(e: ChangeEvent<HTMLInputElement>) => setNewElement({ ...newElement, settings: { ...newElement.settings, textColor: e.target.value } })}\n                       className=\"block w-full h-10 px-1 py-1 border border-gray-600 rounded-md cursor-pointer bg-secondary-bg\"\n                    />\n                 </div>\n                 <Input\n                  label=\"Border Radius (px)\"\n                  id=\"borderRadius\"\n                  name=\"borderRadius\"\n                  type=\"number\"\n                  value={newElement.settings.borderRadius}\n                  onChange={(e: ChangeEvent<HTMLInputElement>) => setNewElement({ ...newElement, settings: { ...newElement.settings, borderRadius: e.target.value } })}\n                 />\n                 {/* Use standard select element */}\n                 <div>\n                    <label htmlFor=\"size\" className=\"block text-sm font-medium text-text-secondary mb-1\">Size</label>\n                    <select\n                       id=\"size\"\n                       name=\"size\"\n                       value={newElement.settings.size}\n                       onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewElement({ ...newElement, settings: { ...newElement.settings, size: e.target.value as ElementSettings['size'] } })}\n                       className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                    >\n                       <option value=\"small\">Small</option>\n                       <option value=\"medium\">Medium</option>\n                       <option value=\"large\">Large</option>\n                    </select>\n                 </div>\n                  <div>\n                     <label htmlFor=\"animation\" className=\"block text-sm font-medium text-text-secondary mb-1\">Animation</label>\n                     <select\n                        id=\"animation\"\n                        name=\"animation\"\n                        value={newElement.settings.animation}\n                        onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewElement({ ...newElement, settings: { ...newElement.settings, animation: e.target.value as ElementSettings['animation'] } })}\n                        className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                     >\n                        <option value=\"none\">None</option>\n                        <option value=\"pulse\">Pulse</option>\n                        <option value=\"bounce\">Bounce</option>\n                     </select>\n                  </div>\n              {/* --- End Settings --- */} \n                \n              <div className=\"flex justify-end pt-2\">\n                 <Button type=\"submit\" disabled={loading}>\n                   {loading ? 'Creating...' : 'Create Element'}\n                 </Button>\n              </div>\n            </form>\n          </Card>\n          \n          {/* Existing Elements List - Column 2 */} \n          <Card className=\"lg:col-span-1\"> \n             <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Existing Elements</h2>\n             {loading && elements.length === 0 ? (\n                 <div className=\"p-4 text-center text-text-secondary\">Loading elements...</div>\n             ) : elements.length === 0 ? (\n                 <div className=\"p-4 text-center text-text-secondary\">No interactive elements created yet.</div>\n             ) : (\n                <ul className=\"divide-y divide-gray-700 max-h-96 overflow-y-auto\"> {/* Add max height and scroll */} \n                  {elements.map((element) => (\n                    <li \n                      key={element.id} \n                      className={`p-4 hover:bg-gray-700 cursor-pointer ${selectedElement?.id === element.id ? 'bg-gray-700' : ''}`}\n                      onClick={() => handleElementSelection(element)}\n                    >\n                       <p className=\"text-sm font-medium text-text-primary truncate\">{element.content}</p>\n                       <p className=\"text-xs text-text-secondary\">Type: {element.type}</p>\n                     </li>\n                  ))}\n                </ul>\n             )}\n          </Card>\n\n          {/* Preview & Analytics - Column 3 */} \n          <Card className=\"lg:col-span-1\"> \n             <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Preview & Analytics</h2>\n             <div className=\"p-4\">\n               {selectedElement ? (\n                 <>\n                   <h3 className=\"text-md font-medium text-text-secondary mb-2\">Live Preview</h3>\n                   <div className=\"mb-4 p-4 border border-dashed border-gray-600 rounded-md flex justify-center items-center min-h-[100px]\">\n                      {renderElementPreview(selectedElement)} \n                    </div>\n                   <h3 className=\"text-md font-medium text-text-secondary mb-2\">Analytics for \"{selectedElement.content}\"</h3>\n                    {elementAnalytics ? (\n                        <ul className=\"text-sm text-text-secondary space-y-1\">\n                           <li>Views: {elementAnalytics.views || 0}</li>\n                           <li>Clicks/Interactions: {elementAnalytics.interactions || 0}</li>\n                           <li>Conversion Rate: {elementAnalytics.conversionRate || 0}%</li>\n                        </ul>\n                    ) : (\n                       <p className=\"text-sm text-text-secondary italic\">Loading analytics...</p>\n                    )}\n                 </>\n               ) : (\n                  <p className=\"text-text-secondary italic text-center\">Select an element from the list to see preview and analytics.</p>\n               )}\n             </div>\n          </Card>\n        </div>\n      </div>\n    // </Sidebar> // Removed\n  );\n};\n\nexport default InteractiveElements;\n"], "names": ["InteractiveElements", "elements", "setElements", "useState", "loading", "setLoading", "error", "setError", "selectedElement", "setSelectedElement", "elementAnalytics", "setElementAnalytics", "newElement", "setNewElement", "type", "content", "settings", "backgroundColor", "textColor", "borderRadius", "size", "animation", "showPreview", "setShowPreview", "useEffect", "async", "response", "interactiveService", "getElements", "success", "data", "message", "err", "fetchElements", "getElementAnalytics", "id", "console", "fetchElementAnalytics", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON>", "onClose", "Card", "onSubmit", "e", "preventDefault", "trim", "createElement", "htmlFor", "name", "value", "onChange", "target", "Input", "label", "placeholder", "required", "<PERSON><PERSON>", "disabled", "length", "map", "element", "onClick", "handleElementSelection", "_Fragment", "elementToRender", "combinedStyles", "color", "padding", "border", "cursor", "fontWeight", "transition", "style", "textAlign", "fontSize", "marginBottom", "display", "justifyContent", "gap", "min<PERSON><PERSON><PERSON>", "flexDirection", "option", "index", "alignItems", "marginTop", "renderElementPreview", "views", "interactions", "conversionRate"], "sourceRoot": ""}