import {
  NextFunction,
  Request,
  Response,
} from 'express';
import jwt from 'jsonwebtoken';

// REMOVE AWS SDK imports
// import {
//   GetSecretValueCommand,
//   SecretsManagerClient,
// } from '@aws-sdk/client-secrets-manager'; 
import { createError } from '../utils/error.util';

// REMOVE Secrets Manager client initialization
// const secretsClient = new SecretsManagerClient({ region: process.env.AWS_REGION || "us-east-2" });

// Cache for the secret (still potentially useful)
let cachedJwtSecret: string | null = null;

// Async function to retrieve the secret - MODIFIED
export async function getJwtSecret(): Promise<string> { 
  if (cachedJwtSecret) {
    return cachedJwtSecret;
  }
  
  const jwtSecretFromEnv = process.env.JWT_SECRET;

  if (!jwtSecretFromEnv) {
    console.error('Authentication Error: JWT_SECRET environment variable is not set.');
    throw new Error("Configuration error preventing authentication.");
  }

  cachedJwtSecret = jwtSecretFromEnv;
  console.log("Successfully retrieved JWT_SECRET from environment variable.");
  return cachedJwtSecret;
}

// Interface for decoded JWT token (adjust if your payload is different)
interface JwtPayload {
  id: string;
  email: string;
  role: string;
}

// Extend Express Request type if you haven't defined req.userId elsewhere globally
// Make sure this matches how you access the user ID in your controllers (req.userId or req.user.id)
declare global {
  namespace Express {
    interface Request {
      // userId?: string; // Keep commented or remove if not used
      user?: { // Update type definition
        id: string;
        email: string;
        role: string;
      };
    }
  }
}


export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  // --- Start Auth Debug Logging ---
  console.log(`[AuthMiddleware] Processing request for: ${req.originalUrl}`);
  const authHeader = req.headers.authorization;
  console.log(`[AuthMiddleware] Authorization header present: ${!!authHeader}`);
  // --- End Auth Debug Logging ---

  try {
    let token;
    if (authHeader && authHeader.startsWith('Bearer')) {
      token = authHeader.split(' ')[1];
      // console.log(`[AuthMiddleware] Extracted Token: ${token}`); // Optional: Log token only if needed for deep debug
    }

    if (!token) {
      console.log('[AuthMiddleware] No token found.');
      return next(createError(401, 'Not authorized, no token'));
    }

    console.log('[AuthMiddleware] Attempting to get JWT secret...');
    const secret = await getJwtSecret(); 

    if (!secret) {
      console.error("[AuthMiddleware] JWT Secret was unexpectedly null/undefined after retrieval.");
      return next(createError(500, "Authentication configuration error."));
    }
    console.log('[AuthMiddleware] JWT Secret obtained. Verifying token...');

    // Verify token
    const decoded = jwt.verify(token, secret) as JwtPayload;
    console.log(`[AuthMiddleware] Token verified successfully. Decoded user ID: ${decoded.id}`);

    // Attach user object to request
    req.user = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role
    };

    console.log('[AuthMiddleware] User attached to request. Calling next().');
    next();

  } catch (error: any) {
    // --- Start Auth Error Debug Logging ---
    console.error(`[AuthMiddleware] Error processing request for ${req.originalUrl}:`, error.name, error.message);
    // --- End Auth Error Debug Logging ---

    console.error("Authentication Error:", error.message); // Log specific error internally
    if (error.message === "Configuration error preventing authentication.") {
         return next(createError(500, "Authentication configuration error."));
    } else if (error.name === 'JsonWebTokenError') {
         return next(createError(401, 'Not authorized, token failed'));
    } else if (error.name === 'TokenExpiredError') {
         return next(createError(401, 'Not authorized, token expired'));
    } else {
         return next(createError(500, "Authentication failed."));
    }
  }
};