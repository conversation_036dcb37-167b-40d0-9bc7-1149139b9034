import mongoose, {
  Document,
  Schema,
} from 'mongoose';

import { IContact } from '../types';

const ContactSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email']
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  isSubscribed: {
    type: Boolean,
    default: true
  },
  consentedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Compound index to ensure a contact email is unique per user
ContactSchema.index({ userId: 1, email: 1 }, { unique: true });

export default mongoose.model<IContact & Document>('Contact', ContactSchema);
