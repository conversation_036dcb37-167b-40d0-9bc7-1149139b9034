"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[793],{9793:(e,s,a)=>{a.r(s),a.d(s,{default:()=>r});a(5043);var t=a(552),n=a(579);const r=()=>(0,n.jsx)("div",{className:"p-4 md:p-6 lg:p-8 bg-primary-bg text-text-primary min-h-screen",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-text-primary mb-6",children:"AI Template Recommendations"}),(0,n.jsxs)(t.Zp,{className:"bg-secondary-bg border border-border-color p-6",children:[(0,n.jsx)("p",{className:"text-text-secondary mb-4",children:"This feature is under development. The AI Template Recommendations system will suggest email templates based on your campaign goals and audience."}),(0,n.jsx)(t.Fc,{type:"info",message:"Coming soon! Check back for updates on this feature.",className:"mt-4"})]})]})})}}]);
//# sourceMappingURL=793.de72ee1e.chunk.js.map