{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\MjmlEditor.tsx\",\n  _s = $RefreshSig$();\nimport 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\n\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';\nimport grapesjs from 'grapesjs';\n// @ts-ignore - grapesjs-mjml lacks official types\nimport grapesjsMjml from 'grapesjs-mjml';\n\n// Define the Ref type for exposing editor methods\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MjmlEditor = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  initialMjml = '',\n  initialHtml = '',\n  onSave,\n  height = '70vh'\n}, ref) => {\n  _s();\n  const editorRef = useRef(null);\n  const grapesEditor = useRef(null);\n\n  // Initialize GrapesJS Editor\n  useEffect(() => {\n    if (!editorRef.current) return; // Check for DOM element\n\n    // Always destroy and recreate the editor to ensure consistent behavior\n    if (grapesEditor.current) {\n      console.log(\"[MjmlEditor] Cleaning up previous editor instance\");\n      grapesEditor.current.destroy();\n      grapesEditor.current = null;\n    }\n    console.log(\"[MjmlEditor] Initializing editor with props:\", {\n      hasMjml: !!initialMjml,\n      mjmlLength: (initialMjml === null || initialMjml === void 0 ? void 0 : initialMjml.length) || 0,\n      hasHtml: !!initialHtml,\n      htmlLength: (initialHtml === null || initialHtml === void 0 ? void 0 : initialHtml.length) || 0\n    });\n    try {\n      const editor = grapesjs.init({\n        container: editorRef.current,\n        fromElement: false,\n        // Don't load from existing HTML/CSS in the container\n        height: String(height),\n        width: 'auto',\n        storageManager: false,\n        // Disable default storage manager\n        plugins: [grapesjsMjml],\n        pluginsOpts: {\n          'grapesjs-mjml': {\n            // MJML plugin options (optional)\n            // columnsPadding: '0px',\n            useXmlParser: true,\n            // Use the faster XML parser\n            resetBlocks: false // Try keeping default GrapesJS blocks\n            // ... other options\n          }\n        }\n        // Optional: Configure panels, blocks, styles etc.\n      });\n\n      // Make sure the editor was initialized properly\n      if (!editor) {\n        console.error(\"[MjmlEditor] Failed to initialize editor\");\n        return;\n      }\n      grapesEditor.current = editor;\n\n      // Register missing commands that are expected by the editor\n      if (!editor.Commands.has('mjml-get-code')) {\n        console.log(\"[MjmlEditor] Registering missing mjml-get-code command\");\n        editor.Commands.add('mjml-get-code', {\n          run: editor => {\n            const mjml = editor.getHtml();\n            // Simple implementation for missing command\n            return {\n              mjml: mjml,\n              html: mjml // We'll process this later if needed\n            };\n          }\n        });\n      }\n      if (!editor.Commands.has('gjs-get-html')) {\n        console.log(\"[MjmlEditor] Registering missing gjs-get-html command\");\n        editor.Commands.add('gjs-get-html', {\n          run: editor => {\n            return editor.getHtml({\n              component: editor.getWrapper()\n            });\n          }\n        });\n      }\n\n      // Use a small timeout to ensure editor is fully initialized\n      setTimeout(() => {\n        if (!grapesEditor.current) {\n          console.error(\"[MjmlEditor] Editor instance not available after timeout\");\n          return;\n        }\n\n        // Verify the editor's components API is available\n        if (!grapesEditor.current.setComponents) {\n          console.error(\"[MjmlEditor] Editor's setComponents method is not available\");\n          return;\n        }\n        try {\n          // Load initial content\n          if (initialMjml) {\n            console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\n            try {\n              grapesEditor.current.setComponents(initialMjml); // Use setComponents for MJML\n              console.log(\"[MjmlEditor] Successfully loaded MJML content\");\n            } catch (e) {\n              console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\n              // Fallback to HTML if MJML fails?\n              if (initialHtml) {\n                console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\n                grapesEditor.current.setComponents(initialHtml); // Use setComponents for HTML as well\n                console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\n              }\n            }\n          } else if (initialHtml) {\n            console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\n            grapesEditor.current.setComponents(initialHtml);\n            console.log(\"[MjmlEditor] Successfully loaded HTML content\");\n          } else {\n            // Load default MJML template if nothing is provided\n            console.log(\"[MjmlEditor] No content provided, loading default template\");\n            grapesEditor.current.setComponents(`\n                <mjml>\n                  <mj-body>\n                    <mj-section>\n                      <mj-column>\n                        <mj-text>Start designing your email!</mj-text>\n                      </mj-column>\n                    </mj-section>\n                  </mj-body>\n                </mjml>\n              `);\n          }\n        } catch (error) {\n          console.error(\"[MjmlEditor] Error in content loading phase:\", error);\n        }\n      }, 100);\n\n      // Declare timeout variable in outer scope so it's accessible in cleanup function\n      let saveTimeout;\n      let isSaving = false; // Flag to prevent multiple simultaneous save operations\n\n      // Attach save listener with debounce\n      editor.on('change:changesCount', () => {\n        if (onSave && editor && !isSaving) {\n          // Only proceed if not already saving\n          // Clear existing timeout\n          if (saveTimeout) clearTimeout(saveTimeout);\n\n          // Set a new timeout\n          saveTimeout = setTimeout(() => {\n            // Assign to the outer scope variable\n            try {\n              isSaving = true; // Set saving flag\n              // Simplify code retrieval: Prioritize commands, fallback to getHtml()\n              let finalMjml = '';\n              let finalHtml = '';\n              try {\n                // Try the specific mjml command first\n                const mjmlCode = editor.runCommand('mjml-get-code');\n                if (mjmlCode && typeof mjmlCode === 'object') {\n                  // Command should return object {mjml, html}\n                  finalMjml = mjmlCode.mjml || '';\n                  finalHtml = mjmlCode.html || '';\n                  console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\n                }\n              } catch (cmdErr) {\n                console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\n              }\n\n              // If command failed or didn't return expected structure, use fallbacks\n              if (!finalMjml && !finalHtml) {\n                finalMjml = editor.getHtml() || ''; // Often gets MJML\n                try {\n                  // Try getting HTML via command\n                  finalHtml = editor.runCommand('gjs-get-html') || '';\n                } catch (htmlCmdErr) {\n                  console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\n                }\n                // As a last resort for HTML, maybe just use the component HTML (less reliable)\n                if (!finalHtml) {\n                  finalHtml = editor.getHtml({\n                    component: editor.getWrapper()\n                  }) || '';\n                }\n                console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\n              }\n\n              // Don't save if we have no content, prevents potential refresh cycles\n              if (!finalMjml.trim()) {\n                console.log(\"[MjmlEditor] No MJML content to save, skipping save\");\n                isSaving = false;\n                return;\n              }\n              console.log(\"[MjmlEditor] Attempting to call onSave...\");\n              // Call onSave as long as the editor instance exists and the prop was passed\n              // Even if mjml/html strings are empty, let the parent decide what to do\n              onSave(finalMjml, finalHtml);\n              console.log(\"[MjmlEditor] onSave callback executed.\");\n            } catch (error) {\n              console.error(\"Error during editor change listener:\", error);\n            } finally {\n              isSaving = false; // Reset flag whether save succeeded or failed\n            }\n          }, 500); // 500ms debounce\n        }\n      });\n\n      // Return cleanup function\n      return () => {\n        if (saveTimeout) clearTimeout(saveTimeout); // Now accessible here\n        if (grapesEditor.current) {\n          // Clean up panels, commands, etc. specific to this instance if necessary\n          try {\n            grapesEditor.current.destroy();\n          } catch (destroyError) {\n            console.error(\"[MjmlEditor] Error during editor cleanup:\", destroyError);\n          }\n          grapesEditor.current = null;\n        }\n      };\n    } catch (initError) {\n      console.error(\"[MjmlEditor] Critical error during editor initialization:\", initError);\n    }\n  }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\n\n  // Expose save method via ref\n  useImperativeHandle(ref, () => ({\n    save: async () => {\n      let generatedCode = {\n        mjml: '',\n        html: ''\n      }; // Initialize with empty strings\n      if (grapesEditor.current) {\n        try {\n          // Check if command exists first to avoid warnings\n          const editor = grapesEditor.current;\n          if (editor.Commands.has('mjml-get-code')) {\n            // Try the primary command\n            const result = editor.runCommand('mjml-get-code');\n            // Check if the command returned the expected object structure\n            if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\n              generatedCode.mjml = result.mjml || '';\n              generatedCode.html = result.html || '';\n              console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", {\n                mjml: generatedCode.mjml.substring(0, 50) + '...',\n                html: generatedCode.html.substring(0, 50) + '...'\n              });\n            } else {\n              console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\n              // Throw an error to trigger the catch block for fallback logic\n              throw new Error(\"Command returned unexpected structure\");\n            }\n          } else {\n            // Command doesn't exist, go straight to fallback\n            throw new Error(\"mjml-get-code command not available\");\n          }\n        } catch (cmdErr) {\n          console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\n          try {\n            // Fallback attempts\n            const editor = grapesEditor.current;\n            const rawMjml = editor.getHtml() || '';\n            let generatedHtml = '';\n\n            // Try gjs-get-html only if it exists\n            if (editor.Commands.has('gjs-get-html')) {\n              generatedHtml = editor.runCommand('gjs-get-html') || '';\n            } else {\n              // Direct fallback to component HTML\n              generatedHtml = editor.getHtml({\n                component: editor.getWrapper()\n              }) || '';\n            }\n            if (rawMjml || generatedHtml) {\n              // Use if *either* fallback worked\n              generatedCode.mjml = rawMjml;\n              generatedCode.html = generatedHtml || rawMjml; // Use MJML as HTML if no HTML generated\n              console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\n            } else {\n              console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\n            }\n          } catch (fallbackErr) {\n            console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\n          }\n        }\n      } else {\n        console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\n      }\n\n      // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\n      await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\n\n      // Re-fetch the HTML specifically after the delay, as it might have updated\n      if (grapesEditor.current && !generatedCode.html.trim()) {\n        // Only re-fetch if HTML was initially empty\n        console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\n        try {\n          const editor = grapesEditor.current;\n          let potentiallyUpdatedHtml = '';\n          if (editor.Commands.has('gjs-get-html')) {\n            potentiallyUpdatedHtml = editor.runCommand('gjs-get-html');\n          }\n          if (!potentiallyUpdatedHtml) {\n            potentiallyUpdatedHtml = editor.getHtml({\n              component: editor.getWrapper()\n            }) || '';\n          }\n          if (potentiallyUpdatedHtml.trim()) {\n            console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\n            generatedCode.html = potentiallyUpdatedHtml;\n          } else {\n            console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\n            // If still no HTML but we have MJML, use that\n            if (generatedCode.mjml && !generatedCode.html) {\n              generatedCode.html = generatedCode.mjml;\n            }\n          }\n        } catch (refetchErr) {\n          console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\n        }\n      }\n\n      // ALWAYS return the potentially updated generatedCode object\n      return generatedCode;\n    },\n    getEditor: () => grapesEditor.current\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: editorRef,\n    style: {\n      height: height\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 336,\n    columnNumber: 12\n  }, this);\n}, \"qr1qkDeKnw5h2q20lqEB8SlN9eE=\")), \"qr1qkDeKnw5h2q20lqEB8SlN9eE=\");\n\n// Assign display name for debugging\n_c2 = MjmlEditor;\nMjmlEditor.displayName = 'MjmlEditor';\nexport default MjmlEditor;\nvar _c, _c2;\n$RefreshReg$(_c, \"MjmlEditor$forwardRef\");\n$RefreshReg$(_c2, \"MjmlEditor\");", "map": {"version": 3, "names": ["React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "<PERSON><PERSON><PERSON>", "grapesjsMjml", "jsxDEV", "_jsxDEV", "MjmlEditor", "_s", "_c", "initialMjml", "initialHtml", "onSave", "height", "ref", "editor<PERSON><PERSON>", "grapesEditor", "current", "console", "log", "destroy", "hasMjml", "mjm<PERSON><PERSON><PERSON><PERSON>", "length", "hasHtml", "htmlLength", "editor", "init", "container", "fromElement", "String", "width", "storageManager", "plugins", "pluginsOpts", "useXmlParser", "resetBlocks", "error", "Commands", "has", "add", "run", "mjml", "getHtml", "html", "component", "getWrapper", "setTimeout", "setComponents", "substring", "e", "saveTimeout", "isSaving", "on", "clearTimeout", "finalMjml", "finalHtml", "mjmlCode", "runCommand", "cmdErr", "warn", "htmlCmdErr", "trim", "destroyError", "initError", "save", "generatedCode", "result", "Error", "rawMjml", "generatedHtml", "fallbackErr", "Promise", "resolve", "potentiallyUpdatedHtml", "refetchErr", "getEditor", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/MjmlEditor.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n// @ts-ignore - grapesjs-mjml lacks official types\r\nimport grapesjsMjml from 'grapesjs-mjml';\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface MjmlEditorRef {\r\n  save: () => Promise<{ mjml: string; html: string }>;\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface MjmlEditorProps {\r\n  initialMjml?: string;\r\n  initialHtml?: string; // Added to potentially load HTML if MJML is missing\r\n  onSave?: (mjml: string, html: string) => void;\r\n  height?: string | number;\r\n}\r\n\r\nconst MjmlEditor = forwardRef<MjmlEditorRef, MjmlEditorProps>(\r\n  ({ initialMjml = '', initialHtml = '', onSave, height = '70vh' }, ref) => {\r\n    const editorRef = useRef<HTMLDivElement>(null);\r\n    const grapesEditor = useRef<Editor | null>(null);\r\n\r\n    // Initialize GrapesJS Editor\r\n    useEffect(() => {\r\n      if (!editorRef.current) return; // Check for DOM element\r\n      \r\n      // Always destroy and recreate the editor to ensure consistent behavior\r\n      if (grapesEditor.current) {\r\n        console.log(\"[MjmlEditor] Cleaning up previous editor instance\");\r\n        grapesEditor.current.destroy();\r\n        grapesEditor.current = null;\r\n      }\r\n\r\n      console.log(\"[MjmlEditor] Initializing editor with props:\", {\r\n        hasMjml: !!initialMjml,\r\n        mjmlLength: initialMjml?.length || 0,\r\n        hasHtml: !!initialHtml,\r\n        htmlLength: initialHtml?.length || 0\r\n      });\r\n\r\n      try {\r\n        const editor = grapesjs.init({\r\n          container: editorRef.current,\r\n          fromElement: false, // Don't load from existing HTML/CSS in the container\r\n          height: String(height),\r\n          width: 'auto',\r\n          storageManager: false, // Disable default storage manager\r\n          plugins: [grapesjsMjml],\r\n          pluginsOpts: {\r\n            'grapesjs-mjml': {\r\n              // MJML plugin options (optional)\r\n              // columnsPadding: '0px',\r\n               useXmlParser: true, // Use the faster XML parser\r\n               resetBlocks: false, // Try keeping default GrapesJS blocks\r\n               // ... other options\r\n            }\r\n          },\r\n          // Optional: Configure panels, blocks, styles etc.\r\n        });\r\n\r\n        // Make sure the editor was initialized properly\r\n        if (!editor) {\r\n          console.error(\"[MjmlEditor] Failed to initialize editor\");\r\n          return;\r\n        }\r\n\r\n        grapesEditor.current = editor;\r\n\r\n        // Register missing commands that are expected by the editor\r\n        if (!editor.Commands.has('mjml-get-code')) {\r\n          console.log(\"[MjmlEditor] Registering missing mjml-get-code command\");\r\n          editor.Commands.add('mjml-get-code', {\r\n            run: (editor) => {\r\n              const mjml = editor.getHtml();\r\n              // Simple implementation for missing command\r\n              return { \r\n                mjml: mjml,\r\n                html: mjml // We'll process this later if needed\r\n              };\r\n            }\r\n          });\r\n        }\r\n\r\n        if (!editor.Commands.has('gjs-get-html')) {\r\n          console.log(\"[MjmlEditor] Registering missing gjs-get-html command\");\r\n          editor.Commands.add('gjs-get-html', {\r\n            run: (editor) => {\r\n              return editor.getHtml({ component: editor.getWrapper() });\r\n            }\r\n          });\r\n        }\r\n\r\n        // Use a small timeout to ensure editor is fully initialized\r\n        setTimeout(() => {\r\n          if (!grapesEditor.current) {\r\n            console.error(\"[MjmlEditor] Editor instance not available after timeout\");\r\n            return;\r\n          }\r\n          \r\n          // Verify the editor's components API is available\r\n          if (!grapesEditor.current.setComponents) {\r\n            console.error(\"[MjmlEditor] Editor's setComponents method is not available\");\r\n            return;\r\n          }\r\n          \r\n          try {\r\n            // Load initial content\r\n            if (initialMjml) {\r\n              console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\r\n              try {\r\n                grapesEditor.current.setComponents(initialMjml); // Use setComponents for MJML\r\n                console.log(\"[MjmlEditor] Successfully loaded MJML content\");\r\n              } catch (e) {\r\n                console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\r\n                // Fallback to HTML if MJML fails?\r\n                if (initialHtml) {\r\n                  console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\r\n                  grapesEditor.current.setComponents(initialHtml); // Use setComponents for HTML as well\r\n                  console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\r\n                }\r\n              }\r\n            } else if (initialHtml) {\r\n              console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\r\n              grapesEditor.current.setComponents(initialHtml);\r\n              console.log(\"[MjmlEditor] Successfully loaded HTML content\");\r\n            } else {\r\n              // Load default MJML template if nothing is provided\r\n              console.log(\"[MjmlEditor] No content provided, loading default template\");\r\n              grapesEditor.current.setComponents(`\r\n                <mjml>\r\n                  <mj-body>\r\n                    <mj-section>\r\n                      <mj-column>\r\n                        <mj-text>Start designing your email!</mj-text>\r\n                      </mj-column>\r\n                    </mj-section>\r\n                  </mj-body>\r\n                </mjml>\r\n              `);\r\n            }\r\n          } catch (error) {\r\n            console.error(\"[MjmlEditor] Error in content loading phase:\", error);\r\n          }\r\n        }, 100);\r\n\r\n        // Declare timeout variable in outer scope so it's accessible in cleanup function\r\n        let saveTimeout: NodeJS.Timeout | undefined;\r\n        let isSaving = false; // Flag to prevent multiple simultaneous save operations\r\n        \r\n        // Attach save listener with debounce\r\n        editor.on('change:changesCount', () => {\r\n          if (onSave && editor && !isSaving) { // Only proceed if not already saving\r\n            // Clear existing timeout\r\n            if (saveTimeout) clearTimeout(saveTimeout);\r\n            \r\n            // Set a new timeout\r\n            saveTimeout = setTimeout(() => { // Assign to the outer scope variable\r\n              try {\r\n                isSaving = true; // Set saving flag\r\n                // Simplify code retrieval: Prioritize commands, fallback to getHtml()\r\n                let finalMjml = '';\r\n                let finalHtml = '';\r\n\r\n                try {\r\n                  // Try the specific mjml command first\r\n                  const mjmlCode = editor.runCommand('mjml-get-code');\r\n                  if (mjmlCode && typeof mjmlCode === 'object') { // Command should return object {mjml, html}\r\n                    finalMjml = mjmlCode.mjml || '';\r\n                    finalHtml = mjmlCode.html || '';\r\n                    console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\r\n                  }\r\n                } catch (cmdErr) {\r\n                   console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\r\n                }\r\n\r\n                // If command failed or didn't return expected structure, use fallbacks\r\n                if (!finalMjml && !finalHtml) {\r\n                    finalMjml = editor.getHtml() || ''; // Often gets MJML\r\n                    try {\r\n                      // Try getting HTML via command\r\n                      finalHtml = editor.runCommand('gjs-get-html') || ''; \r\n                    } catch(htmlCmdErr) {\r\n                       console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\r\n                    }\r\n                    // As a last resort for HTML, maybe just use the component HTML (less reliable)\r\n                    if (!finalHtml) {\r\n                      finalHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                    }\r\n                    console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\r\n                }\r\n                \r\n                // Don't save if we have no content, prevents potential refresh cycles\r\n                if (!finalMjml.trim()) {\r\n                  console.log(\"[MjmlEditor] No MJML content to save, skipping save\");\r\n                  isSaving = false;\r\n                  return;\r\n                }\r\n                \r\n                console.log(\"[MjmlEditor] Attempting to call onSave...\");\r\n                // Call onSave as long as the editor instance exists and the prop was passed\r\n                // Even if mjml/html strings are empty, let the parent decide what to do\r\n                onSave(finalMjml, finalHtml);\r\n                console.log(\"[MjmlEditor] onSave callback executed.\");\r\n\r\n             } catch (error) {\r\n                 console.error(\"Error during editor change listener:\", error);\r\n             } finally {\r\n                 isSaving = false; // Reset flag whether save succeeded or failed\r\n             }\r\n            }, 500); // 500ms debounce\r\n          }\r\n        });\r\n\r\n        // Return cleanup function\r\n        return () => {\r\n          if (saveTimeout) clearTimeout(saveTimeout); // Now accessible here\r\n          if (grapesEditor.current) {\r\n             // Clean up panels, commands, etc. specific to this instance if necessary\r\n             try {\r\n               grapesEditor.current.destroy();\r\n             } catch (destroyError) {\r\n               console.error(\"[MjmlEditor] Error during editor cleanup:\", destroyError);\r\n             }\r\n             grapesEditor.current = null;\r\n          }\r\n        };\r\n      } catch (initError) {\r\n        console.error(\"[MjmlEditor] Critical error during editor initialization:\", initError);\r\n      }\r\n    }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\r\n\r\n    // Expose save method via ref\r\n    useImperativeHandle(ref, () => ({\r\n      save: async () => {\r\n        let generatedCode = { mjml: '', html: '' }; // Initialize with empty strings\r\n        if (grapesEditor.current) {\r\n           try {\r\n               // Check if command exists first to avoid warnings\r\n               const editor = grapesEditor.current;\r\n               if (editor.Commands.has('mjml-get-code')) {\r\n                 // Try the primary command\r\n                 const result = editor.runCommand('mjml-get-code');\r\n                 // Check if the command returned the expected object structure\r\n                 if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\r\n                   generatedCode.mjml = result.mjml || '';\r\n                   generatedCode.html = result.html || '';\r\n                   console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", { mjml: generatedCode.mjml.substring(0,50)+'...', html: generatedCode.html.substring(0,50)+'...' });\r\n                 } else {\r\n                    console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\r\n                    // Throw an error to trigger the catch block for fallback logic\r\n                    throw new Error(\"Command returned unexpected structure\"); \r\n                 }\r\n               } else {\r\n                 // Command doesn't exist, go straight to fallback\r\n                 throw new Error(\"mjml-get-code command not available\");\r\n               }\r\n           } catch (cmdErr) {\r\n               console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\r\n               try {\r\n                 // Fallback attempts\r\n                 const editor = grapesEditor.current;\r\n                 const rawMjml = editor.getHtml() || '';\r\n                 let generatedHtml = '';\r\n                 \r\n                 // Try gjs-get-html only if it exists\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   generatedHtml = editor.runCommand('gjs-get-html') || '';\r\n                 } else {\r\n                   // Direct fallback to component HTML\r\n                   generatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                  \r\n                 if (rawMjml || generatedHtml) { // Use if *either* fallback worked\r\n                     generatedCode.mjml = rawMjml;\r\n                     generatedCode.html = generatedHtml || rawMjml; // Use MJML as HTML if no HTML generated\r\n                     console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\r\n                 } else {\r\n                     console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\r\n                 }\r\n               } catch (fallbackErr) {\r\n                  console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\r\n               }\r\n           }\r\n        } else {\r\n          console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\r\n        }\r\n\r\n        // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\r\n        await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\r\n        \r\n        // Re-fetch the HTML specifically after the delay, as it might have updated\r\n        if (grapesEditor.current && !generatedCode.html.trim()) { // Only re-fetch if HTML was initially empty\r\n            console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\r\n            try {\r\n                 const editor = grapesEditor.current;\r\n                 \r\n                 let potentiallyUpdatedHtml = '';\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   potentiallyUpdatedHtml = editor.runCommand('gjs-get-html');\r\n                 }\r\n                 \r\n                 if (!potentiallyUpdatedHtml) {\r\n                   potentiallyUpdatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                 \r\n                 if (potentiallyUpdatedHtml.trim()) {\r\n                     console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\r\n                     generatedCode.html = potentiallyUpdatedHtml;\r\n                 } else {\r\n                    console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\r\n                    // If still no HTML but we have MJML, use that\r\n                    if (generatedCode.mjml && !generatedCode.html) {\r\n                      generatedCode.html = generatedCode.mjml;\r\n                    }\r\n                 }\r\n            } catch (refetchErr) {\r\n                console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\r\n            }\r\n        }\r\n\r\n        // ALWAYS return the potentially updated generatedCode object\r\n        return generatedCode; \r\n      },\r\n       getEditor: () => grapesEditor.current,\r\n    }));\r\n\r\n    return <div ref={editorRef} style={{ height: height }} />;\r\n  }\r\n);\r\n\r\n// Assign display name for debugging\r\nMjmlEditor.displayName = 'MjmlEditor';\r\n\r\nexport default MjmlEditor;"], "mappings": ";;AAAA,OAAO,kCAAkC,CAAC,CAAC;;AAE3C,OAAOA,KAAK,IACVC,UAAU,EACVC,SAAS,EACTC,mBAAmB,EACnBC,MAAM,QACD,OAAO;AAEd,OAAOC,QAAQ,MAAkB,UAAU;AAC3C;AACA,OAAOC,YAAY,MAAM,eAAe;;AAExC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAaA,MAAMC,UAAU,gBAAAC,EAAA,cAAGT,UAAU,CAAAU,EAAA,GAAAD,EAAA,CAC3B,CAAC;EAAEE,WAAW,GAAG,EAAE;EAAEC,WAAW,GAAG,EAAE;EAAEC,MAAM;EAAEC,MAAM,GAAG;AAAO,CAAC,EAAEC,GAAG,KAAK;EAAAN,EAAA;EACxE,MAAMO,SAAS,GAAGb,MAAM,CAAiB,IAAI,CAAC;EAC9C,MAAMc,YAAY,GAAGd,MAAM,CAAgB,IAAI,CAAC;;EAEhD;EACAF,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,SAAS,CAACE,OAAO,EAAE,OAAO,CAAC;;IAEhC;IACA,IAAID,YAAY,CAACC,OAAO,EAAE;MACxBC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChEH,YAAY,CAACC,OAAO,CAACG,OAAO,CAAC,CAAC;MAC9BJ,YAAY,CAACC,OAAO,GAAG,IAAI;IAC7B;IAEAC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DE,OAAO,EAAE,CAAC,CAACX,WAAW;MACtBY,UAAU,EAAE,CAAAZ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEa,MAAM,KAAI,CAAC;MACpCC,OAAO,EAAE,CAAC,CAACb,WAAW;MACtBc,UAAU,EAAE,CAAAd,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,MAAM,KAAI;IACrC,CAAC,CAAC;IAEF,IAAI;MACF,MAAMG,MAAM,GAAGvB,QAAQ,CAACwB,IAAI,CAAC;QAC3BC,SAAS,EAAEb,SAAS,CAACE,OAAO;QAC5BY,WAAW,EAAE,KAAK;QAAE;QACpBhB,MAAM,EAAEiB,MAAM,CAACjB,MAAM,CAAC;QACtBkB,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,KAAK;QAAE;QACvBC,OAAO,EAAE,CAAC7B,YAAY,CAAC;QACvB8B,WAAW,EAAE;UACX,eAAe,EAAE;YACf;YACA;YACCC,YAAY,EAAE,IAAI;YAAE;YACpBC,WAAW,EAAE,KAAK,CAAE;YACpB;UACH;QACF;QACA;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAACV,MAAM,EAAE;QACXR,OAAO,CAACmB,KAAK,CAAC,0CAA0C,CAAC;QACzD;MACF;MAEArB,YAAY,CAACC,OAAO,GAAGS,MAAM;;MAE7B;MACA,IAAI,CAACA,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC,EAAE;QACzCrB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrEO,MAAM,CAACY,QAAQ,CAACE,GAAG,CAAC,eAAe,EAAE;UACnCC,GAAG,EAAGf,MAAM,IAAK;YACf,MAAMgB,IAAI,GAAGhB,MAAM,CAACiB,OAAO,CAAC,CAAC;YAC7B;YACA,OAAO;cACLD,IAAI,EAAEA,IAAI;cACVE,IAAI,EAAEF,IAAI,CAAC;YACb,CAAC;UACH;QACF,CAAC,CAAC;MACJ;MAEA,IAAI,CAAChB,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE;QACxCrB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpEO,MAAM,CAACY,QAAQ,CAACE,GAAG,CAAC,cAAc,EAAE;UAClCC,GAAG,EAAGf,MAAM,IAAK;YACf,OAAOA,MAAM,CAACiB,OAAO,CAAC;cAAEE,SAAS,EAAEnB,MAAM,CAACoB,UAAU,CAAC;YAAE,CAAC,CAAC;UAC3D;QACF,CAAC,CAAC;MACJ;;MAEA;MACAC,UAAU,CAAC,MAAM;QACf,IAAI,CAAC/B,YAAY,CAACC,OAAO,EAAE;UACzBC,OAAO,CAACmB,KAAK,CAAC,0DAA0D,CAAC;UACzE;QACF;;QAEA;QACA,IAAI,CAACrB,YAAY,CAACC,OAAO,CAAC+B,aAAa,EAAE;UACvC9B,OAAO,CAACmB,KAAK,CAAC,6DAA6D,CAAC;UAC5E;QACF;QAEA,IAAI;UACF;UACA,IAAI3B,WAAW,EAAE;YACfQ,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAET,WAAW,CAACuC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;YACxF,IAAI;cACFjC,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAACtC,WAAW,CAAC,CAAC,CAAC;cACjDQ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;YAC9D,CAAC,CAAC,OAAO+B,CAAC,EAAE;cACVhC,OAAO,CAACmB,KAAK,CAAC,0CAA0C,EAAEa,CAAC,CAAC;cAC5D;cACA,IAAIvC,WAAW,EAAE;gBACfO,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;gBAChEH,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAACrC,WAAW,CAAC,CAAC,CAAC;gBACjDO,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;cAClE;YACF;UACF,CAAC,MAAM,IAAIR,WAAW,EAAE;YACtBO,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAER,WAAW,CAACsC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;YAC5GjC,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAACrC,WAAW,CAAC;YAC/CO,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC9D,CAAC,MAAM;YACL;YACAD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzEH,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,CAAC;UACJ;QACF,CAAC,CAAC,OAAOX,KAAK,EAAE;UACdnB,OAAO,CAACmB,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACtE;MACF,CAAC,EAAE,GAAG,CAAC;;MAEP;MACA,IAAIc,WAAuC;MAC3C,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAC;;MAEtB;MACA1B,MAAM,CAAC2B,EAAE,CAAC,qBAAqB,EAAE,MAAM;QACrC,IAAIzC,MAAM,IAAIc,MAAM,IAAI,CAAC0B,QAAQ,EAAE;UAAE;UACnC;UACA,IAAID,WAAW,EAAEG,YAAY,CAACH,WAAW,CAAC;;UAE1C;UACAA,WAAW,GAAGJ,UAAU,CAAC,MAAM;YAAE;YAC/B,IAAI;cACFK,QAAQ,GAAG,IAAI,CAAC,CAAC;cACjB;cACA,IAAIG,SAAS,GAAG,EAAE;cAClB,IAAIC,SAAS,GAAG,EAAE;cAElB,IAAI;gBACF;gBACA,MAAMC,QAAQ,GAAG/B,MAAM,CAACgC,UAAU,CAAC,eAAe,CAAC;gBACnD,IAAID,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;kBAAE;kBAC9CF,SAAS,GAAGE,QAAQ,CAACf,IAAI,IAAI,EAAE;kBAC/Bc,SAAS,GAAGC,QAAQ,CAACb,IAAI,IAAI,EAAE;kBAC/B1B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;gBAClE;cACF,CAAC,CAAC,OAAOwC,MAAM,EAAE;gBACdzC,OAAO,CAAC0C,IAAI,CAAC,yDAAyD,EAAED,MAAM,CAAC;cAClF;;cAEA;cACA,IAAI,CAACJ,SAAS,IAAI,CAACC,SAAS,EAAE;gBAC1BD,SAAS,GAAG7B,MAAM,CAACiB,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpC,IAAI;kBACF;kBACAa,SAAS,GAAG9B,MAAM,CAACgC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE;gBACrD,CAAC,CAAC,OAAMG,UAAU,EAAE;kBACjB3C,OAAO,CAAC0C,IAAI,CAAC,gCAAgC,EAAEC,UAAU,CAAC;gBAC7D;gBACA;gBACA,IAAI,CAACL,SAAS,EAAE;kBACdA,SAAS,GAAG9B,MAAM,CAACiB,OAAO,CAAC;oBAAEE,SAAS,EAAEnB,MAAM,CAACoB,UAAU,CAAC;kBAAE,CAAC,CAAC,IAAI,EAAE;gBACtE;gBACA5B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;cACvE;;cAEA;cACA,IAAI,CAACoC,SAAS,CAACO,IAAI,CAAC,CAAC,EAAE;gBACrB5C,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;gBAClEiC,QAAQ,GAAG,KAAK;gBAChB;cACF;cAEAlC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;cACxD;cACA;cACAP,MAAM,CAAC2C,SAAS,EAAEC,SAAS,CAAC;cAC5BtC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;YAExD,CAAC,CAAC,OAAOkB,KAAK,EAAE;cACZnB,OAAO,CAACmB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;YAChE,CAAC,SAAS;cACNe,QAAQ,GAAG,KAAK,CAAC,CAAC;YACtB;UACD,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACX;MACF,CAAC,CAAC;;MAEF;MACA,OAAO,MAAM;QACX,IAAID,WAAW,EAAEG,YAAY,CAACH,WAAW,CAAC,CAAC,CAAC;QAC5C,IAAInC,YAAY,CAACC,OAAO,EAAE;UACvB;UACA,IAAI;YACFD,YAAY,CAACC,OAAO,CAACG,OAAO,CAAC,CAAC;UAChC,CAAC,CAAC,OAAO2C,YAAY,EAAE;YACrB7C,OAAO,CAACmB,KAAK,CAAC,2CAA2C,EAAE0B,YAAY,CAAC;UAC1E;UACA/C,YAAY,CAACC,OAAO,GAAG,IAAI;QAC9B;MACF,CAAC;IACH,CAAC,CAAC,OAAO+C,SAAS,EAAE;MAClB9C,OAAO,CAACmB,KAAK,CAAC,2DAA2D,EAAE2B,SAAS,CAAC;IACvF;EACF,CAAC,EAAE,CAACtD,WAAW,EAAEC,WAAW,EAAEE,MAAM,EAAED,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACAX,mBAAmB,CAACa,GAAG,EAAE,OAAO;IAC9BmD,IAAI,EAAE,MAAAA,CAAA,KAAY;MAChB,IAAIC,aAAa,GAAG;QAAExB,IAAI,EAAE,EAAE;QAAEE,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC;MAC5C,IAAI5B,YAAY,CAACC,OAAO,EAAE;QACvB,IAAI;UACA;UACA,MAAMS,MAAM,GAAGV,YAAY,CAACC,OAAO;UACnC,IAAIS,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC,EAAE;YACxC;YACA,MAAM4B,MAAM,GAAGzC,MAAM,CAACgC,UAAU,CAAC,eAAe,CAAC;YACjD;YACA,IAAIS,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,MAAM,IAAIA,MAAM,IAAI,MAAM,IAAIA,MAAM,EAAE;cAChFD,aAAa,CAACxB,IAAI,GAAGyB,MAAM,CAACzB,IAAI,IAAI,EAAE;cACtCwB,aAAa,CAACtB,IAAI,GAAGuB,MAAM,CAACvB,IAAI,IAAI,EAAE;cACtC1B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;gBAAEuB,IAAI,EAAEwB,aAAa,CAACxB,IAAI,CAACO,SAAS,CAAC,CAAC,EAAC,EAAE,CAAC,GAAC,KAAK;gBAAEL,IAAI,EAAEsB,aAAa,CAACtB,IAAI,CAACK,SAAS,CAAC,CAAC,EAAC,EAAE,CAAC,GAAC;cAAM,CAAC,CAAC;YACvK,CAAC,MAAM;cACJ/B,OAAO,CAAC0C,IAAI,CAAC,2EAA2E,CAAC;cACzF;cACA,MAAM,IAAIQ,KAAK,CAAC,uCAAuC,CAAC;YAC3D;UACF,CAAC,MAAM;YACL;YACA,MAAM,IAAIA,KAAK,CAAC,qCAAqC,CAAC;UACxD;QACJ,CAAC,CAAC,OAAOT,MAAM,EAAE;UACbzC,OAAO,CAAC0C,IAAI,CAAC,8DAA8D,EAAED,MAAM,CAAC;UACpF,IAAI;YACF;YACA,MAAMjC,MAAM,GAAGV,YAAY,CAACC,OAAO;YACnC,MAAMoD,OAAO,GAAG3C,MAAM,CAACiB,OAAO,CAAC,CAAC,IAAI,EAAE;YACtC,IAAI2B,aAAa,GAAG,EAAE;;YAEtB;YACA,IAAI5C,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE;cACvC+B,aAAa,GAAG5C,MAAM,CAACgC,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE;YACzD,CAAC,MAAM;cACL;cACAY,aAAa,GAAG5C,MAAM,CAACiB,OAAO,CAAC;gBAAEE,SAAS,EAAEnB,MAAM,CAACoB,UAAU,CAAC;cAAE,CAAC,CAAC,IAAI,EAAE;YAC1E;YAEA,IAAIuB,OAAO,IAAIC,aAAa,EAAE;cAAE;cAC5BJ,aAAa,CAACxB,IAAI,GAAG2B,OAAO;cAC5BH,aAAa,CAACtB,IAAI,GAAG0B,aAAa,IAAID,OAAO,CAAC,CAAC;cAC/CnD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;YAClF,CAAC,MAAM;cACHD,OAAO,CAACmB,KAAK,CAAC,yEAAyE,CAAC;YAC5F;UACF,CAAC,CAAC,OAAOkC,WAAW,EAAE;YACnBrD,OAAO,CAACmB,KAAK,CAAC,6DAA6D,EAAEkC,WAAW,CAAC;UAC5F;QACJ;MACH,CAAC,MAAM;QACLrD,OAAO,CAACmB,KAAK,CAAC,kDAAkD,CAAC;MACnE;;MAEA;MACA,MAAM,IAAImC,OAAO,CAACC,OAAO,IAAI1B,UAAU,CAAC0B,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;;MAExD;MACA,IAAIzD,YAAY,CAACC,OAAO,IAAI,CAACiD,aAAa,CAACtB,IAAI,CAACkB,IAAI,CAAC,CAAC,EAAE;QAAE;QACtD5C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;QACzE,IAAI;UACC,MAAMO,MAAM,GAAGV,YAAY,CAACC,OAAO;UAEnC,IAAIyD,sBAAsB,GAAG,EAAE;UAC/B,IAAIhD,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE;YACvCmC,sBAAsB,GAAGhD,MAAM,CAACgC,UAAU,CAAC,cAAc,CAAC;UAC5D;UAEA,IAAI,CAACgB,sBAAsB,EAAE;YAC3BA,sBAAsB,GAAGhD,MAAM,CAACiB,OAAO,CAAC;cAAEE,SAAS,EAAEnB,MAAM,CAACoB,UAAU,CAAC;YAAE,CAAC,CAAC,IAAI,EAAE;UACnF;UAEA,IAAI4B,sBAAsB,CAACZ,IAAI,CAAC,CAAC,EAAE;YAC/B5C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzE+C,aAAa,CAACtB,IAAI,GAAG8B,sBAAsB;UAC/C,CAAC,MAAM;YACJxD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;YACvE;YACA,IAAI+C,aAAa,CAACxB,IAAI,IAAI,CAACwB,aAAa,CAACtB,IAAI,EAAE;cAC7CsB,aAAa,CAACtB,IAAI,GAAGsB,aAAa,CAACxB,IAAI;YACzC;UACH;QACL,CAAC,CAAC,OAAOiC,UAAU,EAAE;UACjBzD,OAAO,CAACmB,KAAK,CAAC,gEAAgE,EAAEsC,UAAU,CAAC;QAC/F;MACJ;;MAEA;MACA,OAAOT,aAAa;IACtB,CAAC;IACAU,SAAS,EAAEA,CAAA,KAAM5D,YAAY,CAACC;EACjC,CAAC,CAAC,CAAC;EAEH,oBAAOX,OAAA;IAAKQ,GAAG,EAAEC,SAAU;IAAC8D,KAAK,EAAE;MAAEhE,MAAM,EAAEA;IAAO;EAAE;IAAAiE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3D,CAAC,iCACH,CAAC;;AAED;AAAAC,GAAA,GAzTM3E,UAAU;AA0ThBA,UAAU,CAAC4E,WAAW,GAAG,YAAY;AAErC,eAAe5E,UAAU;AAAC,IAAAE,EAAA,EAAAyE,GAAA;AAAAE,YAAA,CAAA3E,EAAA;AAAA2E,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}