"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[235],{2677:(e,t,a)=>{a.d(t,{A:()=>r});a(5043);var s=a(7304),n=a(6058),i=a(579);s.t1.register(s.PP,s.kc,s.FN,s.No,s.E8,s.Bs,s.hE,s.m_,s.s$);const r=e=>{let{type:t,data:a,options:s,height:r,width:c}=e;const l={responsive:!0,maintainAspectRatio:!0,...s};return(0,i.jsx)("div",{className:"chart-container",children:(()=>{switch(t){case"line":return(0,i.jsx)(n.N1,{data:a,options:l,height:r,width:c});case"bar":return(0,i.jsx)(n.yP,{data:a,options:l,height:r,width:c});case"pie":return(0,i.jsx)(n.Fq,{data:a,options:l,height:r,width:c});default:return(0,i.jsx)("p",{children:"Unsupported chart type"})}})()})}},5235:(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var s=a(5043),n=a(8417),i=a(1411),r=a(2677),c=a(6517),l=a(9066),o=a(6291),d=a(579);const u=()=>{const{user:e}=(0,l.A)(),[t,a]=(0,s.useState)(null),[u,m]=(0,s.useState)(!0),[h,x]=(0,s.useState)(""),[b,p]=(0,s.useState)(null),[g,j]=(0,s.useState)([]);(0,s.useEffect)((()=>{(async()=>{try{m(!0);const e=localStorage.getItem("token");if(!e)return x("Authentication token not found"),void m(!1);const t=await o.A.get("/analytics/dashboard",{headers:{Authorization:`Bearer ${e}`}}),a=await o.A.get("/campaigns",{headers:{Authorization:`Bearer ${e}`}}),s=t.data.data,n=a.data.data.campaigns||[];j(n);const i={totals:{sent:s.metrics.totalSent||0,opened:s.metrics.totalOpens||0,clicked:s.metrics.totalClicks||0,bounced:n.reduce(((e,t)=>e+(t.bounceCount||0)),0),unsubscribed:n.reduce(((e,t)=>e+(t.unsubscribeCount||0)),0)},rates:{openRate:s.metrics.averageOpenRate||0,clickRate:s.metrics.averageClickRate||0,bounceRate:s.metrics.totalSent?n.reduce(((e,t)=>e+(t.bounceCount||0)),0)/s.metrics.totalSent*100:0,unsubscribeRate:s.metrics.totalSent?n.reduce(((e,t)=>e+(t.unsubscribeCount||0)),0)/s.metrics.totalSent*100:0},chartData:{labels:s.recentCampaigns.map((e=>e.name)),datasets:[{label:"Sent",data:s.recentCampaigns.map((e=>e.sentCount||0)),backgroundColor:"rgba(54, 162, 235, 0.2)",borderColor:"rgba(54, 162, 235, 1)"},{label:"Opened",data:s.recentCampaigns.map((e=>e.openCount||0)),backgroundColor:"rgba(75, 192, 192, 0.2)",borderColor:"rgba(75, 192, 192, 1)"},{label:"Clicked",data:s.recentCampaigns.map((e=>e.clickCount||0)),backgroundColor:"rgba(153, 102, 255, 0.2)",borderColor:"rgba(153, 102, 255, 1)"}]}};let r;if(s.timeAnalytics&&s.timeAnalytics.length>0){const e=s.timeAnalytics.map((e=>e._id)),t=s.timeAnalytics.map((e=>e.sent>0?e.opens/e.sent*100:0));r={labels:e,datasets:[{label:"Open Rate",data:t,borderColor:"rgba(75, 192, 192, 1)",backgroundColor:"rgba(75, 192, 192, 0.2)"},{label:"Click Rate",data:s.timeAnalytics.map((e=>e.sent>0?e.clicks/e.sent*100:0)),borderColor:"rgba(153, 102, 255, 1)",backgroundColor:"rgba(153, 102, 255, 0.2)"}]}}else r={labels:["Jan","Feb","Mar","Apr","May","Jun"],datasets:[{label:"Open Rate",data:[.9*s.metrics.averageOpenRate,.95*s.metrics.averageOpenRate,1.05*s.metrics.averageOpenRate,1*s.metrics.averageOpenRate,.98*s.metrics.averageOpenRate,s.metrics.averageOpenRate],borderColor:"rgba(75, 192, 192, 1)",backgroundColor:"rgba(75, 192, 192, 0.2)"},{label:"Click Rate",data:[.9*s.metrics.averageClickRate,.95*s.metrics.averageClickRate,1.05*s.metrics.averageClickRate,1*s.metrics.averageClickRate,.98*s.metrics.averageClickRate,s.metrics.averageClickRate],borderColor:"rgba(153, 102, 255, 1)",backgroundColor:"rgba(153, 102, 255, 0.2)"}]};p({...i,trendsData:r})}catch(e){console.error("Error fetching analytics:",e),x("Failed to fetch analytics data")}finally{m(!1)}})()}),[]),(0,s.useEffect)((()=>{if(!t)return;(async()=>{try{m(!0);const e=localStorage.getItem("token");if(!e)return x("Authentication token not found"),void m(!1);const a=(await o.A.get(`/analytics/campaign/${t}`,{headers:{Authorization:`Bearer ${e}`}})).data.data;p((e=>({...e,totals:{sent:a.campaign.sentCount||0,opened:a.campaign.openCount||0,clicked:a.campaign.clickCount||0,bounced:a.campaign.bounceCount||0,unsubscribed:a.campaign.unsubscribeCount||0},rates:{openRate:a.metrics.openRate||0,clickRate:a.metrics.clickRate||0,bounceRate:a.metrics.bounceRate||0,unsubscribeRate:a.metrics.unsubscribeRate||0}})))}catch(e){console.error("Error fetching campaign analytics:",e),x("Failed to fetch campaign analytics")}finally{m(!1)}})()}),[t]);return u?(0,d.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):h?(0,d.jsx)("div",{className:"p-4 bg-red-800 text-white rounded-md",children:h}):b?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between",children:[(0,d.jsxs)("div",{className:"mb-4 md:mb-0",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"Campaign Performance"}),(0,d.jsx)("p",{className:"text-text-secondary",children:"View analytics for your email campaigns"})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)("select",{className:"form-input bg-gray-700 text-white border border-gray-600 rounded px-3 py-2",value:t||"",onChange:e=>a(e.target.value||null),children:[(0,d.jsx)("option",{value:"",children:"All Campaigns"}),g.map((e=>(0,d.jsx)("option",{value:e._id,children:e.name},e._id)))]}),(0,d.jsx)(n.A,{variant:"secondary",onClick:()=>{if(!b)return;const e=[["Emails Sent",b.totals.sent],["Emails Opened",b.totals.opened],["Emails Clicked",b.totals.clicked],["Bounced",b.totals.bounced],["Unsubscribed",b.totals.unsubscribed],["Open Rate",`${b.rates.openRate.toFixed(1)}%`],["Click Rate",`${b.rates.clickRate.toFixed(1)}%`],["Bounce Rate",`${b.rates.bounceRate.toFixed(1)}%`],["Unsubscribe Rate",`${b.rates.unsubscribeRate.toFixed(1)}%`]],a=[["Metric","Value"].join(","),...e.map((e=>e.join(",")))].join("\n"),s=new Blob([a],{type:"text/csv;charset=utf-8;"}),n=URL.createObjectURL(s),i=document.createElement("a");i.href=n,i.setAttribute("download",`analytics-${t||"all"}.csv`),document.body.appendChild(i),i.click(),document.body.removeChild(i)},children:"Export CSV"})]})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,d.jsx)(c.A,{title:"Emails Sent",value:b.totals.sent,icon:"mail"}),(0,d.jsx)(c.A,{title:"Open Rate",value:`${b.rates.openRate.toFixed(1)}%`,icon:"eye",change:{value:"2.1%",isPositive:!0}}),(0,d.jsx)(c.A,{title:"Click Rate",value:`${b.rates.clickRate.toFixed(1)}%`,icon:"cursor-click",change:{value:"1.5%",isPositive:!0}}),(0,d.jsx)(c.A,{title:"Bounce Rate",value:`${b.rates.bounceRate.toFixed(1)}%`,icon:"exclamation",change:{value:"0.2%",isPositive:!1}})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,d.jsx)(i.A,{title:"Email Performance",children:(0,d.jsx)(r.A,{type:"bar",data:b.chartData,height:300})}),(0,d.jsx)(i.A,{title:"Performance Trends",children:(0,d.jsx)(r.A,{type:"line",data:b.trendsData,height:300})})]}),(0,d.jsx)(i.A,{title:"Detailed Analytics",children:(0,d.jsx)("div",{className:"table-container",children:(0,d.jsxs)("table",{className:"table w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{children:"Email"}),(0,d.jsx)("th",{children:"Sent"}),(0,d.jsx)("th",{children:"Opened"}),(0,d.jsx)("th",{children:"Clicked"}),(0,d.jsx)("th",{children:"Bounced"}),(0,d.jsx)("th",{children:"Unsubscribed"}),(0,d.jsx)("th",{children:"Open Rate"}),(0,d.jsx)("th",{children:"Click Rate"})]})}),(0,d.jsxs)("tbody",{children:[g.slice(0,5).map((e=>{const t=e.sentCount?((e.openCount||0)/e.sentCount*100).toFixed(1):"0.0",a=e.sentCount?((e.clickCount||0)/e.sentCount*100).toFixed(1):"0.0";return(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{children:e.name}),(0,d.jsx)("td",{children:e.sentCount||0}),(0,d.jsx)("td",{children:e.openCount||0}),(0,d.jsx)("td",{children:e.clickCount||0}),(0,d.jsx)("td",{children:e.bounceCount||0}),(0,d.jsx)("td",{children:e.unsubscribeCount||0}),(0,d.jsxs)("td",{children:[t,"%"]}),(0,d.jsxs)("td",{children:[a,"%"]})]},e._id)})),0===g.length&&(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:8,className:"text-center py-4",children:"No campaign data available"})})]})]})})})]}):(0,d.jsx)("div",{className:"p-4 bg-gray-800 text-white rounded-md",children:"No analytics data available"})}},6517:(e,t,a)=>{a.d(t,{A:()=>i});a(5043);var s=a(579);const n=e=>{let{name:t}=e;return(0,s.jsx)("i",{className:`placeholder-icon-${t} w-5 h-5`})},i=e=>{let{title:t,value:a,icon:i,change:r,className:c="",tooltip:l,details:o}=e;return(0,s.jsxs)("div",{className:`card container-futuristic flex flex-col ${c}`,children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h3",{className:"stat-label",children:t}),i&&(0,s.jsx)("span",{className:"text-text-secondary opacity-80",children:(0,s.jsx)(n,{name:i})})]}),(0,s.jsx)("div",{className:"stat-value mb-1",children:a}),l&&(0,s.jsx)("div",{className:"text-xs text-text-secondary mt-1 opacity-90",children:l}),r&&(0,s.jsxs)("div",{className:"text-sm mt-2 flex items-center font-medium "+(r.isPositive?"text-growth-green":"text-danger"),children:[(0,s.jsx)("span",{className:"mr-1",children:r.isPositive?(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})}),r.value]}),o&&o.length>0&&(0,s.jsx)("div",{className:"mt-auto pt-3 border-t border-border mt-3",children:o.map(((e,t)=>(0,s.jsxs)("div",{className:"flex justify-between text-xs py-1",children:[(0,s.jsx)("span",{className:"text-text-secondary opacity-90",children:e.label}),(0,s.jsx)("span",{className:"font-medium",children:e.value})]},t)))})]})}}}]);
//# sourceMappingURL=235.002d2012.chunk.js.map