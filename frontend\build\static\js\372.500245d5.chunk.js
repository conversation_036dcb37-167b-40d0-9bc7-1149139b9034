"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[372],{1372:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var n=s(5043),r=s(9291),l=s(8417),o=s(1411),i=s(4741),a=s(9579),d=s(579);const c=()=>{const[e,t]=(0,n.useState)([]),[s,c]=(0,n.useState)(!0),[x,m]=(0,n.useState)(null),[u,p]=(0,n.useState)(null),[b,g]=(0,n.useState)(null),[h,y]=(0,n.useState)({type:"button",content:"",settings:{backgroundColor:"#3b82f6",textColor:"#ffffff",borderRadius:"4",size:"medium",animation:"none"}}),[f,j]=(0,n.useState)(!1);(0,n.useEffect)((()=>{(async()=>{c(!0),m(null);try{const e=await a.mv.getElements();e.success?t(e.data):m(e.message||"Failed to fetch interactive elements")}catch(e){m(e.message||"An error occurred while fetching elements")}finally{c(!1)}})()}),[]),(0,n.useEffect)((()=>{if(!u)return void g(null);(async()=>{try{const e=await a.mv.getElementAnalytics(u.id);e.success?g(e.data):(console.error("Failed to fetch element analytics:",e.message),g(null))}catch(e){console.error("Error fetching element analytics:",e),g(null)}})()}),[u]);return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,d.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,d.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"Interactive Email Elements"})}),(0,d.jsx)("p",{className:"text-text-secondary mb-6",children:"Create and manage interactive elements like buttons, polls, and countdowns for your emails."}),x&&(0,d.jsx)(r.A,{type:"error",message:x,onClose:()=>m(null),className:"mb-6"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[" ",(0,d.jsxs)(o.A,{className:"lg:col-span-1",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Create New Element"}),(0,d.jsxs)("form",{onSubmit:async s=>{if(s.preventDefault(),h.content.trim()){c(!0),m(null);try{const s=await a.mv.createElement(h.type,h.content,h.settings);s.success?(t([...e,s.data]),y({type:"button",content:"",settings:{backgroundColor:"#3b82f6",textColor:"#ffffff",borderRadius:"4",size:"medium",animation:"none"}})):m(s.message||"Failed to create interactive element")}catch(n){m(n.message||"An error occurred while creating element")}finally{c(!1)}}else m("Element content is required")},className:"p-4 space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"elementType",className:"block text-sm font-medium text-text-secondary mb-1",children:"Element Type"}),(0,d.jsxs)("select",{id:"elementType",name:"elementType",value:h.type,onChange:e=>y({...h,type:e.target.value}),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:[(0,d.jsx)("option",{value:"button",children:"Button"}),(0,d.jsx)("option",{value:"countdown",children:"Countdown Timer"}),(0,d.jsx)("option",{value:"poll",children:"Poll"})]})]}),(0,d.jsx)(i.A,{label:"Content",id:"elementContent",name:"elementContent",value:h.content,onChange:e=>y({...h,content:e.target.value}),placeholder:"button"===h.type?"Click Here":"countdown"===h.type?"Sale Ends In":"What do you think?",required:!0}),(0,d.jsx)("h3",{className:"text-md font-medium text-text-secondary pt-2",children:"Settings"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"backgroundColor",className:"block text-sm font-medium text-text-secondary mb-1",children:"Background Color"}),(0,d.jsx)("input",{type:"color",id:"backgroundColor",name:"backgroundColor",value:h.settings.backgroundColor,onChange:e=>y({...h,settings:{...h.settings,backgroundColor:e.target.value}}),className:"block w-full h-10 px-1 py-1 border border-gray-600 rounded-md cursor-pointer bg-secondary-bg"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"textColor",className:"block text-sm font-medium text-text-secondary mb-1",children:"Text Color"}),(0,d.jsx)("input",{type:"color",id:"textColor",name:"textColor",value:h.settings.textColor,onChange:e=>y({...h,settings:{...h.settings,textColor:e.target.value}}),className:"block w-full h-10 px-1 py-1 border border-gray-600 rounded-md cursor-pointer bg-secondary-bg"})]}),(0,d.jsx)(i.A,{label:"Border Radius (px)",id:"borderRadius",name:"borderRadius",type:"number",value:h.settings.borderRadius,onChange:e=>y({...h,settings:{...h.settings,borderRadius:e.target.value}})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"size",className:"block text-sm font-medium text-text-secondary mb-1",children:"Size"}),(0,d.jsxs)("select",{id:"size",name:"size",value:h.settings.size,onChange:e=>y({...h,settings:{...h.settings,size:e.target.value}}),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:[(0,d.jsx)("option",{value:"small",children:"Small"}),(0,d.jsx)("option",{value:"medium",children:"Medium"}),(0,d.jsx)("option",{value:"large",children:"Large"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"animation",className:"block text-sm font-medium text-text-secondary mb-1",children:"Animation"}),(0,d.jsxs)("select",{id:"animation",name:"animation",value:h.settings.animation,onChange:e=>y({...h,settings:{...h.settings,animation:e.target.value}}),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:[(0,d.jsx)("option",{value:"none",children:"None"}),(0,d.jsx)("option",{value:"pulse",children:"Pulse"}),(0,d.jsx)("option",{value:"bounce",children:"Bounce"})]})]}),(0,d.jsx)("div",{className:"flex justify-end pt-2",children:(0,d.jsx)(l.A,{type:"submit",disabled:s,children:s?"Creating...":"Create Element"})})]})]}),(0,d.jsxs)(o.A,{className:"lg:col-span-1",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Existing Elements"}),s&&0===e.length?(0,d.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"Loading elements..."}):0===e.length?(0,d.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"No interactive elements created yet."}):(0,d.jsxs)("ul",{className:"divide-y divide-gray-700 max-h-96 overflow-y-auto",children:[" ",e.map((e=>(0,d.jsxs)("li",{className:"p-4 hover:bg-gray-700 cursor-pointer "+((null===u||void 0===u?void 0:u.id)===e.id?"bg-gray-700":""),onClick:()=>(e=>{p(e)})(e),children:[(0,d.jsx)("p",{className:"text-sm font-medium text-text-primary truncate",children:e.content}),(0,d.jsxs)("p",{className:"text-xs text-text-secondary",children:["Type: ",e.type]})]},e.id)))]})]}),(0,d.jsxs)(o.A,{className:"lg:col-span-1",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Preview & Analytics"}),(0,d.jsx)("div",{className:"p-4",children:u?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-text-secondary mb-2",children:"Live Preview"}),(0,d.jsx)("div",{className:"mb-4 p-4 border border-dashed border-gray-600 rounded-md flex justify-center items-center min-h-[100px]",children:(e=>{if(!e)return null;const{type:t,content:s,settings:n}=e,r={...{backgroundColor:n.backgroundColor||"#3b82f6",color:n.textColor||"#ffffff",borderRadius:`${n.borderRadius||4}px`,padding:"small"===n.size?"4px 8px":"large"===n.size?"12px 24px":"8px 16px",border:"none",cursor:"pointer",fontWeight:"bold",transition:"all 0.3s ease"},..."pulse"===n.animation?{animation:"pulse 2s infinite"}:"bounce"===n.animation?{animation:"bounce 1s infinite"}:{}};switch(t){case"button":return(0,d.jsx)("button",{style:r,children:s});case"countdown":return(0,d.jsxs)("div",{style:{textAlign:"center",padding:"10px",border:"1px solid #4b5563",borderRadius:"4px",color:n.textColor},children:[(0,d.jsx)("div",{style:{fontSize:"14px",marginBottom:"5px"},children:s}),(0,d.jsxs)("div",{style:{display:"flex",justifyContent:"center",gap:"10px"},children:[(0,d.jsxs)("div",{style:{padding:"8px",backgroundColor:n.backgroundColor,color:n.textColor,borderRadius:`${n.borderRadius}px`,minWidth:"40px"},children:["00 ",(0,d.jsx)("span",{style:{fontSize:"10px",display:"block"},children:"Days"})]}),(0,d.jsxs)("div",{style:{padding:"8px",backgroundColor:n.backgroundColor,color:n.textColor,borderRadius:`${n.borderRadius}px`,minWidth:"40px"},children:["00 ",(0,d.jsx)("span",{style:{fontSize:"10px",display:"block"},children:"Hours"})]}),(0,d.jsxs)("div",{style:{padding:"8px",backgroundColor:n.backgroundColor,color:n.textColor,borderRadius:`${n.borderRadius}px`,minWidth:"40px"},children:["00 ",(0,d.jsx)("span",{style:{fontSize:"10px",display:"block"},children:"Mins"})]})]})]});case"poll":return(0,d.jsxs)("div",{style:{border:"1px solid #4b5563",borderRadius:"4px",padding:"10px",color:n.textColor},children:[(0,d.jsx)("div",{style:{fontSize:"16px",fontWeight:"bold",marginBottom:"10px"},children:s}),(0,d.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"8px"},children:["Option 1","Option 2","Option 3"].map(((t,s)=>(0,d.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,d.jsx)("input",{type:"radio",id:`poll-option-${s}`,name:`poll-${e.id}`,disabled:!0}),(0,d.jsx)("label",{htmlFor:`poll-option-${s}`,children:t})]},s)))}),(0,d.jsx)("button",{style:{...r,marginTop:"10px",fontSize:"14px"},disabled:!0,children:"Submit"})]});default:return(0,d.jsx)("div",{children:s})}})(u)}),(0,d.jsxs)("h3",{className:"text-md font-medium text-text-secondary mb-2",children:['Analytics for "',u.content,'"']}),b?(0,d.jsxs)("ul",{className:"text-sm text-text-secondary space-y-1",children:[(0,d.jsxs)("li",{children:["Views: ",b.views||0]}),(0,d.jsxs)("li",{children:["Clicks/Interactions: ",b.interactions||0]}),(0,d.jsxs)("li",{children:["Conversion Rate: ",b.conversionRate||0,"%"]})]}):(0,d.jsx)("p",{className:"text-sm text-text-secondary italic",children:"Loading analytics..."})]}):(0,d.jsx)("p",{className:"text-text-secondary italic text-center",children:"Select an element from the list to see preview and analytics."})})]})]})]})}}}]);
//# sourceMappingURL=372.500245d5.chunk.js.map