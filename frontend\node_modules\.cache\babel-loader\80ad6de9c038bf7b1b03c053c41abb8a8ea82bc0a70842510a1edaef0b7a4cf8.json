{"ast": null, "code": "import React from'react';// Define a type for social links which could be string or object\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const FooterBlock=_ref=>{let{content}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 pt-4 border-t border-gray-700 text-center text-xs text-gray-400\",children:[content.text&&/*#__PURE__*/_jsx(\"p\",{className:\"mb-2\",children:content.text}),content.company_name&&!content.text&&/*#__PURE__*/_jsxs(\"p\",{className:\"mb-2\",children:[\"\\xA9 \",new Date().getFullYear(),\" \",content.company_name,\". All rights reserved.\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[content.contact_url&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"a\",{href:content.contact_url,target:\"_blank\",rel:\"noopener noreferrer\",className:\"underline hover:text-gray-200\",children:\"Contact\"}),\" | \"]}),content.unsubscribe_url&&/*#__PURE__*/_jsx(\"a\",{href:content.unsubscribe_url,target:\"_blank\",rel:\"noopener noreferrer\",className:\"underline hover:text-gray-200\",children:\"Unsubscribe\"})]}),content.social_links&&content.social_links.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-2\",children:content.social_links.map((link,index)=>{// Check if link is an object with platform and url\nif(typeof link==='object'&&link!==null&&'platform'in link&&'url'in link){return/*#__PURE__*/_jsxs(\"a\",{href:link.url||'#',target:\"_blank\",rel:\"noopener noreferrer\",className:\"inline-block mx-1 underline hover:text-gray-200 capitalize\",children:[link.platform,\" \"]},`${link.platform}-${index}`);}// Otherwise, assume it's a string (fallback)\nelse if(typeof link==='string'){return/*#__PURE__*/_jsxs(\"a\",{href:`#${link}`// Simple href if only string is provided\n,className:\"inline-block mx-1 underline hover:text-gray-200 capitalize\",children:[link,\" \"]},`${link}-${index}`);}return null;// Skip rendering if format is unexpected\n})})]});};export default FooterBlock;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "content", "className", "children", "text", "company_name", "Date", "getFullYear", "contact_url", "href", "target", "rel", "unsubscribe_url", "social_links", "length", "map", "link", "index", "url", "platform"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/emailBlocks/FooterBlock.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\n// Define a type for social links which could be string or object\r\ntype SocialLink = string | { platform: string; url: string };\r\n\r\ninterface FooterBlockProps {\r\n  content: {\r\n    text?: string;\r\n    contact_url?: string;\r\n    unsubscribe_url?: string;\r\n    company_name?: string; // Added based on earlier mock data\r\n    social_links?: SocialLink[]; // Use the new type\r\n  };\r\n}\r\n\r\nconst FooterBlock: React.FC<FooterBlockProps> = ({ content }) => {\r\n  return (\r\n    <div className=\"mt-6 pt-4 border-t border-gray-700 text-center text-xs text-gray-400\">\r\n      {content.text && <p className=\"mb-2\">{content.text}</p>}\r\n      {content.company_name && !content.text && <p className=\"mb-2\">© {new Date().getFullYear()} {content.company_name}. All rights reserved.</p>} \r\n      \r\n      <p>\r\n        {content.contact_url && \r\n          <><a href={content.contact_url} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-gray-200\">Contact</a> | </>}\r\n        {content.unsubscribe_url && \r\n          <a href={content.unsubscribe_url} target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-gray-200\">Unsubscribe</a>}\r\n      </p>\r\n      \r\n      {content.social_links && content.social_links.length > 0 && (\r\n          <div className=\"mt-2\">\r\n              {content.social_links.map((link: SocialLink, index: number) => {\r\n                  // Check if link is an object with platform and url\r\n                  if (typeof link === 'object' && link !== null && 'platform' in link && 'url' in link) {\r\n                    return (\r\n                        <a \r\n                            key={`${link.platform}-${index}`} \r\n                            href={link.url || '#'} \r\n                            target=\"_blank\" \r\n                            rel=\"noopener noreferrer\" \r\n                            className=\"inline-block mx-1 underline hover:text-gray-200 capitalize\"\r\n                        >\r\n                            {link.platform} {/* Display platform name */}\r\n                        </a>\r\n                    );\r\n                  } \r\n                  // Otherwise, assume it's a string (fallback)\r\n                  else if (typeof link === 'string') {\r\n                    return (\r\n                        <a \r\n                            key={`${link}-${index}`} \r\n                            href={`#${link}`} // Simple href if only string is provided\r\n                            className=\"inline-block mx-1 underline hover:text-gray-200 capitalize\"\r\n                        >\r\n                            {link} {/* Display the string directly */}\r\n                        </a>\r\n                    );\r\n                  }\r\n                  return null; // Skip rendering if format is unexpected\r\n              })}\r\n          </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FooterBlock; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAaA,KAAM,CAAAC,WAAuC,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CAC1D,mBACEJ,KAAA,QAAKM,SAAS,CAAC,sEAAsE,CAAAC,QAAA,EAClFF,OAAO,CAACG,IAAI,eAAIV,IAAA,MAAGQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEF,OAAO,CAACG,IAAI,CAAI,CAAC,CACtDH,OAAO,CAACI,YAAY,EAAI,CAACJ,OAAO,CAACG,IAAI,eAAIR,KAAA,MAAGM,SAAS,CAAC,MAAM,CAAAC,QAAA,EAAC,OAAE,CAAC,GAAI,CAAAG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GAAC,CAACN,OAAO,CAACI,YAAY,CAAC,wBAAsB,EAAG,CAAC,cAE3IT,KAAA,MAAAO,QAAA,EACGF,OAAO,CAACO,WAAW,eAClBZ,KAAA,CAAAE,SAAA,EAAAK,QAAA,eAAET,IAAA,MAAGe,IAAI,CAAER,OAAO,CAACO,WAAY,CAACE,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAACT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,SAAO,CAAG,CAAC,MAAG,EAAE,CAAC,CACtIF,OAAO,CAACW,eAAe,eACtBlB,IAAA,MAAGe,IAAI,CAAER,OAAO,CAACW,eAAgB,CAACF,MAAM,CAAC,QAAQ,CAACC,GAAG,CAAC,qBAAqB,CAACT,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,aAAW,CAAG,CAAC,EACtI,CAAC,CAEHF,OAAO,CAACY,YAAY,EAAIZ,OAAO,CAACY,YAAY,CAACC,MAAM,CAAG,CAAC,eACpDpB,IAAA,QAAKQ,SAAS,CAAC,MAAM,CAAAC,QAAA,CAChBF,OAAO,CAACY,YAAY,CAACE,GAAG,CAAC,CAACC,IAAgB,CAAEC,KAAa,GAAK,CAC3D;AACA,GAAI,MAAO,CAAAD,IAAI,GAAK,QAAQ,EAAIA,IAAI,GAAK,IAAI,EAAI,UAAU,EAAI,CAAAA,IAAI,EAAI,KAAK,EAAI,CAAAA,IAAI,CAAE,CACpF,mBACIpB,KAAA,MAEIa,IAAI,CAAEO,IAAI,CAACE,GAAG,EAAI,GAAI,CACtBR,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBT,SAAS,CAAC,4DAA4D,CAAAC,QAAA,EAErEa,IAAI,CAACG,QAAQ,CAAC,GAAC,GANX,GAAGH,IAAI,CAACG,QAAQ,IAAIF,KAAK,EAO/B,CAAC,CAEV,CACA;AAAA,IACK,IAAI,MAAO,CAAAD,IAAI,GAAK,QAAQ,CAAE,CACjC,mBACIpB,KAAA,MAEIa,IAAI,CAAE,IAAIO,IAAI,EAAI;AAAA,CAClBd,SAAS,CAAC,4DAA4D,CAAAC,QAAA,EAErEa,IAAI,CAAC,GAAC,GAJF,GAAGA,IAAI,IAAIC,KAAK,EAKtB,CAAC,CAEV,CACA,MAAO,KAAI,CAAE;AACjB,CAAC,CAAC,CACD,CACR,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}