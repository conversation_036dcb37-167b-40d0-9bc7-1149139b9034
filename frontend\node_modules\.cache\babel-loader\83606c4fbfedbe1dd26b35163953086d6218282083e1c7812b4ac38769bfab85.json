{"ast": null, "code": "import React,{useEffect,useState}from'react';import{templateRecommendationService}from'services';import{v4 as uuidv4}from'uuid';import Alert from'../components/Alert';import Button from'../components/Button';// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from'../components/Card';import Input from'../components/Input';// Import the new types\n// Define a simple Template type for simulation\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const Automations=()=>{// Use the Automation type for the list of automations\nconst[automations,setAutomations]=useState([]);const[showCreateModal,setShowCreateModal]=useState(false);// State for the new automation being built in the modal\nconst[newAutomationName,setNewAutomationName]=useState('');const[newAutomationTrigger,setNewAutomationTrigger]=useState('manual');const[newAutomationNodes,setNewAutomationNodes]=useState([]);const[showAddStepOptions,setShowAddStepOptions]=useState(false);// State to show/hide node type selection\n// State for Templates\nconst[availableTemplates,setAvailableTemplates]=useState([]);const[templatesLoading,setTemplatesLoading]=useState(false);const[templatesError,setTemplatesError]=useState(null);// State for Template Selection Modal\nconst[showTemplateModal,setShowTemplateModal]=useState(false);const[selectingForNodeId,setSelectingForNodeId]=useState(null);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[editingAutomation,setEditingAutomation]=useState(null);// State for the automation being edited\nconst[showEditModal,setShowEditModal]=useState(false);// State for edit modal visibility\n// --- Effects ---\nuseEffect(()=>{// Fetch real templates when component mounts\nconst fetchTemplates=async()=>{setTemplatesLoading(true);setTemplatesError(null);try{// Use the service to fetch templates\nconst response=await templateRecommendationService.getAllTemplates();if(response&&response.data){// Assuming response.data is an array of templates with _id and name\nconst formattedTemplates=response.data.map(tmpl=>({id:tmpl._id,// Use _id from backend\nname:tmpl.templateName||tmpl.name||'Untitled Template'// Adjust based on actual field name\n}));setAvailableTemplates(formattedTemplates);}else{// Handle case where data is not as expected\nconsole.warn('Received unexpected template data structure:',response);setTemplatesError('Failed to load templates: Unexpected format.');setAvailableTemplates([]);// Set to empty array\n}}catch(err){var _response,_response$data;// Catch any error\nconsole.error('Failed to fetch email templates:',err);// Try to get a specific message, otherwise use a generic one\nconst errorMessage=((_response=err.response)===null||_response===void 0?void 0:(_response$data=_response.data)===null||_response$data===void 0?void 0:_response$data.message)||err.message||'Failed to load email templates.';setTemplatesError(errorMessage);setAvailableTemplates([]);// Ensure it's empty on error\n}finally{setTemplatesLoading(false);}};fetchTemplates();},[]);const handleCreateAutomation=()=>{if(!newAutomationName){setError('Automation name is required');return;}// Basic validation: Ensure there's at least one step\nif(newAutomationNodes.length===0){setError('Automation must have at least one step.');return;}setError('');// Clear error if validation passes\n// This would be an actual API call in the real app\nconst newId=automations.length>0?Math.max(...automations.map(a=>a.id))+1:1;const newWorkflow={trigger:newAutomationTrigger,nodes:newAutomationNodes};const newAutomationObject={id:newId,name:newAutomationName,status:'inactive',// Default to inactive\nworkflow:newWorkflow,created:new Date().toISOString().split('T')[0]};setAutomations([...automations,newAutomationObject]);setSuccess('Automation created successfully!');setShowCreateModal(false);// Reset all creation states\nsetNewAutomationName('');setNewAutomationTrigger('manual');setNewAutomationNodes([]);setTimeout(()=>{setSuccess('');},3000);};// Function to add a new node to the workflow\nconst addNodeToWorkflow=type=>{let newNode;const nodeId=uuidv4();// Generate unique ID\nif(type==='sendEmail'){newNode={id:nodeId,type:'sendEmail',templateId:null// Default to no template selected\n};}else{// type === 'delay'\nnewNode={id:nodeId,type:'delay',duration:1,// Default to 1 day\nunit:'days'};}setNewAutomationNodes([...newAutomationNodes,newNode]);setShowAddStepOptions(false);// Hide options after adding\n};// Function to remove a node from the workflow\nconst removeNodeFromWorkflow=nodeId=>{setNewAutomationNodes(newAutomationNodes.filter(node=>node.id!==nodeId));};// Function to update a specific node in the workflow\nconst updateNodeInWorkflow=(nodeId,updates)=>{setNewAutomationNodes(prevNodes=>prevNodes.map(node=>{if(node.id===nodeId){// Create a new object with updates, ensuring type correctness\nconst updatedNode={...node,...updates};// We might need more sophisticated type checking here if updates \n// could potentially change the node 'type' or mix properties,\n// but for simple field updates this should generally be okay.\n// A more robust solution would check node.type and apply specific updates.\nreturn updatedNode;// Assert the type for now\n}return node;}));};const toggleAutomationStatus=id=>{setAutomations(automations.map(automation=>{if(automation.id===id){return{...automation,status:automation.status==='active'?'inactive':'active'};}return automation;}));};// Function to handle clicking the Edit button\nconst handleEditClick=automation=>{setEditingAutomation(automation);setShowEditModal(true);setError('');// Clear any previous errors\n};return(/*#__PURE__*/// <Layout title=\"Automations\">\n_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"Email Sequences\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary\",children:\"Create automated email sequences for your contacts\"})]}),/*#__PURE__*/_jsx(Button,{onClick:()=>setShowCreateModal(true),children:\"Create Automation\"})]}),success&&/*#__PURE__*/_jsx(Alert,{type:\"success\",message:success,onClose:()=>setSuccess(''),className:\"mb-6\"}),automations.length===0?/*#__PURE__*/_jsx(Card,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl mb-4\",children:\"\\uD83D\\uDCE7\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-medium mb-2\",children:\"No Automations Yet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-4\",children:\"Create your first automation to start sending automated email sequences.\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>setShowCreateModal(true),children:\"Create Your First Automation\"})]})}):/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",children:automations.map(automation=>/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-start mb-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium\",children:automation.name}),/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 text-xs rounded ${automation.status==='active'?'bg-green-800':'bg-gray-700'}`,children:automation.status==='active'?'Active':'Inactive'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2 mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-text-secondary\",children:\"Emails:\"}),/*#__PURE__*/_jsx(\"span\",{children:automation.workflow.nodes.filter(node=>node.type==='sendEmail').length})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-text-secondary\",children:\"Trigger:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"capitalize\",children:automation.workflow.trigger==='newContact'?'New Contact':'Manual'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-text-secondary\",children:\"Created:\"}),/*#__PURE__*/_jsx(\"span\",{children:automation.created})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>toggleAutomationStatus(automation.id),children:automation.status==='active'?'Deactivate':'Activate'}),/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>handleEditClick(automation),children:\"Edit\"})]})]},automation.id))}),showCreateModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold mb-4\",children:\"Create Automation\"}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(''),className:\"mb-4\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(Input,{id:\"automationName\",name:\"automationName\",label:\"Automation Name\",value:newAutomationName,onChange:e=>setNewAutomationName(e.target.value),required:true})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"form-label\",children:\"Trigger\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",id:\"triggerManual\",name:\"trigger\",value:\"manual\",checked:newAutomationTrigger==='manual',onChange:()=>setNewAutomationTrigger('manual'),className:\"mr-2\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"triggerManual\",children:\"Manual (start sequence manually)\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",id:\"triggerNewContact\",name:\"trigger\",value:\"newContact\",checked:newAutomationTrigger==='newContact',onChange:()=>setNewAutomationTrigger('newContact'),className:\"mr-2\"}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"triggerNewContact\",children:\"New Contact (start when a new contact is added)\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsx(\"hr\",{className:\"my-4 border-gray-600\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium mb-3\",children:\"Workflow Steps\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3 mb-4\",children:[/*#__PURE__*/_jsx(\"hr\",{className:\"my-4 border-gray-600\"}),newAutomationNodes.length===0?/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary text-center\",children:\"(No steps added yet)\"}):newAutomationNodes.map((node,index)=>{var _availableTemplates$f;return/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 bg-primary rounded border border-gray-600 flex justify-between items-center\",children:[/*#__PURE__*/_jsx(\"hr\",{className:\"my-4 border-gray-600\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"span\",{className:\"font-medium\",children:[\"Step \",index+1,\": \"]}),node.type==='sendEmail'&&/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"hr\",{className:\"my-4 border-gray-600\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Send Email:\"}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>{setSelectingForNodeId(node.id);setShowTemplateModal(true);},children:node.templateId?((_availableTemplates$f=availableTemplates.find(t=>t.id===node.templateId))===null||_availableTemplates$f===void 0?void 0:_availableTemplates$f.name)||'Invalid Template':'Select Template'})]}),node.type==='delay'&&/*#__PURE__*/_jsxs(\"span\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"hr\",{className:\"my-4 border-gray-600\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Wait for\"}),/*#__PURE__*/_jsx(Input,{id:`duration-${node.id}`,name:`duration-${node.id}`,type:\"number\",value:String(node.duration),onChange:e=>{const newDuration=parseInt(e.target.value,10)||1;// Ensure duration is at least 1\nupdateNodeInWorkflow(node.id,{duration:Math.max(1,newDuration)});},className:\"w-16 p-1 text-center\"}),/*#__PURE__*/_jsxs(\"select\",{value:node.unit,onChange:e=>updateNodeInWorkflow(node.id,{unit:e.target.value}),className:\"p-1 bg-secondary-bg border border-gray-500 rounded\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"minutes\",children:\"Minutes\"}),/*#__PURE__*/_jsx(\"option\",{value:\"hours\",children:\"Hours\"}),/*#__PURE__*/_jsx(\"option\",{value:\"days\",children:\"Days\"})]})]})]}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",size:\"sm\",onClick:()=>removeNodeFromWorkflow(node.id),className:\"ml-2\",children:\"\\xD7 \"})]},node.id);})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"hr\",{className:\"my-4 border-gray-600\"}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",className:\"w-full\",onClick:()=>setShowAddStepOptions(!showAddStepOptions),children:\"+ Add Step\"}),showAddStepOptions&&/*#__PURE__*/_jsxs(\"div\",{className:\"absolute left-0 right-0 mt-2 bg-secondary-bg border border-gray-600 rounded shadow-lg z-10\",children:[/*#__PURE__*/_jsx(\"hr\",{className:\"my-4 border-gray-600\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>addNodeToWorkflow('sendEmail'),className:\"block w-full text-left px-4 py-2 hover:bg-primary\",children:\"Send Email\"})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>addNodeToWorkflow('delay'),className:\"block w-full text-left px-4 py-2 hover:bg-primary\",children:\"Wait / Delay\"})})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>setShowCreateModal(false),children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleCreateAutomation,children:\"Create\"})]})]})}),showEditModal&&editingAutomation&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\",children:[/*#__PURE__*/_jsxs(\"h2\",{className:\"text-xl font-semibold mb-4\",children:[\"Edit Automation: \",editingAutomation.name]}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(''),className:\"mb-4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-center text-text-secondary my-8\",children:\"Edit form fields will go here.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>setShowEditModal(false),children:\"Cancel\"}),/*#__PURE__*/_jsxs(Button/*onClick={handleUpdateAutomation}*/,{children:[\" \",\"Save Changes\"]})]})]})}),showTemplateModal&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-secondary-bg rounded-lg p-6 max-w-lg w-full max-h-[80vh] flex flex-col\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold mb-4\",children:\"Select Email Template\"}),templatesLoading&&/*#__PURE__*/_jsx(\"p\",{children:\"Loading templates...\"}),templatesError&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:templatesError,onClose:()=>setTemplatesError(null)}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-grow overflow-y-auto mb-4\",children:!templatesLoading&&!templatesError&&/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-2\",children:availableTemplates.length>0?availableTemplates.map(template=>/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(\"button\",{className:\"w-full text-left p-3 hover:bg-primary rounded border border-gray-700\",onClick:()=>{if(selectingForNodeId){// Update the node with the selected template ID\nupdateNodeInWorkflow(selectingForNodeId,{templateId:template.id});}setShowTemplateModal(false);setSelectingForNodeId(null);},children:[template.name,\" \",/*#__PURE__*/_jsxs(\"span\",{className:\"text-xs text-text-secondary\",children:[\"(ID: \",template.id,\")\"]})]})},template.id)):/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary text-center\",children:\"(No templates found)\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-end\",children:/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>{setShowTemplateModal(false);setSelectingForNodeId(null);},children:\"Cancel\"})})]})})]})// </Layout>\n);};export default Automations;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "templateRecommendationService", "v4", "uuidv4", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "Input", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Automations", "automations", "setAutomations", "showCreateModal", "setShowCreateModal", "newAutomationName", "setNewAutomationName", "newAutomationTrigger", "setNewAutomationTrigger", "newAutomationNodes", "setNewAutomationNodes", "showAddStepOptions", "setShowAddStepOptions", "availableTemplates", "setAvailableTemplates", "templatesLoading", "setTemplatesLoading", "templatesError", "setTemplatesError", "showTemplateModal", "setShowTemplateModal", "selectingForNodeId", "setSelectingForNodeId", "error", "setError", "success", "setSuccess", "editingAutomation", "setEditingAutomation", "showEditModal", "setShowEditModal", "fetchTemplates", "response", "getAllTemplates", "data", "formattedTemplates", "map", "tmpl", "id", "_id", "name", "templateName", "console", "warn", "err", "_response", "_response$data", "errorMessage", "message", "handleCreateAutomation", "length", "newId", "Math", "max", "a", "newWorkflow", "trigger", "nodes", "newAutomationObject", "status", "workflow", "created", "Date", "toISOString", "split", "setTimeout", "addNodeToWorkflow", "type", "newNode", "nodeId", "templateId", "duration", "unit", "removeNodeFromWorkflow", "filter", "node", "updateNodeInWorkflow", "updates", "prevNodes", "updatedNode", "toggleAutomationStatus", "automation", "handleEditClick", "children", "className", "onClick", "onClose", "variant", "size", "label", "value", "onChange", "e", "target", "required", "checked", "htmlFor", "index", "_availableTemplates$f", "find", "t", "String", "newDuration", "parseInt", "template"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/Automations.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport { templateRecommendationService } from 'services';\nimport { v4 as uuidv4 } from 'uuid';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\n// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from '../components/Card';\nimport Input from '../components/Input';\n// Import the new types\nimport {\n  Automation,\n  AutomationTriggerType,\n  AutomationWorkflow,\n  WorkflowDelayNode,\n  WorkflowNode,\n  WorkflowSendEmailNode,\n} from '../types/automations';\n\n// Define a simple Template type for simulation\ninterface EmailTemplate {\n  id: string;\n  name: string;\n  // Add other relevant template fields if needed\n}\n\nconst Automations: React.FC = () => {\n  // Use the Automation type for the list of automations\n  const [automations, setAutomations] = useState<Automation[]>([]);\n  \n  const [showCreateModal, setShowCreateModal] = useState(false);\n  \n  // State for the new automation being built in the modal\n  const [newAutomationName, setNewAutomationName] = useState('');\n  const [newAutomationTrigger, setNewAutomationTrigger] = useState<AutomationTriggerType>('manual');\n  const [newAutomationNodes, setNewAutomationNodes] = useState<WorkflowNode[]>([]);\n  const [showAddStepOptions, setShowAddStepOptions] = useState(false); // State to show/hide node type selection\n  \n  // State for Templates\n  const [availableTemplates, setAvailableTemplates] = useState<EmailTemplate[]>([]);\n  const [templatesLoading, setTemplatesLoading] = useState(false);\n  const [templatesError, setTemplatesError] = useState<string | null>(null);\n\n  // State for Template Selection Modal\n  const [showTemplateModal, setShowTemplateModal] = useState(false);\n  const [selectingForNodeId, setSelectingForNodeId] = useState<string | null>(null);\n\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingAutomation, setEditingAutomation] = useState<Automation | null>(null); // State for the automation being edited\n  const [showEditModal, setShowEditModal] = useState(false); // State for edit modal visibility\n  \n  // --- Effects ---\n  useEffect(() => {\n    // Fetch real templates when component mounts\n    const fetchTemplates = async () => {\n      setTemplatesLoading(true);\n      setTemplatesError(null);\n      try {\n        // Use the service to fetch templates\n        const response = await templateRecommendationService.getAllTemplates();\n        if (response && response.data) {\n          // Assuming response.data is an array of templates with _id and name\n          const formattedTemplates: EmailTemplate[] = response.data.map((tmpl: any) => ({\n            id: tmpl._id, // Use _id from backend\n            name: tmpl.templateName || tmpl.name || 'Untitled Template' // Adjust based on actual field name\n          }));\n          setAvailableTemplates(formattedTemplates);\n        } else {\n          // Handle case where data is not as expected\n          console.warn('Received unexpected template data structure:', response);\n          setTemplatesError('Failed to load templates: Unexpected format.');\n          setAvailableTemplates([]); // Set to empty array\n        }\n      } catch (err) { // Catch any error\n        console.error('Failed to fetch email templates:', err);\n        // Try to get a specific message, otherwise use a generic one\n        const errorMessage = (err as any).response?.data?.message || (err as any).message || 'Failed to load email templates.';\n        setTemplatesError(errorMessage);\n        setAvailableTemplates([]); // Ensure it's empty on error\n      } finally {\n        setTemplatesLoading(false);\n      }\n    };\n\n    fetchTemplates();\n  }, []);\n\n  const handleCreateAutomation = () => {\n    if (!newAutomationName) {\n      setError('Automation name is required');\n      return;\n    }\n    // Basic validation: Ensure there's at least one step\n    if (newAutomationNodes.length === 0) {\n      setError('Automation must have at least one step.');\n      return;\n    }\n    setError(''); // Clear error if validation passes\n    \n    // This would be an actual API call in the real app\n    const newId = automations.length > 0 ? Math.max(...automations.map(a => a.id)) + 1 : 1;\n    \n    const newWorkflow: AutomationWorkflow = {\n        trigger: newAutomationTrigger,\n        nodes: newAutomationNodes\n    };\n\n    const newAutomationObject: Automation = {\n        id: newId,\n        name: newAutomationName,\n        status: 'inactive', // Default to inactive\n        workflow: newWorkflow,\n        created: new Date().toISOString().split('T')[0]\n    };\n    \n    setAutomations([...automations, newAutomationObject]);\n    \n    setSuccess('Automation created successfully!');\n    setShowCreateModal(false);\n    // Reset all creation states\n    setNewAutomationName('');\n    setNewAutomationTrigger('manual');\n    setNewAutomationNodes([]); \n    \n    setTimeout(() => {\n      setSuccess('');\n    }, 3000);\n  };\n  \n  // Function to add a new node to the workflow\n  const addNodeToWorkflow = (type: 'sendEmail' | 'delay') => {\n    let newNode: WorkflowNode;\n    const nodeId = uuidv4(); // Generate unique ID\n\n    if (type === 'sendEmail') {\n      newNode = {\n        id: nodeId,\n        type: 'sendEmail',\n        templateId: null // Default to no template selected\n      };\n    } else { // type === 'delay'\n      newNode = {\n        id: nodeId,\n        type: 'delay',\n        duration: 1, // Default to 1 day\n        unit: 'days'\n      };\n    }\n\n    setNewAutomationNodes([...newAutomationNodes, newNode]);\n    setShowAddStepOptions(false); // Hide options after adding\n  };\n  \n  // Function to remove a node from the workflow\n  const removeNodeFromWorkflow = (nodeId: string) => {\n    setNewAutomationNodes(newAutomationNodes.filter(node => node.id !== nodeId));\n  };\n  \n  // Function to update a specific node in the workflow\n  const updateNodeInWorkflow = (nodeId: string, updates: Partial<WorkflowNode>) => {\n    setNewAutomationNodes(prevNodes => \n      prevNodes.map(node => {\n        if (node.id === nodeId) {\n          // Create a new object with updates, ensuring type correctness\n          const updatedNode = { ...node, ...updates };\n          // We might need more sophisticated type checking here if updates \n          // could potentially change the node 'type' or mix properties,\n          // but for simple field updates this should generally be okay.\n          // A more robust solution would check node.type and apply specific updates.\n          return updatedNode as WorkflowNode; // Assert the type for now\n        }\n        return node;\n      })\n    );\n  };\n  \n  const toggleAutomationStatus = (id: number) => {\n    setAutomations(automations.map(automation => {\n      if (automation.id === id) {\n        return {\n          ...automation,\n          status: automation.status === 'active' ? 'inactive' : 'active'\n        };\n      }\n      return automation;\n    }));\n  };\n  \n  // Function to handle clicking the Edit button\n  const handleEditClick = (automation: Automation) => {\n    setEditingAutomation(automation);\n    setShowEditModal(true);\n    setError(''); // Clear any previous errors\n  };\n  \n  return (\n    // <Layout title=\"Automations\">\n    <>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-xl font-semibold\">Email Sequences</h2>\n          <p className=\"text-text-secondary\">Create automated email sequences for your contacts</p>\n        </div>\n        \n        <Button onClick={() => setShowCreateModal(true)}>\n          Create Automation\n        </Button>\n      </div>\n      \n      {success && (\n        <Alert\n          type=\"success\"\n          message={success}\n          onClose={() => setSuccess('')}\n          className=\"mb-6\"\n        />\n      )}\n      \n      {automations.length === 0 ? (\n        <Card>\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-4\">📧</div>\n            <h3 className=\"text-xl font-medium mb-2\">No Automations Yet</h3>\n            <p className=\"text-text-secondary mb-4\">\n              Create your first automation to start sending automated email sequences.\n            </p>\n            <Button onClick={() => setShowCreateModal(true)}>\n              Create Your First Automation\n            </Button>\n          </div>\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {automations.map(automation => (\n            <Card key={automation.id}>\n              <div className=\"flex justify-between items-start mb-4\">\n                <h3 className=\"text-lg font-medium\">{automation.name}</h3>\n                <span className={`px-2 py-1 text-xs rounded ${\n                  automation.status === 'active' ? 'bg-green-800' : 'bg-gray-700'\n                }`}>\n                  {automation.status === 'active' ? 'Active' : 'Inactive'}\n                </span>\n              </div>\n              \n              <div className=\"space-y-2 mb-6\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-text-secondary\">Emails:</span>\n                  <span>\n                    {automation.workflow.nodes.filter(node => node.type === 'sendEmail').length}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-text-secondary\">Trigger:</span>\n                  <span className=\"capitalize\">\n                    {automation.workflow.trigger === 'newContact' ? 'New Contact' : 'Manual'}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-text-secondary\">Created:</span>\n                  <span>{automation.created}</span>\n                </div>\n              </div>\n              \n              <div className=\"flex space-x-2\">\n                <Button variant=\"secondary\" size=\"sm\" onClick={() => toggleAutomationStatus(automation.id)}>\n                  {automation.status === 'active' ? 'Deactivate' : 'Activate'}\n                </Button>\n                <Button size=\"sm\" onClick={() => handleEditClick(automation)}>\n                  Edit\n                </Button>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n      \n      {/* Create Automation Modal */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\">\n            <h2 className=\"text-xl font-semibold mb-4\">Create Automation</h2>\n            \n            {error && (\n              <Alert\n                type=\"error\"\n                message={error}\n                onClose={() => setError('')}\n                className=\"mb-4\"\n              />\n            )}\n            \n            <div className=\"mb-4\">\n              <Input\n                id=\"automationName\"\n                name=\"automationName\"\n                label=\"Automation Name\"\n                value={newAutomationName}\n                onChange={(e) => setNewAutomationName(e.target.value)}\n                required\n              />\n            </div>\n            \n            <div className=\"mb-6\">\n              <label className=\"form-label\">Trigger</label>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    id=\"triggerManual\"\n                    name=\"trigger\"\n                    value=\"manual\"\n                    checked={newAutomationTrigger === 'manual'}\n                    onChange={() => setNewAutomationTrigger('manual')}\n                    className=\"mr-2\"\n                  />\n                  <label htmlFor=\"triggerManual\">Manual (start sequence manually)</label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    id=\"triggerNewContact\"\n                    name=\"trigger\"\n                    value=\"newContact\"\n                    checked={newAutomationTrigger === 'newContact'}\n                    onChange={() => setNewAutomationTrigger('newContact')}\n                    className=\"mr-2\"\n                  />\n                  <label htmlFor=\"triggerNewContact\">New Contact (start when a new contact is added)</label>\n                </div>\n              </div>\n            </div>\n\n            {/* Workflow Steps Builder */}\n            <div className=\"mb-6\"><hr className=\"my-4 border-gray-600\"/>\n              <h3 className=\"text-lg font-medium mb-3\">Workflow Steps</h3>\n              <div className=\"space-y-3 mb-4\"><hr className=\"my-4 border-gray-600\"/>\n                {newAutomationNodes.length === 0 ? (\n                  <p className=\"text-text-secondary text-center\">(No steps added yet)</p>\n                ) : (\n                  newAutomationNodes.map((node, index) => (\n                    <div key={node.id} className=\"p-3 bg-primary rounded border border-gray-600 flex justify-between items-center\"><hr className=\"my-4 border-gray-600\"/>\n                      <div>\n                        <span className=\"font-medium\">Step {index + 1}: </span>\n                        {node.type === 'sendEmail' && (\n                          <span className=\"flex items-center space-x-2\"><hr className=\"my-4 border-gray-600\"/>\n                            <span>Send Email:</span>\n                            <Button \n                              variant=\"secondary\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectingForNodeId(node.id);\n                                setShowTemplateModal(true);\n                              }}\n                            >\n                              {/* Find template name from availableTemplates or show placeholder */}\n                              { (node as WorkflowSendEmailNode).templateId \n                                ? availableTemplates.find(t => t.id === (node as WorkflowSendEmailNode).templateId)?.name || 'Invalid Template'\n                                : 'Select Template'\n                              }\n                            </Button>\n                          </span>\n                        )}\n                        {node.type === 'delay' && (\n                          <span className=\"flex items-center space-x-2\"><hr className=\"my-4 border-gray-600\"/>\n                            <span>Wait for</span>\n                            <Input\n                              id={`duration-${node.id}`}\n                              name={`duration-${node.id}`}\n                              type=\"number\"\n                              value={String((node as WorkflowDelayNode).duration)}\n                              onChange={(e) => {\n                                const newDuration = parseInt(e.target.value, 10) || 1;\n                                // Ensure duration is at least 1\n                                updateNodeInWorkflow(node.id, { duration: Math.max(1, newDuration) })\n                              }}\n                              className=\"w-16 p-1 text-center\"\n                            />\n                            <select\n                              value={(node as WorkflowDelayNode).unit}\n                              onChange={(e) => updateNodeInWorkflow(node.id, { unit: e.target.value as WorkflowDelayNode['unit'] })}\n                              className=\"p-1 bg-secondary-bg border border-gray-500 rounded\"\n                            >\n                              <option value=\"minutes\">Minutes</option>\n                              <option value=\"hours\">Hours</option>\n                              <option value=\"days\">Days</option>\n                            </select>\n                          </span>\n                        )}\n                      </div>\n                      {/* Remove Node Button */}\n                      <Button \n                        variant=\"danger\" \n                        size=\"sm\" \n                        onClick={() => removeNodeFromWorkflow(node.id)}\n                        className=\"ml-2\"\n                      >\n                        &times; {/* Times symbol for Remove */}\n                      </Button>\n                    </div>\n                  ))\n                )}\n              </div>\n\n              {/* Add Step Button and Options */}\n              <div className=\"relative\"><hr className=\"my-4 border-gray-600\"/>\n                <Button \n                  variant=\"secondary\"\n                  className=\"w-full\" \n                  onClick={() => setShowAddStepOptions(!showAddStepOptions)}\n                >\n                  + Add Step\n                </Button>\n                {showAddStepOptions && (\n                  <div className=\"absolute left-0 right-0 mt-2 bg-secondary-bg border border-gray-600 rounded shadow-lg z-10\"><hr className=\"my-4 border-gray-600\"/>\n                    <ul>\n                      <li>\n                        <button \n                          onClick={() => addNodeToWorkflow('sendEmail')}\n                          className=\"block w-full text-left px-4 py-2 hover:bg-primary\"\n                        >\n                          Send Email\n                        </button>\n                      </li>\n                      <li>\n                        <button \n                          onClick={() => addNodeToWorkflow('delay')}\n                          className=\"block w-full text-left px-4 py-2 hover:bg-primary\"\n                        >\n                          Wait / Delay\n                        </button>\n                      </li>\n                      {/* // TODO: Add other node types here later (Condition, Tag, etc.) */}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-2\">\n              <Button variant=\"secondary\" onClick={() => setShowCreateModal(false)}>\n                Cancel\n              </Button>\n              <Button onClick={handleCreateAutomation}>\n                Create\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Automation Modal */}\n      {showEditModal && editingAutomation && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\">\n            <h2 className=\"text-xl font-semibold mb-4\">Edit Automation: {editingAutomation.name}</h2>\n\n            {error && (\n              <Alert\n                type=\"error\"\n                message={error}\n                onClose={() => setError('')}\n                className=\"mb-4\"\n              />\n            )}\n\n            {/* TODO: Add form fields for editing automation details */}\n            <p className=\"text-center text-text-secondary my-8\">Edit form fields will go here.</p>\n\n            <div className=\"flex justify-end space-x-2\">\n              <Button variant=\"secondary\" onClick={() => setShowEditModal(false)}>\n                Cancel\n              </Button>\n              <Button /*onClick={handleUpdateAutomation}*/ > {/* TODO: Add update handler */}\n                Save Changes\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Template Selection Modal */}\n      {showTemplateModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-lg w-full max-h-[80vh] flex flex-col\">\n            <h2 className=\"text-xl font-semibold mb-4\">Select Email Template</h2>\n            \n            {templatesLoading && <p>Loading templates...</p>}\n            {templatesError && <Alert type=\"error\" message={templatesError} onClose={() => setTemplatesError(null)} />}\n\n            <div className=\"flex-grow overflow-y-auto mb-4\">\n              {!templatesLoading && !templatesError && (\n                <ul className=\"space-y-2\">\n                  {availableTemplates.length > 0 ? (\n                    availableTemplates.map(template => (\n                      <li key={template.id}>\n                        <button \n                          className=\"w-full text-left p-3 hover:bg-primary rounded border border-gray-700\" \n                          onClick={() => {\n                            if (selectingForNodeId) {\n                              // Update the node with the selected template ID\n                              updateNodeInWorkflow(selectingForNodeId, { templateId: template.id });\n                            }\n                            setShowTemplateModal(false);\n                            setSelectingForNodeId(null);\n                          }}\n                        >\n                          {template.name} <span className=\"text-xs text-text-secondary\">(ID: {template.id})</span>\n                        </button>\n                      </li>\n                    ))\n                  ) : (\n                    <p className=\"text-text-secondary text-center\">(No templates found)</p>\n                  )}\n                </ul>\n              )}\n            </div>\n            \n            <div className=\"flex justify-end\">\n              <Button \n                variant=\"secondary\" \n                onClick={() => {\n                  setShowTemplateModal(false);\n                  setSelectingForNodeId(null);\n                }}\n              >\n                Cancel\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n    // </Layout>\n  );\n};\n\nexport default Automations;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,OAASC,6BAA6B,KAAQ,UAAU,CACxD,OAASC,EAAE,GAAI,CAAAC,MAAM,KAAQ,MAAM,CAEnC,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC;AACA,MAAO,CAAAC,IAAI,KAAM,oBAAoB,CACrC,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC;AAUA;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAOA,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC;AACA,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGhB,QAAQ,CAAe,EAAE,CAAC,CAEhE,KAAM,CAACiB,eAAe,CAAEC,kBAAkB,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAACmB,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACqB,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtB,QAAQ,CAAwB,QAAQ,CAAC,CACjG,KAAM,CAACuB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxB,QAAQ,CAAiB,EAAE,CAAC,CAChF,KAAM,CAACyB,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAAE;AAErE;AACA,KAAM,CAAC2B,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG5B,QAAQ,CAAkB,EAAE,CAAC,CACjF,KAAM,CAAC6B,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC+B,cAAc,CAAEC,iBAAiB,CAAC,CAAGhC,QAAQ,CAAgB,IAAI,CAAC,CAEzE;AACA,KAAM,CAACiC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACmC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGpC,QAAQ,CAAgB,IAAI,CAAC,CAEjF,KAAM,CAACqC,KAAK,CAAEC,QAAQ,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACuC,OAAO,CAAEC,UAAU,CAAC,CAAGxC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACyC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1C,QAAQ,CAAoB,IAAI,CAAC,CAAE;AACrF,KAAM,CAAC2C,aAAa,CAAEC,gBAAgB,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CAAE;AAE3D;AACAD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA8C,cAAc,CAAG,KAAAA,CAAA,GAAY,CACjCf,mBAAmB,CAAC,IAAI,CAAC,CACzBE,iBAAiB,CAAC,IAAI,CAAC,CACvB,GAAI,CACF;AACA,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAA7C,6BAA6B,CAAC8C,eAAe,CAAC,CAAC,CACtE,GAAID,QAAQ,EAAIA,QAAQ,CAACE,IAAI,CAAE,CAC7B;AACA,KAAM,CAAAC,kBAAmC,CAAGH,QAAQ,CAACE,IAAI,CAACE,GAAG,CAAEC,IAAS,GAAM,CAC5EC,EAAE,CAAED,IAAI,CAACE,GAAG,CAAE;AACdC,IAAI,CAAEH,IAAI,CAACI,YAAY,EAAIJ,IAAI,CAACG,IAAI,EAAI,mBAAoB;AAC9D,CAAC,CAAC,CAAC,CACH1B,qBAAqB,CAACqB,kBAAkB,CAAC,CAC3C,CAAC,IAAM,CACL;AACAO,OAAO,CAACC,IAAI,CAAC,8CAA8C,CAAEX,QAAQ,CAAC,CACtEd,iBAAiB,CAAC,8CAA8C,CAAC,CACjEJ,qBAAqB,CAAC,EAAE,CAAC,CAAE;AAC7B,CACF,CAAE,MAAO8B,GAAG,CAAE,KAAAC,SAAA,CAAAC,cAAA,CAAE;AACdJ,OAAO,CAACnB,KAAK,CAAC,kCAAkC,CAAEqB,GAAG,CAAC,CACtD;AACA,KAAM,CAAAG,YAAY,CAAG,EAAAF,SAAA,CAACD,GAAG,CAASZ,QAAQ,UAAAa,SAAA,kBAAAC,cAAA,CAArBD,SAAA,CAAuBX,IAAI,UAAAY,cAAA,iBAA3BA,cAAA,CAA6BE,OAAO,GAAKJ,GAAG,CAASI,OAAO,EAAI,iCAAiC,CACtH9B,iBAAiB,CAAC6B,YAAY,CAAC,CAC/BjC,qBAAqB,CAAC,EAAE,CAAC,CAAE;AAC7B,CAAC,OAAS,CACRE,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAEDe,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAkB,sBAAsB,CAAGA,CAAA,GAAM,CACnC,GAAI,CAAC5C,iBAAiB,CAAE,CACtBmB,QAAQ,CAAC,6BAA6B,CAAC,CACvC,OACF,CACA;AACA,GAAIf,kBAAkB,CAACyC,MAAM,GAAK,CAAC,CAAE,CACnC1B,QAAQ,CAAC,yCAAyC,CAAC,CACnD,OACF,CACAA,QAAQ,CAAC,EAAE,CAAC,CAAE;AAEd;AACA,KAAM,CAAA2B,KAAK,CAAGlD,WAAW,CAACiD,MAAM,CAAG,CAAC,CAAGE,IAAI,CAACC,GAAG,CAAC,GAAGpD,WAAW,CAACmC,GAAG,CAACkB,CAAC,EAAIA,CAAC,CAAChB,EAAE,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAEtF,KAAM,CAAAiB,WAA+B,CAAG,CACpCC,OAAO,CAAEjD,oBAAoB,CAC7BkD,KAAK,CAAEhD,kBACX,CAAC,CAED,KAAM,CAAAiD,mBAA+B,CAAG,CACpCpB,EAAE,CAAEa,KAAK,CACTX,IAAI,CAAEnC,iBAAiB,CACvBsD,MAAM,CAAE,UAAU,CAAE;AACpBC,QAAQ,CAAEL,WAAW,CACrBM,OAAO,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAClD,CAAC,CAED9D,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEyD,mBAAmB,CAAC,CAAC,CAErDhC,UAAU,CAAC,kCAAkC,CAAC,CAC9CtB,kBAAkB,CAAC,KAAK,CAAC,CACzB;AACAE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,uBAAuB,CAAC,QAAQ,CAAC,CACjCE,qBAAqB,CAAC,EAAE,CAAC,CAEzBuD,UAAU,CAAC,IAAM,CACfvC,UAAU,CAAC,EAAE,CAAC,CAChB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA,KAAM,CAAAwC,iBAAiB,CAAIC,IAA2B,EAAK,CACzD,GAAI,CAAAC,OAAqB,CACzB,KAAM,CAAAC,MAAM,CAAGhF,MAAM,CAAC,CAAC,CAAE;AAEzB,GAAI8E,IAAI,GAAK,WAAW,CAAE,CACxBC,OAAO,CAAG,CACR9B,EAAE,CAAE+B,MAAM,CACVF,IAAI,CAAE,WAAW,CACjBG,UAAU,CAAE,IAAK;AACnB,CAAC,CACH,CAAC,IAAM,CAAE;AACPF,OAAO,CAAG,CACR9B,EAAE,CAAE+B,MAAM,CACVF,IAAI,CAAE,OAAO,CACbI,QAAQ,CAAE,CAAC,CAAE;AACbC,IAAI,CAAE,MACR,CAAC,CACH,CAEA9D,qBAAqB,CAAC,CAAC,GAAGD,kBAAkB,CAAE2D,OAAO,CAAC,CAAC,CACvDxD,qBAAqB,CAAC,KAAK,CAAC,CAAE;AAChC,CAAC,CAED;AACA,KAAM,CAAA6D,sBAAsB,CAAIJ,MAAc,EAAK,CACjD3D,qBAAqB,CAACD,kBAAkB,CAACiE,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACrC,EAAE,GAAK+B,MAAM,CAAC,CAAC,CAC9E,CAAC,CAED;AACA,KAAM,CAAAO,oBAAoB,CAAGA,CAACP,MAAc,CAAEQ,OAA8B,GAAK,CAC/EnE,qBAAqB,CAACoE,SAAS,EAC7BA,SAAS,CAAC1C,GAAG,CAACuC,IAAI,EAAI,CACpB,GAAIA,IAAI,CAACrC,EAAE,GAAK+B,MAAM,CAAE,CACtB;AACA,KAAM,CAAAU,WAAW,CAAG,CAAE,GAAGJ,IAAI,CAAE,GAAGE,OAAQ,CAAC,CAC3C;AACA;AACA;AACA;AACA,MAAO,CAAAE,WAAW,CAAkB;AACtC,CACA,MAAO,CAAAJ,IAAI,CACb,CAAC,CACH,CAAC,CACH,CAAC,CAED,KAAM,CAAAK,sBAAsB,CAAI1C,EAAU,EAAK,CAC7CpC,cAAc,CAACD,WAAW,CAACmC,GAAG,CAAC6C,UAAU,EAAI,CAC3C,GAAIA,UAAU,CAAC3C,EAAE,GAAKA,EAAE,CAAE,CACxB,MAAO,CACL,GAAG2C,UAAU,CACbtB,MAAM,CAAEsB,UAAU,CAACtB,MAAM,GAAK,QAAQ,CAAG,UAAU,CAAG,QACxD,CAAC,CACH,CACA,MAAO,CAAAsB,UAAU,CACnB,CAAC,CAAC,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAID,UAAsB,EAAK,CAClDrD,oBAAoB,CAACqD,UAAU,CAAC,CAChCnD,gBAAgB,CAAC,IAAI,CAAC,CACtBN,QAAQ,CAAC,EAAE,CAAC,CAAE;AAChB,CAAC,CAED,oBACE;AACA3B,KAAA,CAAAE,SAAA,EAAAoF,QAAA,eACEtF,KAAA,QAAKuF,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrDtF,KAAA,QAAAsF,QAAA,eACExF,IAAA,OAAIyF,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC1DxF,IAAA,MAAGyF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAC,oDAAkD,CAAG,CAAC,EACtF,CAAC,cAENxF,IAAA,CAACJ,MAAM,EAAC8F,OAAO,CAAEA,CAAA,GAAMjF,kBAAkB,CAAC,IAAI,CAAE,CAAA+E,QAAA,CAAC,mBAEjD,CAAQ,CAAC,EACN,CAAC,CAEL1D,OAAO,eACN9B,IAAA,CAACL,KAAK,EACJ6E,IAAI,CAAC,SAAS,CACdnB,OAAO,CAAEvB,OAAQ,CACjB6D,OAAO,CAAEA,CAAA,GAAM5D,UAAU,CAAC,EAAE,CAAE,CAC9B0D,SAAS,CAAC,MAAM,CACjB,CACF,CAEAnF,WAAW,CAACiD,MAAM,GAAK,CAAC,cACvBvD,IAAA,CAACH,IAAI,EAAA2F,QAAA,cACHtF,KAAA,QAAKuF,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BxF,IAAA,QAAKyF,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCxF,IAAA,OAAIyF,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAChExF,IAAA,MAAGyF,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,0EAExC,CAAG,CAAC,cACJxF,IAAA,CAACJ,MAAM,EAAC8F,OAAO,CAAEA,CAAA,GAAMjF,kBAAkB,CAAC,IAAI,CAAE,CAAA+E,QAAA,CAAC,8BAEjD,CAAQ,CAAC,EACN,CAAC,CACF,CAAC,cAEPxF,IAAA,QAAKyF,SAAS,CAAC,sDAAsD,CAAAD,QAAA,CAClElF,WAAW,CAACmC,GAAG,CAAC6C,UAAU,eACzBpF,KAAA,CAACL,IAAI,EAAA2F,QAAA,eACHtF,KAAA,QAAKuF,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDxF,IAAA,OAAIyF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEF,UAAU,CAACzC,IAAI,CAAK,CAAC,cAC1D7C,IAAA,SAAMyF,SAAS,CAAE,6BACfH,UAAU,CAACtB,MAAM,GAAK,QAAQ,CAAG,cAAc,CAAG,aAAa,EAC9D,CAAAwB,QAAA,CACAF,UAAU,CAACtB,MAAM,GAAK,QAAQ,CAAG,QAAQ,CAAG,UAAU,CACnD,CAAC,EACJ,CAAC,cAEN9D,KAAA,QAAKuF,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BtF,KAAA,QAAKuF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCxF,IAAA,SAAMyF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,cACpDxF,IAAA,SAAAwF,QAAA,CACGF,UAAU,CAACrB,QAAQ,CAACH,KAAK,CAACiB,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACR,IAAI,GAAK,WAAW,CAAC,CAACjB,MAAM,CACvE,CAAC,EACJ,CAAC,cACNrD,KAAA,QAAKuF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCxF,IAAA,SAAMyF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,cACrDxF,IAAA,SAAMyF,SAAS,CAAC,YAAY,CAAAD,QAAA,CACzBF,UAAU,CAACrB,QAAQ,CAACJ,OAAO,GAAK,YAAY,CAAG,aAAa,CAAG,QAAQ,CACpE,CAAC,EACJ,CAAC,cACN3D,KAAA,QAAKuF,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnCxF,IAAA,SAAMyF,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAC,UAAQ,CAAM,CAAC,cACrDxF,IAAA,SAAAwF,QAAA,CAAOF,UAAU,CAACpB,OAAO,CAAO,CAAC,EAC9B,CAAC,EACH,CAAC,cAENhE,KAAA,QAAKuF,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BxF,IAAA,CAACJ,MAAM,EAACgG,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,IAAI,CAACH,OAAO,CAAEA,CAAA,GAAML,sBAAsB,CAACC,UAAU,CAAC3C,EAAE,CAAE,CAAA6C,QAAA,CACxFF,UAAU,CAACtB,MAAM,GAAK,QAAQ,CAAG,YAAY,CAAG,UAAU,CACrD,CAAC,cACThE,IAAA,CAACJ,MAAM,EAACiG,IAAI,CAAC,IAAI,CAACH,OAAO,CAAEA,CAAA,GAAMH,eAAe,CAACD,UAAU,CAAE,CAAAE,QAAA,CAAC,MAE9D,CAAQ,CAAC,EACN,CAAC,GApCGF,UAAU,CAAC3C,EAqChB,CACP,CAAC,CACC,CACN,CAGAnC,eAAe,eACdR,IAAA,QAAKyF,SAAS,CAAC,gFAAgF,CAAAD,QAAA,cAC7FtF,KAAA,QAAKuF,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DxF,IAAA,OAAIyF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,mBAAiB,CAAI,CAAC,CAEhE5D,KAAK,eACJ5B,IAAA,CAACL,KAAK,EACJ6E,IAAI,CAAC,OAAO,CACZnB,OAAO,CAAEzB,KAAM,CACf+D,OAAO,CAAEA,CAAA,GAAM9D,QAAQ,CAAC,EAAE,CAAE,CAC5B4D,SAAS,CAAC,MAAM,CACjB,CACF,cAEDzF,IAAA,QAAKyF,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxF,IAAA,CAACF,KAAK,EACJ6C,EAAE,CAAC,gBAAgB,CACnBE,IAAI,CAAC,gBAAgB,CACrBiD,KAAK,CAAC,iBAAiB,CACvBC,KAAK,CAAErF,iBAAkB,CACzBsF,QAAQ,CAAGC,CAAC,EAAKtF,oBAAoB,CAACsF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACtDI,QAAQ,MACT,CAAC,CACC,CAAC,cAENjG,KAAA,QAAKuF,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBxF,IAAA,UAAOyF,SAAS,CAAC,YAAY,CAAAD,QAAA,CAAC,SAAO,CAAO,CAAC,cAC7CtF,KAAA,QAAKuF,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBtF,KAAA,QAAKuF,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCxF,IAAA,UACEwE,IAAI,CAAC,OAAO,CACZ7B,EAAE,CAAC,eAAe,CAClBE,IAAI,CAAC,SAAS,CACdkD,KAAK,CAAC,QAAQ,CACdK,OAAO,CAAExF,oBAAoB,GAAK,QAAS,CAC3CoF,QAAQ,CAAEA,CAAA,GAAMnF,uBAAuB,CAAC,QAAQ,CAAE,CAClD4E,SAAS,CAAC,MAAM,CACjB,CAAC,cACFzF,IAAA,UAAOqG,OAAO,CAAC,eAAe,CAAAb,QAAA,CAAC,kCAAgC,CAAO,CAAC,EACpE,CAAC,cACNtF,KAAA,QAAKuF,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChCxF,IAAA,UACEwE,IAAI,CAAC,OAAO,CACZ7B,EAAE,CAAC,mBAAmB,CACtBE,IAAI,CAAC,SAAS,CACdkD,KAAK,CAAC,YAAY,CAClBK,OAAO,CAAExF,oBAAoB,GAAK,YAAa,CAC/CoF,QAAQ,CAAEA,CAAA,GAAMnF,uBAAuB,CAAC,YAAY,CAAE,CACtD4E,SAAS,CAAC,MAAM,CACjB,CAAC,cACFzF,IAAA,UAAOqG,OAAO,CAAC,mBAAmB,CAAAb,QAAA,CAAC,iDAA+C,CAAO,CAAC,EACvF,CAAC,EACH,CAAC,EACH,CAAC,cAGNtF,KAAA,QAAKuF,SAAS,CAAC,MAAM,CAAAD,QAAA,eAACxF,IAAA,OAAIyF,SAAS,CAAC,sBAAsB,CAAC,CAAC,cAC1DzF,IAAA,OAAIyF,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC5DtF,KAAA,QAAKuF,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAACxF,IAAA,OAAIyF,SAAS,CAAC,sBAAsB,CAAC,CAAC,CACnE3E,kBAAkB,CAACyC,MAAM,GAAK,CAAC,cAC9BvD,IAAA,MAAGyF,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,sBAAoB,CAAG,CAAC,CAEvE1E,kBAAkB,CAAC2B,GAAG,CAAC,CAACuC,IAAI,CAAEsB,KAAK,QAAAC,qBAAA,oBACjCrG,KAAA,QAAmBuF,SAAS,CAAC,iFAAiF,CAAAD,QAAA,eAACxF,IAAA,OAAIyF,SAAS,CAAC,sBAAsB,CAAC,CAAC,cACnJvF,KAAA,QAAAsF,QAAA,eACEtF,KAAA,SAAMuF,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAC,OAAK,CAACc,KAAK,CAAG,CAAC,CAAC,IAAE,EAAM,CAAC,CACtDtB,IAAI,CAACR,IAAI,GAAK,WAAW,eACxBtE,KAAA,SAAMuF,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAACxF,IAAA,OAAIyF,SAAS,CAAC,sBAAsB,CAAC,CAAC,cAClFzF,IAAA,SAAAwF,QAAA,CAAM,aAAW,CAAM,CAAC,cACxBxF,IAAA,CAACJ,MAAM,EACLgG,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,IAAI,CACTH,OAAO,CAAEA,CAAA,GAAM,CACb/D,qBAAqB,CAACqD,IAAI,CAACrC,EAAE,CAAC,CAC9BlB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CAAA+D,QAAA,CAGCR,IAAI,CAA2BL,UAAU,CACxC,EAAA4B,qBAAA,CAAArF,kBAAkB,CAACsF,IAAI,CAACC,CAAC,EAAIA,CAAC,CAAC9D,EAAE,GAAMqC,IAAI,CAA2BL,UAAU,CAAC,UAAA4B,qBAAA,iBAAjFA,qBAAA,CAAmF1D,IAAI,GAAI,kBAAkB,CAC7G,iBAAiB,CAEf,CAAC,EACL,CACP,CACAmC,IAAI,CAACR,IAAI,GAAK,OAAO,eACpBtE,KAAA,SAAMuF,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAACxF,IAAA,OAAIyF,SAAS,CAAC,sBAAsB,CAAC,CAAC,cAClFzF,IAAA,SAAAwF,QAAA,CAAM,UAAQ,CAAM,CAAC,cACrBxF,IAAA,CAACF,KAAK,EACJ6C,EAAE,CAAE,YAAYqC,IAAI,CAACrC,EAAE,EAAG,CAC1BE,IAAI,CAAE,YAAYmC,IAAI,CAACrC,EAAE,EAAG,CAC5B6B,IAAI,CAAC,QAAQ,CACbuB,KAAK,CAAEW,MAAM,CAAE1B,IAAI,CAAuBJ,QAAQ,CAAE,CACpDoB,QAAQ,CAAGC,CAAC,EAAK,CACf,KAAM,CAAAU,WAAW,CAAGC,QAAQ,CAACX,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,EAAE,CAAC,EAAI,CAAC,CACrD;AACAd,oBAAoB,CAACD,IAAI,CAACrC,EAAE,CAAE,CAAEiC,QAAQ,CAAEnB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEiD,WAAW,CAAE,CAAC,CAAC,CACvE,CAAE,CACFlB,SAAS,CAAC,sBAAsB,CACjC,CAAC,cACFvF,KAAA,WACE6F,KAAK,CAAGf,IAAI,CAAuBH,IAAK,CACxCmB,QAAQ,CAAGC,CAAC,EAAKhB,oBAAoB,CAACD,IAAI,CAACrC,EAAE,CAAE,CAAEkC,IAAI,CAAEoB,CAAC,CAACC,MAAM,CAACH,KAAmC,CAAC,CAAE,CACtGN,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eAE9DxF,IAAA,WAAQ+F,KAAK,CAAC,SAAS,CAAAP,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxCxF,IAAA,WAAQ+F,KAAK,CAAC,OAAO,CAAAP,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCxF,IAAA,WAAQ+F,KAAK,CAAC,MAAM,CAAAP,QAAA,CAAC,MAAI,CAAQ,CAAC,EAC5B,CAAC,EACL,CACP,EACE,CAAC,cAENxF,IAAA,CAACJ,MAAM,EACLgG,OAAO,CAAC,QAAQ,CAChBC,IAAI,CAAC,IAAI,CACTH,OAAO,CAAEA,CAAA,GAAMZ,sBAAsB,CAACE,IAAI,CAACrC,EAAE,CAAE,CAC/C8C,SAAS,CAAC,MAAM,CAAAD,QAAA,CACjB,OACS,CACF,CAAC,GAzDDR,IAAI,CAACrC,EA0DV,CAAC,EACP,CACF,EACE,CAAC,cAGNzC,KAAA,QAAKuF,SAAS,CAAC,UAAU,CAAAD,QAAA,eAACxF,IAAA,OAAIyF,SAAS,CAAC,sBAAsB,CAAC,CAAC,cAC9DzF,IAAA,CAACJ,MAAM,EACLgG,OAAO,CAAC,WAAW,CACnBH,SAAS,CAAC,QAAQ,CAClBC,OAAO,CAAEA,CAAA,GAAMzE,qBAAqB,CAAC,CAACD,kBAAkB,CAAE,CAAAwE,QAAA,CAC3D,YAED,CAAQ,CAAC,CACRxE,kBAAkB,eACjBd,KAAA,QAAKuF,SAAS,CAAC,4FAA4F,CAAAD,QAAA,eAACxF,IAAA,OAAIyF,SAAS,CAAC,sBAAsB,CAAC,CAAC,cAChJvF,KAAA,OAAAsF,QAAA,eACExF,IAAA,OAAAwF,QAAA,cACExF,IAAA,WACE0F,OAAO,CAAEA,CAAA,GAAMnB,iBAAiB,CAAC,WAAW,CAAE,CAC9CkB,SAAS,CAAC,mDAAmD,CAAAD,QAAA,CAC9D,YAED,CAAQ,CAAC,CACP,CAAC,cACLxF,IAAA,OAAAwF,QAAA,cACExF,IAAA,WACE0F,OAAO,CAAEA,CAAA,GAAMnB,iBAAiB,CAAC,OAAO,CAAE,CAC1CkB,SAAS,CAAC,mDAAmD,CAAAD,QAAA,CAC9D,cAED,CAAQ,CAAC,CACP,CAAC,EAEH,CAAC,EACF,CACN,EACE,CAAC,EACH,CAAC,cAENtF,KAAA,QAAKuF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCxF,IAAA,CAACJ,MAAM,EAACgG,OAAO,CAAC,WAAW,CAACF,OAAO,CAAEA,CAAA,GAAMjF,kBAAkB,CAAC,KAAK,CAAE,CAAA+E,QAAA,CAAC,QAEtE,CAAQ,CAAC,cACTxF,IAAA,CAACJ,MAAM,EAAC8F,OAAO,CAAEpC,sBAAuB,CAAAkC,QAAA,CAAC,QAEzC,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CACN,CAGAtD,aAAa,EAAIF,iBAAiB,eACjChC,IAAA,QAAKyF,SAAS,CAAC,gFAAgF,CAAAD,QAAA,cAC7FtF,KAAA,QAAKuF,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DtF,KAAA,OAAIuF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAC,mBAAiB,CAACxD,iBAAiB,CAACa,IAAI,EAAK,CAAC,CAExFjB,KAAK,eACJ5B,IAAA,CAACL,KAAK,EACJ6E,IAAI,CAAC,OAAO,CACZnB,OAAO,CAAEzB,KAAM,CACf+D,OAAO,CAAEA,CAAA,GAAM9D,QAAQ,CAAC,EAAE,CAAE,CAC5B4D,SAAS,CAAC,MAAM,CACjB,CACF,cAGDzF,IAAA,MAAGyF,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,gCAA8B,CAAG,CAAC,cAEtFtF,KAAA,QAAKuF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCxF,IAAA,CAACJ,MAAM,EAACgG,OAAO,CAAC,WAAW,CAACF,OAAO,CAAEA,CAAA,GAAMvD,gBAAgB,CAAC,KAAK,CAAE,CAAAqD,QAAA,CAAC,QAEpE,CAAQ,CAAC,cACTtF,KAAA,CAACN,MAAO,sCAAA4F,QAAA,EAAsC,GAAC,CAAgC,cAE/E,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CACN,CAGAhE,iBAAiB,eAChBxB,IAAA,QAAKyF,SAAS,CAAC,kFAAkF,CAAAD,QAAA,cAC/FtF,KAAA,QAAKuF,SAAS,CAAC,2EAA2E,CAAAD,QAAA,eACxFxF,IAAA,OAAIyF,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAC,uBAAqB,CAAI,CAAC,CAEpEpE,gBAAgB,eAAIpB,IAAA,MAAAwF,QAAA,CAAG,sBAAoB,CAAG,CAAC,CAC/ClE,cAAc,eAAItB,IAAA,CAACL,KAAK,EAAC6E,IAAI,CAAC,OAAO,CAACnB,OAAO,CAAE/B,cAAe,CAACqE,OAAO,CAAEA,CAAA,GAAMpE,iBAAiB,CAAC,IAAI,CAAE,CAAE,CAAC,cAE1GvB,IAAA,QAAKyF,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAC5C,CAACpE,gBAAgB,EAAI,CAACE,cAAc,eACnCtB,IAAA,OAAIyF,SAAS,CAAC,WAAW,CAAAD,QAAA,CACtBtE,kBAAkB,CAACqC,MAAM,CAAG,CAAC,CAC5BrC,kBAAkB,CAACuB,GAAG,CAACoE,QAAQ,eAC7B7G,IAAA,OAAAwF,QAAA,cACEtF,KAAA,WACEuF,SAAS,CAAC,sEAAsE,CAChFC,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIhE,kBAAkB,CAAE,CACtB;AACAuD,oBAAoB,CAACvD,kBAAkB,CAAE,CAAEiD,UAAU,CAAEkC,QAAQ,CAAClE,EAAG,CAAC,CAAC,CACvE,CACAlB,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAE,CAAA6D,QAAA,EAEDqB,QAAQ,CAAChE,IAAI,CAAC,GAAC,cAAA3C,KAAA,SAAMuF,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,OAAK,CAACqB,QAAQ,CAAClE,EAAE,CAAC,GAAC,EAAM,CAAC,EAClF,CAAC,EAbFkE,QAAQ,CAAClE,EAcd,CACL,CAAC,cAEF3C,IAAA,MAAGyF,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,sBAAoB,CAAG,CACvE,CACC,CACL,CACE,CAAC,cAENxF,IAAA,QAAKyF,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BxF,IAAA,CAACJ,MAAM,EACLgG,OAAO,CAAC,WAAW,CACnBF,OAAO,CAAEA,CAAA,GAAM,CACbjE,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAE,CAAA6D,QAAA,CACH,QAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CACN,EACD,CACF;AAAA,EAEJ,CAAC,CAED,cAAe,CAAAnF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}