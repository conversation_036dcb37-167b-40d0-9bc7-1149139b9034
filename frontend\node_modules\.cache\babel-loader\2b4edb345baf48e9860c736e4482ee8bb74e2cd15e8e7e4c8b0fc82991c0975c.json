{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Card=_ref=>{let{title,children,className='',...rest}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:`card ${className}`,...rest,children:[title&&/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold mb-4\",children:title}),children]});};export default Card;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Card", "_ref", "title", "children", "className", "rest"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/Card.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  title?: string;\n}\n\nconst Card: React.FC<CardProps> = ({ title, children, className = '', ...rest }) => {\n  return (\n    <div className={`card ${className}`} {...rest}>\n      {title && <h2 className=\"text-lg font-semibold mb-4\">{title}</h2>}\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAM1B,KAAM,CAAAC,IAAyB,CAAGC,IAAA,EAAkD,IAAjD,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,SAAS,CAAG,EAAE,CAAE,GAAGC,IAAK,CAAC,CAAAJ,IAAA,CAC7E,mBACEF,KAAA,QAAKK,SAAS,CAAE,QAAQA,SAAS,EAAG,IAAKC,IAAI,CAAAF,QAAA,EAC1CD,KAAK,eAAIL,IAAA,OAAIO,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CAAED,KAAK,CAAK,CAAC,CAChEC,QAAQ,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}