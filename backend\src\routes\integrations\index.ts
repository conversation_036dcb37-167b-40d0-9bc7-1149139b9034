import { Router } from 'express';

import {
  connectIntegration,
  disconnectIntegration,
  getAvailableIntegrations,
  getUserIntegrationConnections,
  syncIntegrationData,
  updateIntegrationSettings,
} from '../../controllers/integrations/integration.controller';
import { authenticate } from '../../middleware/auth.middleware';
import mosaicoRoutes from './mosaico.routes';

const router = Router();

// All routes require authentication
router.use(authenticate);

// Get available integrations
router.get('/available', getAvailableIntegrations as any);

// Connect integration
router.post('/connect', connectIntegration as any);

// Get user's integration connections
router.get('/connections', getUserIntegrationConnections as any);

// Update integration connection settings
router.put('/connections/:connectionId/settings', updateIntegrationSettings as any);

// Disconnect integration
router.put('/connections/:connectionId/disconnect', disconnectIntegration as any);

// Sync integration data
router.post('/connections/:connectionId/sync', syncIntegrationData as any);

// Mosaico-specific routes
router.use('/mosaico', mosaicoRoutes);

export default router;
