{"version": 3, "file": "static/js/852.8880ac38.chunk.js", "mappings": "0MAUA,MAAMA,EAAcC,IAClB,IAAKA,EAAY,MAAO,MACxB,IACE,OAAO,IAAIC,KAAKD,GAAYE,mBAAmB,QAAS,CACtDC,KAAM,UACNC,MAAO,OACPC,IAAK,WAET,CAAE,MAAOC,GAEP,OADAC,QAAQD,MAAM,yBAA0BA,GACjC,cACT,GAkUF,EA/T2BE,KAAO,IAADC,EAC/B,MAAM,KAAEC,IAASC,EAAAA,EAAAA,MAEVC,EAAiBC,IAAsBC,EAAAA,EAAAA,UAAS,CACrDC,MAAU,OAAJL,QAAI,IAAJA,OAAI,EAAJA,EAAMK,OAAQ,WACpBC,OAAW,OAAJN,QAAI,IAAJA,OAAI,EAAJA,EAAMM,QAAS,mBACtBC,aAAiB,OAAJP,QAAI,IAAJA,OAAI,EAAJA,EAAMO,cAAe,YAClCC,UAAc,OAAJR,QAAI,IAAJA,OAAI,EAAJA,EAAMQ,WAAY,WAGvBC,EAAiBC,IAAsBN,EAAAA,EAAAA,UAAS,CACrDO,gBAAiB,6CACjBC,gBAAiB,8EAGZC,EAAkBC,IAAuBV,EAAAA,EAAAA,UAAS,CACvDW,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,MAGZC,EAAgBC,IAAqBf,EAAAA,EAAAA,WAAS,IAC9CgB,EAAgBC,IAAqBjB,EAAAA,EAAAA,WAAS,IAC9CkB,EAAiBC,IAAsBnB,EAAAA,EAAAA,WAAS,IAChDoB,EAAeC,IAAoBrB,EAAAA,EAAAA,UAAS,IAmDnD,OAEEsB,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCF,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeF,SAAA,EAC5BC,EAAAA,EAAAA,MAACE,EAAAA,EAAI,CAACC,MAAM,mBAAkBJ,SAAA,CAC3BV,IACCQ,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CACJC,KAAK,UACLC,QAAQ,yCACRL,UAAU,UAIdD,EAAAA,EAAAA,MAAA,QAAMO,SA/DaC,IAC3BA,EAAEC,iBAEFC,YAAW,KACTpB,GAAkB,GAClBoB,YAAW,IAAMpB,GAAkB,IAAQ,IAAK,GAC/C,IAAI,EAyDqCS,SAAA,EAClCC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CF,SAAA,EACzDF,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACJC,GAAG,OACHpC,KAAK,OACLqC,MAAM,YACNC,MAAOzC,EAAgBG,KACvBuC,SAAWP,GAAMlC,EAAmB,IAAID,EAAiBG,KAAMgC,EAAEQ,OAAOF,QACxEG,UAAQ,KAGVpB,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACJC,GAAG,QACHpC,KAAK,QACL6B,KAAK,QACLQ,MAAM,gBACNC,MAAOzC,EAAgBI,MACvBsC,SAAWP,GAAMlC,EAAmB,IAAID,EAAiBI,MAAO+B,EAAEQ,OAAOF,QACzEG,UAAQ,QAIZjB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CF,SAAA,EACzDF,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACJC,GAAG,cACHpC,KAAK,cACLqC,MAAM,eACNC,MAAOzC,EAAgBK,YACvBqC,SAAWP,GAAMlC,EAAmB,IAAID,EAAiBK,YAAa8B,EAAEQ,OAAOF,WAGjFd,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOqB,QAAQ,WAAWjB,UAAU,aAAYF,SAAC,cACjDC,EAAAA,EAAAA,MAAA,UACEY,GAAG,WACHpC,KAAK,WACLsC,MAAOzC,EAAgBM,SACvBoC,SAAWP,GAAMlC,EAAmB,IAAID,EAAiBM,SAAU6B,EAAEQ,OAAOF,QAC5Eb,UAAU,oBAAmBF,SAAA,EAE7BF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,SAAQf,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,SAAQf,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,SAAQf,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,qBACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,sBACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,qBACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,qBACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,QAAOf,SAAC,WACtBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,SAAQf,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,SAAQf,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,SAAQf,SAAC,qBAK7BF,EAAAA,EAAAA,KAACsB,EAAAA,EAAM,CAACd,KAAK,SAAQN,SAAC,iCAM1BC,EAAAA,EAAAA,MAACE,EAAAA,EAAI,CAACC,MAAM,sBAAsBF,UAAU,OAAMF,SAAA,CAC/CR,IACCM,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CACJC,KAAK,UACLC,QAAQ,0CACRL,UAAU,UAIdJ,EAAAA,EAAAA,KAAA,KAAGI,UAAU,2BAA0BF,SAAC,mHAIxCC,EAAAA,EAAAA,MAAA,QAAMO,SA9IaC,IAC3BA,EAAEC,iBAEFC,YAAW,KACTlB,GAAkB,GAClBkB,YAAW,IAAMlB,GAAkB,IAAQ,IAAK,GAC/C,IAAI,EAwIqCO,SAAA,EAClCC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOkB,QAAQ,kBAAkBjB,UAAU,aAAYF,SAAA,CAAC,qBACrCF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,eAAcF,SAAC,UAElDF,EAAAA,EAAAA,KAAA,YACEe,GAAG,kBACHpC,KAAK,kBACLsC,MAAOlC,EAAgBE,gBACvBiC,SAAWP,GAAM3B,EAAmB,IAAID,EAAiBE,gBAAiB0B,EAAEQ,OAAOF,QACnFG,UAAQ,EACRG,KAAM,EACNnB,UAAU,0BAIdD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOkB,QAAQ,kBAAkBjB,UAAU,aAAYF,SAAA,CAAC,qBACrCF,EAAAA,EAAAA,KAAA,QAAMI,UAAU,eAAcF,SAAC,UAElDF,EAAAA,EAAAA,KAAA,YACEe,GAAG,kBACHpC,KAAK,kBACLsC,MAAOlC,EAAgBG,gBACvBgC,SAAWP,GAAM3B,EAAmB,IAAID,EAAiBG,gBAAiByB,EAAEQ,OAAOF,QACnFG,UAAQ,EACRG,KAAM,EACNnB,UAAU,0BAIdJ,EAAAA,EAAAA,KAACsB,EAAAA,EAAM,CAACd,KAAK,SAAQN,SAAC,qCAO5BC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAACE,EAAAA,EAAI,CAACC,MAAM,kBAAiBJ,SAAA,CAC1BN,IACCI,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CACJC,KAAK,UACLC,QAAQ,iCACRL,UAAU,SAIbN,IACCE,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CACJC,KAAK,QACLC,QAASX,EACT0B,QAASA,IAAMzB,EAAiB,IAChCK,UAAU,UAIdD,EAAAA,EAAAA,MAAA,QAAMO,SA9LcC,IAC5BA,EAAEC,iBAEGzB,EAAiBE,iBAAoBF,EAAiBG,aAAgBH,EAAiBI,gBAKxFJ,EAAiBG,cAAgBH,EAAiBI,gBAKlDJ,EAAiBG,YAAYmC,OAAS,EACxC1B,EAAiB,mDAKnBc,YAAW,KACThB,GAAmB,GACnBT,EAAoB,CAClBC,gBAAiB,GACjBC,YAAa,GACbC,gBAAiB,KAEnBQ,EAAiB,IACjBc,YAAW,IAAMhB,GAAmB,IAAQ,IAAK,GAChD,KAnBDE,EAAiB,8BALjBA,EAAiB,mCAwBZ,EAkKsCG,SAAA,EACnCF,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACJC,GAAG,kBACHpC,KAAK,kBACL6B,KAAK,WACLQ,MAAM,mBACNC,MAAO9B,EAAiBE,gBACxB6B,SAAWP,GAAMvB,EAAoB,IAAID,EAAkBE,gBAAiBsB,EAAEQ,OAAOF,QACrFG,UAAQ,EACRhB,UAAU,UAGZJ,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACJC,GAAG,cACHpC,KAAK,cACL6B,KAAK,WACLQ,MAAM,eACNC,MAAO9B,EAAiBG,YACxB4B,SAAWP,GAAMvB,EAAoB,IAAID,EAAkBG,YAAaqB,EAAEQ,OAAOF,QACjFG,UAAQ,EACRhB,UAAU,UAGZJ,EAAAA,EAAAA,KAACc,EAAAA,EAAK,CACJC,GAAG,kBACHpC,KAAK,kBACL6B,KAAK,WACLQ,MAAM,uBACNC,MAAO9B,EAAiBI,gBACxB2B,SAAWP,GAAMvB,EAAoB,IAAID,EAAkBI,gBAAiBoB,EAAEQ,OAAOF,QACrFG,UAAQ,EACRhB,UAAU,UAGZJ,EAAAA,EAAAA,KAACsB,EAAAA,EAAM,CAACd,KAAK,SAAQN,SAAC,2BAM1BC,EAAAA,EAAAA,MAACE,EAAAA,EAAI,CAACC,MAAM,sBAAsBF,UAAU,OAAMF,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBF,SAAC,kBACrCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,yBAAwBF,UAAM,OAAJ5B,QAAI,IAAJA,OAAI,EAAJA,EAAMoD,cAAe,YAGhEvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBF,SAAC,kBACrCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaF,SAAEvC,EAAe,OAAJW,QAAI,IAAJA,OAAI,EAAJA,EAAMqD,iBAGjDxB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBF,SAAC,mBACrCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaF,SACrB,OAAJ5B,QAAI,IAAJA,GAAY,QAARD,EAAJC,EAAMsD,cAAM,IAAAvD,GAAZA,EAAcwD,QACZ7B,EAAAA,EAAAA,KAAA,QAAMI,UAAW,yCACQ,WAAvB9B,EAAKsD,OAAOC,OAAsB,eACX,YAAvBvD,EAAKsD,OAAOC,OAAuB,gBACnC,cACC3B,SACA5B,EAAKsD,OAAOC,UAGf7B,EAAAA,EAAAA,KAAA,QAAMI,UAAU,wCAAuCF,SAAC,oBAK/DC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBF,SAAC,qBACrCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaF,SAAC,YAG/BC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMF,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,sBAAqBF,SAAC,qBACrCF,EAAAA,EAAAA,KAAA,OAAKI,UAAU,cAAaF,SAAC,qBAMvC,C", "sources": ["pages/Settings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\n// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport { useAuth } from '../contexts/AuthContext'; // Import useAuth\n\n// Helper function to format date\nconst formatDate = (dateString: string | undefined): string => {\n  if (!dateString) return 'N/A';\n  try {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    return 'Invalid Date';\n  }\n};\n\nconst Settings: React.FC = () => {\n  const { user } = useAuth(); // Get user from context\n\n  const [generalSettings, setGeneralSettings] = useState({\n    name: user?.name || '<PERSON>', // Initialize with user data or fallback\n    email: user?.email || '<EMAIL>', // Initialize with user data or fallback\n    companyName: user?.companyName || 'Acme Inc.', // Optional: Add companyName to user type if needed\n    timezone: user?.timezone || 'UTC-5', // Optional: Add timezone to user type if needed\n  });\n  \n  const [canSpamSettings, setCanSpamSettings] = useState({\n    physicalAddress: '123 Main St, Suite 100, New York, NY 10001',\n    unsubscribeText: 'If you no longer wish to receive these emails, you can unsubscribe here.',\n  });\n  \n  const [passwordSettings, setPasswordSettings] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n  \n  const [generalSuccess, setGeneralSuccess] = useState(false);\n  const [canSpamSuccess, setCanSpamSuccess] = useState(false);\n  const [passwordSuccess, setPasswordSuccess] = useState(false);\n  const [passwordError, setPasswordError] = useState('');\n  \n  const handleGeneralSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setGeneralSuccess(true);\n      setTimeout(() => setGeneralSuccess(false), 3000);\n    }, 500);\n  };\n  \n  const handleCanSpamSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setCanSpamSuccess(true);\n      setTimeout(() => setCanSpamSuccess(false), 3000);\n    }, 500);\n  };\n  \n  const handlePasswordSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!passwordSettings.currentPassword || !passwordSettings.newPassword || !passwordSettings.confirmPassword) {\n      setPasswordError('All password fields are required');\n      return;\n    }\n    \n    if (passwordSettings.newPassword !== passwordSettings.confirmPassword) {\n      setPasswordError('New passwords do not match');\n      return;\n    }\n    \n    if (passwordSettings.newPassword.length < 8) {\n      setPasswordError('New password must be at least 8 characters long');\n      return;\n    }\n    \n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setPasswordSuccess(true);\n      setPasswordSettings({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n      });\n      setPasswordError('');\n      setTimeout(() => setPasswordSuccess(false), 3000);\n    }, 500);\n  };\n  \n  return (\n    // <Layout title=\"Settings\">\n    <>\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2\">\n          <Card title=\"General Settings\">\n            {generalSuccess && (\n              <Alert\n                type=\"success\"\n                message=\"General settings updated successfully!\"\n                className=\"mb-4\"\n              />\n            )}\n            \n            <form onSubmit={handleGeneralSubmit}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                <Input\n                  id=\"name\"\n                  name=\"name\"\n                  label=\"Full Name\"\n                  value={generalSettings.name}\n                  onChange={(e) => setGeneralSettings({...generalSettings, name: e.target.value})}\n                  required\n                />\n                \n                <Input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  label=\"Email Address\"\n                  value={generalSettings.email}\n                  onChange={(e) => setGeneralSettings({...generalSettings, email: e.target.value})}\n                  required\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n                <Input\n                  id=\"companyName\"\n                  name=\"companyName\"\n                  label=\"Company Name\"\n                  value={generalSettings.companyName}\n                  onChange={(e) => setGeneralSettings({...generalSettings, companyName: e.target.value})}\n                />\n                \n                <div>\n                  <label htmlFor=\"timezone\" className=\"form-label\">Timezone</label>\n                  <select\n                    id=\"timezone\"\n                    name=\"timezone\"\n                    value={generalSettings.timezone}\n                    onChange={(e) => setGeneralSettings({...generalSettings, timezone: e.target.value})}\n                    className=\"form-input w-full\"\n                  >\n                    <option value=\"UTC-12\">UTC-12</option>\n                    <option value=\"UTC-11\">UTC-11</option>\n                    <option value=\"UTC-10\">UTC-10</option>\n                    <option value=\"UTC-9\">UTC-9</option>\n                    <option value=\"UTC-8\">UTC-8 (Pacific)</option>\n                    <option value=\"UTC-7\">UTC-7 (Mountain)</option>\n                    <option value=\"UTC-6\">UTC-6 (Central)</option>\n                    <option value=\"UTC-5\">UTC-5 (Eastern)</option>\n                    <option value=\"UTC-4\">UTC-4</option>\n                    <option value=\"UTC-3\">UTC-3</option>\n                    <option value=\"UTC-2\">UTC-2</option>\n                    <option value=\"UTC-1\">UTC-1</option>\n                    <option value=\"UTC+0\">UTC+0</option>\n                    <option value=\"UTC+1\">UTC+1</option>\n                    <option value=\"UTC+2\">UTC+2</option>\n                    <option value=\"UTC+3\">UTC+3</option>\n                    <option value=\"UTC+4\">UTC+4</option>\n                    <option value=\"UTC+5\">UTC+5</option>\n                    <option value=\"UTC+6\">UTC+6</option>\n                    <option value=\"UTC+7\">UTC+7</option>\n                    <option value=\"UTC+8\">UTC+8</option>\n                    <option value=\"UTC+9\">UTC+9</option>\n                    <option value=\"UTC+10\">UTC+10</option>\n                    <option value=\"UTC+11\">UTC+11</option>\n                    <option value=\"UTC+12\">UTC+12</option>\n                  </select>\n                </div>\n              </div>\n              \n              <Button type=\"submit\">\n                Save General Settings\n              </Button>\n            </form>\n          </Card>\n          \n          <Card title=\"CAN-SPAM Compliance\" className=\"mt-6\">\n            {canSpamSuccess && (\n              <Alert\n                type=\"success\"\n                message=\"CAN-SPAM settings updated successfully!\"\n                className=\"mb-4\"\n              />\n            )}\n            \n            <p className=\"text-text-secondary mb-4\">\n              The CAN-SPAM Act requires that all commercial emails include your physical address and an unsubscribe option.\n            </p>\n            \n            <form onSubmit={handleCanSpamSubmit}>\n              <div className=\"mb-4\">\n                <label htmlFor=\"physicalAddress\" className=\"form-label\">\n                  Physical Address <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"physicalAddress\"\n                  name=\"physicalAddress\"\n                  value={canSpamSettings.physicalAddress}\n                  onChange={(e) => setCanSpamSettings({...canSpamSettings, physicalAddress: e.target.value})}\n                  required\n                  rows={3}\n                  className=\"form-input w-full\"\n                ></textarea>\n              </div>\n              \n              <div className=\"mb-6\">\n                <label htmlFor=\"unsubscribeText\" className=\"form-label\">\n                  Unsubscribe Text <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"unsubscribeText\"\n                  name=\"unsubscribeText\"\n                  value={canSpamSettings.unsubscribeText}\n                  onChange={(e) => setCanSpamSettings({...canSpamSettings, unsubscribeText: e.target.value})}\n                  required\n                  rows={2}\n                  className=\"form-input w-full\"\n                ></textarea>\n              </div>\n              \n              <Button type=\"submit\">\n                Save CAN-SPAM Settings\n              </Button>\n            </form>\n          </Card>\n        </div>\n        \n        <div>\n          <Card title=\"Change Password\">\n            {passwordSuccess && (\n              <Alert\n                type=\"success\"\n                message=\"Password updated successfully!\"\n                className=\"mb-4\"\n              />\n            )}\n            \n            {passwordError && (\n              <Alert\n                type=\"error\"\n                message={passwordError}\n                onClose={() => setPasswordError('')}\n                className=\"mb-4\"\n              />\n            )}\n            \n            <form onSubmit={handlePasswordSubmit}>\n              <Input\n                id=\"currentPassword\"\n                name=\"currentPassword\"\n                type=\"password\"\n                label=\"Current Password\"\n                value={passwordSettings.currentPassword}\n                onChange={(e) => setPasswordSettings({...passwordSettings, currentPassword: e.target.value})}\n                required\n                className=\"mb-4\"\n              />\n              \n              <Input\n                id=\"newPassword\"\n                name=\"newPassword\"\n                type=\"password\"\n                label=\"New Password\"\n                value={passwordSettings.newPassword}\n                onChange={(e) => setPasswordSettings({...passwordSettings, newPassword: e.target.value})}\n                required\n                className=\"mb-4\"\n              />\n              \n              <Input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                label=\"Confirm New Password\"\n                value={passwordSettings.confirmPassword}\n                onChange={(e) => setPasswordSettings({...passwordSettings, confirmPassword: e.target.value})}\n                required\n                className=\"mb-6\"\n              />\n              \n              <Button type=\"submit\">\n                Update Password\n              </Button>\n            </form>\n          </Card>\n          \n          <Card title=\"Account Information\" className=\"mt-6\">\n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Account Type</div>\n              <div className=\"font-medium capitalize\">{user?.accountType || 'N/A'}</div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Member Since</div>\n              <div className=\"font-medium\">{formatDate(user?.createdAt)}</div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Domain Status</div>\n              <div className=\"font-medium\">\n                {user?.domain?.status ? (\n                   <span className={`px-2 py-1 text-xs rounded capitalize ${ \n                     user.domain.status === 'active' ? 'bg-green-800' : \n                     user.domain.status === 'pending' ? 'bg-yellow-800' : \n                     'bg-red-800'\n                   }`}>\n                     {user.domain.status}\n                   </span>\n                ) : (\n                   <span className=\"px-2 py-1 text-xs rounded bg-gray-700\">Not Setup</span>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Flows Purchased</div>\n              <div className=\"font-medium\">N/A</div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Flows Remaining</div>\n              <div className=\"font-medium\">N/A</div>\n            </div>\n          </Card>\n        </div>\n      </div>\n    </>\n    // </Layout>\n  );\n};\n\nexport default Settings;\n"], "names": ["formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "error", "console", "Settings", "_user$domain", "user", "useAuth", "generalSettings", "setGeneralSettings", "useState", "name", "email", "companyName", "timezone", "canSpamSettings", "setCanSpamSettings", "<PERSON><PERSON><PERSON>ress", "unsubscribeText", "passwordSettings", "setPasswordSettings", "currentPassword", "newPassword", "confirmPassword", "generalSuccess", "setGeneralSuccess", "canSpamSuccess", "setCanSpamSuccess", "passwordSuccess", "setPasswordSuccess", "passwordError", "setPasswordError", "_jsx", "_Fragment", "children", "_jsxs", "className", "Card", "title", "<PERSON><PERSON>", "type", "message", "onSubmit", "e", "preventDefault", "setTimeout", "Input", "id", "label", "value", "onChange", "target", "required", "htmlFor", "<PERSON><PERSON>", "rows", "onClose", "length", "accountType", "createdAt", "domain", "status"], "sourceRoot": ""}