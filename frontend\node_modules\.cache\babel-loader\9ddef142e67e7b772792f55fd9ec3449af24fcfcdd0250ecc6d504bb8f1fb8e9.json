{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';// Import all icons\nimport{ArrowsUpDownIcon,Bars3Icon,BeakerIcon,CalendarIcon,ChartBarIcon,ClockIcon,CursorArrowRaysIcon,DevicePhoneMobileIcon,HomeIcon,InboxIcon,MapIcon,PencilSquareIcon,PuzzlePieceIcon,SparklesIcon,StarIcon,UsersIcon,XMarkIcon}from'@heroicons/react/24/outline';// Define the sidebar navigation items\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const navigationItems=[{name:'Dashboard',path:'/',icon:'HomeIcon'},{name:'AI Content Generator',path:'/ai-content-generator',icon:'SparklesIcon'},{name:'Personalization Editor',path:'/personalization-editor',icon:'PencilSquareIcon'},{name:'Interactive Elements',path:'/interactive-elements',icon:'CursorArrowRaysIcon'},{name:'Send Time Optimization',path:'/send-time-optimization',icon:'ClockIcon'},{name:'A/B Testing',path:'/ab-testing',icon:'BeakerIcon'},{name:'Segment Builder',path:'/segment-builder',icon:'UsersIcon'},{name:'Deliverability Dashboard',path:'/deliverability-dashboard',icon:'InboxIcon'},{name:'Journey Builder',path:'/journey-builder',icon:'MapIcon'},{name:'Integration Marketplace',path:'/integration-marketplace',icon:'PuzzlePieceIcon'},{name:'Mobile Preview',path:'/mobile-preview',icon:'DevicePhoneMobileIcon'},{name:'Advanced Analytics',path:'/advanced-analytics',icon:'ChartBarIcon'},{name:'Template Recommendations',path:'/template-recommendations',icon:'StarIcon'},{name:'Scheduling & Automation',path:'/scheduling-automation',icon:'CalendarIcon'},{name:'Data Export & Import',path:'/data-export-import',icon:'ArrowsUpDownIcon'}];// Map of icon names to components\nconst iconMap={HomeIcon,SparklesIcon,PencilSquareIcon,CursorArrowRaysIcon,ClockIcon,BeakerIcon,UsersIcon,InboxIcon,MapIcon,PuzzlePieceIcon,DevicePhoneMobileIcon,ChartBarIcon,StarIcon,CalendarIcon,ArrowsUpDownIcon};const Sidebar=_ref=>{let{children}=_ref;const[sidebarOpen,setSidebarOpen]=React.useState(false);return/*#__PURE__*/_jsxs(\"div\",{className:\"flex h-screen bg-gray-100\",children:[/*#__PURE__*/_jsx(\"div\",{className:`fixed inset-0 z-20 bg-gray-600 bg-opacity-75 transition-opacity ease-linear duration-300 ${sidebarOpen?'opacity-100':'opacity-0 pointer-events-none'}`,onClick:()=>setSidebarOpen(false)}),/*#__PURE__*/_jsxs(\"div\",{className:`fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transform transition ease-in-out duration-300 ${sidebarOpen?'translate-x-0':'-translate-x-full'} md:translate-x-0 md:static md:h-screen md:z-auto`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16 px-4 border-b border-gray-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"img\",{className:\"h-8 w-auto\",src:\"/logo.svg\",alt:\"Driftly\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-xl font-semibold text-gray-800\",children:\"Driftly\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"md:hidden\",onClick:()=>setSidebarOpen(false),children:/*#__PURE__*/_jsx(XMarkIcon,{className:\"h-6 w-6 text-gray-500\"})})]}),/*#__PURE__*/_jsx(\"nav\",{className:\"mt-5 px-2 space-y-1 overflow-y-auto h-[calc(100vh-4rem)]\",children:navigationItems.map(item=>{const Icon=iconMap[item.icon];return/*#__PURE__*/_jsxs(Link,{to:item.path,className:\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\",children:[/*#__PURE__*/_jsx(Icon,{className:\"mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500\"}),item.name]},item.name);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white shadow-sm z-10\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16 px-4\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"md:hidden\",onClick:()=>setSidebarOpen(true),children:/*#__PURE__*/_jsx(Bars3Icon,{className:\"h-6 w-6 text-gray-500\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center\",children:/*#__PURE__*/_jsx(\"div\",{className:\"ml-3 relative\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-full w-full text-gray-300\",fill:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-2 text-sm font-medium text-gray-700\",children:\"User\"})]})})})]})}),/*#__PURE__*/_jsx(\"main\",{className:\"flex-1 overflow-auto bg-gray-100 p-4\",children:children})]})]});};export default Sidebar;", "map": {"version": 3, "names": ["React", "Link", "ArrowsUpDownIcon", "Bars3Icon", "BeakerIcon", "CalendarIcon", "ChartBarIcon", "ClockIcon", "CursorArrowRaysIcon", "DevicePhoneMobileIcon", "HomeIcon", "InboxIcon", "MapIcon", "PencilSquareIcon", "PuzzlePieceIcon", "SparklesIcon", "StarIcon", "UsersIcon", "XMarkIcon", "jsx", "_jsx", "jsxs", "_jsxs", "navigationItems", "name", "path", "icon", "iconMap", "Sidebar", "_ref", "children", "sidebarOpen", "setSidebarOpen", "useState", "className", "onClick", "src", "alt", "map", "item", "Icon", "to", "fill", "viewBox", "d"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { Link } from 'react-router-dom';\n\n// Import all icons\nimport {\n  ArrowsUpDownIcon,\n  Bars3Icon,\n  BeakerIcon,\n  CalendarIcon,\n  ChartBarIcon,\n  ClockIcon,\n  CursorArrowRaysIcon,\n  DevicePhoneMobileIcon,\n  HomeIcon,\n  InboxIcon,\n  MapIcon,\n  PencilSquareIcon,\n  PuzzlePieceIcon,\n  SparklesIcon,\n  StarIcon,\n  UsersIcon,\n  XMarkIcon,\n} from '@heroicons/react/24/outline';\n\n// Define the sidebar navigation items\nconst navigationItems = [\n  { name: 'Dashboard', path: '/', icon: 'HomeIcon' },\n  { name: 'AI Content Generator', path: '/ai-content-generator', icon: 'SparklesIcon' },\n  { name: 'Personalization Editor', path: '/personalization-editor', icon: 'PencilSquareIcon' },\n  { name: 'Interactive Elements', path: '/interactive-elements', icon: 'CursorArrowRaysIcon' },\n  { name: 'Send Time Optimization', path: '/send-time-optimization', icon: 'ClockIcon' },\n  { name: 'A/B Testing', path: '/ab-testing', icon: 'BeakerIcon' },\n  { name: 'Segment Builder', path: '/segment-builder', icon: 'UsersIcon' },\n  { name: 'Deliverability Dashboard', path: '/deliverability-dashboard', icon: 'InboxIcon' },\n  { name: 'Journey Builder', path: '/journey-builder', icon: 'MapIcon' },\n  { name: 'Integration Marketplace', path: '/integration-marketplace', icon: 'PuzzlePieceIcon' },\n  { name: 'Mobile Preview', path: '/mobile-preview', icon: 'DevicePhoneMobileIcon' },\n  { name: 'Advanced Analytics', path: '/advanced-analytics', icon: 'ChartBarIcon' },\n  { name: 'Template Recommendations', path: '/template-recommendations', icon: 'StarIcon' },\n  { name: 'Scheduling & Automation', path: '/scheduling-automation', icon: 'CalendarIcon' },\n  { name: 'Data Export & Import', path: '/data-export-import', icon: 'ArrowsUpDownIcon' },\n];\n\n// Map of icon names to components\nconst iconMap = {\n  HomeIcon,\n  SparklesIcon,\n  PencilSquareIcon,\n  CursorArrowRaysIcon,\n  ClockIcon,\n  BeakerIcon,\n  UsersIcon,\n  InboxIcon,\n  MapIcon,\n  PuzzlePieceIcon,\n  DevicePhoneMobileIcon,\n  ChartBarIcon,\n  StarIcon,\n  CalendarIcon,\n  ArrowsUpDownIcon,\n};\n\ninterface SidebarProps {\n  children: React.ReactNode;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ children }) => {\n  const [sidebarOpen, setSidebarOpen] = React.useState(false);\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Mobile sidebar overlay */}\n      <div\n        className={`fixed inset-0 z-20 bg-gray-600 bg-opacity-75 transition-opacity ease-linear duration-300 ${\n          sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'\n        }`}\n        onClick={() => setSidebarOpen(false)}\n      ></div>\n\n      {/* Mobile sidebar */}\n      <div\n        className={`fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transform transition ease-in-out duration-300 ${\n          sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n        } md:translate-x-0 md:static md:h-screen md:z-auto`}\n      >\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n          <div className=\"flex items-center\">\n            <img\n              className=\"h-8 w-auto\"\n              src=\"/logo.svg\"\n              alt=\"Driftly\"\n            />\n            <span className=\"ml-2 text-xl font-semibold text-gray-800\">Driftly</span>\n          </div>\n          <button\n            className=\"md:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          >\n            <XMarkIcon className=\"h-6 w-6 text-gray-500\" />\n          </button>\n        </div>\n        <nav className=\"mt-5 px-2 space-y-1 overflow-y-auto h-[calc(100vh-4rem)]\">\n          {navigationItems.map((item) => {\n            const Icon = iconMap[item.icon as keyof typeof iconMap];\n            return (\n              <Link\n                key={item.name}\n                to={item.path}\n                className=\"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n              >\n                <Icon className=\"mr-3 h-6 w-6 text-gray-400 group-hover:text-gray-500\" />\n                {item.name}\n              </Link>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Top header */}\n        <div className=\"bg-white shadow-sm z-10\">\n          <div className=\"flex items-center justify-between h-16 px-4\">\n            <button\n              className=\"md:hidden\"\n              onClick={() => setSidebarOpen(true)}\n            >\n              <Bars3Icon className=\"h-6 w-6 text-gray-500\" />\n            </button>\n            <div className=\"flex items-center\">\n              {/* User profile dropdown would go here */}\n              <div className=\"ml-3 relative\">\n                <div className=\"flex items-center\">\n                  <span className=\"inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100\">\n                    <svg className=\"h-full w-full text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path d=\"M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z\" />\n                    </svg>\n                  </span>\n                  <span className=\"ml-2 text-sm font-medium text-gray-700\">User</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 overflow-auto bg-gray-100 p-4\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,OAASC,IAAI,KAAQ,kBAAkB,CAEvC;AACA,OACEC,gBAAgB,CAChBC,SAAS,CACTC,UAAU,CACVC,YAAY,CACZC,YAAY,CACZC,SAAS,CACTC,mBAAmB,CACnBC,qBAAqB,CACrBC,QAAQ,CACRC,SAAS,CACTC,OAAO,CACPC,gBAAgB,CAChBC,eAAe,CACfC,YAAY,CACZC,QAAQ,CACRC,SAAS,CACTC,SAAS,KACJ,6BAA6B,CAEpC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,eAAe,CAAG,CACtB,CAAEC,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAE,GAAG,CAAEC,IAAI,CAAE,UAAW,CAAC,CAClD,CAAEF,IAAI,CAAE,sBAAsB,CAAEC,IAAI,CAAE,uBAAuB,CAAEC,IAAI,CAAE,cAAe,CAAC,CACrF,CAAEF,IAAI,CAAE,wBAAwB,CAAEC,IAAI,CAAE,yBAAyB,CAAEC,IAAI,CAAE,kBAAmB,CAAC,CAC7F,CAAEF,IAAI,CAAE,sBAAsB,CAAEC,IAAI,CAAE,uBAAuB,CAAEC,IAAI,CAAE,qBAAsB,CAAC,CAC5F,CAAEF,IAAI,CAAE,wBAAwB,CAAEC,IAAI,CAAE,yBAAyB,CAAEC,IAAI,CAAE,WAAY,CAAC,CACtF,CAAEF,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAE,aAAa,CAAEC,IAAI,CAAE,YAAa,CAAC,CAChE,CAAEF,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,kBAAkB,CAAEC,IAAI,CAAE,WAAY,CAAC,CACxE,CAAEF,IAAI,CAAE,0BAA0B,CAAEC,IAAI,CAAE,2BAA2B,CAAEC,IAAI,CAAE,WAAY,CAAC,CAC1F,CAAEF,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,kBAAkB,CAAEC,IAAI,CAAE,SAAU,CAAC,CACtE,CAAEF,IAAI,CAAE,yBAAyB,CAAEC,IAAI,CAAE,0BAA0B,CAAEC,IAAI,CAAE,iBAAkB,CAAC,CAC9F,CAAEF,IAAI,CAAE,gBAAgB,CAAEC,IAAI,CAAE,iBAAiB,CAAEC,IAAI,CAAE,uBAAwB,CAAC,CAClF,CAAEF,IAAI,CAAE,oBAAoB,CAAEC,IAAI,CAAE,qBAAqB,CAAEC,IAAI,CAAE,cAAe,CAAC,CACjF,CAAEF,IAAI,CAAE,0BAA0B,CAAEC,IAAI,CAAE,2BAA2B,CAAEC,IAAI,CAAE,UAAW,CAAC,CACzF,CAAEF,IAAI,CAAE,yBAAyB,CAAEC,IAAI,CAAE,wBAAwB,CAAEC,IAAI,CAAE,cAAe,CAAC,CACzF,CAAEF,IAAI,CAAE,sBAAsB,CAAEC,IAAI,CAAE,qBAAqB,CAAEC,IAAI,CAAE,kBAAmB,CAAC,CACxF,CAED;AACA,KAAM,CAAAC,OAAO,CAAG,CACdjB,QAAQ,CACRK,YAAY,CACZF,gBAAgB,CAChBL,mBAAmB,CACnBD,SAAS,CACTH,UAAU,CACVa,SAAS,CACTN,SAAS,CACTC,OAAO,CACPE,eAAe,CACfL,qBAAqB,CACrBH,YAAY,CACZU,QAAQ,CACRX,YAAY,CACZH,gBACF,CAAC,CAMD,KAAM,CAAA0B,OAA+B,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACnD,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGhC,KAAK,CAACiC,QAAQ,CAAC,KAAK,CAAC,CAE3D,mBACEX,KAAA,QAAKY,SAAS,CAAC,2BAA2B,CAAAJ,QAAA,eAExCV,IAAA,QACEc,SAAS,CAAE,4FACTH,WAAW,CAAG,aAAa,CAAG,+BAA+B,EAC5D,CACHI,OAAO,CAAEA,CAAA,GAAMH,cAAc,CAAC,KAAK,CAAE,CACjC,CAAC,cAGPV,KAAA,QACEY,SAAS,CAAE,qGACTH,WAAW,CAAG,eAAe,CAAG,mBAAmB,mDACD,CAAAD,QAAA,eAEpDR,KAAA,QAAKY,SAAS,CAAC,sEAAsE,CAAAJ,QAAA,eACnFR,KAAA,QAAKY,SAAS,CAAC,mBAAmB,CAAAJ,QAAA,eAChCV,IAAA,QACEc,SAAS,CAAC,YAAY,CACtBE,GAAG,CAAC,WAAW,CACfC,GAAG,CAAC,SAAS,CACd,CAAC,cACFjB,IAAA,SAAMc,SAAS,CAAC,0CAA0C,CAAAJ,QAAA,CAAC,SAAO,CAAM,CAAC,EACtE,CAAC,cACNV,IAAA,WACEc,SAAS,CAAC,WAAW,CACrBC,OAAO,CAAEA,CAAA,GAAMH,cAAc,CAAC,KAAK,CAAE,CAAAF,QAAA,cAErCV,IAAA,CAACF,SAAS,EAACgB,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACzC,CAAC,EACN,CAAC,cACNd,IAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAJ,QAAA,CACtEP,eAAe,CAACe,GAAG,CAAEC,IAAI,EAAK,CAC7B,KAAM,CAAAC,IAAI,CAAGb,OAAO,CAACY,IAAI,CAACb,IAAI,CAAyB,CACvD,mBACEJ,KAAA,CAACrB,IAAI,EAEHwC,EAAE,CAAEF,IAAI,CAACd,IAAK,CACdS,SAAS,CAAC,uHAAuH,CAAAJ,QAAA,eAEjIV,IAAA,CAACoB,IAAI,EAACN,SAAS,CAAC,sDAAsD,CAAE,CAAC,CACxEK,IAAI,CAACf,IAAI,GALLe,IAAI,CAACf,IAMN,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cAGNF,KAAA,QAAKY,SAAS,CAAC,sCAAsC,CAAAJ,QAAA,eAEnDV,IAAA,QAAKc,SAAS,CAAC,yBAAyB,CAAAJ,QAAA,cACtCR,KAAA,QAAKY,SAAS,CAAC,6CAA6C,CAAAJ,QAAA,eAC1DV,IAAA,WACEc,SAAS,CAAC,WAAW,CACrBC,OAAO,CAAEA,CAAA,GAAMH,cAAc,CAAC,IAAI,CAAE,CAAAF,QAAA,cAEpCV,IAAA,CAACjB,SAAS,EAAC+B,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACzC,CAAC,cACTd,IAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAJ,QAAA,cAEhCV,IAAA,QAAKc,SAAS,CAAC,eAAe,CAAAJ,QAAA,cAC5BR,KAAA,QAAKY,SAAS,CAAC,mBAAmB,CAAAJ,QAAA,eAChCV,IAAA,SAAMc,SAAS,CAAC,+DAA+D,CAAAJ,QAAA,cAC7EV,IAAA,QAAKc,SAAS,CAAC,6BAA6B,CAACQ,IAAI,CAAC,cAAc,CAACC,OAAO,CAAC,WAAW,CAAAb,QAAA,cAClFV,IAAA,SAAMwB,CAAC,CAAC,4HAA4H,CAAE,CAAC,CACpI,CAAC,CACF,CAAC,cACPxB,IAAA,SAAMc,SAAS,CAAC,wCAAwC,CAAAJ,QAAA,CAAC,MAAI,CAAM,CAAC,EACjE,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNV,IAAA,SAAMc,SAAS,CAAC,sCAAsC,CAAAJ,QAAA,CACnDA,QAAQ,CACL,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}