import {
  NextFunction,
  Request,
  Response,
  Router,
} from 'express';
import fs from 'fs';
import multer from 'multer';
import path from 'path';

import {
  getMosaicoConfig,
  handleMosaicoUpload,
  proxyMosaicoRequest,
  saveMosaicoTemplate,
} from '../../controllers/integrations/mosaico.controller';
import { authenticate } from '../../middleware/auth.middleware';

const router = Router();

// --- Define Private Upload Directory --- 
const privateUploadDir = path.join(__dirname, '../../../private_uploads');
console.log(`[Mosaico Routes] Calculated private upload directory: ${privateUploadDir}`);
if (!fs.existsSync(privateUploadDir)) {
  try {
    fs.mkdirSync(privateUploadDir, { recursive: true });
    console.log(`[Mosaico Routes] Created private upload directory: ${privateUploadDir}`);
  } catch (mkdirError) {
    console.error(`[Mosaico Routes] ERROR creating directory ${privateUploadDir}:`, mkdirError);
  }
}
// --- Remove old public upload dir creation (if desired, or keep if used elsewhere) ---
// const uploadDir = path.join(__dirname, '../../../uploads');
// if (!fs.existsSync(uploadDir)) {
//   fs.mkdirSync(uploadDir, { recursive: true });
// }

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // ---> Use the private directory <--- 
    cb(null, privateUploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({ storage });

// --- Add Multer Error Handling Middleware ---
const handleMulterError = (err: any, req: Request, res: Response, next: NextFunction) => {
  if (err instanceof multer.MulterError) {
    // A Multer error occurred (e.g., file too large)
    console.error('[Multer Error Middleware] Multer error:', err);
    return res.status(400).json({ success: false, message: `Multer error: ${err.message}` });
  } else if (err) {
    // An unexpected error occurred during upload
    console.error('[Multer Error Middleware] Unexpected upload error:', err);
    return res.status(500).json({ success: false, message: 'File upload failed.' });
  }
  // If no error, proceed
  next();
};
// --- End Multer Error Handling ---

// All routes require authentication
router.use(authenticate);

// Get Mosaico configuration
router.get('/config', getMosaicoConfig);

// Handle file uploads for Mosaico
router.post('/upload', upload.single('file'), handleMulterError, handleMosaicoUpload);

// --- Add Proxy Upload Route --- 
// This route is intended to be called directly by Mosaico if it fails 
// to use the dynamically configured uploadUrl. It uses the same handler.
router.post('/internal_upload_proxy', upload.single('file'), handleMulterError, handleMosaicoUpload);
// --- End Proxy Upload Route --- 

// Save Mosaico template to a campaign
router.post('/campaigns/:campaignId/template', saveMosaicoTemplate);

// Proxy all other requests to Mosaico server
router.all('/proxy/*', proxyMosaicoRequest);

export default router; 