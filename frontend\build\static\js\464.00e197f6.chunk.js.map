{"version": 3, "file": "static/js/464.00e197f6.chunk.js", "mappings": "0MAwDA,MAmbA,EAnb4BA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAChC,MAAOC,EAAOC,IAAYC,EAAAA,EAAAA,UAAmB,KACtCC,EAASC,IAAcF,EAAAA,EAAAA,WAAkB,IACzCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAC3CK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAwB,OACzDO,EAAaC,IAAkBR,EAAAA,EAAAA,UAA6B,OAC5DS,EAAgBC,IAAqBV,EAAAA,EAAAA,WAAkB,IAGvDW,EAASC,IAAcZ,EAAAA,EAAAA,UAAS,CACrCa,KAAM,GACNC,YAAa,GACbC,SAAU,UACVC,SAAU,GACVC,SAAU,GACVC,aAAc,GACdC,gBAAiB,YACjBC,UAAW,KAIPC,EAAsB,CAC1B,CAAEC,GAAI,WAAYT,KAAM,gBACxB,CAAES,GAAI,WAAYT,KAAM,mBACxB,CAAES,GAAI,WAAYT,KAAM,kBACxB,CAAES,GAAI,WAAYT,KAAM,qBAI1BU,EAAAA,EAAAA,YAAU,KACWC,WACjBtB,GAAW,GACXE,EAAS,MACT,IACE,MAAMqB,QAAiBC,EAAAA,GAAcC,eACjCF,EAASG,QACX7B,EAAS0B,EAASI,MAElBzB,EAASqB,EAASK,SAAW,4BAEjC,CAAE,MAAOC,GACP3B,EAAS2B,EAAID,SAAW,yCAC1B,CAAC,QACC5B,GAAW,EACb,GAGF8B,EAAY,GACX,KAGHT,EAAAA,EAAAA,YAAU,KACR,IAAKlB,GAAyC,YAAxBA,EAAa4B,QAAgD,cAAxB5B,EAAa4B,OAEpE,YADAzB,EAAe,MAIMgB,WACvBd,GAAkB,GAClB,IACE,MAAMe,QAAiBC,EAAAA,GAAcQ,eAAe7B,EAAaiB,IAC7DG,EAASG,QACXpB,EAAeiB,EAASI,OAEtBM,QAAQhC,MAAM,gCAAiCsB,EAASK,SACxDtB,EAAe,MAErB,CAAE,MAAOuB,GACPI,QAAQhC,MAAM,+BAAgC4B,GAC9CvB,EAAe,KACjB,CAAC,QACGE,GAAkB,EACtB,GAGF0B,EAAkB,GACjB,CAAC/B,IAGJ,MA8GMgC,EAAkBJ,IAErB,OADAA,EAASA,GAAU,SAElB,IAAK,QAAS,MAAO,CAAEK,MAAO,OAAQC,KAAM,SAC5C,IAAK,UAAW,MAAO,CAAED,MAAO,OAAQC,KAAM,WAC9C,IAAK,YAAa,MAAO,CAAED,MAAO,QAASC,KAAM,aACjD,IAAK,YAAa,MAAO,CAAED,MAAO,MAAOC,KAAM,aAC/C,QAAS,MAAO,CAAED,MAAO,OAAQC,KAAMN,GACzC,EAIIO,EAAmBF,IACrB,OAAOA,GACH,IAAK,OAIL,QAAS,MAAO,4BAHhB,IAAK,OAAQ,MAAO,4BACpB,IAAK,QAAS,MAAO,8BACrB,IAAK,MAAO,MAAO,0BAEvB,EAIJ,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACpDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SAAC,4BAE3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,kEAIxCxC,IACEyC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,KAAK,QAAQhB,QAAS3B,EAAO4C,QAASA,IAAM3C,EAAS,MAAOsC,UAAU,UAGhFD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,yBACtFF,EAAAA,EAAAA,MAAA,QAAMQ,SArJWzB,UAGvB,GAFA0B,EAAEC,iBAEGxC,EAAQE,KAAKuC,QAAWzC,EAAQK,SAASoC,QAAWzC,EAAQM,SAASmC,OAK1E,GAAIzC,EAAQO,aAAe,GAAKP,EAAQO,aAAe,IACnDd,EAAS,gDADb,CAKAF,GAAW,GACXE,EAAS,MAET,IACE,MAAMiD,EAAwB,CAC5BxC,KAAMF,EAAQE,KACdC,YAAaH,EAAQG,kBAAewC,EACpCvC,SAAUJ,EAAQI,SAClBwC,SAAU,CACRC,EAAG7C,EAAQK,SACXyC,EAAG9C,EAAQM,UAEbC,aAAcP,EAAQO,aACtBC,gBAAiBR,EAAQQ,gBACzBC,UAAWT,EAAQS,gBAAakC,GAE5B7B,QAAiBC,EAAAA,GAAcgC,WAAWL,GAE5C5B,EAASG,SACX7B,EAAS,IAAID,EAAO2B,EAASI,OAC7BjB,EAAW,CACTC,KAAM,GACNC,YAAa,GACbC,SAAU,UACVC,SAAU,GACVC,SAAU,GACVC,aAAc,GACdC,gBAAiB,YACjBC,UAAW,MAGbhB,EAASqB,EAASK,SAAW,4BAEjC,CAAE,MAAOC,GACP3B,EAAS2B,EAAID,SAAW,wCAC1B,CAAC,QACC5B,GAAW,EACb,CAvCA,MAPEE,EAAS,2CA8CX,EAmGwCsC,UAAU,gBAAeC,SAAA,EACzDC,EAAAA,EAAAA,KAACe,EAAAA,EAAK,CACHC,MAAM,YACNtC,GAAG,WACHT,KAAK,WACLgD,MAAOlD,EAAQE,KACfiD,SAAWZ,GAAqCtC,EAAW,IAAKD,EAASE,KAAMqC,EAAEa,OAAOF,QACxFG,YAAY,gCACZC,UAAQ,KAEXxB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,kBAAkBxB,UAAU,qDAAoDC,SAAC,4BAGhGC,EAAAA,EAAAA,KAAA,YACGtB,GAAG,kBACHT,KAAK,kBACLsD,KAAM,EACNN,MAAOlD,EAAQG,YACfgD,SAAWZ,GAAwCtC,EAAW,IAAKD,EAASG,YAAaoC,EAAEa,OAAOF,QAClGG,YAAY,gCACZtB,UAAU,wMAGjBD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,WAAWxB,UAAU,qDAAoDC,SAAC,eACzFF,EAAAA,EAAAA,MAAA,UACInB,GAAG,WACHT,KAAK,WACLgD,MAAOlD,EAAQI,SACf+C,SAAWZ,GAAsCtC,EAAW,IAAKD,EAASI,SAAUmC,EAAEa,OAAOF,QAC7FnB,UAAU,6KAA4KC,SAAA,EAEvLC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,UAASlB,SAAC,kBACxBC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,UAASlB,SAAC,mBACxBC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,SAAQlB,SAAC,iBACvBC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,MAAKlB,SAAC,oBACpBC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,OAAMlB,SAAC,qBAG3BC,EAAAA,EAAAA,KAACe,EAAAA,EAAK,CACHC,MAAM,YACNtC,GAAG,WACHT,KAAK,WACLgD,MAAOlD,EAAQK,SACf8C,SAAWZ,GAAqCtC,EAAW,IAAKD,EAASK,SAAUkC,EAAEa,OAAOF,QAC5FG,YAAkC,YAArBrD,EAAQI,SAAyB,oCAA2D,YAArBJ,EAAQI,SAAyB,gCAAkC,kBACvJkD,UAAQ,KAEXrB,EAAAA,EAAAA,KAACe,EAAAA,EAAK,CACHC,MAAM,YACNtC,GAAG,WACHT,KAAK,WACLgD,MAAOlD,EAAQM,SACf6C,SAAWZ,GAAqCtC,EAAW,IAAKD,EAASM,SAAUiC,EAAEa,OAAOF,QAC5FG,YAAkC,YAArBrD,EAAQI,SAAyB,0CAAuD,YAArBJ,EAAQI,SAAyB,gCAAkC,kBACnJkD,UAAQ,KAEVrB,EAAAA,EAAAA,KAACe,EAAAA,EAAK,CACJC,MAAM,oBACNtC,GAAG,eACHT,KAAK,eACLiC,KAAK,SACLe,MAAOlD,EAAQO,aAAakD,WAC5BN,SAAWZ,IACP,MAAMmB,EAAMC,SAASpB,EAAEa,OAAOF,QACzBU,MAAMF,IAAQA,GAAO,GAAKA,GAAO,IAClCzD,EAAW,IAAKD,EAASO,aAAcmD,IACb,KAAnBnB,EAAEa,OAAOF,OAChBjD,EAAW,IAAKD,EAASO,aAAc,GAC3C,KAGNuB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,kBAAkBxB,UAAU,qDAAoDC,SAAC,sBAChGF,EAAAA,EAAAA,MAAA,UACEnB,GAAG,kBACHT,KAAK,kBACLgD,MAAOlD,EAAQQ,gBACf2C,SAAWZ,GAAsCtC,EAAW,IAAKD,EAASQ,gBAAiB+B,EAAEa,OAAOF,QACpGnB,UAAU,6KAA4KC,SAAA,EAEpLC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,YAAWlB,SAAC,eAC1BC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,aAAYlB,SAAC,gBAC3BC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,kBAAiBlB,SAAC,2BAGxCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAOsB,QAAQ,YAAYxB,UAAU,qDAAoDC,SAAC,wBAC1FF,EAAAA,EAAAA,MAAA,UACEnB,GAAG,YACHT,KAAK,YACLgD,MAAOlD,EAAQS,UACf0C,SAAWZ,GAAsCtC,EAAW,IAAKD,EAASS,UAAW8B,EAAEa,OAAOF,QAC9FnB,UAAU,6KAA4KC,SAAA,EAEpLC,EAAAA,EAAAA,KAAA,UAAQiB,MAAM,GAAElB,SAAC,mBAChBtB,EAASmD,KAAIC,IACV7B,EAAAA,EAAAA,KAAA,UAAmBiB,MAAOY,EAAEnD,GAAGqB,SAAE8B,EAAE5D,MAAtB4D,EAAEnD,aAI5BsB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CAAC5B,KAAK,SAAS6B,SAAU1E,EAASyC,UAAU,UAASC,SACzD1C,EAAU,cAAgB,yBAOpCwC,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,mBACpF1C,GAA4B,IAAjBH,EAAM8E,QACdhC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,qBACpC,IAAjB7C,EAAM8E,QACNhC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,+BAEtDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,CAAC,IACpE7C,EAAM0E,KAAKK,IACVpC,EAAAA,EAAAA,MAAA,MAEEC,UAAW,0CAAoD,OAAZrC,QAAY,IAAZA,OAAY,EAAZA,EAAciB,MAAOuD,EAAKvD,GAAK,cAAgB,IAClGwD,QAASA,IA1NED,MACX,OAAZxE,QAAY,IAAZA,OAAY,EAAZA,EAAciB,MAAOuD,EAAKvD,IAC9BhB,EAAgBuE,EAAK,EAwNUE,CAAoBF,GAAMlC,SAAA,EAExCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DAA4DC,SAAEkC,EAAKhE,QAChF+B,EAAAA,EAAAA,KAAA,QAAMF,UAAW,2DAA2DF,EAAgBH,EAAewC,EAAK5C,QAAU,SAASK,SAASK,SACzIN,EAAewC,EAAK5C,QAAU,SAASM,WAG5CE,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,CAAC,SAAOkC,EAAK9D,cAVxD8D,EAAKvD,aAkBtBmB,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,4BACrFC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACjBtC,GACCoC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAEtC,EAAaQ,QACzE+B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEtC,EAAaS,eAC9D2B,EAAAA,EAAAA,MAAA,QAAMC,UAAW,wEAAwEF,EAAgBH,EAAehC,EAAa4B,QAAU,SAASK,cAAcK,SAAA,CAAC,WAC5JN,EAAehC,EAAa4B,QAAU,SAASM,SAE1DE,EAAAA,EAAAA,MAAA,KAAGC,UAAU,8BAA6BC,SAAA,CAAC,SAAOtC,EAAaU,aAC/D0B,EAAAA,EAAAA,MAAA,KAAGC,UAAU,8BAA6BC,SAAA,CAAC,aAAWtC,EAAaa,aAAa,QAChFuB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,8BAA6BC,SAAA,CAAC,aAAWtC,EAAac,mBAClEd,EAAae,YAAaqB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,8BAA6BC,SAAA,CAAC,aAA2D,QAAjDpD,EAAA8B,EAAS2D,MAAKP,GAAGA,EAAEnD,KAAOjB,EAAae,mBAAU,IAAA7B,OAAA,EAAjDA,EAAmDsB,OAAQ,iBAG7I4B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0CAAyCC,SAAC,gBACxDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEAAmEC,SAAuB,QAAvBnD,EAAEa,EAAakD,gBAAQ,IAAA/D,OAAA,EAArBA,EAAuBgE,QAE3Gf,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0CAAyCC,SAAC,gBACxDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEAAmEC,SAAuB,QAAvBlD,EAAEY,EAAakD,gBAAQ,IAAA9D,OAAA,EAArBA,EAAuBgE,QAIlF,UAAxBpD,EAAa4B,QAA8C,WAAxB5B,EAAa4B,UAC9CW,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CAACI,QAhQHtD,UACtB,GAAKnB,GAAwC,UAAxBA,EAAa4B,OAAlC,CAEA/B,GAAW,GACX,IACE,MAAMuB,QAAiBC,EAAAA,GAAcuD,UAAU5E,EAAaiB,IAC5D,GAAIG,EAASG,QAAS,CACpB,MAAMsD,EAAc,IAAK7E,EAAc4B,OAAQ,WAC/ClC,EAASD,EAAM0E,KAAIK,GACjBA,EAAKvD,KAAOjB,EAAaiB,GAAK4D,EAAcL,KAE9CvE,EAAgB4E,EAClB,MACI9E,EAASqB,EAASK,SAAW,uBAEnC,CAAE,MAAOC,GACP3B,EAAS2B,EAAID,SAAW,uBACxBK,QAAQhC,MAAM,uBAAwB4B,EACxC,CAAC,QACG7B,GAAW,EACf,CAnB4D,CAmB5D,EA4OmDiF,KAAK,KAAKzC,UAAU,UAASC,SAAC,eAE3C,YAAxBtC,EAAa4B,SACVW,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CAACI,QA3OFtD,UACvB,GAAKnB,GAAwC,YAAxBA,EAAa4B,QAE7BmD,OAAOC,QAAQ,sDAApB,CAEAnF,GAAW,GACX,IACE,MAAMuB,QAAiBC,EAAAA,GAAc4D,WAAWjF,EAAaiB,IAC7D,GAAIG,EAASG,QAAS,CACpB,MAAMsD,EAAc,IAAK7E,EAAc4B,OAAQ,aAC/ClC,EAASD,EAAM0E,KAAIK,GACjBA,EAAKvD,KAAOjB,EAAaiB,GAAK4D,EAAcL,KAE9CvE,EAAgB4E,EAClB,MACI9E,EAASqB,EAASK,SAAW,wBAEnC,CAAE,MAAOC,GACP3B,EAAS2B,EAAID,SAAW,yBACxBK,QAAQhC,MAAM,yBAA0B4B,EAC1C,CAAC,QACG7B,GAAW,EACf,CAnBiF,CAmBjF,EAqNoDqF,QAAQ,SAASJ,KAAK,KAAIxC,SAAC,gBAIjEpC,GACCkC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qCAAoCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,YAC1DpC,EAAYiF,QACT5C,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,KAAK,UAAUhB,QAAS,mBAAmBvB,EAAYiF,OAAOC,kBAAkBlF,EAAYmF,yBAA0BhD,UAAU,SAChH,cAAvBnC,EAAY0B,QACZW,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,KAAK,OAAOhB,QAAQ,uCAAuCY,UAAU,UAE5EE,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,KAAK,OAAOhB,QAAQ,2BAA2BY,UAAU,UAIpED,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACnDF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,cAAYpC,EAAYoF,SAAS,OAAuB,QAAnBjG,EAAAa,EAAYqF,eAAO,IAAAlG,GAAG,QAAHC,EAAnBD,EAAqB8D,SAAC,IAAA7D,OAAH,EAAnBA,EAAwBkG,OAAQ,MAAM,QAC9EpD,EAAAA,EAAAA,MAAA,KAAAE,SAAA,CAAG,cAAYpC,EAAYoF,SAAS,OAAuB,QAAnB/F,EAAAW,EAAYqF,eAAO,IAAAhG,GAAG,QAAHC,EAAnBD,EAAqB6D,SAAC,IAAA5D,OAAH,EAAnBA,EAAwBgG,OAAQ,MAAM,aAI1D,YAAxBxF,EAAa4B,QAAgD,cAAxB5B,EAAa4B,QACpDW,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0CAAyCC,SAAC,uBACrD,SAILC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCAAwCC,SAAC,sEAK5D,C", "sources": ["pages/ABTesting.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  FormEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport { abTestService } from '../services';\n\n// Define interfaces if needed\ninterface TestVariantData {\n    a: string;\n    b: string;\n}\n\ninterface NewTestData {\n    name: string;\n    description?: string;\n    testType: string;\n    variants: TestVariantData;\n    audienceSize: number;\n    winningCriteria: string;\n    segmentId?: string;\n}\n\ninterface ABTest extends NewTestData {\n    id: string;\n    status?: 'draft' | 'running' | 'completed' | 'cancelled' | string; // Allow string for other statuses\n    // Add other properties from API response\n}\n\ninterface TestResultData {\n    rate?: number;\n    // Add other result metrics\n}\n\ninterface TestResults {\n    status: string;\n    winner?: 'a' | 'b';\n    confidence?: string;\n    criteria?: string;\n    results?: {\n        a?: TestResultData;\n        b?: TestResultData;\n    };\n}\n\ninterface Segment {\n  id: string;\n  name: string;\n}\n\nconst ABTesting: React.FC = () => {\n  const [tests, setTests] = useState<ABTest[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedTest, setSelectedTest] = useState<ABTest | null>(null);\n  const [testResults, setTestResults] = useState<TestResults | null>(null);\n  const [resultsLoading, setResultsLoading] = useState<boolean>(false);\n\n  // New test form state\n  const [newTest, setNewTest] = useState({\n    name: '',\n    description: '',\n    testType: 'subject',\n    variantA: '',\n    variantB: '',\n    audienceSize: 30,\n    winningCriteria: 'open_rate',\n    segmentId: ''\n  });\n\n  // Mock segments data (in a real app, this would come from an API)\n  const segments: Segment[] = [\n    { id: 'segment1', name: 'Active Users' },\n    { id: 'segment2', name: 'New Subscribers' },\n    { id: 'segment3', name: 'Inactive Users' },\n    { id: 'segment4', name: 'High Engagement' }\n  ];\n\n  // Fetch tests on component mount\n  useEffect(() => {\n    const fetchTests = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const response = await abTestService.getUserTests();\n        if (response.success) {\n          setTests(response.data);\n        } else {\n          setError(response.message || 'Failed to fetch A/B tests');\n        }\n      } catch (err: any) {\n        setError(err.message || 'An error occurred while fetching tests');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTests();\n  }, []);\n\n  // Fetch test results when a test is selected\n  useEffect(() => {\n    if (!selectedTest || (selectedTest.status !== 'running' && selectedTest.status !== 'completed')) {\n        setTestResults(null);\n        return;\n    }\n\n    const fetchTestResults = async () => {\n      setResultsLoading(true);\n      try {\n        const response = await abTestService.getTestResults(selectedTest.id);\n        if (response.success) {\n          setTestResults(response.data);\n        } else {\n            console.error('Failed to fetch test results:', response.message);\n            setTestResults(null);\n        }\n      } catch (err) {\n        console.error('Error fetching test results:', err);\n        setTestResults(null);\n      } finally {\n          setResultsLoading(false);\n      }\n    };\n\n    fetchTestResults();\n  }, [selectedTest]);\n\n  // Handle creating a new test\n  const handleCreateTest = async (e: FormEvent) => {\n    e.preventDefault();\n\n    if (!newTest.name.trim() || !newTest.variantA.trim() || !newTest.variantB.trim()) {\n      setError('Test name and both variants are required');\n      return;\n    }\n\n    if (newTest.audienceSize < 1 || newTest.audienceSize > 100) {\n        setError('Audience size must be between 1 and 100%');\n        return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const testData: NewTestData = {\n        name: newTest.name,\n        description: newTest.description || undefined,\n        testType: newTest.testType,\n        variants: {\n          a: newTest.variantA,\n          b: newTest.variantB\n        },\n        audienceSize: newTest.audienceSize,\n        winningCriteria: newTest.winningCriteria,\n        segmentId: newTest.segmentId || undefined\n      };\n      const response = await abTestService.createTest(testData);\n\n      if (response.success) {\n        setTests([...tests, response.data]);\n        setNewTest({\n          name: '',\n          description: '',\n          testType: 'subject',\n          variantA: '',\n          variantB: '',\n          audienceSize: 30,\n          winningCriteria: 'open_rate',\n          segmentId: ''\n        });\n      } else {\n        setError(response.message || 'Failed to create A/B test');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating test');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle test selection\n  const handleTestSelection = (test: ABTest) => {\n    if (selectedTest?.id === test.id) return;\n    setSelectedTest(test);\n  };\n\n  // Handle starting a test\n  const handleStartTest = async () => {\n    if (!selectedTest || selectedTest.status !== 'draft') return;\n\n    setLoading(true);\n    try {\n      const response = await abTestService.startTest(selectedTest.id);\n      if (response.success) {\n        const updatedTest = { ...selectedTest, status: 'running' };\n        setTests(tests.map(test =>\n          test.id === selectedTest.id ? updatedTest : test\n        ));\n        setSelectedTest(updatedTest);\n      } else {\n          setError(response.message || 'Failed to start test');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Error starting test');\n      console.error('Error starting test:', err);\n    } finally {\n        setLoading(false);\n    }\n  };\n\n  // Handle cancelling a test\n  const handleCancelTest = async () => {\n    if (!selectedTest || selectedTest.status !== 'running') return;\n\n    if (!window.confirm('Are you sure you want to cancel this running test?')) return;\n\n    setLoading(true);\n    try {\n      const response = await abTestService.cancelTest(selectedTest.id);\n      if (response.success) {\n        const updatedTest = { ...selectedTest, status: 'cancelled' };\n        setTests(tests.map(test =>\n          test.id === selectedTest.id ? updatedTest : test\n        ));\n        setSelectedTest(updatedTest);\n      } else {\n          setError(response.message || 'Failed to cancel test');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Error cancelling test');\n      console.error('Error cancelling test:', err);\n    } finally {\n        setLoading(false);\n    }\n  };\n\n  // Get status badge color and text representation\n  const getStatusStyle = (status: ABTest['status']): { color: string, text: string } => {\n     status = status || 'draft';\n     switch (status) {\n      case 'draft': return { color: 'gray', text: 'Draft' };\n      case 'running': return { color: 'blue', text: 'Running' };\n      case 'completed': return { color: 'green', text: 'Completed' };\n      case 'cancelled': return { color: 'red', text: 'Cancelled' };\n      default: return { color: 'gray', text: status };\n    }\n  };\n\n  // Helper function to get background color for badge based on status\n  const getBadgeBgColor = (color: string): string => {\n      switch(color) {\n          case 'gray': return 'bg-gray-700 text-gray-200';\n          case 'blue': return 'bg-blue-800 text-blue-100';\n          case 'green': return 'bg-green-800 text-green-100';\n          case 'red': return 'bg-red-800 text-red-100';\n          default: return 'bg-gray-700 text-gray-200';\n      }\n  }\n\n  // Remove Sidebar wrapper\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n         <h1 className=\"text-2xl font-semibold text-text-primary\">Enhanced A/B Testing</h1>\n      </div>\n       <p className=\"text-text-secondary mb-6\">\n          Create and manage A/B tests to optimize your email campaigns.\n       </p>\n      \n      {error && (\n         <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n      )}\n      \n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n        <Card className=\"lg:col-span-1\">\n           <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Create New A/B Test</h2>\n          <form onSubmit={handleCreateTest} className=\"p-4 space-y-4\">\n            <Input\n               label=\"Test Name\"\n               id=\"testName\"\n               name=\"testName\"\n               value={newTest.name}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => setNewTest({ ...newTest, name: e.target.value })}\n               placeholder=\"e.g., Subject Line Test - May\"\n               required\n            />\n            <div>\n                <label htmlFor=\"testDescription\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                    Description (Optional)\n                </label>\n                <textarea\n                   id=\"testDescription\"\n                   name=\"testDescription\"\n                   rows={2}\n                   value={newTest.description}\n                   onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setNewTest({ ...newTest, description: e.target.value })}\n                   placeholder=\"Brief description of the test\"\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500\"\n                />\n            </div>\n            <div>\n               <label htmlFor=\"testType\" className=\"block text-sm font-medium text-text-secondary mb-1\">Test Type</label>\n               <select\n                   id=\"testType\"\n                   name=\"testType\"\n                   value={newTest.testType}\n                   onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewTest({ ...newTest, testType: e.target.value })}\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                >\n                  <option value=\"subject\">Subject Line</option>\n                  <option value=\"content\">Email Content</option>\n                  <option value=\"sender\">Sender Name</option>\n                  <option value=\"cta\">Call to Action</option>\n                  <option value=\"time\">Send Time</option>\n               </select>\n            </div>\n            <Input\n               label=\"Variant A\"\n               id=\"variantA\"\n               name=\"variantA\"\n               value={newTest.variantA}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => setNewTest({ ...newTest, variantA: e.target.value })}\n               placeholder={newTest.testType === 'subject' ? 'e.g., Check out our new features!' : newTest.testType === 'content' ? 'Enter HTML/Text for Variant A' : 'Variant A value'}\n               required\n             />\n            <Input\n               label=\"Variant B\"\n               id=\"variantB\"\n               name=\"variantB\"\n               value={newTest.variantB}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => setNewTest({ ...newTest, variantB: e.target.value })}\n               placeholder={newTest.testType === 'subject' ? 'e.g., New Features Inside! 🔥' : newTest.testType === 'content' ? 'Enter HTML/Text for Variant B' : 'Variant B value'}\n               required\n            />\n             <Input\n               label=\"Audience Size (%)\"\n               id=\"audienceSize\"\n               name=\"audienceSize\"\n               type=\"number\"\n               value={newTest.audienceSize.toString()}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => {\n                   const val = parseInt(e.target.value);\n                   if (!isNaN(val) && val >= 0 && val <= 100) { \n                       setNewTest({ ...newTest, audienceSize: val })\n                   } else if (e.target.value === '') {\n                       setNewTest({ ...newTest, audienceSize: 0 })\n                   }\n               }}\n             />\n             <div>\n                 <label htmlFor=\"winningCriteria\" className=\"block text-sm font-medium text-text-secondary mb-1\">Winning Criteria</label>\n                 <select\n                   id=\"winningCriteria\"\n                   name=\"winningCriteria\"\n                   value={newTest.winningCriteria}\n                   onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewTest({ ...newTest, winningCriteria: e.target.value })}\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                >\n                     <option value=\"open_rate\">Open Rate</option>\n                     <option value=\"click_rate\">Click Rate</option>\n                     <option value=\"conversion_rate\">Conversion Rate</option> \n                 </select>\n             </div>\n             <div>\n                 <label htmlFor=\"segmentId\" className=\"block text-sm font-medium text-text-secondary mb-1\">Segment (Optional)</label>\n                 <select\n                   id=\"segmentId\"\n                   name=\"segmentId\"\n                   value={newTest.segmentId}\n                   onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewTest({ ...newTest, segmentId: e.target.value })}\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                >\n                     <option value=\"\">All Recipients</option>\n                     {segments.map(s => (\n                         <option key={s.id} value={s.id}>{s.name}</option>\n                     ))}\n                 </select>\n             </div>\n            <div className=\"flex justify-end pt-2\">\n               <Button type=\"submit\" disabled={loading} className=\"btn-cta\">\n                 {loading ? 'Creating...' : 'Create Test'}\n               </Button>\n            </div>\n          </form>\n        </Card>\n        \n        {/* Existing Tests List - Column 2 */} \n        <Card className=\"lg:col-span-1\"> \n           <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Existing Tests</h2>\n           {loading && tests.length === 0 ? (\n               <div className=\"p-4 text-center text-text-secondary\">Loading tests...</div>\n           ) : tests.length === 0 ? (\n               <div className=\"p-4 text-center text-text-secondary\">No A/B tests created yet.</div>\n           ) : (\n              <ul className=\"divide-y divide-gray-700 max-h-[500px] overflow-y-auto\"> {/* Adjust max height */} \n                {tests.map((test) => (\n                  <li \n                    key={test.id} \n                    className={`p-4 hover:bg-gray-700 cursor-pointer ${selectedTest?.id === test.id ? 'bg-gray-700' : ''}`}\n                    onClick={() => handleTestSelection(test)}\n                  >\n                     <div className=\"flex justify-between items-center\">\n                       <p className=\"text-sm font-medium text-text-primary truncate flex-1 mr-2\">{test.name}</p>\n                       <span className={`px-2 py-0.5 rounded-full text-xs font-medium capitalize ${getBadgeBgColor(getStatusStyle(test.status || 'draft').color)}`}>\n                         {getStatusStyle(test.status || 'draft').text}\n                       </span>\n                     </div>\n                     <p className=\"text-xs text-text-secondary mt-1\">Type: {test.testType}</p>\n                   </li>\n                ))}\n              </ul>\n           )}\n        </Card>\n\n        {/* Test Details & Results - Column 3 */} \n        <Card className=\"lg:col-span-1\"> \n           <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Test Details & Results</h2>\n           <div className=\"p-4\">\n             {selectedTest ? (\n               <div className=\"space-y-4\">\n                 <div>\n                   <h3 className=\"text-md font-medium text-text-primary mb-1\">{selectedTest.name}</h3>\n                   <p className=\"text-sm text-text-secondary mb-2\">{selectedTest.description}</p>\n                   <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium capitalize ${getBadgeBgColor(getStatusStyle(selectedTest.status || 'draft').color)} mb-2`}>\n                     Status: {getStatusStyle(selectedTest.status || 'draft').text}\n                   </span>\n                   <p className=\"text-sm text-text-secondary\">Type: {selectedTest.testType}</p>\n                   <p className=\"text-sm text-text-secondary\">Audience: {selectedTest.audienceSize}%</p>\n                   <p className=\"text-sm text-text-secondary\">Criteria: {selectedTest.winningCriteria}</p>\n                   {selectedTest.segmentId && <p className=\"text-sm text-text-secondary\">Segment: {segments.find(s=>s.id === selectedTest.segmentId)?.name || 'Unknown'}</p>}\n                 </div>\n\n                 <div className=\"space-y-1\">\n                    <h4 className=\"text-sm font-medium text-text-secondary\">Variant A:</h4>\n                    <p className=\"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words\">{selectedTest.variants?.a}</p>\n                 </div>\n                  <div className=\"space-y-1\">\n                    <h4 className=\"text-sm font-medium text-text-secondary\">Variant B:</h4>\n                    <p className=\"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words\">{selectedTest.variants?.b}</p>\n                 </div>\n                 \n                 {/* Actions */} \n                 {(selectedTest.status === 'draft' || selectedTest.status === 'paused') && (\n                     <Button onClick={handleStartTest} size=\"sm\" className=\"btn-cta\">Start Test</Button>\n                 )}\n                 {selectedTest.status === 'running' && (\n                     <Button onClick={handleCancelTest} variant=\"danger\" size=\"sm\">Cancel Test</Button>\n                 )}\n                 \n                 {/* Results */} \n                 {testResults ? (\n                   <div className=\"mt-4 pt-4 border-t border-gray-700\">\n                      <h3 className=\"text-md font-medium text-text-primary mb-2\">Results</h3>\n                      {testResults.winner ? (\n                          <Alert type=\"success\" message={`Winner: Variant ${testResults.winner.toUpperCase()} (${testResults.confidence} confidence)`} className=\"mb-2\" />\n                      ) : testResults.status === 'completed' ? (\n                          <Alert type=\"info\" message=\"Test completed, inconclusive result.\" className=\"mb-2\" />\n                      ) : (\n                          <Alert type=\"info\" message=\"Test is still running...\" className=\"mb-2\" />\n                      )}\n                      \n                      {/* Simplified results display */} \n                      <div className=\"text-sm text-text-secondary space-y-1\">\n                         <p>Variant A ({testResults.criteria}): {testResults.results?.a?.rate || 'N/A'}%</p>\n                         <p>Variant B ({testResults.criteria}): {testResults.results?.b?.rate || 'N/A'}%</p>\n                         {/* Add more detailed stats if needed */}\n                      </div>\n                   </div>\n                 ) : selectedTest.status === 'running' || selectedTest.status === 'completed' ? (\n                   <p className=\"text-sm text-text-secondary italic mt-4\">Loading results...</p>\n                 ) : null}\n                 \n               </div>\n             ) : (\n                <p className=\"text-text-secondary italic text-center\">Select a test from the list to see details and results.</p>\n             )}\n           </div>\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default ABTesting;\n"], "names": ["ABTesting", "_segments$find", "_selectedTest$variant", "_selectedTest$variant2", "_testResults$results", "_testResults$results$", "_testResults$results2", "_testResults$results3", "tests", "setTests", "useState", "loading", "setLoading", "error", "setError", "selectedTest", "setSelectedTest", "testResults", "setTestResults", "resultsLoading", "setResultsLoading", "newTest", "setNewTest", "name", "description", "testType", "variantA", "variantB", "audienceSize", "winningCriteria", "segmentId", "segments", "id", "useEffect", "async", "response", "abTestService", "getUserTests", "success", "data", "message", "err", "fetchTests", "status", "getTestResults", "console", "fetchTestResults", "getStatusStyle", "color", "text", "getBadgeBgColor", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON>", "type", "onClose", "Card", "onSubmit", "e", "preventDefault", "trim", "testData", "undefined", "variants", "a", "b", "createTest", "Input", "label", "value", "onChange", "target", "placeholder", "required", "htmlFor", "rows", "toString", "val", "parseInt", "isNaN", "map", "s", "<PERSON><PERSON>", "disabled", "length", "test", "onClick", "handleTestSelection", "find", "startTest", "updatedTest", "size", "window", "confirm", "cancelTest", "variant", "winner", "toUpperCase", "confidence", "criteria", "results", "rate"], "sourceRoot": ""}