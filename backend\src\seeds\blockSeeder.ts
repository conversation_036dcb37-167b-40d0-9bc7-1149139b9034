import dotenv from 'dotenv';

import dbService
  from '../config/database'; // Import the DatabaseService instance
import Block from '../models/Block'; // Import the Block model

dotenv.config();

// --- Define Default Blocks ---
const defaultBlocks = [
  {
    blockId: 'content/headline',
    category: 'content',
    name: 'Headline',
    description: 'A large heading text block.',
    mjml: '<mj-text font-size="28px" font-weight="bold" padding="10px 25px">{{headline}}</mj-text>',
    tags: ['text', 'heading', 'title'],
    isCustom: false,
  },
  {
    blockId: 'content/text',
    category: 'content',
    name: 'Text',
    description: 'A standard paragraph text block.',
    mjml: '<mj-text padding="10px 25px">{{text}}</mj-text>',
    tags: ['text', 'paragraph', 'body'],
    isCustom: false,
  },
  {
    blockId: 'content/image',
    category: 'content',
    name: 'Image',
    description: 'An image block.',
    mjml: '<mj-image src="{{imageUrl}}" alt="{{imageAlt}}" padding="10px 25px" />',
    tags: ['image', 'picture', 'visual'],
    isCustom: false,
  },
  {
    blockId: 'cta/button',
    category: 'cta',
    name: 'Button',
    description: 'A call-to-action button.',
    mjml: '<mj-section padding="20px 0 40px 0" background-color="#ffffff">\n' +
          '  <mj-column width="100%">\n' +
          '    <mj-button href="{{buttonUrl}}" background-color="#4F46E5" color="#ffffff" border-radius="8px" font-weight="bold" font-size="18px" inner-padding="20px 40px">{{buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['button', 'cta', 'link', 'action'],
    isCustom: false,
  },
  {
    blockId: 'misc/spacer',
    category: 'misc',
    name: 'Spacer',
    description: 'Adds vertical space between blocks.',
    mjml: '<mj-spacer height="{{spacerHeight}}px" />',
    tags: ['spacer', 'divider', 'space', 'layout'],
    isCustom: false,
  },
  {
    blockId: 'misc/divider',
    category: 'misc',
    name: 'Divider',
    description: 'A horizontal line divider.',
    mjml: '<mj-divider border-color="#dddddd" border-width="1px" padding="10px 0" />',
    tags: ['divider', 'separator', 'line', 'layout'],
    isCustom: false,
  },
  {
    blockId: 'layout/two-column',
    category: 'content',
    name: 'Two Columns',
    description: 'A section with two equal-width columns.',
    mjml: '<mj-section>\n' +
          '  <mj-column>\n' +
          '    <mj-text padding="10px 10px">{{column1_text}}</mj-text>\n' +
          '    <mj-image padding="10px 10px" src="{{column1_imageUrl}}" alt="{{column1_imageAlt}}"></mj-image>\n' +
          '  </mj-column>\n' +
          '  <mj-column>\n' +
          '    <mj-text padding="10px 10px">{{column2_text}}</mj-text>\n' +
          '    <mj-image padding="10px 10px" src="{{column2_imageUrl}}" alt="{{column2_imageAlt}}"></mj-image>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['layout', 'columns', 'two', 'structure'],
    isCustom: false,
  },
   {
    blockId: 'layout/three-column',
    category: 'content',
    name: 'Three Columns',
    description: 'A section with three equal-width columns.',
    mjml: '<mj-section text-align="center">\n' +
          '  <mj-column>\n' +
          '    <mj-image width="100px" src="{{col1_imageUrl}}" alt="{{col1_imageAlt}}"></mj-image>\n' +
          '    <mj-text font-weight="bold">{{col1_headline}}</mj-text>\n' +
          '    <mj-text>{{col1_text}}</mj-text>\n' +
          '    <mj-button href="{{col1_buttonUrl}}">{{col1_buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '  <mj-column>\n' +
          '    <mj-image width="100px" src="{{col2_imageUrl}}" alt="{{col2_imageAlt}}"></mj-image>\n' +
          '    <mj-text font-weight="bold">{{col2_headline}}</mj-text>\n' +
          '    <mj-text>{{col2_text}}</mj-text>\n' +
          '    <mj-button href="{{col2_buttonUrl}}">{{col2_buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '  <mj-column>\n' +
          '    <mj-image width="100px" src="{{col3_imageUrl}}" alt="{{col3_imageAlt}}"></mj-image>\n' +
          '    <mj-text font-weight="bold">{{col3_headline}}</mj-text>\n' +
          '    <mj-text>{{col3_text}}</mj-text>\n' +
          '    <mj-button href="{{col3_buttonUrl}}">{{col3_buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['layout', 'columns', 'three', 'features', 'grid'],
    isCustom: false,
  },
  {
    blockId: 'layout/hero',
    category: 'header',
    name: 'Hero Image',
    description: 'A full-width image, often with text overlay (text added separately).',
    mjml: '<mj-section padding="0px" background-color="#4F46E5">\n' +
          '  <mj-column width="100%">\n' +
          '    <mj-text align="center" color="#ffffff" font-size="32px" font-weight="bold" padding="50px 25px 20px 25px">{{heroHeadline}}</mj-text>\n' +
          '    <mj-button href="{{heroButtonUrl}}" background-color="#ffffff" color="#4F46E5" font-weight="bold" border-radius="8px" padding="10px 25px 50px 25px" inner-padding="15px 30px">{{heroButtonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['layout', 'hero', 'banner', 'image', 'header'],
    isCustom: false,
  },
  {
    blockId: 'header/simple-nav',
    category: 'header',
    name: 'Simple Navigation',
    description: 'A header with logo and simple navigation links.',
    mjml: '<mj-section padding="10px 0" background-color="#f9fafb">\n' +
          '  <mj-column width="30%">\n' +
          '    <mj-image width="120px" src="{{logoUrl}}" alt="{{logoAlt}}" padding="10px 25px"></mj-image>\n' +
          '  </mj-column>\n' +
          '  <mj-column width="70%">\n' +
          '    <mj-navbar base-url="{{baseUrl}}" hamburger="hamburger" ico-color="#1E3A8A">\n' +
          '      <mj-navbar-link href="{{nav1_url}}" color="#1E3A8A" font-weight="600" padding="0 15px">{{nav1_text}}</mj-navbar-link>\n' +
          '      <mj-navbar-link href="{{nav2_url}}" color="#1E3A8A" font-weight="600" padding="0 15px">{{nav2_text}}</mj-navbar-link>\n' +
          '      <mj-navbar-link href="{{nav3_url}}" color="#1E3A8A" font-weight="600" padding="0 15px">{{nav3_text}}</mj-navbar-link>\n' +
          '    </mj-navbar>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['header', 'navigation', 'nav', 'menu', 'logo'],
    isCustom: false,
  },
  {
    blockId: 'footer/standard',
    category: 'footer',
    name: 'Standard Footer',
    description: 'A standard footer with address, links, and social icons.',
    mjml: '<mj-section border-top="1px solid #dddddd" padding-top="30px" background-color="#f9fafb">\n' +
          '  <mj-column width="100%">\n' +
          '    <mj-text font-size="18px" font-weight="bold" color="#333333" align="center" padding-bottom="10px">{{companyName}}</mj-text>\n' +
          '  </mj-column>\n' +
          '</mj-section>\n' +
          '<mj-section background-color="#f9fafb" padding-top="0px">\n' +
          '  <mj-column width="60%">\n' +
          '    <mj-text font-size="14px" color="#666666" align="center">\n' +
          `      © ${new Date().getFullYear()} {{companyName}}. All rights reserved.<br/>\n` +
          '      {{companyAddress}}<br/>\n' +
          '      <a href="{{unsubscribeUrl}}" style="color: #666666; text-decoration: underline;">Unsubscribe</a> | <a href="{{privacyUrl}}" style="color: #666666; text-decoration: underline;">Privacy Policy</a>\n' +
          '    </mj-text>\n' +
          '  </mj-column>\n' +
          '  <mj-column width="40%">\n' +
          '     <mj-social font-size="15px" icon-size="25px" mode="horizontal" align="center">\n' +
          '        <mj-social-element name="facebook" href="{{facebookUrl}}" background-color="#4F46E5"></mj-social-element>\n' +
          '        <mj-social-element name="twitter" href="{{twitterUrl}}" background-color="#4F46E5"></mj-social-element>\n' +
          '        <mj-social-element name="linkedin" href="{{linkedinUrl}}" background-color="#4F46E5"></mj-social-element>\n' +
          '        <mj-social-element name="instagram" href="{{instagramUrl}}" background-color="#4F46E5"></mj-social-element>\n' +
          '      </mj-social>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['footer', 'contact', 'address', 'social', 'links'],
    isCustom: false,
  },
  {
    blockId: 'content/feature',
    category: 'content',
    name: 'Feature Item',
    description: 'Icon/Image with Headline and Text, good for feature lists.',
    mjml: '<mj-section padding-bottom="0px">\n' +
          '  <mj-column width="15%">\n' +
          '    <mj-image width="48px" src="{{featureIconUrl}}" alt="{{featureIconAlt}}"></mj-image>\n' +
          '  </mj-column>\n' +
          '  <mj-column width="85%">\n' +
          '    <mj-text font-weight="bold" font-size="16px">{{featureHeadline}}</mj-text>\n' +
          '    <mj-text>{{featureText}}</mj-text>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['feature', 'icon', 'list', 'content'],
    isCustom: false,
  },
  {
    blockId: 'content/testimonial',
    category: 'testimonial',
    name: 'Testimonial',
    description: 'Block for displaying customer quotes or testimonials.',
    mjml: '<mj-section background-color="#f0f0f0" border-radius="4px" padding="15px">\n' +
          '  <mj-column width="20%">\n' +
          '     <mj-image width="60px" border-radius="50%" src="{{avatarUrl}}" alt="{{avatarAlt}}"></mj-image>\n' +
          '  </mj-column>\n' +
          '  <mj-column width="80%">\n' +
          '      <mj-text font-style="italic">"{{quote}}"</mj-text>\n' +
          '      <mj-text align="right" font-weight="bold">- {{authorName}}, {{authorTitle}}</mj-text>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['testimonial', 'quote', 'review', 'social proof'],
    isCustom: false,
  },

  // --- Product Blocks ---
  {
    blockId: 'product/card',
    category: 'product',
    name: 'Product Card',
    description: 'Display a single product with image, title, description, and price/button.',
    mjml: '<mj-section>' +
          '  <mj-column width="40%">\n' +
          '    <mj-image src="{{productImageUrl}}" alt="{{productImageAlt}}" padding="0"></mj-image>\n' +
          '  </mj-column>\n' +
          '  <mj-column width="60%">\n' +
          '    <mj-text font-size="18px" font-weight="bold">{{productName}}</mj-text>\n' +
          '    <mj-text font-size="14px">{{productDescription}}</mj-text>\n' +
          '    <mj-text font-size="16px" font-weight="bold" color="#1E3A8A">{{productPrice}}</mj-text>\n' +
          '    <mj-button href="{{productUrl}}" background-color="#4F46E5">{{productButtonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['product', 'ecommerce', 'card', 'item'],
    isCustom: false,
  },
  // Note: A true product grid might need dynamic looping. Using 3-column as a base.
  {
    blockId: 'product/grid',
    category: 'product',
    name: 'Product Grid (3 Items)',
    description: 'Display three products side-by-side.',
    mjml: '<mj-section padding="30px 0 10px 0" background-color="#ffffff" border-radius="8px">\n' +
          '  <mj-column>\n' +
          '    <mj-text font-size="28px" font-weight="bold" align="center" color="#333333" padding-bottom="20px">Featured Products</mj-text>\n' +
          '  </mj-column>\n' +
          '</mj-section>\n' +
          '<mj-section text-align="center" padding="10px 0 30px 0" background-color="#ffffff">\n' +
          '  <mj-column>\n' +
          '    <mj-image width="150px" src="{{prod1_imageUrl}}" alt="{{prod1_imageAlt}}" padding-bottom="10px"></mj-image>\n' +
          '    <mj-text font-weight="bold" font-size="18px" padding="5px 10px">{{prod1_name}}</mj-text>\n' +
          '    <mj-text font-size="16px" font-weight="bold" color="#4F46E5" padding="5px 10px">{{prod1_price}}</mj-text>\n' +
          '    <mj-button href="{{prod1_url}}" background-color="#4F46E5" color="#ffffff" border-radius="4px" font-weight="600" inner-padding="10px 20px">{{prod1_buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '  <mj-column>\n' +
          '    <mj-image width="150px" src="{{prod2_imageUrl}}" alt="{{prod2_imageAlt}}" padding-bottom="10px"></mj-image>\n' +
          '    <mj-text font-weight="bold" font-size="18px" padding="5px 10px">{{prod2_name}}</mj-text>\n' +
          '    <mj-text font-size="16px" font-weight="bold" color="#4F46E5" padding="5px 10px">{{prod2_price}}</mj-text>\n' +
          '    <mj-button href="{{prod2_url}}" background-color="#4F46E5" color="#ffffff" border-radius="4px" font-weight="600" inner-padding="10px 20px">{{prod2_buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '  <mj-column>\n' +
          '    <mj-image width="150px" src="{{prod3_imageUrl}}" alt="{{prod3_imageAlt}}" padding-bottom="10px"></mj-image>\n' +
          '    <mj-text font-weight="bold" font-size="18px" padding="5px 10px">{{prod3_name}}</mj-text>\n' +
          '    <mj-text font-size="16px" font-weight="bold" color="#4F46E5" padding="5px 10px">{{prod3_price}}</mj-text>\n' +
          '    <mj-button href="{{prod3_url}}" background-color="#4F46E5" color="#ffffff" border-radius="4px" font-weight="600" inner-padding="10px 20px">{{prod3_buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['product', 'ecommerce', 'grid', 'multiple', 'layout'],
    isCustom: false,
  },

  // --- Other Content Blocks ---
  {
    blockId: 'content/video',
    category: 'content',
    name: 'Video Thumbnail',
    description: 'Display a video thumbnail that links to the video.',
    mjml: '<mj-section padding="10px 25px">\n' +
          '  <mj-column>\n' +
          '    <mj-image src="{{videoThumbnailUrl}}" alt="{{videoAltText}}" href="{{videoUrl}}" width="550px" padding="0"></mj-image>\n' +
          // Optional: Add a play button overlay or text below
          '    <mj-text align="center" font-size="12px" padding-top="5px"><a href="{{videoUrl}}" style="color:#555;">Watch Video: {{videoTitle}}</a></mj-text>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['video', 'media', 'thumbnail', 'visual'],
    isCustom: false,
  },
  {
    blockId: 'header/centered-logo',
    category: 'header',
    name: 'Centered Logo Header',
    description: 'A simple header with a centered logo.',
    mjml: '<mj-section padding="10px 0">\n' +
          '  <mj-column>\n' +
          '    <mj-image width="150px" src="{{logoUrl}}" alt="{{logoAlt}}" align="center"></mj-image>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['header', 'logo', 'simple', 'branding'],
    isCustom: false,
  },
  {
    blockId: 'footer/detailed',
    category: 'footer',
    name: 'Detailed Footer',
    description: 'A more detailed footer with multiple links and sections.',
    mjml: '<mj-section border-top="1px solid #dddddd" padding-top="20px" padding-bottom="20px">\n' +
          '  <mj-column width="100%">\n' +
          '    <mj-text align="center" font-size="12px" color="#888888">{{footerTextLine1}}</mj-text>\n' +
          '    <mj-text align="center" font-size="12px" color="#888888">{{footerTextLine2}}</mj-text>\n' +
          '    <mj-text align="center" font-size="12px" color="#888888">\n' +
          '      <a href="{{link1Url}}" style="color:#888888; text-decoration:underline;">{{link1Text}}</a> | \n' +
          '      <a href="{{link2Url}}" style="color:#888888; text-decoration:underline;">{{link2Text}}</a> | \n' +
          '      <a href="{{link3Url}}" style="color:#888888; text-decoration:underline;">{{link3Text}}</a>\n' +
          '    </mj-text>\n' +
          '    <mj-social font-size="12px" icon-size="24px" mode="horizontal" align="center" padding-top="10px">\n' +
          '       {{socialIconsArea}}' +
          '       <mj-social-element name="facebook" href="{{facebookUrl}}"></mj-social-element>\n' +
          '       <mj-social-element name="twitter" href="{{twitterUrl}}"></mj-social-element>\n' +
          '       <mj-social-element name="instagram" href="{{instagramUrl}}"></mj-social-element>\n' +
          '       <mj-social-element name="linkedin" href="{{linkedinUrl}}"></mj-social-element>\n' +
          '       <mj-social-element name="website" href="{{websiteUrl}}" src="https://example.com/link-icon.png"></mj-social-element>\n' +
          '     </mj-social>\n' +
          '  </mj-column>\n' +
          '</mj-section>',
    tags: ['footer', 'detailed', 'links', 'social', 'contact'],
    isCustom: false,
  },
   {
    blockId: 'layout/image-text-swap',
    category: 'content',
    name: 'Image & Text (Swappable)',
    description: 'Image and text side-by-side, order can be swapped via content.',
    // Relies on AI providing image_position ('left' or 'right') in content
    mjml: '<mj-section>\n' +
          '  <mj-column width="{{imageWidthPercentage}}%">\n' +
          '    <mj-image src="{{imageUrl}}" alt="{{imageAlt}}" padding="0"></mj-image>\n' +
          '  </mj-column>\n' +
          '  <mj-column width="{{textWidthPercentage}}%">\n' +
          '    <mj-text font-weight="bold">{{headline}}</mj-text>\n' +
          '    <mj-text>{{text}}</mj-text>\n' +
          '    <mj-button href="{{buttonUrl}}">{{buttonText}}</mj-button>\n' +
          '  </mj-column>\n' +
          // Note: Logic to swap column order based on content.image_position 
          // would need to happen *before* this MJML is used, potentially in the frontend 
          // or by having two separate block definitions (text-left, text-right).
          // This seeded block assumes image is on the left.
          '</mj-section>',
    tags: ['layout', 'image', 'text', 'column', 'swap'],
    isCustom: false,
  },
];

// --- Seeder Function ---
export const seedBlocks = async () => {
  console.log('Connecting to database for block seeding...');
  try {
    await dbService.connectMongo(); // Use the connectMongo method from the service
    console.log('Database connected.');

    console.log('Clearing existing non-custom blocks...');
    // Delete only system blocks to avoid removing user-created ones
    await Block.deleteMany({ isCustom: false });
    console.log('Existing system blocks cleared.');

    console.log(`Inserting ${defaultBlocks.length} default blocks...`);
    await Block.insertMany(defaultBlocks);
    console.log('Default blocks inserted successfully.');

  } catch (error) {
    console.error('Error during block seeding:', error);
    process.exit(1); // Exit with error code
  } finally {
    try {
      await dbService.disconnect(); // Use the disconnect method from the service
      console.log('Database connection closed after seeding.');
    } catch (disconnectError) {
        console.error('Error disconnecting from database:', disconnectError);
    }
  }
};

// --- Run Seeder ---
seedBlocks(); 