{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\campaigns\\\\CampaignCreate.tsx\",\n  _s = $RefreshSig$();\n// frontend/src/pages/campaigns/CampaignCreate.tsx\n\nimport '../../styles/editor.css';\nimport React, { useEffect, useRef, useState } from 'react';\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport Input from 'components/Input';\nimport MjmlEditor from 'components/MjmlEditor';\nimport { Modal } from 'components/Modal';\nimport { useAuth } from 'contexts/AuthContext';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { templateRecommendationService } from 'services';\nimport api, { campaignAPI } from 'services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CampaignCreate = () => {\n  _s();\n  var _user$domain;\n  const {\n    user\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // Campaign metadata\n  const [campaignName, setCampaignName] = useState('');\n  const [subject, setSubject] = useState('');\n  const [fromName, setFromName] = useState((user === null || user === void 0 ? void 0 : user.name) || '');\n  const [fromEmail, setFromEmail] = useState((user === null || user === void 0 ? void 0 : (_user$domain = user.domain) === null || _user$domain === void 0 ? void 0 : _user$domain.status) === 'active' ? `noreply@${user.domain.name}` : '');\n  const [replyTo, setReplyTo] = useState('');\n\n  // UI state\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [templateLoading, setTemplateLoading] = useState(false);\n  const [step, setStep] = useState(1);\n\n  // Email sequence (up to 10)\n  const [currentEmail, setCurrentEmail] = useState(1);\n  const [emailContents, setEmailContents] = useState(() => {\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          return arr.map(item => ({\n            mjml: item.mjml || '',\n            html: item.html || ''\n          }));\n        }\n      } catch {\n        // ignore parse errors\n      }\n    }\n    return Array.from({\n      length: 10\n    }, () => ({\n      mjml: '',\n      html: ''\n    }));\n  });\n\n  // Scheduling\n  const [scheduleSettings, setScheduleSettings] = useState({\n    intervals: [24],\n    unit: 'hours'\n  });\n\n  // Preview and template picker\n  const [campaignPreview, setCampaignPreview] = useState(null);\n  const [showTemplatePicker, setShowTemplatePicker] = useState(false);\n  const [templatesList, setTemplatesList] = useState([]);\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n\n  // Recipients\n  const [sendScheduleOption, setSendScheduleOption] = useState('later');\n  const [scheduledDateTime, setScheduledDateTime] = useState(() => {\n    const date = new Date(Date.now() + 60 * 60 * 1000);\n    return date.toISOString().slice(0, 16);\n  });\n  const [recipients, setRecipients] = useState([]);\n  const [manualRecipient, setManualRecipient] = useState({\n    email: '',\n    name: ''\n  });\n  const [contacts, setContacts] = useState([]);\n  const [selectedContactIds, setSelectedContactIds] = useState([]);\n  const [loadingContacts, setLoadingContacts] = useState(false);\n\n  // Ref to MJML editor\n  const editorRef = useRef(null);\n\n  // State to track which emails are actively selected for sending\n  const [activeEmailIndices, setActiveEmailIndices] = useState(() => {\n    // Initialize based on emails that already have HTML content\n    const initialActive = [];\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    let initialContents = Array.from({\n      length: 10\n    }, () => ({\n      mjml: '',\n      html: ''\n    }));\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          initialContents = arr.map(item => ({\n            mjml: item.mjml || '',\n            html: item.html || ''\n          }));\n        }\n      } catch {}\n    }\n    initialContents.forEach((email, index) => {\n      if (email.html && email.html.trim()) {\n        initialActive.push(index);\n      }\n    });\n    return initialActive;\n  });\n\n  // Handle URL param for templateId\n  const queryParams = new URLSearchParams(location.search);\n  const templateId = queryParams.get('templateId');\n  useEffect(() => {\n    if (templateId) fetchTemplate(templateId);\n  }, [templateId]);\n\n  // Fetch AI or saved template by ID\n  const fetchTemplate = async id => {\n    setTemplateLoading(true);\n    try {\n      const res = await templateRecommendationService.getTemplateById(id);\n      if (res.success && res.template) {\n        const temp = res.template;\n        if (!campaignName) setCampaignName(`Campaign based on ${temp.name}`);\n        const updated = [...emailContents];\n        updated[0] = {\n          mjml: temp.mjmlContent || '',\n          html: temp.content || ''\n        };\n        setEmailContents(updated);\n      } else {\n        setError('Failed to load template.');\n      }\n    } catch {\n      setError('Failed to load template.');\n    } finally {\n      setTemplateLoading(false);\n    }\n  };\n\n  // Save handler from MJML editor\n  const handleMjmlSave = (mjml, html) => {\n    console.log(`--- handleMjmlSave called for email ${currentEmail} ---`);\n    console.log('Received MJML:', mjml ? mjml.substring(0, 100) + '...' : '(empty)');\n    console.log('Received HTML:', html ? html.substring(0, 100) + '...' : '(empty)');\n    const updated = [...emailContents];\n    const currentIndex = currentEmail - 1; // Get index before state update\n    updated[currentIndex] = {\n      mjml,\n      html\n    };\n    setEmailContents(updated);\n    console.log('State AFTER update attempt:', updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n\n    // If saving resulted in valid HTML, ensure this email is marked active\n    if (html && html.trim()) {\n      setActiveEmailIndices(prev => {\n        if (!prev.includes(currentIndex)) {\n          // Add if not already present, keep sorted for consistency\n          return [...prev, currentIndex].sort((a, b) => a - b);\n        }\n        return prev; // Already active, no change needed\n      });\n      console.log(`Ensured email index ${currentIndex} is active after save.`);\n    } else {\n      // If saving resulted in empty HTML, ensure it's deactivated\n      setActiveEmailIndices(prev => prev.filter(i => i !== currentIndex));\n      console.log(`Deactivated email index ${currentIndex} after save due to empty HTML.`);\n    }\n  };\n\n  // Template picker effects and handlers\n  useEffect(() => {\n    if (showTemplatePicker) {\n      setLoadingTemplates(true);\n      templateRecommendationService.getAllTemplates().then(r => setTemplatesList(r.data || [])).catch(console.error).finally(() => setLoadingTemplates(false));\n    }\n  }, [showTemplatePicker]);\n  const handleTemplateSelect = async id => {\n    setLoadingTemplates(true);\n    try {\n      console.log(`[Debug] Selecting template with ID: ${id}`);\n      const r = await templateRecommendationService.getTemplateById(id);\n      console.log('[Debug] Template API response:', r);\n      if (r.success && r.template) {\n        var _r$template$content;\n        console.log('[Debug] Setting selected template:', {\n          id: r.template.id || r.template._id,\n          name: r.template.name,\n          hasMjml: !!r.template.mjmlContent,\n          hasContent: !!r.template.content,\n          contentLength: ((_r$template$content = r.template.content) === null || _r$template$content === void 0 ? void 0 : _r$template$content.length) || 0\n        });\n      }\n      setSelectedTemplate(r.template);\n    } catch (err) {\n      console.error('[Debug] Error selecting template:', err);\n      setError('Failed to load selected template.');\n    } finally {\n      setLoadingTemplates(false);\n    }\n  };\n  const handleUseTemplate = () => {\n    console.log('[Debug] Using selected template:', selectedTemplate ? {\n      id: selectedTemplate.id || selectedTemplate._id,\n      name: selectedTemplate.name,\n      hasMjml: !!selectedTemplate.mjmlContent,\n      hasContent: !!selectedTemplate.content\n    } : 'No template selected');\n    if (selectedTemplate) {\n      const updated = [...emailContents];\n      updated[currentEmail - 1] = {\n        mjml: selectedTemplate.mjmlContent || '',\n        html: selectedTemplate.content || ''\n      };\n      console.log('[Debug] Updated email contents:', {\n        emailIndex: currentEmail - 1,\n        hasMjml: !!updated[currentEmail - 1].mjml,\n        hasHtml: !!updated[currentEmail - 1].html,\n        mjmlLength: updated[currentEmail - 1].mjml.length,\n        htmlLength: updated[currentEmail - 1].html.length\n      });\n\n      // Save the updated contents to state and local storage\n      setEmailContents(updated);\n      localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n\n      // Close the modal first to reduce complexity\n      setSelectedTemplate(null);\n      setShowTemplatePicker(false);\n\n      // Force a complete re-render of the editor component\n      // Wait a moment to ensure state updates have completed\n      setTimeout(() => {\n        // This will force the MjmlEditor to completely remount with the new content\n        // Temporarily set current email to a value that won't match any existing email\n        setCurrentEmail(-1);\n\n        // After a brief delay, restore the current email index\n        setTimeout(() => {\n          setCurrentEmail(currentEmail);\n        }, 50);\n      }, 100);\n    }\n  };\n\n  // Campaign creation\n  const handleCreateCampaign = async () => {\n    if (!campaignName || !subject || !fromName || !fromEmail) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    const validEmails = emailContents.filter(e => e.html.trim());\n    if (validEmails.length === 0) {\n      setError('Please add at least one email with valid HTML content');\n      return;\n    }\n    if (recipients.length === 0) {\n      setError('Please add at least one recipient');\n      return;\n    }\n    setLoading(true);\n    try {\n      var _activeEmailsToSend$;\n      // Filter emails with actual HTML content AND that are marked active\n      const activeEmailsToSend = emailContents.filter((email, index) => activeEmailIndices.includes(index) && email.html && email.html.trim());\n\n      // Validation based on filtered list\n      if (activeEmailsToSend.length === 0) {\n        setError('Please select at least one email with valid HTML content to send.');\n        setLoading(false);\n        return;\n      }\n      if (recipients.length === 0) {\n        setError('Please add at least one recipient');\n        setLoading(false);\n        return;\n      }\n      // --- End new validation ---\n\n      // Construct the base campaign data\n      const campaignData = {\n        name: campaignName,\n        subject,\n        fromName,\n        fromEmail,\n        replyTo: replyTo || fromEmail,\n        scheduled: sendScheduleOption === 'later',\n        ...(sendScheduleOption === 'later' && {\n          scheduledFor: new Date(scheduledDateTime).toISOString()\n        }),\n        recipientList: recipients,\n        userId: user === null || user === void 0 ? void 0 : user.id,\n        status: 'draft',\n        // Use the first ACTIVE email for top-level content\n        htmlContent: ((_activeEmailsToSend$ = activeEmailsToSend[0]) === null || _activeEmailsToSend$ === void 0 ? void 0 : _activeEmailsToSend$.html) || '',\n        // Send ONLY the emails that are ACTIVE and have content\n        emailContents: activeEmailsToSend.map(e => ({\n          mjml: e.mjml,\n          html: e.html\n        }))\n      };\n\n      // Add schedule only if there's more than one ACTIVE email with content\n      if (activeEmailsToSend.length > 1) {\n        campaignData.schedule = {\n          unit: scheduleSettings.unit,\n          // Slice intervals to match the number of gaps between ACTIVE emails\n          emailIntervals: scheduleSettings.intervals.slice(0, activeEmailsToSend.length - 1).map(d => ({\n            delay: d,\n            unit: scheduleSettings.unit\n          }))\n        };\n      }\n      console.log(\"Sending filtered & active campaign data:\", campaignData);\n\n      // Send the filtered data to the API\n      const res = await campaignAPI.createCampaign(campaignData);\n      // Check for _id primarily, then id\n      const campaignId = (res === null || res === void 0 ? void 0 : res._id) || (res === null || res === void 0 ? void 0 : res.id);\n      console.log('Create campaign response:', res, 'Extracted ID:', campaignId);\n      setSuccess(sendScheduleOption === 'later' ? `Campaign scheduled for ${new Date(scheduledDateTime).toLocaleString()}` : 'Campaign created and will start sending shortly');\n      localStorage.removeItem('driftly_campaign_create_email_contents');\n      // Clear draft info if it exists\n      localStorage.removeItem('driftly_campaign_draft');\n      sessionStorage.setItem('reloadCampaigns', 'true');\n      setTimeout(() => {\n        // Navigate using the extracted ID\n        if (campaignId) navigate(`/campaigns/${campaignId}/summary`);else {\n          console.warn('No ID found in createCampaign response, navigating to list.');\n          navigate('/campaigns');\n        }\n      }, 1500);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBrowseTemplates = () => {\n    if (campaignName || subject || fromName || fromEmail || replyTo) {\n      localStorage.setItem('driftly_campaign_draft', JSON.stringify({\n        campaignName,\n        subject,\n        fromName,\n        fromEmail,\n        replyTo\n      }));\n    }\n    navigate('/email-templates');\n  };\n  useEffect(() => {\n    const draft = localStorage.getItem('driftly_campaign_draft');\n    if (draft) {\n      try {\n        const d = JSON.parse(draft);\n        setCampaignName(d.campaignName || '');\n        setSubject(d.subject || '');\n        setFromName(d.fromName || (user === null || user === void 0 ? void 0 : user.name) || '');\n        setFromEmail(d.fromEmail || '');\n        setReplyTo(d.replyTo || '');\n      } catch {}\n      localStorage.removeItem('driftly_campaign_draft');\n    }\n  }, [user]);\n  const handleEmailSelect = i => setCurrentEmail(i);\n  useEffect(() => {\n    if (step === 3) {\n      // Count emails that are BOTH active AND have HTML\n      const activeEmailCount = emailContents.reduce((count, email, index) => {\n        if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n          return count + 1;\n        }\n        return count;\n      }, 0);\n      console.log('Active emails with HTML for scheduling:', activeEmailCount);\n      if (activeEmailCount > 1) {\n        setScheduleSettings(prev => ({\n          ...prev,\n          // Ensure intervals array matches the number of gaps between ACTIVE emails\n          intervals: Array(activeEmailCount - 1).fill(prev.intervals[0] || 24)\n        }));\n      } else {\n        // If 0 or 1 active emails, no intervals needed\n        setScheduleSettings(prev => ({\n          ...prev,\n          intervals: []\n        }));\n      }\n    }\n  }, [step, emailContents, activeEmailIndices]); // Add activeEmailIndices dependency\n\n  const fetchContacts = async () => {\n    if (step === 4) {\n      setLoadingContacts(true);\n      try {\n        var _r$data;\n        const r = await api.get('/contacts');\n        let list = Array.isArray(r.data) ? r.data : ((_r$data = r.data) === null || _r$data === void 0 ? void 0 : _r$data.data) || [];\n        setContacts(list.map(c => ({\n          id: c.id || c._id || String(Math.random()),\n          email: c.email,\n          name: c.name || c.fullName || ''\n        })));\n      } catch {\n        setError('Failed to load contacts');\n      } finally {\n        setLoadingContacts(false);\n      }\n    }\n  };\n  useEffect(() => {\n    fetchContacts();\n  }, [step]);\n  const addManualRecipient = () => {\n    if (!manualRecipient.email) {\n      setError('Please enter an email address.');\n    } else if (!recipients.some(r => r.email === manualRecipient.email)) {\n      setRecipients([...recipients, {\n        id: null,\n        ...manualRecipient\n      }]);\n      setManualRecipient({\n        email: '',\n        name: ''\n      });\n    } else {\n      setError('Recipient already added.');\n    }\n    setTimeout(() => setError(''), 3000);\n  };\n  const addSelectedContacts = () => {\n    const toAdd = contacts.filter(c => selectedContactIds.includes(c.id) && !recipients.some(r => r.email === c.email));\n    setRecipients([...recipients, ...toAdd]);\n    setSelectedContactIds([]);\n  };\n  const handleContactSelection = e => setSelectedContactIds(Array.from(e.target.selectedOptions, o => o.value));\n  const removeRecipient = email => setRecipients(recipients.filter(r => r.email !== email));\n\n  // Step navigation\n  const handleNext = async () => {\n    setError('');\n\n    // Validation for Step 1\n    if (step === 1 && (!campaignName || !subject || !fromName || !fromEmail)) {\n      setError('Please fill in all required fields.');\n      return;\n    }\n\n    // Validation for Step 2\n    if (step === 2) {\n      console.log('--- handleNext: Step 2 Validation ---');\n      let latestContent = null;\n      let savedContentHasHtml = false;\n      let stateHasHtml = false;\n\n      // --- Force save current editor's content FIRST ---\n      if (editorRef.current) {\n        console.log('Forcing editor save for current email:', currentEmail);\n        // Await the result of the now async save method\n        latestContent = await editorRef.current.save();\n        if (latestContent) {\n          console.log('Async save function returned content.');\n          // Log the returned HTML content (or lack thereof)\n          console.log('Returned MJML:', latestContent.mjml ? latestContent.mjml.substring(0, 50) + '...' : '(empty)');\n          console.log('Returned HTML:', latestContent.html ? latestContent.html.substring(0, 50) + '...' : '(empty)');\n\n          // Check if the *returned* content has HTML\n          if (latestContent.html && latestContent.html.trim()) {\n            console.log('RETURNED content has non-empty HTML.');\n            savedContentHasHtml = true;\n          } else {\n            console.log('RETURNED content has empty or no HTML.');\n          }\n\n          // Trigger state update (async) - Allow this to proceed even if HTML is missing\n          console.log('Triggering handleMjmlSave state update...');\n          handleMjmlSave(latestContent.mjml, latestContent.html);\n        } else {\n          // This case might be less likely now if save always returns an object\n          console.warn('editorRef.current.save() did not return expected content object.');\n        }\n      } else {\n        console.error('MjmlEditor ref is not available for forced save.');\n      }\n      // --- End Force save ---\n\n      // --- Validation ---\n      // Since save is now awaited and includes a delay, checking the returned content\n      // (savedContentHasHtml) should be more reliable. We also check state.\n\n      // Check if the SAVED content belongs to an index currently marked ACTIVE\n      const savedContentIsActive = savedContentHasHtml && activeEmailIndices.includes(currentEmail - 1);\n      if (savedContentIsActive) {\n        console.log('Saved content is for an active email.');\n      }\n      console.log('Checking emailContents state AFTER awaiting save/update attempt...');\n      // Check if ANY email in state is BOTH active AND has HTML\n      stateHasHtml = emailContents.some((email, index) => {\n        const hasHtml = email.html && email.html.trim();\n        const isActive = activeEmailIndices.includes(index);\n        if (hasHtml && isActive) {\n          console.log(`Email index ${index} in STATE is ACTIVE and has HTML.`);\n          return true;\n        }\n        return false;\n      });\n      if (!stateHasHtml) {\n        console.log('No email in STATE is both active and has HTML.');\n      }\n\n      // Final decision: Did *either* the direct save (if active) *or* the state check find an ACTIVE email with HTML?\n      const finalHasHtmlCheck = savedContentIsActive || stateHasHtml;\n      console.log('Final check result (Active & HTML):', finalHasHtmlCheck, `(Saved Active: ${savedContentIsActive}, State Active: ${stateHasHtml})`);\n      if (!finalHasHtmlCheck) {\n        setError('Please ensure at least one selected email (marked as \\'Sending\\') has valid HTML content.');\n        return; // Stop execution if validation fails specifically for step 2\n      }\n      console.log('Step 2 validation passed (Active HTML check).');\n    }\n\n    // Validation for Step 3\n    if (step === 3) {\n      const cnt = emailContents.filter(e => e.html.trim()).length;\n      if (cnt > 1) {\n        if (scheduleSettings.intervals.length !== cnt - 1) {\n          setError(`Define ${cnt - 1} intervals.`);\n          return;\n        }\n        if (scheduleSettings.intervals.some(v => v <= 0)) {\n          setError('Intervals must be positive.');\n          return;\n        }\n      }\n    }\n\n    // Validation for Step 4\n    if (step === 4 && recipients.length === 0) {\n      setError('Please add at least one recipient.');\n      return;\n    }\n\n    // Proceed to next step if all relevant validations passed and not on the last step\n    if (step < 5) {\n      console.log(`Proceeding from step ${step} to step ${step + 1}`);\n      setStep(step + 1);\n    }\n  };\n  const handlePrevious = () => {\n    setError('');\n    if (step > 1) setStep(step - 1);\n  };\n\n  // --- Add this handler ---\n  const handleClearEmailContent = index => {\n    const updated = [...emailContents];\n    updated[index] = {\n      mjml: '',\n      html: ''\n    };\n    setEmailContents(updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n    // Also remove from active indices when cleared\n    setActiveEmailIndices(prev => prev.filter(i => i !== index));\n    console.log(`Cleared content and deactivated email index ${index}`);\n  };\n  // --- End Add Handler ---\n\n  const renderTemplatePicker = () => /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: showTemplatePicker,\n    onClose: () => setShowTemplatePicker(false),\n    title: \"Choose Template\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\",\n      children: loadingTemplates ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 585,\n        columnNumber: 13\n      }, this) : templatesList.map(t => /*#__PURE__*/_jsxDEV(Card, {\n        className: `cursor-pointer ${(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.id) === t.id ? 'ring-2 ring-primary' : ''}`,\n        onClick: () => handleTemplateSelect(t.id),\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold mb-2 truncate\",\n          children: t.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\",\n          children: t.thumbnailUrl ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: t.thumbnailUrl,\n            alt: t.name,\n            className: \"object-contain h-full w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 35\n          }, this) : 'No Preview'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 15\n        }, this)]\n      }, t.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 flex justify-end gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: () => setShowTemplatePicker(false),\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 601,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleUseTemplate,\n        disabled: !selectedTemplate || loadingTemplates,\n        children: \"Use Selected\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 582,\n    columnNumber: 5\n  }, this);\n  const renderStepContent = () => {\n    var _user$domain2, _user$domain3;\n    switch (step) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"campaignName\",\n            name: \"campaignName\",\n            label: \"Campaign Name\",\n            value: campaignName,\n            onChange: e => setCampaignName(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"subject\",\n            name: \"subject\",\n            label: \"Email Subject\",\n            value: subject,\n            onChange: e => setSubject(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 614,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              id: \"fromName\",\n              name: \"fromName\",\n              label: \"From Name\",\n              value: fromName,\n              onChange: e => setFromName(e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"fromEmail\",\n              name: \"fromEmail\",\n              label: \"From Email\",\n              type: \"email\",\n              value: fromEmail,\n              onChange: e => setFromEmail(e.target.value),\n              disabled: (user === null || user === void 0 ? void 0 : (_user$domain2 = user.domain) === null || _user$domain2 === void 0 ? void 0 : _user$domain2.status) === 'active',\n              required: true,\n              helpText: (user === null || user === void 0 ? void 0 : (_user$domain3 = user.domain) === null || _user$domain3 === void 0 ? void 0 : _user$domain3.status) === 'active' ? `Using verified domain: ${user.domain.name}` : 'Verify domain for deliverability.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"replyTo\",\n            name: \"replyTo\",\n            label: \"Reply-To Email (optional)\",\n            type: \"email\",\n            value: replyTo,\n            onChange: e => setReplyTo(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 9\n        }, this);\n      case 2:\n        const curr = emailContents[currentEmail - 1];\n        // Use stricter validation: check for non-empty HTML\n        const emailsWithContent = emailContents.filter(e => e.html.trim()).length;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-4 h-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full lg:w-1/5 flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold mb-2\",\n              children: \"Email Sequence (Click to include/exclude):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this), Array.from({\n              length: 10\n            }).map((_, idx) => {\n              const hasHtmlContent = !!(emailContents[idx].html && emailContents[idx].html.trim());\n              const isSelected = currentEmail === idx + 1;\n              const isActive = activeEmailIndices.includes(idx);\n\n              // Determine button appearance based on content and active state\n              let buttonVariant = \"secondary\";\n              let customClasses = \"\";\n              let buttonText = `Email ${idx + 1}`;\n              let icon = null;\n              if (hasHtmlContent) {\n                if (isActive) {\n                  buttonVariant = \"primary\"; // Has content, is active\n                  icon = /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 text-green-500 dark:text-green-400 font-bold\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 28\n                  }, this);\n                  buttonText += \" (Sending)\";\n                } else {\n                  buttonVariant = \"secondary\"; // Has content, but inactive - use secondary variant\n                  customClasses = \"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\"; // Custom styling for inactive\n                  icon = /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 text-gray-500 dark:text-gray-400 font-bold\",\n                    children: \"\\u23F8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 661,\n                    columnNumber: 28\n                  }, this); // Pause icon maybe?\n                  buttonText += \" (Excluded)\";\n                }\n              } else {\n                // No content, always secondary, cannot be active\n                // Make it look distinct but clickable\n                customClasses = \"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\";\n                buttonText += \" (No Content - Click to Edit)\";\n              }\n              const selectionRingClass = isSelected ? \"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\" : \"\";\n              return /*#__PURE__*/_jsxDEV(Button, {\n                variant: buttonVariant // Will be 'secondary' for no content\n                ,\n                onClick: () => {\n                  // Set as current email for editing - ALWAYS DO THIS\n                  setCurrentEmail(idx + 1);\n\n                  // Toggle active state ONLY if it has content\n                  if (hasHtmlContent) {\n                    setActiveEmailIndices(prev => prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx]);\n                  }\n                },\n                size: \"sm\",\n                className: `w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses}`,\n                title: hasHtmlContent ? isActive ? \"Click to exclude from sending\" : \"Click to include in sending\" : \"Click to edit this email\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-grow truncate\",\n                  children: buttonText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 692,\n                  columnNumber: 21\n                }, this), icon]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 19\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium mb-1\",\n                children: \"Email Status Legend:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 mr-2 text-green-500 dark:text-green-400 font-bold\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-700 dark:text-green-400\",\n                  children: \"Has Content, Will Send\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-block w-3 h-3 mr-2 bg-gray-400 dark:bg-gray-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-700 dark:text-gray-300\",\n                  children: [\"Has Content, \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold\",\n                    children: \"Excluded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 83\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-block w-3 h-3 mr-2 border border-dashed border-gray-400 dark:border-gray-600 rounded-full opacity-75\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 710,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-700 dark:text-gray-300\",\n                  children: \"No Content (Click to Edit)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs mt-2 text-gray-600 dark:text-gray-400\",\n                children: \"Click emails with content to toggle inclusion. Click any email to edit.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 18\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: () => setShowTemplatePicker(true),\n                children: \"Choose Template\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: handleBrowseTemplates,\n                className: \"mt-2\",\n                children: \"Browse All Templates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full lg:w-4/5 flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(MjmlEditor, {\n              ref: editorRef,\n              initialMjml: curr.mjml,\n              onSave: handleMjmlSave,\n              height: \"70vh\"\n            }, currentEmail, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 dark:text-gray-400 mt-2\",\n              children: \"Changes are saved automatically. Emails with a green check mark (\\u2713) will be included in your campaign.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this);\n      case 3:\n        {\n          // Calculate count based on emails that are BOTH active AND have HTML\n          const activeEmailCount = emailContents.reduce((count, email, index) => {\n            if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n              return count + 1;\n            }\n            return count;\n          }, 0);\n          console.log('Active emails with HTML for rendering Step 3:', activeEmailCount);\n\n          // Handlers for main scheduling options\n          const handleScheduleOptionChange = e => {\n            setSendScheduleOption(e.target.value);\n          };\n          const handleScheduleTimeChange = e => {\n            setScheduledDateTime(e.target.value);\n          };\n\n          // Handlers for interval settings (only relevant if emailCount > 1)\n          const handleIntervalChange = (index, value) => {\n            const numValue = parseInt(value, 10);\n            if (!isNaN(numValue) && numValue > 0) {\n              const newIntervals = [...scheduleSettings.intervals];\n              newIntervals[index] = numValue;\n              setScheduleSettings({\n                ...scheduleSettings,\n                intervals: newIntervals\n              });\n            }\n          };\n          const handleUnitChange = e => {\n            setScheduleSettings({\n              ...scheduleSettings,\n              unit: e.target.value\n            });\n          };\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: \"Campaign Start Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"scheduleNow\",\n                    name: \"scheduleOption\",\n                    type: \"radio\",\n                    value: \"now\",\n                    checked: sendScheduleOption === 'now',\n                    onChange: handleScheduleOptionChange,\n                    className: \"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 774,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"scheduleNow\",\n                    className: \"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                    children: \"Send Immediately\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 783,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 773,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"scheduleLater\",\n                    name: \"scheduleOption\",\n                    type: \"radio\",\n                    value: \"later\",\n                    checked: sendScheduleOption === 'later',\n                    onChange: handleScheduleOptionChange,\n                    className: \"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 788,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"scheduleLater\",\n                    className: \"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                    children: \"Schedule for Later\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 797,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 17\n                }, this), sendScheduleOption === 'later' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pl-7 mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Input, {\n                    id: \"scheduledDateTime\",\n                    name: \"scheduledDateTime\",\n                    type: \"datetime-local\",\n                    value: scheduledDateTime,\n                    onChange: handleScheduleTimeChange,\n                    label: \"Scheduled Date & Time\",\n                    required: true,\n                    className: \"max-w-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 803,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                    children: \"Your local timezone.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 22\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 13\n            }, this), activeEmailCount > 1 && /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: \"Email Sequence Timing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 18\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                children: \"Set the time interval between each subsequent email send. The first email is sent according to the 'Campaign Start Time' setting above.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 18\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"scheduleUnit\",\n                  className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                  children: \"Interval Time Unit:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 20\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"scheduleUnit\",\n                  name: \"scheduleUnit\",\n                  value: scheduleSettings.unit,\n                  onChange: handleUnitChange,\n                  className: \"mt-1 block w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"minutes\",\n                    children: \"Minutes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"hours\",\n                    children: \"Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"days\",\n                    children: \"Days\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 839,\n                    columnNumber: 22\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 830,\n                  columnNumber: 20\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 18\n              }, this), Array.from({\n                length: activeEmailCount - 1\n              }).map((_, index) => {\n                var _scheduleSettings$int;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4 mt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: `interval-${index}`,\n                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 w-48\",\n                    children: [\"Wait before sending Email #\", index + 2, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(Input, {\n                    id: `interval-${index}`,\n                    name: `interval-${index}`,\n                    type: \"number\",\n                    value: ((_scheduleSettings$int = scheduleSettings.intervals[index]) === null || _scheduleSettings$int === void 0 ? void 0 : _scheduleSettings$int.toString()) || '24',\n                    onChange: e => handleIntervalChange(index, e.target.value),\n                    className: \"w-24 mb-0\" // Adjusted margin\n                    ,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600 dark:text-gray-400 capitalize\",\n                    children: scheduleSettings.unit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 22\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 20\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this), activeEmailCount <= 1 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-400 text-sm px-4\",\n              children: \"Sequence timing options are available when your campaign has more than one selected email with content.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 18\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 11\n          }, this);\n        }\n      case 4:\n        // Add Recipients Step\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Add Recipients\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-3 text-text-primary\",\n              children: \"Add Manually\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:flex md:items-end md:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-grow mb-3 md:mb-0\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"manualRecipient\",\n                  name: \"manualRecipient\",\n                  label: \"Email Address\",\n                  type: \"email\",\n                  value: manualRecipient.email,\n                  onChange: e => setManualRecipient({\n                    ...manualRecipient,\n                    email: e.target.value\n                  }),\n                  placeholder: \"Enter email address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: addManualRecipient,\n                variant: \"secondary\",\n                className: \"btn-cta mt-4 md:mt-0\",\n                children: \"Add Recipient\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 879,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-3 text-text-primary\",\n              children: \"Add From Contacts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this), loadingContacts ? /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading contacts...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 17\n            }, this) : contacts.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:flex md:items-end md:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-grow mb-3 md:mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"contactSelect\",\n                  className: \"block text-sm font-medium text-text-secondary mb-1\",\n                  children: \"Select Contacts (use Ctrl/Cmd to select multiple)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"contactSelect\",\n                  multiple: true,\n                  value: selectedContactIds,\n                  onChange: handleContactSelection,\n                  className: \"form-input w-full h-32 border border-border rounded-md\",\n                  children: contacts.map(contact => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: contact.id,\n                    children: contact.name ? `${contact.name} (${contact.email})` : contact.email\n                  }, contact.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 917,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 905,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: addSelectedContacts,\n                variant: \"secondary\",\n                className: \"btn-cta mt-4 md:mt-0\",\n                children: \"Add Selected\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 904,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary\",\n              children: \"No contacts found. You can add contacts in the 'Contacts' section.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-3 text-text-primary\",\n              children: [\"Recipients Added (\", recipients.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 15\n            }, this), recipients.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"divide-y divide-border max-h-60 overflow-y-auto\",\n              children: recipients.map((rec, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"py-2 flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: rec.name ? `${rec.name} (${rec.email})` : rec.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => removeRecipient(rec.email),\n                  className: \"text-red-500 hover:text-red-700 text-sm\",\n                  children: \"\\xD7 Remove\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 934,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary\",\n              children: \"No recipients added yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 931,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 11\n        }, this);\n      case 5:\n        {\n          // Review and Schedule Step\n          const scheduledTimeString = sendScheduleOption === 'later' && scheduledDateTime ? new Date(scheduledDateTime).toLocaleString() : 'Immediately';\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"Review & Schedule\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: \"Campaign Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 19\n                }, this), \" \", campaignName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 959,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Subject:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 19\n                }, this), \" \", subject]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"From:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 961,\n                  columnNumber: 19\n                }, this), \" \", fromName, \" <\", fromEmail, \">\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 16\n              }, this), replyTo && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Reply To:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 31\n                }, this), \" \", replyTo]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: \"Sequence (Emails to Send)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 16\n              }, this), emailContents.filter((e, idx) => activeEmailIndices.includes(idx) && e.html.trim()).length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-decimal pl-5 space-y-1\",\n                children: emailContents.reduce((acc, email, index) => {\n                  // Use stricter validation: check for non-empty HTML AND active status\n                  if (activeEmailIndices.includes(index) && email.html.trim()) {\n                    const emailNumberInActiveSequence = acc.length + 1;\n                    let timingText = 'Sent at Campaign Start Time'; // Updated default text\n                    // Calculate timing based on the position in the *active* sequence\n                    if (emailNumberInActiveSequence > 1 && scheduleSettings.intervals[emailNumberInActiveSequence - 2] !== undefined) {\n                      timingText = `Sent ${scheduleSettings.intervals[emailNumberInActiveSequence - 2]} ${scheduleSettings.unit} after Email #${emailNumberInActiveSequence - 1}`;\n                    } else if (emailNumberInActiveSequence > 1) {\n                      timingText = `Timing interval missing`; // Fallback\n                    }\n                    acc.push(/*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\"Email \", index + 1, \" - \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-text-secondary\",\n                        children: timingText\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 985,\n                        columnNumber: 48\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 983,\n                      columnNumber: 26\n                    }, this));\n                  }\n                  return acc;\n                }, [])\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 18\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500\",\n                children: \"No emails selected or have valid HTML content. Go back to Step 2.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 18\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: [\"Recipients (\", recipients.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 998,\n                columnNumber: 16\n              }, this), recipients.length > 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [recipients.length, \" contacts selected.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 18\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-red-500\",\n                children: \"No recipients added. Go back to Step 4.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1002,\n                columnNumber: 18\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: \"Schedule\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Send Time:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 19\n                }, this), \" \", scheduledTimeString]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 16\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleCreateCampaign\n              // Update disabled check: need at least one ACTIVE email with HTML\n              ,\n              disabled: loading || recipients.length === 0 || !emailContents.some((e, idx) => activeEmailIndices.includes(idx) && e.html.trim()),\n              className: \"w-full btn-cta\",\n              children: loading ? 'Creating Campaign...' : 'Create & Schedule Campaign'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 14\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 12\n          }, this);\n        }\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Invalid Step\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Define steps for stepper\n  const steps = ['Campaign Info', 'Email Content', 'Scheduling', 'Recipients', 'Review'];\n  // Use step state variable as currentStep\n  const currentStep = step;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-3xl font-bold mb-6 text-text-primary\",\n      children: \"Create New Campaign\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1034,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"ol\", {\n        className: \"flex items-center w-full text-sm font-medium text-center text-text-secondary sm:text-base\",\n        children: steps.map((stepName, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: `flex md:w-full items-center ${currentStep > index + 1 ? 'text-primary-blue' : currentStep === index + 1 ? 'text-accent-coral' : 'text-text-secondary'} ${index + 1 < steps.length ? \"after:content-['\\u00a0'] after:w-full after:h-1 after:border-b after:border-border after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700\" : \"\"}`,\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `flex items-center ${index + 1 < steps.length ? \"sm:after:content-['\\u00a0'] after:hidden sm:after:inline-block after:w-4 after:h-4 after:rounded-full after:mx-auto after:mt-1\" : \"\"}`,\n            children: [currentStep > index + 1 ? /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2.5\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1043,\n                columnNumber: 110\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 24\n            }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1045,\n              columnNumber: 24\n            }, this), stepName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1041,\n            columnNumber: 18\n          }, this)\n        }, stepName, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1038,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1037,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      type: \"error\",\n      message: error,\n      onClose: () => setError(''),\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1054,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n      type: \"success\",\n      message: success\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1055,\n      columnNumber: 19\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: renderStepContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1058,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handlePrevious,\n        disabled: currentStep === 1 || loading,\n        variant: \"secondary\",\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1064,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleNext,\n        disabled: loading || currentStep === 5 /* Disable Next on last step */,\n        children: currentStep === steps.length - 1 ? 'Review Campaign' : 'Next'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1071,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1063,\n      columnNumber: 7\n    }, this), renderTemplatePicker()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1033,\n    columnNumber: 5\n  }, this);\n};\n_s(CampaignCreate, \"a88Po2WRFxJ4M0StD1/RJ6Y4ITY=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c = CampaignCreate;\nexport default CampaignCreate;\nvar _c;\n$RefreshReg$(_c, \"CampaignCreate\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "Input", "MjmlEditor", "Modal", "useAuth", "useLocation", "useNavigate", "templateRecommendationService", "api", "campaignAPI", "jsxDEV", "_jsxDEV", "CampaignCreate", "_s", "_user$domain", "user", "location", "navigate", "campaignName", "setCampaignName", "subject", "setSubject", "fromName", "setFromName", "name", "fromEmail", "setFromEmail", "domain", "status", "replyTo", "setReplyTo", "error", "setError", "success", "setSuccess", "loading", "setLoading", "templateLoading", "setTemplateLoading", "step", "setStep", "currentEmail", "setCurrentEmail", "emailContents", "setEmailContents", "saved", "localStorage", "getItem", "arr", "JSON", "parse", "Array", "isArray", "length", "map", "item", "mjml", "html", "from", "scheduleSettings", "setScheduleSettings", "intervals", "unit", "campaignPreview", "setCampaignPreview", "showTemplatePicker", "setShowTemplatePicker", "templatesList", "setTemplatesList", "loadingTemplates", "setLoadingTemplates", "selectedTemplate", "setSelectedTemplate", "sendScheduleOption", "setSendScheduleOption", "scheduledDateTime", "setScheduledDateTime", "date", "Date", "now", "toISOString", "slice", "recipients", "setRecipients", "manualRecipient", "set<PERSON>anualRecip<PERSON>", "email", "contacts", "setContacts", "selectedContactIds", "setSelectedContactIds", "loadingContacts", "setLoadingContacts", "editor<PERSON><PERSON>", "activeEmailIndices", "setActiveEmailIndices", "initialActive", "initialContents", "for<PERSON>ach", "index", "trim", "push", "queryParams", "URLSearchParams", "search", "templateId", "get", "fetchTemplate", "id", "res", "getTemplateById", "template", "temp", "updated", "mjml<PERSON><PERSON><PERSON>", "content", "handleMjmlSave", "console", "log", "substring", "currentIndex", "setItem", "stringify", "prev", "includes", "sort", "a", "b", "filter", "i", "getAllTemplates", "then", "r", "data", "catch", "finally", "handleTemplateSelect", "_r$template$content", "_id", "hasMjml", "<PERSON><PERSON><PERSON><PERSON>", "contentLength", "err", "handleUseTemplate", "emailIndex", "hasHtml", "mjm<PERSON><PERSON><PERSON><PERSON>", "htmlLength", "setTimeout", "handleCreateCampaign", "validEmails", "e", "_activeEmailsToSend$", "activeEmailsToSend", "campaignData", "scheduled", "scheduledFor", "recipientList", "userId", "htmlContent", "schedule", "emailIntervals", "d", "delay", "createCampaign", "campaignId", "toLocaleString", "removeItem", "sessionStorage", "warn", "_err$response", "_err$response$data", "response", "message", "handleBrowseTemplates", "draft", "handleEmailSelect", "activeEmailCount", "reduce", "count", "fill", "fetchContacts", "_r$data", "list", "c", "String", "Math", "random", "fullName", "addManualRecipient", "some", "addSelectedContacts", "toAdd", "handleContactSelection", "target", "selectedOptions", "o", "value", "removeRecipient", "handleNext", "latestContent", "savedContentHasHtml", "stateHasHtml", "current", "save", "savedContentIsActive", "isActive", "finalHasHtmlCheck", "cnt", "v", "handlePrevious", "handleClearEmailContent", "renderTemplatePicker", "isOpen", "onClose", "title", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "t", "onClick", "thumbnailUrl", "src", "alt", "variant", "disabled", "renderStepContent", "_user$domain2", "_user$domain3", "label", "onChange", "required", "type", "helpText", "curr", "emailsWithContent", "_", "idx", "hasHtmlContent", "isSelected", "buttonVariant", "customClasses", "buttonText", "icon", "selectionRingClass", "size", "ref", "initialMjml", "onSave", "height", "handleScheduleOptionChange", "handleScheduleTimeChange", "handleIntervalChange", "numValue", "parseInt", "isNaN", "newIntervals", "handleUnitChange", "checked", "htmlFor", "_scheduleSettings$int", "toString", "placeholder", "multiple", "contact", "rec", "scheduledTimeString", "acc", "emailNumberInActiveSequence", "timingText", "undefined", "steps", "currentStep", "<PERSON><PERSON><PERSON>", "viewBox", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/campaigns/CampaignCreate.tsx"], "sourcesContent": ["// frontend/src/pages/campaigns/CampaignCreate.tsx\n\nimport '../../styles/editor.css';\n\nimport React, {\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport Input from 'components/Input';\nimport MjmlEditor, { MjmlEditorRef } from 'components/MjmlEditor';\nimport { Modal } from 'components/Modal';\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useLocation,\n  useNavigate,\n} from 'react-router-dom';\nimport { templateRecommendationService } from 'services';\nimport api, { campaignAPI } from 'services/api';\n\nconst CampaignCreate: React.FC = () => {\n  const { user } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // Campaign metadata\n  const [campaignName, setCampaignName] = useState('');\n  const [subject, setSubject] = useState('');\n  const [fromName, setFromName] = useState(user?.name || '');\n  const [fromEmail, setFromEmail] = useState(\n    user?.domain?.status === 'active' ? `noreply@${user.domain.name}` : ''\n  );\n  const [replyTo, setReplyTo] = useState('');\n\n  // UI state\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [templateLoading, setTemplateLoading] = useState(false);\n  const [step, setStep] = useState(1);\n\n  // Email sequence (up to 10)\n  const [currentEmail, setCurrentEmail] = useState<number>(1);\n  const [emailContents, setEmailContents] = useState<{ mjml: string; html: string }[]>(() => {\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          return arr.map((item: any) => ({\n            mjml: item.mjml || '',\n            html: item.html || '',\n          }));\n        }\n      } catch {\n        // ignore parse errors\n      }\n    }\n    return Array.from({ length: 10 }, () => ({ mjml: '', html: '' }));\n  });\n\n  // Scheduling\n  const [scheduleSettings, setScheduleSettings] = useState<{\n    intervals: number[];\n    unit: 'minutes' | 'hours' | 'days';\n  }>({\n    intervals: [24],\n    unit: 'hours',\n  });\n\n  // Preview and template picker\n  const [campaignPreview, setCampaignPreview] = useState<any>(null);\n  const [showTemplatePicker, setShowTemplatePicker] = useState(false);\n  const [templatesList, setTemplatesList] = useState<any[]>([]);\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);\n\n  // Recipients\n  const [sendScheduleOption, setSendScheduleOption] = useState<'now' | 'later'>('later');\n  const [scheduledDateTime, setScheduledDateTime] = useState<string>(() => {\n    const date = new Date(Date.now() + 60 * 60 * 1000);\n    return date.toISOString().slice(0, 16);\n  });\n  const [recipients, setRecipients] = useState<{ id: string | null; email: string; name?: string }[]>([]);\n  const [manualRecipient, setManualRecipient] = useState({ email: '', name: '' });\n  const [contacts, setContacts] = useState<{ id: string; email: string; name: string }[]>([]);\n  const [selectedContactIds, setSelectedContactIds] = useState<string[]>([]);\n  const [loadingContacts, setLoadingContacts] = useState(false);\n\n  // Ref to MJML editor\n  const editorRef = useRef<MjmlEditorRef>(null);\n\n  // State to track which emails are actively selected for sending\n  const [activeEmailIndices, setActiveEmailIndices] = useState<number[]>(() => {\n    // Initialize based on emails that already have HTML content\n    const initialActive: number[] = [];\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    let initialContents = Array.from({ length: 10 }, () => ({ mjml: '', html: '' }));\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          initialContents = arr.map((item: any) => ({ mjml: item.mjml || '', html: item.html || '' }));\n        }\n      } catch {}\n    }\n    initialContents.forEach((email, index) => {\n      if (email.html && email.html.trim()) {\n        initialActive.push(index);\n      }\n    });\n    return initialActive;\n  });\n\n  // Handle URL param for templateId\n  const queryParams = new URLSearchParams(location.search);\n  const templateId = queryParams.get('templateId');\n  useEffect(() => {\n    if (templateId) fetchTemplate(templateId);\n  }, [templateId]);\n\n  // Fetch AI or saved template by ID\n  const fetchTemplate = async (id: string) => {\n    setTemplateLoading(true);\n    try {\n      const res = await templateRecommendationService.getTemplateById(id);\n      if (res.success && res.template) {\n        const temp = res.template;\n        if (!campaignName) setCampaignName(`Campaign based on ${temp.name}`);\n        const updated = [...emailContents];\n        updated[0] = {\n          mjml: temp.mjmlContent || '',\n          html: temp.content || '',\n        };\n        setEmailContents(updated);\n      } else {\n        setError('Failed to load template.');\n      }\n    } catch {\n      setError('Failed to load template.');\n    } finally {\n      setTemplateLoading(false);\n    }\n  };\n\n  // Save handler from MJML editor\n  const handleMjmlSave = (mjml: string, html: string) => {\n    console.log(`--- handleMjmlSave called for email ${currentEmail} ---`);\n    console.log('Received MJML:', mjml ? mjml.substring(0, 100) + '...' : '(empty)');\n    console.log('Received HTML:', html ? html.substring(0, 100) + '...' : '(empty)');\n    const updated = [...emailContents];\n    const currentIndex = currentEmail - 1; // Get index before state update\n    updated[currentIndex] = { mjml, html };\n    setEmailContents(updated);\n    console.log('State AFTER update attempt:', updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n\n    // If saving resulted in valid HTML, ensure this email is marked active\n    if (html && html.trim()) {\n      setActiveEmailIndices(prev => {\n        if (!prev.includes(currentIndex)) {\n          // Add if not already present, keep sorted for consistency\n          return [...prev, currentIndex].sort((a, b) => a - b);\n        }\n        return prev; // Already active, no change needed\n      });\n      console.log(`Ensured email index ${currentIndex} is active after save.`);\n    } else {\n      // If saving resulted in empty HTML, ensure it's deactivated\n      setActiveEmailIndices(prev => prev.filter(i => i !== currentIndex));\n      console.log(`Deactivated email index ${currentIndex} after save due to empty HTML.`);\n    }\n  };\n\n  // Template picker effects and handlers\n  useEffect(() => {\n    if (showTemplatePicker) {\n      setLoadingTemplates(true);\n      templateRecommendationService.getAllTemplates()\n        .then(r => setTemplatesList(r.data || []))\n        .catch(console.error)\n        .finally(() => setLoadingTemplates(false));\n    }\n  }, [showTemplatePicker]);\n\n  const handleTemplateSelect = async (id: string) => {\n    setLoadingTemplates(true);\n    try {\n      console.log(`[Debug] Selecting template with ID: ${id}`);\n      const r = await templateRecommendationService.getTemplateById(id);\n      console.log('[Debug] Template API response:', r);\n      if (r.success && r.template) {\n        console.log('[Debug] Setting selected template:', {\n          id: r.template.id || r.template._id,\n          name: r.template.name,\n          hasMjml: !!r.template.mjmlContent,\n          hasContent: !!r.template.content,\n          contentLength: r.template.content?.length || 0\n        });\n      }\n      setSelectedTemplate(r.template);\n    } catch (err) {\n      console.error('[Debug] Error selecting template:', err);\n      setError('Failed to load selected template.');\n    } finally {\n      setLoadingTemplates(false);\n    }\n  };\n\n  const handleUseTemplate = () => {\n    console.log('[Debug] Using selected template:', selectedTemplate ? {\n      id: selectedTemplate.id || selectedTemplate._id,\n      name: selectedTemplate.name,\n      hasMjml: !!selectedTemplate.mjmlContent,\n      hasContent: !!selectedTemplate.content\n    } : 'No template selected');\n    \n    if (selectedTemplate) {\n      const updated = [...emailContents];\n      updated[currentEmail - 1] = {\n        mjml: selectedTemplate.mjmlContent || '',\n        html: selectedTemplate.content || '',\n      };\n      \n      console.log('[Debug] Updated email contents:', {\n        emailIndex: currentEmail - 1,\n        hasMjml: !!updated[currentEmail - 1].mjml,\n        hasHtml: !!updated[currentEmail - 1].html,\n        mjmlLength: updated[currentEmail - 1].mjml.length,\n        htmlLength: updated[currentEmail - 1].html.length\n      });\n      \n      // Save the updated contents to state and local storage\n      setEmailContents(updated);\n      localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n      \n      // Close the modal first to reduce complexity\n      setSelectedTemplate(null);\n      setShowTemplatePicker(false);\n      \n      // Force a complete re-render of the editor component\n      // Wait a moment to ensure state updates have completed\n      setTimeout(() => {\n        // This will force the MjmlEditor to completely remount with the new content\n        // Temporarily set current email to a value that won't match any existing email\n        setCurrentEmail(-1);\n        \n        // After a brief delay, restore the current email index\n        setTimeout(() => {\n          setCurrentEmail(currentEmail);\n        }, 50);\n      }, 100);\n    }\n  };\n\n  // Campaign creation\n  const handleCreateCampaign = async () => {\n    if (!campaignName || !subject || !fromName || !fromEmail) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    const validEmails = emailContents.filter(e => e.html.trim());\n    if (validEmails.length === 0) {\n      setError('Please add at least one email with valid HTML content');\n      return;\n    }\n    if (recipients.length === 0) {\n      setError('Please add at least one recipient');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Filter emails with actual HTML content AND that are marked active\n      const activeEmailsToSend = emailContents.filter((email, index) => \n        activeEmailIndices.includes(index) && email.html && email.html.trim()\n      );\n\n      // Validation based on filtered list\n      if (activeEmailsToSend.length === 0) {\n        setError('Please select at least one email with valid HTML content to send.');\n        setLoading(false);\n        return;\n      }\n      if (recipients.length === 0) {\n        setError('Please add at least one recipient');\n        setLoading(false);\n        return;\n      }\n      // --- End new validation ---\n\n      // Construct the base campaign data\n      const campaignData: any = {\n        name: campaignName,\n        subject,\n        fromName,\n        fromEmail,\n        replyTo: replyTo || fromEmail,\n        scheduled: sendScheduleOption === 'later',\n        ...(sendScheduleOption === 'later' && { scheduledFor: new Date(scheduledDateTime).toISOString() }),\n        recipientList: recipients,\n        userId: user?.id,\n        status: 'draft',\n        // Use the first ACTIVE email for top-level content\n        htmlContent: activeEmailsToSend[0]?.html || '', \n        // Send ONLY the emails that are ACTIVE and have content\n        emailContents: activeEmailsToSend.map(e => ({ mjml: e.mjml, html: e.html })),\n      };\n\n      // Add schedule only if there's more than one ACTIVE email with content\n      if (activeEmailsToSend.length > 1) {\n        campaignData.schedule = {\n          unit: scheduleSettings.unit,\n          // Slice intervals to match the number of gaps between ACTIVE emails\n          emailIntervals: scheduleSettings.intervals.slice(0, activeEmailsToSend.length - 1)\n            .map(d => ({ delay: d, unit: scheduleSettings.unit })),\n        };\n      }\n\n      console.log(\"Sending filtered & active campaign data:\", campaignData);\n\n      // Send the filtered data to the API\n      const res = await campaignAPI.createCampaign(campaignData);\n      // Check for _id primarily, then id\n      const campaignId = res?._id || res?.id;\n      console.log('Create campaign response:', res, 'Extracted ID:', campaignId);\n\n      setSuccess(sendScheduleOption === 'later'\n        ? `Campaign scheduled for ${new Date(scheduledDateTime).toLocaleString()}`\n        : 'Campaign created and will start sending shortly');\n      localStorage.removeItem('driftly_campaign_create_email_contents');\n      // Clear draft info if it exists\n      localStorage.removeItem('driftly_campaign_draft'); \n      sessionStorage.setItem('reloadCampaigns', 'true');\n      setTimeout(() => {\n        // Navigate using the extracted ID\n        if (campaignId) navigate(`/campaigns/${campaignId}/summary`);\n        else {\n          console.warn('No ID found in createCampaign response, navigating to list.');\n          navigate('/campaigns');\n        }\n      }, 1500);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBrowseTemplates = () => {\n    if (campaignName || subject || fromName || fromEmail || replyTo) {\n      localStorage.setItem('driftly_campaign_draft',\n        JSON.stringify({ campaignName, subject, fromName, fromEmail, replyTo }));\n    }\n    navigate('/email-templates');\n  };\n\n  useEffect(() => {\n    const draft = localStorage.getItem('driftly_campaign_draft');\n    if (draft) {\n      try {\n        const d = JSON.parse(draft);\n        setCampaignName(d.campaignName || '');\n        setSubject(d.subject || '');\n        setFromName(d.fromName || user?.name || '');\n        setFromEmail(d.fromEmail || '');\n        setReplyTo(d.replyTo || '');\n      } catch {}\n      localStorage.removeItem('driftly_campaign_draft');\n    }\n  }, [user]);\n\n  const handleEmailSelect = (i: number) => setCurrentEmail(i);\n\n  useEffect(() => {\n    if (step === 3) {\n      // Count emails that are BOTH active AND have HTML\n      const activeEmailCount = emailContents.reduce((count, email, index) => {\n        if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n          return count + 1;\n        }\n        return count;\n      }, 0);\n\n      console.log('Active emails with HTML for scheduling:', activeEmailCount);\n\n      if (activeEmailCount > 1) {\n        setScheduleSettings(prev => ({\n          ...prev,\n          // Ensure intervals array matches the number of gaps between ACTIVE emails\n          intervals: Array(activeEmailCount - 1).fill(prev.intervals[0] || 24),\n        }));\n      } else {\n        // If 0 or 1 active emails, no intervals needed\n        setScheduleSettings(prev => ({ ...prev, intervals: [] }));\n      }\n    }\n  }, [step, emailContents, activeEmailIndices]); // Add activeEmailIndices dependency\n\n  const fetchContacts = async () => {\n    if (step === 4) {\n      setLoadingContacts(true);\n      try {\n        const r = await api.get('/contacts');\n        let list = Array.isArray(r.data) ? r.data : r.data?.data || [];\n        setContacts(list.map((c: any) => ({\n          id: c.id || c._id || String(Math.random()),\n          email: c.email,\n          name: c.name || c.fullName || '',\n        })));\n      } catch {\n        setError('Failed to load contacts');\n      } finally {\n        setLoadingContacts(false);\n      }\n    }\n  };\n\n  useEffect(() => {\n    fetchContacts();\n  }, [step]);\n\n  const addManualRecipient = () => {\n    if (!manualRecipient.email) {\n      setError('Please enter an email address.');\n    } else if (!recipients.some(r => r.email === manualRecipient.email)) {\n      setRecipients([...recipients, { id: null, ...manualRecipient }]);\n      setManualRecipient({ email: '', name: '' });\n    } else {\n      setError('Recipient already added.');\n    }\n    setTimeout(() => setError(''), 3000);\n  };\n\n  const addSelectedContacts = () => {\n    const toAdd = contacts.filter(c =>\n      selectedContactIds.includes(c.id) && !recipients.some(r => r.email === c.email)\n    );\n    setRecipients([...recipients, ...toAdd]);\n    setSelectedContactIds([]);\n  };\n  const handleContactSelection = (e: React.ChangeEvent<HTMLSelectElement>) =>\n    setSelectedContactIds(Array.from(e.target.selectedOptions, o => o.value));\n  const removeRecipient = (email: string) =>\n    setRecipients(recipients.filter(r => r.email !== email));\n\n  // Step navigation\n  const handleNext = async () => {\n    setError('');\n\n    // Validation for Step 1\n    if (step === 1 && (!campaignName || !subject || !fromName || !fromEmail)) {\n      setError('Please fill in all required fields.');\n      return;\n    }\n\n    // Validation for Step 2\n    if (step === 2) {\n      console.log('--- handleNext: Step 2 Validation ---');\n      let latestContent: { mjml: string; html: string } | null = null;\n      let savedContentHasHtml = false;\n      let stateHasHtml = false;\n\n      // --- Force save current editor's content FIRST ---\n      if (editorRef.current) {\n        console.log('Forcing editor save for current email:', currentEmail);\n        // Await the result of the now async save method\n        latestContent = await editorRef.current.save(); \n        if (latestContent) {\n          console.log('Async save function returned content.');\n          // Log the returned HTML content (or lack thereof)\n          console.log('Returned MJML:', latestContent.mjml ? latestContent.mjml.substring(0, 50) + '...' : '(empty)');\n          console.log('Returned HTML:', latestContent.html ? latestContent.html.substring(0, 50) + '...' : '(empty)');\n\n          // Check if the *returned* content has HTML\n          if (latestContent.html && latestContent.html.trim()) {\n            console.log('RETURNED content has non-empty HTML.');\n            savedContentHasHtml = true;\n          } else {\n            console.log('RETURNED content has empty or no HTML.');\n          }\n\n          // Trigger state update (async) - Allow this to proceed even if HTML is missing\n          console.log('Triggering handleMjmlSave state update...');\n          handleMjmlSave(latestContent.mjml, latestContent.html);\n\n        } else {\n          // This case might be less likely now if save always returns an object\n          console.warn('editorRef.current.save() did not return expected content object.');\n        }\n      } else {\n         console.error('MjmlEditor ref is not available for forced save.');\n      }\n      // --- End Force save ---\n\n      // --- Validation ---\n      // Since save is now awaited and includes a delay, checking the returned content\n      // (savedContentHasHtml) should be more reliable. We also check state.\n      \n      // Check if the SAVED content belongs to an index currently marked ACTIVE\n      const savedContentIsActive = savedContentHasHtml && activeEmailIndices.includes(currentEmail - 1);\n      if (savedContentIsActive) {\n        console.log('Saved content is for an active email.');\n      }\n\n      console.log('Checking emailContents state AFTER awaiting save/update attempt...');\n      // Check if ANY email in state is BOTH active AND has HTML\n      stateHasHtml = emailContents.some((email, index) => {\n        const hasHtml = email.html && email.html.trim();\n        const isActive = activeEmailIndices.includes(index);\n        if (hasHtml && isActive) {\n             console.log(`Email index ${index} in STATE is ACTIVE and has HTML.`);\n             return true;\n        }\n        return false;\n      });\n      if (!stateHasHtml) {\n          console.log('No email in STATE is both active and has HTML.');\n      }\n\n      // Final decision: Did *either* the direct save (if active) *or* the state check find an ACTIVE email with HTML?\n      const finalHasHtmlCheck = savedContentIsActive || stateHasHtml;\n      console.log('Final check result (Active & HTML):', finalHasHtmlCheck, `(Saved Active: ${savedContentIsActive}, State Active: ${stateHasHtml})`);\n\n      if (!finalHasHtmlCheck) {\n        setError('Please ensure at least one selected email (marked as \\'Sending\\') has valid HTML content.');\n        return; // Stop execution if validation fails specifically for step 2\n      }\n      console.log('Step 2 validation passed (Active HTML check).');\n    }\n\n    // Validation for Step 3\n    if (step === 3) {\n      const cnt = emailContents.filter(e => e.html.trim()).length;\n      if (cnt > 1) {\n        if (scheduleSettings.intervals.length !== cnt - 1) {\n          setError(`Define ${cnt - 1} intervals.`);\n          return;\n        }\n        if (scheduleSettings.intervals.some(v => v <= 0)) {\n          setError('Intervals must be positive.');\n          return;\n        }\n      }\n    }\n\n    // Validation for Step 4\n    if (step === 4 && recipients.length === 0) {\n      setError('Please add at least one recipient.');\n      return;\n    }\n\n    // Proceed to next step if all relevant validations passed and not on the last step\n    if (step < 5) {\n      console.log(`Proceeding from step ${step} to step ${step + 1}`);\n      setStep(step + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    setError('');\n    if (step > 1) setStep(step - 1);\n  };\n\n  // --- Add this handler ---\n  const handleClearEmailContent = (index: number) => {\n    const updated = [...emailContents];\n    updated[index] = { mjml: '', html: '' };\n    setEmailContents(updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n    // Also remove from active indices when cleared\n    setActiveEmailIndices(prev => prev.filter(i => i !== index));\n    console.log(`Cleared content and deactivated email index ${index}`);\n  };\n  // --- End Add Handler ---\n\n  const renderTemplatePicker = () => (\n    <Modal isOpen={showTemplatePicker} onClose={() => setShowTemplatePicker(false)} title=\"Choose Template\">\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\">\n        {loadingTemplates\n          ? <p>Loading...</p>\n          : templatesList.map(t => (\n            <Card\n              key={t.id}\n              className={`cursor-pointer ${selectedTemplate?.id === t.id ? 'ring-2 ring-primary' : ''}`}\n              onClick={() => handleTemplateSelect(t.id)}\n            >\n              <h4 className=\"font-semibold mb-2 truncate\">{t.name}</h4>\n              <div className=\"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\">\n                {t.thumbnailUrl ? <img src={t.thumbnailUrl} alt={t.name} className=\"object-contain h-full w-full\" /> : 'No Preview'}\n              </div>\n            </Card>\n          ))\n        }\n      </div>\n      <div className=\"mt-4 flex justify-end gap-2\">\n        <Button variant=\"secondary\" onClick={() => setShowTemplatePicker(false)}>Cancel</Button>\n        <Button onClick={handleUseTemplate} disabled={!selectedTemplate || loadingTemplates}>\n          Use Selected\n        </Button>\n      </div>\n    </Modal>\n  );\n\n  const renderStepContent = () => {\n    switch (step) {\n      case 1: return (\n        <div className=\"space-y-4\">\n          <Input id=\"campaignName\" name=\"campaignName\" label=\"Campaign Name\" value={campaignName} onChange={e => setCampaignName(e.target.value)} required />\n          <Input id=\"subject\" name=\"subject\" label=\"Email Subject\" value={subject} onChange={e => setSubject(e.target.value)} required />\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input id=\"fromName\" name=\"fromName\" label=\"From Name\" value={fromName} onChange={e => setFromName(e.target.value)} required />\n            <Input\n              id=\"fromEmail\"\n              name=\"fromEmail\"\n              label=\"From Email\"\n              type=\"email\"\n              value={fromEmail}\n              onChange={e => setFromEmail(e.target.value)}\n              disabled={user?.domain?.status === 'active'}\n              required\n              helpText={user?.domain?.status === 'active' ? `Using verified domain: ${user.domain.name}` : 'Verify domain for deliverability.'}\n            />\n          </div>\n          <Input id=\"replyTo\" name=\"replyTo\" label=\"Reply-To Email (optional)\" type=\"email\" value={replyTo} onChange={e => setReplyTo(e.target.value)} />\n        </div>\n      );\n      case 2:\n        const curr = emailContents[currentEmail - 1];\n        // Use stricter validation: check for non-empty HTML\n        const emailsWithContent = emailContents.filter(e => e.html.trim()).length;\n        \n        return (\n          <div className=\"flex flex-col lg:flex-row gap-4 h-full\">\n            {/* Email sequence sidebar */}\n            <div className=\"w-full lg:w-1/5 flex flex-col gap-2\">\n              <h3 className=\"font-semibold mb-2\">Email Sequence (Click to include/exclude):</h3>\n              {Array.from({ length: 10 }).map((_, idx) => {\n                const hasHtmlContent = !!(emailContents[idx].html && emailContents[idx].html.trim());\n                const isSelected = currentEmail === idx + 1;\n                const isActive = activeEmailIndices.includes(idx);\n\n                // Determine button appearance based on content and active state\n                let buttonVariant: \"primary\" | \"secondary\" = \"secondary\";\n                let customClasses = \"\";\n                let buttonText = `Email ${idx + 1}`;\n                let icon = null;\n\n                if (hasHtmlContent) {\n                  if (isActive) {\n                    buttonVariant = \"primary\"; // Has content, is active\n                    icon = <span className=\"ml-1 text-green-500 dark:text-green-400 font-bold\">✓</span>;\n                    buttonText += \" (Sending)\";\n                  } else {\n                    buttonVariant = \"secondary\"; // Has content, but inactive - use secondary variant\n                    customClasses = \"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\"; // Custom styling for inactive\n                    icon = <span className=\"ml-1 text-gray-500 dark:text-gray-400 font-bold\">⏸</span>; // Pause icon maybe?\n                    buttonText += \" (Excluded)\";\n                  }\n                } else {\n                   // No content, always secondary, cannot be active\n                   // Make it look distinct but clickable\n                   customClasses = \"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\"; \n                   buttonText += \" (No Content - Click to Edit)\";\n                }\n                \n                const selectionRingClass = isSelected ? \"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\" : \"\";\n                \n                return (\n                  <Button\n                    key={idx}\n                    variant={buttonVariant} // Will be 'secondary' for no content\n                    onClick={() => {\n                      // Set as current email for editing - ALWAYS DO THIS\n                      setCurrentEmail(idx + 1);\n                      \n                      // Toggle active state ONLY if it has content\n                      if (hasHtmlContent) {\n                        setActiveEmailIndices(prev => \n                          prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx]\n                        );\n                      }\n                    }}\n                    size=\"sm\"\n                    className={`w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses}`}\n                    title={hasHtmlContent ? (isActive ? \"Click to exclude from sending\" : \"Click to include in sending\") : \"Click to edit this email\"}\n                  >\n                    <span className=\"flex-grow truncate\">{buttonText}</span>\n                    {icon} \n                  </Button>\n                );\n              })}\n              \n              {/* Add helper text explaining the buttons */}\n              <div className=\"mt-4 p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm space-y-1\">\n                <p className=\"font-medium mb-1\">Email Status Legend:</p>\n                <p className=\"flex items-center\">\n                  <span className=\"ml-1 mr-2 text-green-500 dark:text-green-400 font-bold\">✓</span> \n                  <span className=\"text-green-700 dark:text-green-400\">Has Content, Will Send</span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"inline-block w-3 h-3 mr-2 bg-gray-400 dark:bg-gray-500 rounded-full\"></span> \n                  <span className=\"text-gray-700 dark:text-gray-300\">Has Content, <span className='font-bold'>Excluded</span></span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"inline-block w-3 h-3 mr-2 border border-dashed border-gray-400 dark:border-gray-600 rounded-full opacity-75\"></span> \n                  <span className=\"text-gray-700 dark:text-gray-300\">No Content (Click to Edit)</span>\n                 </p>\n                 <p className=\"text-xs mt-2 text-gray-600 dark:text-gray-400\">Click emails with content to toggle inclusion. Click any email to edit.</p>\n              </div>\n              \n              <div className=\"mt-4\">\n                <Button variant=\"secondary\" onClick={() => setShowTemplatePicker(true)}>Choose Template</Button>\n                <Button variant=\"secondary\" onClick={handleBrowseTemplates} className=\"mt-2\">Browse All Templates</Button>\n              </div>\n            </div>\n            {/* Editor */}\n            <div className=\"w-full lg:w-4/5 flex flex-col\">\n              <MjmlEditor\n                key={currentEmail}\n                ref={editorRef}\n                initialMjml={curr.mjml}\n                onSave={handleMjmlSave}\n                height=\"70vh\"\n              />\n              <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-2\">\n                  Changes are saved automatically. Emails with a green check mark (✓) will be included in your campaign.\n              </p>\n            </div>\n          </div>\n        );\n      case 3: {\n        // Calculate count based on emails that are BOTH active AND have HTML\n        const activeEmailCount = emailContents.reduce((count, email, index) => {\n          if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n            return count + 1;\n          }\n          return count;\n        }, 0);\n        console.log('Active emails with HTML for rendering Step 3:', activeEmailCount);\n\n        // Handlers for main scheduling options\n        const handleScheduleOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n          setSendScheduleOption(e.target.value as 'now' | 'later');\n        };\n        const handleScheduleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n          setScheduledDateTime(e.target.value);\n        };\n\n        // Handlers for interval settings (only relevant if emailCount > 1)\n        const handleIntervalChange = (index: number, value: string) => {\n          const numValue = parseInt(value, 10);\n          if (!isNaN(numValue) && numValue > 0) {\n            const newIntervals = [...scheduleSettings.intervals];\n            newIntervals[index] = numValue;\n            setScheduleSettings({ ...scheduleSettings, intervals: newIntervals });\n          }\n        };\n        const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n          setScheduleSettings({ ...scheduleSettings, unit: e.target.value as 'minutes' | 'hours' | 'days' });\n        };\n\n        return (\n          <div className=\"space-y-6\">\n            {/* --- Start Time Scheduling --- */}\n            <Card>\n              <h2 className=\"text-xl font-semibold mb-4\">Campaign Start Time</h2>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"scheduleNow\"\n                    name=\"scheduleOption\"\n                    type=\"radio\"\n                    value=\"now\"\n                    checked={sendScheduleOption === 'now'}\n                    onChange={handleScheduleOptionChange}\n                    className=\"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  />\n                  <label htmlFor=\"scheduleNow\" className=\"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Send Immediately\n                  </label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"scheduleLater\"\n                    name=\"scheduleOption\"\n                    type=\"radio\"\n                    value=\"later\"\n                    checked={sendScheduleOption === 'later'}\n                    onChange={handleScheduleOptionChange}\n                    className=\"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  />\n                  <label htmlFor=\"scheduleLater\" className=\"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Schedule for Later\n                  </label>\n                </div>\n                {sendScheduleOption === 'later' && (\n                  <div className=\"pl-7 mt-2\">\n                    <Input\n                      id=\"scheduledDateTime\"\n                      name=\"scheduledDateTime\"\n                      type=\"datetime-local\"\n                      value={scheduledDateTime}\n                      onChange={handleScheduleTimeChange}\n                      label=\"Scheduled Date & Time\"\n                      required\n                      className=\"max-w-sm\"\n                    />\n                     <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">Your local timezone.</p>\n                  </div>\n                )}\n              </div>\n            </Card>\n            \n            {/* --- Sequence Interval Timing (Conditional) --- */}\n            {activeEmailCount > 1 && (\n              <Card>\n                 <h2 className=\"text-xl font-semibold mb-4\">Email Sequence Timing</h2>\n                 <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                    Set the time interval between each subsequent email send. The first email is sent according to the 'Campaign Start Time' setting above.\n                 </p>\n                 <div className=\"flex items-center gap-4 mb-6\">\n                   <label htmlFor=\"scheduleUnit\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                     Interval Time Unit:\n                   </label>\n                   <select\n                     id=\"scheduleUnit\"\n                     name=\"scheduleUnit\"\n                     value={scheduleSettings.unit}\n                     onChange={handleUnitChange}\n                     className=\"mt-1 block w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                   >\n                     <option value=\"minutes\">Minutes</option>\n                     <option value=\"hours\">Hours</option>\n                     <option value=\"days\">Days</option>\n                   </select>\n                 </div>\n\n                 {Array.from({ length: activeEmailCount - 1 }).map((_, index) => (\n                   <div key={index} className=\"flex items-center gap-4 mt-4\">\n                     <label htmlFor={`interval-${index}`} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 w-48\">\n                       Wait before sending Email #{index + 2}:\n                     </label>\n                     <Input\n                       id={`interval-${index}`}\n                       name={`interval-${index}`}\n                       type=\"number\"\n                       value={scheduleSettings.intervals[index]?.toString() || '24'}\n                       onChange={(e) => handleIntervalChange(index, e.target.value)}\n                       className=\"w-24 mb-0\" // Adjusted margin\n                       required\n                     />\n                     <span className=\"text-gray-600 dark:text-gray-400 capitalize\">{scheduleSettings.unit}</span>\n                   </div>\n                 ))}\n               </Card>\n             )}\n             \n             {/* Informative text if only one active email */}\n             {activeEmailCount <= 1 && (\n                 <p className=\"text-gray-600 dark:text-gray-400 text-sm px-4\">\n                     Sequence timing options are available when your campaign has more than one selected email with content.\n                 </p>\n             )}\n          </div>\n        );\n      }\n      case 4:\n        // Add Recipients Step\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Add Recipients</h2>\n\n            {/* Manual Add */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Add Manually</h3>\n              <div className=\"md:flex md:items-end md:space-x-3\">\n                <div className=\"flex-grow mb-3 md:mb-0\">\n                  <Input\n                    id=\"manualRecipient\"\n                    name=\"manualRecipient\"\n                    label=\"Email Address\"\n                    type=\"email\"\n                    value={manualRecipient.email}\n                    onChange={(e) => setManualRecipient({ ...manualRecipient, email: e.target.value })}\n                    placeholder=\"Enter email address\"\n                  />\n                </div>\n                {/* Apply Coral CTA Style */}\n                <Button onClick={addManualRecipient} variant=\"secondary\" className=\"btn-cta mt-4 md:mt-0\">Add Recipient</Button>\n              </div>\n            </Card>\n\n            {/* Add from Contacts */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Add From Contacts</h3>\n              {loadingContacts ? (\n                <p>Loading contacts...</p>\n              ) : contacts.length > 0 ? (\n                <div className=\"md:flex md:items-end md:space-x-3\">\n                   <div className=\"flex-grow mb-3 md:mb-0\">\n                      <label htmlFor=\"contactSelect\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Select Contacts (use Ctrl/Cmd to select multiple)\n                      </label>\n                      <select\n                        id=\"contactSelect\"\n                        multiple\n                        value={selectedContactIds}\n                        onChange={handleContactSelection}\n                        className=\"form-input w-full h-32 border border-border rounded-md\"\n                      >\n                        {contacts.map(contact => (\n                          <option key={contact.id} value={contact.id}>\n                            {contact.name ? `${contact.name} (${contact.email})` : contact.email}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  <Button onClick={addSelectedContacts} variant=\"secondary\" className=\"btn-cta mt-4 md:mt-0\">Add Selected</Button>\n                </div>\n              ) : (\n                <p className=\"text-text-secondary\">No contacts found. You can add contacts in the 'Contacts' section.</p>\n              )}\n            </Card>\n\n            {/* Added Recipients List */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Recipients Added ({recipients.length})</h3>\n              {recipients.length > 0 ? (\n                <ul className=\"divide-y divide-border max-h-60 overflow-y-auto\">\n                  {recipients.map((rec, index) => (\n                    <li key={index} className=\"py-2 flex justify-between items-center\">\n                      <span>{rec.name ? `${rec.name} (${rec.email})` : rec.email}</span>\n                      <button onClick={() => removeRecipient(rec.email)} className=\"text-red-500 hover:text-red-700 text-sm\">&times; Remove</button>\n                    </li>\n                  ))}\n                </ul>\n              ) : (\n                <p className=\"text-text-secondary\">No recipients added yet.</p>\n              )}\n            </Card>\n          </div>\n        );\n      case 5: {\n         // Review and Schedule Step\n         const scheduledTimeString = sendScheduleOption === 'later' && scheduledDateTime\n           ? new Date(scheduledDateTime).toLocaleString()\n           : 'Immediately';\n         return (\n           <div className=\"space-y-6\">\n             <h2 className=\"text-xl font-semibold mb-4\">Review & Schedule</h2>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Campaign Details</h3>\n               <p><span className=\"font-semibold\">Name:</span> {campaignName}</p>\n               <p><span className=\"font-semibold\">Subject:</span> {subject}</p>\n               <p><span className=\"font-semibold\">From:</span> {fromName} &lt;{fromEmail}&gt;</p>\n               {replyTo && <p><span className=\"font-semibold\">Reply To:</span> {replyTo}</p>}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Sequence (Emails to Send)</h3>\n               {/* Filter based on ACTIVE and HTML content */}\n               {emailContents.filter((e, idx) => activeEmailIndices.includes(idx) && e.html.trim()).length > 0 ? (\n                 <ul className=\"list-decimal pl-5 space-y-1\">\n                   {emailContents.reduce((acc, email, index) => {\n                     // Use stricter validation: check for non-empty HTML AND active status\n                     if (activeEmailIndices.includes(index) && email.html.trim()) {\n                       const emailNumberInActiveSequence = acc.length + 1;\n                       let timingText = 'Sent at Campaign Start Time'; // Updated default text\n                       // Calculate timing based on the position in the *active* sequence\n                       if (emailNumberInActiveSequence > 1 && scheduleSettings.intervals[emailNumberInActiveSequence - 2] !== undefined) {\n                         timingText = `Sent ${scheduleSettings.intervals[emailNumberInActiveSequence - 2]} ${scheduleSettings.unit} after Email #${emailNumberInActiveSequence - 1}`;\n                       } else if (emailNumberInActiveSequence > 1) {\n                           timingText = `Timing interval missing`; // Fallback\n                       }\n                       \n                       acc.push(\n                         <li key={index}>\n                           {/* Display original email number (index + 1) but calculate timing based on active sequence */} \n                           Email {index + 1} - <span className=\"text-text-secondary\">{timingText}</span>\n                         </li>\n                       );\n                     }\n                     return acc;\n                   }, [] as React.ReactNode[])}\n                 </ul>\n               ) : (\n                 <p className=\"text-red-500\">No emails selected or have valid HTML content. Go back to Step 2.</p>\n               )}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Recipients ({recipients.length})</h3>\n               {recipients.length > 0 ? (\n                 <p>{recipients.length} contacts selected.</p>\n               ) : (\n                 <p className=\"text-red-500\">No recipients added. Go back to Step 4.</p>\n               )}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Schedule</h3>\n               <p><span className=\"font-semibold\">Send Time:</span> {scheduledTimeString}</p>\n             </Card>\n\n             <Button\n               onClick={handleCreateCampaign}\n               // Update disabled check: need at least one ACTIVE email with HTML\n               disabled={loading || recipients.length === 0 || !emailContents.some((e, idx) => activeEmailIndices.includes(idx) && e.html.trim())}\n               className=\"w-full btn-cta\"\n             >\n               {loading ? 'Creating Campaign...' : 'Create & Schedule Campaign'}\n             </Button>\n           </div>\n         );\n      }\n      default:\n        return <div>Invalid Step</div>;\n    }\n  };\n\n  // Define steps for stepper\n  const steps = ['Campaign Info', 'Email Content', 'Scheduling', 'Recipients', 'Review'];\n  // Use step state variable as currentStep\n  const currentStep = step;\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      <h1 className=\"text-3xl font-bold mb-6 text-text-primary\">Create New Campaign</h1>\n\n      {/* Stepper */}\n      <div className=\"mb-8\">\n        <ol className=\"flex items-center w-full text-sm font-medium text-center text-text-secondary sm:text-base\">\n           {steps.map((stepName, index) => (\n              <li key={stepName} className={`flex md:w-full items-center ${currentStep > index + 1 ? 'text-primary-blue' : currentStep === index + 1 ? 'text-accent-coral' : 'text-text-secondary'} ${index + 1 < steps.length ? \"after:content-['\\u00a0'] after:w-full after:h-1 after:border-b after:border-border after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700\" : \"\"}`}>\n                 <span className={`flex items-center ${index + 1 < steps.length ? \"sm:after:content-['\\u00a0'] after:hidden sm:after:inline-block after:w-4 after:h-4 after:rounded-full after:mx-auto after:mt-1\" : \"\"}`}>\n                    {currentStep > index + 1 ? (\n                       <svg className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\"><path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" /></svg>\n                    ) : (\n                       <span className=\"mr-2\">{index + 1}</span>\n                    )}\n                    {stepName}\n                 </span>\n              </li>\n           ))}\n        </ol>\n      </div>\n\n      {error && <Alert type=\"error\" message={error} onClose={() => setError('')} className=\"mb-6\" />}\n      {success && <Alert type=\"success\" message={success} />}\n\n      {/* Step Content */} \n      <div className=\"mb-8\">\n         {renderStepContent()}\n      </div>\n\n      {/* Navigation Buttons */}\n      <div className=\"flex justify-between mt-8\">\n        <Button\n          onClick={handlePrevious}\n          disabled={currentStep === 1 || loading}\n          variant=\"secondary\"\n        >\n          Back\n        </Button>\n        <Button\n          onClick={handleNext}\n          disabled={loading || (currentStep === 5) /* Disable Next on last step */}\n        >\n          {currentStep === steps.length -1 ? 'Review Campaign' : 'Next'}\n        </Button>\n      </div>\n\n      {renderTemplatePicker()}\n    </div>\n  );\n};\n\nexport default CampaignCreate;"], "mappings": ";;AAAA;;AAEA,OAAO,yBAAyB;AAEhC,OAAOA,KAAK,IACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,QACH,OAAO;AAEd,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,UAAU,MAAyB,uBAAuB;AACjE,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SACEC,WAAW,EACXC,WAAW,QACN,kBAAkB;AACzB,SAASC,6BAA6B,QAAQ,UAAU;AACxD,OAAOC,GAAG,IAAIC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA;EACrC,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,CAAAkB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI,EAAE,CAAC;EAC1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CACxC,CAAAkB,IAAI,aAAJA,IAAI,wBAAAD,YAAA,GAAJC,IAAI,CAAEY,MAAM,cAAAb,YAAA,uBAAZA,YAAA,CAAcc,MAAM,MAAK,QAAQ,GAAG,WAAWb,IAAI,CAACY,MAAM,CAACH,IAAI,EAAE,GAAG,EACtE,CAAC;EACD,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;;EAEnC;EACA,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAS,CAAC,CAAC;EAC3D,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAmC,MAAM;IACzF,MAAMgD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,wCAAwC,CAAC;IAC5E,IAAIF,KAAK,EAAE;MACT,IAAI;QACF,MAAMG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC;QAC7B,IAAIM,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,IAAIA,GAAG,CAACK,MAAM,KAAK,EAAE,EAAE;UAC3C,OAAOL,GAAG,CAACM,GAAG,CAAEC,IAAS,KAAM;YAC7BC,IAAI,EAAED,IAAI,CAACC,IAAI,IAAI,EAAE;YACrBC,IAAI,EAAEF,IAAI,CAACE,IAAI,IAAI;UACrB,CAAC,CAAC,CAAC;QACL;MACF,CAAC,CAAC,MAAM;QACN;MAAA;IAEJ;IACA,OAAON,KAAK,CAACO,IAAI,CAAC;MAAEL,MAAM,EAAE;IAAG,CAAC,EAAE,OAAO;MAAEG,IAAI,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC;EACnE,CAAC,CAAC;;EAEF;EACA,MAAM,CAACE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/D,QAAQ,CAGrD;IACDgE,SAAS,EAAE,CAAC,EAAE,CAAC;IACfC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnE,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAACoE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsE,aAAa,EAAEC,gBAAgB,CAAC,GAAGvE,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAa,IAAI,CAAC;;EAE1E;EACA,MAAM,CAAC4E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7E,QAAQ,CAAkB,OAAO,CAAC;EACtF,MAAM,CAAC8E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/E,QAAQ,CAAS,MAAM;IACvE,MAAMgF,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAClD,OAAOF,IAAI,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAwD,EAAE,CAAC;EACvG,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAC;IAAEyF,KAAK,EAAE,EAAE;IAAE9D,IAAI,EAAE;EAAG,CAAC,CAAC;EAC/E,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAG3F,QAAQ,CAAgD,EAAE,CAAC;EAC3F,MAAM,CAAC4F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7F,QAAQ,CAAW,EAAE,CAAC;EAC1E,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG/F,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMgG,SAAS,GAAGjG,MAAM,CAAgB,IAAI,CAAC;;EAE7C;EACA,MAAM,CAACkG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlG,QAAQ,CAAW,MAAM;IAC3E;IACA,MAAMmG,aAAuB,GAAG,EAAE;IAClC,MAAMnD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,wCAAwC,CAAC;IAC5E,IAAIkD,eAAe,GAAG9C,KAAK,CAACO,IAAI,CAAC;MAAEL,MAAM,EAAE;IAAG,CAAC,EAAE,OAAO;MAAEG,IAAI,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC;IAChF,IAAIZ,KAAK,EAAE;MACT,IAAI;QACF,MAAMG,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACL,KAAK,CAAC;QAC7B,IAAIM,KAAK,CAACC,OAAO,CAACJ,GAAG,CAAC,IAAIA,GAAG,CAACK,MAAM,KAAK,EAAE,EAAE;UAC3C4C,eAAe,GAAGjD,GAAG,CAACM,GAAG,CAAEC,IAAS,KAAM;YAAEC,IAAI,EAAED,IAAI,CAACC,IAAI,IAAI,EAAE;YAAEC,IAAI,EAAEF,IAAI,CAACE,IAAI,IAAI;UAAG,CAAC,CAAC,CAAC;QAC9F;MACF,CAAC,CAAC,MAAM,CAAC;IACX;IACAwC,eAAe,CAACC,OAAO,CAAC,CAACZ,KAAK,EAAEa,KAAK,KAAK;MACxC,IAAIb,KAAK,CAAC7B,IAAI,IAAI6B,KAAK,CAAC7B,IAAI,CAAC2C,IAAI,CAAC,CAAC,EAAE;QACnCJ,aAAa,CAACK,IAAI,CAACF,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC;IACF,OAAOH,aAAa;EACtB,CAAC,CAAC;;EAEF;EACA,MAAMM,WAAW,GAAG,IAAIC,eAAe,CAACvF,QAAQ,CAACwF,MAAM,CAAC;EACxD,MAAMC,UAAU,GAAGH,WAAW,CAACI,GAAG,CAAC,YAAY,CAAC;EAChD/G,SAAS,CAAC,MAAM;IACd,IAAI8G,UAAU,EAAEE,aAAa,CAACF,UAAU,CAAC;EAC3C,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAME,aAAa,GAAG,MAAOC,EAAU,IAAK;IAC1CtE,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAMuE,GAAG,GAAG,MAAMtG,6BAA6B,CAACuG,eAAe,CAACF,EAAE,CAAC;MACnE,IAAIC,GAAG,CAAC5E,OAAO,IAAI4E,GAAG,CAACE,QAAQ,EAAE;QAC/B,MAAMC,IAAI,GAAGH,GAAG,CAACE,QAAQ;QACzB,IAAI,CAAC7F,YAAY,EAAEC,eAAe,CAAC,qBAAqB6F,IAAI,CAACxF,IAAI,EAAE,CAAC;QACpE,MAAMyF,OAAO,GAAG,CAAC,GAAGtE,aAAa,CAAC;QAClCsE,OAAO,CAAC,CAAC,CAAC,GAAG;UACXzD,IAAI,EAAEwD,IAAI,CAACE,WAAW,IAAI,EAAE;UAC5BzD,IAAI,EAAEuD,IAAI,CAACG,OAAO,IAAI;QACxB,CAAC;QACDvE,gBAAgB,CAACqE,OAAO,CAAC;MAC3B,CAAC,MAAM;QACLjF,QAAQ,CAAC,0BAA0B,CAAC;MACtC;IACF,CAAC,CAAC,MAAM;MACNA,QAAQ,CAAC,0BAA0B,CAAC;IACtC,CAAC,SAAS;MACRM,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM8E,cAAc,GAAGA,CAAC5D,IAAY,EAAEC,IAAY,KAAK;IACrD4D,OAAO,CAACC,GAAG,CAAC,uCAAuC7E,YAAY,MAAM,CAAC;IACtE4E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE9D,IAAI,GAAGA,IAAI,CAAC+D,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,SAAS,CAAC;IAChFF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE7D,IAAI,GAAGA,IAAI,CAAC8D,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,SAAS,CAAC;IAChF,MAAMN,OAAO,GAAG,CAAC,GAAGtE,aAAa,CAAC;IAClC,MAAM6E,YAAY,GAAG/E,YAAY,GAAG,CAAC,CAAC,CAAC;IACvCwE,OAAO,CAACO,YAAY,CAAC,GAAG;MAAEhE,IAAI;MAAEC;IAAK,CAAC;IACtCb,gBAAgB,CAACqE,OAAO,CAAC;IACzBI,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEL,OAAO,CAAC;IACnDnE,YAAY,CAAC2E,OAAO,CAAC,wCAAwC,EAAExE,IAAI,CAACyE,SAAS,CAACT,OAAO,CAAC,CAAC;;IAEvF;IACA,IAAIxD,IAAI,IAAIA,IAAI,CAAC2C,IAAI,CAAC,CAAC,EAAE;MACvBL,qBAAqB,CAAC4B,IAAI,IAAI;QAC5B,IAAI,CAACA,IAAI,CAACC,QAAQ,CAACJ,YAAY,CAAC,EAAE;UAChC;UACA,OAAO,CAAC,GAAGG,IAAI,EAAEH,YAAY,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;QACtD;QACA,OAAOJ,IAAI,CAAC,CAAC;MACf,CAAC,CAAC;MACFN,OAAO,CAACC,GAAG,CAAC,uBAAuBE,YAAY,wBAAwB,CAAC;IAC1E,CAAC,MAAM;MACL;MACAzB,qBAAqB,CAAC4B,IAAI,IAAIA,IAAI,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKT,YAAY,CAAC,CAAC;MACnEH,OAAO,CAACC,GAAG,CAAC,2BAA2BE,YAAY,gCAAgC,CAAC;IACtF;EACF,CAAC;;EAED;EACA7H,SAAS,CAAC,MAAM;IACd,IAAIsE,kBAAkB,EAAE;MACtBK,mBAAmB,CAAC,IAAI,CAAC;MACzB/D,6BAA6B,CAAC2H,eAAe,CAAC,CAAC,CAC5CC,IAAI,CAACC,CAAC,IAAIhE,gBAAgB,CAACgE,CAAC,CAACC,IAAI,IAAI,EAAE,CAAC,CAAC,CACzCC,KAAK,CAACjB,OAAO,CAACtF,KAAK,CAAC,CACpBwG,OAAO,CAAC,MAAMjE,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC9C;EACF,CAAC,EAAE,CAACL,kBAAkB,CAAC,CAAC;EAExB,MAAMuE,oBAAoB,GAAG,MAAO5B,EAAU,IAAK;IACjDtC,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACF+C,OAAO,CAACC,GAAG,CAAC,uCAAuCV,EAAE,EAAE,CAAC;MACxD,MAAMwB,CAAC,GAAG,MAAM7H,6BAA6B,CAACuG,eAAe,CAACF,EAAE,CAAC;MACjES,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEc,CAAC,CAAC;MAChD,IAAIA,CAAC,CAACnG,OAAO,IAAImG,CAAC,CAACrB,QAAQ,EAAE;QAAA,IAAA0B,mBAAA;QAC3BpB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE;UAChDV,EAAE,EAAEwB,CAAC,CAACrB,QAAQ,CAACH,EAAE,IAAIwB,CAAC,CAACrB,QAAQ,CAAC2B,GAAG;UACnClH,IAAI,EAAE4G,CAAC,CAACrB,QAAQ,CAACvF,IAAI;UACrBmH,OAAO,EAAE,CAAC,CAACP,CAAC,CAACrB,QAAQ,CAACG,WAAW;UACjC0B,UAAU,EAAE,CAAC,CAACR,CAAC,CAACrB,QAAQ,CAACI,OAAO;UAChC0B,aAAa,EAAE,EAAAJ,mBAAA,GAAAL,CAAC,CAACrB,QAAQ,CAACI,OAAO,cAAAsB,mBAAA,uBAAlBA,mBAAA,CAAoBpF,MAAM,KAAI;QAC/C,CAAC,CAAC;MACJ;MACAmB,mBAAmB,CAAC4D,CAAC,CAACrB,QAAQ,CAAC;IACjC,CAAC,CAAC,OAAO+B,GAAG,EAAE;MACZzB,OAAO,CAACtF,KAAK,CAAC,mCAAmC,EAAE+G,GAAG,CAAC;MACvD9G,QAAQ,CAAC,mCAAmC,CAAC;IAC/C,CAAC,SAAS;MACRsC,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAMyE,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE/C,gBAAgB,GAAG;MACjEqC,EAAE,EAAErC,gBAAgB,CAACqC,EAAE,IAAIrC,gBAAgB,CAACmE,GAAG;MAC/ClH,IAAI,EAAE+C,gBAAgB,CAAC/C,IAAI;MAC3BmH,OAAO,EAAE,CAAC,CAACpE,gBAAgB,CAAC2C,WAAW;MACvC0B,UAAU,EAAE,CAAC,CAACrE,gBAAgB,CAAC4C;IACjC,CAAC,GAAG,sBAAsB,CAAC;IAE3B,IAAI5C,gBAAgB,EAAE;MACpB,MAAM0C,OAAO,GAAG,CAAC,GAAGtE,aAAa,CAAC;MAClCsE,OAAO,CAACxE,YAAY,GAAG,CAAC,CAAC,GAAG;QAC1Be,IAAI,EAAEe,gBAAgB,CAAC2C,WAAW,IAAI,EAAE;QACxCzD,IAAI,EAAEc,gBAAgB,CAAC4C,OAAO,IAAI;MACpC,CAAC;MAEDE,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAC7C0B,UAAU,EAAEvG,YAAY,GAAG,CAAC;QAC5BkG,OAAO,EAAE,CAAC,CAAC1B,OAAO,CAACxE,YAAY,GAAG,CAAC,CAAC,CAACe,IAAI;QACzCyF,OAAO,EAAE,CAAC,CAAChC,OAAO,CAACxE,YAAY,GAAG,CAAC,CAAC,CAACgB,IAAI;QACzCyF,UAAU,EAAEjC,OAAO,CAACxE,YAAY,GAAG,CAAC,CAAC,CAACe,IAAI,CAACH,MAAM;QACjD8F,UAAU,EAAElC,OAAO,CAACxE,YAAY,GAAG,CAAC,CAAC,CAACgB,IAAI,CAACJ;MAC7C,CAAC,CAAC;;MAEF;MACAT,gBAAgB,CAACqE,OAAO,CAAC;MACzBnE,YAAY,CAAC2E,OAAO,CAAC,wCAAwC,EAAExE,IAAI,CAACyE,SAAS,CAACT,OAAO,CAAC,CAAC;;MAEvF;MACAzC,mBAAmB,CAAC,IAAI,CAAC;MACzBN,qBAAqB,CAAC,KAAK,CAAC;;MAE5B;MACA;MACAkF,UAAU,CAAC,MAAM;QACf;QACA;QACA1G,eAAe,CAAC,CAAC,CAAC,CAAC;;QAEnB;QACA0G,UAAU,CAAC,MAAM;UACf1G,eAAe,CAACD,YAAY,CAAC;QAC/B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;;EAED;EACA,MAAM4G,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACnI,YAAY,IAAI,CAACE,OAAO,IAAI,CAACE,QAAQ,IAAI,CAACG,SAAS,EAAE;MACxDO,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;IACA,MAAMsH,WAAW,GAAG3G,aAAa,CAACqF,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC;IAC5D,IAAIkD,WAAW,CAACjG,MAAM,KAAK,CAAC,EAAE;MAC5BrB,QAAQ,CAAC,uDAAuD,CAAC;MACjE;IACF;IACA,IAAIkD,UAAU,CAAC7B,MAAM,KAAK,CAAC,EAAE;MAC3BrB,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEAI,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAAoH,oBAAA;MACF;MACA,MAAMC,kBAAkB,GAAG9G,aAAa,CAACqF,MAAM,CAAC,CAAC1C,KAAK,EAAEa,KAAK,KAC3DL,kBAAkB,CAAC8B,QAAQ,CAACzB,KAAK,CAAC,IAAIb,KAAK,CAAC7B,IAAI,IAAI6B,KAAK,CAAC7B,IAAI,CAAC2C,IAAI,CAAC,CACtE,CAAC;;MAED;MACA,IAAIqD,kBAAkB,CAACpG,MAAM,KAAK,CAAC,EAAE;QACnCrB,QAAQ,CAAC,mEAAmE,CAAC;QAC7EI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA,IAAI8C,UAAU,CAAC7B,MAAM,KAAK,CAAC,EAAE;QAC3BrB,QAAQ,CAAC,mCAAmC,CAAC;QAC7CI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA;;MAEA;MACA,MAAMsH,YAAiB,GAAG;QACxBlI,IAAI,EAAEN,YAAY;QAClBE,OAAO;QACPE,QAAQ;QACRG,SAAS;QACTI,OAAO,EAAEA,OAAO,IAAIJ,SAAS;QAC7BkI,SAAS,EAAElF,kBAAkB,KAAK,OAAO;QACzC,IAAIA,kBAAkB,KAAK,OAAO,IAAI;UAAEmF,YAAY,EAAE,IAAI9E,IAAI,CAACH,iBAAiB,CAAC,CAACK,WAAW,CAAC;QAAE,CAAC,CAAC;QAClG6E,aAAa,EAAE3E,UAAU;QACzB4E,MAAM,EAAE/I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,EAAE;QAChBhF,MAAM,EAAE,OAAO;QACf;QACAmI,WAAW,EAAE,EAAAP,oBAAA,GAAAC,kBAAkB,CAAC,CAAC,CAAC,cAAAD,oBAAA,uBAArBA,oBAAA,CAAuB/F,IAAI,KAAI,EAAE;QAC9C;QACAd,aAAa,EAAE8G,kBAAkB,CAACnG,GAAG,CAACiG,CAAC,KAAK;UAAE/F,IAAI,EAAE+F,CAAC,CAAC/F,IAAI;UAAEC,IAAI,EAAE8F,CAAC,CAAC9F;QAAK,CAAC,CAAC;MAC7E,CAAC;;MAED;MACA,IAAIgG,kBAAkB,CAACpG,MAAM,GAAG,CAAC,EAAE;QACjCqG,YAAY,CAACM,QAAQ,GAAG;UACtBlG,IAAI,EAAEH,gBAAgB,CAACG,IAAI;UAC3B;UACAmG,cAAc,EAAEtG,gBAAgB,CAACE,SAAS,CAACoB,KAAK,CAAC,CAAC,EAAEwE,kBAAkB,CAACpG,MAAM,GAAG,CAAC,CAAC,CAC/EC,GAAG,CAAC4G,CAAC,KAAK;YAAEC,KAAK,EAAED,CAAC;YAAEpG,IAAI,EAAEH,gBAAgB,CAACG;UAAK,CAAC,CAAC;QACzD,CAAC;MACH;MAEAuD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEoC,YAAY,CAAC;;MAErE;MACA,MAAM7C,GAAG,GAAG,MAAMpG,WAAW,CAAC2J,cAAc,CAACV,YAAY,CAAC;MAC1D;MACA,MAAMW,UAAU,GAAG,CAAAxD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE6B,GAAG,MAAI7B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAED,EAAE;MACtCS,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAET,GAAG,EAAE,eAAe,EAAEwD,UAAU,CAAC;MAE1EnI,UAAU,CAACuC,kBAAkB,KAAK,OAAO,GACrC,0BAA0B,IAAIK,IAAI,CAACH,iBAAiB,CAAC,CAAC2F,cAAc,CAAC,CAAC,EAAE,GACxE,iDAAiD,CAAC;MACtDxH,YAAY,CAACyH,UAAU,CAAC,wCAAwC,CAAC;MACjE;MACAzH,YAAY,CAACyH,UAAU,CAAC,wBAAwB,CAAC;MACjDC,cAAc,CAAC/C,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;MACjD2B,UAAU,CAAC,MAAM;QACf;QACA,IAAIiB,UAAU,EAAEpJ,QAAQ,CAAC,cAAcoJ,UAAU,UAAU,CAAC,CAAC,KACxD;UACHhD,OAAO,CAACoD,IAAI,CAAC,6DAA6D,CAAC;UAC3ExJ,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO6H,GAAQ,EAAE;MAAA,IAAA4B,aAAA,EAAAC,kBAAA;MACjB3I,QAAQ,CAAC,EAAA0I,aAAA,GAAA5B,GAAG,CAAC8B,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcrC,IAAI,cAAAsC,kBAAA,uBAAlBA,kBAAA,CAAoBE,OAAO,KAAI,2BAA2B,CAAC;IACtE,CAAC,SAAS;MACRzI,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0I,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI5J,YAAY,IAAIE,OAAO,IAAIE,QAAQ,IAAIG,SAAS,IAAII,OAAO,EAAE;MAC/DiB,YAAY,CAAC2E,OAAO,CAAC,wBAAwB,EAC3CxE,IAAI,CAACyE,SAAS,CAAC;QAAExG,YAAY;QAAEE,OAAO;QAAEE,QAAQ;QAAEG,SAAS;QAAEI;MAAQ,CAAC,CAAC,CAAC;IAC5E;IACAZ,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAEDtB,SAAS,CAAC,MAAM;IACd,MAAMoL,KAAK,GAAGjI,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC5D,IAAIgI,KAAK,EAAE;MACT,IAAI;QACF,MAAMb,CAAC,GAAGjH,IAAI,CAACC,KAAK,CAAC6H,KAAK,CAAC;QAC3B5J,eAAe,CAAC+I,CAAC,CAAChJ,YAAY,IAAI,EAAE,CAAC;QACrCG,UAAU,CAAC6I,CAAC,CAAC9I,OAAO,IAAI,EAAE,CAAC;QAC3BG,WAAW,CAAC2I,CAAC,CAAC5I,QAAQ,KAAIP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI,EAAE,CAAC;QAC3CE,YAAY,CAACwI,CAAC,CAACzI,SAAS,IAAI,EAAE,CAAC;QAC/BK,UAAU,CAACoI,CAAC,CAACrI,OAAO,IAAI,EAAE,CAAC;MAC7B,CAAC,CAAC,MAAM,CAAC;MACTiB,YAAY,CAACyH,UAAU,CAAC,wBAAwB,CAAC;IACnD;EACF,CAAC,EAAE,CAACxJ,IAAI,CAAC,CAAC;EAEV,MAAMiK,iBAAiB,GAAI/C,CAAS,IAAKvF,eAAe,CAACuF,CAAC,CAAC;EAE3DtI,SAAS,CAAC,MAAM;IACd,IAAI4C,IAAI,KAAK,CAAC,EAAE;MACd;MACA,MAAM0I,gBAAgB,GAAGtI,aAAa,CAACuI,MAAM,CAAC,CAACC,KAAK,EAAE7F,KAAK,EAAEa,KAAK,KAAK;QACrE,IAAIL,kBAAkB,CAAC8B,QAAQ,CAACzB,KAAK,CAAC,IAAIb,KAAK,CAAC7B,IAAI,IAAI6B,KAAK,CAAC7B,IAAI,CAAC2C,IAAI,CAAC,CAAC,EAAE;UACzE,OAAO+E,KAAK,GAAG,CAAC;QAClB;QACA,OAAOA,KAAK;MACd,CAAC,EAAE,CAAC,CAAC;MAEL9D,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE2D,gBAAgB,CAAC;MAExE,IAAIA,gBAAgB,GAAG,CAAC,EAAE;QACxBrH,mBAAmB,CAAC+D,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP;UACA9D,SAAS,EAAEV,KAAK,CAAC8H,gBAAgB,GAAG,CAAC,CAAC,CAACG,IAAI,CAACzD,IAAI,CAAC9D,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QACrE,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACAD,mBAAmB,CAAC+D,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE9D,SAAS,EAAE;QAAG,CAAC,CAAC,CAAC;MAC3D;IACF;EACF,CAAC,EAAE,CAACtB,IAAI,EAAEI,aAAa,EAAEmD,kBAAkB,CAAC,CAAC,CAAC,CAAC;;EAE/C,MAAMuF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI9I,IAAI,KAAK,CAAC,EAAE;MACdqD,kBAAkB,CAAC,IAAI,CAAC;MACxB,IAAI;QAAA,IAAA0F,OAAA;QACF,MAAMlD,CAAC,GAAG,MAAM5H,GAAG,CAACkG,GAAG,CAAC,WAAW,CAAC;QACpC,IAAI6E,IAAI,GAAGpI,KAAK,CAACC,OAAO,CAACgF,CAAC,CAACC,IAAI,CAAC,GAAGD,CAAC,CAACC,IAAI,GAAG,EAAAiD,OAAA,GAAAlD,CAAC,CAACC,IAAI,cAAAiD,OAAA,uBAANA,OAAA,CAAQjD,IAAI,KAAI,EAAE;QAC9D7C,WAAW,CAAC+F,IAAI,CAACjI,GAAG,CAAEkI,CAAM,KAAM;UAChC5E,EAAE,EAAE4E,CAAC,CAAC5E,EAAE,IAAI4E,CAAC,CAAC9C,GAAG,IAAI+C,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;UAC1CrG,KAAK,EAAEkG,CAAC,CAAClG,KAAK;UACd9D,IAAI,EAAEgK,CAAC,CAAChK,IAAI,IAAIgK,CAAC,CAACI,QAAQ,IAAI;QAChC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,MAAM;QACN5J,QAAQ,CAAC,yBAAyB,CAAC;MACrC,CAAC,SAAS;QACR4D,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF;EACF,CAAC;EAEDjG,SAAS,CAAC,MAAM;IACd0L,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC9I,IAAI,CAAC,CAAC;EAEV,MAAMsJ,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACzG,eAAe,CAACE,KAAK,EAAE;MAC1BtD,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,MAAM,IAAI,CAACkD,UAAU,CAAC4G,IAAI,CAAC1D,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAKF,eAAe,CAACE,KAAK,CAAC,EAAE;MACnEH,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE;QAAE0B,EAAE,EAAE,IAAI;QAAE,GAAGxB;MAAgB,CAAC,CAAC,CAAC;MAChEC,kBAAkB,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAE9D,IAAI,EAAE;MAAG,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLQ,QAAQ,CAAC,0BAA0B,CAAC;IACtC;IACAoH,UAAU,CAAC,MAAMpH,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACtC,CAAC;EAED,MAAM+J,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,KAAK,GAAGzG,QAAQ,CAACyC,MAAM,CAACwD,CAAC,IAC7B/F,kBAAkB,CAACmC,QAAQ,CAAC4D,CAAC,CAAC5E,EAAE,CAAC,IAAI,CAAC1B,UAAU,CAAC4G,IAAI,CAAC1D,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAKkG,CAAC,CAAClG,KAAK,CAChF,CAAC;IACDH,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE,GAAG8G,KAAK,CAAC,CAAC;IACxCtG,qBAAqB,CAAC,EAAE,CAAC;EAC3B,CAAC;EACD,MAAMuG,sBAAsB,GAAI1C,CAAuC,IACrE7D,qBAAqB,CAACvC,KAAK,CAACO,IAAI,CAAC6F,CAAC,CAAC2C,MAAM,CAACC,eAAe,EAAEC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EAC3E,MAAMC,eAAe,GAAIhH,KAAa,IACpCH,aAAa,CAACD,UAAU,CAAC8C,MAAM,CAACI,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAKA,KAAK,CAAC,CAAC;;EAE1D;EACA,MAAMiH,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BvK,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIO,IAAI,KAAK,CAAC,KAAK,CAACrB,YAAY,IAAI,CAACE,OAAO,IAAI,CAACE,QAAQ,IAAI,CAACG,SAAS,CAAC,EAAE;MACxEO,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;;IAEA;IACA,IAAIO,IAAI,KAAK,CAAC,EAAE;MACd8E,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,IAAIkF,aAAoD,GAAG,IAAI;MAC/D,IAAIC,mBAAmB,GAAG,KAAK;MAC/B,IAAIC,YAAY,GAAG,KAAK;;MAExB;MACA,IAAI7G,SAAS,CAAC8G,OAAO,EAAE;QACrBtF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE7E,YAAY,CAAC;QACnE;QACA+J,aAAa,GAAG,MAAM3G,SAAS,CAAC8G,OAAO,CAACC,IAAI,CAAC,CAAC;QAC9C,IAAIJ,aAAa,EAAE;UACjBnF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;UACpD;UACAD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkF,aAAa,CAAChJ,IAAI,GAAGgJ,aAAa,CAAChJ,IAAI,CAAC+D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,SAAS,CAAC;UAC3GF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEkF,aAAa,CAAC/I,IAAI,GAAG+I,aAAa,CAAC/I,IAAI,CAAC8D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,SAAS,CAAC;;UAE3G;UACA,IAAIiF,aAAa,CAAC/I,IAAI,IAAI+I,aAAa,CAAC/I,IAAI,CAAC2C,IAAI,CAAC,CAAC,EAAE;YACnDiB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnDmF,mBAAmB,GAAG,IAAI;UAC5B,CAAC,MAAM;YACLpF,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACvD;;UAEA;UACAD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxDF,cAAc,CAACoF,aAAa,CAAChJ,IAAI,EAAEgJ,aAAa,CAAC/I,IAAI,CAAC;QAExD,CAAC,MAAM;UACL;UACA4D,OAAO,CAACoD,IAAI,CAAC,kEAAkE,CAAC;QAClF;MACF,CAAC,MAAM;QACJpD,OAAO,CAACtF,KAAK,CAAC,kDAAkD,CAAC;MACpE;MACA;;MAEA;MACA;MACA;;MAEA;MACA,MAAM8K,oBAAoB,GAAGJ,mBAAmB,IAAI3G,kBAAkB,CAAC8B,QAAQ,CAACnF,YAAY,GAAG,CAAC,CAAC;MACjG,IAAIoK,oBAAoB,EAAE;QACxBxF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD;MAEAD,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;MACjF;MACAoF,YAAY,GAAG/J,aAAa,CAACmJ,IAAI,CAAC,CAACxG,KAAK,EAAEa,KAAK,KAAK;QAClD,MAAM8C,OAAO,GAAG3D,KAAK,CAAC7B,IAAI,IAAI6B,KAAK,CAAC7B,IAAI,CAAC2C,IAAI,CAAC,CAAC;QAC/C,MAAM0G,QAAQ,GAAGhH,kBAAkB,CAAC8B,QAAQ,CAACzB,KAAK,CAAC;QACnD,IAAI8C,OAAO,IAAI6D,QAAQ,EAAE;UACpBzF,OAAO,CAACC,GAAG,CAAC,eAAenB,KAAK,mCAAmC,CAAC;UACpE,OAAO,IAAI;QAChB;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MACF,IAAI,CAACuG,YAAY,EAAE;QACfrF,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MACjE;;MAEA;MACA,MAAMyF,iBAAiB,GAAGF,oBAAoB,IAAIH,YAAY;MAC9DrF,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEyF,iBAAiB,EAAE,kBAAkBF,oBAAoB,mBAAmBH,YAAY,GAAG,CAAC;MAE/I,IAAI,CAACK,iBAAiB,EAAE;QACtB/K,QAAQ,CAAC,2FAA2F,CAAC;QACrG,OAAO,CAAC;MACV;MACAqF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC9D;;IAEA;IACA,IAAI/E,IAAI,KAAK,CAAC,EAAE;MACd,MAAMyK,GAAG,GAAGrK,aAAa,CAACqF,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC,CAAC/C,MAAM;MAC3D,IAAI2J,GAAG,GAAG,CAAC,EAAE;QACX,IAAIrJ,gBAAgB,CAACE,SAAS,CAACR,MAAM,KAAK2J,GAAG,GAAG,CAAC,EAAE;UACjDhL,QAAQ,CAAC,UAAUgL,GAAG,GAAG,CAAC,aAAa,CAAC;UACxC;QACF;QACA,IAAIrJ,gBAAgB,CAACE,SAAS,CAACiI,IAAI,CAACmB,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,EAAE;UAChDjL,QAAQ,CAAC,6BAA6B,CAAC;UACvC;QACF;MACF;IACF;;IAEA;IACA,IAAIO,IAAI,KAAK,CAAC,IAAI2C,UAAU,CAAC7B,MAAM,KAAK,CAAC,EAAE;MACzCrB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;;IAEA;IACA,IAAIO,IAAI,GAAG,CAAC,EAAE;MACZ8E,OAAO,CAACC,GAAG,CAAC,wBAAwB/E,IAAI,YAAYA,IAAI,GAAG,CAAC,EAAE,CAAC;MAC/DC,OAAO,CAACD,IAAI,GAAG,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAM2K,cAAc,GAAGA,CAAA,KAAM;IAC3BlL,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAIO,IAAI,GAAG,CAAC,EAAEC,OAAO,CAACD,IAAI,GAAG,CAAC,CAAC;EACjC,CAAC;;EAED;EACA,MAAM4K,uBAAuB,GAAIhH,KAAa,IAAK;IACjD,MAAMc,OAAO,GAAG,CAAC,GAAGtE,aAAa,CAAC;IAClCsE,OAAO,CAACd,KAAK,CAAC,GAAG;MAAE3C,IAAI,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;IACvCb,gBAAgB,CAACqE,OAAO,CAAC;IACzBnE,YAAY,CAAC2E,OAAO,CAAC,wCAAwC,EAAExE,IAAI,CAACyE,SAAS,CAACT,OAAO,CAAC,CAAC;IACvF;IACAlB,qBAAqB,CAAC4B,IAAI,IAAIA,IAAI,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK9B,KAAK,CAAC,CAAC;IAC5DkB,OAAO,CAACC,GAAG,CAAC,+CAA+CnB,KAAK,EAAE,CAAC;EACrE,CAAC;EACD;;EAEA,MAAMiH,oBAAoB,GAAGA,CAAA,kBAC3BzM,OAAA,CAACR,KAAK;IAACkN,MAAM,EAAEpJ,kBAAmB;IAACqJ,OAAO,EAAEA,CAAA,KAAMpJ,qBAAqB,CAAC,KAAK,CAAE;IAACqJ,KAAK,EAAC,iBAAiB;IAAAC,QAAA,gBACrG7M,OAAA;MAAK8M,SAAS,EAAC,wEAAwE;MAAAD,QAAA,EACpFnJ,gBAAgB,gBACb1D,OAAA;QAAA6M,QAAA,EAAG;MAAU;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GACjB1J,aAAa,CAACb,GAAG,CAACwK,CAAC,iBACnBnN,OAAA,CAACX,IAAI;QAEHyN,SAAS,EAAE,kBAAkB,CAAAlJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqC,EAAE,MAAKkH,CAAC,CAAClH,EAAE,GAAG,qBAAqB,GAAG,EAAE,EAAG;QAC1FmH,OAAO,EAAEA,CAAA,KAAMvF,oBAAoB,CAACsF,CAAC,CAAClH,EAAE,CAAE;QAAA4G,QAAA,gBAE1C7M,OAAA;UAAI8M,SAAS,EAAC,6BAA6B;UAAAD,QAAA,EAAEM,CAAC,CAACtM;QAAI;UAAAkM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzDlN,OAAA;UAAK8M,SAAS,EAAC,0FAA0F;UAAAD,QAAA,EACtGM,CAAC,CAACE,YAAY,gBAAGrN,OAAA;YAAKsN,GAAG,EAAEH,CAAC,CAACE,YAAa;YAACE,GAAG,EAAEJ,CAAC,CAACtM,IAAK;YAACiM,SAAS,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChH,CAAC;MAAA,GAPDC,CAAC,CAAClH,EAAE;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQL,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAED,CAAC,eACNlN,OAAA;MAAK8M,SAAS,EAAC,6BAA6B;MAAAD,QAAA,gBAC1C7M,OAAA,CAACZ,MAAM;QAACoO,OAAO,EAAC,WAAW;QAACJ,OAAO,EAAEA,CAAA,KAAM7J,qBAAqB,CAAC,KAAK,CAAE;QAAAsJ,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxFlN,OAAA,CAACZ,MAAM;QAACgO,OAAO,EAAEhF,iBAAkB;QAACqF,QAAQ,EAAE,CAAC7J,gBAAgB,IAAIF,gBAAiB;QAAAmJ,QAAA,EAAC;MAErF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACR;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,aAAA,EAAAC,aAAA;IAC9B,QAAQhM,IAAI;MACV,KAAK,CAAC;QAAE,oBACN5B,OAAA;UAAK8M,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB7M,OAAA,CAACV,KAAK;YAAC2G,EAAE,EAAC,cAAc;YAACpF,IAAI,EAAC,cAAc;YAACgN,KAAK,EAAC,eAAe;YAACnC,KAAK,EAAEnL,YAAa;YAACuN,QAAQ,EAAElF,CAAC,IAAIpI,eAAe,CAACoI,CAAC,CAAC2C,MAAM,CAACG,KAAK,CAAE;YAACqC,QAAQ;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnJlN,OAAA,CAACV,KAAK;YAAC2G,EAAE,EAAC,SAAS;YAACpF,IAAI,EAAC,SAAS;YAACgN,KAAK,EAAC,eAAe;YAACnC,KAAK,EAAEjL,OAAQ;YAACqN,QAAQ,EAAElF,CAAC,IAAIlI,UAAU,CAACkI,CAAC,CAAC2C,MAAM,CAACG,KAAK,CAAE;YAACqC,QAAQ;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/HlN,OAAA;YAAK8M,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpD7M,OAAA,CAACV,KAAK;cAAC2G,EAAE,EAAC,UAAU;cAACpF,IAAI,EAAC,UAAU;cAACgN,KAAK,EAAC,WAAW;cAACnC,KAAK,EAAE/K,QAAS;cAACmN,QAAQ,EAAElF,CAAC,IAAIhI,WAAW,CAACgI,CAAC,CAAC2C,MAAM,CAACG,KAAK,CAAE;cAACqC,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/HlN,OAAA,CAACV,KAAK;cACJ2G,EAAE,EAAC,WAAW;cACdpF,IAAI,EAAC,WAAW;cAChBgN,KAAK,EAAC,YAAY;cAClBG,IAAI,EAAC,OAAO;cACZtC,KAAK,EAAE5K,SAAU;cACjBgN,QAAQ,EAAElF,CAAC,IAAI7H,YAAY,CAAC6H,CAAC,CAAC2C,MAAM,CAACG,KAAK,CAAE;cAC5C+B,QAAQ,EAAE,CAAArN,IAAI,aAAJA,IAAI,wBAAAuN,aAAA,GAAJvN,IAAI,CAAEY,MAAM,cAAA2M,aAAA,uBAAZA,aAAA,CAAc1M,MAAM,MAAK,QAAS;cAC5C8M,QAAQ;cACRE,QAAQ,EAAE,CAAA7N,IAAI,aAAJA,IAAI,wBAAAwN,aAAA,GAAJxN,IAAI,CAAEY,MAAM,cAAA4M,aAAA,uBAAZA,aAAA,CAAc3M,MAAM,MAAK,QAAQ,GAAG,0BAA0Bb,IAAI,CAACY,MAAM,CAACH,IAAI,EAAE,GAAG;YAAoC;cAAAkM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlN,OAAA,CAACV,KAAK;YAAC2G,EAAE,EAAC,SAAS;YAACpF,IAAI,EAAC,SAAS;YAACgN,KAAK,EAAC,2BAA2B;YAACG,IAAI,EAAC,OAAO;YAACtC,KAAK,EAAExK,OAAQ;YAAC4M,QAAQ,EAAElF,CAAC,IAAIzH,UAAU,CAACyH,CAAC,CAAC2C,MAAM,CAACG,KAAK;UAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5I,CAAC;MAER,KAAK,CAAC;QACJ,MAAMgB,IAAI,GAAGlM,aAAa,CAACF,YAAY,GAAG,CAAC,CAAC;QAC5C;QACA,MAAMqM,iBAAiB,GAAGnM,aAAa,CAACqF,MAAM,CAACuB,CAAC,IAAIA,CAAC,CAAC9F,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC,CAAC/C,MAAM;QAEzE,oBACE1C,OAAA;UAAK8M,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBAErD7M,OAAA;YAAK8M,SAAS,EAAC,qCAAqC;YAAAD,QAAA,gBAClD7M,OAAA;cAAI8M,SAAS,EAAC,oBAAoB;cAAAD,QAAA,EAAC;YAA0C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACjF1K,KAAK,CAACO,IAAI,CAAC;cAAEL,MAAM,EAAE;YAAG,CAAC,CAAC,CAACC,GAAG,CAAC,CAACyL,CAAC,EAAEC,GAAG,KAAK;cAC1C,MAAMC,cAAc,GAAG,CAAC,EAAEtM,aAAa,CAACqM,GAAG,CAAC,CAACvL,IAAI,IAAId,aAAa,CAACqM,GAAG,CAAC,CAACvL,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC;cACpF,MAAM8I,UAAU,GAAGzM,YAAY,KAAKuM,GAAG,GAAG,CAAC;cAC3C,MAAMlC,QAAQ,GAAGhH,kBAAkB,CAAC8B,QAAQ,CAACoH,GAAG,CAAC;;cAEjD;cACA,IAAIG,aAAsC,GAAG,WAAW;cACxD,IAAIC,aAAa,GAAG,EAAE;cACtB,IAAIC,UAAU,GAAG,SAASL,GAAG,GAAG,CAAC,EAAE;cACnC,IAAIM,IAAI,GAAG,IAAI;cAEf,IAAIL,cAAc,EAAE;gBAClB,IAAInC,QAAQ,EAAE;kBACZqC,aAAa,GAAG,SAAS,CAAC,CAAC;kBAC3BG,IAAI,gBAAG3O,OAAA;oBAAM8M,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;kBACnFwB,UAAU,IAAI,YAAY;gBAC5B,CAAC,MAAM;kBACLF,aAAa,GAAG,WAAW,CAAC,CAAC;kBAC7BC,aAAa,GAAG,wGAAwG,CAAC,CAAC;kBAC1HE,IAAI,gBAAG3O,OAAA;oBAAM8M,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,CAAC,CAAC;kBACnFwB,UAAU,IAAI,aAAa;gBAC7B;cACF,CAAC,MAAM;gBACJ;gBACA;gBACAD,aAAa,GAAG,sEAAsE;gBACtFC,UAAU,IAAI,+BAA+B;cAChD;cAEA,MAAME,kBAAkB,GAAGL,UAAU,GAAG,kEAAkE,GAAG,EAAE;cAE/G,oBACEvO,OAAA,CAACZ,MAAM;gBAELoO,OAAO,EAAEgB,aAAc,CAAC;gBAAA;gBACxBpB,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACArL,eAAe,CAACsM,GAAG,GAAG,CAAC,CAAC;;kBAExB;kBACA,IAAIC,cAAc,EAAE;oBAClBlJ,qBAAqB,CAAC4B,IAAI,IACxBA,IAAI,CAACC,QAAQ,CAACoH,GAAG,CAAC,GAAGrH,IAAI,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK+G,GAAG,CAAC,GAAG,CAAC,GAAGrH,IAAI,EAAEqH,GAAG,CAClE,CAAC;kBACH;gBACF,CAAE;gBACFQ,IAAI,EAAC,IAAI;gBACT/B,SAAS,EAAE,sDAAsD8B,kBAAkB,IAAIH,aAAa,EAAG;gBACvG7B,KAAK,EAAE0B,cAAc,GAAInC,QAAQ,GAAG,+BAA+B,GAAG,6BAA6B,GAAI,0BAA2B;gBAAAU,QAAA,gBAElI7M,OAAA;kBAAM8M,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,EAAE6B;gBAAU;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvDyB,IAAI;cAAA,GAlBAN,GAAG;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBF,CAAC;YAEb,CAAC,CAAC,eAGFlN,OAAA;cAAK8M,SAAS,EAAC,oEAAoE;cAAAD,QAAA,gBACjF7M,OAAA;gBAAG8M,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxDlN,OAAA;gBAAG8M,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9B7M,OAAA;kBAAM8M,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjFlN,OAAA;kBAAM8M,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eACJlN,OAAA;gBAAG8M,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9B7M,OAAA;kBAAM8M,SAAS,EAAC;gBAAqE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7FlN,OAAA;kBAAM8M,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,GAAC,eAAa,eAAA7M,OAAA;oBAAM8M,SAAS,EAAC,WAAW;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC,eACJlN,OAAA;gBAAG8M,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9B7M,OAAA;kBAAM8M,SAAS,EAAC;gBAA6G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrIlN,OAAA;kBAAM8M,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACJlN,OAAA;gBAAG8M,SAAS,EAAC,+CAA+C;gBAAAD,QAAA,EAAC;cAAuE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtI,CAAC,eAENlN,OAAA;cAAK8M,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB7M,OAAA,CAACZ,MAAM;gBAACoO,OAAO,EAAC,WAAW;gBAACJ,OAAO,EAAEA,CAAA,KAAM7J,qBAAqB,CAAC,IAAI,CAAE;gBAAAsJ,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChGlN,OAAA,CAACZ,MAAM;gBAACoO,OAAO,EAAC,WAAW;gBAACJ,OAAO,EAAEjD,qBAAsB;gBAAC2C,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlN,OAAA;YAAK8M,SAAS,EAAC,+BAA+B;YAAAD,QAAA,gBAC5C7M,OAAA,CAACT,UAAU;cAETuP,GAAG,EAAE5J,SAAU;cACf6J,WAAW,EAAEb,IAAI,CAACrL,IAAK;cACvBmM,MAAM,EAAEvI,cAAe;cACvBwI,MAAM,EAAC;YAAM,GAJRnN,YAAY;cAAAiL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKlB,CAAC,eACFlN,OAAA;cAAG8M,SAAS,EAAC,+CAA+C;cAAAD,QAAA,EAAC;YAE7D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QAAE;UACN;UACA,MAAM5C,gBAAgB,GAAGtI,aAAa,CAACuI,MAAM,CAAC,CAACC,KAAK,EAAE7F,KAAK,EAAEa,KAAK,KAAK;YACrE,IAAIL,kBAAkB,CAAC8B,QAAQ,CAACzB,KAAK,CAAC,IAAIb,KAAK,CAAC7B,IAAI,IAAI6B,KAAK,CAAC7B,IAAI,CAAC2C,IAAI,CAAC,CAAC,EAAE;cACzE,OAAO+E,KAAK,GAAG,CAAC;YAClB;YACA,OAAOA,KAAK;UACd,CAAC,EAAE,CAAC,CAAC;UACL9D,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE2D,gBAAgB,CAAC;;UAE9E;UACA,MAAM4E,0BAA0B,GAAItG,CAAsC,IAAK;YAC7E7E,qBAAqB,CAAC6E,CAAC,CAAC2C,MAAM,CAACG,KAAwB,CAAC;UAC1D,CAAC;UACD,MAAMyD,wBAAwB,GAAIvG,CAAsC,IAAK;YAC3E3E,oBAAoB,CAAC2E,CAAC,CAAC2C,MAAM,CAACG,KAAK,CAAC;UACtC,CAAC;;UAED;UACA,MAAM0D,oBAAoB,GAAGA,CAAC5J,KAAa,EAAEkG,KAAa,KAAK;YAC7D,MAAM2D,QAAQ,GAAGC,QAAQ,CAAC5D,KAAK,EAAE,EAAE,CAAC;YACpC,IAAI,CAAC6D,KAAK,CAACF,QAAQ,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;cACpC,MAAMG,YAAY,GAAG,CAAC,GAAGxM,gBAAgB,CAACE,SAAS,CAAC;cACpDsM,YAAY,CAAChK,KAAK,CAAC,GAAG6J,QAAQ;cAC9BpM,mBAAmB,CAAC;gBAAE,GAAGD,gBAAgB;gBAAEE,SAAS,EAAEsM;cAAa,CAAC,CAAC;YACvE;UACF,CAAC;UACD,MAAMC,gBAAgB,GAAI7G,CAAuC,IAAK;YACpE3F,mBAAmB,CAAC;cAAE,GAAGD,gBAAgB;cAAEG,IAAI,EAAEyF,CAAC,CAAC2C,MAAM,CAACG;YAAsC,CAAC,CAAC;UACpG,CAAC;UAED,oBACE1L,OAAA;YAAK8M,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAExB7M,OAAA,CAACX,IAAI;cAAAwN,QAAA,gBACH7M,OAAA;gBAAI8M,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnElN,OAAA;gBAAK8M,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxB7M,OAAA;kBAAK8M,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChC7M,OAAA;oBACEiG,EAAE,EAAC,aAAa;oBAChBpF,IAAI,EAAC,gBAAgB;oBACrBmN,IAAI,EAAC,OAAO;oBACZtC,KAAK,EAAC,KAAK;oBACXgE,OAAO,EAAE5L,kBAAkB,KAAK,KAAM;oBACtCgK,QAAQ,EAAEoB,0BAA2B;oBACrCpC,SAAS,EAAC;kBAA+D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACFlN,OAAA;oBAAO2P,OAAO,EAAC,aAAa;oBAAC7C,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAEzG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNlN,OAAA;kBAAK8M,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChC7M,OAAA;oBACEiG,EAAE,EAAC,eAAe;oBAClBpF,IAAI,EAAC,gBAAgB;oBACrBmN,IAAI,EAAC,OAAO;oBACZtC,KAAK,EAAC,OAAO;oBACbgE,OAAO,EAAE5L,kBAAkB,KAAK,OAAQ;oBACxCgK,QAAQ,EAAEoB,0BAA2B;oBACrCpC,SAAS,EAAC;kBAA+D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACFlN,OAAA;oBAAO2P,OAAO,EAAC,eAAe;oBAAC7C,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAE3G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACLpJ,kBAAkB,KAAK,OAAO,iBAC7B9D,OAAA;kBAAK8M,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxB7M,OAAA,CAACV,KAAK;oBACJ2G,EAAE,EAAC,mBAAmB;oBACtBpF,IAAI,EAAC,mBAAmB;oBACxBmN,IAAI,EAAC,gBAAgB;oBACrBtC,KAAK,EAAE1H,iBAAkB;oBACzB8J,QAAQ,EAAEqB,wBAAyB;oBACnCtB,KAAK,EAAC,uBAAuB;oBAC7BE,QAAQ;oBACRjB,SAAS,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACDlN,OAAA;oBAAG8M,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAGN5C,gBAAgB,GAAG,CAAC,iBACnBtK,OAAA,CAACX,IAAI;cAAAwN,QAAA,gBACF7M,OAAA;gBAAI8M,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrElN,OAAA;gBAAG8M,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJlN,OAAA;gBAAK8M,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3C7M,OAAA;kBAAO2P,OAAO,EAAC,cAAc;kBAAC7C,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,EAAC;gBAErG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlN,OAAA;kBACEiG,EAAE,EAAC,cAAc;kBACjBpF,IAAI,EAAC,cAAc;kBACnB6K,KAAK,EAAE1I,gBAAgB,CAACG,IAAK;kBAC7B2K,QAAQ,EAAE2B,gBAAiB;kBAC3B3C,SAAS,EAAC,0MAA0M;kBAAAD,QAAA,gBAEpN7M,OAAA;oBAAQ0L,KAAK,EAAC,SAAS;oBAAAmB,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxClN,OAAA;oBAAQ0L,KAAK,EAAC,OAAO;oBAAAmB,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClN,OAAA;oBAAQ0L,KAAK,EAAC,MAAM;oBAAAmB,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAEL1K,KAAK,CAACO,IAAI,CAAC;gBAAEL,MAAM,EAAE4H,gBAAgB,GAAG;cAAE,CAAC,CAAC,CAAC3H,GAAG,CAAC,CAACyL,CAAC,EAAE5I,KAAK;gBAAA,IAAAoK,qBAAA;gBAAA,oBACzD5P,OAAA;kBAAiB8M,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,gBACvD7M,OAAA;oBAAO2P,OAAO,EAAE,YAAYnK,KAAK,EAAG;oBAACsH,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,GAAC,6BACpF,EAACrH,KAAK,GAAG,CAAC,EAAC,GACxC;kBAAA;oBAAAuH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRlN,OAAA,CAACV,KAAK;oBACJ2G,EAAE,EAAE,YAAYT,KAAK,EAAG;oBACxB3E,IAAI,EAAE,YAAY2E,KAAK,EAAG;oBAC1BwI,IAAI,EAAC,QAAQ;oBACbtC,KAAK,EAAE,EAAAkE,qBAAA,GAAA5M,gBAAgB,CAACE,SAAS,CAACsC,KAAK,CAAC,cAAAoK,qBAAA,uBAAjCA,qBAAA,CAAmCC,QAAQ,CAAC,CAAC,KAAI,IAAK;oBAC7D/B,QAAQ,EAAGlF,CAAC,IAAKwG,oBAAoB,CAAC5J,KAAK,EAAEoD,CAAC,CAAC2C,MAAM,CAACG,KAAK,CAAE;oBAC7DoB,SAAS,EAAC,WAAW,CAAC;oBAAA;oBACtBiB,QAAQ;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFlN,OAAA;oBAAM8M,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,EAAE7J,gBAAgB,CAACG;kBAAI;oBAAA4J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAbpF1H,KAAK;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcV,CAAC;cAAA,CACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,EAGA5C,gBAAgB,IAAI,CAAC,iBAClBtK,OAAA;cAAG8M,SAAS,EAAC,+CAA+C;cAAAD,QAAA,EAAC;YAE7D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAEV;MACA,KAAK,CAAC;QACJ;QACA,oBACElN,OAAA;UAAK8M,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxB7M,OAAA;YAAI8M,SAAS,EAAC,4BAA4B;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAG9DlN,OAAA,CAACX,IAAI;YAAAwN,QAAA,gBACH7M,OAAA;cAAI8M,SAAS,EAAC,4CAA4C;cAAAD,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5ElN,OAAA;cAAK8M,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChD7M,OAAA;gBAAK8M,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,eACrC7M,OAAA,CAACV,KAAK;kBACJ2G,EAAE,EAAC,iBAAiB;kBACpBpF,IAAI,EAAC,iBAAiB;kBACtBgN,KAAK,EAAC,eAAe;kBACrBG,IAAI,EAAC,OAAO;kBACZtC,KAAK,EAAEjH,eAAe,CAACE,KAAM;kBAC7BmJ,QAAQ,EAAGlF,CAAC,IAAKlE,kBAAkB,CAAC;oBAAE,GAAGD,eAAe;oBAAEE,KAAK,EAAEiE,CAAC,CAAC2C,MAAM,CAACG;kBAAM,CAAC,CAAE;kBACnFoE,WAAW,EAAC;gBAAqB;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlN,OAAA,CAACZ,MAAM;gBAACgO,OAAO,EAAElC,kBAAmB;gBAACsC,OAAO,EAAC,WAAW;gBAACV,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPlN,OAAA,CAACX,IAAI;YAAAwN,QAAA,gBACH7M,OAAA;cAAI8M,SAAS,EAAC,4CAA4C;cAAAD,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChFlI,eAAe,gBACdhF,OAAA;cAAA6M,QAAA,EAAG;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GACxBtI,QAAQ,CAAClC,MAAM,GAAG,CAAC,gBACrB1C,OAAA;cAAK8M,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAC/C7M,OAAA;gBAAK8M,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACpC7M,OAAA;kBAAO2P,OAAO,EAAC,eAAe;kBAAC7C,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,EAAC;gBAE9F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRlN,OAAA;kBACEiG,EAAE,EAAC,eAAe;kBAClB8J,QAAQ;kBACRrE,KAAK,EAAE5G,kBAAmB;kBAC1BgJ,QAAQ,EAAExC,sBAAuB;kBACjCwB,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EAEjEjI,QAAQ,CAACjC,GAAG,CAACqN,OAAO,iBACnBhQ,OAAA;oBAAyB0L,KAAK,EAAEsE,OAAO,CAAC/J,EAAG;oBAAA4G,QAAA,EACxCmD,OAAO,CAACnP,IAAI,GAAG,GAAGmP,OAAO,CAACnP,IAAI,KAAKmP,OAAO,CAACrL,KAAK,GAAG,GAAGqL,OAAO,CAACrL;kBAAK,GADzDqL,OAAO,CAAC/J,EAAE;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACRlN,OAAA,CAACZ,MAAM;gBAACgO,OAAO,EAAEhC,mBAAoB;gBAACoC,OAAO,EAAC,WAAW;gBAACV,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7G,CAAC,gBAENlN,OAAA;cAAG8M,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAAC;YAAkE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACzG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGPlN,OAAA,CAACX,IAAI;YAAAwN,QAAA,gBACH7M,OAAA;cAAI8M,SAAS,EAAC,4CAA4C;cAAAD,QAAA,GAAC,oBAAkB,EAACtI,UAAU,CAAC7B,MAAM,EAAC,GAAC;YAAA;cAAAqK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrG3I,UAAU,CAAC7B,MAAM,GAAG,CAAC,gBACpB1C,OAAA;cAAI8M,SAAS,EAAC,iDAAiD;cAAAD,QAAA,EAC5DtI,UAAU,CAAC5B,GAAG,CAAC,CAACsN,GAAG,EAAEzK,KAAK,kBACzBxF,OAAA;gBAAgB8M,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,gBAChE7M,OAAA;kBAAA6M,QAAA,EAAOoD,GAAG,CAACpP,IAAI,GAAG,GAAGoP,GAAG,CAACpP,IAAI,KAAKoP,GAAG,CAACtL,KAAK,GAAG,GAAGsL,GAAG,CAACtL;gBAAK;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClElN,OAAA;kBAAQoN,OAAO,EAAEA,CAAA,KAAMzB,eAAe,CAACsE,GAAG,CAACtL,KAAK,CAAE;kBAACmI,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAFvH1H,KAAK;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAELlN,OAAA;cAAG8M,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC/D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAEV,KAAK,CAAC;QAAE;UACL;UACA,MAAMgD,mBAAmB,GAAGpM,kBAAkB,KAAK,OAAO,IAAIE,iBAAiB,GAC3E,IAAIG,IAAI,CAACH,iBAAiB,CAAC,CAAC2F,cAAc,CAAC,CAAC,GAC5C,aAAa;UACjB,oBACE3J,OAAA;YAAK8M,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxB7M,OAAA;cAAI8M,SAAS,EAAC,4BAA4B;cAAAD,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEjElN,OAAA,CAACX,IAAI;cAAAwN,QAAA,gBACH7M,OAAA;gBAAI8M,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFlN,OAAA;gBAAA6M,QAAA,gBAAG7M,OAAA;kBAAM8M,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC3M,YAAY;cAAA;gBAAAwM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClElN,OAAA;gBAAA6M,QAAA,gBAAG7M,OAAA;kBAAM8M,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACzM,OAAO;cAAA;gBAAAsM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChElN,OAAA;gBAAA6M,QAAA,gBAAG7M,OAAA;kBAAM8M,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACvM,QAAQ,EAAC,IAAK,EAACG,SAAS,EAAC,GAAI;cAAA;gBAAAiM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjFhM,OAAO,iBAAIlB,OAAA;gBAAA6M,QAAA,gBAAG7M,OAAA;kBAAM8M,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAChM,OAAO;cAAA;gBAAA6L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eAEPlN,OAAA,CAACX,IAAI;cAAAwN,QAAA,gBACH7M,OAAA;gBAAI8M,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAExFlL,aAAa,CAACqF,MAAM,CAAC,CAACuB,CAAC,EAAEyF,GAAG,KAAKlJ,kBAAkB,CAAC8B,QAAQ,CAACoH,GAAG,CAAC,IAAIzF,CAAC,CAAC9F,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAC,CAAC/C,MAAM,GAAG,CAAC,gBAC7F1C,OAAA;gBAAI8M,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EACxC7K,aAAa,CAACuI,MAAM,CAAC,CAAC4F,GAAG,EAAExL,KAAK,EAAEa,KAAK,KAAK;kBAC3C;kBACA,IAAIL,kBAAkB,CAAC8B,QAAQ,CAACzB,KAAK,CAAC,IAAIb,KAAK,CAAC7B,IAAI,CAAC2C,IAAI,CAAC,CAAC,EAAE;oBAC3D,MAAM2K,2BAA2B,GAAGD,GAAG,CAACzN,MAAM,GAAG,CAAC;oBAClD,IAAI2N,UAAU,GAAG,6BAA6B,CAAC,CAAC;oBAChD;oBACA,IAAID,2BAA2B,GAAG,CAAC,IAAIpN,gBAAgB,CAACE,SAAS,CAACkN,2BAA2B,GAAG,CAAC,CAAC,KAAKE,SAAS,EAAE;sBAChHD,UAAU,GAAG,QAAQrN,gBAAgB,CAACE,SAAS,CAACkN,2BAA2B,GAAG,CAAC,CAAC,IAAIpN,gBAAgB,CAACG,IAAI,iBAAiBiN,2BAA2B,GAAG,CAAC,EAAE;oBAC7J,CAAC,MAAM,IAAIA,2BAA2B,GAAG,CAAC,EAAE;sBACxCC,UAAU,GAAG,yBAAyB,CAAC,CAAC;oBAC5C;oBAEAF,GAAG,CAACzK,IAAI,cACN1F,OAAA;sBAAA6M,QAAA,GACiG,QACzF,EAACrH,KAAK,GAAG,CAAC,EAAC,KAAG,eAAAxF,OAAA;wBAAM8M,SAAS,EAAC,qBAAqB;wBAAAD,QAAA,EAAEwD;sBAAU;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA,GAFtE1H,KAAK;sBAAAuH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAGV,CACN,CAAC;kBACH;kBACA,OAAOiD,GAAG;gBACZ,CAAC,EAAE,EAAuB;cAAC;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,gBAELlN,OAAA;gBAAG8M,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAiE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACjG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAEPlN,OAAA,CAACX,IAAI;cAAAwN,QAAA,gBACH7M,OAAA;gBAAI8M,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,GAAC,cAAY,EAACtI,UAAU,CAAC7B,MAAM,EAAC,GAAC;cAAA;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAC/F3I,UAAU,CAAC7B,MAAM,GAAG,CAAC,gBACpB1C,OAAA;gBAAA6M,QAAA,GAAItI,UAAU,CAAC7B,MAAM,EAAC,qBAAmB;cAAA;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,gBAE7ClN,OAAA;gBAAG8M,SAAS,EAAC,cAAc;gBAAAD,QAAA,EAAC;cAAuC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACvE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAEPlN,OAAA,CAACX,IAAI;cAAAwN,QAAA,gBACH7M,OAAA;gBAAI8M,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxElN,OAAA;gBAAA6M,QAAA,gBAAG7M,OAAA;kBAAM8M,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACgD,mBAAmB;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eAEPlN,OAAA,CAACZ,MAAM;cACLgO,OAAO,EAAE1E;cACT;cAAA;cACA+E,QAAQ,EAAEjM,OAAO,IAAI+C,UAAU,CAAC7B,MAAM,KAAK,CAAC,IAAI,CAACV,aAAa,CAACmJ,IAAI,CAAC,CAACvC,CAAC,EAAEyF,GAAG,KAAKlJ,kBAAkB,CAAC8B,QAAQ,CAACoH,GAAG,CAAC,IAAIzF,CAAC,CAAC9F,IAAI,CAAC2C,IAAI,CAAC,CAAC,CAAE;cACnIqH,SAAS,EAAC,gBAAgB;cAAAD,QAAA,EAEzBrL,OAAO,GAAG,sBAAsB,GAAG;YAA4B;cAAAuL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAEX;MACA;QACE,oBAAOlN,OAAA;UAAA6M,QAAA,EAAK;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAMqD,KAAK,GAAG,CAAC,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,CAAC;EACtF;EACA,MAAMC,WAAW,GAAG5O,IAAI;EAExB,oBACE5B,OAAA;IAAK8M,SAAS,EAAC,6BAA6B;IAAAD,QAAA,gBAC1C7M,OAAA;MAAI8M,SAAS,EAAC,2CAA2C;MAAAD,QAAA,EAAC;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAGlFlN,OAAA;MAAK8M,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnB7M,OAAA;QAAI8M,SAAS,EAAC,2FAA2F;QAAAD,QAAA,EACrG0D,KAAK,CAAC5N,GAAG,CAAC,CAAC8N,QAAQ,EAAEjL,KAAK,kBACxBxF,OAAA;UAAmB8M,SAAS,EAAE,+BAA+B0D,WAAW,GAAGhL,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAGgL,WAAW,KAAKhL,KAAK,GAAG,CAAC,GAAG,mBAAmB,GAAG,qBAAqB,IAAIA,KAAK,GAAG,CAAC,GAAG+K,KAAK,CAAC7N,MAAM,GAAG,2LAA2L,GAAG,EAAE,EAAG;UAAAmK,QAAA,eACnZ7M,OAAA;YAAM8M,SAAS,EAAE,qBAAqBtH,KAAK,GAAG,CAAC,GAAG+K,KAAK,CAAC7N,MAAM,GAAG,gIAAgI,GAAG,EAAE,EAAG;YAAAmK,QAAA,GACrM2D,WAAW,GAAGhL,KAAK,GAAG,CAAC,gBACrBxF,OAAA;cAAK8M,SAAS,EAAC,8BAA8B;cAACrC,IAAI,EAAC,cAAc;cAACiG,OAAO,EAAC,WAAW;cAAA7D,QAAA,eAAC7M,OAAA;gBAAM2Q,QAAQ,EAAC,SAAS;gBAACpH,CAAC,EAAC,uIAAuI;gBAACqH,QAAQ,EAAC;cAAS;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEpRlN,OAAA;cAAM8M,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAErH,KAAK,GAAG;YAAC;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAC1C,EACAuD,QAAQ;UAAA;YAAA1D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC,GARDuD,QAAQ;UAAA1D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASb,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEL9L,KAAK,iBAAIpB,OAAA,CAACb,KAAK;MAAC6O,IAAI,EAAC,OAAO;MAAC9D,OAAO,EAAE9I,KAAM;MAACuL,OAAO,EAAEA,CAAA,KAAMtL,QAAQ,CAAC,EAAE,CAAE;MAACyL,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC7F5L,OAAO,iBAAItB,OAAA,CAACb,KAAK;MAAC6O,IAAI,EAAC,SAAS;MAAC9D,OAAO,EAAE5I;IAAQ;MAAAyL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGtDlN,OAAA;MAAK8M,SAAS,EAAC,MAAM;MAAAD,QAAA,EACjBa,iBAAiB,CAAC;IAAC;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGNlN,OAAA;MAAK8M,SAAS,EAAC,2BAA2B;MAAAD,QAAA,gBACxC7M,OAAA,CAACZ,MAAM;QACLgO,OAAO,EAAEb,cAAe;QACxBkB,QAAQ,EAAE+C,WAAW,KAAK,CAAC,IAAIhP,OAAQ;QACvCgM,OAAO,EAAC,WAAW;QAAAX,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlN,OAAA,CAACZ,MAAM;QACLgO,OAAO,EAAExB,UAAW;QACpB6B,QAAQ,EAAEjM,OAAO,IAAKgP,WAAW,KAAK,CAAE,CAAC,+BAAgC;QAAA3D,QAAA,EAExE2D,WAAW,KAAKD,KAAK,CAAC7N,MAAM,GAAE,CAAC,GAAG,iBAAiB,GAAG;MAAM;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELT,oBAAoB,CAAC,CAAC;EAAA;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEV,CAAC;AAAChN,EAAA,CAjiCID,cAAwB;EAAA,QACXR,OAAO,EACPC,WAAW,EACXC,WAAW;AAAA;AAAAkR,EAAA,GAHxB5Q,cAAwB;AAmiC9B,eAAeA,cAAc;AAAC,IAAA4Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}