"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[764],{4764:(e,a,n)=>{n.r(a),n.d(a,{default:()=>m});var i=n(5043),o=n(9291),s=n(8417),t=n(1411),r=n(4741),l=n(9066),d=n(8231),c=n(6291),u=n(579);const m=()=>{const{user:e}=(0,l.A)(),a=(0,d.Zp)();console.log("[DomainSetup] User object from useAuth():",JSON.stringify(e));const[n,m]=(0,i.useState)(""),[v,f]=(0,i.useState)(""),[g,h]=(0,i.useState)(!1),[p,x]=(0,i.useState)(!1),[S,y]=(0,i.useState)(""),[N,j]=(0,i.useState)(""),[b,k]=(0,i.useState)([]),[w,D]=(0,i.useState)(null),[A,E]=(0,i.useState)(!1);(0,i.useEffect)((()=>{console.log(`[DomainSetup useEffect] Running check. initialCheckDone: ${A}, user:`,JSON.stringify(e));!A&&e&&(async()=>{var a;let n=null,i="";if(null!==e&&void 0!==e&&null!==(a=e.domain)&&void 0!==a&&a.name)if(i=e.domain.name,m(i),"active"===e.domain.status)console.log(`[DomainSetup useEffect] Found ACTIVE domain in user object: ${i}`),n="active",k([]);else if("pending"===e.domain.status){console.log(`[DomainSetup useEffect] Found PENDING domain in user object: ${i}. Setting status to pending & fetching records.`),n="pending",j("Existing domain verification is pending. Please add DNS records or click Check Status.");try{var o,s;h(!0);const e=await c.A.get("/domains/records"),a=null===e||void 0===e||null===(o=e.data)||void 0===o||null===(s=o.data)||void 0===s?void 0:s.records;a&&a.length>0?(console.log("[DomainSetup useEffect] Fetched DNS records for pending domain:",a),k(a)):(console.error("[DomainSetup useEffect] API returned success but no records for pending domain."),y("Could not retrieve DNS records for the pending domain. Please try cancelling and restarting."),k([]))}catch(l){var t,r;console.error("[DomainSetup useEffect] Error fetching DNS records for pending domain:",l),y((null===(t=l.response)||void 0===t||null===(r=t.data)||void 0===r?void 0:r.message)||l.message||"Failed to fetch DNS records for pending domain."),k([])}finally{h(!1)}}else console.log(`[DomainSetup useEffect] Found domain in user object but status is neither active/pending: ${e.domain.status}. Treating as null.`),n=null,k([]);else console.log("[DomainSetup useEffect] No domain found in user object."),n=null,k([]);D(n),E(!0),console.log(`[DomainSetup useEffect] Check complete. verificationStatus set to: ${n}`)})()}),[e,A]);const C=async()=>{if(!n)return void y("Please enter a domain name");if(/^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/.test(n))try{var e,a,i;h(!0),y(""),j(""),k([]),console.log(`Registering domain via API: ${n}`);const s=await c.A.post("/domains/register",{domain:n}),t=null===s||void 0===s||null===(e=s.data)||void 0===e||null===(a=e.data)||void 0===a?void 0:a.dnsRecords;if(console.log("API Response Records:",t),null!==s&&void 0!==s&&null!==(i=s.data)&&void 0!==i&&i.success&&t&&t.length>0)k(t),j("Domain registration initiated with SES. Please add the following DNS records to verify your domain."),D("pending");else{var o;const e=(null===s||void 0===s||null===(o=s.data)||void 0===o?void 0:o.message)||"API request failed or did not return expected DNS records.";console.error("Domain registration API error:",e,null===s||void 0===s?void 0:s.data),y(e),D(null)}}catch(r){var s,t;console.error("Axios error during domain registration:",r);const e=null===(s=r.response)||void 0===s||null===(t=s.data)||void 0===t?void 0:t.message;y(e||r.message||"Failed to register domain with SES due to a network or server error."),D(null)}finally{h(!1)}else y("Please enter a valid domain name")},P=async()=>{if(n)try{var e,a;x(!0),y(""),j(""),console.log(`Checking verification via API for domain: ${n}`);const i=await c.A.get("/domains/verify"),o=null===i||void 0===i||null===(e=i.data)||void 0===e||null===(a=e.data)||void 0===a?void 0:a.status;switch(console.log("API Response Status:",o),o){case"active":k([]),D("active"),j("Domain verified successfully with SES! You can now send emails from this domain.");break;case"pending":j("Domain verification is still pending with SES. Please ensure DNS records are correct and allow time for propagation (up to 48 hours)."),D("pending");break;default:console.error("Unexpected status received from verification API:",o),y("Received an unexpected status while checking verification. Please try again later.")}}catch(s){var i,o;console.error("Axios error during domain verification check:",s),y((null===(i=s.response)||void 0===i||null===(o=i.data)||void 0===o?void 0:o.message)||s.message||"Failed to check domain verification status due to a network or server error."),D("pending")}finally{x(!1)}else y("Cannot check verification without a domain name.")},I=async()=>{if(n){h(!0),y(""),j("");try{var e;console.log(`Revoking verification request for domain: ${n}`);const i=await c.A.delete("/domains/revoke");var a;if(null!==i&&void 0!==i&&null!==(e=i.data)&&void 0!==e&&e.success)j(`Verification request for ${n} has been revoked.`),m(""),f(""),k([]),D(null);else y((null===i||void 0===i||null===(a=i.data)||void 0===a?void 0:a.message)||"Failed to revoke verification request.")}catch(s){var i,o;console.error("Axios error during domain verification revoke:",s),y((null===(i=s.response)||void 0===i||null===(o=i.data)||void 0===o?void 0:o.message)||s.message||"Failed to revoke verification due to a network or server error.")}finally{h(!1)}}else y("No pending domain found to revoke.")};return(0,u.jsxs)(t.A,{title:"Email Sending Domain Setup (AWS SES)",children:[(0,u.jsx)("p",{className:"text-text-secondary mb-6",children:"Register and verify a domain with AWS SES to use it for sending emails. Add the generated DNS records to your domain provider."}),S&&(0,u.jsx)(o.A,{type:"error",message:S,onClose:()=>y(""),className:"mb-4"}),N&&(0,u.jsx)(o.A,{type:"success",message:N,className:"mb-4"}),(()=>{if(!A)return(0,u.jsx)("p",{children:"Loading domain status..."});switch(w){case"active":return(0,u.jsxs)("div",{className:"mb-6",children:[(0,u.jsxs)("div",{className:"flex items-center mb-4",children:[(0,u.jsx)("div",{className:"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center mr-2",children:(0,u.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-white",viewBox:"0 0 20 20",fill:"currentColor",children:(0,u.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),(0,u.jsx)("span",{className:"text-lg font-medium",children:"Domain Verified"})]}),(0,u.jsxs)("p",{className:"text-text-secondary mb-4",children:["Your domain ",(0,u.jsx)("strong",{children:n})," has been verified and is ready to use for sending emails."]}),(0,u.jsx)(s.A,{onClick:()=>a("/campaigns"),children:"Go to Campaigns"})]});case"pending":return(0,u.jsxs)("div",{className:"mb-6",children:[(0,u.jsxs)("h3",{className:"text-lg font-medium mb-2",children:["Verification Pending for: ",(0,u.jsx)("span",{className:"font-bold text-primary",children:n})]}),(0,u.jsx)("p",{className:"text-text-secondary mb-4",children:"Add the following DNS records to your domain provider (e.g., GoDaddy, Cloudflare) to verify ownership with AWS SES:"}),b.length>0?(0,u.jsx)("div",{className:"bg-gray-800 p-4 rounded-md mb-4 overflow-x-auto",children:(0,u.jsxs)("table",{className:"min-w-full",children:[(0,u.jsx)("thead",{children:(0,u.jsxs)("tr",{children:[(0,u.jsx)("th",{className:"text-left text-text-secondary",children:"Type"}),(0,u.jsx)("th",{className:"text-left text-text-secondary",children:"Name/Host"}),(0,u.jsx)("th",{className:"text-left text-text-secondary",children:"Value/Points To"})]})}),(0,u.jsx)("tbody",{children:b.map(((e,a)=>(0,u.jsxs)("tr",{children:[(0,u.jsx)("td",{className:"py-2 pr-4 break-all",children:e.type}),(0,u.jsx)("td",{className:"py-2 pr-4 break-all",children:e.name}),(0,u.jsx)("td",{className:"py-2 break-all",children:e.value})]},a)))})]})}):(0,u.jsx)("p",{className:"text-text-secondary mb-4 italic",children:"DNS records are not currently available. Try checking status."}),(0,u.jsx)("p",{className:"text-text-secondary mb-4",children:"After adding these records, click the button below to check verification. DNS changes may take up to 48 hours to propagate."}),(0,u.jsxs)("div",{className:"flex items-center space-x-4 mt-4",children:[" ",(0,u.jsx)(s.A,{onClick:P,disabled:p||g,children:p?"Checking...":"Check Verification Status"}),(0,u.jsx)(s.A,{onClick:I,disabled:g||p,variant:"danger",children:g?"Cancelling...":"Cancel Request"})]})]});default:return(0,u.jsxs)("div",{className:"mb-6",children:[(0,u.jsx)(r.A,{id:"domain",name:"domain",label:"Domain Name",placeholder:"yourdomain.com",value:n,onChange:e=>m(e.target.value),required:!0,className:"mb-4"}),(0,u.jsx)(r.A,{id:"domainId",name:"domainId",label:"Domain ID (Optional)",placeholder:"Optional reference ID",value:v,onChange:e=>f(e.target.value),className:"mb-4"}),(0,u.jsx)(s.A,{onClick:C,disabled:g,children:g?"Registering with SES...":"Register Domain with SES"})]})}})()]})}}}]);
//# sourceMappingURL=764.b452c00b.chunk.js.map