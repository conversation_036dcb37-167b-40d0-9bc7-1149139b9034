FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Clean previous artifacts before installing
RUN rm -rf node_modules dist || true

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Clean dist before building (ensure it happens)
RUN rm -rf dist || true 
# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Clean previous artifacts before installing production deps
RUN rm -rf node_modules || true
# Install production dependencies only
RUN npm ci --only=production

# Copy built files from builder stage
COPY --from=builder /app/dist ./dist

# Set environment variables
ENV NODE_ENV=production
# ENV PORT=5000 # Port is likely set by docker-compose/env file

# Expose port (Should match the actual running port)
EXPOSE 5000

# Start the application without bootstrap.js preloaded
CMD ["node", "dist/server.js"]
