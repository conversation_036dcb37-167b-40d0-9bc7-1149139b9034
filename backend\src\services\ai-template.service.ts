/**
 * AI Template Service for Driftly
 * Handles communication with AI services for email template generation
 */

import axios from 'axios';
import mjml2html from 'mjml';

import Block from '../models/Block';
import UserPreference, { IUserPreference } from '../models/UserPreference';
import { redisClient } from './redis.service';

// Cache expiry time (1 hour)
const CACHE_EXPIRY = 3600;

// AI service URL (can be configured in .env)
const AI_SERVICE_URL = process.env.AI_SERVICE_URL || 'http://localhost:5000';

// Define expected structure for AI content keys (can be expanded/refined)
// This helps in constructing the prompt dynamically.
const EXPECTED_KEYS_PER_BLOCK: { [blockId: string]: string[] } = {
    'content/headline': ['headline'],
    'content/text': ['text'],
    'content/image': ['imageUrl', 'imageAlt'],
    'cta/button': ['buttonText', 'buttonUrl'],
    'misc/spacer': ['spacerHeight'],
    'misc/divider': [], // No keys needed
    'layout/two-column': ['column1_text', 'column1_imageUrl', 'column1_imageAlt', 'column2_text', 'column2_imageUrl', 'column2_imageAlt'],
    'layout/three-column': ['col1_imageUrl', 'col1_imageAlt', 'col1_headline', 'col1_text', 'col1_buttonUrl', 'col1_buttonText', 'col2_imageUrl', 'col2_imageAlt', 'col2_headline', 'col2_text', 'col2_buttonUrl', 'col2_buttonText', 'col3_imageUrl', 'col3_imageAlt', 'col3_headline', 'col3_text', 'col3_buttonUrl', 'col3_buttonText'],
    'layout/hero': ['heroImageUrl', 'heroHeadline', 'heroButtonUrl', 'heroButtonText'],
    'header/simple-nav': ['logoUrl', 'logoAlt', 'nav_links'], // nav_links is array of {name, url}
    'footer/standard': ['companyName', 'companyAddress', 'unsubscribeUrl', 'privacyUrl', 'social_icons'], // social_icons is array of {platform, url}
    'content/feature': ['featureIconUrl', 'featureIconAlt', 'featureHeadline', 'featureText'],
    'content/testimonial': ['avatarUrl', 'avatarAlt', 'quote', 'authorName', 'authorTitle'],
    'product/card': ['productImageUrl', 'productImageAlt', 'productName', 'productDescription', 'productPrice', 'productUrl', 'productButtonText'],
    'product/grid': ['prod1_imageUrl', 'prod1_imageAlt', 'prod1_name', 'prod1_price', 'prod1_url', 'prod1_buttonText', 'prod2_imageUrl', 'prod2_imageAlt', 'prod2_name', 'prod2_price', 'prod2_url', 'prod2_buttonText', 'prod3_imageUrl', 'prod3_imageAlt', 'prod3_name', 'prod3_price', 'prod3_url', 'prod3_buttonText'],
    'content/video': ['videoThumbnailUrl', 'videoAltText', 'videoUrl', 'videoTitle'],
    'header/centered-logo': ['logoUrl', 'logoAlt'],
    'footer/detailed': ['footerTextLine1', 'footerTextLine2', 'link1Url', 'link1Text', 'link2Url', 'link2Text', 'link3Url', 'link3Text', 'social_icons'],
    'layout/image-text-swap': ['imageUrl', 'imageAlt', 'headline', 'text', 'buttonUrl', 'buttonText', 'imageWidthPercentage', 'textWidthPercentage'],
};

/**
 * Generate email template from AI service with improved prompt
 */
export const generateTemplate = async (userPrompt: string, userId: string): Promise<any> => {
  try {
    const cacheKey = `template:ai:v2:${userId}:${userPrompt.toLowerCase().trim()}`; // Updated cache key version

    // Check cache first
    try {
      const cachedResult = await redisClient.get(cacheKey);
      if (cachedResult) {
        console.log('[AI Template Service] Cache hit for template generation');
        return JSON.parse(cachedResult);
      }
    } catch (cacheError) {
      console.warn('[AI Template Service] Cache get error:', cacheError);
    }

    // --- Fetch User Preferences --- 
    let userPrefs: IUserPreference | null = null;
    let userContextPrompt = 'User context: General audience.'; // Default context
    try {
        userPrefs = await UserPreference.findOne({ userId }).lean<IUserPreference>();
        
        if (userPrefs) {
            userContextPrompt = `User context: \n- Brand Name: ${userPrefs.companyName || 'Not specified'}\n- Industry: ${userPrefs.industry || 'Not specified'}\n- Target Audience: ${userPrefs.targetAudience || 'General'}\n- Brand Colors: ${JSON.stringify(userPrefs.brandColors) || 'Default'}\n- Preferred Tone: ${userPrefs.preferredTone || 'Neutral'}`;
            console.log('[AI Template Service] Fetched user preferences for context.');
        } else {
             console.log('[AI Template Service] No user preferences found, using default context.');
        }
    } catch (prefError) {
        console.warn('[AI Template Service] Error fetching user preferences:', prefError);
        // Proceed without preferences if fetch fails
    }
    // --- End Fetch User Preferences ---

    // --- Fetch Available Blocks --- 
    let availableBlocksPromptSection = '';
    try {
        const blocks = await Block.find({ isCustom: false }).select('blockId name description').lean();
        if (blocks.length === 0) {
            console.error('[AI Template Service] No system blocks found in database! Cannot generate prompt effectively.');
            return generateMockTemplate(userPrompt); 
        }
        
        // Build the block list string for the prompt using the user's improved format
        availableBlocksPromptSection = blocks.map(b => {
            const keys = EXPECTED_KEYS_PER_BLOCK[b.blockId] || [];
            // Determine data type hints (simple approach, could be more sophisticated)
            const keyDetails = keys.map(key => {
                let typeHint = '(string)';
                if (key.toLowerCase().includes('url')) typeHint = '(string, URL)';
                else if (key.toLowerCase().includes('height') || key.toLowerCase().includes('percentage')) typeHint = '(number, pixels/percentage)';
                else if (key.toLowerCase().includes('links') || key.toLowerCase().includes('icons')) typeHint = '(array of objects - see Notes)';
                return `${key} ${typeHint}`;
            }).join(', ');
            const keyList = keys.length > 0 ? `Required keys: ${keyDetails}` : 'Required keys: None';
            return `blockId: ${b.blockId}\n${keyList}`; // Use the user's cleaner format
        }).join('\n');

    } catch (dbError) {
        console.error('[AI Template Service] Error fetching blocks from DB:', dbError);
        return generateMockTemplate(userPrompt);
    }

    // --- Construct the Revised Detailed Prompt --- 
    const detailedPrompt = `
Instructions for Advanced Email Template JSON Generation

**Overall Goal:** You are an expert email marketer and designer. Generate a JSON object describing an advanced, professional, and engaging email template based on the User Request and User Context. Use only the provided blocks and keys. Focus on creativity, maximizing marketing effectiveness, and creating a high-quality user experience.

**User Context:**
${userContextPrompt}

**Available Blocks and Required Content Keys:**
(Use only these blockIds. Provide realistic, high-quality content for ALL required keys for each block you choose.)

${availableBlocksPromptSection}

**User Request:**
${userPrompt}

**Instructions & Quality Guidelines:**

1.  **Interpret & Expand Input:**
    *   Analyze the User Request and Context. Infer the primary **Goal** (e.g., drive sales, inform, build loyalty) and adopt the **Preferred Tone**.
    *   If the input is vague (e.g., "a store email"), assume a common, high-impact scenario (e.g., "Promotional email for a small business launching a new sustainable product line, aiming for sales and brand awareness, using an enthusiastic tone") and add relevant creative details.

2.  **Advanced Structure & Flow:**
    *   Select a **logical sequence of at least 6 diverse blocks** from the list above. Create a narrative flow that guides the reader towards the Goal.
    *   Consider modern layouts: Start strong (e.g., \`header\`, \`layout/hero\`), provide value/details (e.g., \`layout/two-column\` for features, \`content/text\` for story), build trust (e.g., \`content/testimonial\`), and finish with a clear call to action (e.g., \`cta/button\`) before the \`footer\`

3.  **Exceptional Content Quality:**
    *   Write **engaging, specific, and persuasive copy** for all text fields (\`headline\`, \`text\`, \`body\`, \`buttonText\`, etc.). Avoid generic placeholders.
    *   **Headlines:** Make them attention-grabbing and benefit-oriented.
    *   **Body Text:** Provide valuable information, tell a story, or clearly explain the offer. Use short paragraphs and clear language.
    *   **CTAs:** Use strong, action-oriented verbs (e.g., "Shop the Collection," "Claim Your Discount," "Register Today"). Make the benefit clear.
    *   **Personalization:** Include relevant placeholders like \`{{FIRST_NAME}}\` or \`{{PRODUCT_NAME}}\` where appropriate.
    *   **Social Proof/Urgency:** Add elements like "Trusted by 500+ happy customers" or "Offer ends Friday!" if suitable for the goal.

4.  **Visual Content (Descriptions):**
    *   For image blocks (\`content/image\`, \`layout/hero\`, columns, etc.), provide highly **descriptive \`imageAlt\` text**. For placeholder URLs, make the alt text reflect the intended image (e.g., \`alt="Smiling customer using Product X"\`). Use \`https://via.placeholder.com/600x400?text=Descriptive+Alt+Text\` format for placeholders.

5.  **Brand & Context Alignment:**
    *   Subtly weave in the **Brand Name** or **Industry** context where it makes sense.
    *   Ensure the generated content aligns with the **Target Audience** and **Preferred Tone**.

6.  **Advanced Concepts (Content):**
    *   Suggest **dynamic content** placeholders if applicable (e.g., \`{% if user.segment == 'VIP' %}Special VIP Offer Text{% endif %}\`).
    *   Include **accessibility** considerations in content (e.g., descriptive alt text).

7.  **Strict Block & Key Usage:**
    *   **Only use \`blockId\` values from the "Available Blocks" list.** Do not invent new block types.
    *   For each chosen block, **include ALL required content keys** listed for that \`blockId\`. Do not add extra keys or omit required ones. Populate them with high-quality content.

8.  **Output Format:**
    *   Return **ONLY a single, valid JSON object**. Do not include any explanatory text before or after the JSON.
    *   The JSON object must have:
        *   \`subject\`: A concise, engaging, and relevant subject line (string).
        *   \`blocks\`: An array of block objects. Each block object MUST contain:
            *   \`blockId\`: The chosen block type (string from the list).
            *   \`content\`: An object containing ALL required keys for that \`blockId\`, populated with high-quality values (strings, numbers, or arrays as specified).

**Example JSON Output Structure (Illustrative - Content must be high quality based on the request):**
\`\`\`json
{
  "subject": "Unlock Exclusive Savings Inside!",
  "blocks": [
    {
      "blockId": "header/simple-nav",
      "content": { /* ... keys and high-quality content ... */ }
    },
    {
      "blockId": "layout/hero",
      "content": { /* ... keys and high-quality content ... */ }
    },
    // ... more blocks ...
    {
      "blockId": "cta/button",
      "content": { /* ... keys and high-quality content ... */ }
    },
    {
      "blockId": "footer/standard",
      "content": { /* ... keys and high-quality content ... */ }
    }
  ]
}
\`\`\`

Generate the advanced JSON structure based on the User Request and Context, following all instructions precisely.
`;

    // --- Call AI Service --- 
    try {
      console.log('[AI Template Service] Sending detailed prompt to AI...');
      console.log(`>>> Attempting to POST to: ${AI_SERVICE_URL}/api/generate`);
      // Send the *detailed* prompt to the AI service
      const aiResponse = await axios.post(`${AI_SERVICE_URL}/api/generate`, {
        prompt: detailedPrompt, // Send the full structured prompt
        userId // Still pass userId if the AI service uses it
      }, {
        timeout: 60000 // Increase timeout for potentially longer generation
      });

      // --- Process AI Response ---
      // IMPORTANT: Adjust parsing based on the ACTUAL structure returned by your AI service
      // Assuming the AI service returns the JSON structure directly in response.data 
      // or nested like response.data.result or response.data.template
      let aiResultData = aiResponse.data; 
      console.log('[AI Template Service] Raw response data from AI service:', JSON.stringify(aiResultData, null, 2));

      if (aiResultData.result) aiResultData = aiResultData.result; // Example if nested
      else if (aiResultData.template) aiResultData = aiResultData.template; // Example if nested differently

      console.log('[AI Template Service] Parsed/unwrapped aiResultData:', JSON.stringify(aiResultData, null, 2));

      // Validate the structure roughly
      if (!aiResultData || !aiResultData.subject || !Array.isArray(aiResultData.blocks)) {
          console.error('[AI Template Service] AI response missing expected structure (subject or blocks array):', aiResultData);
          throw new Error('AI service returned an invalid response structure.');
      }
      
      // Prepare result for caching and returning to the route handler
      const finalResult = {
        template: { // Nest within 'template' key as the route handler expects
          subject: aiResultData.subject,
          blocks: aiResultData.blocks,
          // Add other fields if the AI provides them (e.g., tone, color_scheme)
        },
        processing_time: aiResponse.data.processing_time || null 
      };

      // --- Cache Result --- 
      try {
        await redisClient.setex(cacheKey, CACHE_EXPIRY, JSON.stringify(finalResult));
      } catch (cacheError) {
        console.warn('[AI Template Service] Cache set error:', cacheError);
      }

      return finalResult;

    } catch (aiError: any) {
      console.error('[AI Template Service] Error calling AI service with detailed prompt:', aiError.response?.data || aiError.message);
      // Fall back to mock data on AI failure
      console.log('[AI Template Service] Falling back to mock template generation.');
      return generateMockTemplate(userPrompt);
    }

  } catch (error) {
    console.error('[AI Template Service] Unexpected error in generateTemplate:', error);
    throw error; // Rethrow unexpected errors
  }
};

/**
 * Get AI content suggestions for template improvement
 */
export const getSuggestions = async (content: string, suggestionType: string): Promise<string[]> => {
  try {
    // Create cache key
    const cacheKey = `suggestion:${suggestionType}:${content.substring(0, 50)}`;

    // Check cache
    try {
      const cachedSuggestions = await redisClient.get(cacheKey);

      if (cachedSuggestions) {
        console.log('[AI Template] Cache hit for content suggestions');
        return JSON.parse(cachedSuggestions);
      }
    } catch (cacheError) {
      console.warn('[AI Template] Cache error for suggestions:', cacheError);
      // Continue without cache
    }

    // Try to call AI service
    try {
      const aiResponse = await axios.post(`${AI_SERVICE_URL}/api/suggest`, {
        content,
        suggestion_type: suggestionType
      }, {
        timeout: 15000 // 15 second timeout
      });

      const suggestions = aiResponse.data.suggestions;

      // Cache the result
      try {
        await redisClient.setex(cacheKey, CACHE_EXPIRY, JSON.stringify(suggestions));
      } catch (cacheError) {
        console.warn('[AI Template] Cache set error for suggestions:', cacheError);
        // Continue without cache
      }

      return suggestions;
    } catch (aiError) {
      console.error('[AI Template] Error calling AI service for suggestions:', aiError);

      // Fall back to mock suggestions
      return generateMockSuggestions(content, suggestionType);
    }
  } catch (error) {
    console.error('[AI Template] Error getting suggestions:', error);
    throw error;
  }
};

/**
 * Generate mock template when AI service is not available
 */
const generateMockTemplate = (prompt: string): any => {
  console.log('[AI Template] Generating mock template for prompt:', prompt);

  // Simple mock template
  const mockTemplate = {
    template: {
      subject: `Email about ${prompt}`,
      blocks: [
        {
          type: "hero_with_cta",
          category: "header",
          content: {
            headline: `Learn more about ${prompt}`,
            body: "We have important information to share with you.",
            cta_text: "Learn More",
            cta_url: "#",
            image_description: `Hero image related to ${prompt}`
          }
        },
        {
          type: "two_column_text_image",
          category: "content",
          content: {
            headline: "Key Information",
            body: `Here are some important details about ${prompt} that we think you'll find valuable.`,
            image_description: `Informative image about ${prompt}`
          }
        },
        {
          type: "full_width_cta",
          category: "cta",
          content: {
            headline: "Ready to get started?",
            body: "Contact us today to learn more about our offerings.",
            cta_text: "Contact Us",
            cta_url: "#"
          }
        },
        {
          type: "simple_footer",
          category: "footer",
          content: {
            company_name: "Your Company",
            social_links: ["facebook", "twitter", "instagram"]
          }
        }
      ],
      tone: "informative",
      color_scheme: "blue"
    },
    processing_time: 0.5
  };

  return mockTemplate;
};

/**
 * Generate mock suggestions when AI service is not available
 */
const generateMockSuggestions = (content: string, suggestionType: string): string[] => {
  console.log('[AI Template] Generating mock suggestions for type:', suggestionType);

  if (suggestionType === 'headline') {
    return [
      "Discover Our Exclusive Offer Today!",
      "Limited Time: Special Promotion Inside",
      "You Won't Want to Miss This Opportunity"
    ];
  } else if (suggestionType === 'body') {
    return [
      "We're excited to share our latest products with you. Our team has been working hard to bring you the best quality and value.",
      "Thank you for being a valued customer. We appreciate your business and want to offer you something special as a token of our gratitude.",
      "Looking for the perfect solution? We've got you covered with our comprehensive range of services designed to meet your needs."
    ];
  } else if (suggestionType === 'cta') {
    return [
      "Get Started Now",
      "Claim Your Offer",
      "Learn More Today"
    ];
  }

  return ["No suggestions available"];
};

/**
 * Convert MJML template to HTML
 */
export const convertMjmlToHtml = (mjmlContent: string): { html: string, errors?: any[] } => {
  try {
    return mjml2html(mjmlContent, { minify: true });
  } catch (error) {
    console.error('[AI Template] Error converting MJML to HTML:', error);
    throw error;
  }
};

export default {
  generateTemplate,
  getSuggestions,
  convertMjmlToHtml,
};

