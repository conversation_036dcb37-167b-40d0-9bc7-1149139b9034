{"ast": null, "code": "export class OptionsReader {\n  get window() {\n    if (this.globalContext) {\n      return this.globalContext;\n    } else if (typeof window !== 'undefined') {\n      return window;\n    }\n    return undefined;\n  }\n  get document() {\n    var ref;\n    if ((ref = this.globalContext) === null || ref === void 0 ? void 0 : ref.document) {\n      return this.globalContext.document;\n    } else if (this.window) {\n      return this.window.document;\n    } else {\n      return undefined;\n    }\n  }\n  get rootElement() {\n    var ref;\n    return ((ref = this.optionsArgs) === null || ref === void 0 ? void 0 : ref.rootElement) || this.window;\n  }\n  constructor(globalContext, options) {\n    this.ownerDocument = null;\n    this.globalContext = globalContext;\n    this.optionsArgs = options;\n  }\n}", "map": {"version": 3, "names": ["OptionsReader", "window", "globalContext", "undefined", "document", "ref", "rootElement", "optionsArgs", "constructor", "options", "ownerDocument"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd-html5-backend\\src\\OptionsReader.ts"], "sourcesContent": ["import type { HTML5BackendContext, HTML5BackendOptions } from './types.js'\n\nexport class OptionsReader {\n\tpublic ownerDocument: Document | null = null\n\tprivate globalContext: HTML5BackendContext\n\tprivate optionsArgs: HTML5BackendOptions | undefined\n\n\tpublic constructor(\n\t\tglobalContext: HTML5BackendContext,\n\t\toptions?: HTML5BackendOptions,\n\t) {\n\t\tthis.globalContext = globalContext\n\t\tthis.optionsArgs = options\n\t}\n\n\tpublic get window(): Window | undefined {\n\t\tif (this.globalContext) {\n\t\t\treturn this.globalContext\n\t\t} else if (typeof window !== 'undefined') {\n\t\t\treturn window\n\t\t}\n\t\treturn undefined\n\t}\n\n\tpublic get document(): Document | undefined {\n\t\tif (this.globalContext?.document) {\n\t\t\treturn this.globalContext.document\n\t\t} else if (this.window) {\n\t\t\treturn this.window.document\n\t\t} else {\n\t\t\treturn undefined\n\t\t}\n\t}\n\n\tpublic get rootElement(): Node | undefined {\n\t\treturn this.optionsArgs?.rootElement || this.window\n\t}\n}\n"], "mappings": "AAEA,OAAO,MAAMA,aAAa;EAazB,IAAWC,MAAMA,CAAA,EAAuB;IACvC,IAAI,IAAI,CAACC,aAAa,EAAE;MACvB,OAAO,IAAI,CAACA,aAAa;KACzB,MAAM,IAAI,OAAOD,MAAM,KAAK,WAAW,EAAE;MACzC,OAAOA,MAAM;;IAEd,OAAOE,SAAS;;EAGjB,IAAWC,QAAQA,CAAA,EAAyB;QACvCC,GAAkB;IAAtB,IAAI,CAAAA,GAAkB,GAAlB,IAAI,CAACH,aAAa,cAAlBG,GAAkB,WAAU,GAA5B,MAA4B,GAA5BA,GAAkB,CAAED,QAAQ,EAAE;MACjC,OAAO,IAAI,CAACF,aAAa,CAACE,QAAQ;KAClC,MAAM,IAAI,IAAI,CAACH,MAAM,EAAE;MACvB,OAAO,IAAI,CAACA,MAAM,CAACG,QAAQ;KAC3B,MAAM;MACN,OAAOD,SAAS;;;EAIlB,IAAWG,WAAWA,CAAA,EAAqB;QACnCD,GAAgB;IAAvB,OAAO,EAAAA,GAAgB,GAAhB,IAAI,CAACE,WAAW,cAAhBF,GAAgB,WAAa,GAA7B,MAA6B,GAA7BA,GAAgB,CAAEC,WAAW,KAAI,IAAI,CAACL,MAAM;;EA5BpDO,YACCN,aAAkC,EAClCO,OAA6B,EAC5B;IAPF,KAAOC,aAAa,GAAoB,IAAI;IAQ3C,IAAI,CAACR,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACK,WAAW,GAAGE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}