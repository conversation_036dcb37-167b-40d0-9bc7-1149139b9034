import {
  NextFunction,
  Request,
  Response,
} from 'express';
import fs from 'fs';
import createError from 'http-errors';
import path from 'path';

// Define the path to the private uploads directory relative to the backend root
const privateUploadsDir = path.resolve(__dirname, '../../private_uploads');

/**
 * Serves a privately stored image.
 */
export const servePrivateImage = (req: Request, res: Response, next: NextFunction) => {
  const { filename } = req.params;

  if (!filename) {
    return next(createError(400, 'Filename parameter is missing'));
  }

  // Basic security: Prevent directory traversal
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    return next(createError(400, 'Invalid filename'));
  }

  // --- Add Logging ---
  console.log(`[servePrivateImage] Decoded filename from req.params: ${filename}`);
  // --- End Logging ---

  const filePath = path.join(privateUploadsDir, filename);
  console.log(`[servePrivateImage] Attempting to serve file with path: ${filePath}`); // Log exact path

  // Check if file exists
  fs.access(filePath, fs.constants.R_OK, (err) => {
    if (err) {
      console.error(`[servePrivateImage] File not found or not readable: ${filePath}`, err);
      // Use a generic 404 Not Found error
      return next(createError(404, 'Image not found')); 
    }

    // Send the file
    // res.sendFile automatically handles Content-Type based on extension
    res.sendFile(filePath, (err) => {
      if (err) {
        console.error(`[servePrivateImage] Error sending file ${filePath}:`, err);
        // If an error occurs after starting to send, pass it to the default error handler
        // Check if headers have already been sent
        if (!res.headersSent) {
          next(createError(500, 'Error serving image'));
        }
      }
    });
  });
}; 