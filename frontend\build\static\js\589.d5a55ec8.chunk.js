"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[589],{3589:(e,s,n)=>{n.r(s),n.d(s,{default:()=>m});var l=n(5043),i=n(9291),t=n(8417),a=n(1411),c=n(9066),d=n(8231),r=n(6291),o=n(579);const m=()=>{var e;const{id:s}=(0,d.g)(),{user:n}=(0,c.A)(),m=(0,d.Zp)(),[x,p]=(0,l.useState)(null),[h,u]=(0,l.useState)(!0),[g,j]=(0,l.useState)(""),[v,f]=(0,l.useState)({delivered:0,opened:0,clicked:0,bounced:0,unsubscribed:0,complaints:0,openRate:0,clickRate:0,clickToOpenRate:0,bounceRate:0,unsubscribeRate:0,topLinks:[],opensByDevice:{desktop:0,mobile:0,tablet:0},opensByLocation:{"United States":0,"United Kingdom":0,Canada:0,Australia:0,Other:0},opensByTime:{morning:0,afternoon:0,evening:0,night:0}}),[b,y]=(0,l.useState)(!1);(0,l.useEffect)((()=>{let e=null;const n=async function(){let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];n&&(u(!0),p(null),f({delivered:0,opened:0,clicked:0,bounced:0,unsubscribed:0,complaints:0,openRate:0,clickRate:0,clickToOpenRate:0,bounceRate:0,unsubscribeRate:0,topLinks:[],opensByDevice:{desktop:0,mobile:0,tablet:0},opensByLocation:{"United States":0,"United Kingdom":0,Canada:0,Australia:0,Other:0},opensByTime:{morning:0,afternoon:0,evening:0,night:0}}),j("")),console.log(`[CampaignAnalytics] ${n?"Initial fetch":"Polling fetch"} for ID: ${s}`);try{const i=localStorage.getItem("token");if(!i)throw new Error("Authentication token not found");if(n||!x){const n=(await r.A.get(`/campaigns/${s}`)).data;if(!n.success||!n.data.campaign)return j(n.message||"Campaign not found"),u(!1),void(e&&clearInterval(e));p(n.data.campaign),"sent"!==n.data.campaign.status&&"completed"!==n.data.campaign.status||l(i,n.data.campaign)}else"sent"===x.status||"completed"===x.status?l(i,x):n&&u(!1)}catch(a){var i,t;console.error(`Error fetching campaign data (${n?"initial":"poll"}):`,a);const s=(null===(i=a.response)||void 0===i||null===(t=i.data)||void 0===t?void 0:t.message)||a.message||"Failed to fetch campaign";j(s),n&&u(!1),e&&clearInterval(e)}},l=async(e,n)=>{try{const e=(await r.A.get(`/analytics/campaign/${s}`)).data;if(e.success&&e.data){var l,i,t,a,c,d,o,m,x,p,g,j,v,b,y,N,k,w,B,C;const s=e.data;console.log("[CampaignAnalytics] Received analytics data object for setAnalytics:",s),f({delivered:null!==(l=null!==(i=s.delivered)&&void 0!==i?i:n.sentCount)&&void 0!==l?l:0,opened:null!==(t=null!==(a=s.opened)&&void 0!==a?a:n.openCount)&&void 0!==t?t:0,clicked:null!==(c=null!==(d=s.clicked)&&void 0!==d?d:n.clickCount)&&void 0!==c?c:0,bounced:null!==(o=null!==(m=s.bounced)&&void 0!==m?m:n.bounceCount)&&void 0!==o?o:0,unsubscribed:null!==(x=null!==(p=s.unsubscribed)&&void 0!==p?p:n.unsubscribeCount)&&void 0!==x?x:0,complaints:null!==(g=null!==(j=s.complaints)&&void 0!==j?j:n.complaintCount)&&void 0!==g?g:0,openRate:null!==(v=null!==(b=s.openRate)&&void 0!==b?b:n.openRate)&&void 0!==v?v:0,clickRate:null!==(y=null!==(N=s.clickRate)&&void 0!==N?N:n.clickRate)&&void 0!==y?y:0,clickToOpenRate:null!==(k=s.clickToOpenRate)&&void 0!==k?k:0,bounceRate:null!==(w=s.bounceRate)&&void 0!==w?w:n.recipientCountActual?n.bounceCount/n.recipientCountActual*100:0,unsubscribeRate:null!==(B=s.unsubscribeRate)&&void 0!==B?B:n.recipientCountActual?(null!==(C=n.unsubscribeCount)&&void 0!==C?C:0)/n.recipientCountActual*100:0,topLinks:s.topLinks||[],opensByDevice:s.opensByDevice||{desktop:0,mobile:0,tablet:0},opensByLocation:s.opensByLocation||{},opensByTime:s.opensByTime||{morning:0,afternoon:0,evening:0,night:0}})}}catch(A){console.error("Error fetching analytics data (poll/initial):",A)}finally{h&&u(!1)}};n(!0);return e=setInterval((()=>n(!1)),3e5),()=>{e&&(console.log(`[CampaignAnalytics] Clearing interval for ID: ${s}`),clearInterval(e))}}),[s]);return h?(0,o.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):x?"sent"!==x.status&&"completed"!==x.status?(0,o.jsxs)("div",{children:[(0,o.jsx)(i.A,{type:"warning",message:"Analytics are only available for sent or completed campaigns",className:"mb-6"}),(0,o.jsx)(t.A,{onClick:()=>m("/campaigns"),children:"Back to Campaigns"})]}):(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold",children:[x.name," - Analytics"]}),(0,o.jsx)("p",{className:"text-text-secondary",children:"Campaign performance metrics"})]}),(0,o.jsx)(t.A,{variant:"secondary",onClick:()=>m("/campaigns"),children:"Back to Campaigns"})]}),(0,o.jsxs)(a.A,{className:"mb-6",children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Campaign Overview"}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Subject:"})," ",x.subject]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"From:"})," ",x.fromName," <",x.fromEmail,">"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Sent/Completed Date:"})," ",(e=>{if(!e)return"-";return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})})(x.completedAt||x.startedAt||"")]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Recipients:"})," ",null!==(e=x.recipientCountActual)&&void 0!==e?e:"N/A"]})]})})]}),(0,o.jsxs)(a.A,{className:"mb-6",children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Key Metrics"}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-4 justify-around",children:[(0,o.jsxs)("div",{className:"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]",children:[(0,o.jsx)("p",{className:"text-xs text-gray-400",children:"Delivered"}),(0,o.jsx)("p",{className:"text-xl font-bold",children:v.delivered})]}),(0,o.jsxs)("div",{className:"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]",children:[(0,o.jsx)("p",{className:"text-xs text-gray-400",children:"Opened"}),(0,o.jsx)("p",{className:"text-xl font-bold",children:v.opened})]}),(0,o.jsxs)("div",{className:"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]",children:[(0,o.jsx)("p",{className:"text-xs text-gray-400",children:"Clicked"}),(0,o.jsx)("p",{className:"text-xl font-bold",children:v.clicked})]}),(0,o.jsxs)("div",{className:"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]",children:[(0,o.jsx)("p",{className:"text-xs text-gray-400",children:"Bounced"}),(0,o.jsx)("p",{className:"text-xl font-bold",children:v.bounced})]}),(0,o.jsxs)("div",{className:"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]",children:[(0,o.jsx)("p",{className:"text-xs text-gray-400",children:"Unsubscribed"}),(0,o.jsx)("p",{className:"text-xl font-bold",children:v.unsubscribed})]}),(0,o.jsxs)("div",{className:"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]",children:[(0,o.jsx)("p",{className:"text-xs text-gray-400",children:"Complaints"}),(0,o.jsx)("p",{className:"text-xl font-bold",children:v.complaints})]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,o.jsxs)(a.A,{children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Top Clicked Links"}),v.topLinks.length>0?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"space-y-3",children:(b?v.topLinks:v.topLinks.slice(0,5)).map(((e,s)=>(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("div",{className:"truncate max-w-xs",children:(0,o.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:underline",title:e.url,children:e.url})}),(0,o.jsx)("div",{className:"flex items-center ml-2 flex-shrink-0",children:(0,o.jsxs)("span",{className:"text-sm",children:[e.clicks," clicks"]})})]},s)))}),v.topLinks.length>5&&(0,o.jsx)("div",{className:"mt-4 text-center",children:(0,o.jsx)(t.A,{onClick:()=>y(!b),children:b?"View Less":"View More"})})]}):(0,o.jsx)("p",{className:"text-gray-400",children:"No link clicks recorded"})]}),(0,o.jsxs)(a.A,{children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Opens by Device"}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:"Desktop"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[v.opensByDevice.desktop," (",Math.floor(v.opensByDevice.desktop/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${Math.floor(v.opensByDevice.desktop/v.opened*100)}%`}})})]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:"Mobile"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[v.opensByDevice.mobile," (",Math.floor(v.opensByDevice.mobile/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${Math.floor(v.opensByDevice.mobile/v.opened*100)}%`}})})]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:"Tablet"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[v.opensByDevice.tablet," (",Math.floor(v.opensByDevice.tablet/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-purple-500 h-2 rounded-full",style:{width:`${Math.floor(v.opensByDevice.tablet/v.opened*100)}%`}})})]})]})]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)(a.A,{children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Opens by Location"}),(0,o.jsx)("div",{className:"space-y-3",children:Object.entries(v.opensByLocation).map((e=>{let[s,n]=e;return(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:s}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[n," (",Math.floor(n/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full",style:{width:`${Math.floor(n/v.opened*100)}%`}})})]})]},s)}))})]}),(0,o.jsxs)(a.A,{children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Opens by Time"}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:"Morning (6am-12pm)"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[v.opensByTime.morning," (",Math.floor(v.opensByTime.morning/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-yellow-400 h-2 rounded-full",style:{width:`${Math.floor(v.opensByTime.morning/v.opened*100)}%`}})})]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:"Afternoon (12pm-6pm)"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[v.opensByTime.afternoon," (",Math.floor(v.opensByTime.afternoon/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-orange-500 h-2 rounded-full",style:{width:`${Math.floor(v.opensByTime.afternoon/v.opened*100)}%`}})})]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:"Evening (6pm-12am)"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[v.opensByTime.evening," (",Math.floor(v.opensByTime.evening/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-red-500 h-2 rounded-full",style:{width:`${Math.floor(v.opensByTime.evening/v.opened*100)}%`}})})]})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{children:"Night (12am-6am)"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-sm mr-2",children:[v.opensByTime.night," (",Math.floor(v.opensByTime.night/v.opened*100),"%)"]}),(0,o.jsx)("div",{className:"w-24 bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-indigo-500 h-2 rounded-full",style:{width:`${Math.floor(v.opensByTime.night/v.opened*100)}%`}})})]})]})]})]})]})]}):(0,o.jsxs)("div",{children:[(0,o.jsx)(i.A,{type:"error",message:"Campaign not found",className:"mb-6"}),(0,o.jsx)(t.A,{onClick:()=>m("/campaigns"),children:"Back to Campaigns"})]})}}}]);
//# sourceMappingURL=589.d5a55ec8.chunk.js.map