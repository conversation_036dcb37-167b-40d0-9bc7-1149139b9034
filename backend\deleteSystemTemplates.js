// Plain JavaScript script to delete system templates
const mongoose = require('mongoose');

// Direct MongoDB connection URI
const URI = "mongodb+srv://oblongjones1992:<EMAIL>/driftly?retryWrites=true&w=majority&appName=driftly-db";

// Define a simplified schema that matches the template.model.ts
const templateSchema = new mongoose.Schema({
  name: String,
  description: String,
  content: String,
  category: String,
  isSystem: Boolean
});

// Create a model with the schema
const Template = mongoose.model('Template', templateSchema);

async function deleteSystemTemplates() {
  try {
    console.log('Attempting to connect to MongoDB...');
    await mongoose.connect(URI);
    console.log('Connected to MongoDB');

    // Count system templates
    const count = await Template.countDocuments({ isSystem: true });
    console.log(`Found ${count} system templates to delete`);

    // Delete system templates
    const result = await Template.deleteMany({ isSystem: true });
    console.log(`✅ Successfully deleted ${result.deletedCount} system templates`);
    
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error deleting system templates:', error);
    if (mongoose.connection.readyState) {
      await mongoose.disconnect();
    }
    process.exit(1);
  }
}

// Execute the function
deleteSystemTemplates().catch(console.error); 