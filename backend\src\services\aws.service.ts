import {
  NextFunction,
  Request,
  Response,
} from 'express';

import {
  GetIdentityVerificationAttributesCommand,
  SendRawEmailCommand,
  SESClient,
  VerifyDomainIdentityCommand,
} from '@aws-sdk/client-ses';
import {
  PublishCommand,
  SNSClient,
} from '@aws-sdk/client-sns';

import { createError } from '../utils/error.util';

// AWS Configuration
const region = process.env.AWS_REGION || 'us-east-2';
const accessKeyId = process.env.AWS_ACCESS_KEY_ID;
const secretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;

// Log AWS configuration
console.log('AWS Region:', region);
console.log('AWS Access Key ID:', accessKeyId ? 'Configured' : 'Not configured');
console.log('AWS Secret Access Key:', secretAccessKey ? 'Configured' : 'Not configured');

// Override some AWS environment variables but keep configuration set
process.env.AWS_SDK_LOAD_CONFIG = 'false';
process.env.AWS_CONFIG_FILE = '/non-existent-path';
process.env.AWS_SHARED_CREDENTIALS_FILE = '/non-existent-path';
// Keep the SES_CONFIGURATION_SET from .env file

// Initialize SES client with explicit credentials
const sesClient = new SESClient({
  region,
  credentials: accessKeyId && secretAccessKey ? {
    accessKeyId,
    secretAccessKey
  } : undefined
  // The SESClient doesn't support loadedConfig property
});

// Initialize SNS client with explicit credentials
const snsClient = new SNSClient({
  region,
  credentials: accessKeyId && secretAccessKey ? {
    accessKeyId,
    secretAccessKey
  } : undefined
});

// Service for AWS SES operations
export const SESService = {
  // Verify a domain with SES
  verifyDomain: async (domain: string): Promise<string> => {
    try {
      const command = new VerifyDomainIdentityCommand({
        Domain: domain
      });

      const response = await sesClient.send(command);
      return response.VerificationToken || '';
    } catch (error: any) {
      console.error('Error verifying domain with SES:', error);
      throw error;
    }
  },

  // Check domain verification status and return the status string
  getDomainVerificationStatus: async (domain: string): Promise<string> => {
    try {
      const command = new GetIdentityVerificationAttributesCommand({
        Identities: [domain]
      });

      const response = await sesClient.send(command);
      const attributes = response.VerificationAttributes || {};

      // Return the status directly, or 'NotFound' if attributes for the domain don't exist
      return attributes[domain]?.VerificationStatus || 'NotFound';
    } catch (error: any) {
      console.error('Error checking domain verification status:', error);
      // Consider specific error handling, e.g., if the error indicates invalid domain format
      // For now, re-throw to be handled by the controller
      throw error;
    }
  },

  // Send an email using raw email format to ensure proper content-type headers
  sendEmail: async (params: {
    from: string;
    to: string[];
    subject: string;
    html: string;
    text?: string;
    replyTo?: string;
    campaignId?: string;
    subscriberId?: string;
  }): Promise<string> => {
    try {
      const { from, to, subject, html, text, replyTo, campaignId, subscriberId } = params;

      console.log('[AWS SES] Sending email with params:', {
        from,
        to,
        subject,
        replyTo,
        campaignId: campaignId || 'Not specified',
        subscriberId: subscriberId || 'Not specified',
        htmlLength: html.length
      });

      // Create a new SES client for this specific operation
      // This ensures we don't use any cached configuration
      const tempSesClient = new SESClient({
        region,
        credentials: accessKeyId && secretAccessKey ? {
          accessKeyId,
          secretAccessKey
        } : undefined
      });

      console.log('[AWS SES] Created temporary SES client');

      // Always use the driftly-tracking configuration set for email tracking
      const configSet = process.env.SES_CONFIGURATION_SET || 'driftly-tracking';
      console.log('[AWS SES] Using configuration set:', configSet);

      // Prepare email message tags
      const tags = [];
      
      // Add campaign ID tag if provided
      if (campaignId) {
        tags.push({
          Name: 'campaignId',
          Value: campaignId
        });
      }
      
      // Add subscriber ID tag if provided
      if (subscriberId) {
        tags.push({
          Name: 'subscriberId',
          Value: subscriberId
        });
      }

      // Construct raw email with proper headers for HTML content
      const rawMessage = [
        `From: ${from}`,
        `To: ${to.join(', ')}`,
        `Subject: ${subject}`,
        'MIME-Version: 1.0',
        'Content-Type: text/html; charset=UTF-8',
        'X-Content-Type-Options: nosniff',
        ...(replyTo ? [`Reply-To: ${replyTo}`] : []),
        '',  // Empty line separates headers from body
        html  // HTML body
      ].join('\r\n');

      const emailParams = {
        Source: from,
        Destinations: to,
        RawMessage: {
          Data: Buffer.from(rawMessage)
        },
        ConfigurationSetName: configSet,
        ...(tags.length > 0 && { Tags: tags })
      };

      const command = new SendRawEmailCommand(emailParams);

      console.log('[AWS SES] Sending email...');
      const response = await tempSesClient.send(command);
      console.log('[AWS SES] Email sent successfully:', response.MessageId);
      
      return response.MessageId || '';
    } catch (error) {
      console.error('[AWS SES] Error sending email:', error);
      throw error;
    }
  }
};

// Service for AWS SNS operations
export const SNSService = {
  // Publish a notification
  publishNotification: async (params: {
    topicArn: string;
    message: string;
    subject?: string;
  }): Promise<string> => {
    try {
      const { topicArn, message, subject } = params;

      const command = new PublishCommand({
        TopicArn: topicArn,
        Message: message,
        ...(subject && { Subject: subject })
      });

      const response = await snsClient.send(command);
      return response.MessageId || '';
    } catch (error: any) {
      console.error('Error publishing notification with SNS:', error);
      throw error;
    }
  }
};

// Middleware to handle AWS service errors
export const awsErrorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  if (err.name === 'CredentialsProviderError') {
    return next(createError(500, 'AWS credentials not configured properly'));
  }

  if (err.name === 'InvalidParameterException') {
    return next(createError(400, err.message));
  }

  if (err.name === 'MessageRejected') {
    return next(createError(400, 'Email message was rejected: ' + err.message));
  }

  next(err);
};
