"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[410],{410:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var n=r(5043),s=r(9291),a=r(8417),l=r(1411),o=r(579);const c=e=>{let{content:t}=e;return(0,o.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-700 text-center text-xs text-gray-400",children:[t.text&&(0,o.jsx)("p",{className:"mb-2",children:t.text}),t.company_name&&!t.text&&(0,o.jsxs)("p",{className:"mb-2",children:["\xa9 ",(new Date).getFullYear()," ",t.company_name,". All rights reserved."]}),(0,o.jsxs)("p",{children:[t.contact_url&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("a",{href:t.contact_url,target:"_blank",rel:"noopener noreferrer",className:"underline hover:text-gray-200",children:"Contact"})," | "]}),t.unsubscribe_url&&(0,o.jsx)("a",{href:t.unsubscribe_url,target:"_blank",rel:"noopener noreferrer",className:"underline hover:text-gray-200",children:"Unsubscribe"})]}),t.social_links&&t.social_links.length>0&&(0,o.jsx)("div",{className:"mt-2",children:t.social_links.map(((e,t)=>"object"===typeof e&&null!==e&&"platform"in e&&"url"in e?(0,o.jsxs)("a",{href:e.url||"#",target:"_blank",rel:"noopener noreferrer",className:"inline-block mx-1 underline hover:text-gray-200 capitalize",children:[e.platform," "]},`${e.platform}-${t}`):"string"===typeof e?(0,o.jsxs)("a",{href:`#${e}`,className:"inline-block mx-1 underline hover:text-gray-200 capitalize",children:[e," "]},`${e}-${t}`):null))})]})},i=e=>{let{content:t}=e;return(0,o.jsxs)("div",{className:"mb-4 p-4 border border-gray-600 rounded bg-gray-800 text-center",children:[t.image_description&&(0,o.jsxs)("p",{className:"text-xs text-gray-400 mb-2",children:["(Image: ",t.image_description,")"]}),t.headline&&(0,o.jsx)("h2",{className:"text-xl font-bold mb-2 text-blue-400",children:t.headline}),t.body&&(0,o.jsx)("p",{className:"mb-3 text-gray-300",children:t.body}),t.cta_text&&(0,o.jsx)("a",{href:t.cta_url||"#",target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors",children:t.cta_text})]})},d=e=>{let{content:t}=e;return(0,o.jsxs)("div",{className:"mb-4 p-4 border border-gray-600 rounded bg-gray-800 flex items-center justify-between",children:[t.logo_url&&(0,o.jsx)("img",{src:t.logo_url,alt:"Logo",className:"h-8 w-auto max-w-[100px] object-contain mr-4",onError:e=>{e.target.style.display="none"}}),!t.logo_url&&(0,o.jsx)("div",{className:"text-gray-400 text-sm",children:"(No Logo Provided)"}),(0,o.jsx)("nav",{children:(0,o.jsx)("ul",{className:"flex space-x-4",children:t.nav_links&&t.nav_links.map(((e,t)=>{const r="object"===typeof e?e.name:e,n="object"===typeof e?e.url:"#";return(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:n,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-400 hover:text-blue-300 hover:underline",children:r})},t)}))})})]})},m=e=>{let{content:t}=e;const r=t.products||(t.show_name?[t]:[]);return(0,o.jsxs)("div",{className:"mb-4 p-4 border border-gray-600 rounded bg-gray-800",children:[t.headline&&(0,o.jsx)("h3",{className:"text-lg font-semibold mb-3 text-gray-100",children:t.headline}),(0,o.jsx)("div",{className:"space-y-3",children:r.map(((e,t)=>(0,o.jsxs)("div",{className:"p-3 border border-gray-700 rounded bg-gray-700/50 flex flex-col sm:flex-row gap-3 items-center",children:[e.image_url&&(0,o.jsx)("img",{src:e.image_url,alt:e.show_name||"Product Image",className:"w-full sm:w-24 h-auto sm:h-24 object-cover rounded mb-2 sm:mb-0 sm:mr-3 flex-shrink-0",onError:e=>{e.target.style.display="none"}}),(0,o.jsxs)("div",{className:"flex-grow text-center sm:text-left",children:[e.show_name&&(0,o.jsx)("h4",{className:"font-semibold text-gray-200",children:e.show_name}),(e.date||e.time)&&(0,o.jsxs)("p",{className:"text-sm text-gray-300 mb-2",children:[e.date,e.date&&e.time&&", ",e.time]}),e.cta_text&&(0,o.jsx)("a",{href:e.cta_url||"#",target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-green-600 text-white text-sm py-1 px-3 rounded hover:bg-green-700 transition-colors",children:e.cta_text})]})]},t)))})]})},x=e=>{let{content:t}=e;return(0,o.jsxs)("div",{className:"mb-4 p-4 border border-dashed border-purple-400 rounded bg-gray-800 text-center",children:[t.quote&&(0,o.jsxs)("blockquote",{className:"text-lg italic text-gray-200 mb-3",children:['" ',t.quote,' "']}),t.author&&(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[t.author_image_url&&(0,o.jsx)("img",{src:t.author_image_url,alt:t.author,className:"w-8 h-8 rounded-full object-cover",onError:e=>{e.target.style.display="none"}}),(0,o.jsxs)("cite",{className:"text-sm font-semibold text-purple-300 not-italic",children:["- ",t.author]})]})]})},u=e=>{let{content:t,type:r}=e;return(0,o.jsxs)("div",{className:"mb-4 p-4 border border-gray-600 rounded bg-gray-800",children:[t.headline&&(0,o.jsx)("h3",{className:"text-lg font-semibold mb-1 text-gray-100",children:t.headline}),t.body&&(0,o.jsx)("p",{className:"text-gray-300",children:t.body})]})};var h=r(9579);const p=()=>{const[e,t]=(0,n.useState)(""),[r,p]=(0,n.useState)("email"),[b,g]=(0,n.useState)(null),[y,j]=(0,n.useState)(!1),[f,v]=(0,n.useState)(null),[N,_]=(0,n.useState)([]),[w,k]=(0,n.useState)(!1);(0,n.useEffect)((()=>{(async()=>{try{const e=await h.rG.getHistory();e.success&&_(e.data)}catch(e){console.error("Error fetching history:",e)}})()}),[]);return(0,o.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"AI Content Generator"}),(0,o.jsx)(a.A,{onClick:()=>k(!w),variant:"secondary",children:w?"Hide History":"Show History"})]}),(0,o.jsx)("p",{className:"text-text-secondary mb-6",children:"Generate email content, subject lines, and more using AI."}),(0,o.jsx)(l.A,{className:"mb-6",children:(0,o.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),e.trim()){j(!0),v(null),g(null);try{const t=await h.rG.generateContent(e,r);t.success&&t.data&&t.data.content?(g(t.data.content),_((n=>[{...t.data.content,id:t.data.id,prompt:e,type:r,createdAt:(new Date).toISOString()},...n]))):v(t.message||"Failed to generate content - unexpected response structure")}catch(n){v(n.message||"An error occurred while generating content")}finally{j(!1)}}else v("Please enter a prompt")},className:"p-6 space-y-4 "+(y?"opacity-70 pointer-events-none":""),children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"contentType",className:"block text-sm font-medium text-text-secondary mb-1",children:"Content Type"}),(0,o.jsxs)("select",{id:"contentType",value:r,onChange:e=>p(e.target.value),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base bg-secondary-bg border border-gray-600 text-text-primary focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md",children:[(0,o.jsx)("option",{value:"email",children:"Email Body"}),(0,o.jsx)("option",{value:"subject",children:"Subject Line"}),(0,o.jsx)("option",{value:"headline",children:"Headline"}),(0,o.jsx)("option",{value:"cta",children:"Call to Action"}),(0,o.jsx)("option",{value:"product_description",children:"Product Description"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"prompt",className:"block text-sm font-medium text-text-secondary mb-1",children:"Prompt"}),(0,o.jsx)("textarea",{id:"prompt",rows:4,value:e,onChange:e=>t(e.target.value),placeholder:"Describe what you want to generate...",className:"mt-1 block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 text-text-primary focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"})]}),f&&(0,o.jsx)(s.A,{type:"error",message:f,onClose:()=>v(null)}),(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsx)(a.A,{type:"submit",disabled:y,children:y?"Generating...":"Generate Content"})})]})}),y&&(0,o.jsxs)("div",{className:"flex justify-center items-center p-6",children:[(0,o.jsx)("div",{className:"spinner"})," ",(0,o.jsx)("p",{className:"ml-3 text-text-secondary",children:"Generating content, please wait..."})," "]}),w&&N.length>0&&(0,o.jsx)(l.A,{className:"mb-6",children:(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h2",{className:"text-lg font-medium text-text-primary mb-4",children:"Generation History"}),(0,o.jsx)("div",{className:"space-y-3 max-h-60 overflow-y-auto pr-2",children:N.map(((e,r)=>(0,o.jsxs)("div",{className:"p-3 border border-gray-700 rounded-md hover:bg-gray-700 cursor-pointer",onClick:()=>(e=>{e.generatedContent&&"object"===typeof e.generatedContent?g(e.generatedContent):(console.warn("Selected history item does not have the expected content structure:",e),g(null)),t(e.prompt||""),p(e.contentType||"email"),k(!1)})(e),children:[(0,o.jsx)("p",{className:"text-sm font-medium text-text-primary truncate",children:e.prompt||`Generated content ${r+1}`}),(0,o.jsx)("p",{className:"text-xs text-text-secondary",children:new Date(e.createdAt).toLocaleString()})]},e.id||r)))})]})}),!y&&b&&(0,o.jsx)(l.A,{children:(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,o.jsxs)("h2",{className:"text-lg font-medium text-text-primary",children:["Generated Content: ",b.subject||"No Subject"]}),(0,o.jsx)(a.A,{onClick:async()=>{if(b)try{var t;const r=null===(t=N.find((e=>e.subject===b.subject)))||void 0===t?void 0:t.id;if(!r)return void console.error("Could not find ID for the generated content in history.");const n=e.substring(0,30)+"...";await h.rG.saveContent(r,n),alert("Content saved!")}catch(r){console.error("Error saving content:",r),alert("Failed to save content.")}},variant:"primary",size:"sm",children:"Save Content"})]}),(0,o.jsxs)("div",{className:"border border-gray-700 rounded p-4 bg-gray-900",children:[(0,o.jsx)("h3",{className:"text-md font-semibold mb-3 border-b border-gray-600 pb-2",children:"Email Preview"}),b.blocks&&b.blocks.length>0?b.blocks.map(((e,t)=>{switch(e.type){case"hero_with_cta":case"full_width_cta":return(0,o.jsx)(i,{content:e.content},`${e.type}-${t}`);case"two_column_text_image":case"headline_supporting_text":case"text_with_video":return(0,o.jsx)(u,{content:e.content,type:e.type},`${e.type}-${t}`);case"product_card":return(0,o.jsx)(m,{content:e.content},`${e.type}-${t}`);case"simple_footer":return(0,o.jsx)(c,{content:e.content},`${e.type}-${t}`);case"simple_logo_nav":return(0,o.jsx)(d,{content:e.content},`${e.type}-${t}`);case"quote_testimonial":return(0,o.jsx)(x,{content:e.content},`${e.type}-${t}`);default:return(0,o.jsxs)("div",{className:"mb-4 p-2 border border-dashed border-red-400 rounded bg-gray-800",children:[(0,o.jsxs)("p",{className:"text-xs text-red-300 mb-1",children:["Unsupported Block Type: ",e.type]}),(0,o.jsx)("pre",{className:"text-xs text-gray-400",style:{whiteSpace:"pre-wrap",wordBreak:"break-all"},children:JSON.stringify(e.content||{},null,2)})]},`unknown-${t}`)}})):(0,o.jsx)("p",{className:"text-gray-400 italic",children:"No blocks generated."})]})]})})]})}}}]);
//# sourceMappingURL=410.a874cfbb.chunk.js.map