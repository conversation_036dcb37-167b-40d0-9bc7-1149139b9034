"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[975],{2677:(e,t,a)=>{a.d(t,{A:()=>i});a(5043);var s=a(7304),r=a(6058),l=a(579);s.t1.register(s.PP,s.kc,s.FN,s.No,s.E8,s.Bs,s.hE,s.m_,s.s$);const i=e=>{let{type:t,data:a,options:s,height:i,width:n}=e;const o={responsive:!0,maintainAspectRatio:!0,...s};return(0,l.jsx)("div",{className:"chart-container",children:(()=>{switch(t){case"line":return(0,l.jsx)(r.N1,{data:a,options:o,height:i,width:n});case"bar":return(0,l.jsx)(r.yP,{data:a,options:o,height:i,width:n});case"pie":return(0,l.jsx)(r.Fq,{data:a,options:o,height:i,width:n});default:return(0,l.jsx)("p",{children:"Unsupported chart type"})}})()})}},5975:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h});var s=a(5043),r=a(6213),l=a(1411),i=a(2677),n=a(6517),o=a(9066),c=a(6291),d=a(579);const h=()=>{var e;let t;try{t=(0,o.A)(),console.log("Dashboard: Successfully called useAuth. Value:",t)}catch(f){throw console.error("Dashboard: Error calling useAuth!",f),f}const{user:a}=t,[h,u]=(0,s.useState)(!0),[m,x]=(0,s.useState)(""),[g,p]=(0,s.useState)(null),[b,v]=(0,s.useState)(null);(0,s.useEffect)((()=>{(async()=>{try{const e=await c.A.get("/analytics/campaign-stats",{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});if(!e.data.success)throw new Error(e.data.message||"Failed to fetch campaign stats");v(e.data.data)}catch(f){console.error("Error fetching campaign stats:",f),x("Failed to fetch campaign statistics")}})()}),[]),(0,s.useEffect)((()=>{(async()=>{try{const e=localStorage.getItem("token");if(!e)throw new Error("Authentication token not found.");const t=await c.A.get("/analytics/dashboard",{headers:{Authorization:`Bearer ${e}`}});t.data&&t.data.success&&t.data.data?p(t.data.data):(console.error("Dashboard data fetch successful but response format is unexpected:",t.data),x("Failed to retrieve valid dashboard data."))}catch(f){var e,t;if(console.error("Error fetching dashboard data:",f),r.A.isAxiosError(f))x(`Failed to fetch dashboard data: ${(null===(e=f.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||f.message}`);else f instanceof Error?x(`Failed to fetch dashboard data: ${f.message}`):x("An unknown error occurred while fetching dashboard data.")}finally{u(!1)}})()}),[a]);const j=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),y=g?(()=>{if(!g)return null;return{campaignPerformance:{labels:g.timeAnalytics.map((e=>j(e._id))),datasets:[{label:"Sent",data:g.timeAnalytics.map((e=>e.sent)),backgroundColor:"rgba(59, 130, 246, 0.5)",borderColor:"rgb(59, 130, 246)",borderWidth:1},{label:"Opens",data:g.timeAnalytics.map((e=>e.opens)),backgroundColor:"rgba(75, 192, 192, 0.5)",borderColor:"rgb(75, 192, 192)",borderWidth:1},{label:"Clicks",data:g.timeAnalytics.map((e=>e.clicks)),backgroundColor:"rgba(255, 94, 98, 0.5)",borderColor:"rgb(255, 94, 98)",borderWidth:1}]},engagementRate:{labels:g.timeAnalytics.map((e=>j(e._id))),datasets:[{label:"Open Rate (%)",data:g.timeAnalytics.map((e=>e.sent>0?(e.opens/e.sent*100).toFixed(1):0)),backgroundColor:"rgba(75, 192, 192, 0.5)",borderColor:"rgb(75, 192, 192)",borderWidth:1},{label:"Click Rate (%)",data:g.timeAnalytics.map((e=>e.sent>0?(e.clicks/e.sent*100).toFixed(1):0)),backgroundColor:"rgba(255, 94, 98, 0.5)",borderColor:"rgb(255, 94, 98)",borderWidth:1}]}}})():null;return h?(0,d.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-accent-coral"})}):m?(0,d.jsx)("div",{className:"bg-danger/80 text-white p-4 rounded-md mb-6",children:m}):g?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("h2",{className:"text-3xl font-semibold mb-2",children:["Welcome, ",g.user.name,"!"]}),(0,d.jsxs)("p",{className:"text-text-secondary",children:["You have ",g.user.flowsPurchased," flow",1!==g.user.flowsPurchased?"s":""," available.","active"===(null===(e=g.user.domain)||void 0===e?void 0:e.status)&&(0,d.jsxs)("span",{children:[" Your domain ",(0,d.jsx)("strong",{className:"text-text-primary",children:g.user.domain.name})," is active and ready for sending."]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 p-4 glassmorphic rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg",children:[(0,d.jsx)(n.A,{title:"Total Campaigns",value:g.metrics.totalCampaigns,icon:"chart-bar",details:b?[{label:"Draft",value:b.byStatus.draft},{label:"Scheduled",value:b.byStatus.scheduled},{label:"Sending",value:b.byStatus.sending},{label:"Completed",value:b.byStatus.completed}]:void 0}),(0,d.jsx)(n.A,{title:"Active Campaigns",value:g.metrics.activeCampaigns,icon:"rocket-launch",tooltip:"Campaigns currently sending or scheduled"}),(0,d.jsx)(n.A,{title:"Total Recipients",value:(null===b||void 0===b?void 0:b.totalRecipients)||0,icon:"users",details:b?[{label:"Sent",value:b.totalSent},{label:"Errors",value:b.totalErrors}]:void 0}),(0,d.jsx)(n.A,{title:"Avg. Open Rate",value:`${g.metrics.averageOpenRate.toFixed(1)}%`,icon:"envelope-open"})]}),(null===b||void 0===b?void 0:b.recipients)&&(0,d.jsxs)("div",{className:"mb-8 glassmorphic rounded-lg p-6 transition-all duration-300 ease-in-out hover:shadow-lg",children:[(0,d.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-text-primary",children:"Recipient Statistics"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)(n.A,{title:"Unique Recipients",value:b.recipients.totalUnique,icon:"identification",tooltip:"Total unique recipients in your database",className:"bg-neutral-base/80"}),(0,d.jsx)(n.A,{title:"Delivery Status",value:b.recipients.byStatus.sent,icon:"inbox-arrow-down",tooltip:"Successfully sent emails",details:[{label:"Pending",value:b.recipients.byStatus.pending},{label:"Sent",value:b.recipients.byStatus.sent},{label:"Bounced",value:b.recipients.byStatus.bounced},{label:"Unsubscribed",value:b.recipients.byStatus.unsubscribed}],className:"bg-neutral-base/80"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,d.jsxs)(l.A,{title:"Campaign Performance",className:"transition-all duration-300 ease-in-out hover:shadow-lg",children:[(0,d.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-text-primary",children:"Campaign Performance"}),y&&(0,d.jsx)(i.A,{type:"bar",data:y.campaignPerformance,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"var(--color-text-secondary)"}}},scales:{y:{beginAtZero:!0,grid:{color:"var(--color-border)"},ticks:{color:"var(--color-text-secondary)"}},x:{grid:{display:!1},ticks:{color:"var(--color-text-secondary)"}}}}})]}),(0,d.jsxs)(l.A,{title:"Engagement Rates",className:"transition-all duration-300 ease-in-out hover:shadow-lg",children:[(0,d.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-text-primary",children:"Engagement Rates"}),y&&(0,d.jsx)(i.A,{type:"line",data:y.engagementRate,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{labels:{color:"var(--color-text-secondary)"}}},scales:{y:{beginAtZero:!0,max:100,grid:{color:"var(--color-border)"},ticks:{color:"var(--color-text-secondary)"}},x:{grid:{display:!1},ticks:{color:"var(--color-text-secondary)"}}}}})]})]}),(0,d.jsxs)(l.A,{title:"Recent Campaigns",className:"transition-all duration-300 ease-in-out hover:shadow-lg",children:[(0,d.jsx)("h3",{className:"text-2xl font-semibold mb-4 text-text-primary",children:"Recent Campaigns"}),(0,d.jsx)("div",{className:"table-container",children:(0,d.jsxs)("table",{className:"table w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{scope:"col",children:"Name"}),(0,d.jsx)("th",{scope:"col",children:"Status"}),(0,d.jsx)("th",{scope:"col",children:"Sent"}),(0,d.jsx)("th",{scope:"col",children:"Opens"}),(0,d.jsx)("th",{scope:"col",children:"Clicks"}),(0,d.jsx)("th",{scope:"col",children:"Created"})]})}),(0,d.jsx)("tbody",{children:0===g.recentCampaigns.length?(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:6,className:"text-center py-4 text-text-secondary",children:"No campaigns yet"})}):g.recentCampaigns.map((e=>(0,d.jsxs)("tr",{className:"hover:bg-neutral-light",children:[(0,d.jsx)("td",{children:(0,d.jsx)("a",{href:`/campaigns/edit/${e._id}`,className:"hover:underline",children:e.name})}),(0,d.jsx)("td",{children:(0,d.jsx)("span",{className:"px-2 py-1 text-xs rounded font-medium "+("draft"===e.status?"bg-neutral-light text-text-secondary":"scheduled"===e.status?"bg-primary-blue/80 text-white":"sending"===e.status?"bg-accent-coral/80 text-white":"completed"===e.status?"bg-growth-green/80 text-white":"bg-danger/80 text-white"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,d.jsx)("td",{children:e.sentCount||0}),(0,d.jsx)("td",{children:e.sentCount>0?(0,d.jsxs)(d.Fragment,{children:[e.openCount||0," (",((e.openCount||0)/e.sentCount*100).toFixed(1),"%)"]}):"-"}),(0,d.jsx)("td",{children:e.sentCount>0?(0,d.jsxs)(d.Fragment,{children:[e.clickCount||0," (",((e.clickCount||0)/e.sentCount*100).toFixed(1),"%)"]}):"-"}),(0,d.jsx)("td",{children:j(e.createdAt)})]},e._id)))})]})})]})]}):(0,d.jsx)("div",{className:"text-center py-8",children:(0,d.jsx)("p",{className:"text-text-secondary",children:"No dashboard data available"})})}},6517:(e,t,a)=>{a.d(t,{A:()=>l});a(5043);var s=a(579);const r=e=>{let{name:t}=e;return(0,s.jsx)("i",{className:`placeholder-icon-${t} w-5 h-5`})},l=e=>{let{title:t,value:a,icon:l,change:i,className:n="",tooltip:o,details:c}=e;return(0,s.jsxs)("div",{className:`card container-futuristic flex flex-col ${n}`,children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h3",{className:"stat-label",children:t}),l&&(0,s.jsx)("span",{className:"text-text-secondary opacity-80",children:(0,s.jsx)(r,{name:l})})]}),(0,s.jsx)("div",{className:"stat-value mb-1",children:a}),o&&(0,s.jsx)("div",{className:"text-xs text-text-secondary mt-1 opacity-90",children:o}),i&&(0,s.jsxs)("div",{className:"text-sm mt-2 flex items-center font-medium "+(i.isPositive?"text-growth-green":"text-danger"),children:[(0,s.jsx)("span",{className:"mr-1",children:i.isPositive?(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})}):(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})}),i.value]}),c&&c.length>0&&(0,s.jsx)("div",{className:"mt-auto pt-3 border-t border-border mt-3",children:c.map(((e,t)=>(0,s.jsxs)("div",{className:"flex justify-between text-xs py-1",children:[(0,s.jsx)("span",{className:"text-text-secondary opacity-90",children:e.label}),(0,s.jsx)("span",{className:"font-medium",children:e.value})]},t)))})]})}}}]);
//# sourceMappingURL=975.5fdbce93.chunk.js.map