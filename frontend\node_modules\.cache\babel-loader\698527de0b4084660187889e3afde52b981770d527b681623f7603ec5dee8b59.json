{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\campaigns\\\\CampaignCreate.tsx\",\n  _s = $RefreshSig$();\n// frontend/src/pages/campaigns/CampaignCreate.tsx\n\nimport '../../styles/editor.css';\nimport React, { useEffect, useRef, useState } from 'react';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport HtmlEmailEditor from 'components/HtmlEmailEditor';\nimport Input from 'components/Input';\nimport { Modal } from 'components/Modal';\nimport { useAuth } from 'contexts/AuthContext';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport { templateRecommendationService } from 'services';\nimport api, { campaignAPI } from 'services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CampaignCreate = () => {\n  _s();\n  var _user$domain;\n  const {\n    user\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // Campaign metadata\n  const [campaignName, setCampaignName] = useState('');\n  const [subject, setSubject] = useState('');\n  const [fromName, setFromName] = useState((user === null || user === void 0 ? void 0 : user.name) || '');\n  const [fromEmail, setFromEmail] = useState((user === null || user === void 0 ? void 0 : (_user$domain = user.domain) === null || _user$domain === void 0 ? void 0 : _user$domain.status) === 'active' ? `noreply@${user.domain.name}` : '');\n  const [replyTo, setReplyTo] = useState('');\n\n  // UI state\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [templateLoading, setTemplateLoading] = useState(false);\n  const [step, setStep] = useState(1);\n\n  // Email sequence (up to 10)\n  const [currentEmail, setCurrentEmail] = useState(1);\n  const [emailContents, setEmailContents] = useState(() => {\n    const savedContents = localStorage.getItem('driftly_campaign_create_email_contents');\n    if (savedContents) {\n      try {\n        const parsed = JSON.parse(savedContents);\n        // Convert any old format data to new format (html only)\n        return parsed.map(item => ({\n          html: item.html || item.mjml || ''\n        }));\n      } catch (e) {\n        console.error('Failed to parse saved email contents:', e);\n      }\n    }\n    // Initialize with empty HTML strings\n    return Array.from({\n      length: 10\n    }, () => ({\n      html: ''\n    }));\n  });\n\n  // Scheduling\n  const [scheduleSettings, setScheduleSettings] = useState({\n    intervals: [24],\n    unit: 'hours'\n  });\n\n  // Preview and template picker\n  const [campaignPreview, setCampaignPreview] = useState(null);\n  const [showTemplatePicker, setShowTemplatePicker] = useState(false);\n  const [templatesList, setTemplatesList] = useState([]);\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n\n  // Recipients\n  const [sendScheduleOption, setSendScheduleOption] = useState('later');\n  const [scheduledDateTime, setScheduledDateTime] = useState(() => {\n    const date = new Date(Date.now() + 60 * 60 * 1000);\n    return date.toISOString().slice(0, 16);\n  });\n  const [recipients, setRecipients] = useState([]);\n  const [manualRecipient, setManualRecipient] = useState({\n    email: '',\n    name: ''\n  });\n  const [contacts, setContacts] = useState([]);\n  const [selectedContactIds, setSelectedContactIds] = useState([]);\n  const [loadingContacts, setLoadingContacts] = useState(false);\n\n  // Ref to MJML editor\n  const editorRef = useRef(null);\n\n  // State to track which emails are actively selected for sending\n  const [activeEmailIndices, setActiveEmailIndices] = useState(() => {\n    // Initialize based on emails that already have HTML content\n    const initialActive = [];\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    let initialContents = Array.from({\n      length: 10\n    }, () => ({\n      html: ''\n    }));\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          initialContents = arr.map(item => ({\n            html: item.html || ''\n          }));\n        }\n      } catch {}\n    }\n    initialContents.forEach((email, index) => {\n      if (email.html && email.html.trim()) {\n        initialActive.push(index);\n      }\n    });\n    return initialActive;\n  });\n\n  // Handle URL param for templateId\n  const queryParams = new URLSearchParams(location.search);\n  const templateId = queryParams.get('templateId');\n  useEffect(() => {\n    if (templateId) fetchTemplate(templateId);\n  }, [templateId]);\n\n  // Fetch AI or saved template by ID\n  const fetchTemplate = async id => {\n    setTemplateLoading(true);\n    try {\n      const res = await templateRecommendationService.getTemplateById(id);\n      if (res.success && res.template) {\n        const temp = res.template;\n        if (!campaignName) setCampaignName(`Campaign based on ${temp.name}`);\n        const updated = [...emailContents];\n        updated[0] = {\n          html: temp.content || ''\n        };\n        setEmailContents(updated);\n      } else {\n        setError('Failed to load template.');\n      }\n    } catch {\n      setError('Failed to load template.');\n    } finally {\n      setTemplateLoading(false);\n    }\n  };\n\n  // Save handler from MJML editor\n  const handleHtmlSave = html => {\n    // Update HTML content for current email\n    const updated = [...emailContents];\n    const currentIndex = currentEmail - 1;\n    updated[currentIndex] = {\n      html\n    };\n    setEmailContents(updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n\n    // If saving resulted in valid HTML, ensure this email is marked active\n    if (html && html.trim()) {\n      setActiveEmailIndices(prev => {\n        if (!prev.includes(currentIndex)) {\n          // Add if not already present, keep sorted for consistency\n          return [...prev, currentIndex].sort((a, b) => a - b);\n        }\n        return prev; // Already active, no change needed\n      });\n      console.log(`Ensured email index ${currentIndex} is active after save.`);\n    } else {\n      // If saving resulted in empty HTML, ensure it's deactivated\n      setActiveEmailIndices(prev => prev.filter(i => i !== currentIndex));\n      console.log(`Deactivated email index ${currentIndex} after save due to empty HTML.`);\n    }\n  };\n\n  // Template picker effects and handlers\n  useEffect(() => {\n    if (showTemplatePicker) {\n      setLoadingTemplates(true);\n      templateRecommendationService.getAllTemplates().then(r => setTemplatesList(r.data || [])).catch(console.error).finally(() => setLoadingTemplates(false));\n    }\n  }, [showTemplatePicker]);\n  const handleTemplateSelect = async id => {\n    setLoadingTemplates(true);\n    try {\n      console.log(`[Debug] Selecting template with ID: ${id}`);\n      const r = await templateRecommendationService.getTemplateById(id);\n      console.log('[Debug] Template API response:', r);\n      if (r.success && r.template) {\n        var _r$template$content;\n        console.log('[Debug] Setting selected template:', {\n          id: r.template.id || r.template._id,\n          name: r.template.name,\n          hasMjml: !!r.template.mjmlContent,\n          hasContent: !!r.template.content,\n          contentLength: ((_r$template$content = r.template.content) === null || _r$template$content === void 0 ? void 0 : _r$template$content.length) || 0\n        });\n      }\n      setSelectedTemplate(r.template);\n    } catch (err) {\n      console.error('[Debug] Error selecting template:', err);\n      setError('Failed to load selected template.');\n    } finally {\n      setLoadingTemplates(false);\n    }\n  };\n  const handleUseTemplate = () => {\n    console.log('[Debug] Using selected template:', selectedTemplate ? {\n      id: selectedTemplate.id || selectedTemplate._id,\n      name: selectedTemplate.name,\n      hasMjml: !!selectedTemplate.mjmlContent,\n      hasContent: !!selectedTemplate.content\n    } : 'No template selected');\n    if (selectedTemplate) {\n      const updated = [...emailContents];\n      updated[currentEmail - 1] = {\n        html: selectedTemplate.content || ''\n      };\n      console.log('[Debug] Updated email contents:', {\n        emailIndex: currentEmail - 1,\n        hasMjml: !!updated[currentEmail - 1].html,\n        hasHtml: !!updated[currentEmail - 1].html,\n        mjmlLength: updated[currentEmail - 1].html.length\n      });\n\n      // Save the updated contents to state and local storage\n      setEmailContents(updated);\n      localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n\n      // Close the modal first to reduce complexity\n      setSelectedTemplate(null);\n      setShowTemplatePicker(false);\n\n      // Force a complete re-render of the editor component\n      // Wait a moment to ensure state updates have completed\n      setTimeout(() => {\n        // This will force the MjmlEditor to completely remount with the new content\n        // Temporarily set current email to a value that won't match any existing email\n        setCurrentEmail(-1);\n\n        // After a brief delay, restore the current email index\n        setTimeout(() => {\n          setCurrentEmail(currentEmail);\n        }, 50);\n      }, 100);\n    }\n  };\n\n  // Campaign creation\n  const handleCreateCampaign = async () => {\n    if (!campaignName || !subject || !fromName || !fromEmail) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    const validEmails = emailContents.filter(e => e.html.trim());\n    if (validEmails.length === 0) {\n      setError('Please add at least one email with valid HTML content');\n      return;\n    }\n    if (recipients.length === 0) {\n      setError('Please add at least one recipient');\n      return;\n    }\n    setLoading(true);\n    try {\n      var _activeEmailsToSend$;\n      // Filter emails with actual HTML content AND that are marked active\n      const activeEmailsToSend = emailContents.filter((email, index) => activeEmailIndices.includes(index) && email.html && email.html.trim());\n\n      // Validation based on filtered list\n      if (activeEmailsToSend.length === 0) {\n        setError('Please select at least one email with valid HTML content to send.');\n        setLoading(false);\n        return;\n      }\n      if (recipients.length === 0) {\n        setError('Please add at least one recipient');\n        setLoading(false);\n        return;\n      }\n      // --- End new validation ---\n\n      // Construct the base campaign data\n      const campaignData = {\n        name: campaignName,\n        subject,\n        fromName,\n        fromEmail,\n        replyTo: replyTo || fromEmail,\n        scheduled: sendScheduleOption === 'later',\n        ...(sendScheduleOption === 'later' && {\n          scheduledFor: new Date(scheduledDateTime).toISOString()\n        }),\n        recipientList: recipients,\n        userId: user === null || user === void 0 ? void 0 : user.id,\n        status: 'draft',\n        // Use the first ACTIVE email for top-level content\n        htmlContent: ((_activeEmailsToSend$ = activeEmailsToSend[0]) === null || _activeEmailsToSend$ === void 0 ? void 0 : _activeEmailsToSend$.html) || '',\n        // Send ONLY the emails that are ACTIVE and have content\n        emailContents: activeEmailsToSend.map(e => ({\n          html: e.html\n        }))\n      };\n\n      // Add schedule only if there's more than one ACTIVE email with content\n      if (activeEmailsToSend.length > 1) {\n        campaignData.schedule = {\n          unit: scheduleSettings.unit,\n          // Slice intervals to match the number of gaps between ACTIVE emails\n          emailIntervals: scheduleSettings.intervals.slice(0, activeEmailsToSend.length - 1).map(d => ({\n            delay: d,\n            unit: scheduleSettings.unit\n          }))\n        };\n      }\n      console.log(\"Sending filtered & active campaign data:\", campaignData);\n\n      // Send the filtered data to the API\n      const res = await campaignAPI.createCampaign(campaignData);\n      // Check for _id primarily, then id\n      const campaignId = (res === null || res === void 0 ? void 0 : res._id) || (res === null || res === void 0 ? void 0 : res.id);\n      console.log('Create campaign response:', res, 'Extracted ID:', campaignId);\n      setSuccess(sendScheduleOption === 'later' ? `Campaign scheduled for ${new Date(scheduledDateTime).toLocaleString()}` : 'Campaign created and will start sending shortly');\n      localStorage.removeItem('driftly_campaign_create_email_contents');\n      // Clear draft info if it exists\n      localStorage.removeItem('driftly_campaign_draft');\n      sessionStorage.setItem('reloadCampaigns', 'true');\n      setTimeout(() => {\n        // Navigate using the extracted ID\n        if (campaignId) navigate(`/campaigns/${campaignId}/summary`);else {\n          console.warn('No ID found in createCampaign response, navigating to list.');\n          navigate('/campaigns');\n        }\n      }, 1500);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBrowseTemplates = () => {\n    if (campaignName || subject || fromName || fromEmail || replyTo) {\n      localStorage.setItem('driftly_campaign_draft', JSON.stringify({\n        campaignName,\n        subject,\n        fromName,\n        fromEmail,\n        replyTo\n      }));\n    }\n    navigate('/email-templates');\n  };\n  useEffect(() => {\n    const draft = localStorage.getItem('driftly_campaign_draft');\n    if (draft) {\n      try {\n        const d = JSON.parse(draft);\n        setCampaignName(d.campaignName || '');\n        setSubject(d.subject || '');\n        setFromName(d.fromName || (user === null || user === void 0 ? void 0 : user.name) || '');\n        setFromEmail(d.fromEmail || '');\n        setReplyTo(d.replyTo || '');\n      } catch {}\n      localStorage.removeItem('driftly_campaign_draft');\n    }\n  }, [user]);\n  const handleEmailSelect = i => setCurrentEmail(i);\n  useEffect(() => {\n    if (step === 3) {\n      // Count emails that are BOTH active AND have HTML\n      const activeEmailCount = emailContents.reduce((count, email, index) => {\n        if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n          return count + 1;\n        }\n        return count;\n      }, 0);\n      console.log('Active emails with HTML for scheduling:', activeEmailCount);\n      if (activeEmailCount > 1) {\n        setScheduleSettings(prev => ({\n          ...prev,\n          // Ensure intervals array matches the number of gaps between ACTIVE emails\n          intervals: Array(activeEmailCount - 1).fill(prev.intervals[0] || 24)\n        }));\n      } else {\n        // If 0 or 1 active emails, no intervals needed\n        setScheduleSettings(prev => ({\n          ...prev,\n          intervals: []\n        }));\n      }\n    }\n  }, [step, emailContents, activeEmailIndices]); // Add activeEmailIndices dependency\n\n  const fetchContacts = async () => {\n    if (step === 4) {\n      setLoadingContacts(true);\n      try {\n        var _r$data;\n        const r = await api.get('/contacts');\n        let list = Array.isArray(r.data) ? r.data : ((_r$data = r.data) === null || _r$data === void 0 ? void 0 : _r$data.data) || [];\n        setContacts(list.map(c => ({\n          id: c.id || c._id || String(Math.random()),\n          email: c.email,\n          name: c.name || c.fullName || ''\n        })));\n      } catch {\n        setError('Failed to load contacts');\n      } finally {\n        setLoadingContacts(false);\n      }\n    }\n  };\n  useEffect(() => {\n    fetchContacts();\n  }, [step]);\n  const addManualRecipient = () => {\n    if (!manualRecipient.email) {\n      setError('Please enter an email address.');\n    } else if (!recipients.some(r => r.email === manualRecipient.email)) {\n      setRecipients([...recipients, {\n        id: null,\n        ...manualRecipient\n      }]);\n      setManualRecipient({\n        email: '',\n        name: ''\n      });\n    } else {\n      setError('Recipient already added.');\n    }\n    setTimeout(() => setError(''), 3000);\n  };\n  const addSelectedContacts = () => {\n    const toAdd = contacts.filter(c => selectedContactIds.includes(c.id) && !recipients.some(r => r.email === c.email));\n    setRecipients([...recipients, ...toAdd]);\n    setSelectedContactIds([]);\n  };\n  const handleContactSelection = e => setSelectedContactIds(Array.from(e.target.selectedOptions, o => o.value));\n  const removeRecipient = email => setRecipients(recipients.filter(r => r.email !== email));\n\n  // Step navigation\n  const handleNext = async () => {\n    setError('');\n\n    // Validation for Step 1\n    if (step === 1 && (!campaignName || !subject || !fromName || !fromEmail)) {\n      setError('Please fill in all required fields.');\n      return;\n    }\n\n    // Validation for Step 2\n    if (step === 2) {\n      console.log('--- handleNext: Step 2 Validation ---');\n      let latestContent = null;\n      let savedContentHasHtml = false;\n      let stateHasHtml = false;\n\n      // --- Force save current editor's content FIRST ---\n      if (editorRef.current) {\n        console.log('Forcing editor save for current email:', currentEmail);\n        // Await the result of the now async save method\n        latestContent = await editorRef.current.save();\n        if (latestContent) {\n          console.log('Async save function returned content.');\n          // Log the returned HTML content (or lack thereof)\n          console.log('Returned HTML:', latestContent.html ? latestContent.html.substring(0, 50) + '...' : '(empty)');\n\n          // Check if the *returned* content has HTML\n          if (latestContent.html && latestContent.html.trim()) {\n            console.log('RETURNED content has non-empty HTML.');\n            savedContentHasHtml = true;\n          } else {\n            console.log('RETURNED content has empty or no HTML.');\n          }\n\n          // Trigger state update (async) - Allow this to proceed even if HTML is missing\n          console.log('Triggering handleHtmlSave state update...');\n          handleHtmlSave(latestContent.html);\n        } else {\n          // This case might be less likely now if save always returns an object\n          console.warn('editorRef.current.save() did not return expected content object.');\n        }\n      } else {\n        console.error('HtmlEmailEditor ref is not available for forced save.');\n      }\n      // --- End Force save ---\n\n      // --- Validation ---\n      // Since save is now awaited and includes a delay, checking the returned content\n      // (savedContentHasHtml) should be more reliable. We also check state.\n\n      // Check if the SAVED content belongs to an index currently marked ACTIVE\n      const savedContentIsActive = savedContentHasHtml && activeEmailIndices.includes(currentEmail - 1);\n      if (savedContentIsActive) {\n        console.log('Saved content is for an active email.');\n      }\n      console.log('Checking emailContents state AFTER awaiting save/update attempt...');\n      // Check if ANY email in state is BOTH active AND has HTML\n      stateHasHtml = emailContents.some((email, index) => {\n        const hasHtml = email.html && email.html.trim();\n        const isActive = activeEmailIndices.includes(index);\n        if (hasHtml && isActive) {\n          console.log(`Email index ${index} in STATE is ACTIVE and has HTML.`);\n          return true;\n        }\n        return false;\n      });\n      if (!stateHasHtml) {\n        console.log('No email in STATE is both active and has HTML.');\n      }\n\n      // Final decision: Did *either* the direct save (if active) *or* the state check find an ACTIVE email with HTML?\n      const finalHasHtmlCheck = savedContentIsActive || stateHasHtml;\n      console.log('Final check result (Active & HTML):', finalHasHtmlCheck, `(Saved Active: ${savedContentIsActive}, State Active: ${stateHasHtml})`);\n      if (!finalHasHtmlCheck) {\n        setError('Please ensure at least one selected email (marked as \\'Sending\\') has valid HTML content.');\n        return; // Stop execution if validation fails specifically for step 2\n      }\n      console.log('Step 2 validation passed (Active HTML check).');\n    }\n\n    // Validation for Step 3\n    if (step === 3) {\n      const cnt = emailContents.filter(e => e.html.trim()).length;\n      if (cnt > 1) {\n        if (scheduleSettings.intervals.length !== cnt - 1) {\n          setError(`Define ${cnt - 1} intervals.`);\n          return;\n        }\n        if (scheduleSettings.intervals.some(v => v <= 0)) {\n          setError('Intervals must be positive.');\n          return;\n        }\n      }\n    }\n\n    // Validation for Step 4\n    if (step === 4 && recipients.length === 0) {\n      setError('Please add at least one recipient.');\n      return;\n    }\n\n    // Proceed to next step if all relevant validations passed and not on the last step\n    if (step < 5) {\n      console.log(`Proceeding from step ${step} to step ${step + 1}`);\n      setStep(step + 1);\n    }\n  };\n  const handlePrevious = () => {\n    setError('');\n    if (step > 1) setStep(step - 1);\n  };\n\n  // --- Add this handler ---\n  const handleClearEmailContent = index => {\n    const updated = [...emailContents];\n    updated[index] = {\n      html: ''\n    };\n    setEmailContents(updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n    // Also remove from active indices when cleared\n    setActiveEmailIndices(prev => prev.filter(i => i !== index));\n    console.log(`Cleared content and deactivated email index ${index}`);\n  };\n  // --- End Add Handler ---\n\n  const renderTemplatePicker = () => /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: showTemplatePicker,\n    onClose: () => setShowTemplatePicker(false),\n    title: \"Choose Template\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\",\n      children: loadingTemplates ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 13\n      }, this) : templatesList.map(t => /*#__PURE__*/_jsxDEV(Card, {\n        className: `cursor-pointer ${(selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.id) === t.id ? 'ring-2 ring-primary' : ''}`,\n        onClick: () => handleTemplateSelect(t.id),\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"font-semibold mb-2 truncate\",\n          children: t.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\",\n          children: t.thumbnailUrl ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: t.thumbnailUrl,\n            alt: t.name,\n            className: \"object-contain h-full w-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 35\n          }, this) : 'No Preview'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 15\n        }, this)]\n      }, t.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 flex justify-end gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: () => setShowTemplatePicker(false),\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleUseTemplate,\n        disabled: !selectedTemplate || loadingTemplates,\n        children: \"Use Selected\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 595,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 575,\n    columnNumber: 5\n  }, this);\n  const renderStepContent = () => {\n    var _user$domain2, _user$domain3;\n    switch (step) {\n      case 1:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            id: \"campaignName\",\n            name: \"campaignName\",\n            label: \"Campaign Name\",\n            value: campaignName,\n            onChange: e => setCampaignName(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"subject\",\n            name: \"subject\",\n            label: \"Email Subject\",\n            value: subject,\n            onChange: e => setSubject(e.target.value),\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              id: \"fromName\",\n              name: \"fromName\",\n              label: \"From Name\",\n              value: fromName,\n              onChange: e => setFromName(e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Input, {\n              id: \"fromEmail\",\n              name: \"fromEmail\",\n              label: \"From Email\",\n              type: \"email\",\n              value: fromEmail,\n              onChange: e => setFromEmail(e.target.value),\n              disabled: (user === null || user === void 0 ? void 0 : (_user$domain2 = user.domain) === null || _user$domain2 === void 0 ? void 0 : _user$domain2.status) === 'active',\n              required: true,\n              helpText: (user === null || user === void 0 ? void 0 : (_user$domain3 = user.domain) === null || _user$domain3 === void 0 ? void 0 : _user$domain3.status) === 'active' ? `Using verified domain: ${user.domain.name}` : 'Verify domain for deliverability.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            id: \"replyTo\",\n            name: \"replyTo\",\n            label: \"Reply-To Email (optional)\",\n            type: \"email\",\n            value: replyTo,\n            onChange: e => setReplyTo(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 9\n        }, this);\n      case 2:\n        const curr = emailContents[currentEmail - 1];\n        // Use stricter validation: check for non-empty HTML\n        const emailsWithContent = emailContents.filter(e => e.html.trim()).length;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-4 h-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full lg:w-1/5 flex flex-col gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold mb-2\",\n              children: \"Email Sequence (Click to include/exclude):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this), Array.from({\n              length: 10\n            }).map((_, idx) => {\n              const hasHtmlContent = !!(emailContents[idx].html && emailContents[idx].html.trim());\n              const isSelected = currentEmail === idx + 1;\n              const isActive = activeEmailIndices.includes(idx);\n\n              // Determine button appearance based on content and active state\n              let buttonVariant = \"secondary\";\n              let customClasses = \"\";\n              let buttonText = `Email ${idx + 1}`;\n              let icon = null;\n              if (hasHtmlContent) {\n                if (isActive) {\n                  buttonVariant = \"primary\"; // Has content, is active\n                  icon = /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 text-green-500 dark:text-green-400 font-bold\",\n                    children: \"\\u2713\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 28\n                  }, this);\n                  buttonText += \" (Sending)\";\n                } else {\n                  buttonVariant = \"secondary\"; // Has content, but inactive - use secondary variant\n                  customClasses = \"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\"; // Custom styling for inactive\n                  icon = /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 text-gray-500 dark:text-gray-400 font-bold\",\n                    children: \"\\u23F8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 28\n                  }, this); // Pause icon maybe?\n                  buttonText += \" (Excluded)\";\n                }\n              } else {\n                // No content, always secondary, cannot be active\n                // Make it look distinct but clickable\n                customClasses = \"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\";\n                buttonText += \" (No Content - Click to Edit)\";\n              }\n              const selectionRingClass = isSelected ? \"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\" : \"\";\n              return /*#__PURE__*/_jsxDEV(Button, {\n                variant: buttonVariant // Will be 'secondary' for no content\n                ,\n                onClick: () => {\n                  // Set as current email for editing - ALWAYS DO THIS\n                  setCurrentEmail(idx + 1);\n\n                  // Toggle active state ONLY if it has content\n                  if (hasHtmlContent) {\n                    setActiveEmailIndices(prev => prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx]);\n                  }\n                },\n                size: \"sm\",\n                className: `w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses}`,\n                title: hasHtmlContent ? isActive ? \"Click to exclude from sending\" : \"Click to include in sending\" : \"Click to edit this email\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-grow truncate\",\n                  children: buttonText\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 685,\n                  columnNumber: 21\n                }, this), icon]\n              }, idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 667,\n                columnNumber: 19\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium mb-1\",\n                children: \"Email Status Legend:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-1 mr-2 text-green-500 dark:text-green-400 font-bold\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 695,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-green-700 dark:text-green-400\",\n                  children: \"Has Content, Will Send\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 696,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-block w-3 h-3 mr-2 bg-gray-400 dark:bg-gray-500 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-700 dark:text-gray-300\",\n                  children: [\"Has Content, \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-bold\",\n                    children: \"Excluded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 700,\n                    columnNumber: 83\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-block w-3 h-3 mr-2 border border-dashed border-gray-400 dark:border-gray-600 rounded-full opacity-75\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 703,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-700 dark:text-gray-300\",\n                  children: \"No Content (Click to Edit)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs mt-2 text-gray-600 dark:text-gray-400\",\n                children: \"Click emails with content to toggle inclusion. Click any email to edit.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 18\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: () => setShowTemplatePicker(true),\n                children: \"Choose Template\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                onClick: handleBrowseTemplates,\n                className: \"mt-2\",\n                children: \"Browse All Templates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full lg:w-4/5 flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(HtmlEmailEditor, {\n              ref: editorRef,\n              initialHtml: curr.html,\n              onSave: handleHtmlSave,\n              height: \"60vh\"\n            }, `email-${currentEmail}-${Date.now()}`, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 dark:text-gray-400 mt-2\",\n              children: \"Changes are saved automatically. Emails with a green check mark (\\u2713) will be included in your campaign.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 723,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 715,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this);\n      case 3:\n        {\n          // Calculate count based on emails that are BOTH active AND have HTML\n          const activeEmailCount = emailContents.reduce((count, email, index) => {\n            if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n              return count + 1;\n            }\n            return count;\n          }, 0);\n          console.log('Active emails with HTML for rendering Step 3:', activeEmailCount);\n\n          // Handlers for main scheduling options\n          const handleScheduleOptionChange = e => {\n            setSendScheduleOption(e.target.value);\n          };\n          const handleScheduleTimeChange = e => {\n            setScheduledDateTime(e.target.value);\n          };\n\n          // Handlers for interval settings (only relevant if emailCount > 1)\n          const handleIntervalChange = (index, value) => {\n            const numValue = parseInt(value, 10);\n            if (!isNaN(numValue) && numValue > 0) {\n              const newIntervals = [...scheduleSettings.intervals];\n              newIntervals[index] = numValue;\n              setScheduleSettings({\n                ...scheduleSettings,\n                intervals: newIntervals\n              });\n            }\n          };\n          const handleUnitChange = e => {\n            setScheduleSettings({\n              ...scheduleSettings,\n              unit: e.target.value\n            });\n          };\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: \"Campaign Start Time\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 764,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"scheduleNow\",\n                    name: \"scheduleOption\",\n                    type: \"radio\",\n                    value: \"now\",\n                    checked: sendScheduleOption === 'now',\n                    onChange: handleScheduleOptionChange,\n                    className: \"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"scheduleNow\",\n                    className: \"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                    children: \"Send Immediately\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 776,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"scheduleLater\",\n                    name: \"scheduleOption\",\n                    type: \"radio\",\n                    value: \"later\",\n                    checked: sendScheduleOption === 'later',\n                    onChange: handleScheduleOptionChange,\n                    className: \"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 781,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"scheduleLater\",\n                    className: \"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                    children: \"Schedule for Later\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 780,\n                  columnNumber: 17\n                }, this), sendScheduleOption === 'later' && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pl-7 mt-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Input, {\n                    id: \"scheduledDateTime\",\n                    name: \"scheduledDateTime\",\n                    type: \"datetime-local\",\n                    value: scheduledDateTime,\n                    onChange: handleScheduleTimeChange,\n                    label: \"Scheduled Date & Time\",\n                    required: true,\n                    className: \"max-w-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 796,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                    children: \"Your local timezone.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 22\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 765,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 763,\n              columnNumber: 13\n            }, this), activeEmailCount > 1 && /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold mb-4\",\n                children: \"Email Sequence Timing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 815,\n                columnNumber: 18\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                children: \"Set the time interval between each subsequent email send. The first email is sent according to the 'Campaign Start Time' setting above.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 18\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-4 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"scheduleUnit\",\n                  className: \"block text-sm font-medium text-gray-700 dark:text-gray-300\",\n                  children: \"Interval Time Unit:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 20\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"scheduleUnit\",\n                  name: \"scheduleUnit\",\n                  value: scheduleSettings.unit,\n                  onChange: handleUnitChange,\n                  className: \"mt-1 block w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"minutes\",\n                    children: \"Minutes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 830,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"hours\",\n                    children: \"Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 831,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"days\",\n                    children: \"Days\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 22\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 823,\n                  columnNumber: 20\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 18\n              }, this), Array.from({\n                length: activeEmailCount - 1\n              }).map((_, index) => {\n                var _scheduleSettings$int;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4 mt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: `interval-${index}`,\n                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 w-48\",\n                    children: [\"Wait before sending Email #\", index + 2, \":\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(Input, {\n                    id: `interval-${index}`,\n                    name: `interval-${index}`,\n                    type: \"number\",\n                    value: ((_scheduleSettings$int = scheduleSettings.intervals[index]) === null || _scheduleSettings$int === void 0 ? void 0 : _scheduleSettings$int.toString()) || '24',\n                    onChange: e => handleIntervalChange(index, e.target.value),\n                    className: \"w-24 mb-0\" // Adjusted margin\n                    ,\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 22\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600 dark:text-gray-400 capitalize\",\n                    children: scheduleSettings.unit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 850,\n                    columnNumber: 22\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 20\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 15\n            }, this), activeEmailCount <= 1 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-400 text-sm px-4\",\n              children: \"Sequence timing options are available when your campaign has more than one selected email with content.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 18\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 11\n          }, this);\n        }\n      case 4:\n        // Add Recipients Step\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Add Recipients\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-3 text-text-primary\",\n              children: \"Add Manually\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:flex md:items-end md:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-grow mb-3 md:mb-0\",\n                children: /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"manualRecipient\",\n                  name: \"manualRecipient\",\n                  label: \"Email Address\",\n                  type: \"email\",\n                  value: manualRecipient.email,\n                  onChange: e => setManualRecipient({\n                    ...manualRecipient,\n                    email: e.target.value\n                  }),\n                  placeholder: \"Enter email address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: addManualRecipient,\n                variant: \"secondary\",\n                className: \"btn-cta mt-4 md:mt-0\",\n                children: \"Add Recipient\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-3 text-text-primary\",\n              children: \"Add From Contacts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this), loadingContacts ? /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Loading contacts...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 17\n            }, this) : contacts.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:flex md:items-end md:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-grow mb-3 md:mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"contactSelect\",\n                  className: \"block text-sm font-medium text-text-secondary mb-1\",\n                  children: \"Select Contacts (use Ctrl/Cmd to select multiple)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  id: \"contactSelect\",\n                  multiple: true,\n                  value: selectedContactIds,\n                  onChange: handleContactSelection,\n                  className: \"form-input w-full h-32 border border-border rounded-md\",\n                  children: contacts.map(contact => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: contact.id,\n                    children: contact.name ? `${contact.name} (${contact.email})` : contact.email\n                  }, contact.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: addSelectedContacts,\n                variant: \"secondary\",\n                className: \"btn-cta mt-4 md:mt-0\",\n                children: \"Add Selected\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 897,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary\",\n              children: \"No contacts found. You can add contacts in the 'Contacts' section.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-medium mb-3 text-text-primary\",\n              children: [\"Recipients Added (\", recipients.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 15\n            }, this), recipients.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"divide-y divide-border max-h-60 overflow-y-auto\",\n              children: recipients.map((rec, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"py-2 flex justify-between items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: rec.name ? `${rec.name} (${rec.email})` : rec.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 930,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => removeRecipient(rec.email),\n                  className: \"text-red-500 hover:text-red-700 text-sm\",\n                  children: \"\\xD7 Remove\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 929,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary\",\n              children: \"No recipients added yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 924,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 11\n        }, this);\n      case 5:\n        {\n          // Review and Schedule Step\n          const scheduledTimeString = sendScheduleOption === 'later' && scheduledDateTime ? new Date(scheduledDateTime).toLocaleString() : 'Immediately';\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold mb-4\",\n              children: \"Review & Schedule\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: \"Campaign Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 19\n                }, this), \" \", campaignName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 952,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Subject:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 953,\n                  columnNumber: 19\n                }, this), \" \", subject]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 953,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"From:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 19\n                }, this), \" \", fromName, \" <\", fromEmail, \">\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 16\n              }, this), replyTo && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Reply To:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 31\n                }, this), \" \", replyTo]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 28\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: \"Sequence (Emails to Send)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 959,\n                columnNumber: 16\n              }, this), emailContents.filter((e, idx) => activeEmailIndices.includes(idx) && e.html.trim()).length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-decimal pl-5 space-y-1\",\n                children: emailContents.map((email, index) => {\n                  // Use stricter validation: check for non-empty HTML AND active status\n                  if (activeEmailIndices.includes(index) && email.html.trim()) {\n                    // Find the index of this email within the *filtered* list of active emails\n                    const activeEmails = emailContents.map((e, i) => ({\n                      email: e,\n                      originalIndex: i\n                    })) // Keep track of original index\n                    .filter(({\n                      email,\n                      originalIndex\n                    }) => activeEmailIndices.includes(originalIndex) && email.html.trim());\n                    const activeIndex = activeEmails.findIndex(item => item.originalIndex === index);\n                    const emailNumberInActiveSequence = activeIndex + 1; // 1-based index for display\n\n                    let timingText = 'Sent at Campaign Start Time'; // Default text\n                    // Calculate timing based on the position in the *active* sequence\n                    if (emailNumberInActiveSequence > 1 && scheduleSettings.intervals[emailNumberInActiveSequence - 2] !== undefined) {\n                      timingText = `Sent ${scheduleSettings.intervals[emailNumberInActiveSequence - 2]} ${scheduleSettings.unit} after Email #${activeEmails[activeIndex - 1].originalIndex + 1}`;\n                    }\n                    return /*#__PURE__*/_jsxDEV(\"li\", {\n                      children: [\"Email #\", index + 1, \" - \", timingText]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 984,\n                      columnNumber: 28\n                    }, this);\n                  }\n                  return null; // Don't render anything for inactive/empty emails\n                }).filter(Boolean) // Remove null entries from the map result\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 18\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-text-secondary\",\n                children: \"No emails selected or content found for the sequence.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 995,\n                columnNumber: 18\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: \"Recipients\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [recipients.length, \" recipient(s)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 16\n              }, this), recipients.length > 0 && /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"list-disc pl-5 text-sm max-h-20 overflow-y-auto\",\n                children: [recipients.slice(0, 5).map((r, i) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: r.name ? `${r.name} (${r.email})` : r.email\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 58\n                }, this)), recipients.length > 5 && /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [\"...and \", recipients.length - 5, \" more\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1005,\n                  columnNumber: 46\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 18\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-medium mb-2 text-text-primary\",\n                children: \"Schedule\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1011,\n                columnNumber: 16\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Start sending: \", scheduledTimeString]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 16\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1010,\n              columnNumber: 14\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-6 flex justify-end\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                onClick: handleCreateCampaign,\n                disabled: loading,\n                className: \"btn-cta\" // Apply Coral CTA style\n                ,\n                children: loading ? 'Creating...' : 'Create & Schedule Campaign'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 16\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1015,\n              columnNumber: 14\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 947,\n            columnNumber: 12\n          }, this) // Closing the main div for step 5\n          ; // Closing the return statement for case 5\n        }\n      // Closing the switch statement\n      default:\n        return null;\n    } // Closing the outer switch\n  }; // Closing renderStepContent\n\n  // --- Main Component Render ---\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-2xl font-bold mb-6 text-text-primary\",\n      children: \"Create New Campaign\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1035,\n      columnNumber: 8\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1036,\n        columnNumber: 98\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1036,\n      columnNumber: 18\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1037,\n        columnNumber: 106\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1037,\n      columnNumber: 20\n    }, this), templateLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 text-text-secondary\",\n      children: \"Loading template...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1038,\n      columnNumber: 28\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"ol\", {\n        className: \"flex items-center w-full text-sm font-medium text-center text-text-secondary dark:text-gray-400 sm:text-base\",\n        children: [1, 2, 3, 4, 5].map(s => /*#__PURE__*/_jsxDEV(\"li\", {\n          className: `flex md:w-full items-center ${s === step ? 'text-accent-coral dark:text-blue-500' : ''} ${s < 5 ? 'sm:after:content-[\\'\\'] after:w-full after:h-1 after:border-b after:border-border dark:after:border-gray-700 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-text-disabled ${s < step ? 'text-text-primary dark:text-gray-200' : ''}`,\n            children: [s < step && /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2.5\",\n              \"aria-hidden\": \"true\",\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"currentColor\",\n              viewBox: \"0 0 20 20\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 22\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 20\n            }, this), s === step && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: s\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 33\n            }, this), s > step && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: s\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1052,\n              columnNumber: 31\n            }, this), ['Details', 'Content', 'Schedule', 'Recipients', 'Review'][s - 1]]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 16\n          }, this)\n        }, s, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1044,\n          columnNumber: 14\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 10\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1041,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-6\",\n      children: renderStepContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1061,\n      columnNumber: 8\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between mt-8\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: handlePrevious,\n        disabled: step === 1 || loading,\n        variant: \"secondary\",\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1067,\n        columnNumber: 10\n      }, this), step < 5 ? /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleNext,\n        disabled: loading,\n        variant: \"primary\",\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1069,\n        columnNumber: 12\n      }, this) : /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCreateCampaign,\n        disabled: loading,\n        className: \"btn-cta\",\n        children: [\" \", loading ? 'Creating...' : 'Create & Schedule Campaign']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1071,\n        columnNumber: 12\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1066,\n      columnNumber: 8\n    }, this), renderTemplatePicker()]\n  }, void 0, true);\n}; // Closing the component function\n_s(CampaignCreate, \"bJ2ePjf2TH1i26XnrLLM0TDt4hM=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c = CampaignCreate;\nexport default CampaignCreate; // Export the component\nvar _c;\n$RefreshReg$(_c, \"CampaignCreate\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "<PERSON><PERSON>", "Card", "HtmlEmailEditor", "Input", "Modal", "useAuth", "useLocation", "useNavigate", "templateRecommendationService", "api", "campaignAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CampaignCreate", "_s", "_user$domain", "user", "location", "navigate", "campaignName", "setCampaignName", "subject", "setSubject", "fromName", "setFromName", "name", "fromEmail", "setFromEmail", "domain", "status", "replyTo", "setReplyTo", "error", "setError", "success", "setSuccess", "loading", "setLoading", "templateLoading", "setTemplateLoading", "step", "setStep", "currentEmail", "setCurrentEmail", "emailContents", "setEmailContents", "savedContents", "localStorage", "getItem", "parsed", "JSON", "parse", "map", "item", "html", "mjml", "e", "console", "Array", "from", "length", "scheduleSettings", "setScheduleSettings", "intervals", "unit", "campaignPreview", "setCampaignPreview", "showTemplatePicker", "setShowTemplatePicker", "templatesList", "setTemplatesList", "loadingTemplates", "setLoadingTemplates", "selectedTemplate", "setSelectedTemplate", "sendScheduleOption", "setSendScheduleOption", "scheduledDateTime", "setScheduledDateTime", "date", "Date", "now", "toISOString", "slice", "recipients", "setRecipients", "manualRecipient", "set<PERSON>anualRecip<PERSON>", "email", "contacts", "setContacts", "selectedContactIds", "setSelectedContactIds", "loadingContacts", "setLoadingContacts", "editor<PERSON><PERSON>", "activeEmailIndices", "setActiveEmailIndices", "initialActive", "saved", "initialContents", "arr", "isArray", "for<PERSON>ach", "index", "trim", "push", "queryParams", "URLSearchParams", "search", "templateId", "get", "fetchTemplate", "id", "res", "getTemplateById", "template", "temp", "updated", "content", "handleHtmlSave", "currentIndex", "setItem", "stringify", "prev", "includes", "sort", "a", "b", "log", "filter", "i", "getAllTemplates", "then", "r", "data", "catch", "finally", "handleTemplateSelect", "_r$template$content", "_id", "hasMjml", "mjml<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "contentLength", "err", "handleUseTemplate", "emailIndex", "hasHtml", "mjm<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "handleCreateCampaign", "validEmails", "_activeEmailsToSend$", "activeEmailsToSend", "campaignData", "scheduled", "scheduledFor", "recipientList", "userId", "htmlContent", "schedule", "emailIntervals", "d", "delay", "createCampaign", "campaignId", "toLocaleString", "removeItem", "sessionStorage", "warn", "_err$response", "_err$response$data", "response", "message", "handleBrowseTemplates", "draft", "handleEmailSelect", "activeEmailCount", "reduce", "count", "fill", "fetchContacts", "_r$data", "list", "c", "String", "Math", "random", "fullName", "addManualRecipient", "some", "addSelectedContacts", "toAdd", "handleContactSelection", "target", "selectedOptions", "o", "value", "removeRecipient", "handleNext", "latestContent", "savedContentHasHtml", "stateHasHtml", "current", "save", "substring", "savedContentIsActive", "isActive", "finalHasHtmlCheck", "cnt", "v", "handlePrevious", "handleClearEmailContent", "renderTemplatePicker", "isOpen", "onClose", "title", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "t", "onClick", "thumbnailUrl", "src", "alt", "variant", "disabled", "renderStepContent", "_user$domain2", "_user$domain3", "label", "onChange", "required", "type", "helpText", "curr", "emailsWithContent", "_", "idx", "hasHtmlContent", "isSelected", "buttonVariant", "customClasses", "buttonText", "icon", "selectionRingClass", "size", "ref", "initialHtml", "onSave", "height", "handleScheduleOptionChange", "handleScheduleTimeChange", "handleIntervalChange", "numValue", "parseInt", "isNaN", "newIntervals", "handleUnitChange", "checked", "htmlFor", "_scheduleSettings$int", "toString", "placeholder", "multiple", "contact", "rec", "scheduledTimeString", "activeEmails", "originalIndex", "activeIndex", "findIndex", "emailNumberInActiveSequence", "timingText", "undefined", "Boolean", "s", "xmlns", "viewBox", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/campaigns/CampaignCreate.tsx"], "sourcesContent": ["// frontend/src/pages/campaigns/CampaignCreate.tsx\n\nimport '../../styles/editor.css';\n\nimport React, {\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\n\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport HtmlEmailEditor, {\n  HtmlEmailEditorRef,\n} from 'components/HtmlEmailEditor';\nimport Input from 'components/Input';\nimport { Modal } from 'components/Modal';\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useLocation,\n  useNavigate,\n} from 'react-router-dom';\nimport { templateRecommendationService } from 'services';\nimport api, { campaignAPI } from 'services/api';\n\nconst CampaignCreate: React.FC = () => {\n  const { user } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // Campaign metadata\n  const [campaignName, setCampaignName] = useState('');\n  const [subject, setSubject] = useState('');\n  const [fromName, setFromName] = useState(user?.name || '');\n  const [fromEmail, setFromEmail] = useState(\n    user?.domain?.status === 'active' ? `noreply@${user.domain.name}` : ''\n  );\n  const [replyTo, setReplyTo] = useState('');\n\n  // UI state\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [templateLoading, setTemplateLoading] = useState(false);\n  const [step, setStep] = useState(1);\n\n  // Email sequence (up to 10)\n  const [currentEmail, setCurrentEmail] = useState<number>(1);\n  const [emailContents, setEmailContents] = useState<Array<{ html: string }>>(() => {\n    const savedContents = localStorage.getItem('driftly_campaign_create_email_contents');\n    if (savedContents) {\n      try {\n        const parsed = JSON.parse(savedContents);\n        // Convert any old format data to new format (html only)\n        return parsed.map((item: any) => ({ \n          html: item.html || item.mjml || '' \n        }));\n      } catch (e) {\n        console.error('Failed to parse saved email contents:', e);\n      }\n    }\n    // Initialize with empty HTML strings\n    return Array.from({ length: 10 }, () => ({ html: '' }));\n  });\n\n  // Scheduling\n  const [scheduleSettings, setScheduleSettings] = useState<{\n    intervals: number[];\n    unit: 'minutes' | 'hours' | 'days';\n  }>({\n    intervals: [24],\n    unit: 'hours',\n  });\n\n  // Preview and template picker\n  const [campaignPreview, setCampaignPreview] = useState<any>(null);\n  const [showTemplatePicker, setShowTemplatePicker] = useState(false);\n  const [templatesList, setTemplatesList] = useState<any[]>([]);\n  const [loadingTemplates, setLoadingTemplates] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState<any | null>(null);\n\n  // Recipients\n  const [sendScheduleOption, setSendScheduleOption] = useState<'now' | 'later'>('later');\n  const [scheduledDateTime, setScheduledDateTime] = useState<string>(() => {\n    const date = new Date(Date.now() + 60 * 60 * 1000);\n    return date.toISOString().slice(0, 16);\n  });\n  const [recipients, setRecipients] = useState<{ id: string | null; email: string; name?: string }[]>([]);\n  const [manualRecipient, setManualRecipient] = useState({ email: '', name: '' });\n  const [contacts, setContacts] = useState<{ id: string; email: string; name: string }[]>([]);\n  const [selectedContactIds, setSelectedContactIds] = useState<string[]>([]);\n  const [loadingContacts, setLoadingContacts] = useState(false);\n\n  // Ref to MJML editor\n  const editorRef = useRef<HtmlEmailEditorRef>(null);\n\n  // State to track which emails are actively selected for sending\n  const [activeEmailIndices, setActiveEmailIndices] = useState<number[]>(() => {\n    // Initialize based on emails that already have HTML content\n    const initialActive: number[] = [];\n    const saved = localStorage.getItem('driftly_campaign_create_email_contents');\n    let initialContents = Array.from({ length: 10 }, () => ({ html: '' }));\n    if (saved) {\n      try {\n        const arr = JSON.parse(saved);\n        if (Array.isArray(arr) && arr.length === 10) {\n          initialContents = arr.map((item: any) => ({ html: item.html || '' }));\n        }\n      } catch {}\n    }\n    initialContents.forEach((email, index) => {\n      if (email.html && email.html.trim()) {\n        initialActive.push(index);\n      }\n    });\n    return initialActive;\n  });\n\n  // Handle URL param for templateId\n  const queryParams = new URLSearchParams(location.search);\n  const templateId = queryParams.get('templateId');\n  useEffect(() => {\n    if (templateId) fetchTemplate(templateId);\n  }, [templateId]);\n\n  // Fetch AI or saved template by ID\n  const fetchTemplate = async (id: string) => {\n    setTemplateLoading(true);\n    try {\n      const res = await templateRecommendationService.getTemplateById(id);\n      if (res.success && res.template) {\n        const temp = res.template;\n        if (!campaignName) setCampaignName(`Campaign based on ${temp.name}`);\n        const updated = [...emailContents];\n        updated[0] = {\n          html: temp.content || '',\n        };\n        setEmailContents(updated);\n      } else {\n        setError('Failed to load template.');\n      }\n    } catch {\n      setError('Failed to load template.');\n    } finally {\n      setTemplateLoading(false);\n    }\n  };\n\n  // Save handler from MJML editor\n  const handleHtmlSave = (html: string) => {\n    // Update HTML content for current email\n    const updated = [...emailContents];\n    const currentIndex = currentEmail - 1;\n    updated[currentIndex] = { html };\n    setEmailContents(updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n\n    // If saving resulted in valid HTML, ensure this email is marked active\n    if (html && html.trim()) {\n      setActiveEmailIndices(prev => {\n        if (!prev.includes(currentIndex)) {\n          // Add if not already present, keep sorted for consistency\n          return [...prev, currentIndex].sort((a, b) => a - b);\n        }\n        return prev; // Already active, no change needed\n      });\n      console.log(`Ensured email index ${currentIndex} is active after save.`);\n    } else {\n      // If saving resulted in empty HTML, ensure it's deactivated\n      setActiveEmailIndices(prev => prev.filter(i => i !== currentIndex));\n      console.log(`Deactivated email index ${currentIndex} after save due to empty HTML.`);\n    }\n  };\n\n  // Template picker effects and handlers\n  useEffect(() => {\n    if (showTemplatePicker) {\n      setLoadingTemplates(true);\n      templateRecommendationService.getAllTemplates()\n        .then(r => setTemplatesList(r.data || []))\n        .catch(console.error)\n        .finally(() => setLoadingTemplates(false));\n    }\n  }, [showTemplatePicker]);\n\n  const handleTemplateSelect = async (id: string) => {\n    setLoadingTemplates(true);\n    try {\n      console.log(`[Debug] Selecting template with ID: ${id}`);\n      const r = await templateRecommendationService.getTemplateById(id);\n      console.log('[Debug] Template API response:', r);\n      if (r.success && r.template) {\n        console.log('[Debug] Setting selected template:', {\n          id: r.template.id || r.template._id,\n          name: r.template.name,\n          hasMjml: !!r.template.mjmlContent,\n          hasContent: !!r.template.content,\n          contentLength: r.template.content?.length || 0\n        });\n      }\n      setSelectedTemplate(r.template);\n    } catch (err) {\n      console.error('[Debug] Error selecting template:', err);\n      setError('Failed to load selected template.');\n    } finally {\n      setLoadingTemplates(false);\n    }\n  };\n\n  const handleUseTemplate = () => {\n    console.log('[Debug] Using selected template:', selectedTemplate ? {\n      id: selectedTemplate.id || selectedTemplate._id,\n      name: selectedTemplate.name,\n      hasMjml: !!selectedTemplate.mjmlContent,\n      hasContent: !!selectedTemplate.content\n    } : 'No template selected');\n    \n    if (selectedTemplate) {\n      const updated = [...emailContents];\n      updated[currentEmail - 1] = {\n        html: selectedTemplate.content || '',\n      };\n      \n      console.log('[Debug] Updated email contents:', {\n        emailIndex: currentEmail - 1,\n        hasMjml: !!updated[currentEmail - 1].html,\n        hasHtml: !!updated[currentEmail - 1].html,\n        mjmlLength: updated[currentEmail - 1].html.length\n      });\n      \n      // Save the updated contents to state and local storage\n      setEmailContents(updated);\n      localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n      \n      // Close the modal first to reduce complexity\n      setSelectedTemplate(null);\n      setShowTemplatePicker(false);\n      \n      // Force a complete re-render of the editor component\n      // Wait a moment to ensure state updates have completed\n      setTimeout(() => {\n        // This will force the MjmlEditor to completely remount with the new content\n        // Temporarily set current email to a value that won't match any existing email\n        setCurrentEmail(-1);\n        \n        // After a brief delay, restore the current email index\n        setTimeout(() => {\n          setCurrentEmail(currentEmail);\n        }, 50);\n      }, 100);\n    }\n  };\n\n  // Campaign creation\n  const handleCreateCampaign = async () => {\n    if (!campaignName || !subject || !fromName || !fromEmail) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    const validEmails = emailContents.filter(e => e.html.trim());\n    if (validEmails.length === 0) {\n      setError('Please add at least one email with valid HTML content');\n      return;\n    }\n    if (recipients.length === 0) {\n      setError('Please add at least one recipient');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Filter emails with actual HTML content AND that are marked active\n      const activeEmailsToSend = emailContents.filter((email, index) => \n        activeEmailIndices.includes(index) && email.html && email.html.trim()\n      );\n\n      // Validation based on filtered list\n      if (activeEmailsToSend.length === 0) {\n        setError('Please select at least one email with valid HTML content to send.');\n        setLoading(false);\n        return;\n      }\n      if (recipients.length === 0) {\n        setError('Please add at least one recipient');\n        setLoading(false);\n        return;\n      }\n      // --- End new validation ---\n\n      // Construct the base campaign data\n      const campaignData: any = {\n        name: campaignName,\n        subject,\n        fromName,\n        fromEmail,\n        replyTo: replyTo || fromEmail,\n        scheduled: sendScheduleOption === 'later',\n        ...(sendScheduleOption === 'later' && { scheduledFor: new Date(scheduledDateTime).toISOString() }),\n        recipientList: recipients,\n        userId: user?.id,\n        status: 'draft',\n        // Use the first ACTIVE email for top-level content\n        htmlContent: activeEmailsToSend[0]?.html || '', \n        // Send ONLY the emails that are ACTIVE and have content\n        emailContents: activeEmailsToSend.map(e => ({ html: e.html })),\n      };\n\n      // Add schedule only if there's more than one ACTIVE email with content\n      if (activeEmailsToSend.length > 1) {\n        campaignData.schedule = {\n          unit: scheduleSettings.unit,\n          // Slice intervals to match the number of gaps between ACTIVE emails\n          emailIntervals: scheduleSettings.intervals.slice(0, activeEmailsToSend.length - 1)\n            .map(d => ({ delay: d, unit: scheduleSettings.unit })),\n        };\n      }\n\n      console.log(\"Sending filtered & active campaign data:\", campaignData);\n\n      // Send the filtered data to the API\n      const res = await campaignAPI.createCampaign(campaignData);\n      // Check for _id primarily, then id\n      const campaignId = res?._id || res?.id;\n      console.log('Create campaign response:', res, 'Extracted ID:', campaignId);\n\n      setSuccess(sendScheduleOption === 'later'\n        ? `Campaign scheduled for ${new Date(scheduledDateTime).toLocaleString()}`\n        : 'Campaign created and will start sending shortly');\n      localStorage.removeItem('driftly_campaign_create_email_contents');\n      // Clear draft info if it exists\n      localStorage.removeItem('driftly_campaign_draft'); \n      sessionStorage.setItem('reloadCampaigns', 'true');\n      setTimeout(() => {\n        // Navigate using the extracted ID\n        if (campaignId) navigate(`/campaigns/${campaignId}/summary`);\n        else {\n          console.warn('No ID found in createCampaign response, navigating to list.');\n          navigate('/campaigns');\n        }\n      }, 1500);\n    } catch (err: any) {\n      setError(err.response?.data?.message || 'Failed to create campaign');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBrowseTemplates = () => {\n    if (campaignName || subject || fromName || fromEmail || replyTo) {\n      localStorage.setItem('driftly_campaign_draft',\n        JSON.stringify({ campaignName, subject, fromName, fromEmail, replyTo }));\n    }\n    navigate('/email-templates');\n  };\n\n  useEffect(() => {\n    const draft = localStorage.getItem('driftly_campaign_draft');\n    if (draft) {\n      try {\n        const d = JSON.parse(draft);\n        setCampaignName(d.campaignName || '');\n        setSubject(d.subject || '');\n        setFromName(d.fromName || user?.name || '');\n        setFromEmail(d.fromEmail || '');\n        setReplyTo(d.replyTo || '');\n      } catch {}\n      localStorage.removeItem('driftly_campaign_draft');\n    }\n  }, [user]);\n\n  const handleEmailSelect = (i: number) => setCurrentEmail(i);\n\n  useEffect(() => {\n    if (step === 3) {\n      // Count emails that are BOTH active AND have HTML\n      const activeEmailCount = emailContents.reduce((count, email, index) => {\n        if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n          return count + 1;\n        }\n        return count;\n      }, 0);\n\n      console.log('Active emails with HTML for scheduling:', activeEmailCount);\n\n      if (activeEmailCount > 1) {\n        setScheduleSettings(prev => ({\n          ...prev,\n          // Ensure intervals array matches the number of gaps between ACTIVE emails\n          intervals: Array(activeEmailCount - 1).fill(prev.intervals[0] || 24),\n        }));\n      } else {\n        // If 0 or 1 active emails, no intervals needed\n        setScheduleSettings(prev => ({ ...prev, intervals: [] }));\n      }\n    }\n  }, [step, emailContents, activeEmailIndices]); // Add activeEmailIndices dependency\n\n  const fetchContacts = async () => {\n    if (step === 4) {\n      setLoadingContacts(true);\n      try {\n        const r = await api.get('/contacts');\n        let list = Array.isArray(r.data) ? r.data : r.data?.data || [];\n        setContacts(list.map((c: any) => ({\n          id: c.id || c._id || String(Math.random()),\n          email: c.email,\n          name: c.name || c.fullName || '',\n        })));\n      } catch {\n        setError('Failed to load contacts');\n      } finally {\n        setLoadingContacts(false);\n      }\n    }\n  };\n\n  useEffect(() => {\n    fetchContacts();\n  }, [step]);\n\n  const addManualRecipient = () => {\n    if (!manualRecipient.email) {\n      setError('Please enter an email address.');\n    } else if (!recipients.some(r => r.email === manualRecipient.email)) {\n      setRecipients([...recipients, { id: null, ...manualRecipient }]);\n      setManualRecipient({ email: '', name: '' });\n    } else {\n      setError('Recipient already added.');\n    }\n    setTimeout(() => setError(''), 3000);\n  };\n\n  const addSelectedContacts = () => {\n    const toAdd = contacts.filter(c =>\n      selectedContactIds.includes(c.id) && !recipients.some(r => r.email === c.email)\n    );\n    setRecipients([...recipients, ...toAdd]);\n    setSelectedContactIds([]);\n  };\n  const handleContactSelection = (e: React.ChangeEvent<HTMLSelectElement>) =>\n    setSelectedContactIds(Array.from(e.target.selectedOptions, o => o.value));\n  const removeRecipient = (email: string) =>\n    setRecipients(recipients.filter(r => r.email !== email));\n\n  // Step navigation\n  const handleNext = async () => {\n    setError('');\n\n    // Validation for Step 1\n    if (step === 1 && (!campaignName || !subject || !fromName || !fromEmail)) {\n      setError('Please fill in all required fields.');\n      return;\n    }\n\n    // Validation for Step 2\n    if (step === 2) {\n      console.log('--- handleNext: Step 2 Validation ---');\n      let latestContent: { html: string } | null = null;\n      let savedContentHasHtml = false;\n      let stateHasHtml = false;\n\n      // --- Force save current editor's content FIRST ---\n      if (editorRef.current) {\n        console.log('Forcing editor save for current email:', currentEmail);\n        // Await the result of the now async save method\n        latestContent = await editorRef.current.save(); \n        if (latestContent) {\n          console.log('Async save function returned content.');\n          // Log the returned HTML content (or lack thereof)\n          console.log('Returned HTML:', latestContent.html ? latestContent.html.substring(0, 50) + '...' : '(empty)');\n\n          // Check if the *returned* content has HTML\n          if (latestContent.html && latestContent.html.trim()) {\n            console.log('RETURNED content has non-empty HTML.');\n            savedContentHasHtml = true;\n          } else {\n            console.log('RETURNED content has empty or no HTML.');\n          }\n\n          // Trigger state update (async) - Allow this to proceed even if HTML is missing\n          console.log('Triggering handleHtmlSave state update...');\n          handleHtmlSave(latestContent.html);\n\n        } else {\n          // This case might be less likely now if save always returns an object\n          console.warn('editorRef.current.save() did not return expected content object.');\n        }\n      } else {\n         console.error('HtmlEmailEditor ref is not available for forced save.');\n      }\n      // --- End Force save ---\n\n      // --- Validation ---\n      // Since save is now awaited and includes a delay, checking the returned content\n      // (savedContentHasHtml) should be more reliable. We also check state.\n      \n      // Check if the SAVED content belongs to an index currently marked ACTIVE\n      const savedContentIsActive = savedContentHasHtml && activeEmailIndices.includes(currentEmail - 1);\n      if (savedContentIsActive) {\n        console.log('Saved content is for an active email.');\n      }\n\n      console.log('Checking emailContents state AFTER awaiting save/update attempt...');\n      // Check if ANY email in state is BOTH active AND has HTML\n      stateHasHtml = emailContents.some((email, index) => {\n        const hasHtml = email.html && email.html.trim();\n        const isActive = activeEmailIndices.includes(index);\n        if (hasHtml && isActive) {\n             console.log(`Email index ${index} in STATE is ACTIVE and has HTML.`);\n             return true;\n        }\n        return false;\n      });\n      if (!stateHasHtml) {\n          console.log('No email in STATE is both active and has HTML.');\n      }\n\n      // Final decision: Did *either* the direct save (if active) *or* the state check find an ACTIVE email with HTML?\n      const finalHasHtmlCheck = savedContentIsActive || stateHasHtml;\n      console.log('Final check result (Active & HTML):', finalHasHtmlCheck, `(Saved Active: ${savedContentIsActive}, State Active: ${stateHasHtml})`);\n\n      if (!finalHasHtmlCheck) {\n        setError('Please ensure at least one selected email (marked as \\'Sending\\') has valid HTML content.');\n        return; // Stop execution if validation fails specifically for step 2\n      }\n      console.log('Step 2 validation passed (Active HTML check).');\n    }\n\n    // Validation for Step 3\n    if (step === 3) {\n      const cnt = emailContents.filter(e => e.html.trim()).length;\n      if (cnt > 1) {\n        if (scheduleSettings.intervals.length !== cnt - 1) {\n          setError(`Define ${cnt - 1} intervals.`);\n          return;\n        }\n        if (scheduleSettings.intervals.some(v => v <= 0)) {\n          setError('Intervals must be positive.');\n          return;\n        }\n      }\n    }\n\n    // Validation for Step 4\n    if (step === 4 && recipients.length === 0) {\n      setError('Please add at least one recipient.');\n      return;\n    }\n\n    // Proceed to next step if all relevant validations passed and not on the last step\n    if (step < 5) {\n      console.log(`Proceeding from step ${step} to step ${step + 1}`);\n      setStep(step + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    setError('');\n    if (step > 1) setStep(step - 1);\n  };\n\n  // --- Add this handler ---\n  const handleClearEmailContent = (index: number) => {\n    const updated = [...emailContents];\n    updated[index] = { html: '' };\n    setEmailContents(updated);\n    localStorage.setItem('driftly_campaign_create_email_contents', JSON.stringify(updated));\n    // Also remove from active indices when cleared\n    setActiveEmailIndices(prev => prev.filter(i => i !== index));\n    console.log(`Cleared content and deactivated email index ${index}`);\n  };\n  // --- End Add Handler ---\n\n  const renderTemplatePicker = () => (\n    <Modal isOpen={showTemplatePicker} onClose={() => setShowTemplatePicker(false)} title=\"Choose Template\">\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1\">\n        {loadingTemplates\n          ? <p>Loading...</p>\n          : templatesList.map(t => (\n            <Card\n              key={t.id}\n              className={`cursor-pointer ${selectedTemplate?.id === t.id ? 'ring-2 ring-primary' : ''}`}\n              onClick={() => handleTemplateSelect(t.id)}\n            >\n              <h4 className=\"font-semibold mb-2 truncate\">{t.name}</h4>\n              <div className=\"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500\">\n                {t.thumbnailUrl ? <img src={t.thumbnailUrl} alt={t.name} className=\"object-contain h-full w-full\" /> : 'No Preview'}\n              </div>\n            </Card>\n          ))\n        }\n      </div>\n      <div className=\"mt-4 flex justify-end gap-2\">\n        <Button variant=\"secondary\" onClick={() => setShowTemplatePicker(false)}>Cancel</Button>\n        <Button onClick={handleUseTemplate} disabled={!selectedTemplate || loadingTemplates}>\n          Use Selected\n        </Button>\n      </div>\n    </Modal>\n  );\n\n  const renderStepContent = () => {\n    switch (step) {\n      case 1: return (\n        <div className=\"space-y-4\">\n          <Input id=\"campaignName\" name=\"campaignName\" label=\"Campaign Name\" value={campaignName} onChange={e => setCampaignName(e.target.value)} required />\n          <Input id=\"subject\" name=\"subject\" label=\"Email Subject\" value={subject} onChange={e => setSubject(e.target.value)} required />\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input id=\"fromName\" name=\"fromName\" label=\"From Name\" value={fromName} onChange={e => setFromName(e.target.value)} required />\n            <Input\n              id=\"fromEmail\"\n              name=\"fromEmail\"\n              label=\"From Email\"\n              type=\"email\"\n              value={fromEmail}\n              onChange={e => setFromEmail(e.target.value)}\n              disabled={user?.domain?.status === 'active'}\n              required\n              helpText={user?.domain?.status === 'active' ? `Using verified domain: ${user.domain.name}` : 'Verify domain for deliverability.'}\n            />\n          </div>\n          <Input id=\"replyTo\" name=\"replyTo\" label=\"Reply-To Email (optional)\" type=\"email\" value={replyTo} onChange={e => setReplyTo(e.target.value)} />\n        </div>\n      );\n      case 2:\n        const curr = emailContents[currentEmail - 1];\n        // Use stricter validation: check for non-empty HTML\n        const emailsWithContent = emailContents.filter(e => e.html.trim()).length;\n        \n        return (\n          <div className=\"flex flex-col lg:flex-row gap-4 h-full\">\n            {/* Email sequence sidebar */}\n            <div className=\"w-full lg:w-1/5 flex flex-col gap-2\">\n              <h3 className=\"font-semibold mb-2\">Email Sequence (Click to include/exclude):</h3>\n              {Array.from({ length: 10 }).map((_, idx) => {\n                const hasHtmlContent = !!(emailContents[idx].html && emailContents[idx].html.trim());\n                const isSelected = currentEmail === idx + 1;\n                const isActive = activeEmailIndices.includes(idx);\n\n                // Determine button appearance based on content and active state\n                let buttonVariant: \"primary\" | \"secondary\" = \"secondary\";\n                let customClasses = \"\";\n                let buttonText = `Email ${idx + 1}`;\n                let icon = null;\n\n                if (hasHtmlContent) {\n                  if (isActive) {\n                    buttonVariant = \"primary\"; // Has content, is active\n                    icon = <span className=\"ml-1 text-green-500 dark:text-green-400 font-bold\">✓</span>;\n                    buttonText += \" (Sending)\";\n                  } else {\n                    buttonVariant = \"secondary\"; // Has content, but inactive - use secondary variant\n                    customClasses = \"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500\"; // Custom styling for inactive\n                    icon = <span className=\"ml-1 text-gray-500 dark:text-gray-400 font-bold\">⏸</span>; // Pause icon maybe?\n                    buttonText += \" (Excluded)\";\n                  }\n                } else {\n                   // No content, always secondary, cannot be active\n                   // Make it look distinct but clickable\n                   customClasses = \"opacity-75 border border-dashed border-gray-400 dark:border-gray-600\"; \n                   buttonText += \" (No Content - Click to Edit)\";\n                }\n                \n                const selectionRingClass = isSelected ? \"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800\" : \"\";\n                \n                return (\n                  <Button\n                    key={idx}\n                    variant={buttonVariant} // Will be 'secondary' for no content\n                    onClick={() => {\n                      // Set as current email for editing - ALWAYS DO THIS\n                      setCurrentEmail(idx + 1);\n                      \n                      // Toggle active state ONLY if it has content\n                      if (hasHtmlContent) {\n                        setActiveEmailIndices(prev => \n                          prev.includes(idx) ? prev.filter(i => i !== idx) : [...prev, idx]\n                        );\n                      }\n                    }}\n                    size=\"sm\"\n                    className={`w-full text-left flex items-center justify-between ${selectionRingClass} ${customClasses}`}\n                    title={hasHtmlContent ? (isActive ? \"Click to exclude from sending\" : \"Click to include in sending\") : \"Click to edit this email\"}\n                  >\n                    <span className=\"flex-grow truncate\">{buttonText}</span>\n                    {icon} \n                  </Button>\n                );\n              })}\n              \n              {/* Add helper text explaining the buttons */}\n              <div className=\"mt-4 p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm space-y-1\">\n                <p className=\"font-medium mb-1\">Email Status Legend:</p>\n                <p className=\"flex items-center\">\n                  <span className=\"ml-1 mr-2 text-green-500 dark:text-green-400 font-bold\">✓</span> \n                  <span className=\"text-green-700 dark:text-green-400\">Has Content, Will Send</span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"inline-block w-3 h-3 mr-2 bg-gray-400 dark:bg-gray-500 rounded-full\"></span> \n                  <span className=\"text-gray-700 dark:text-gray-300\">Has Content, <span className='font-bold'>Excluded</span></span>\n                </p>\n                <p className=\"flex items-center\">\n                  <span className=\"inline-block w-3 h-3 mr-2 border border-dashed border-gray-400 dark:border-gray-600 rounded-full opacity-75\"></span> \n                  <span className=\"text-gray-700 dark:text-gray-300\">No Content (Click to Edit)</span>\n                 </p>\n                 <p className=\"text-xs mt-2 text-gray-600 dark:text-gray-400\">Click emails with content to toggle inclusion. Click any email to edit.</p>\n              </div>\n              \n              <div className=\"mt-4\">\n                <Button variant=\"secondary\" onClick={() => setShowTemplatePicker(true)}>Choose Template</Button>\n                <Button variant=\"secondary\" onClick={handleBrowseTemplates} className=\"mt-2\">Browse All Templates</Button>\n              </div>\n            </div>\n            {/* Editor */}\n            <div className=\"w-full lg:w-4/5 flex flex-col\">\n              <HtmlEmailEditor\n                key={`email-${currentEmail}-${Date.now()}`}\n                ref={editorRef}\n                initialHtml={curr.html}\n                onSave={handleHtmlSave}\n                height=\"60vh\"\n              />\n              <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-2\">\n                  Changes are saved automatically. Emails with a green check mark (✓) will be included in your campaign.\n              </p>\n            </div>\n          </div>\n        );\n      case 3: {\n        // Calculate count based on emails that are BOTH active AND have HTML\n        const activeEmailCount = emailContents.reduce((count, email, index) => {\n          if (activeEmailIndices.includes(index) && email.html && email.html.trim()) {\n            return count + 1;\n          }\n          return count;\n        }, 0);\n        console.log('Active emails with HTML for rendering Step 3:', activeEmailCount);\n\n        // Handlers for main scheduling options\n        const handleScheduleOptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n          setSendScheduleOption(e.target.value as 'now' | 'later');\n        };\n        const handleScheduleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n          setScheduledDateTime(e.target.value);\n        };\n\n        // Handlers for interval settings (only relevant if emailCount > 1)\n        const handleIntervalChange = (index: number, value: string) => {\n          const numValue = parseInt(value, 10);\n          if (!isNaN(numValue) && numValue > 0) {\n            const newIntervals = [...scheduleSettings.intervals];\n            newIntervals[index] = numValue;\n            setScheduleSettings({ ...scheduleSettings, intervals: newIntervals });\n          }\n        };\n        const handleUnitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n          setScheduleSettings({ ...scheduleSettings, unit: e.target.value as 'minutes' | 'hours' | 'days' });\n        };\n\n        return (\n          <div className=\"space-y-6\">\n            {/* --- Start Time Scheduling --- */}\n            <Card>\n              <h2 className=\"text-xl font-semibold mb-4\">Campaign Start Time</h2>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"scheduleNow\"\n                    name=\"scheduleOption\"\n                    type=\"radio\"\n                    value=\"now\"\n                    checked={sendScheduleOption === 'now'}\n                    onChange={handleScheduleOptionChange}\n                    className=\"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  />\n                  <label htmlFor=\"scheduleNow\" className=\"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Send Immediately\n                  </label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"scheduleLater\"\n                    name=\"scheduleOption\"\n                    type=\"radio\"\n                    value=\"later\"\n                    checked={sendScheduleOption === 'later'}\n                    onChange={handleScheduleOptionChange}\n                    className=\"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300\"\n                  />\n                  <label htmlFor=\"scheduleLater\" className=\"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Schedule for Later\n                  </label>\n                </div>\n                {sendScheduleOption === 'later' && (\n                  <div className=\"pl-7 mt-2\">\n                    <Input\n                      id=\"scheduledDateTime\"\n                      name=\"scheduledDateTime\"\n                      type=\"datetime-local\"\n                      value={scheduledDateTime}\n                      onChange={handleScheduleTimeChange}\n                      label=\"Scheduled Date & Time\"\n                      required\n                      className=\"max-w-sm\"\n                    />\n                     <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">Your local timezone.</p>\n                  </div>\n                )}\n              </div>\n            </Card>\n            \n            {/* --- Sequence Interval Timing (Conditional) --- */}\n            {activeEmailCount > 1 && (\n              <Card>\n                 <h2 className=\"text-xl font-semibold mb-4\">Email Sequence Timing</h2>\n                 <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                    Set the time interval between each subsequent email send. The first email is sent according to the 'Campaign Start Time' setting above.\n                 </p>\n                 <div className=\"flex items-center gap-4 mb-6\">\n                   <label htmlFor=\"scheduleUnit\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                     Interval Time Unit:\n                   </label>\n                   <select\n                     id=\"scheduleUnit\"\n                     name=\"scheduleUnit\"\n                     value={scheduleSettings.unit}\n                     onChange={handleUnitChange}\n                     className=\"mt-1 block w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                   >\n                     <option value=\"minutes\">Minutes</option>\n                     <option value=\"hours\">Hours</option>\n                     <option value=\"days\">Days</option>\n                   </select>\n                 </div>\n\n                 {Array.from({ length: activeEmailCount - 1 }).map((_, index) => (\n                   <div key={index} className=\"flex items-center gap-4 mt-4\">\n                     <label htmlFor={`interval-${index}`} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 w-48\">\n                       Wait before sending Email #{index + 2}:\n                     </label>\n                     <Input\n                       id={`interval-${index}`}\n                       name={`interval-${index}`}\n                       type=\"number\"\n                       value={scheduleSettings.intervals[index]?.toString() || '24'}\n                       onChange={(e) => handleIntervalChange(index, e.target.value)}\n                       className=\"w-24 mb-0\" // Adjusted margin\n                       required\n                     />\n                     <span className=\"text-gray-600 dark:text-gray-400 capitalize\">{scheduleSettings.unit}</span>\n                   </div>\n                 ))}\n               </Card>\n             )}\n             \n             {/* Informative text if only one active email */}\n             {activeEmailCount <= 1 && (\n                 <p className=\"text-gray-600 dark:text-gray-400 text-sm px-4\">\n                     Sequence timing options are available when your campaign has more than one selected email with content.\n                 </p>\n             )}\n          </div>\n        );\n      }\n      case 4:\n        // Add Recipients Step\n        return (\n          <div className=\"space-y-6\">\n            <h2 className=\"text-xl font-semibold mb-4\">Add Recipients</h2>\n\n            {/* Manual Add */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Add Manually</h3>\n              <div className=\"md:flex md:items-end md:space-x-3\">\n                <div className=\"flex-grow mb-3 md:mb-0\">\n                  <Input\n                    id=\"manualRecipient\"\n                    name=\"manualRecipient\"\n                    label=\"Email Address\"\n                    type=\"email\"\n                    value={manualRecipient.email}\n                    onChange={(e) => setManualRecipient({ ...manualRecipient, email: e.target.value })}\n                    placeholder=\"Enter email address\"\n                  />\n                </div>\n                {/* Apply Coral CTA Style */}\n                <Button onClick={addManualRecipient} variant=\"secondary\" className=\"btn-cta mt-4 md:mt-0\">Add Recipient</Button>\n              </div>\n            </Card>\n\n            {/* Add from Contacts */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Add From Contacts</h3>\n              {loadingContacts ? (\n                <p>Loading contacts...</p>\n              ) : contacts.length > 0 ? (\n                <div className=\"md:flex md:items-end md:space-x-3\">\n                   <div className=\"flex-grow mb-3 md:mb-0\">\n                      <label htmlFor=\"contactSelect\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Select Contacts (use Ctrl/Cmd to select multiple)\n                      </label>\n                      <select\n                        id=\"contactSelect\"\n                        multiple\n                        value={selectedContactIds}\n                        onChange={handleContactSelection}\n                        className=\"form-input w-full h-32 border border-border rounded-md\"\n                      >\n                        {contacts.map(contact => (\n                          <option key={contact.id} value={contact.id}>\n                            {contact.name ? `${contact.name} (${contact.email})` : contact.email}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  <Button onClick={addSelectedContacts} variant=\"secondary\" className=\"btn-cta mt-4 md:mt-0\">Add Selected</Button>\n                </div>\n              ) : (\n                <p className=\"text-text-secondary\">No contacts found. You can add contacts in the 'Contacts' section.</p>\n              )}\n            </Card>\n\n            {/* Added Recipients List */}\n            <Card>\n              <h3 className=\"text-lg font-medium mb-3 text-text-primary\">Recipients Added ({recipients.length})</h3>\n              {recipients.length > 0 ? (\n                <ul className=\"divide-y divide-border max-h-60 overflow-y-auto\">\n                  {recipients.map((rec, index) => (\n                    <li key={index} className=\"py-2 flex justify-between items-center\">\n                      <span>{rec.name ? `${rec.name} (${rec.email})` : rec.email}</span>\n                      <button onClick={() => removeRecipient(rec.email)} className=\"text-red-500 hover:text-red-700 text-sm\">&times; Remove</button>\n                    </li>\n                  ))}\n                </ul>\n              ) : (\n                <p className=\"text-text-secondary\">No recipients added yet.</p>\n              )}\n            </Card>\n          </div>\n        );\n      case 5: {\n         // Review and Schedule Step\n         const scheduledTimeString = sendScheduleOption === 'later' && scheduledDateTime\n           ? new Date(scheduledDateTime).toLocaleString()\n           : 'Immediately';\n         return (\n           <div className=\"space-y-6\">\n             <h2 className=\"text-xl font-semibold mb-4\">Review & Schedule</h2>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Campaign Details</h3>\n               <p><span className=\"font-semibold\">Name:</span> {campaignName}</p>\n               <p><span className=\"font-semibold\">Subject:</span> {subject}</p>\n               <p><span className=\"font-semibold\">From:</span> {fromName} &lt;{fromEmail}&gt;</p>\n               {replyTo && <p><span className=\"font-semibold\">Reply To:</span> {replyTo}</p>}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Sequence (Emails to Send)</h3>\n               {/* Filter based on ACTIVE and HTML content */}\n               {emailContents.filter((e, idx) => activeEmailIndices.includes(idx) && e.html.trim()).length > 0 ? (\n                 <ul className=\"list-decimal pl-5 space-y-1\">\n                   {emailContents\n                     .map((email, index) => {\n                       // Use stricter validation: check for non-empty HTML AND active status\n                       if (activeEmailIndices.includes(index) && email.html.trim()) {\n                         // Find the index of this email within the *filtered* list of active emails\n                         const activeEmails = emailContents\n                           .map((e, i) => ({ email: e, originalIndex: i })) // Keep track of original index\n                           .filter(({ email, originalIndex }) => \n                             activeEmailIndices.includes(originalIndex) && email.html.trim()\n                           );\n                         const activeIndex = activeEmails.findIndex(item => item.originalIndex === index);\n                         \n                         const emailNumberInActiveSequence = activeIndex + 1; // 1-based index for display\n\n                         let timingText = 'Sent at Campaign Start Time'; // Default text\n                         // Calculate timing based on the position in the *active* sequence\n                         if (emailNumberInActiveSequence > 1 && scheduleSettings.intervals[emailNumberInActiveSequence - 2] !== undefined) {\n                           timingText = `Sent ${scheduleSettings.intervals[emailNumberInActiveSequence - 2]} ${scheduleSettings.unit} after Email #${activeEmails[activeIndex - 1].originalIndex + 1}`;\n                         }\n                       \n                         return (\n                           <li key={index}>\n                             Email #{index + 1} - {timingText}\n                           </li>\n                         );\n                       }\n                       return null; // Don't render anything for inactive/empty emails\n                     })\n                     .filter(Boolean) // Remove null entries from the map result\n                   }\n                 </ul>\n               ) : (\n                 <p className=\"text-text-secondary\">No emails selected or content found for the sequence.</p>\n               )}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Recipients</h3>\n               <p>{recipients.length} recipient(s)</p>\n               {recipients.length > 0 && (\n                 <ul className=\"list-disc pl-5 text-sm max-h-20 overflow-y-auto\">\n                   {recipients.slice(0, 5).map((r, i) => <li key={i}>{r.name ? `${r.name} (${r.email})` : r.email}</li>)}\n                   {recipients.length > 5 && <li>...and {recipients.length - 5} more</li>}\n                 </ul>\n               )}\n             </Card>\n\n             <Card>\n               <h3 className=\"text-lg font-medium mb-2 text-text-primary\">Schedule</h3>\n               <p>Start sending: {scheduledTimeString}</p>\n             </Card>\n\n             <div className=\"mt-6 flex justify-end\">\n               {/* Coral Style CTA for final step */}\n               <Button \n                 onClick={handleCreateCampaign} \n                 disabled={loading}\n                 className=\"btn-cta\" // Apply Coral CTA style\n               >\n                 {loading ? 'Creating...' : 'Create & Schedule Campaign'}\n               </Button>\n             </div>\n           </div> // Closing the main div for step 5\n         ); // Closing the return statement for case 5\n       } // Closing the switch statement\n       default: return null;\n     } // Closing the outer switch\n   }; // Closing renderStepContent\n\n   // --- Main Component Render ---\n   return (\n     <>\n       <h1 className=\"text-2xl font-bold mb-6 text-text-primary\">Create New Campaign</h1>\n       {error && <div className=\"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\"><p>{error}</p></div>}\n       {success && <div className=\"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded\"><p>{success}</p></div>}\n       {templateLoading && <div className=\"mb-4 text-text-secondary\">Loading template...</div>}\n\n       {/* Step Indicator */}\n       <div className=\"mb-8\">\n         <ol className=\"flex items-center w-full text-sm font-medium text-center text-text-secondary dark:text-gray-400 sm:text-base\">\n           {[1, 2, 3, 4, 5].map(s => (\n             <li key={s} className={`flex md:w-full items-center ${s === step ? 'text-accent-coral dark:text-blue-500' : ''} ${s < 5 ? 'sm:after:content-[\\'\\'] after:w-full after:h-1 after:border-b after:border-border dark:after:border-gray-700 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10' : ''}`}>\n               <span className={`flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-text-disabled ${s < step ? 'text-text-primary dark:text-gray-200' : ''}`}>\n                 {s < step && (\n                   <svg className=\"w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2.5\" aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                     <path d=\"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z\"/>\n                   </svg>\n                 )}\n                 {s === step && <span className=\"mr-2\">{s}</span>}\n                 {s > step && <span className=\"mr-2\">{s}</span>}\n                 {['Details', 'Content', 'Schedule', 'Recipients', 'Review'][s-1]}\n               </span>\n             </li>\n           ))}\n         </ol>\n       </div>\n\n\n       <Card className=\"mb-6\">\n         {renderStepContent()}\n       </Card>\n\n       {/* Navigation Buttons */}\n       <div className=\"flex justify-between mt-8\">\n         <Button onClick={handlePrevious} disabled={step === 1 || loading} variant=\"secondary\">Previous</Button>\n         {step < 5 ? (\n           <Button onClick={handleNext} disabled={loading} variant=\"primary\">Next</Button>\n         ) : (\n           <Button onClick={handleCreateCampaign} disabled={loading} className=\"btn-cta\"> {/* Replicate final CTA style here */}\n               {loading ? 'Creating...' : 'Create & Schedule Campaign'}\n           </Button>\n         )}\n       </div>\n\n       {renderTemplatePicker()}\n     </>\n   );\n }; // Closing the component function\n\n export default CampaignCreate; // Export the component\n"], "mappings": ";;AAAA;;AAEA,OAAO,yBAAyB;AAEhC,OAAOA,KAAK,IACVC,SAAS,EACTC,MAAM,EACNC,QAAQ,QACH,OAAO;AAEd,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,eAAe,MAEf,4BAA4B;AACnC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,KAAK,QAAQ,kBAAkB;AACxC,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,SACEC,WAAW,EACXC,WAAW,QACN,kBAAkB;AACzB,SAASC,6BAA6B,QAAQ,UAAU;AACxD,OAAOC,GAAG,IAAIC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA;EACrC,MAAM;IAAEC;EAAK,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC1B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,CAAAmB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI,EAAE,CAAC;EAC1D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CACxC,CAAAmB,IAAI,aAAJA,IAAI,wBAAAD,YAAA,GAAJC,IAAI,CAAEY,MAAM,cAAAb,YAAA,uBAAZA,YAAA,CAAcc,MAAM,MAAK,QAAQ,GAAG,WAAWb,IAAI,CAACY,MAAM,CAACH,IAAI,EAAE,GAAG,EACtE,CAAC;EACD,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2C,IAAI,EAAEC,OAAO,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;;EAEnC;EACA,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAS,CAAC,CAAC;EAC3D,MAAM,CAAC+C,aAAa,EAAEC,gBAAgB,CAAC,GAAGhD,QAAQ,CAA0B,MAAM;IAChF,MAAMiD,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,wCAAwC,CAAC;IACpF,IAAIF,aAAa,EAAE;MACjB,IAAI;QACF,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,aAAa,CAAC;QACxC;QACA,OAAOG,MAAM,CAACG,GAAG,CAAEC,IAAS,KAAM;UAChCC,IAAI,EAAED,IAAI,CAACC,IAAI,IAAID,IAAI,CAACE,IAAI,IAAI;QAClC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,OAAOC,CAAC,EAAE;QACVC,OAAO,CAACzB,KAAK,CAAC,uCAAuC,EAAEwB,CAAC,CAAC;MAC3D;IACF;IACA;IACA,OAAOE,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC,EAAE,OAAO;MAAEN,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC;EACzD,CAAC,CAAC;;EAEF;EACA,MAAM,CAACO,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjE,QAAQ,CAGrD;IACDkE,SAAS,EAAE,CAAC,EAAE,CAAC;IACfC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAM,IAAI,CAAC;EACjE,MAAM,CAACsE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACwE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAAC0E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7E,QAAQ,CAAa,IAAI,CAAC;;EAE1E;EACA,MAAM,CAAC8E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/E,QAAQ,CAAkB,OAAO,CAAC;EACtF,MAAM,CAACgF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjF,QAAQ,CAAS,MAAM;IACvE,MAAMkF,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAClD,OAAOF,IAAI,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACxC,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxF,QAAQ,CAAwD,EAAE,CAAC;EACvG,MAAM,CAACyF,eAAe,EAAEC,kBAAkB,CAAC,GAAG1F,QAAQ,CAAC;IAAE2F,KAAK,EAAE,EAAE;IAAE/D,IAAI,EAAE;EAAG,CAAC,CAAC;EAC/E,MAAM,CAACgE,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAgD,EAAE,CAAC;EAC3F,MAAM,CAAC8F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/F,QAAQ,CAAW,EAAE,CAAC;EAC1E,MAAM,CAACgG,eAAe,EAAEC,kBAAkB,CAAC,GAAGjG,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAMkG,SAAS,GAAGnG,MAAM,CAAqB,IAAI,CAAC;;EAElD;EACA,MAAM,CAACoG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpG,QAAQ,CAAW,MAAM;IAC3E;IACA,MAAMqG,aAAuB,GAAG,EAAE;IAClC,MAAMC,KAAK,GAAGpD,YAAY,CAACC,OAAO,CAAC,wCAAwC,CAAC;IAC5E,IAAIoD,eAAe,GAAG1C,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC,EAAE,OAAO;MAAEN,IAAI,EAAE;IAAG,CAAC,CAAC,CAAC;IACtE,IAAI6C,KAAK,EAAE;MACT,IAAI;QACF,MAAME,GAAG,GAAGnD,IAAI,CAACC,KAAK,CAACgD,KAAK,CAAC;QAC7B,IAAIzC,KAAK,CAAC4C,OAAO,CAACD,GAAG,CAAC,IAAIA,GAAG,CAACzC,MAAM,KAAK,EAAE,EAAE;UAC3CwC,eAAe,GAAGC,GAAG,CAACjD,GAAG,CAAEC,IAAS,KAAM;YAAEC,IAAI,EAAED,IAAI,CAACC,IAAI,IAAI;UAAG,CAAC,CAAC,CAAC;QACvE;MACF,CAAC,CAAC,MAAM,CAAC;IACX;IACA8C,eAAe,CAACG,OAAO,CAAC,CAACf,KAAK,EAAEgB,KAAK,KAAK;MACxC,IAAIhB,KAAK,CAAClC,IAAI,IAAIkC,KAAK,CAAClC,IAAI,CAACmD,IAAI,CAAC,CAAC,EAAE;QACnCP,aAAa,CAACQ,IAAI,CAACF,KAAK,CAAC;MAC3B;IACF,CAAC,CAAC;IACF,OAAON,aAAa;EACtB,CAAC,CAAC;;EAEF;EACA,MAAMS,WAAW,GAAG,IAAIC,eAAe,CAAC3F,QAAQ,CAAC4F,MAAM,CAAC;EACxD,MAAMC,UAAU,GAAGH,WAAW,CAACI,GAAG,CAAC,YAAY,CAAC;EAChDpH,SAAS,CAAC,MAAM;IACd,IAAImH,UAAU,EAAEE,aAAa,CAACF,UAAU,CAAC;EAC3C,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAME,aAAa,GAAG,MAAOC,EAAU,IAAK;IAC1C1E,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAM2E,GAAG,GAAG,MAAM5G,6BAA6B,CAAC6G,eAAe,CAACF,EAAE,CAAC;MACnE,IAAIC,GAAG,CAAChF,OAAO,IAAIgF,GAAG,CAACE,QAAQ,EAAE;QAC/B,MAAMC,IAAI,GAAGH,GAAG,CAACE,QAAQ;QACzB,IAAI,CAACjG,YAAY,EAAEC,eAAe,CAAC,qBAAqBiG,IAAI,CAAC5F,IAAI,EAAE,CAAC;QACpE,MAAM6F,OAAO,GAAG,CAAC,GAAG1E,aAAa,CAAC;QAClC0E,OAAO,CAAC,CAAC,CAAC,GAAG;UACXhE,IAAI,EAAE+D,IAAI,CAACE,OAAO,IAAI;QACxB,CAAC;QACD1E,gBAAgB,CAACyE,OAAO,CAAC;MAC3B,CAAC,MAAM;QACLrF,QAAQ,CAAC,0BAA0B,CAAC;MACtC;IACF,CAAC,CAAC,MAAM;MACNA,QAAQ,CAAC,0BAA0B,CAAC;IACtC,CAAC,SAAS;MACRM,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMiF,cAAc,GAAIlE,IAAY,IAAK;IACvC;IACA,MAAMgE,OAAO,GAAG,CAAC,GAAG1E,aAAa,CAAC;IAClC,MAAM6E,YAAY,GAAG/E,YAAY,GAAG,CAAC;IACrC4E,OAAO,CAACG,YAAY,CAAC,GAAG;MAAEnE;IAAK,CAAC;IAChCT,gBAAgB,CAACyE,OAAO,CAAC;IACzBvE,YAAY,CAAC2E,OAAO,CAAC,wCAAwC,EAAExE,IAAI,CAACyE,SAAS,CAACL,OAAO,CAAC,CAAC;;IAEvF;IACA,IAAIhE,IAAI,IAAIA,IAAI,CAACmD,IAAI,CAAC,CAAC,EAAE;MACvBR,qBAAqB,CAAC2B,IAAI,IAAI;QAC5B,IAAI,CAACA,IAAI,CAACC,QAAQ,CAACJ,YAAY,CAAC,EAAE;UAChC;UACA,OAAO,CAAC,GAAGG,IAAI,EAAEH,YAAY,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;QACtD;QACA,OAAOJ,IAAI,CAAC,CAAC;MACf,CAAC,CAAC;MACFnE,OAAO,CAACwE,GAAG,CAAC,uBAAuBR,YAAY,wBAAwB,CAAC;IAC1E,CAAC,MAAM;MACL;MACAxB,qBAAqB,CAAC2B,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKV,YAAY,CAAC,CAAC;MACnEhE,OAAO,CAACwE,GAAG,CAAC,2BAA2BR,YAAY,gCAAgC,CAAC;IACtF;EACF,CAAC;;EAED;EACA9H,SAAS,CAAC,MAAM;IACd,IAAIwE,kBAAkB,EAAE;MACtBK,mBAAmB,CAAC,IAAI,CAAC;MACzBlE,6BAA6B,CAAC8H,eAAe,CAAC,CAAC,CAC5CC,IAAI,CAACC,CAAC,IAAIhE,gBAAgB,CAACgE,CAAC,CAACC,IAAI,IAAI,EAAE,CAAC,CAAC,CACzCC,KAAK,CAAC/E,OAAO,CAACzB,KAAK,CAAC,CACpByG,OAAO,CAAC,MAAMjE,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAC9C;EACF,CAAC,EAAE,CAACL,kBAAkB,CAAC,CAAC;EAExB,MAAMuE,oBAAoB,GAAG,MAAOzB,EAAU,IAAK;IACjDzC,mBAAmB,CAAC,IAAI,CAAC;IACzB,IAAI;MACFf,OAAO,CAACwE,GAAG,CAAC,uCAAuChB,EAAE,EAAE,CAAC;MACxD,MAAMqB,CAAC,GAAG,MAAMhI,6BAA6B,CAAC6G,eAAe,CAACF,EAAE,CAAC;MACjExD,OAAO,CAACwE,GAAG,CAAC,gCAAgC,EAAEK,CAAC,CAAC;MAChD,IAAIA,CAAC,CAACpG,OAAO,IAAIoG,CAAC,CAAClB,QAAQ,EAAE;QAAA,IAAAuB,mBAAA;QAC3BlF,OAAO,CAACwE,GAAG,CAAC,oCAAoC,EAAE;UAChDhB,EAAE,EAAEqB,CAAC,CAAClB,QAAQ,CAACH,EAAE,IAAIqB,CAAC,CAAClB,QAAQ,CAACwB,GAAG;UACnCnH,IAAI,EAAE6G,CAAC,CAAClB,QAAQ,CAAC3F,IAAI;UACrBoH,OAAO,EAAE,CAAC,CAACP,CAAC,CAAClB,QAAQ,CAAC0B,WAAW;UACjCC,UAAU,EAAE,CAAC,CAACT,CAAC,CAAClB,QAAQ,CAACG,OAAO;UAChCyB,aAAa,EAAE,EAAAL,mBAAA,GAAAL,CAAC,CAAClB,QAAQ,CAACG,OAAO,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoB/E,MAAM,KAAI;QAC/C,CAAC,CAAC;MACJ;MACAc,mBAAmB,CAAC4D,CAAC,CAAClB,QAAQ,CAAC;IACjC,CAAC,CAAC,OAAO6B,GAAG,EAAE;MACZxF,OAAO,CAACzB,KAAK,CAAC,mCAAmC,EAAEiH,GAAG,CAAC;MACvDhH,QAAQ,CAAC,mCAAmC,CAAC;IAC/C,CAAC,SAAS;MACRuC,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,MAAM0E,iBAAiB,GAAGA,CAAA,KAAM;IAC9BzF,OAAO,CAACwE,GAAG,CAAC,kCAAkC,EAAExD,gBAAgB,GAAG;MACjEwC,EAAE,EAAExC,gBAAgB,CAACwC,EAAE,IAAIxC,gBAAgB,CAACmE,GAAG;MAC/CnH,IAAI,EAAEgD,gBAAgB,CAAChD,IAAI;MAC3BoH,OAAO,EAAE,CAAC,CAACpE,gBAAgB,CAACqE,WAAW;MACvCC,UAAU,EAAE,CAAC,CAACtE,gBAAgB,CAAC8C;IACjC,CAAC,GAAG,sBAAsB,CAAC;IAE3B,IAAI9C,gBAAgB,EAAE;MACpB,MAAM6C,OAAO,GAAG,CAAC,GAAG1E,aAAa,CAAC;MAClC0E,OAAO,CAAC5E,YAAY,GAAG,CAAC,CAAC,GAAG;QAC1BY,IAAI,EAAEmB,gBAAgB,CAAC8C,OAAO,IAAI;MACpC,CAAC;MAED9D,OAAO,CAACwE,GAAG,CAAC,iCAAiC,EAAE;QAC7CkB,UAAU,EAAEzG,YAAY,GAAG,CAAC;QAC5BmG,OAAO,EAAE,CAAC,CAACvB,OAAO,CAAC5E,YAAY,GAAG,CAAC,CAAC,CAACY,IAAI;QACzC8F,OAAO,EAAE,CAAC,CAAC9B,OAAO,CAAC5E,YAAY,GAAG,CAAC,CAAC,CAACY,IAAI;QACzC+F,UAAU,EAAE/B,OAAO,CAAC5E,YAAY,GAAG,CAAC,CAAC,CAACY,IAAI,CAACM;MAC7C,CAAC,CAAC;;MAEF;MACAf,gBAAgB,CAACyE,OAAO,CAAC;MACzBvE,YAAY,CAAC2E,OAAO,CAAC,wCAAwC,EAAExE,IAAI,CAACyE,SAAS,CAACL,OAAO,CAAC,CAAC;;MAEvF;MACA5C,mBAAmB,CAAC,IAAI,CAAC;MACzBN,qBAAqB,CAAC,KAAK,CAAC;;MAE5B;MACA;MACAkF,UAAU,CAAC,MAAM;QACf;QACA;QACA3G,eAAe,CAAC,CAAC,CAAC,CAAC;;QAEnB;QACA2G,UAAU,CAAC,MAAM;UACf3G,eAAe,CAACD,YAAY,CAAC;QAC/B,CAAC,EAAE,EAAE,CAAC;MACR,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC;;EAED;EACA,MAAM6G,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAACpI,YAAY,IAAI,CAACE,OAAO,IAAI,CAACE,QAAQ,IAAI,CAACG,SAAS,EAAE;MACxDO,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;IACA,MAAMuH,WAAW,GAAG5G,aAAa,CAACsF,MAAM,CAAC1E,CAAC,IAAIA,CAAC,CAACF,IAAI,CAACmD,IAAI,CAAC,CAAC,CAAC;IAC5D,IAAI+C,WAAW,CAAC5F,MAAM,KAAK,CAAC,EAAE;MAC5B3B,QAAQ,CAAC,uDAAuD,CAAC;MACjE;IACF;IACA,IAAImD,UAAU,CAACxB,MAAM,KAAK,CAAC,EAAE;MAC3B3B,QAAQ,CAAC,mCAAmC,CAAC;MAC7C;IACF;IAEAI,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MAAA,IAAAoH,oBAAA;MACF;MACA,MAAMC,kBAAkB,GAAG9G,aAAa,CAACsF,MAAM,CAAC,CAAC1C,KAAK,EAAEgB,KAAK,KAC3DR,kBAAkB,CAAC6B,QAAQ,CAACrB,KAAK,CAAC,IAAIhB,KAAK,CAAClC,IAAI,IAAIkC,KAAK,CAAClC,IAAI,CAACmD,IAAI,CAAC,CACtE,CAAC;;MAED;MACA,IAAIiD,kBAAkB,CAAC9F,MAAM,KAAK,CAAC,EAAE;QACnC3B,QAAQ,CAAC,mEAAmE,CAAC;QAC7EI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA,IAAI+C,UAAU,CAACxB,MAAM,KAAK,CAAC,EAAE;QAC3B3B,QAAQ,CAAC,mCAAmC,CAAC;QAC7CI,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MACA;;MAEA;MACA,MAAMsH,YAAiB,GAAG;QACxBlI,IAAI,EAAEN,YAAY;QAClBE,OAAO;QACPE,QAAQ;QACRG,SAAS;QACTI,OAAO,EAAEA,OAAO,IAAIJ,SAAS;QAC7BkI,SAAS,EAAEjF,kBAAkB,KAAK,OAAO;QACzC,IAAIA,kBAAkB,KAAK,OAAO,IAAI;UAAEkF,YAAY,EAAE,IAAI7E,IAAI,CAACH,iBAAiB,CAAC,CAACK,WAAW,CAAC;QAAE,CAAC,CAAC;QAClG4E,aAAa,EAAE1E,UAAU;QACzB2E,MAAM,EAAE/I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,EAAE;QAChBpF,MAAM,EAAE,OAAO;QACf;QACAmI,WAAW,EAAE,EAAAP,oBAAA,GAAAC,kBAAkB,CAAC,CAAC,CAAC,cAAAD,oBAAA,uBAArBA,oBAAA,CAAuBnG,IAAI,KAAI,EAAE;QAC9C;QACAV,aAAa,EAAE8G,kBAAkB,CAACtG,GAAG,CAACI,CAAC,KAAK;UAAEF,IAAI,EAAEE,CAAC,CAACF;QAAK,CAAC,CAAC;MAC/D,CAAC;;MAED;MACA,IAAIoG,kBAAkB,CAAC9F,MAAM,GAAG,CAAC,EAAE;QACjC+F,YAAY,CAACM,QAAQ,GAAG;UACtBjG,IAAI,EAAEH,gBAAgB,CAACG,IAAI;UAC3B;UACAkG,cAAc,EAAErG,gBAAgB,CAACE,SAAS,CAACoB,KAAK,CAAC,CAAC,EAAEuE,kBAAkB,CAAC9F,MAAM,GAAG,CAAC,CAAC,CAC/ER,GAAG,CAAC+G,CAAC,KAAK;YAAEC,KAAK,EAAED,CAAC;YAAEnG,IAAI,EAAEH,gBAAgB,CAACG;UAAK,CAAC,CAAC;QACzD,CAAC;MACH;MAEAP,OAAO,CAACwE,GAAG,CAAC,0CAA0C,EAAE0B,YAAY,CAAC;;MAErE;MACA,MAAMzC,GAAG,GAAG,MAAM1G,WAAW,CAAC6J,cAAc,CAACV,YAAY,CAAC;MAC1D;MACA,MAAMW,UAAU,GAAG,CAAApD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE0B,GAAG,MAAI1B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAED,EAAE;MACtCxD,OAAO,CAACwE,GAAG,CAAC,2BAA2B,EAAEf,GAAG,EAAE,eAAe,EAAEoD,UAAU,CAAC;MAE1EnI,UAAU,CAACwC,kBAAkB,KAAK,OAAO,GACrC,0BAA0B,IAAIK,IAAI,CAACH,iBAAiB,CAAC,CAAC0F,cAAc,CAAC,CAAC,EAAE,GACxE,iDAAiD,CAAC;MACtDxH,YAAY,CAACyH,UAAU,CAAC,wCAAwC,CAAC;MACjE;MACAzH,YAAY,CAACyH,UAAU,CAAC,wBAAwB,CAAC;MACjDC,cAAc,CAAC/C,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC;MACjD4B,UAAU,CAAC,MAAM;QACf;QACA,IAAIgB,UAAU,EAAEpJ,QAAQ,CAAC,cAAcoJ,UAAU,UAAU,CAAC,CAAC,KACxD;UACH7G,OAAO,CAACiH,IAAI,CAAC,6DAA6D,CAAC;UAC3ExJ,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAO+H,GAAQ,EAAE;MAAA,IAAA0B,aAAA,EAAAC,kBAAA;MACjB3I,QAAQ,CAAC,EAAA0I,aAAA,GAAA1B,GAAG,CAAC4B,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcpC,IAAI,cAAAqC,kBAAA,uBAAlBA,kBAAA,CAAoBE,OAAO,KAAI,2BAA2B,CAAC;IACtE,CAAC,SAAS;MACRzI,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0I,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI5J,YAAY,IAAIE,OAAO,IAAIE,QAAQ,IAAIG,SAAS,IAAII,OAAO,EAAE;MAC/DiB,YAAY,CAAC2E,OAAO,CAAC,wBAAwB,EAC3CxE,IAAI,CAACyE,SAAS,CAAC;QAAExG,YAAY;QAAEE,OAAO;QAAEE,QAAQ;QAAEG,SAAS;QAAEI;MAAQ,CAAC,CAAC,CAAC;IAC5E;IACAZ,QAAQ,CAAC,kBAAkB,CAAC;EAC9B,CAAC;EAEDvB,SAAS,CAAC,MAAM;IACd,MAAMqL,KAAK,GAAGjI,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC;IAC5D,IAAIgI,KAAK,EAAE;MACT,IAAI;QACF,MAAMb,CAAC,GAAGjH,IAAI,CAACC,KAAK,CAAC6H,KAAK,CAAC;QAC3B5J,eAAe,CAAC+I,CAAC,CAAChJ,YAAY,IAAI,EAAE,CAAC;QACrCG,UAAU,CAAC6I,CAAC,CAAC9I,OAAO,IAAI,EAAE,CAAC;QAC3BG,WAAW,CAAC2I,CAAC,CAAC5I,QAAQ,KAAIP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,KAAI,EAAE,CAAC;QAC3CE,YAAY,CAACwI,CAAC,CAACzI,SAAS,IAAI,EAAE,CAAC;QAC/BK,UAAU,CAACoI,CAAC,CAACrI,OAAO,IAAI,EAAE,CAAC;MAC7B,CAAC,CAAC,MAAM,CAAC;MACTiB,YAAY,CAACyH,UAAU,CAAC,wBAAwB,CAAC;IACnD;EACF,CAAC,EAAE,CAACxJ,IAAI,CAAC,CAAC;EAEV,MAAMiK,iBAAiB,GAAI9C,CAAS,IAAKxF,eAAe,CAACwF,CAAC,CAAC;EAE3DxI,SAAS,CAAC,MAAM;IACd,IAAI6C,IAAI,KAAK,CAAC,EAAE;MACd;MACA,MAAM0I,gBAAgB,GAAGtI,aAAa,CAACuI,MAAM,CAAC,CAACC,KAAK,EAAE5F,KAAK,EAAEgB,KAAK,KAAK;QACrE,IAAIR,kBAAkB,CAAC6B,QAAQ,CAACrB,KAAK,CAAC,IAAIhB,KAAK,CAAClC,IAAI,IAAIkC,KAAK,CAAClC,IAAI,CAACmD,IAAI,CAAC,CAAC,EAAE;UACzE,OAAO2E,KAAK,GAAG,CAAC;QAClB;QACA,OAAOA,KAAK;MACd,CAAC,EAAE,CAAC,CAAC;MAEL3H,OAAO,CAACwE,GAAG,CAAC,yCAAyC,EAAEiD,gBAAgB,CAAC;MAExE,IAAIA,gBAAgB,GAAG,CAAC,EAAE;QACxBpH,mBAAmB,CAAC8D,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP;UACA7D,SAAS,EAAEL,KAAK,CAACwH,gBAAgB,GAAG,CAAC,CAAC,CAACG,IAAI,CAACzD,IAAI,CAAC7D,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;QACrE,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACAD,mBAAmB,CAAC8D,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE7D,SAAS,EAAE;QAAG,CAAC,CAAC,CAAC;MAC3D;IACF;EACF,CAAC,EAAE,CAACvB,IAAI,EAAEI,aAAa,EAAEoD,kBAAkB,CAAC,CAAC,CAAC,CAAC;;EAE/C,MAAMsF,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI9I,IAAI,KAAK,CAAC,EAAE;MACdsD,kBAAkB,CAAC,IAAI,CAAC;MACxB,IAAI;QAAA,IAAAyF,OAAA;QACF,MAAMjD,CAAC,GAAG,MAAM/H,GAAG,CAACwG,GAAG,CAAC,WAAW,CAAC;QACpC,IAAIyE,IAAI,GAAG9H,KAAK,CAAC4C,OAAO,CAACgC,CAAC,CAACC,IAAI,CAAC,GAAGD,CAAC,CAACC,IAAI,GAAG,EAAAgD,OAAA,GAAAjD,CAAC,CAACC,IAAI,cAAAgD,OAAA,uBAANA,OAAA,CAAQhD,IAAI,KAAI,EAAE;QAC9D7C,WAAW,CAAC8F,IAAI,CAACpI,GAAG,CAAEqI,CAAM,KAAM;UAChCxE,EAAE,EAAEwE,CAAC,CAACxE,EAAE,IAAIwE,CAAC,CAAC7C,GAAG,IAAI8C,MAAM,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;UAC1CpG,KAAK,EAAEiG,CAAC,CAACjG,KAAK;UACd/D,IAAI,EAAEgK,CAAC,CAAChK,IAAI,IAAIgK,CAAC,CAACI,QAAQ,IAAI;QAChC,CAAC,CAAC,CAAC,CAAC;MACN,CAAC,CAAC,MAAM;QACN5J,QAAQ,CAAC,yBAAyB,CAAC;MACrC,CAAC,SAAS;QACR6D,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IACF;EACF,CAAC;EAEDnG,SAAS,CAAC,MAAM;IACd2L,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAAC9I,IAAI,CAAC,CAAC;EAEV,MAAMsJ,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAI,CAACxG,eAAe,CAACE,KAAK,EAAE;MAC1BvD,QAAQ,CAAC,gCAAgC,CAAC;IAC5C,CAAC,MAAM,IAAI,CAACmD,UAAU,CAAC2G,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAKF,eAAe,CAACE,KAAK,CAAC,EAAE;MACnEH,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE;QAAE6B,EAAE,EAAE,IAAI;QAAE,GAAG3B;MAAgB,CAAC,CAAC,CAAC;MAChEC,kBAAkB,CAAC;QAAEC,KAAK,EAAE,EAAE;QAAE/D,IAAI,EAAE;MAAG,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLQ,QAAQ,CAAC,0BAA0B,CAAC;IACtC;IACAqH,UAAU,CAAC,MAAMrH,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACtC,CAAC;EAED,MAAM+J,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,KAAK,GAAGxG,QAAQ,CAACyC,MAAM,CAACuD,CAAC,IAC7B9F,kBAAkB,CAACkC,QAAQ,CAAC4D,CAAC,CAACxE,EAAE,CAAC,IAAI,CAAC7B,UAAU,CAAC2G,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAKiG,CAAC,CAACjG,KAAK,CAChF,CAAC;IACDH,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE,GAAG6G,KAAK,CAAC,CAAC;IACxCrG,qBAAqB,CAAC,EAAE,CAAC;EAC3B,CAAC;EACD,MAAMsG,sBAAsB,GAAI1I,CAAuC,IACrEoC,qBAAqB,CAAClC,KAAK,CAACC,IAAI,CAACH,CAAC,CAAC2I,MAAM,CAACC,eAAe,EAAEC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EAC3E,MAAMC,eAAe,GAAI/G,KAAa,IACpCH,aAAa,CAACD,UAAU,CAAC8C,MAAM,CAACI,CAAC,IAAIA,CAAC,CAAC9C,KAAK,KAAKA,KAAK,CAAC,CAAC;;EAE1D;EACA,MAAMgH,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BvK,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAIO,IAAI,KAAK,CAAC,KAAK,CAACrB,YAAY,IAAI,CAACE,OAAO,IAAI,CAACE,QAAQ,IAAI,CAACG,SAAS,CAAC,EAAE;MACxEO,QAAQ,CAAC,qCAAqC,CAAC;MAC/C;IACF;;IAEA;IACA,IAAIO,IAAI,KAAK,CAAC,EAAE;MACdiB,OAAO,CAACwE,GAAG,CAAC,uCAAuC,CAAC;MACpD,IAAIwE,aAAsC,GAAG,IAAI;MACjD,IAAIC,mBAAmB,GAAG,KAAK;MAC/B,IAAIC,YAAY,GAAG,KAAK;;MAExB;MACA,IAAI5G,SAAS,CAAC6G,OAAO,EAAE;QACrBnJ,OAAO,CAACwE,GAAG,CAAC,wCAAwC,EAAEvF,YAAY,CAAC;QACnE;QACA+J,aAAa,GAAG,MAAM1G,SAAS,CAAC6G,OAAO,CAACC,IAAI,CAAC,CAAC;QAC9C,IAAIJ,aAAa,EAAE;UACjBhJ,OAAO,CAACwE,GAAG,CAAC,uCAAuC,CAAC;UACpD;UACAxE,OAAO,CAACwE,GAAG,CAAC,gBAAgB,EAAEwE,aAAa,CAACnJ,IAAI,GAAGmJ,aAAa,CAACnJ,IAAI,CAACwJ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG,SAAS,CAAC;;UAE3G;UACA,IAAIL,aAAa,CAACnJ,IAAI,IAAImJ,aAAa,CAACnJ,IAAI,CAACmD,IAAI,CAAC,CAAC,EAAE;YACnDhD,OAAO,CAACwE,GAAG,CAAC,sCAAsC,CAAC;YACnDyE,mBAAmB,GAAG,IAAI;UAC5B,CAAC,MAAM;YACLjJ,OAAO,CAACwE,GAAG,CAAC,wCAAwC,CAAC;UACvD;;UAEA;UACAxE,OAAO,CAACwE,GAAG,CAAC,2CAA2C,CAAC;UACxDT,cAAc,CAACiF,aAAa,CAACnJ,IAAI,CAAC;QAEpC,CAAC,MAAM;UACL;UACAG,OAAO,CAACiH,IAAI,CAAC,kEAAkE,CAAC;QAClF;MACF,CAAC,MAAM;QACJjH,OAAO,CAACzB,KAAK,CAAC,uDAAuD,CAAC;MACzE;MACA;;MAEA;MACA;MACA;;MAEA;MACA,MAAM+K,oBAAoB,GAAGL,mBAAmB,IAAI1G,kBAAkB,CAAC6B,QAAQ,CAACnF,YAAY,GAAG,CAAC,CAAC;MACjG,IAAIqK,oBAAoB,EAAE;QACxBtJ,OAAO,CAACwE,GAAG,CAAC,uCAAuC,CAAC;MACtD;MAEAxE,OAAO,CAACwE,GAAG,CAAC,oEAAoE,CAAC;MACjF;MACA0E,YAAY,GAAG/J,aAAa,CAACmJ,IAAI,CAAC,CAACvG,KAAK,EAAEgB,KAAK,KAAK;QAClD,MAAM4C,OAAO,GAAG5D,KAAK,CAAClC,IAAI,IAAIkC,KAAK,CAAClC,IAAI,CAACmD,IAAI,CAAC,CAAC;QAC/C,MAAMuG,QAAQ,GAAGhH,kBAAkB,CAAC6B,QAAQ,CAACrB,KAAK,CAAC;QACnD,IAAI4C,OAAO,IAAI4D,QAAQ,EAAE;UACpBvJ,OAAO,CAACwE,GAAG,CAAC,eAAezB,KAAK,mCAAmC,CAAC;UACpE,OAAO,IAAI;QAChB;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MACF,IAAI,CAACmG,YAAY,EAAE;QACflJ,OAAO,CAACwE,GAAG,CAAC,gDAAgD,CAAC;MACjE;;MAEA;MACA,MAAMgF,iBAAiB,GAAGF,oBAAoB,IAAIJ,YAAY;MAC9DlJ,OAAO,CAACwE,GAAG,CAAC,qCAAqC,EAAEgF,iBAAiB,EAAE,kBAAkBF,oBAAoB,mBAAmBJ,YAAY,GAAG,CAAC;MAE/I,IAAI,CAACM,iBAAiB,EAAE;QACtBhL,QAAQ,CAAC,2FAA2F,CAAC;QACrG,OAAO,CAAC;MACV;MACAwB,OAAO,CAACwE,GAAG,CAAC,+CAA+C,CAAC;IAC9D;;IAEA;IACA,IAAIzF,IAAI,KAAK,CAAC,EAAE;MACd,MAAM0K,GAAG,GAAGtK,aAAa,CAACsF,MAAM,CAAC1E,CAAC,IAAIA,CAAC,CAACF,IAAI,CAACmD,IAAI,CAAC,CAAC,CAAC,CAAC7C,MAAM;MAC3D,IAAIsJ,GAAG,GAAG,CAAC,EAAE;QACX,IAAIrJ,gBAAgB,CAACE,SAAS,CAACH,MAAM,KAAKsJ,GAAG,GAAG,CAAC,EAAE;UACjDjL,QAAQ,CAAC,UAAUiL,GAAG,GAAG,CAAC,aAAa,CAAC;UACxC;QACF;QACA,IAAIrJ,gBAAgB,CAACE,SAAS,CAACgI,IAAI,CAACoB,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,EAAE;UAChDlL,QAAQ,CAAC,6BAA6B,CAAC;UACvC;QACF;MACF;IACF;;IAEA;IACA,IAAIO,IAAI,KAAK,CAAC,IAAI4C,UAAU,CAACxB,MAAM,KAAK,CAAC,EAAE;MACzC3B,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACF;;IAEA;IACA,IAAIO,IAAI,GAAG,CAAC,EAAE;MACZiB,OAAO,CAACwE,GAAG,CAAC,wBAAwBzF,IAAI,YAAYA,IAAI,GAAG,CAAC,EAAE,CAAC;MAC/DC,OAAO,CAACD,IAAI,GAAG,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAM4K,cAAc,GAAGA,CAAA,KAAM;IAC3BnL,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAIO,IAAI,GAAG,CAAC,EAAEC,OAAO,CAACD,IAAI,GAAG,CAAC,CAAC;EACjC,CAAC;;EAED;EACA,MAAM6K,uBAAuB,GAAI7G,KAAa,IAAK;IACjD,MAAMc,OAAO,GAAG,CAAC,GAAG1E,aAAa,CAAC;IAClC0E,OAAO,CAACd,KAAK,CAAC,GAAG;MAAElD,IAAI,EAAE;IAAG,CAAC;IAC7BT,gBAAgB,CAACyE,OAAO,CAAC;IACzBvE,YAAY,CAAC2E,OAAO,CAAC,wCAAwC,EAAExE,IAAI,CAACyE,SAAS,CAACL,OAAO,CAAC,CAAC;IACvF;IACArB,qBAAqB,CAAC2B,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK3B,KAAK,CAAC,CAAC;IAC5D/C,OAAO,CAACwE,GAAG,CAAC,+CAA+CzB,KAAK,EAAE,CAAC;EACrE,CAAC;EACD;;EAEA,MAAM8G,oBAAoB,GAAGA,CAAA,kBAC3B5M,OAAA,CAACR,KAAK;IAACqN,MAAM,EAAEpJ,kBAAmB;IAACqJ,OAAO,EAAEA,CAAA,KAAMpJ,qBAAqB,CAAC,KAAK,CAAE;IAACqJ,KAAK,EAAC,iBAAiB;IAAAC,QAAA,gBACrGhN,OAAA;MAAKiN,SAAS,EAAC,wEAAwE;MAAAD,QAAA,EACpFnJ,gBAAgB,gBACb7D,OAAA;QAAAgN,QAAA,EAAG;MAAU;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,GACjB1J,aAAa,CAACjB,GAAG,CAAC4K,CAAC,iBACnBtN,OAAA,CAACX,IAAI;QAEH4N,SAAS,EAAE,kBAAkB,CAAAlJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwC,EAAE,MAAK+G,CAAC,CAAC/G,EAAE,GAAG,qBAAqB,GAAG,EAAE,EAAG;QAC1FgH,OAAO,EAAEA,CAAA,KAAMvF,oBAAoB,CAACsF,CAAC,CAAC/G,EAAE,CAAE;QAAAyG,QAAA,gBAE1ChN,OAAA;UAAIiN,SAAS,EAAC,6BAA6B;UAAAD,QAAA,EAAEM,CAAC,CAACvM;QAAI;UAAAmM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzDrN,OAAA;UAAKiN,SAAS,EAAC,0FAA0F;UAAAD,QAAA,EACtGM,CAAC,CAACE,YAAY,gBAAGxN,OAAA;YAAKyN,GAAG,EAAEH,CAAC,CAACE,YAAa;YAACE,GAAG,EAAEJ,CAAC,CAACvM,IAAK;YAACkM,SAAS,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAY;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChH,CAAC;MAAA,GAPDC,CAAC,CAAC/G,EAAE;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQL,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAED,CAAC,eACNrN,OAAA;MAAKiN,SAAS,EAAC,6BAA6B;MAAAD,QAAA,gBAC1ChN,OAAA,CAACZ,MAAM;QAACuO,OAAO,EAAC,WAAW;QAACJ,OAAO,EAAEA,CAAA,KAAM7J,qBAAqB,CAAC,KAAK,CAAE;QAAAsJ,QAAA,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxFrN,OAAA,CAACZ,MAAM;QAACmO,OAAO,EAAE/E,iBAAkB;QAACoF,QAAQ,EAAE,CAAC7J,gBAAgB,IAAIF,gBAAiB;QAAAmJ,QAAA,EAAC;MAErF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CACR;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,aAAA,EAAAC,aAAA;IAC9B,QAAQjM,IAAI;MACV,KAAK,CAAC;QAAE,oBACN9B,OAAA;UAAKiN,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBhN,OAAA,CAACT,KAAK;YAACgH,EAAE,EAAC,cAAc;YAACxF,IAAI,EAAC,cAAc;YAACiN,KAAK,EAAC,eAAe;YAACpC,KAAK,EAAEnL,YAAa;YAACwN,QAAQ,EAAEnL,CAAC,IAAIpC,eAAe,CAACoC,CAAC,CAAC2I,MAAM,CAACG,KAAK,CAAE;YAACsC,QAAQ;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnJrN,OAAA,CAACT,KAAK;YAACgH,EAAE,EAAC,SAAS;YAACxF,IAAI,EAAC,SAAS;YAACiN,KAAK,EAAC,eAAe;YAACpC,KAAK,EAAEjL,OAAQ;YAACsN,QAAQ,EAAEnL,CAAC,IAAIlC,UAAU,CAACkC,CAAC,CAAC2I,MAAM,CAACG,KAAK,CAAE;YAACsC,QAAQ;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/HrN,OAAA;YAAKiN,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBACpDhN,OAAA,CAACT,KAAK;cAACgH,EAAE,EAAC,UAAU;cAACxF,IAAI,EAAC,UAAU;cAACiN,KAAK,EAAC,WAAW;cAACpC,KAAK,EAAE/K,QAAS;cAACoN,QAAQ,EAAEnL,CAAC,IAAIhC,WAAW,CAACgC,CAAC,CAAC2I,MAAM,CAACG,KAAK,CAAE;cAACsC,QAAQ;YAAA;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/HrN,OAAA,CAACT,KAAK;cACJgH,EAAE,EAAC,WAAW;cACdxF,IAAI,EAAC,WAAW;cAChBiN,KAAK,EAAC,YAAY;cAClBG,IAAI,EAAC,OAAO;cACZvC,KAAK,EAAE5K,SAAU;cACjBiN,QAAQ,EAAEnL,CAAC,IAAI7B,YAAY,CAAC6B,CAAC,CAAC2I,MAAM,CAACG,KAAK,CAAE;cAC5CgC,QAAQ,EAAE,CAAAtN,IAAI,aAAJA,IAAI,wBAAAwN,aAAA,GAAJxN,IAAI,CAAEY,MAAM,cAAA4M,aAAA,uBAAZA,aAAA,CAAc3M,MAAM,MAAK,QAAS;cAC5C+M,QAAQ;cACRE,QAAQ,EAAE,CAAA9N,IAAI,aAAJA,IAAI,wBAAAyN,aAAA,GAAJzN,IAAI,CAAEY,MAAM,cAAA6M,aAAA,uBAAZA,aAAA,CAAc5M,MAAM,MAAK,QAAQ,GAAG,0BAA0Bb,IAAI,CAACY,MAAM,CAACH,IAAI,EAAE,GAAG;YAAoC;cAAAmM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrN,OAAA,CAACT,KAAK;YAACgH,EAAE,EAAC,SAAS;YAACxF,IAAI,EAAC,SAAS;YAACiN,KAAK,EAAC,2BAA2B;YAACG,IAAI,EAAC,OAAO;YAACvC,KAAK,EAAExK,OAAQ;YAAC6M,QAAQ,EAAEnL,CAAC,IAAIzB,UAAU,CAACyB,CAAC,CAAC2I,MAAM,CAACG,KAAK;UAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5I,CAAC;MAER,KAAK,CAAC;QACJ,MAAMgB,IAAI,GAAGnM,aAAa,CAACF,YAAY,GAAG,CAAC,CAAC;QAC5C;QACA,MAAMsM,iBAAiB,GAAGpM,aAAa,CAACsF,MAAM,CAAC1E,CAAC,IAAIA,CAAC,CAACF,IAAI,CAACmD,IAAI,CAAC,CAAC,CAAC,CAAC7C,MAAM;QAEzE,oBACElD,OAAA;UAAKiN,SAAS,EAAC,wCAAwC;UAAAD,QAAA,gBAErDhN,OAAA;YAAKiN,SAAS,EAAC,qCAAqC;YAAAD,QAAA,gBAClDhN,OAAA;cAAIiN,SAAS,EAAC,oBAAoB;cAAAD,QAAA,EAAC;YAA0C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACjFrK,KAAK,CAACC,IAAI,CAAC;cAAEC,MAAM,EAAE;YAAG,CAAC,CAAC,CAACR,GAAG,CAAC,CAAC6L,CAAC,EAAEC,GAAG,KAAK;cAC1C,MAAMC,cAAc,GAAG,CAAC,EAAEvM,aAAa,CAACsM,GAAG,CAAC,CAAC5L,IAAI,IAAIV,aAAa,CAACsM,GAAG,CAAC,CAAC5L,IAAI,CAACmD,IAAI,CAAC,CAAC,CAAC;cACpF,MAAM2I,UAAU,GAAG1M,YAAY,KAAKwM,GAAG,GAAG,CAAC;cAC3C,MAAMlC,QAAQ,GAAGhH,kBAAkB,CAAC6B,QAAQ,CAACqH,GAAG,CAAC;;cAEjD;cACA,IAAIG,aAAsC,GAAG,WAAW;cACxD,IAAIC,aAAa,GAAG,EAAE;cACtB,IAAIC,UAAU,GAAG,SAASL,GAAG,GAAG,CAAC,EAAE;cACnC,IAAIM,IAAI,GAAG,IAAI;cAEf,IAAIL,cAAc,EAAE;gBAClB,IAAInC,QAAQ,EAAE;kBACZqC,aAAa,GAAG,SAAS,CAAC,CAAC;kBAC3BG,IAAI,gBAAG9O,OAAA;oBAAMiN,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;kBACnFwB,UAAU,IAAI,YAAY;gBAC5B,CAAC,MAAM;kBACLF,aAAa,GAAG,WAAW,CAAC,CAAC;kBAC7BC,aAAa,GAAG,wGAAwG,CAAC,CAAC;kBAC1HE,IAAI,gBAAG9O,OAAA;oBAAMiN,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,EAAC;kBAAC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,CAAC,CAAC;kBACnFwB,UAAU,IAAI,aAAa;gBAC7B;cACF,CAAC,MAAM;gBACJ;gBACA;gBACAD,aAAa,GAAG,sEAAsE;gBACtFC,UAAU,IAAI,+BAA+B;cAChD;cAEA,MAAME,kBAAkB,GAAGL,UAAU,GAAG,kEAAkE,GAAG,EAAE;cAE/G,oBACE1O,OAAA,CAACZ,MAAM;gBAELuO,OAAO,EAAEgB,aAAc,CAAC;gBAAA;gBACxBpB,OAAO,EAAEA,CAAA,KAAM;kBACb;kBACAtL,eAAe,CAACuM,GAAG,GAAG,CAAC,CAAC;;kBAExB;kBACA,IAAIC,cAAc,EAAE;oBAClBlJ,qBAAqB,CAAC2B,IAAI,IACxBA,IAAI,CAACC,QAAQ,CAACqH,GAAG,CAAC,GAAGtH,IAAI,CAACM,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAK+G,GAAG,CAAC,GAAG,CAAC,GAAGtH,IAAI,EAAEsH,GAAG,CAClE,CAAC;kBACH;gBACF,CAAE;gBACFQ,IAAI,EAAC,IAAI;gBACT/B,SAAS,EAAE,sDAAsD8B,kBAAkB,IAAIH,aAAa,EAAG;gBACvG7B,KAAK,EAAE0B,cAAc,GAAInC,QAAQ,GAAG,+BAA+B,GAAG,6BAA6B,GAAI,0BAA2B;gBAAAU,QAAA,gBAElIhN,OAAA;kBAAMiN,SAAS,EAAC,oBAAoB;kBAAAD,QAAA,EAAE6B;gBAAU;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACvDyB,IAAI;cAAA,GAlBAN,GAAG;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBF,CAAC;YAEb,CAAC,CAAC,eAGFrN,OAAA;cAAKiN,SAAS,EAAC,oEAAoE;cAAAD,QAAA,gBACjFhN,OAAA;gBAAGiN,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACxDrN,OAAA;gBAAGiN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9BhN,OAAA;kBAAMiN,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EAAC;gBAAC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjFrN,OAAA;kBAAMiN,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eACJrN,OAAA;gBAAGiN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9BhN,OAAA;kBAAMiN,SAAS,EAAC;gBAAqE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7FrN,OAAA;kBAAMiN,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,GAAC,eAAa,eAAAhN,OAAA;oBAAMiN,SAAS,EAAC,WAAW;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC,eACJrN,OAAA;gBAAGiN,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAC9BhN,OAAA;kBAAMiN,SAAS,EAAC;gBAA6G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrIrN,OAAA;kBAAMiN,SAAS,EAAC,kCAAkC;kBAAAD,QAAA,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACJrN,OAAA;gBAAGiN,SAAS,EAAC,+CAA+C;gBAAAD,QAAA,EAAC;cAAuE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtI,CAAC,eAENrN,OAAA;cAAKiN,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBhN,OAAA,CAACZ,MAAM;gBAACuO,OAAO,EAAC,WAAW;gBAACJ,OAAO,EAAEA,CAAA,KAAM7J,qBAAqB,CAAC,IAAI,CAAE;gBAAAsJ,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChGrN,OAAA,CAACZ,MAAM;gBAACuO,OAAO,EAAC,WAAW;gBAACJ,OAAO,EAAElD,qBAAsB;gBAAC4C,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrN,OAAA;YAAKiN,SAAS,EAAC,+BAA+B;YAAAD,QAAA,gBAC5ChN,OAAA,CAACV,eAAe;cAEd2P,GAAG,EAAE5J,SAAU;cACf6J,WAAW,EAAEb,IAAI,CAACzL,IAAK;cACvBuM,MAAM,EAAErI,cAAe;cACvBsI,MAAM,EAAC;YAAM,GAJR,SAASpN,YAAY,IAAIsC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;cAAA2I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAK3C,CAAC,eACFrN,OAAA;cAAGiN,SAAS,EAAC,+CAA+C;cAAAD,QAAA,EAAC;YAE7D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,CAAC;QAAE;UACN;UACA,MAAM7C,gBAAgB,GAAGtI,aAAa,CAACuI,MAAM,CAAC,CAACC,KAAK,EAAE5F,KAAK,EAAEgB,KAAK,KAAK;YACrE,IAAIR,kBAAkB,CAAC6B,QAAQ,CAACrB,KAAK,CAAC,IAAIhB,KAAK,CAAClC,IAAI,IAAIkC,KAAK,CAAClC,IAAI,CAACmD,IAAI,CAAC,CAAC,EAAE;cACzE,OAAO2E,KAAK,GAAG,CAAC;YAClB;YACA,OAAOA,KAAK;UACd,CAAC,EAAE,CAAC,CAAC;UACL3H,OAAO,CAACwE,GAAG,CAAC,+CAA+C,EAAEiD,gBAAgB,CAAC;;UAE9E;UACA,MAAM6E,0BAA0B,GAAIvM,CAAsC,IAAK;YAC7EoB,qBAAqB,CAACpB,CAAC,CAAC2I,MAAM,CAACG,KAAwB,CAAC;UAC1D,CAAC;UACD,MAAM0D,wBAAwB,GAAIxM,CAAsC,IAAK;YAC3EsB,oBAAoB,CAACtB,CAAC,CAAC2I,MAAM,CAACG,KAAK,CAAC;UACtC,CAAC;;UAED;UACA,MAAM2D,oBAAoB,GAAGA,CAACzJ,KAAa,EAAE8F,KAAa,KAAK;YAC7D,MAAM4D,QAAQ,GAAGC,QAAQ,CAAC7D,KAAK,EAAE,EAAE,CAAC;YACpC,IAAI,CAAC8D,KAAK,CAACF,QAAQ,CAAC,IAAIA,QAAQ,GAAG,CAAC,EAAE;cACpC,MAAMG,YAAY,GAAG,CAAC,GAAGxM,gBAAgB,CAACE,SAAS,CAAC;cACpDsM,YAAY,CAAC7J,KAAK,CAAC,GAAG0J,QAAQ;cAC9BpM,mBAAmB,CAAC;gBAAE,GAAGD,gBAAgB;gBAAEE,SAAS,EAAEsM;cAAa,CAAC,CAAC;YACvE;UACF,CAAC;UACD,MAAMC,gBAAgB,GAAI9M,CAAuC,IAAK;YACpEM,mBAAmB,CAAC;cAAE,GAAGD,gBAAgB;cAAEG,IAAI,EAAER,CAAC,CAAC2I,MAAM,CAACG;YAAsC,CAAC,CAAC;UACpG,CAAC;UAED,oBACE5L,OAAA;YAAKiN,SAAS,EAAC,WAAW;YAAAD,QAAA,gBAExBhN,OAAA,CAACX,IAAI;cAAA2N,QAAA,gBACHhN,OAAA;gBAAIiN,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnErN,OAAA;gBAAKiN,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACxBhN,OAAA;kBAAKiN,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChChN,OAAA;oBACEuG,EAAE,EAAC,aAAa;oBAChBxF,IAAI,EAAC,gBAAgB;oBACrBoN,IAAI,EAAC,OAAO;oBACZvC,KAAK,EAAC,KAAK;oBACXiE,OAAO,EAAE5L,kBAAkB,KAAK,KAAM;oBACtCgK,QAAQ,EAAEoB,0BAA2B;oBACrCpC,SAAS,EAAC;kBAA+D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACFrN,OAAA;oBAAO8P,OAAO,EAAC,aAAa;oBAAC7C,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAEzG;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNrN,OAAA;kBAAKiN,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,gBAChChN,OAAA;oBACEuG,EAAE,EAAC,eAAe;oBAClBxF,IAAI,EAAC,gBAAgB;oBACrBoN,IAAI,EAAC,OAAO;oBACZvC,KAAK,EAAC,OAAO;oBACbiE,OAAO,EAAE5L,kBAAkB,KAAK,OAAQ;oBACxCgK,QAAQ,EAAEoB,0BAA2B;oBACrCpC,SAAS,EAAC;kBAA+D;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACFrN,OAAA;oBAAO8P,OAAO,EAAC,eAAe;oBAAC7C,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,EAAC;kBAE3G;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EACLpJ,kBAAkB,KAAK,OAAO,iBAC7BjE,OAAA;kBAAKiN,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxBhN,OAAA,CAACT,KAAK;oBACJgH,EAAE,EAAC,mBAAmB;oBACtBxF,IAAI,EAAC,mBAAmB;oBACxBoN,IAAI,EAAC,gBAAgB;oBACrBvC,KAAK,EAAEzH,iBAAkB;oBACzB8J,QAAQ,EAAEqB,wBAAyB;oBACnCtB,KAAK,EAAC,uBAAuB;oBAC7BE,QAAQ;oBACRjB,SAAS,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACDrN,OAAA;oBAAGiN,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAGN7C,gBAAgB,GAAG,CAAC,iBACnBxK,OAAA,CAACX,IAAI;cAAA2N,QAAA,gBACFhN,OAAA;gBAAIiN,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrErN,OAAA;gBAAGiN,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,EAAC;cAErD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJrN,OAAA;gBAAKiN,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3ChN,OAAA;kBAAO8P,OAAO,EAAC,cAAc;kBAAC7C,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,EAAC;gBAErG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrN,OAAA;kBACEuG,EAAE,EAAC,cAAc;kBACjBxF,IAAI,EAAC,cAAc;kBACnB6K,KAAK,EAAEzI,gBAAgB,CAACG,IAAK;kBAC7B2K,QAAQ,EAAE2B,gBAAiB;kBAC3B3C,SAAS,EAAC,0MAA0M;kBAAAD,QAAA,gBAEpNhN,OAAA;oBAAQ4L,KAAK,EAAC,SAAS;oBAAAoB,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCrN,OAAA;oBAAQ4L,KAAK,EAAC,OAAO;oBAAAoB,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCrN,OAAA;oBAAQ4L,KAAK,EAAC,MAAM;oBAAAoB,QAAA,EAAC;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAELrK,KAAK,CAACC,IAAI,CAAC;gBAAEC,MAAM,EAAEsH,gBAAgB,GAAG;cAAE,CAAC,CAAC,CAAC9H,GAAG,CAAC,CAAC6L,CAAC,EAAEzI,KAAK;gBAAA,IAAAiK,qBAAA;gBAAA,oBACzD/P,OAAA;kBAAiBiN,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,gBACvDhN,OAAA;oBAAO8P,OAAO,EAAE,YAAYhK,KAAK,EAAG;oBAACmH,SAAS,EAAC,iEAAiE;oBAAAD,QAAA,GAAC,6BACpF,EAAClH,KAAK,GAAG,CAAC,EAAC,GACxC;kBAAA;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRrN,OAAA,CAACT,KAAK;oBACJgH,EAAE,EAAE,YAAYT,KAAK,EAAG;oBACxB/E,IAAI,EAAE,YAAY+E,KAAK,EAAG;oBAC1BqI,IAAI,EAAC,QAAQ;oBACbvC,KAAK,EAAE,EAAAmE,qBAAA,GAAA5M,gBAAgB,CAACE,SAAS,CAACyC,KAAK,CAAC,cAAAiK,qBAAA,uBAAjCA,qBAAA,CAAmCC,QAAQ,CAAC,CAAC,KAAI,IAAK;oBAC7D/B,QAAQ,EAAGnL,CAAC,IAAKyM,oBAAoB,CAACzJ,KAAK,EAAEhD,CAAC,CAAC2I,MAAM,CAACG,KAAK,CAAE;oBAC7DqB,SAAS,EAAC,WAAW,CAAC;oBAAA;oBACtBiB,QAAQ;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFrN,OAAA;oBAAMiN,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,EAAE7J,gBAAgB,CAACG;kBAAI;oBAAA4J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAbpFvH,KAAK;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcV,CAAC;cAAA,CACP,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,EAGA7C,gBAAgB,IAAI,CAAC,iBAClBxK,OAAA;cAAGiN,SAAS,EAAC,+CAA+C;cAAAD,QAAA,EAAC;YAE7D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAEV;MACA,KAAK,CAAC;QACJ;QACA,oBACErN,OAAA;UAAKiN,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACxBhN,OAAA;YAAIiN,SAAS,EAAC,4BAA4B;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAG9DrN,OAAA,CAACX,IAAI;YAAA2N,QAAA,gBACHhN,OAAA;cAAIiN,SAAS,EAAC,4CAA4C;cAAAD,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5ErN,OAAA;cAAKiN,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAChDhN,OAAA;gBAAKiN,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,eACrChN,OAAA,CAACT,KAAK;kBACJgH,EAAE,EAAC,iBAAiB;kBACpBxF,IAAI,EAAC,iBAAiB;kBACtBiN,KAAK,EAAC,eAAe;kBACrBG,IAAI,EAAC,OAAO;kBACZvC,KAAK,EAAEhH,eAAe,CAACE,KAAM;kBAC7BmJ,QAAQ,EAAGnL,CAAC,IAAK+B,kBAAkB,CAAC;oBAAE,GAAGD,eAAe;oBAAEE,KAAK,EAAEhC,CAAC,CAAC2I,MAAM,CAACG;kBAAM,CAAC,CAAE;kBACnFqE,WAAW,EAAC;gBAAqB;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENrN,OAAA,CAACZ,MAAM;gBAACmO,OAAO,EAAEnC,kBAAmB;gBAACuC,OAAO,EAAC,WAAW;gBAACV,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGPrN,OAAA,CAACX,IAAI;YAAA2N,QAAA,gBACHhN,OAAA;cAAIiN,SAAS,EAAC,4CAA4C;cAAAD,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAChFlI,eAAe,gBACdnF,OAAA;cAAAgN,QAAA,EAAG;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,GACxBtI,QAAQ,CAAC7B,MAAM,GAAG,CAAC,gBACrBlD,OAAA;cAAKiN,SAAS,EAAC,mCAAmC;cAAAD,QAAA,gBAC/ChN,OAAA;gBAAKiN,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACpChN,OAAA;kBAAO8P,OAAO,EAAC,eAAe;kBAAC7C,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,EAAC;gBAE9F;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRrN,OAAA;kBACEuG,EAAE,EAAC,eAAe;kBAClB2J,QAAQ;kBACRtE,KAAK,EAAE3G,kBAAmB;kBAC1BgJ,QAAQ,EAAEzC,sBAAuB;kBACjCyB,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EAEjEjI,QAAQ,CAACrC,GAAG,CAACyN,OAAO,iBACnBnQ,OAAA;oBAAyB4L,KAAK,EAAEuE,OAAO,CAAC5J,EAAG;oBAAAyG,QAAA,EACxCmD,OAAO,CAACpP,IAAI,GAAG,GAAGoP,OAAO,CAACpP,IAAI,KAAKoP,OAAO,CAACrL,KAAK,GAAG,GAAGqL,OAAO,CAACrL;kBAAK,GADzDqL,OAAO,CAAC5J,EAAE;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACRrN,OAAA,CAACZ,MAAM;gBAACmO,OAAO,EAAEjC,mBAAoB;gBAACqC,OAAO,EAAC,WAAW;gBAACV,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7G,CAAC,gBAENrN,OAAA;cAAGiN,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAAC;YAAkE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACzG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAGPrN,OAAA,CAACX,IAAI;YAAA2N,QAAA,gBACHhN,OAAA;cAAIiN,SAAS,EAAC,4CAA4C;cAAAD,QAAA,GAAC,oBAAkB,EAACtI,UAAU,CAACxB,MAAM,EAAC,GAAC;YAAA;cAAAgK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrG3I,UAAU,CAACxB,MAAM,GAAG,CAAC,gBACpBlD,OAAA;cAAIiN,SAAS,EAAC,iDAAiD;cAAAD,QAAA,EAC5DtI,UAAU,CAAChC,GAAG,CAAC,CAAC0N,GAAG,EAAEtK,KAAK,kBACzB9F,OAAA;gBAAgBiN,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,gBAChEhN,OAAA;kBAAAgN,QAAA,EAAOoD,GAAG,CAACrP,IAAI,GAAG,GAAGqP,GAAG,CAACrP,IAAI,KAAKqP,GAAG,CAACtL,KAAK,GAAG,GAAGsL,GAAG,CAACtL;gBAAK;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClErN,OAAA;kBAAQuN,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACuE,GAAG,CAACtL,KAAK,CAAE;kBAACmI,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GAFvHvH,KAAK;gBAAAoH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAELrN,OAAA;cAAGiN,SAAS,EAAC,qBAAqB;cAAAD,QAAA,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAC/D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAEV,KAAK,CAAC;QAAE;UACL;UACA,MAAMgD,mBAAmB,GAAGpM,kBAAkB,KAAK,OAAO,IAAIE,iBAAiB,GAC3E,IAAIG,IAAI,CAACH,iBAAiB,CAAC,CAAC0F,cAAc,CAAC,CAAC,GAC5C,aAAa;UACjB,oBACE7J,OAAA;YAAKiN,SAAS,EAAC,WAAW;YAAAD,QAAA,gBACxBhN,OAAA;cAAIiN,SAAS,EAAC,4BAA4B;cAAAD,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEjErN,OAAA,CAACX,IAAI;cAAA2N,QAAA,gBACHhN,OAAA;gBAAIiN,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChFrN,OAAA;gBAAAgN,QAAA,gBAAGhN,OAAA;kBAAMiN,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC5M,YAAY;cAAA;gBAAAyM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClErN,OAAA;gBAAAgN,QAAA,gBAAGhN,OAAA;kBAAMiN,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAAC1M,OAAO;cAAA;gBAAAuM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChErN,OAAA;gBAAAgN,QAAA,gBAAGhN,OAAA;kBAAMiN,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACxM,QAAQ,EAAC,IAAK,EAACG,SAAS,EAAC,GAAI;cAAA;gBAAAkM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACjFjM,OAAO,iBAAIpB,OAAA;gBAAAgN,QAAA,gBAAGhN,OAAA;kBAAMiN,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,KAAC,EAACjM,OAAO;cAAA;gBAAA8L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eAEPrN,OAAA,CAACX,IAAI;cAAA2N,QAAA,gBACHhN,OAAA;gBAAIiN,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAExFnL,aAAa,CAACsF,MAAM,CAAC,CAAC1E,CAAC,EAAE0L,GAAG,KAAKlJ,kBAAkB,CAAC6B,QAAQ,CAACqH,GAAG,CAAC,IAAI1L,CAAC,CAACF,IAAI,CAACmD,IAAI,CAAC,CAAC,CAAC,CAAC7C,MAAM,GAAG,CAAC,gBAC7FlD,OAAA;gBAAIiN,SAAS,EAAC,6BAA6B;gBAAAD,QAAA,EACxC9K,aAAa,CACXQ,GAAG,CAAC,CAACoC,KAAK,EAAEgB,KAAK,KAAK;kBACrB;kBACA,IAAIR,kBAAkB,CAAC6B,QAAQ,CAACrB,KAAK,CAAC,IAAIhB,KAAK,CAAClC,IAAI,CAACmD,IAAI,CAAC,CAAC,EAAE;oBAC3D;oBACA,MAAMuK,YAAY,GAAGpO,aAAa,CAC/BQ,GAAG,CAAC,CAACI,CAAC,EAAE2E,CAAC,MAAM;sBAAE3C,KAAK,EAAEhC,CAAC;sBAAEyN,aAAa,EAAE9I;oBAAE,CAAC,CAAC,CAAC,CAAC;oBAAA,CAChDD,MAAM,CAAC,CAAC;sBAAE1C,KAAK;sBAAEyL;oBAAc,CAAC,KAC/BjL,kBAAkB,CAAC6B,QAAQ,CAACoJ,aAAa,CAAC,IAAIzL,KAAK,CAAClC,IAAI,CAACmD,IAAI,CAAC,CAChE,CAAC;oBACH,MAAMyK,WAAW,GAAGF,YAAY,CAACG,SAAS,CAAC9N,IAAI,IAAIA,IAAI,CAAC4N,aAAa,KAAKzK,KAAK,CAAC;oBAEhF,MAAM4K,2BAA2B,GAAGF,WAAW,GAAG,CAAC,CAAC,CAAC;;oBAErD,IAAIG,UAAU,GAAG,6BAA6B,CAAC,CAAC;oBAChD;oBACA,IAAID,2BAA2B,GAAG,CAAC,IAAIvN,gBAAgB,CAACE,SAAS,CAACqN,2BAA2B,GAAG,CAAC,CAAC,KAAKE,SAAS,EAAE;sBAChHD,UAAU,GAAG,QAAQxN,gBAAgB,CAACE,SAAS,CAACqN,2BAA2B,GAAG,CAAC,CAAC,IAAIvN,gBAAgB,CAACG,IAAI,iBAAiBgN,YAAY,CAACE,WAAW,GAAG,CAAC,CAAC,CAACD,aAAa,GAAG,CAAC,EAAE;oBAC7K;oBAEA,oBACEvQ,OAAA;sBAAAgN,QAAA,GAAgB,SACP,EAAClH,KAAK,GAAG,CAAC,EAAC,KAAG,EAAC6K,UAAU;oBAAA,GADzB7K,KAAK;sBAAAoH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEV,CAAC;kBAET;kBACA,OAAO,IAAI,CAAC,CAAC;gBACf,CAAC,CAAC,CACD7F,MAAM,CAACqJ,OAAO,CAAC,CAAC;cAAA;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CAAC,gBAELrN,OAAA;gBAAGiN,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAAC;cAAqD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAC5F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAEPrN,OAAA,CAACX,IAAI;cAAA2N,QAAA,gBACHhN,OAAA;gBAAIiN,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1ErN,OAAA;gBAAAgN,QAAA,GAAItI,UAAU,CAACxB,MAAM,EAAC,eAAa;cAAA;gBAAAgK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,EACtC3I,UAAU,CAACxB,MAAM,GAAG,CAAC,iBACpBlD,OAAA;gBAAIiN,SAAS,EAAC,iDAAiD;gBAAAD,QAAA,GAC5DtI,UAAU,CAACD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/B,GAAG,CAAC,CAACkF,CAAC,EAAEH,CAAC,kBAAKzH,OAAA;kBAAAgN,QAAA,EAAapF,CAAC,CAAC7G,IAAI,GAAG,GAAG6G,CAAC,CAAC7G,IAAI,KAAK6G,CAAC,CAAC9C,KAAK,GAAG,GAAG8C,CAAC,CAAC9C;gBAAK,GAA/C2C,CAAC;kBAAAyF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmD,CAAC,CAAC,EACpG3I,UAAU,CAACxB,MAAM,GAAG,CAAC,iBAAIlD,OAAA;kBAAAgN,QAAA,GAAI,SAAO,EAACtI,UAAU,CAACxB,MAAM,GAAG,CAAC,EAAC,OAAK;gBAAA;kBAAAgK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CACL;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAEPrN,OAAA,CAACX,IAAI;cAAA2N,QAAA,gBACHhN,OAAA;gBAAIiN,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxErN,OAAA;gBAAAgN,QAAA,GAAG,iBAAe,EAACqD,mBAAmB;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eAEPrN,OAAA;cAAKiN,SAAS,EAAC,uBAAuB;cAAAD,QAAA,eAEpChN,OAAA,CAACZ,MAAM;gBACLmO,OAAO,EAAE1E,oBAAqB;gBAC9B+E,QAAQ,EAAElM,OAAQ;gBAClBuL,SAAS,EAAC,SAAS,CAAC;gBAAA;gBAAAD,QAAA,EAEnBtL,OAAO,GAAG,aAAa,GAAG;cAA4B;gBAAAwL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,CAAC;UAAA,CACP,CAAC;QACL;MAAE;MACF;QAAS,OAAO,IAAI;IACtB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;;EAEH;EACA,oBACErN,OAAA,CAAAE,SAAA;IAAA8M,QAAA,gBACEhN,OAAA;MAAIiN,SAAS,EAAC,2CAA2C;MAAAD,QAAA,EAAC;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACjF/L,KAAK,iBAAItB,OAAA;MAAKiN,SAAS,EAAC,gEAAgE;MAAAD,QAAA,eAAChN,OAAA;QAAAgN,QAAA,EAAI1L;MAAK;QAAA4L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EAC7G7L,OAAO,iBAAIxB,OAAA;MAAKiN,SAAS,EAAC,sEAAsE;MAAAD,QAAA,eAAChN,OAAA;QAAAgN,QAAA,EAAIxL;MAAO;QAAA0L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EACvHzL,eAAe,iBAAI5B,OAAA;MAAKiN,SAAS,EAAC,0BAA0B;MAAAD,QAAA,EAAC;IAAmB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAGvFrN,OAAA;MAAKiN,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBhN,OAAA;QAAIiN,SAAS,EAAC,8GAA8G;QAAAD,QAAA,EACzH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACtK,GAAG,CAACoO,CAAC,iBACpB9Q,OAAA;UAAYiN,SAAS,EAAE,+BAA+B6D,CAAC,KAAKhP,IAAI,GAAG,sCAAsC,GAAG,EAAE,IAAIgP,CAAC,GAAG,CAAC,GAAG,0LAA0L,GAAG,EAAE,EAAG;UAAA9D,QAAA,eAC1ThN,OAAA;YAAMiN,SAAS,EAAE,6FAA6F6D,CAAC,GAAGhP,IAAI,GAAG,sCAAsC,GAAG,EAAE,EAAG;YAAAkL,QAAA,GACpK8D,CAAC,GAAGhP,IAAI,iBACP9B,OAAA;cAAKiN,SAAS,EAAC,kCAAkC;cAAC,eAAY,MAAM;cAAC8D,KAAK,EAAC,4BAA4B;cAACpG,IAAI,EAAC,cAAc;cAACqG,OAAO,EAAC,WAAW;cAAAhE,QAAA,eAC7IhN,OAAA;gBAAMyJ,CAAC,EAAC;cAA8J;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrK,CACN,EACAyD,CAAC,KAAKhP,IAAI,iBAAI9B,OAAA;cAAMiN,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAE8D;YAAC;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC/CyD,CAAC,GAAGhP,IAAI,iBAAI9B,OAAA;cAAMiN,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAE8D;YAAC;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7C,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAACyD,CAAC,GAAC,CAAC,CAAC;UAAA;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D;QAAC,GAVAyD,CAAC;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWN,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNrN,OAAA,CAACX,IAAI;MAAC4N,SAAS,EAAC,MAAM;MAAAD,QAAA,EACnBa,iBAAiB,CAAC;IAAC;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGPrN,OAAA;MAAKiN,SAAS,EAAC,2BAA2B;MAAAD,QAAA,gBACxChN,OAAA,CAACZ,MAAM;QAACmO,OAAO,EAAEb,cAAe;QAACkB,QAAQ,EAAE9L,IAAI,KAAK,CAAC,IAAIJ,OAAQ;QAACiM,OAAO,EAAC,WAAW;QAAAX,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACtGvL,IAAI,GAAG,CAAC,gBACP9B,OAAA,CAACZ,MAAM;QAACmO,OAAO,EAAEzB,UAAW;QAAC8B,QAAQ,EAAElM,OAAQ;QAACiM,OAAO,EAAC,SAAS;QAAAX,QAAA,EAAC;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,gBAE/ErN,OAAA,CAACZ,MAAM;QAACmO,OAAO,EAAE1E,oBAAqB;QAAC+E,QAAQ,EAAElM,OAAQ;QAACuL,SAAS,EAAC,SAAS;QAAAD,QAAA,GAAC,GAAC,EAC1EtL,OAAO,GAAG,aAAa,GAAG,4BAA4B;MAAA;QAAAwL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELT,oBAAoB,CAAC,CAAC;EAAA,eACvB,CAAC;AAEP,CAAC,CAAC,CAAC;AAAAxM,EAAA,CA9hCED,cAAwB;EAAA,QACXV,OAAO,EACPC,WAAW,EACXC,WAAW;AAAA;AAAAsR,EAAA,GAHxB9Q,cAAwB;AAgiC7B,eAAeA,cAAc,CAAC,CAAC;AAAA,IAAA8Q,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}