{"ast": null, "code": "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ /**\n    * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n    */function $9446cca9a3875146$export$7d15b64cf5a3a4c4(value) {\n  let min = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : -Infinity;\n  let max = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : Infinity;\n  let newValue = Math.min(Math.max(value, min), max);\n  return newValue;\n}\nfunction $9446cca9a3875146$export$e1a7b8e69ef6c52f(value, step) {\n  let roundedValue = value;\n  let stepString = step.toString();\n  let pointIndex = stepString.indexOf('.');\n  let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0;\n  if (precision > 0) {\n    let pow = Math.pow(10, precision);\n    roundedValue = Math.round(roundedValue * pow) / pow;\n  }\n  return roundedValue;\n}\nfunction $9446cca9a3875146$export$cb6e0bb50bc19463(value, min, max, step) {\n  min = Number(min);\n  max = Number(max);\n  let remainder = (value - (isNaN(min) ? 0 : min)) % step;\n  let snappedValue = $9446cca9a3875146$export$e1a7b8e69ef6c52f(Math.abs(remainder) * 2 >= step ? value + Math.sign(remainder) * (step - Math.abs(remainder)) : value - remainder, step);\n  if (!isNaN(min)) {\n    if (snappedValue < min) snappedValue = min;else if (!isNaN(max) && snappedValue > max) snappedValue = min + Math.floor($9446cca9a3875146$export$e1a7b8e69ef6c52f((max - min) / step, step)) * step;\n  } else if (!isNaN(max) && snappedValue > max) snappedValue = Math.floor($9446cca9a3875146$export$e1a7b8e69ef6c52f(max / step, step)) * step;\n  // correct floating point behavior by rounding to step precision\n  snappedValue = $9446cca9a3875146$export$e1a7b8e69ef6c52f(snappedValue, step);\n  return snappedValue;\n}\nfunction $9446cca9a3875146$export$b6268554fba451f(value, digits) {\n  let base = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 10;\n  const pow = Math.pow(base, digits);\n  return Math.round(value * pow) / pow;\n}\nexport { $9446cca9a3875146$export$7d15b64cf5a3a4c4 as clamp, $9446cca9a3875146$export$e1a7b8e69ef6c52f as roundToStepPrecision, $9446cca9a3875146$export$cb6e0bb50bc19463 as snapValueToStep, $9446cca9a3875146$export$b6268554fba451f as toFixedNumber };", "map": {"version": 3, "names": ["$9446cca9a3875146$export$7d15b64cf5a3a4c4", "value", "min", "arguments", "length", "undefined", "Infinity", "max", "newValue", "Math", "$9446cca9a3875146$export$e1a7b8e69ef6c52f", "step", "roundedValue", "stepString", "toString", "pointIndex", "indexOf", "precision", "pow", "round", "$9446cca9a3875146$export$cb6e0bb50bc19463", "Number", "remainder", "isNaN", "snappedValue", "abs", "sign", "floor", "$9446cca9a3875146$export$b6268554fba451f", "digits", "base"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\@react-stately\\utils\\dist\\packages\\@react-stately\\utils\\src\\number.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n/**\n * Takes a value and forces it to the closest min/max if it's outside. Also forces it to the closest valid step.\n */\nexport function clamp(value: number, min: number = -Infinity, max: number = Infinity): number {\n  let newValue = Math.min(Math.max(value, min), max);\n  return newValue;\n}\n\nexport function roundToStepPrecision(value: number, step: number): number {\n  let roundedValue = value;\n  let stepString = step.toString();\n  let pointIndex = stepString.indexOf('.');\n  let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0;\n  if (precision > 0) {\n    let pow = Math.pow(10, precision);\n    roundedValue = Math.round(roundedValue * pow) / pow;\n  }\n  return roundedValue;\n}\n\nexport function snapValueToStep(value: number, min: number | undefined, max: number | undefined, step: number): number {\n  min = Number(min);\n  max = Number(max);\n  let remainder = ((value - (isNaN(min) ? 0 : min)) % step);\n  let snappedValue = roundToStepPrecision(Math.abs(remainder) * 2 >= step\n    ? value + Math.sign(remainder) * (step - Math.abs(remainder))\n    : value - remainder, step);\n\n  if (!isNaN(min)) {\n    if (snappedValue < min) {\n      snappedValue = min;\n    } else if (!isNaN(max) && snappedValue > max) {\n      snappedValue = min + Math.floor(roundToStepPrecision((max - min) / step, step)) * step;\n    }\n  } else if (!isNaN(max) && snappedValue > max) {\n    snappedValue = Math.floor(roundToStepPrecision(max / step, step)) * step;\n  }\n\n  // correct floating point behavior by rounding to step precision\n  snappedValue = roundToStepPrecision(snappedValue, step);\n\n  return snappedValue;\n}\n\n/* Takes a value and rounds off to the number of digits. */\nexport function toFixedNumber(value: number, digits: number, base: number = 10): number {\n  const pow = Math.pow(base, digits);\n\n  return Math.round(value * pow) / pow;\n}\n"], "mappings": "AAAA;;;;;;;;;;GAAA,CAYA;;MAGO,SAASA,0CAAMC,KAAa,EAAiD;EAAA,IAA/CC,GAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAc,CAACG,QAAQ;EAAA,IAAEC,GAAA,GAAAJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAcG,QAAQ;EAClF,IAAIE,QAAA,GAAWC,IAAA,CAAKP,GAAG,CAACO,IAAA,CAAKF,GAAG,CAACN,KAAA,EAAOC,GAAA,GAAMK,GAAA;EAC9C,OAAOC,QAAA;AACT;AAEO,SAASE,0CAAqBT,KAAa,EAAEU,IAAY;EAC9D,IAAIC,YAAA,GAAeX,KAAA;EACnB,IAAIY,UAAA,GAAaF,IAAA,CAAKG,QAAQ;EAC9B,IAAIC,UAAA,GAAaF,UAAA,CAAWG,OAAO,CAAC;EACpC,IAAIC,SAAA,GAAYF,UAAA,IAAc,IAAIF,UAAA,CAAWT,MAAM,GAAGW,UAAA,GAAa;EACnE,IAAIE,SAAA,GAAY,GAAG;IACjB,IAAIC,GAAA,GAAMT,IAAA,CAAKS,GAAG,CAAC,IAAID,SAAA;IACvBL,YAAA,GAAeH,IAAA,CAAKU,KAAK,CAACP,YAAA,GAAeM,GAAA,IAAOA,GAAA;EAClD;EACA,OAAON,YAAA;AACT;AAEO,SAASQ,0CAAgBnB,KAAa,EAAEC,GAAuB,EAAEK,GAAuB,EAAEI,IAAY;EAC3GT,GAAA,GAAMmB,MAAA,CAAOnB,GAAA;EACbK,GAAA,GAAMc,MAAA,CAAOd,GAAA;EACb,IAAIe,SAAA,GAAa,CAACrB,KAAA,IAASsB,KAAA,CAAMrB,GAAA,IAAO,IAAIA,GAAE,CAAC,IAAKS,IAAA;EACpD,IAAIa,YAAA,GAAed,yCAAA,CAAqBD,IAAA,CAAKgB,GAAG,CAACH,SAAA,IAAa,KAAKX,IAAA,GAC/DV,KAAA,GAAQQ,IAAA,CAAKiB,IAAI,CAACJ,SAAA,KAAcX,IAAA,GAAOF,IAAA,CAAKgB,GAAG,CAACH,SAAA,CAAS,IACzDrB,KAAA,GAAQqB,SAAA,EAAWX,IAAA;EAEvB,IAAI,CAACY,KAAA,CAAMrB,GAAA,GAAM;IACf,IAAIsB,YAAA,GAAetB,GAAA,EACjBsB,YAAA,GAAetB,GAAA,MACV,IAAI,CAACqB,KAAA,CAAMhB,GAAA,KAAQiB,YAAA,GAAejB,GAAA,EACvCiB,YAAA,GAAetB,GAAA,GAAMO,IAAA,CAAKkB,KAAK,CAACjB,yCAAA,CAAqB,CAACH,GAAA,GAAML,GAAE,IAAKS,IAAA,EAAMA,IAAA,KAASA,IAAA;EAEtF,OAAO,IAAI,CAACY,KAAA,CAAMhB,GAAA,KAAQiB,YAAA,GAAejB,GAAA,EACvCiB,YAAA,GAAef,IAAA,CAAKkB,KAAK,CAACjB,yCAAA,CAAqBH,GAAA,GAAMI,IAAA,EAAMA,IAAA,KAASA,IAAA;EAGtE;EACAa,YAAA,GAAed,yCAAA,CAAqBc,YAAA,EAAcb,IAAA;EAElD,OAAOa,YAAA;AACT;AAGO,SAASI,yCAAc3B,KAAa,EAAE4B,MAAc,EAAmB;EAAA,IAAjBC,IAAA,GAAA3B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAe,EAAE;EAC5E,MAAMe,GAAA,GAAMT,IAAA,CAAKS,GAAG,CAACY,IAAA,EAAMD,MAAA;EAE3B,OAAOpB,IAAA,CAAKU,KAAK,CAAClB,KAAA,GAAQiB,GAAA,IAAOA,GAAA;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}