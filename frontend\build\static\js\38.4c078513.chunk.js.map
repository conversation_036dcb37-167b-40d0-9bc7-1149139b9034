{"version": 3, "file": "static/js/38.4c078513.chunk.js", "mappings": "mNAqBA,MA2NA,EA3NmCA,KACjC,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,OAEVC,EAASC,KADCC,EAAAA,EAAAA,OACaC,EAAAA,EAAAA,WAAS,KAChCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,KAC5BG,EAASC,IAAcJ,EAAAA,EAAAA,UAAS,KAChCK,EAAUC,IAAeN,EAAAA,EAAAA,UAAc,OAGvCO,EAAkBC,IAAuBR,EAAAA,EAAAA,UAA2B,CACzES,UAAW,CAAC,IACZC,KAAM,WAEDC,EAAYC,IAAiBZ,EAAAA,EAAAA,UAAS,IAE7Ca,EAAAA,EAAAA,YAAU,KA4BJpB,GA3BkBqB,WACpB,IACEhB,GAAW,GACX,MAAMiB,QAAiBC,EAAAA,EAAYC,YAAYxB,GAI/C,GAHAa,EAAYS,GAGRA,EAASG,eAAiBC,MAAMC,QAAQL,EAASG,eAAgB,CACnE,MAAMG,EAAQN,EAASG,cAAcI,QAAQC,GAAeA,EAAMC,OAAMC,OACxEb,EAAcS,GAGVA,EAAQ,GACVb,GAAoBkB,IAAI,IACnBA,EACHjB,UAAWU,MAAME,EAAQ,GAAGM,KAAK,OAGvC,CAEA7B,GAAW,EACb,CAAE,MAAO8B,GAAW,IAADC,EAAAC,EACjB5B,GAAqB,QAAZ2B,EAAAD,EAAIb,gBAAQ,IAAAc,GAAM,QAANC,EAAZD,EAAcE,YAAI,IAAAD,OAAN,EAAZA,EAAoBE,UAAW,oCACxClC,GAAW,EACb,GAIAmC,EACF,GACC,CAACxC,IAEJ,MAaMyC,EAAoBxB,IACxBF,GAAoBkB,IAAI,IACnBA,EACHhB,UACC,EA+BL,OACEyB,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAAAC,SAAA,CACFpC,IACCqC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CACJC,KAAK,QACLR,QAAS/B,EACTwC,QAASA,IAAMvC,EAAS,IACxBwC,UAAU,SAIbvC,IACCmC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CACJC,KAAK,UACLR,QAAS7B,EACTuC,UAAU,UAIdP,EAAAA,EAAAA,MAAA,OAAKO,UAAU,OAAML,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAII,UAAU,6BAA4BL,SAAC,sBAE1CxC,GACCyC,EAAAA,EAAAA,KAAA,KAAGI,UAAU,gBAAeL,SAAC,gCAC3B1B,GAAc,GAChBwB,EAAAA,EAAAA,MAAA,OAAKO,UAAU,wDAAuDL,SAAA,EACpEC,EAAAA,EAAAA,KAAA,KAAGI,UAAU,gBAAeL,SAAC,gEAG7BC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLD,UAAU,OACVE,QAASA,IAAMjD,EAAS,cAAcF,aAAc4C,SACrD,4BAKHF,EAAAA,EAAAA,MAAAU,EAAAA,SAAA,CAAAR,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKI,UAAU,wDAAuDL,UACpEC,EAAAA,EAAAA,KAAA,KAAGI,UAAU,qBAAoBL,SAAC,sHAKpCC,EAAAA,EAAAA,KAAA,OAAKI,UAAU,OAAML,UACnBF,EAAAA,EAAAA,MAAA,OAAKO,UAAU,yBAAwBL,SAAA,EACrCC,EAAAA,EAAAA,KAAA,QAAMI,UAAU,qBAAoBL,SAAC,gBACrCF,EAAAA,EAAAA,MAAA,OAAKO,UAAU,iBAAgBL,SAAA,EAC7BC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLG,KAAK,KACLC,QAAmC,UAA1BxC,EAAiBG,KAAmB,UAAY,YACzDkC,QAASA,IAAMV,EAAiB,SAASG,SAC1C,WAGDC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLG,KAAK,KACLC,QAAmC,SAA1BxC,EAAiBG,KAAkB,UAAY,YACxDkC,QAASA,IAAMV,EAAiB,QAAQG,SACzC,kBAOPC,EAAAA,EAAAA,KAAA,OAAKI,UAAU,YAAWL,SACvB9B,EAAiBE,UAAUuC,KAAI,CAACC,EAAUC,KACzCf,EAAAA,EAAAA,MAAA,OAAiBO,UAAU,oBAAmBL,SAAA,EAC5CC,EAAAA,EAAAA,KAAA,OAAKI,UAAU,qBAAoBL,UACjCC,EAAAA,EAAAA,KAAA,OAAKI,UAAU,+FAA8FL,SAC1Ga,EAAQ,OAGbZ,EAAAA,EAAAA,KAAA,OAAKI,UAAU,wDACfJ,EAAAA,EAAAA,KAAA,OAAKI,UAAU,qBAAoBL,UACjCC,EAAAA,EAAAA,KAAA,OAAKI,UAAU,+FAA8FL,SAC1Ga,EAAQ,OAGbZ,EAAAA,EAAAA,KAAA,OAAKI,UAAU,YAAWL,UACxBC,EAAAA,EAAAA,KAACa,EAAAA,EAAK,CACJ1D,GAAI,YAAYyD,IAChBE,KAAM,YAAYF,IAClBV,KAAK,SACLa,MAAOJ,EAASK,WAChBC,SAAWC,GAtIFC,EAACP,EAAeG,KAC3C,MAAMK,EAAWC,SAASN,IAAU,EAE9BO,EAAaC,KAAKC,IAAI,EAAGJ,GACzBK,EAAmB,IAAIxD,EAAiBE,WAC9CsD,EAAiBb,GAASU,EAE1BpD,GAAoBkB,IAAI,IACnBA,EACHjB,UAAWsD,KACV,EA6HiBN,CAAqBP,EAAOM,EAAEQ,OAAOX,OAEvCY,MAAM,GACNvB,UAAU,YAGdJ,EAAAA,EAAAA,KAAA,OAAKI,UAAU,qBAAoBL,SAChC9B,EAAiBG,SA1BZwC,QAgCdf,EAAAA,EAAAA,MAAA,OAAKO,UAAU,4BAA2BL,SAAA,EACxCC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLI,QAAQ,YACRH,QAASA,IAAMjD,GAAU,GACzBuE,SAAUrE,EAAQwC,SACnB,UAGDC,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,QAzIa9B,UACzB,IACEhB,GAAW,GACXI,EAAS,IAGT,MAAMiE,EAAe,CACnBC,eAAgB7D,EAAiBE,UAAUuC,KAAIC,IAAQ,CACrDoB,MAAOpB,EACPvC,KAAMH,EAAiBG,gBAIrBM,EAAAA,EAAYsD,eAAe7E,EAAI,CAAE8E,SAAUJ,IAEjD/D,EAAW,yCACXN,GAAW,GAGX0E,YAAW,KACT7E,EAAS,cAAcF,YAAa,GACnC,KACL,CAAE,MAAOmC,GAAW,IAAD6C,EAAAC,EACjBxE,GAAqB,QAAZuE,EAAA7C,EAAIb,gBAAQ,IAAA0D,GAAM,QAANC,EAAZD,EAAc1C,YAAI,IAAA2C,OAAN,EAAZA,EAAoB1C,UAAW,2BACxClC,GAAW,EACb,GAiHYoE,SAAUrE,EAAQwC,SAEjBxC,EAAU,YAAc,+BAM9B,C", "sources": ["pages/campaigns/CampaignSchedule.tsx"], "sourcesContent": ["import React, {\r\n  useEffect,\r\n  useState,\r\n} from 'react';\r\n\r\nimport Alert from 'components/Alert';\r\nimport Button from 'components/Button';\r\nimport Card from 'components/Card';\r\nimport Input from 'components/Input';\r\nimport {\r\n  useLocation,\r\n  useNavigate,\r\n  useParams,\r\n} from 'react-router-dom';\r\nimport { campaignAPI } from 'services/api';\r\n\r\ninterface ScheduleSettings {\r\n  intervals: number[];\r\n  unit: 'hours' | 'days';\r\n}\r\n\r\nconst CampaignSchedule: React.FC = () => {\r\n  const { id } = useParams<{ id: string }>();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [campaign, setCampaign] = useState<any>(null);\r\n  \r\n  // Schedule settings\r\n  const [scheduleSettings, setScheduleSettings] = useState<ScheduleSettings>({\r\n    intervals: [24], // Default to 24 hours between emails\r\n    unit: 'hours'\r\n  });\r\n  const [emailCount, setEmailCount] = useState(1);\r\n\r\n  useEffect(() => {\r\n    const fetchCampaign = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await campaignAPI.getCampaign(id);\r\n        setCampaign(response);\r\n        \r\n        // If the campaign has multiple emails, update the email count\r\n        if (response.emailContents && Array.isArray(response.emailContents)) {\r\n          const count = response.emailContents.filter((email: any) => email.html).length;\r\n          setEmailCount(count);\r\n          \r\n          // Initialize intervals array with default values\r\n          if (count > 1) {\r\n            setScheduleSettings(prev => ({\r\n              ...prev,\r\n              intervals: Array(count - 1).fill(24) // Default interval of 24 hours\r\n            }));\r\n          }\r\n        }\r\n        \r\n        setLoading(false);\r\n      } catch (err: any) {\r\n        setError(err.response?.data?.message || 'Failed to fetch campaign details');\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (id) {\r\n      fetchCampaign();\r\n    }\r\n  }, [id]);\r\n\r\n  const handleIntervalChange = (index: number, value: string) => {\r\n    const numValue = parseInt(value) || 0;\r\n    // Ensure the interval is at least 1\r\n    const validValue = Math.max(1, numValue);\r\n    const updatedIntervals = [...scheduleSettings.intervals];\r\n    updatedIntervals[index] = validValue;\r\n    \r\n    setScheduleSettings(prev => ({\r\n      ...prev,\r\n      intervals: updatedIntervals\r\n    }));\r\n  };\r\n\r\n  const handleUnitChange = (unit: 'hours' | 'days') => {\r\n    setScheduleSettings(prev => ({\r\n      ...prev,\r\n      unit\r\n    }));\r\n  };\r\n\r\n  const handleSaveSchedule = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n      \r\n      // Create schedule data\r\n      const scheduleData = {\r\n        emailIntervals: scheduleSettings.intervals.map(interval => ({\r\n          delay: interval,\r\n          unit: scheduleSettings.unit\r\n        }))\r\n      };\r\n      \r\n      await campaignAPI.updateCampaign(id, { schedule: scheduleData });\r\n      \r\n      setSuccess('Campaign schedule saved successfully!');\r\n      setLoading(false);\r\n      \r\n      // Navigate to summary page after a short delay\r\n      setTimeout(() => {\r\n        navigate(`/campaigns/${id}/summary`);\r\n      }, 1500);\r\n    } catch (err: any) {\r\n      setError(err.response?.data?.message || 'Failed to save schedule');\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      {error && (\r\n        <Alert\r\n          type=\"error\"\r\n          message={error}\r\n          onClose={() => setError('')}\r\n          className=\"mb-4\"\r\n        />\r\n      )}\r\n\r\n      {success && (\r\n        <Alert\r\n          type=\"success\"\r\n          message={success}\r\n          className=\"mb-4\"\r\n        />\r\n      )}\r\n\r\n      <div className=\"mb-6\">\r\n        <h2 className=\"text-xl font-semibold mb-4\">Campaign Schedule</h2>\r\n        \r\n        {loading ? (\r\n          <p className=\"text-gray-500\">Loading campaign details...</p>\r\n        ) : emailCount <= 1 ? (\r\n          <div className=\"bg-blue-50 border border-blue-100 rounded-md p-4 mb-6\">\r\n            <p className=\"text-blue-800\">\r\n              This campaign has only one email. No scheduling is needed.\r\n            </p>\r\n            <Button\r\n              className=\"mt-4\"\r\n              onClick={() => navigate(`/campaigns/${id}/summary`)}\r\n            >\r\n              Continue to Summary\r\n            </Button>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <div className=\"bg-gray-50 border border-gray-200 rounded-md p-4 mb-6\">\r\n              <p className=\"text-gray-700 mb-2\">\r\n                Set the delay between emails in your sequence. This determines how much time passes before each email is sent.\r\n              </p>\r\n            </div>\r\n            \r\n            <div className=\"mb-6\">\r\n              <div className=\"flex items-center mb-4\">\r\n                <span className=\"mr-4 text-gray-700\">Time unit:</span>\r\n                <div className=\"flex space-x-2\">\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant={scheduleSettings.unit === 'hours' ? 'primary' : 'secondary'}\r\n                    onClick={() => handleUnitChange('hours')}\r\n                  >\r\n                    Hours\r\n                  </Button>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant={scheduleSettings.unit === 'days' ? 'primary' : 'secondary'}\r\n                    onClick={() => handleUnitChange('days')}\r\n                  >\r\n                    Days\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"space-y-4\">\r\n              {scheduleSettings.intervals.map((interval, index) => (\r\n                <div key={index} className=\"flex items-center\">\r\n                  <div className=\"w-20 flex-shrink-0\">\r\n                    <div className=\"w-10 h-10 rounded-full bg-blue-600 text-white flex items-center justify-center font-semibold\">\r\n                      {index + 1}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex-grow border-t-2 border-dashed border-gray-300\"></div>\r\n                  <div className=\"w-20 flex-shrink-0\">\r\n                    <div className=\"w-10 h-10 rounded-full bg-blue-600 text-white flex items-center justify-center font-semibold\">\r\n                      {index + 2}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"ml-4 w-40\">\r\n                    <Input\r\n                      id={`interval-${index}`}\r\n                      name={`interval-${index}`}\r\n                      type=\"number\"\r\n                      value={interval.toString()}\r\n                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => \r\n                        handleIntervalChange(index, e.target.value)\r\n                      }\r\n                      label=\"\"\r\n                      className=\"mb-0\"\r\n                    />\r\n                  </div>\r\n                  <div className=\"ml-2 text-gray-700\">\r\n                    {scheduleSettings.unit}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n            \r\n            <div className=\"flex justify-between mt-8\">\r\n              <Button\r\n                variant=\"secondary\"\r\n                onClick={() => navigate(-1)}\r\n                disabled={loading}\r\n              >\r\n                Back\r\n              </Button>\r\n              <Button\r\n                onClick={handleSaveSchedule}\r\n                disabled={loading}\r\n              >\r\n                {loading ? 'Saving...' : 'Save & Continue'}\r\n              </Button>\r\n            </div>\r\n          </>\r\n        )}\r\n      </div>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default CampaignSchedule; "], "names": ["CampaignSchedule", "id", "useParams", "navigate", "useNavigate", "loading", "setLoading", "useLocation", "useState", "error", "setError", "success", "setSuccess", "campaign", "setCampaign", "scheduleSettings", "setScheduleSettings", "intervals", "unit", "emailCount", "setEmailCount", "useEffect", "async", "response", "campaignAPI", "getCampaign", "emailContents", "Array", "isArray", "count", "filter", "email", "html", "length", "prev", "fill", "err", "_err$response", "_err$response$data", "data", "message", "fetchCampaign", "handleUnitChange", "_jsxs", "Card", "children", "_jsx", "<PERSON><PERSON>", "type", "onClose", "className", "<PERSON><PERSON>", "onClick", "_Fragment", "size", "variant", "map", "interval", "index", "Input", "name", "value", "toString", "onChange", "e", "handleIntervalChange", "numValue", "parseInt", "validValue", "Math", "max", "updatedIntervals", "target", "label", "disabled", "scheduleData", "emailIntervals", "delay", "updateCampaign", "schedule", "setTimeout", "_err$response2", "_err$response2$data"], "sourceRoot": ""}