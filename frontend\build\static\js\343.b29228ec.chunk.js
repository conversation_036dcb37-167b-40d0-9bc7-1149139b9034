"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[343],{2343:(e,s,a)=>{a.r(s),a.d(s,{default:()=>j});var t=a(5043),n=a(9579);const l={randomUUID:"undefined"!==typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let r;const i=new Uint8Array(16);const d=[];for(let g=0;g<256;++g)d.push((g+256).toString(16).slice(1));function c(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(d[e[s+0]]+d[e[s+1]]+d[e[s+2]]+d[e[s+3]]+"-"+d[e[s+4]]+d[e[s+5]]+"-"+d[e[s+6]]+d[e[s+7]]+"-"+d[e[s+8]]+d[e[s+9]]+"-"+d[e[s+10]]+d[e[s+11]]+d[e[s+12]]+d[e[s+13]]+d[e[s+14]]+d[e[s+15]]).toLowerCase()}const o=function(e,s,a){if(l.randomUUID&&!s&&!e)return l.randomUUID();const t=(e=e||{}).random??e.rng?.()??function(){if(!r){if("undefined"===typeof crypto||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");r=crypto.getRandomValues.bind(crypto)}return r(i)}();if(t.length<16)throw new Error("Random bytes length must be >= 16");if(t[6]=15&t[6]|64,t[8]=63&t[8]|128,s){if((a=a||0)<0||a+16>s.length)throw new RangeError(`UUID byte range ${a}:${a+15} is out of buffer bounds`);for(let e=0;e<16;++e)s[a+e]=t[e];return s}return c(t)};var m=a(9291),x=a(8417),u=a(1411),h=a(4741),p=a(579);const j=()=>{const[e,s]=(0,t.useState)([]),[a,l]=(0,t.useState)(!1),[r,i]=(0,t.useState)(""),[d,c]=(0,t.useState)("manual"),[j,g]=(0,t.useState)([]),[y,f]=(0,t.useState)(!1),[b,v]=(0,t.useState)([]),[N,w]=(0,t.useState)(!1),[C,A]=(0,t.useState)(null),[k,S]=(0,t.useState)(!1),[E,I]=(0,t.useState)(null),[U,D]=(0,t.useState)(""),[z,T]=(0,t.useState)(""),[F,M]=(0,t.useState)(null),[q,R]=(0,t.useState)(!1);(0,t.useEffect)((()=>{(async()=>{w(!0),A(null);try{const e=await n.jg.getAllTemplates();if(e&&e.data){const s=e.data.map((e=>({id:e._id,name:e.templateName||e.name||"Untitled Template"})));v(s)}else console.warn("Received unexpected template data structure:",e),A("Failed to load templates: Unexpected format."),v([])}catch(a){var e,s;console.error("Failed to fetch email templates:",a);const t=(null===(e=a.response)||void 0===e||null===(s=e.data)||void 0===s?void 0:s.message)||a.message||"Failed to load email templates.";A(t),v([])}finally{w(!1)}})()}),[]);const $=e=>{let s;const a=o();s="sendEmail"===e?{id:a,type:"sendEmail",templateId:null}:{id:a,type:"delay",duration:1,unit:"days"},g([...j,s]),f(!1)},V=(e,s)=>{g((a=>a.map((a=>{if(a.id===e){return{...a,...s}}return a}))))};return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("h2",{className:"text-xl font-semibold",children:"Email Sequences"}),(0,p.jsx)("p",{className:"text-text-secondary",children:"Create automated email sequences for your contacts"})]}),(0,p.jsx)(x.A,{onClick:()=>l(!0),children:"Create Automation"})]}),z&&(0,p.jsx)(m.A,{type:"success",message:z,onClose:()=>T(""),className:"mb-6"}),0===e.length?(0,p.jsx)(u.A,{children:(0,p.jsxs)("div",{className:"text-center py-8",children:[(0,p.jsx)("div",{className:"text-4xl mb-4",children:"\ud83d\udce7"}),(0,p.jsx)("h3",{className:"text-xl font-medium mb-2",children:"No Automations Yet"}),(0,p.jsx)("p",{className:"text-text-secondary mb-4",children:"Create your first automation to start sending automated email sequences."}),(0,p.jsx)(x.A,{onClick:()=>l(!0),children:"Create Your First Automation"})]})}):(0,p.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((a=>(0,p.jsxs)(u.A,{children:[(0,p.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,p.jsx)("h3",{className:"text-lg font-medium",children:a.name}),(0,p.jsx)("span",{className:"px-2 py-1 text-xs rounded "+("active"===a.status?"bg-green-800":"bg-gray-700"),children:"active"===a.status?"Active":"Inactive"})]}),(0,p.jsxs)("div",{className:"space-y-2 mb-6",children:[(0,p.jsxs)("div",{className:"flex justify-between",children:[(0,p.jsx)("span",{className:"text-text-secondary",children:"Emails:"}),(0,p.jsx)("span",{children:a.workflow.nodes.filter((e=>"sendEmail"===e.type)).length})]}),(0,p.jsxs)("div",{className:"flex justify-between",children:[(0,p.jsx)("span",{className:"text-text-secondary",children:"Trigger:"}),(0,p.jsx)("span",{className:"capitalize",children:"newContact"===a.workflow.trigger?"New Contact":"Manual"})]}),(0,p.jsxs)("div",{className:"flex justify-between",children:[(0,p.jsx)("span",{className:"text-text-secondary",children:"Created:"}),(0,p.jsx)("span",{children:a.created})]})]}),(0,p.jsxs)("div",{className:"flex space-x-2",children:[(0,p.jsx)(x.A,{variant:"secondary",size:"sm",onClick:()=>{return t=a.id,void s(e.map((e=>e.id===t?{...e,status:"active"===e.status?"inactive":"active"}:e)));var t},children:"active"===a.status?"Deactivate":"Activate"}),(0,p.jsx)(x.A,{size:"sm",onClick:()=>(e=>{M(e),R(!0),D("")})(a),children:"Edit"})]})]},a.id)))}),a&&(0,p.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,p.jsxs)("div",{className:"bg-secondary-bg rounded-lg p-6 max-w-md w-full",children:[(0,p.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Create Automation"}),U&&(0,p.jsx)(m.A,{type:"error",message:U,onClose:()=>D(""),className:"mb-4"}),(0,p.jsx)("div",{className:"mb-4",children:(0,p.jsx)(h.A,{id:"automationName",name:"automationName",label:"Automation Name",value:r,onChange:e=>i(e.target.value),required:!0})}),(0,p.jsxs)("div",{className:"mb-6",children:[(0,p.jsx)("label",{className:"form-label",children:"Trigger"}),(0,p.jsxs)("div",{className:"space-y-2",children:[(0,p.jsxs)("div",{className:"flex items-center",children:[(0,p.jsx)("input",{type:"radio",id:"triggerManual",name:"trigger",value:"manual",checked:"manual"===d,onChange:()=>c("manual"),className:"mr-2"}),(0,p.jsx)("label",{htmlFor:"triggerManual",children:"Manual (start sequence manually)"})]}),(0,p.jsxs)("div",{className:"flex items-center",children:[(0,p.jsx)("input",{type:"radio",id:"triggerNewContact",name:"trigger",value:"newContact",checked:"newContact"===d,onChange:()=>c("newContact"),className:"mr-2"}),(0,p.jsx)("label",{htmlFor:"triggerNewContact",children:"New Contact (start when a new contact is added)"})]})]})]}),(0,p.jsxs)("div",{className:"mb-6",children:[(0,p.jsx)("hr",{className:"my-4 border-gray-600"}),(0,p.jsx)("h3",{className:"text-lg font-medium mb-3",children:"Workflow Steps"}),(0,p.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,p.jsx)("hr",{className:"my-4 border-gray-600"}),0===j.length?(0,p.jsx)("p",{className:"text-text-secondary text-center",children:"(No steps added yet)"}):j.map(((e,s)=>{var a;return(0,p.jsxs)("div",{className:"p-3 bg-primary rounded border border-gray-600 flex justify-between items-center",children:[(0,p.jsx)("hr",{className:"my-4 border-gray-600"}),(0,p.jsxs)("div",{children:[(0,p.jsxs)("span",{className:"font-medium",children:["Step ",s+1,": "]}),"sendEmail"===e.type&&(0,p.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,p.jsx)("hr",{className:"my-4 border-gray-600"}),(0,p.jsx)("span",{children:"Send Email:"}),(0,p.jsx)(x.A,{variant:"secondary",size:"sm",onClick:()=>{I(e.id),S(!0)},children:e.templateId?(null===(a=b.find((s=>s.id===e.templateId)))||void 0===a?void 0:a.name)||"Invalid Template":"Select Template"})]}),"delay"===e.type&&(0,p.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,p.jsx)("hr",{className:"my-4 border-gray-600"}),(0,p.jsx)("span",{children:"Wait for"}),(0,p.jsx)(h.A,{id:`duration-${e.id}`,name:`duration-${e.id}`,type:"number",value:String(e.duration),onChange:s=>{const a=parseInt(s.target.value,10)||1;V(e.id,{duration:Math.max(1,a)})},className:"w-16 p-1 text-center"}),(0,p.jsxs)("select",{value:e.unit,onChange:s=>V(e.id,{unit:s.target.value}),className:"p-1 bg-secondary-bg border border-gray-500 rounded",children:[(0,p.jsx)("option",{value:"minutes",children:"Minutes"}),(0,p.jsx)("option",{value:"hours",children:"Hours"}),(0,p.jsx)("option",{value:"days",children:"Days"})]})]})]}),(0,p.jsx)(x.A,{variant:"danger",size:"sm",onClick:()=>{return s=e.id,void g(j.filter((e=>e.id!==s)));var s},className:"ml-2",children:"\xd7 "})]},e.id)}))]}),(0,p.jsxs)("div",{className:"relative",children:[(0,p.jsx)("hr",{className:"my-4 border-gray-600"}),(0,p.jsx)(x.A,{variant:"secondary",className:"w-full",onClick:()=>f(!y),children:"+ Add Step"}),y&&(0,p.jsxs)("div",{className:"absolute left-0 right-0 mt-2 bg-secondary-bg border border-gray-600 rounded shadow-lg z-10",children:[(0,p.jsx)("hr",{className:"my-4 border-gray-600"}),(0,p.jsxs)("ul",{children:[(0,p.jsx)("li",{children:(0,p.jsx)("button",{onClick:()=>$("sendEmail"),className:"block w-full text-left px-4 py-2 hover:bg-primary",children:"Send Email"})}),(0,p.jsx)("li",{children:(0,p.jsx)("button",{onClick:()=>$("delay"),className:"block w-full text-left px-4 py-2 hover:bg-primary",children:"Wait / Delay"})})]})]})]})]}),(0,p.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,p.jsx)(x.A,{variant:"secondary",onClick:()=>l(!1),children:"Cancel"}),(0,p.jsx)(x.A,{onClick:()=>{if(!r)return void D("Automation name is required");if(0===j.length)return void D("Automation must have at least one step.");D("");const a={id:e.length>0?Math.max(...e.map((e=>e.id)))+1:1,name:r,status:"inactive",workflow:{trigger:d,nodes:j},created:(new Date).toISOString().split("T")[0]};s([...e,a]),T("Automation created successfully!"),l(!1),i(""),c("manual"),g([]),setTimeout((()=>{T("")}),3e3)},children:"Create"})]})]})}),q&&F&&(0,p.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,p.jsxs)("div",{className:"bg-secondary-bg rounded-lg p-6 max-w-md w-full",children:[(0,p.jsxs)("h2",{className:"text-xl font-semibold mb-4",children:["Edit Automation: ",F.name]}),U&&(0,p.jsx)(m.A,{type:"error",message:U,onClose:()=>D(""),className:"mb-4"}),(0,p.jsx)("p",{className:"text-center text-text-secondary my-8",children:"Edit form fields will go here."}),(0,p.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,p.jsx)(x.A,{variant:"secondary",onClick:()=>R(!1),children:"Cancel"}),(0,p.jsxs)(x.A,{children:[" ","Save Changes"]})]})]})}),k&&(0,p.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]",children:(0,p.jsxs)("div",{className:"bg-secondary-bg rounded-lg p-6 max-w-lg w-full max-h-[80vh] flex flex-col",children:[(0,p.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Select Email Template"}),N&&(0,p.jsx)("p",{children:"Loading templates..."}),C&&(0,p.jsx)(m.A,{type:"error",message:C,onClose:()=>A(null)}),(0,p.jsx)("div",{className:"flex-grow overflow-y-auto mb-4",children:!N&&!C&&(0,p.jsx)("ul",{className:"space-y-2",children:b.length>0?b.map((e=>(0,p.jsx)("li",{children:(0,p.jsxs)("button",{className:"w-full text-left p-3 hover:bg-primary rounded border border-gray-700",onClick:()=>{E&&V(E,{templateId:e.id}),S(!1),I(null)},children:[e.name," ",(0,p.jsxs)("span",{className:"text-xs text-text-secondary",children:["(ID: ",e.id,")"]})]})},e.id))):(0,p.jsx)("p",{className:"text-text-secondary text-center",children:"(No templates found)"})})}),(0,p.jsx)("div",{className:"flex justify-end",children:(0,p.jsx)(x.A,{variant:"secondary",onClick:()=>{S(!1),I(null)},children:"Cancel"})})]})})]})}}}]);
//# sourceMappingURL=343.b29228ec.chunk.js.map