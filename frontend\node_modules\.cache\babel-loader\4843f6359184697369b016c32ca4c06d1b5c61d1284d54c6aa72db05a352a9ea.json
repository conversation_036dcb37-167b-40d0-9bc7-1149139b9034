{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\BlockLibrary.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\r\n * Enhanced BlockLibrary component for Driftly Email Generator\r\n * Displays available blocks with improved UI, categorization, and drag feedback\r\n */\n\nimport React, { useCallback, useMemo, useState } from 'react';\nimport { useDrag } from 'react-dnd';\n\n// Use interface instead of importing to avoid conflicts\n// import { Block } from '../types/editor';\n\n// Define ItemTypes if not imported elsewhere\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ItemTypes = {\n  LIBRARY_BLOCK: 'library_block'\n};\n\n// Define Block interface directly here to avoid conflicts\n\n// Component that renders each individual library block\nconst LibraryBlock = /*#__PURE__*/_s(/*#__PURE__*/React.memo(_c = _s(({\n  block,\n  onAddBlock,\n  isVisible\n}) => {\n  _s();\n  const [{\n    isDragging\n  }, drag] = useDrag(() => ({\n    type: ItemTypes.LIBRARY_BLOCK,\n    item: {\n      block,\n      type: ItemTypes.LIBRARY_BLOCK\n    },\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [block]);\n\n  // Don't render content if block is not visible\n  if (!isVisible) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-20 bg-gray-100 animate-pulse rounded\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 12\n    }, this); // Loading placeholder\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: drag,\n    className: `library-block p-3 mb-2 bg-white border rounded-md cursor-move hover:border-blue-400 hover:shadow-sm transition-all ${isDragging ? 'opacity-50 border-blue-500' : 'border-gray-200'}`,\n    onClick: () => onAddBlock(block),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"text-sm font-medium text-gray-800 truncate\",\n        children: block.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded\",\n        children: block.category\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), block.thumbnail ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 w-full h-12 flex items-center justify-center overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: block.thumbnail,\n        alt: block.name,\n        className: \"max-h-full max-w-full object-contain\",\n        loading: \"lazy\" // Add native lazy loading for thumbnails\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-2 text-xs text-gray-400 italic text-center\",\n      children: \"No preview\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n}, \"wuumsFp4qAni9XRJfRhQAZjuD/k=\", false, function () {\n  return [useDrag];\n})), \"wuumsFp4qAni9XRJfRhQAZjuD/k=\", false, function () {\n  return [useDrag];\n});\n\n// Main component for block library\n_c2 = LibraryBlock;\nconst BlockLibrary = ({\n  blocks,\n  onAddBlock\n}) => {\n  _s2();\n  const [activeCategory, setActiveCategory] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [visibleBlocks, setVisibleBlocks] = useState(new Set());\n\n  // Get unique categories for filter tabs\n  const categories = useMemo(() => {\n    const cats = new Set();\n    blocks.forEach(block => {\n      if (block.category) cats.add(block.category);\n    });\n    return ['All', ...Array.from(cats)];\n  }, [blocks]);\n\n  // Filter blocks based on category and search term\n  const filteredBlocks = useMemo(() => {\n    return blocks.filter(block => {\n      // Category filter\n      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {\n        return false;\n      }\n\n      // Search filter\n      if (searchTerm.trim() !== '') {\n        const searchLower = searchTerm.toLowerCase();\n        return block.name.toLowerCase().includes(searchLower) || (block.description || '').toLowerCase().includes(searchLower) || (block.category || '').toLowerCase().includes(searchLower);\n      }\n      return true;\n    });\n  }, [blocks, activeCategory, searchTerm]);\n\n  // Implement intersection observer for lazy loading\n  const blockObserver = useCallback(node => {\n    if (!node) return;\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          const blockId = entry.target.getAttribute('data-block-id');\n          if (blockId) {\n            // Fix Set iteration by using current value to create a new Set\n            setVisibleBlocks(prev => {\n              const newSet = new Set(prev);\n              newSet.add(blockId);\n              return newSet;\n            });\n          }\n        }\n      });\n    }, {\n      rootMargin: '200px 0px'\n    } // Load blocks that are 200px outside viewport\n    );\n    observer.observe(node);\n    return () => observer.disconnect();\n  }, []);\n\n  // Cache check for block visibility\n  const isBlockVisible = useCallback(blockId => {\n    return visibleBlocks.has(blockId);\n  }, [visibleBlocks]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"block-library flex flex-col h-full overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-3 py-2 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"search\",\n        placeholder: \"Search blocks...\",\n        className: \"w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\",\n        value: searchTerm,\n        onChange: e => setSearchTerm(e.target.value)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-nowrap overflow-x-auto px-3 py-2 border-b border-gray-200 hide-scrollbar\",\n      children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n        className: `px-3 py-1 mr-2 text-xs font-medium rounded-full whitespace-nowrap transition-colors ${activeCategory === category || category === 'All' && !activeCategory ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n        onClick: () => setActiveCategory(category === 'All' ? '' : category),\n        children: category\n      }, category, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-3\",\n      children: filteredBlocks.length > 0 ? filteredBlocks.map(block => /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: blockObserver,\n        \"data-block-id\": block.blockId || block._id || `block-${Math.random()}`,\n        children: /*#__PURE__*/_jsxDEV(LibraryBlock, {\n          block: block,\n          onAddBlock: onAddBlock,\n          isVisible: isBlockVisible(block.blockId || block._id || '')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 15\n        }, this)\n      }, block.blockId || block._id || `block-${Math.random()}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-gray-500 text-center p-4\",\n        children: \"No blocks found. Try a different search or category.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n_s2(BlockLibrary, \"GomSnj/dpTzNhBMlHfUCUS9rOqQ=\");\n_c3 = BlockLibrary;\nexport default _c4 = /*#__PURE__*/React.memo(BlockLibrary);\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"LibraryBlock$React.memo\");\n$RefreshReg$(_c2, \"LibraryBlock\");\n$RefreshReg$(_c3, \"BlockLibrary\");\n$RefreshReg$(_c4, \"%default%\");", "map": {"version": 3, "names": ["React", "useCallback", "useMemo", "useState", "useDrag", "jsxDEV", "_jsxDEV", "ItemTypes", "LIBRARY_BLOCK", "LibraryBlock", "_s", "memo", "_c", "block", "onAddBlock", "isVisible", "isDragging", "drag", "type", "item", "collect", "monitor", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "children", "name", "category", "thumbnail", "src", "alt", "loading", "_c2", "BlockLibrary", "blocks", "_s2", "activeCategory", "setActiveCategory", "searchTerm", "setSearchTerm", "visibleBlocks", "setVisibleBlocks", "Set", "categories", "cats", "for<PERSON>ach", "add", "Array", "from", "filteredBlocks", "filter", "trim", "searchLower", "toLowerCase", "includes", "description", "blockObserver", "node", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "blockId", "target", "getAttribute", "prev", "newSet", "rootMargin", "observe", "disconnect", "isBlockVisible", "has", "placeholder", "value", "onChange", "e", "map", "length", "_id", "Math", "random", "_c3", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/BlockLibrary.tsx"], "sourcesContent": ["/**\r\n * Enhanced BlockLibrary component for Driftly Email Generator\r\n * Displays available blocks with improved UI, categorization, and drag feedback\r\n */\r\n\r\nimport React, {\r\n  useCallback,\r\n  useMemo,\r\n  useState,\r\n} from 'react';\r\n\r\nimport { useDrag } from 'react-dnd';\r\n\r\n// Use interface instead of importing to avoid conflicts\r\n// import { Block } from '../types/editor';\r\n\r\n// Define ItemTypes if not imported elsewhere\r\nconst ItemTypes = {\r\n  LIBRARY_BLOCK: 'library_block'\r\n};\r\n\r\n// Define Block interface directly here to avoid conflicts\r\ninterface Block {\r\n  blockId?: string;\r\n  _id?: string; // MongoDB ID for blocks from database\r\n  category: string;\r\n  type: string;\r\n  name: string;\r\n  description?: string;\r\n  mjml?: string;\r\n  thumbnail?: string;\r\n  tags?: string[];\r\n  content?: any;\r\n  isCustom?: boolean;\r\n}\r\n\r\ninterface BlockLibraryProps {\r\n  blocks: Block[];\r\n  onAddBlock: (block: Block) => void;\r\n}\r\n\r\n// Component that renders each individual library block\r\nconst LibraryBlock: React.FC<{\r\n  block: Block,\r\n  onAddBlock: (block: Block) => void,\r\n  isVisible: boolean\r\n}> = React.memo(({ block, onAddBlock, isVisible }) => {\r\n  const [{ isDragging }, drag] = useDrag(() => ({\r\n    type: ItemTypes.LIBRARY_BLOCK,\r\n    item: { block, type: ItemTypes.LIBRARY_BLOCK },\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging()\r\n    })\r\n  }), [block]);\r\n\r\n  // Don't render content if block is not visible\r\n  if (!isVisible) {\r\n    return <div className=\"h-20 bg-gray-100 animate-pulse rounded\" />; // Loading placeholder\r\n  }\r\n\r\n  return (\r\n    <div\r\n      ref={drag}\r\n      className={`library-block p-3 mb-2 bg-white border rounded-md cursor-move hover:border-blue-400 hover:shadow-sm transition-all ${\r\n        isDragging ? 'opacity-50 border-blue-500' : 'border-gray-200'\r\n      }`}\r\n      onClick={() => onAddBlock(block)}\r\n    >\r\n      <div className=\"flex items-center justify-between\">\r\n        <h5 className=\"text-sm font-medium text-gray-800 truncate\">{block.name}</h5>\r\n        <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded\">{block.category}</span>\r\n      </div>\r\n\r\n      {block.thumbnail ? (\r\n        <div className=\"mt-2 w-full h-12 flex items-center justify-center overflow-hidden\">\r\n          <img\r\n            src={block.thumbnail}\r\n            alt={block.name}\r\n            className=\"max-h-full max-w-full object-contain\"\r\n            loading=\"lazy\" // Add native lazy loading for thumbnails\r\n          />\r\n        </div>\r\n      ) : (\r\n        <div className=\"mt-2 text-xs text-gray-400 italic text-center\">No preview</div>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\n// Main component for block library\r\nconst BlockLibrary: React.FC<BlockLibraryProps> = ({ blocks, onAddBlock }) => {\r\n  const [activeCategory, setActiveCategory] = useState<string>('');\r\n  const [searchTerm, setSearchTerm] = useState<string>('');\r\n  const [visibleBlocks, setVisibleBlocks] = useState<Set<string>>(new Set());\r\n\r\n  // Get unique categories for filter tabs\r\n  const categories = useMemo(() => {\r\n    const cats = new Set<string>();\r\n    blocks.forEach(block => {\r\n      if (block.category) cats.add(block.category);\r\n    });\r\n    return ['All', ...Array.from(cats)];\r\n  }, [blocks]);\r\n\r\n  // Filter blocks based on category and search term\r\n  const filteredBlocks = useMemo(() => {\r\n    return blocks.filter(block => {\r\n      // Category filter\r\n      if (activeCategory && activeCategory !== 'All' && block.category !== activeCategory) {\r\n        return false;\r\n      }\r\n\r\n      // Search filter\r\n      if (searchTerm.trim() !== '') {\r\n        const searchLower = searchTerm.toLowerCase();\r\n        return (\r\n          block.name.toLowerCase().includes(searchLower) ||\r\n          (block.description || '').toLowerCase().includes(searchLower) ||\r\n          (block.category || '').toLowerCase().includes(searchLower)\r\n        );\r\n      }\r\n\r\n      return true;\r\n    });\r\n  }, [blocks, activeCategory, searchTerm]);\r\n\r\n  // Implement intersection observer for lazy loading\r\n  const blockObserver = useCallback((node: HTMLDivElement | null) => {\r\n    if (!node) return;\r\n\r\n    const observer = new IntersectionObserver(\r\n      (entries) => {\r\n        entries.forEach(entry => {\r\n          if (entry.isIntersecting) {\r\n            const blockId = entry.target.getAttribute('data-block-id');\r\n            if (blockId) {\r\n              // Fix Set iteration by using current value to create a new Set\r\n              setVisibleBlocks(prev => {\r\n                const newSet = new Set(prev);\r\n                newSet.add(blockId);\r\n                return newSet;\r\n              });\r\n            }\r\n          }\r\n        });\r\n      },\r\n      { rootMargin: '200px 0px' } // Load blocks that are 200px outside viewport\r\n    );\r\n\r\n    observer.observe(node);\r\n\r\n    return () => observer.disconnect();\r\n  }, []);\r\n\r\n  // Cache check for block visibility\r\n  const isBlockVisible = useCallback((blockId: string) => {\r\n    return visibleBlocks.has(blockId);\r\n  }, [visibleBlocks]);\r\n\r\n  return (\r\n    <div className=\"block-library flex flex-col h-full overflow-hidden\">\r\n      {/* Search Box */}\r\n      <div className=\"px-3 py-2 border-b border-gray-200\">\r\n        <input\r\n          type=\"search\"\r\n          placeholder=\"Search blocks...\"\r\n          className=\"w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n          value={searchTerm}\r\n          onChange={(e) => setSearchTerm(e.target.value)}\r\n        />\r\n      </div>\r\n\r\n      {/* Category Tabs */}\r\n      <div className=\"flex flex-nowrap overflow-x-auto px-3 py-2 border-b border-gray-200 hide-scrollbar\">\r\n        {categories.map((category) => (\r\n          <button\r\n            key={category}\r\n            className={`px-3 py-1 mr-2 text-xs font-medium rounded-full whitespace-nowrap transition-colors ${\r\n              activeCategory === category || (category === 'All' && !activeCategory)\r\n                ? 'bg-blue-100 text-blue-700'\r\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n            }`}\r\n            onClick={() => setActiveCategory(category === 'All' ? '' : category)}\r\n          >\r\n            {category}\r\n          </button>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Block List - Virtualized */}\r\n      <div className=\"flex-1 overflow-y-auto p-3\">\r\n        {filteredBlocks.length > 0 ? (\r\n          filteredBlocks.map((block) => (\r\n            <div\r\n              key={block.blockId || block._id || `block-${Math.random()}`}\r\n              ref={blockObserver}\r\n              data-block-id={block.blockId || block._id || `block-${Math.random()}`}\r\n            >\r\n              <LibraryBlock\r\n                block={block}\r\n                onAddBlock={onAddBlock}\r\n                isVisible={isBlockVisible(block.blockId || block._id || '')}\r\n              />\r\n            </div>\r\n          ))\r\n        ) : (\r\n          <div className=\"text-sm text-gray-500 text-center p-4\">\r\n            No blocks found. Try a different search or category.\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default React.memo(BlockLibrary);"], "mappings": ";;;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IACVC,WAAW,EACXC,OAAO,EACPC,QAAQ,QACH,OAAO;AAEd,SAASC,OAAO,QAAQ,WAAW;;AAEnC;AACA;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,SAAS,GAAG;EAChBC,aAAa,EAAE;AACjB,CAAC;;AAED;;AAoBA;AACA,MAAMC,YAIJ,gBAAAC,EAAA,cAAGV,KAAK,CAACW,IAAI,CAAAC,EAAA,GAAAF,EAAA,CAAC,CAAC;EAAEG,KAAK;EAAEC,UAAU;EAAEC;AAAU,CAAC,KAAK;EAAAL,EAAA;EACpD,MAAM,CAAC;IAAEM;EAAW,CAAC,EAAEC,IAAI,CAAC,GAAGb,OAAO,CAAC,OAAO;IAC5Cc,IAAI,EAAEX,SAAS,CAACC,aAAa;IAC7BW,IAAI,EAAE;MAAEN,KAAK;MAAEK,IAAI,EAAEX,SAAS,CAACC;IAAc,CAAC;IAC9CY,OAAO,EAAGC,OAAO,KAAM;MACrBL,UAAU,EAAEK,OAAO,CAACL,UAAU,CAAC;IACjC,CAAC;EACH,CAAC,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;;EAEZ;EACA,IAAI,CAACE,SAAS,EAAE;IACd,oBAAOT,OAAA;MAAKgB,SAAS,EAAC;IAAwC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,CAAC,CAAC;EACrE;EAEA,oBACEpB,OAAA;IACEqB,GAAG,EAAEV,IAAK;IACVK,SAAS,EAAE,sHACTN,UAAU,GAAG,4BAA4B,GAAG,iBAAiB,EAC5D;IACHY,OAAO,EAAEA,CAAA,KAAMd,UAAU,CAACD,KAAK,CAAE;IAAAgB,QAAA,gBAEjCvB,OAAA;MAAKgB,SAAS,EAAC,mCAAmC;MAAAO,QAAA,gBAChDvB,OAAA;QAAIgB,SAAS,EAAC,4CAA4C;QAAAO,QAAA,EAAEhB,KAAK,CAACiB;MAAI;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC5EpB,OAAA;QAAMgB,SAAS,EAAC,uDAAuD;QAAAO,QAAA,EAAEhB,KAAK,CAACkB;MAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5F,CAAC,EAELb,KAAK,CAACmB,SAAS,gBACd1B,OAAA;MAAKgB,SAAS,EAAC,mEAAmE;MAAAO,QAAA,eAChFvB,OAAA;QACE2B,GAAG,EAAEpB,KAAK,CAACmB,SAAU;QACrBE,GAAG,EAAErB,KAAK,CAACiB,IAAK;QAChBR,SAAS,EAAC,sCAAsC;QAChDa,OAAO,EAAC,MAAM,CAAC;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENpB,OAAA;MAAKgB,SAAS,EAAC,+CAA+C;MAAAO,QAAA,EAAC;IAAU;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAC/E;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;EAAA,QAxCgCtB,OAAO;AAAA,EAwCvC,CAAC;EAAA,QAxC+BA,OAAO;AAAA,EAwCtC;;AAEF;AAAAgC,GAAA,GA/CM3B,YAIJ;AA4CF,MAAM4B,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAExB;AAAW,CAAC,KAAK;EAAAyB,GAAA;EAC5E,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtC,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAS,EAAE,CAAC;EACxD,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAc,IAAI2C,GAAG,CAAC,CAAC,CAAC;;EAE1E;EACA,MAAMC,UAAU,GAAG7C,OAAO,CAAC,MAAM;IAC/B,MAAM8C,IAAI,GAAG,IAAIF,GAAG,CAAS,CAAC;IAC9BR,MAAM,CAACW,OAAO,CAACpC,KAAK,IAAI;MACtB,IAAIA,KAAK,CAACkB,QAAQ,EAAEiB,IAAI,CAACE,GAAG,CAACrC,KAAK,CAACkB,QAAQ,CAAC;IAC9C,CAAC,CAAC;IACF,OAAO,CAAC,KAAK,EAAE,GAAGoB,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC;EACrC,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMe,cAAc,GAAGnD,OAAO,CAAC,MAAM;IACnC,OAAOoC,MAAM,CAACgB,MAAM,CAACzC,KAAK,IAAI;MAC5B;MACA,IAAI2B,cAAc,IAAIA,cAAc,KAAK,KAAK,IAAI3B,KAAK,CAACkB,QAAQ,KAAKS,cAAc,EAAE;QACnF,OAAO,KAAK;MACd;;MAEA;MACA,IAAIE,UAAU,CAACa,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC5B,MAAMC,WAAW,GAAGd,UAAU,CAACe,WAAW,CAAC,CAAC;QAC5C,OACE5C,KAAK,CAACiB,IAAI,CAAC2B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC9C,CAAC3C,KAAK,CAAC8C,WAAW,IAAI,EAAE,EAAEF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC,IAC7D,CAAC3C,KAAK,CAACkB,QAAQ,IAAI,EAAE,EAAE0B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,WAAW,CAAC;MAE9D;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAClB,MAAM,EAAEE,cAAc,EAAEE,UAAU,CAAC,CAAC;;EAExC;EACA,MAAMkB,aAAa,GAAG3D,WAAW,CAAE4D,IAA2B,IAAK;IACjE,IAAI,CAACA,IAAI,EAAE;IAEX,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CACtCC,OAAO,IAAK;MACXA,OAAO,CAACf,OAAO,CAACgB,KAAK,IAAI;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxB,MAAMC,OAAO,GAAGF,KAAK,CAACG,MAAM,CAACC,YAAY,CAAC,eAAe,CAAC;UAC1D,IAAIF,OAAO,EAAE;YACX;YACAtB,gBAAgB,CAACyB,IAAI,IAAI;cACvB,MAAMC,MAAM,GAAG,IAAIzB,GAAG,CAACwB,IAAI,CAAC;cAC5BC,MAAM,CAACrB,GAAG,CAACiB,OAAO,CAAC;cACnB,OAAOI,MAAM;YACf,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MAAEC,UAAU,EAAE;IAAY,CAAC,CAAC;IAC9B,CAAC;IAEDV,QAAQ,CAACW,OAAO,CAACZ,IAAI,CAAC;IAEtB,OAAO,MAAMC,QAAQ,CAACY,UAAU,CAAC,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,cAAc,GAAG1E,WAAW,CAAEkE,OAAe,IAAK;IACtD,OAAOvB,aAAa,CAACgC,GAAG,CAACT,OAAO,CAAC;EACnC,CAAC,EAAE,CAACvB,aAAa,CAAC,CAAC;EAEnB,oBACEtC,OAAA;IAAKgB,SAAS,EAAC,oDAAoD;IAAAO,QAAA,gBAEjEvB,OAAA;MAAKgB,SAAS,EAAC,oCAAoC;MAAAO,QAAA,eACjDvB,OAAA;QACEY,IAAI,EAAC,QAAQ;QACb2D,WAAW,EAAC,kBAAkB;QAC9BvD,SAAS,EAAC,kHAAkH;QAC5HwD,KAAK,EAAEpC,UAAW;QAClBqC,QAAQ,EAAGC,CAAC,IAAKrC,aAAa,CAACqC,CAAC,CAACZ,MAAM,CAACU,KAAK;MAAE;QAAAvD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpB,OAAA;MAAKgB,SAAS,EAAC,oFAAoF;MAAAO,QAAA,EAChGkB,UAAU,CAACkC,GAAG,CAAElD,QAAQ,iBACvBzB,OAAA;QAEEgB,SAAS,EAAE,uFACTkB,cAAc,KAAKT,QAAQ,IAAKA,QAAQ,KAAK,KAAK,IAAI,CAACS,cAAe,GAClE,2BAA2B,GAC3B,6CAA6C,EAChD;QACHZ,OAAO,EAAEA,CAAA,KAAMa,iBAAiB,CAACV,QAAQ,KAAK,KAAK,GAAG,EAAE,GAAGA,QAAQ,CAAE;QAAAF,QAAA,EAEpEE;MAAQ,GARJA,QAAQ;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASP,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpB,OAAA;MAAKgB,SAAS,EAAC,4BAA4B;MAAAO,QAAA,EACxCwB,cAAc,CAAC6B,MAAM,GAAG,CAAC,GACxB7B,cAAc,CAAC4B,GAAG,CAAEpE,KAAK,iBACvBP,OAAA;QAEEqB,GAAG,EAAEiC,aAAc;QACnB,iBAAe/C,KAAK,CAACsD,OAAO,IAAItD,KAAK,CAACsE,GAAG,IAAI,SAASC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAG;QAAAxD,QAAA,eAEtEvB,OAAA,CAACG,YAAY;UACXI,KAAK,EAAEA,KAAM;UACbC,UAAU,EAAEA,UAAW;UACvBC,SAAS,EAAE4D,cAAc,CAAC9D,KAAK,CAACsD,OAAO,IAAItD,KAAK,CAACsE,GAAG,IAAI,EAAE;QAAE;UAAA5D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC,GARGb,KAAK,CAACsD,OAAO,IAAItD,KAAK,CAACsE,GAAG,IAAI,SAASC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;QAAA9D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASxD,CACN,CAAC,gBAEFpB,OAAA;QAAKgB,SAAS,EAAC,uCAAuC;QAAAO,QAAA,EAAC;MAEvD;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACa,GAAA,CA3HIF,YAAyC;AAAAiD,GAAA,GAAzCjD,YAAyC;AA6H/C,eAAAkD,GAAA,gBAAevF,KAAK,CAACW,IAAI,CAAC0B,YAAY,CAAC;AAAC,IAAAzB,EAAA,EAAAwB,GAAA,EAAAkD,GAAA,EAAAC,GAAA;AAAAC,YAAA,CAAA5E,EAAA;AAAA4E,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAF,GAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}