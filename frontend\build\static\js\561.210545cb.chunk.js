"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[561],{924:(e,t,l)=>{l.d(t,{A:()=>m});l(9884);var a=l(5043),n=l(722),o=l(5897),s=l.n(o),i=l(579);const r=(0,a.forwardRef)(((e,t)=>{let{initialMjml:l="",initialHtml:o="",onSave:r,height:m="70vh"}=e;const d=(0,a.useRef)(null),c=(0,a.useRef)(null);return(0,a.useEffect)((()=>{if(d.current){c.current&&(console.log("[MjmlEditor] Cleaning up previous editor instance"),c.current.destroy(),c.current=null),console.log("[MjmlEditor] Initializing editor with props:",{hasMjml:!!l,mjmlLength:(null===l||void 0===l?void 0:l.length)||0,hasHtml:!!o,htmlLength:(null===o||void 0===o?void 0:o.length)||0});try{const e=n.Ay.init({container:d.current,fromElement:!1,height:String(m),width:"auto",storageManager:!1,plugins:[s()],pluginsOpts:{"grapesjs-mjml":{useXmlParser:!0,resetBlocks:!1}}});if(!e)return void console.error("[MjmlEditor] Failed to initialize editor");let t;c.current=e,e.Commands.has("mjml-get-code")||(console.log("[MjmlEditor] Registering missing mjml-get-code command"),e.Commands.add("mjml-get-code",{run:e=>{const t=e.getHtml();return{mjml:t,html:t}}})),e.Commands.has("gjs-get-html")||(console.log("[MjmlEditor] Registering missing gjs-get-html command"),e.Commands.add("gjs-get-html",{run:e=>e.getHtml({component:e.getWrapper()})})),setTimeout((()=>{if(c.current)if(c.current.setComponents)try{if(l){console.log("[MjmlEditor] Loading initial MJML:",l.substring(0,100)+"...");try{c.current.setComponents(l),console.log("[MjmlEditor] Successfully loaded MJML content")}catch(e){console.error("[MjmlEditor] Error loading initial MJML:",e),o&&(console.log("[MjmlEditor] Falling back to loading initial HTML"),c.current.setComponents(o),console.log("[MjmlEditor] Successfully loaded HTML as fallback"))}}else o?(console.log("[MjmlEditor] Loading initial HTML (MJML not provided):",o.substring(0,100)+"..."),c.current.setComponents(o),console.log("[MjmlEditor] Successfully loaded HTML content")):(console.log("[MjmlEditor] No content provided, loading default template"),c.current.setComponents("\n                <mjml>\n                  <mj-body>\n                    <mj-section>\n                      <mj-column>\n                        <mj-text>Start designing your email!</mj-text>\n                      </mj-column>\n                    </mj-section>\n                  </mj-body>\n                </mjml>\n              "))}catch(t){console.error("[MjmlEditor] Error in content loading phase:",t)}else console.error("[MjmlEditor] Editor's setComponents method is not available");else console.error("[MjmlEditor] Editor instance not available after timeout")}),100);let a=!1;return e.on("change:changesCount",(()=>{r&&e&&!a&&(t&&clearTimeout(t),t=setTimeout((()=>{try{a=!0;let n="",o="";try{const t=e.runCommand("mjml-get-code");t&&"object"===typeof t&&(n=t.mjml||"",o=t.html||"",console.log("[MjmlEditor] Got code via 'mjml-get-code' command"))}catch(t){console.warn("'mjml-get-code' command failed, using fallback methods:",t)}if(!n&&!o){n=e.getHtml()||"";try{o=e.runCommand("gjs-get-html")||""}catch(l){console.warn("'gjs-get-html' command failed:",l)}o||(o=e.getHtml({component:e.getWrapper()})||""),console.log("[MjmlEditor] Using fallback getHtml()/gjs-get-html()")}if(!n.trim())return console.log("[MjmlEditor] No MJML content to save, skipping save"),void(a=!1);console.log("[MjmlEditor] Attempting to call onSave..."),r(n,o),console.log("[MjmlEditor] onSave callback executed.")}catch(n){console.error("Error during editor change listener:",n)}finally{a=!1}}),500))})),()=>{if(t&&clearTimeout(t),c.current){try{c.current.destroy()}catch(e){console.error("[MjmlEditor] Error during editor cleanup:",e)}c.current=null}}}catch(e){console.error("[MjmlEditor] Critical error during editor initialization:",e)}}}),[l,o,m,r]),(0,a.useImperativeHandle)(t,(()=>({save:async()=>{let e={mjml:"",html:""};if(c.current)try{const t=c.current;if(!t.Commands.has("mjml-get-code"))throw new Error("mjml-get-code command not available");{const l=t.runCommand("mjml-get-code");if(!(l&&"object"===typeof l&&"mjml"in l&&"html"in l))throw console.warn("'mjml-get-code' command did not return expected object, trying fallbacks."),new Error("Command returned unexpected structure");e.mjml=l.mjml||"",e.html=l.html||"",console.log("[MjmlEditor] Manual Save - MJML/HTML from command:",{mjml:e.mjml.substring(0,50)+"...",html:e.html.substring(0,50)+"..."})}}catch(t){console.warn("mjml-get-code command failed on manual save, using fallback:",t);try{const t=c.current,l=t.getHtml()||"";let a="";a=t.Commands.has("gjs-get-html")?t.runCommand("gjs-get-html")||"":t.getHtml({component:t.getWrapper()})||"",l||a?(e.mjml=l,e.html=a||l,console.log("[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.")):console.error("[MjmlEditor] Manual Save - Fallback methods also failed to get content.")}catch(l){console.error("[MjmlEditor] Manual Save - Error during fallback retrieval:",l)}}else console.error("[MjmlEditor] Manual Save - Editor not available.");if(await new Promise((e=>setTimeout(e,100))),c.current&&!e.html.trim()){console.log("[MjmlEditor] Manual Save - Re-fetching HTML after delay...");try{const t=c.current;let l="";t.Commands.has("gjs-get-html")&&(l=t.runCommand("gjs-get-html")),l||(l=t.getHtml({component:t.getWrapper()})||""),l.trim()?(console.log("[MjmlEditor] Manual Save - Found updated HTML after delay."),e.html=l):(console.log("[MjmlEditor] Manual Save - HTML still empty after delay."),e.mjml&&!e.html&&(e.html=e.mjml))}catch(a){console.error("[MjmlEditor] Manual Save - Error re-fetching HTML after delay:",a)}}return e},getEditor:()=>c.current}))),(0,i.jsx)("div",{ref:d,style:{height:m}})}));r.displayName="MjmlEditor";const m=r},4289:(e,t,l)=>{l.d(t,{A:()=>d});var a=l(5043),n=l(6291),o=l(9291),s=l(8417),i=l(4741),r=l(9774),m=l(579);const d=e=>{let{isOpen:t,onClose:l,campaign:d,onScheduled:c}=e;const[g,u]=(0,a.useState)(""),[h,p]=(0,a.useState)(!1),[j,f]=(0,a.useState)("");(0,a.useEffect)((()=>{if(t&&d){let t="";if(d.scheduledFor)try{const e=new Date(d.scheduledFor);if(isNaN(e.getTime()))console.warn("[ScheduleModal] Invalid existing schedule date:",d.scheduledFor);else{const l=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0");t=`${l}-${a}-${n}T${o}:${e.getMinutes().toString().padStart(2,"0")}`,console.log(`[ScheduleModal] Using existing schedule: ${d.scheduledFor} -> Formatted: ${t}`)}}catch(e){console.error("[ScheduleModal] Error parsing existing schedule date:",e)}if(!t){const e=new Date(Date.now()+36e5);t=`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}T${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`,console.log(`[ScheduleModal] Setting default local schedule time: ${t}`)}u(t),f("")}}),[t,d]);const v=()=>{p(!1),f(""),u(""),l()};return(0,m.jsxs)(r.a,{isOpen:t,onClose:v,title:`Schedule Campaign: ${(null===d||void 0===d?void 0:d.name)||""}`,children:[(0,m.jsxs)("div",{className:"space-y-4",children:[j&&(0,m.jsx)(o.A,{type:"error",message:j,onClose:()=>f("")}),(0,m.jsx)("p",{className:"text-text-secondary",children:"Select the date and time to start sending this campaign."}),(0,m.jsx)(i.A,{id:"scheduledDateTimeModal",name:"scheduledDateTimeModal",type:"datetime-local",value:g,onChange:e=>u(e.target.value),label:"Scheduled Date & Time",required:!0,className:"w-full"}),(0,m.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 -mt-2",children:"Your local timezone will be used."})]}),(0,m.jsxs)("div",{className:"mt-6 flex justify-end gap-3",children:[(0,m.jsx)(s.A,{variant:"secondary",onClick:v,disabled:h,children:"Cancel"}),(0,m.jsx)(s.A,{onClick:async()=>{if(d&&g){p(!0),f("");try{const e=new Date(g).toISOString();await n.J.scheduleCampaign(d._id,e),c(`Campaign "${d.name}" scheduled successfully for ${new Date(g).toLocaleString()}.`)}catch(l){var e,t;console.error("Error scheduling campaign:",l),f((null===(e=l.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to schedule campaign.")}finally{p(!1)}}else f("Please select a valid date and time.")},disabled:h||!g,children:h?"Scheduling...":"Schedule Campaign"})]})]})}},5021:()=>{},7561:(e,t,l)=>{l.r(t),l.d(t,{default:()=>j});l(5021),l(9884);var a=l(5043),n=l(9291),o=l(8417),s=l(1411),i=l(4741),r=l(924),m=l(9774),d=l(4289),c=l(9066),g=l(8231),u=l(9579),h=l(6291),p=l(579);const j=()=>{var e,t,l,j;const{id:f}=(0,g.g)(),{user:v}=(0,c.A)(),x=(0,g.Zp)(),[y,C]=(0,a.useState)(null),[b,S]=(0,a.useState)(!0),[M,E]=(0,a.useState)(!1),[N,w]=(0,a.useState)(""),[k,A]=(0,a.useState)(""),[T,F]=(0,a.useState)(1),[D,$]=(0,a.useState)([]),[H,L]=(0,a.useState)((()=>Array.from({length:10},(()=>({mjml:"",html:""}))))),I=(0,a.useRef)(null),[z,J]=(0,a.useState)(""),[R,U]=(0,a.useState)(""),[O,P]=(0,a.useState)(""),[q,_]=(0,a.useState)(""),[B,G]=(0,a.useState)(""),[W,Y]=(0,a.useState)(!1),[V,X]=(0,a.useState)(null),[Z,K]=(0,a.useState)(!1),[Q,ee]=(0,a.useState)([]),[te,le]=(0,a.useState)(!1),[ae,ne]=(0,a.useState)(null);(0,a.useEffect)((()=>{(async()=>{if(!f||"undefined"===f)return console.log("Skipping fetchCampaign due to invalid ID"),void x("/campaigns");console.log("Fetching campaign with ID:",f),S(!0);try{const t=await h.J.getCampaign(f),l=t.data.campaign||t.data;if(l){var e;C(l),J(l.name||""),U(l.subject||""),P(l.fromName||""),_("active"===(null===v||void 0===v||null===(e=v.domain)||void 0===e?void 0:e.status)?`noreply@${v.domain.name}`:l.fromEmail||""),G(l.replyTo||l.fromEmail||"");const t=Array.from({length:10},(()=>({mjml:"",html:""})));l.emailContents&&Array.isArray(l.emailContents)?l.emailContents.forEach(((e,l)=>{l<t.length&&(t[l]={mjml:e.mjml||"",html:e.html||""})})):(l.htmlContent||l.mjmlContent)&&(t[0]={mjml:l.mjmlContent||"",html:l.htmlContent||""}),L(t),S(!1)}else w("Campaign not found"),S(!1)}catch(a){var t,l;console.error("Error fetching campaign:",a),w((null===(t=a.response)||void 0===t||null===(l=t.data)||void 0===l?void 0:l.message)||"Failed to fetch campaign"),S(!1)}})()}),[f,v,x]),(0,a.useEffect)((()=>{const e=[];H.forEach(((t,l)=>{t.html&&t.html.trim()&&e.push(l)})),$(e)}),[H]),(0,a.useEffect)((()=>{Z&&(le(!0),u.jg.getAllTemplates().then((e=>{e.success?ee(e.data):w("Failed to load templates")})).catch((e=>{console.error("Error loading templates:",e),w("Failed to load templates. Please try again.")})).finally((()=>{le(!1)})))}),[Z]);const oe=()=>{if(console.log("[Debug] Using selected template:",ae?{id:ae.id||ae._id,name:ae.name,hasMjml:!!ae.mjmlContent,hasContent:!!ae.content}:"No template selected"),ae){const e=[...H];if(e[T-1]={mjml:ae.mjmlContent||"",html:ae.content||""},console.log("[Debug] Updated email contents:",{emailIndex:T-1,hasMjml:!!e[T-1].mjml,hasHtml:!!e[T-1].html,mjmlLength:e[T-1].mjml.length,htmlLength:e[T-1].html.length}),L(e),ae.content&&ae.content.trim()){const e=T-1;$((t=>t.includes(e)?t:[...t,e].sort(((e,t)=>e-t))))}ne(null),K(!1),setTimeout((()=>{Date.now();F(-1),setTimeout((()=>{F(T)}),50)}),100)}};return b&&!y?(0,p.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,p.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):y||b?y?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsxs)("h1",{className:"text-2xl font-bold mb-6 text-gray-800 dark:text-white",children:["Edit Campaign: ",z||(null===y||void 0===y?void 0:y.name)]}),(0,p.jsxs)(s.A,{children:[N&&(0,p.jsx)(n.A,{type:"error",message:N,onClose:()=>w(""),className:"mb-4"}),k&&(0,p.jsx)(n.A,{type:"success",message:k,onClose:()=>A(""),className:"mb-4"}),(0,p.jsxs)("div",{className:"mb-6 pb-4 border-b dark:border-gray-700 flex flex-wrap items-center justify-between gap-2",children:[(0,p.jsx)(o.A,{onClick:async()=>{if(!f)return void w("Campaign ID missing");if(!z||!R||!O||!q)return void w("Please fill in all required campaign details.");const e=H.filter(((e,t)=>D.includes(t)&&e.html&&e.html.trim()));try{var t,l,a;E(!0),w(""),A("");const n="active"===(null===v||void 0===v||null===(t=v.domain)||void 0===t?void 0:t.status)?`noreply@${v.domain.name}`:q,o={name:z,subject:R,fromName:O,fromEmail:n,replyTo:B||n,emailContents:e.map((e=>({mjml:e.mjml,html:e.html}))),htmlContent:(null===(l=e[0])||void 0===l?void 0:l.html)||""};console.log("Sending update data (Details & Content Only):",o);const s=await h.J.updateCampaign(f,o);A("Campaign details & content updated successfully!"),(null!==(a=s.data)&&void 0!==a&&a.campaign||s.data)&&C(s.data.campaign||s.data),setTimeout((()=>A("")),3e3)}catch(s){var n,o;console.error("Error updating campaign:",s),w((null===(n=s.response)||void 0===n||null===(o=n.data)||void 0===o?void 0:o.message)||"Failed to update campaign")}finally{E(!1)}},disabled:M||b,size:"sm",className:"btn-primary",children:M?"Saving...":"Save Details & Content"}),(0,p.jsxs)("div",{className:"flex space-x-2",children:[(0,p.jsx)(o.A,{variant:"secondary",size:"sm",onClick:()=>x(`/campaigns/recipients/${f}`),disabled:M||b,children:"Manage Recipients"}),(0,p.jsx)(o.A,{variant:"secondary",size:"sm",onClick:()=>{y&&(X({_id:y._id,name:z,scheduledFor:y.scheduledFor}),Y(!0))},disabled:M||b,children:"scheduled"===(null===y||void 0===y?void 0:y.status)?"Reschedule":"Schedule Campaign"}),(0,p.jsx)(o.A,{variant:"secondary",size:"sm",onClick:()=>x("/campaigns"),children:"Back to Dashboard"})]})]}),(0,p.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,p.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,p.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Campaign Details"}),(0,p.jsx)(i.A,{id:"campaignName",name:"campaignName",label:"Campaign Name",value:z,onChange:e=>J(e.target.value),required:!0}),(0,p.jsx)(i.A,{id:"subject",name:"subject",label:"Email Subject",value:R,onChange:e=>U(e.target.value),required:!0}),(0,p.jsx)(i.A,{id:"fromName",name:"fromName",label:"From Name",value:O,onChange:e=>P(e.target.value),required:!0}),(0,p.jsx)(i.A,{id:"fromEmail",name:"fromEmail",label:"From Email",type:"email",value:q,onChange:e=>_(e.target.value),disabled:"active"===(null===v||void 0===v||null===(e=v.domain)||void 0===e?void 0:e.status),required:!0,helpText:"active"===(null===v||void 0===v||null===(t=v.domain)||void 0===t?void 0:t.status)?`Using verified domain: ${v.domain.name}`:""}),(0,p.jsx)(i.A,{id:"replyTo",name:"replyTo",label:"Reply-To Email (optional)",type:"email",value:B,onChange:e=>G(e.target.value)})]}),(0,p.jsxs)("div",{className:"lg:col-span-2",children:[(0,p.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Email Content"}),(0,p.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 h-full",children:[(0,p.jsxs)("div",{className:"w-full lg:w-1/5 flex flex-col gap-2",children:[(0,p.jsxs)("h3",{className:"font-semibold mb-1 text-sm",children:["Select Email (Active: ",D.length,"):"]}),Array.from({length:10}).map(((e,t)=>{var l;const a=!(null===(l=H[t])||void 0===l||!l.html||!H[t].html.trim()),n=T===t+1,s=D.includes(t);let i="secondary",r="",m=null;a?(i=s?"primary":"secondary",r=s?"":"bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500",m=s?(0,p.jsx)("span",{className:"ml-1 text-green-500 dark:text-green-400 font-bold",children:"\u2713"}):(0,p.jsx)("span",{className:"ml-1 text-gray-500 dark:text-gray-400 font-bold",children:"\u23f8"})):r="opacity-75 border border-dashed border-gray-400 dark:border-gray-600";const d=n?"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800":"";return(0,p.jsxs)(o.A,{variant:i,size:"sm",onClick:()=>{F(t+1),a&&$((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t].sort(((e,t)=>e-t))))},className:`w-full text-left flex items-center justify-between ${d} ${r} px-2 py-1`,title:a?s?"Click to exclude":"Click to include":"Click to edit",children:[(0,p.jsxs)("span",{className:"flex-grow truncate text-xs",children:["Email ",t+1,s&&a?" (Active)":a?" (Inactive)":""]}),m]},t)})),(0,p.jsx)(o.A,{variant:"secondary",size:"sm",onClick:()=>K(!0),className:"mt-4",children:"Use Template"})]}),(0,p.jsxs)("div",{className:"w-full lg:w-4/5 flex flex-col",children:[(0,p.jsx)(r.A,{ref:I,initialMjml:(null===(l=H[T-1])||void 0===l?void 0:l.mjml)||"",initialHtml:(null===(j=H[T-1])||void 0===j?void 0:j.html)||"",onSave:(e,t)=>{console.log(`[handleMjmlSave] Saving content for email ${T}`);const l=[...H],a=T-1;a>=0&&a<l.length?(l[a]={mjml:e,html:t},L(l),t&&t.trim()?$((e=>e.includes(a)?e:[...e,a].sort(((e,t)=>e-t)))):$((e=>e.filter((e=>e!==a))))):console.error(`[handleMjmlSave] Invalid currentEmail index: ${T}`)},height:"60vh"},`email-${T}-${Date.now()}`),(0,p.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Click email on left to edit. Active emails (\u2713) will be used if campaign is sent/scheduled."})]})]})]})]})]}),(0,p.jsxs)(m.a,{isOpen:Z,onClose:()=>K(!1),title:"Choose a Template",children:[(0,p.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1",children:[te&&!Q.length?(0,p.jsx)("p",{children:"Loading templates..."}):Q.map((e=>(0,p.jsxs)(s.A,{className:"cursor-pointer transition-all "+((null===ae||void 0===ae?void 0:ae.id)===e.id?"ring-2 ring-primary shadow-lg":"hover:shadow-md"),onClick:()=>(async e=>{console.log(`[Debug] Selecting template with ID: ${e}`),le(!0);try{const l=await u.jg.getTemplateById(e);var t;console.log("[Debug] Template API response:",l),l.success&&l.template?(console.log("[Debug] Setting selected template:",{id:l.template.id||l.template._id,name:l.template.name,hasMjml:!!l.template.mjmlContent,hasContent:!!l.template.content,contentLength:(null===(t=l.template.content)||void 0===t?void 0:t.length)||0}),ne(l.template)):(console.error("[Debug] Failed to load template:",l),w("Failed to load selected template"))}catch(l){console.error("[Debug] Error selecting template:",l),w("Failed to load selected template")}finally{le(!1)}})(e.id),children:[(0,p.jsx)("h4",{className:"font-semibold mb-2 truncate",children:e.name}),(0,p.jsx)("div",{className:"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500",children:e.thumbnailUrl?(0,p.jsx)("img",{src:e.thumbnailUrl,alt:e.name,className:"object-contain h-full w-full"}):(0,p.jsx)("span",{children:"No Preview"})})]},e.id))),te&&ae&&(0,p.jsx)("p",{children:"Loading selected template details..."})]}),(0,p.jsxs)("div",{className:"mt-4 flex justify-end gap-2",children:[(0,p.jsx)(o.A,{variant:"secondary",onClick:()=>{K(!1),ne(null)},children:"Cancel"}),(0,p.jsx)(o.A,{onClick:oe,disabled:!ae||te,children:"Use Selected Template"})]})]}),(0,p.jsx)(d.A,{isOpen:W,onClose:()=>Y(!1),campaign:V,onScheduled:e=>{Y(!1),X(null),A(e),f&&h.J.getCampaign(f).then((e=>C(e.data.campaign||e.data))).catch((e=>console.error("Failed to refresh campaign after schedule:",e)))}})]}):null:(0,p.jsxs)("div",{children:[(0,p.jsx)(n.A,{type:"error",message:N||"Campaign not found or failed to load.",className:"mb-6"}),(0,p.jsx)(o.A,{onClick:()=>x("/campaigns"),children:"Back to Campaigns"})]})}},9774:(e,t,l)=>{l.d(t,{a:()=>r});var a=l(5043),n=l(6018),o=l(6443),s=l(8417),i=l(579);const r=e=>{let{isOpen:t,onClose:l,title:r,children:m,onConfirm:d,confirmText:c="Confirm",confirmVariant:g="primary",cancelText:u="Cancel"}=e;return(0,i.jsx)(n.e,{appear:!0,show:t,as:a.Fragment,children:(0,i.jsxs)(o.lG,{as:"div",className:"relative z-10",onClose:l,children:[(0,i.jsx)(n.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50"})}),(0,i.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,i.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,i.jsx)(n.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,i.jsxs)(o.lG.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all",children:[(0,i.jsx)(o.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-white mb-4",children:r}),(0,i.jsx)("div",{className:"mt-2 text-sm text-gray-300",children:m}),(0,i.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,i.jsx)(s.A,{variant:"secondary",onClick:l,children:u}),d&&(0,i.jsx)(s.A,{variant:g,onClick:d,children:c})]})]})})})})]})})}}}]);
//# sourceMappingURL=561.210545cb.chunk.js.map