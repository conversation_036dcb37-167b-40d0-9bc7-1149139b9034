{"version": 3, "file": "static/js/235.002d2012.chunk.js", "mappings": "6JAqBAA,EAAAA,GAAQC,SACNC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,IAWF,MAuBA,EAvBoCC,IAA6C,IAA5C,KAAEC,EAAI,KAAEC,EAAI,QAAEC,EAAO,OAAEC,EAAM,MAAEC,GAAOL,EACzE,MAAMM,EAAe,CACnBC,YAAY,EACZC,qBAAqB,KAClBL,GAgBL,OAAOM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,SAbnBC,MAClB,OAAQX,GACN,IAAK,OACH,OAAOQ,EAAAA,EAAAA,KAACI,EAAAA,GAAI,CAACX,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACzE,IAAK,MACH,OAAOI,EAAAA,EAAAA,KAACK,EAAAA,GAAG,CAACZ,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACxE,IAAK,MACH,OAAOI,EAAAA,EAAAA,KAACM,EAAAA,GAAG,CAACb,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACxE,QACE,OAAOI,EAAAA,EAAAA,KAAA,KAAAE,SAAG,2BACd,EAGuCC,IAAoB,C,kIChD/D,MA6XA,EA7X4BI,KAC1B,MAAM,KAAEC,IAASC,EAAAA,EAAAA,MACVC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAwB,OACzDC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAAeC,IAAoBN,EAAAA,EAAAA,UAAc,OACjDO,EAAcC,IAAmBR,EAAAA,EAAAA,UAAgB,KAGxDS,EAAAA,EAAAA,YAAU,KACeC,WACrB,IACER,GAAa,GAGb,MAAMS,EAAQC,aAAaC,QAAQ,SAEnC,IAAKF,EAGH,OAFAP,EAAS,uCACTF,GAAa,GAKf,MAAMY,QAA0BC,EAAAA,EAAIC,IAAI,uBAAwB,CAC9DC,QAAS,CAAEC,cAAe,UAAUP,OAIhCQ,QAA0BJ,EAAAA,EAAIC,IAAI,aAAc,CACpDC,QAAS,CAAEC,cAAe,UAAUP,OAIhCS,EAAgBN,EAAkBjC,KAAKA,KACvCwC,EAAYF,EAAkBtC,KAAKA,KAAKwC,WAAa,GAE3Db,EAAgBa,GAGhB,MAAMxC,EAAO,CACXyC,OAAQ,CACNC,KAAMH,EAAcI,QAAQC,WAAa,EACzCC,OAAQN,EAAcI,QAAQG,YAAc,EAC5CC,QAASR,EAAcI,QAAQK,aAAe,EAC9CC,QAAST,EAAUU,QAAO,CAACC,EAAaC,IAAWD,GAAOC,EAAEC,aAAe,IAAI,GAC/EC,aAAcd,EAAUU,QAAO,CAACC,EAAaC,IAAWD,GAAOC,EAAEG,kBAAoB,IAAI,IAE3FC,MAAO,CACLC,SAAUlB,EAAcI,QAAQe,iBAAmB,EACnDC,UAAWpB,EAAcI,QAAQiB,kBAAoB,EACrDC,WAAYtB,EAAcI,QAAQC,UAC/BJ,EAAUU,QAAO,CAACC,EAAaC,IAAWD,GAAOC,EAAEC,aAAe,IAAI,GAAKd,EAAcI,QAAQC,UAAa,IAAM,EACvHkB,gBAAiBvB,EAAcI,QAAQC,UACpCJ,EAAUU,QAAO,CAACC,EAAaC,IAAWD,GAAOC,EAAEG,kBAAoB,IAAI,GAAKhB,EAAcI,QAAQC,UAAa,IAAM,GAE9HmB,UAAW,CACTC,OAAQzB,EAAc0B,gBAAgBC,KAAKd,GAAWA,EAAEe,OACxDC,SAAU,CACR,CACEC,MAAO,OACPrE,KAAMuC,EAAc0B,gBAAgBC,KAAKd,GAAWA,EAAEkB,WAAa,IACnEC,gBAAiB,0BACjBC,YAAa,yBAEf,CACEH,MAAO,SACPrE,KAAMuC,EAAc0B,gBAAgBC,KAAKd,GAAWA,EAAEqB,WAAa,IACnEF,gBAAiB,0BACjBC,YAAa,yBAEf,CACEH,MAAO,UACPrE,KAAMuC,EAAc0B,gBAAgBC,KAAKd,GAAWA,EAAEsB,YAAc,IACpEH,gBAAiB,2BACjBC,YAAa,6BAQrB,IAAIG,EACJ,GAAIpC,EAAcqC,eAAiBrC,EAAcqC,cAAcC,OAAS,EAAG,CAEzE,MAAMb,EAASzB,EAAcqC,cAAcV,KAAKY,GAAcA,EAAKC,MAC7DC,EAAYzC,EAAcqC,cAAcV,KAAKY,GACjDA,EAAKpC,KAAO,EAAKoC,EAAKG,MAAQH,EAAKpC,KAAQ,IAAM,IAInDiC,EAAa,CACXX,SACAI,SAAU,CACR,CACEC,MAAO,YACPrE,KAAMgF,EACNR,YAAa,wBACbD,gBAAiB,2BAEnB,CACEF,MAAO,aACPrE,KAdauC,EAAcqC,cAAcV,KAAKY,GAClDA,EAAKpC,KAAO,EAAKoC,EAAKI,OAASJ,EAAKpC,KAAQ,IAAM,IAc9C8B,YAAa,yBACbD,gBAAiB,6BAIzB,MAEEI,EAAa,CACXX,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,OAC5CI,SAAU,CACR,CACEC,MAAO,YACPrE,KAAM,CACoC,GAAxCuC,EAAcI,QAAQe,gBACkB,IAAxCnB,EAAcI,QAAQe,gBACkB,KAAxCnB,EAAcI,QAAQe,gBACkB,EAAxCnB,EAAcI,QAAQe,gBACkB,IAAxCnB,EAAcI,QAAQe,gBACtBnB,EAAcI,QAAQe,iBAExBc,YAAa,wBACbD,gBAAiB,2BAEnB,CACEF,MAAO,aACPrE,KAAM,CACqC,GAAzCuC,EAAcI,QAAQiB,iBACmB,IAAzCrB,EAAcI,QAAQiB,iBACmB,KAAzCrB,EAAcI,QAAQiB,iBACmB,EAAzCrB,EAAcI,QAAQiB,iBACmB,IAAzCrB,EAAcI,QAAQiB,iBACtBrB,EAAcI,QAAQiB,kBAExBY,YAAa,yBACbD,gBAAiB,8BAMzB9C,EAAiB,IAAIzB,EAAM2E,cAC7B,CAAE,MAAOQ,GACPC,QAAQ9D,MAAM,4BAA6B6D,GAC3C5D,EAAS,iCACX,CAAC,QACCF,GAAa,EACf,GAGFgE,EAAgB,GACf,KAGHzD,EAAAA,EAAAA,YAAU,KACR,IAAKX,EAAc,OAEYY,WAC7B,IACER,GAAa,GAGb,MAAMS,EAAQC,aAAaC,QAAQ,SAEnC,IAAKF,EAGH,OAFAP,EAAS,uCACTF,GAAa,GAKf,MAIMiE,SAJiBpD,EAAAA,EAAIC,IAAI,uBAAuBlB,IAAgB,CACpEmB,QAAS,CAAEC,cAAe,UAAUP,QAGR9B,KAAKA,KAGnCyB,GAAkB8D,IAAS,IACtBA,EACH9C,OAAQ,CACNC,KAAM4C,EAAaE,SAASlB,WAAa,EACzCzB,OAAQyC,EAAaE,SAASf,WAAa,EAC3C1B,QAASuC,EAAaE,SAASd,YAAc,EAC7CzB,QAASqC,EAAaE,SAASnC,aAAe,EAC9CC,aAAcgC,EAAaE,SAASjC,kBAAoB,GAE1DC,MAAO,CACLC,SAAU6B,EAAa3C,QAAQc,UAAY,EAC3CE,UAAW2B,EAAa3C,QAAQgB,WAAa,EAC7CE,WAAYyB,EAAa3C,QAAQkB,YAAc,EAC/CC,gBAAiBwB,EAAa3C,QAAQmB,iBAAmB,MAG/D,CAAE,MAAOqB,GACPC,QAAQ9D,MAAM,qCAAsC6D,GACpD5D,EAAS,qCACX,CAAC,QACCF,GAAa,EACf,GAGFoE,EAAwB,GACvB,CAACxE,IAkCJ,OAAIG,GAEAb,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+EAKjBc,GAEAf,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAsCC,SAClDa,IAKFE,GAUHkE,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAlF,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBiF,EAAAA,EAAAA,MAAA,OAAKlF,UAAU,+DAA8DC,SAAA,EAC3EiF,EAAAA,EAAAA,MAAA,OAAKlF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,SAAC,0BACtCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAqBC,SAAC,gDAGrCiF,EAAAA,EAAAA,MAAA,OAAKlF,UAAU,iBAAgBC,SAAA,EAC7BiF,EAAAA,EAAAA,MAAA,UACElF,UAAU,6EACVoF,MAAO3E,GAAgB,GACvB4E,SAAWC,GAAM5E,EAAgB4E,EAAEC,OAAOH,OAAS,MAAMnF,SAAA,EAEzDF,EAAAA,EAAAA,KAAA,UAAQqF,MAAM,GAAEnF,SAAC,kBAChBiB,EAAawC,KAAKsB,IACjBjF,EAAAA,EAAAA,KAAA,UAA2BqF,MAAOJ,EAAST,IAAItE,SAC5C+E,EAASrB,MADCqB,EAAST,WAM1BxE,EAAAA,EAAAA,KAACyF,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QA/EpBC,KAChB,IAAK3E,EAAe,OAEpB,MACM4E,EAAO,CACX,CAAC,cAAe5E,EAAciB,OAAOC,MACrC,CAAC,gBAAiBlB,EAAciB,OAAOI,QACvC,CAAC,iBAAkBrB,EAAciB,OAAOM,SACxC,CAAC,UAAWvB,EAAciB,OAAOQ,SACjC,CAAC,eAAgBzB,EAAciB,OAAOa,cACtC,CAAC,YAAa,GAAG9B,EAAcgC,MAAMC,SAAS4C,QAAQ,OACtD,CAAC,aAAc,GAAG7E,EAAcgC,MAAMG,UAAU0C,QAAQ,OACxD,CAAC,cAAe,GAAG7E,EAAcgC,MAAMK,WAAWwC,QAAQ,OAC1D,CAAC,mBAAoB,GAAG7E,EAAcgC,MAAMM,gBAAgBuC,QAAQ,QAGhEC,EAAa,CAbH,CAAC,SAAU,SAcjBC,KAAK,QACVH,EAAKlC,KAAIsC,GAAOA,EAAID,KAAK,QAC5BA,KAAK,MAEDE,EAAO,IAAIC,KAAK,CAACJ,GAAa,CAAEvG,KAAM,4BACtC4G,EAAMC,IAAIC,gBAAgBJ,GAC1BK,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAON,EACZG,EAAKI,aAAa,WAAY,aAAajG,GAAgB,aAC3D8F,SAASI,KAAKC,YAAYN,GAC1BA,EAAKO,QACLN,SAASI,KAAKG,YAAYR,EAAK,EAmDwBrG,SAAC,wBAOtDiF,EAAAA,EAAAA,MAAA,OAAKlF,UAAU,4DAA2DC,SAAA,EACxEF,EAAAA,EAAAA,KAACgH,EAAAA,EAAQ,CACPC,MAAM,cACN5B,MAAOpE,EAAciB,OAAOC,KAC5B+E,KAAK,UAEPlH,EAAAA,EAAAA,KAACgH,EAAAA,EAAQ,CACPC,MAAM,YACN5B,MAAO,GAAGpE,EAAcgC,MAAMC,SAAS4C,QAAQ,MAC/CoB,KAAK,MACLC,OAAQ,CAAE9B,MAAO,OAAQ+B,YAAY,MAEvCpH,EAAAA,EAAAA,KAACgH,EAAAA,EAAQ,CACPC,MAAM,aACN5B,MAAO,GAAGpE,EAAcgC,MAAMG,UAAU0C,QAAQ,MAChDoB,KAAK,eACLC,OAAQ,CAAE9B,MAAO,OAAQ+B,YAAY,MAEvCpH,EAAAA,EAAAA,KAACgH,EAAAA,EAAQ,CACPC,MAAM,cACN5B,MAAO,GAAGpE,EAAcgC,MAAMK,WAAWwC,QAAQ,MACjDoB,KAAK,cACLC,OAAQ,CAAE9B,MAAO,OAAQ+B,YAAY,SAIzCjC,EAAAA,EAAAA,MAAA,OAAKlF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAACqH,EAAAA,EAAI,CAACJ,MAAM,oBAAmB/G,UAC7BF,EAAAA,EAAAA,KAACsH,EAAAA,EAAK,CAAC9H,KAAK,MAAMC,KAAMwB,EAAcuC,UAAW7D,OAAQ,SAE3DK,EAAAA,EAAAA,KAACqH,EAAAA,EAAI,CAACJ,MAAM,qBAAoB/G,UAC9BF,EAAAA,EAAAA,KAACsH,EAAAA,EAAK,CAAC9H,KAAK,OAAOC,KAAMwB,EAAcmD,WAAYzE,OAAQ,YAI/DK,EAAAA,EAAAA,KAACqH,EAAAA,EAAI,CAACJ,MAAM,qBAAoB/G,UAC9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BiF,EAAAA,EAAAA,MAAA,SAAOlF,UAAU,eAAcC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,SAAAE,UACEiF,EAAAA,EAAAA,MAAA,MAAAjF,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,UACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,YACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,kBACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,eACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qBAGRiF,EAAAA,EAAAA,MAAA,SAAAjF,SAAA,CACGiB,EAAaoG,MAAM,EAAG,GAAG5D,KAAKsB,IAC7B,MAAM/B,EAAW+B,EAASlB,YACtBkB,EAASf,WAAa,GAAKe,EAASlB,UAAY,KAAK+B,QAAQ,GAAK,MAChE1C,EAAY6B,EAASlB,YACvBkB,EAASd,YAAc,GAAKc,EAASlB,UAAY,KAAK+B,QAAQ,GAAK,MAEvE,OACEX,EAAAA,EAAAA,MAAA,MAAAjF,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,SAAK+E,EAASrB,QACd5D,EAAAA,EAAAA,KAAA,MAAAE,SAAK+E,EAASlB,WAAa,KAC3B/D,EAAAA,EAAAA,KAAA,MAAAE,SAAK+E,EAASf,WAAa,KAC3BlE,EAAAA,EAAAA,KAAA,MAAAE,SAAK+E,EAASd,YAAc,KAC5BnE,EAAAA,EAAAA,KAAA,MAAAE,SAAK+E,EAASnC,aAAe,KAC7B9C,EAAAA,EAAAA,KAAA,MAAAE,SAAK+E,EAASjC,kBAAoB,KAClCmC,EAAAA,EAAAA,MAAA,MAAAjF,SAAA,CAAKgD,EAAS,QACdiC,EAAAA,EAAAA,MAAA,MAAAjF,SAAA,CAAKkD,EAAU,SARR6B,EAAST,IASb,IAGgB,IAAxBrD,EAAamD,SACZtE,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAAA,MAAIwH,QAAS,EAAGvH,UAAU,mBAAkBC,SAAC,+CA7GzDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,SAAC,+BAuHzD,C,uDCtXJ,MAAMuH,EAAOlI,IAAA,IAAC,KAAEqE,GAAwBrE,EAAA,OAAKS,EAAAA,EAAAA,KAAA,KAAGC,UAAW,oBAAoB2D,aAAkB,EAyDjG,EAvD0C8D,IAQnC,IARoC,MACzCT,EAAK,MACL5B,EAAK,KACL6B,EAAI,OACJC,EAAM,UACNlH,EAAY,GAAE,QACd0H,EAAO,QACPC,GACDF,EAKC,OACEvC,EAAAA,EAAAA,MAAA,OAAKlF,UAAW,2CAAoBA,IAAYC,SAAA,EAC9CiF,EAAAA,EAAAA,MAAA,OAAKlF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAE+G,IAC3BC,IAEClH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,UAC9CF,EAAAA,EAAAA,KAACyH,EAAI,CAAC7D,KAAMsD,UAIlBlH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,SAAEmF,IACjCsC,IACC3H,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,SAAEyH,IAE/DR,IAEChC,EAAAA,EAAAA,MAAA,OAAKlF,UAAW,+CAA8CkH,EAAOC,WAAa,oBAAsB,eAAgBlH,SAAA,EACtHF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SACnBiH,EAAOC,YACNpH,EAAAA,EAAAA,KAAA,OAAK6H,MAAM,6BAA6B5H,UAAU,UAAU6H,QAAQ,YAAYC,KAAK,eAAc7H,UAACF,EAAAA,EAAAA,KAAA,QAAMgI,SAAS,UAAUC,EAAE,sHAAsHC,SAAS,eAC9PlI,EAAAA,EAAAA,KAAA,OAAK6H,MAAM,6BAA6B5H,UAAU,UAAU6H,QAAQ,YAAYC,KAAK,eAAc7H,UAACF,EAAAA,EAAAA,KAAA,QAAMgI,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,gBAGhQf,EAAO9B,SAGXuC,GAAWA,EAAQtD,OAAS,IAE3BtE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtD0H,EAAQjE,KAAI,CAACwE,EAAQC,KACpBjD,EAAAA,EAAAA,MAAA,OAAiBlF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAEiI,EAAOrE,SACzD9D,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAEiI,EAAO9C,UAF9B+C,SAOZ,C", "sources": ["components/Chart.tsx", "pages/Analytics.tsx", "components/StatCard.tsx"], "sourcesContent": ["import React from 'react';\n\nimport {\n  ArcElement,\n  BarElement,\n  CategoryScale,\n  Chart as ChartJS,\n  Legend,\n  LinearScale,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n} from 'chart.js';\nimport {\n  Bar,\n  Line,\n  Pie,\n} from 'react-chartjs-2';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface ChartProps {\n  type: 'line' | 'bar' | 'pie';\n  data: any;\n  options?: any;\n  height?: number;\n  width?: number;\n}\n\nconst Chart: React.FC<ChartProps> = ({ type, data, options, height, width }) => {\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: true,\n    ...options,\n  };\n\n  const renderChart = () => {\n    switch (type) {\n      case 'line':\n        return <Line data={data} options={chartOptions} height={height} width={width} />;\n      case 'bar':\n        return <Bar data={data} options={chartOptions} height={height} width={width} />;\n      case 'pie':\n        return <Pie data={data} options={chartOptions} height={height} width={width} />;\n      default:\n        return <p>Unsupported chart type</p>;\n    }\n  };\n\n  return <div className=\"chart-container\">{renderChart()}</div>;\n};\n\nexport default Chart;\n", "import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Button from '../components/Button';\n// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from '../components/Card';\nimport Chart from '../components/Chart';\nimport StatCard from '../components/StatCard';\nimport { useAuth } from '../contexts/AuthContext';\nimport api from '../services/api';\n\nconst Analytics: React.FC = () => {\n  const { user } = useAuth();\n  const [selectedFlow, setSelectedFlow] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [analyticsData, setAnalyticsData] = useState<any>(null);\n  const [campaignList, setCampaignList] = useState<any[]>([]);\n  \n  // Fetch analytics data from the backend\n  useEffect(() => {\n    const fetchAnalytics = async () => {\n      try {\n        setIsLoading(true);\n        \n        // Get token from localStorage\n        const token = localStorage.getItem('token');\n        \n        if (!token) {\n          setError('Authentication token not found');\n          setIsLoading(false);\n          return;\n        }\n        \n        // Fetch dashboard analytics - using api service\n        const dashboardResponse = await api.get('/analytics/dashboard', {\n          headers: { Authorization: `Bearer ${token}` }\n        });\n        \n        // Fetch campaigns for dropdown - using api service\n        const campaignsResponse = await api.get('/campaigns', {\n          headers: { Authorization: `Bearer ${token}` }\n        });\n        \n        // Transform the data for our components\n        const dashboardData = dashboardResponse.data.data;\n        const campaigns = campaignsResponse.data.data.campaigns || [];\n        \n        setCampaignList(campaigns);\n        \n        // Create the analytics data structure\n        const data = {\n          totals: {\n            sent: dashboardData.metrics.totalSent || 0,\n            opened: dashboardData.metrics.totalOpens || 0,\n            clicked: dashboardData.metrics.totalClicks || 0,\n            bounced: campaigns.reduce((sum: number, c: any) => sum + (c.bounceCount || 0), 0),\n            unsubscribed: campaigns.reduce((sum: number, c: any) => sum + (c.unsubscribeCount || 0), 0),\n          },\n          rates: {\n            openRate: dashboardData.metrics.averageOpenRate || 0,\n            clickRate: dashboardData.metrics.averageClickRate || 0,\n            bounceRate: dashboardData.metrics.totalSent ? \n              (campaigns.reduce((sum: number, c: any) => sum + (c.bounceCount || 0), 0) / dashboardData.metrics.totalSent) * 100 : 0,\n            unsubscribeRate: dashboardData.metrics.totalSent ? \n              (campaigns.reduce((sum: number, c: any) => sum + (c.unsubscribeCount || 0), 0) / dashboardData.metrics.totalSent) * 100 : 0,\n          },\n          chartData: {\n            labels: dashboardData.recentCampaigns.map((c: any) => c.name),\n            datasets: [\n              {\n                label: 'Sent',\n                data: dashboardData.recentCampaigns.map((c: any) => c.sentCount || 0),\n                backgroundColor: 'rgba(54, 162, 235, 0.2)',\n                borderColor: 'rgba(54, 162, 235, 1)',\n              },\n              {\n                label: 'Opened',\n                data: dashboardData.recentCampaigns.map((c: any) => c.openCount || 0),\n                backgroundColor: 'rgba(75, 192, 192, 0.2)',\n                borderColor: 'rgba(75, 192, 192, 1)',\n              },\n              {\n                label: 'Clicked',\n                data: dashboardData.recentCampaigns.map((c: any) => c.clickCount || 0),\n                backgroundColor: 'rgba(153, 102, 255, 0.2)',\n                borderColor: 'rgba(153, 102, 255, 1)',\n              },\n            ],\n          },\n        };\n        \n        // Trend data - use real data if available, otherwise create some reasonable mock data\n        // Ideally this would come from a specific trends endpoint\n        let trendsData;\n        if (dashboardData.timeAnalytics && dashboardData.timeAnalytics.length > 0) {\n          // Use real time analytics if available\n          const labels = dashboardData.timeAnalytics.map((item: any) => item._id);\n          const openRates = dashboardData.timeAnalytics.map((item: any) => \n            item.sent > 0 ? (item.opens / item.sent) * 100 : 0);\n          const clickRates = dashboardData.timeAnalytics.map((item: any) => \n            item.sent > 0 ? (item.clicks / item.sent) * 100 : 0);\n            \n          trendsData = {\n            labels,\n            datasets: [\n              {\n                label: 'Open Rate',\n                data: openRates,\n                borderColor: 'rgba(75, 192, 192, 1)',\n                backgroundColor: 'rgba(75, 192, 192, 0.2)',\n              },\n              {\n                label: 'Click Rate',\n                data: clickRates,\n                borderColor: 'rgba(153, 102, 255, 1)',\n                backgroundColor: 'rgba(153, 102, 255, 0.2)',\n              },\n            ],\n          };\n        } else {\n          // Use sample trend data if no real data available\n          trendsData = {\n            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n            datasets: [\n              {\n                label: 'Open Rate',\n                data: [\n                  dashboardData.metrics.averageOpenRate * 0.9,\n                  dashboardData.metrics.averageOpenRate * 0.95,\n                  dashboardData.metrics.averageOpenRate * 1.05,\n                  dashboardData.metrics.averageOpenRate * 1.0,\n                  dashboardData.metrics.averageOpenRate * 0.98,\n                  dashboardData.metrics.averageOpenRate,\n                ],\n                borderColor: 'rgba(75, 192, 192, 1)',\n                backgroundColor: 'rgba(75, 192, 192, 0.2)',\n              },\n              {\n                label: 'Click Rate',\n                data: [\n                  dashboardData.metrics.averageClickRate * 0.9,\n                  dashboardData.metrics.averageClickRate * 0.95,\n                  dashboardData.metrics.averageClickRate * 1.05,\n                  dashboardData.metrics.averageClickRate * 1.0,\n                  dashboardData.metrics.averageClickRate * 0.98,\n                  dashboardData.metrics.averageClickRate,\n                ],\n                borderColor: 'rgba(153, 102, 255, 1)',\n                backgroundColor: 'rgba(153, 102, 255, 0.2)',\n              },\n            ],\n          };\n        }\n        \n        setAnalyticsData({...data, trendsData});\n      } catch (err) {\n        console.error('Error fetching analytics:', err);\n        setError('Failed to fetch analytics data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    fetchAnalytics();\n  }, []);\n  \n  // Fetch campaign-specific analytics when a campaign is selected\n  useEffect(() => {\n    if (!selectedFlow) return;\n    \n    const fetchCampaignAnalytics = async () => {\n      try {\n        setIsLoading(true);\n        \n        // Get token from localStorage\n        const token = localStorage.getItem('token');\n        \n        if (!token) {\n          setError('Authentication token not found');\n          setIsLoading(false);\n          return;\n        }\n        \n        // Use api service for correct URL\n        const response = await api.get(`/analytics/campaign/${selectedFlow}`, {\n          headers: { Authorization: `Bearer ${token}` }\n        });\n        \n        const campaignData = response.data.data;\n        \n        // Update analytics with campaign-specific data\n        setAnalyticsData((prev: any) => ({\n          ...prev,\n          totals: {\n            sent: campaignData.campaign.sentCount || 0,\n            opened: campaignData.campaign.openCount || 0,\n            clicked: campaignData.campaign.clickCount || 0,\n            bounced: campaignData.campaign.bounceCount || 0,\n            unsubscribed: campaignData.campaign.unsubscribeCount || 0,\n          },\n          rates: {\n            openRate: campaignData.metrics.openRate || 0,\n            clickRate: campaignData.metrics.clickRate || 0,\n            bounceRate: campaignData.metrics.bounceRate || 0,\n            unsubscribeRate: campaignData.metrics.unsubscribeRate || 0,\n          },\n        }));\n      } catch (err) {\n        console.error('Error fetching campaign analytics:', err);\n        setError('Failed to fetch campaign analytics');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    fetchCampaignAnalytics();\n  }, [selectedFlow]);\n  \n  // Export data as CSV\n  const exportCSV = () => {\n    if (!analyticsData) return;\n    \n    const headers = ['Metric', 'Value'];\n    const rows = [\n      ['Emails Sent', analyticsData.totals.sent],\n      ['Emails Opened', analyticsData.totals.opened],\n      ['Emails Clicked', analyticsData.totals.clicked],\n      ['Bounced', analyticsData.totals.bounced],\n      ['Unsubscribed', analyticsData.totals.unsubscribed],\n      ['Open Rate', `${analyticsData.rates.openRate.toFixed(1)}%`],\n      ['Click Rate', `${analyticsData.rates.clickRate.toFixed(1)}%`],\n      ['Bounce Rate', `${analyticsData.rates.bounceRate.toFixed(1)}%`],\n      ['Unsubscribe Rate', `${analyticsData.rates.unsubscribeRate.toFixed(1)}%`],\n    ];\n    \n    const csvContent = [\n      headers.join(','),\n      ...rows.map(row => row.join(','))\n    ].join('\\n');\n    \n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.setAttribute('download', `analytics-${selectedFlow || 'all'}.csv`);\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n  \n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n  \n  if (error) {\n    return (\n      <div className=\"p-4 bg-red-800 text-white rounded-md\">\n        {error}\n      </div>\n    );\n  }\n  \n  if (!analyticsData) {\n    return (\n      <div className=\"p-4 bg-gray-800 text-white rounded-md\">\n        No analytics data available\n      </div>\n    );\n  }\n  \n  return (\n    // <Layout title=\"Analytics\">\n    <>\n      <div className=\"mb-6\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between\">\n          <div className=\"mb-4 md:mb-0\">\n            <h2 className=\"text-xl font-semibold\">Campaign Performance</h2>\n            <p className=\"text-text-secondary\">View analytics for your email campaigns</p>\n          </div>\n          \n          <div className=\"flex space-x-2\">\n            <select \n              className=\"form-input bg-gray-700 text-white border border-gray-600 rounded px-3 py-2\"\n              value={selectedFlow || ''}\n              onChange={(e) => setSelectedFlow(e.target.value || null)}\n            >\n              <option value=\"\">All Campaigns</option>\n              {campaignList.map((campaign: any) => (\n                <option key={campaign._id} value={campaign._id}>\n                  {campaign.name}\n                </option>\n              ))}\n            </select>\n            \n            <Button variant=\"secondary\" onClick={exportCSV}>\n              Export CSV\n            </Button>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <StatCard\n          title=\"Emails Sent\"\n          value={analyticsData.totals.sent}\n          icon=\"mail\"\n        />\n        <StatCard\n          title=\"Open Rate\"\n          value={`${analyticsData.rates.openRate.toFixed(1)}%`}\n          icon=\"eye\"\n          change={{ value: '2.1%', isPositive: true }}\n        />\n        <StatCard\n          title=\"Click Rate\"\n          value={`${analyticsData.rates.clickRate.toFixed(1)}%`}\n          icon=\"cursor-click\"\n          change={{ value: '1.5%', isPositive: true }}\n        />\n        <StatCard\n          title=\"Bounce Rate\"\n          value={`${analyticsData.rates.bounceRate.toFixed(1)}%`}\n          icon=\"exclamation\"\n          change={{ value: '0.2%', isPositive: false }}\n        />\n      </div>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n        <Card title=\"Email Performance\">\n          <Chart type=\"bar\" data={analyticsData.chartData} height={300} />\n        </Card>\n        <Card title=\"Performance Trends\">\n          <Chart type=\"line\" data={analyticsData.trendsData} height={300} />\n        </Card>\n      </div>\n      \n      <Card title=\"Detailed Analytics\">\n        <div className=\"table-container\">\n          <table className=\"table w-full\">\n            <thead>\n              <tr>\n                <th>Email</th>\n                <th>Sent</th>\n                <th>Opened</th>\n                <th>Clicked</th>\n                <th>Bounced</th>\n                <th>Unsubscribed</th>\n                <th>Open Rate</th>\n                <th>Click Rate</th>\n              </tr>\n            </thead>\n            <tbody>\n              {campaignList.slice(0, 5).map((campaign: any) => {\n                const openRate = campaign.sentCount ? \n                  ((campaign.openCount || 0) / campaign.sentCount * 100).toFixed(1) : '0.0';\n                const clickRate = campaign.sentCount ? \n                  ((campaign.clickCount || 0) / campaign.sentCount * 100).toFixed(1) : '0.0';\n                  \n                return (\n                  <tr key={campaign._id}>\n                    <td>{campaign.name}</td>\n                    <td>{campaign.sentCount || 0}</td>\n                    <td>{campaign.openCount || 0}</td>\n                    <td>{campaign.clickCount || 0}</td>\n                    <td>{campaign.bounceCount || 0}</td>\n                    <td>{campaign.unsubscribeCount || 0}</td>\n                    <td>{openRate}%</td>\n                    <td>{clickRate}%</td>\n                  </tr>\n                );\n              })}\n              {campaignList.length === 0 && (\n                <tr>\n                  <td colSpan={8} className=\"text-center py-4\">\n                    No campaign data available\n                  </td>\n                </tr>\n              )}\n            </tbody>\n          </table>\n        </div>\n      </Card>\n    </>\n    // </Layout>\n  );\n};\n\nexport default Analytics;\n", "import React from 'react';\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  icon?: string;\n  change?: {\n    value: string | number;\n    isPositive: boolean;\n  };\n  className?: string;\n  tooltip?: string;\n  details?: { label: string; value: string | number }[];\n}\n\n// Helper for icon rendering (replace with your actual icon logic)\nconst Icon = ({ name }: { name: string }) => <i className={`placeholder-icon-${name} w-5 h-5`} />; \n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  icon,\n  change,\n  className = '',\n  tooltip,\n  details\n}) => {\n  // Use the base .card or apply .glassmorphic for futuristic feel\n  // Use container-futuristic if defined, otherwise default card styling from index.css\n  const baseCardClass = \"card container-futuristic flex flex-col\"; \n\n  return (\n    <div className={`${baseCardClass} ${className}`}>\n      <div className=\"flex justify-between items-start mb-2\">\n        <h3 className=\"stat-label\">{title}</h3>\n        {icon && (\n          // Use text-secondary for default icon color\n          <span className=\"text-text-secondary opacity-80\">\n            <Icon name={icon} />\n          </span>\n        )}\n      </div>\n      <div className=\"stat-value mb-1\">{value}</div>\n      {tooltip && (\n        <div className=\"text-xs text-text-secondary mt-1 opacity-90\">{tooltip}</div>\n      )}\n      {change && (\n        // Apply theme colors: growth-green for positive, danger for negative\n        <div className={`text-sm mt-2 flex items-center font-medium ${change.isPositive ? 'text-growth-green' : 'text-danger'}`}>\n          <span className=\"mr-1\">\n            {change.isPositive ? \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\" clipRule=\"evenodd\" /></svg> : \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" /></svg> \n            }\n          </span>\n          {change.value}\n        </div>\n      )}\n      {details && details.length > 0 && (\n        // Use the theme border color\n        <div className=\"mt-auto pt-3 border-t border-border mt-3\">\n          {details.map((detail, index) => (\n            <div key={index} className=\"flex justify-between text-xs py-1\">\n              <span className=\"text-text-secondary opacity-90\">{detail.label}</span>\n              <span className=\"font-medium\">{detail.value}</span>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StatCard;\n"], "names": ["ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "_ref", "type", "data", "options", "height", "width", "chartOptions", "responsive", "maintainAspectRatio", "_jsx", "className", "children", "<PERSON><PERSON><PERSON>", "Line", "Bar", "Pie", "Analytics", "user", "useAuth", "<PERSON><PERSON><PERSON>", "setSelectedFlow", "useState", "isLoading", "setIsLoading", "error", "setError", "analyticsData", "setAnalyticsData", "campaignList", "setCampaignList", "useEffect", "async", "token", "localStorage", "getItem", "dashboardResponse", "api", "get", "headers", "Authorization", "campaignsResponse", "dashboardData", "campaigns", "totals", "sent", "metrics", "totalSent", "opened", "totalOpens", "clicked", "totalClicks", "bounced", "reduce", "sum", "c", "bounceCount", "unsubscribed", "unsubscribeCount", "rates", "openRate", "averageOpenRate", "clickRate", "averageClickRate", "bounceRate", "unsubscribeRate", "chartData", "labels", "recentCampaigns", "map", "name", "datasets", "label", "sentCount", "backgroundColor", "borderColor", "openCount", "clickCount", "trendsData", "timeAnalytics", "length", "item", "_id", "openRates", "opens", "clicks", "err", "console", "fetchAnalytics", "campaignData", "prev", "campaign", "fetchCampaignAnalytics", "_jsxs", "_Fragment", "value", "onChange", "e", "target", "<PERSON><PERSON>", "variant", "onClick", "exportCSV", "rows", "toFixed", "csv<PERSON><PERSON>nt", "join", "row", "blob", "Blob", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "StatCard", "title", "icon", "change", "isPositive", "Card", "Chart", "slice", "colSpan", "Icon", "_ref2", "tooltip", "details", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "detail", "index"], "sourceRoot": ""}