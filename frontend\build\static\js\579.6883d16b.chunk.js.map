{"version": 3, "file": "static/js/579.6883d16b.chunk.js", "mappings": "6NAGO,MAAMA,EAAmB,CAE9BC,gBAAiBC,MAAOC,EAAgBC,KACtC,IAEE,aADuBC,EAAAA,EAAIC,KAAK,eAAgB,CAAEH,SAAQC,UAC1CG,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4BAA6BA,GACrCA,CACR,GAIFE,WAAYR,UACV,IAEE,aADuBG,EAAAA,EAAIM,IAAI,gBACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,kCAAmCA,GAC3CA,CACR,GAIFI,YAAaV,MAAOW,EAAmBC,KACrC,IAEE,aADuBT,EAAAA,EAAIC,KAAK,WAAY,CAAEO,YAAWC,UACzCP,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,wBAAyBA,GACjCA,CACR,IAKSO,EAAyB,CAEpCC,aAAcd,UACZ,IAEE,aADuBG,EAAAA,EAAIM,IAAI,+BACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4CAA6CA,GACrDA,CACR,GAIFS,eAAgBf,MAAOY,EAAcI,KACnC,IAEE,aADuBb,EAAAA,EAAIC,KAAK,oCAAqC,CAAEQ,OAAMI,kBAC7DX,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2CAA4CA,GACpDA,CACR,GAIFW,aAAcjB,UACZ,IAEE,aADuBG,EAAAA,EAAIM,IAAI,+BACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4CAA6CA,GACrDA,CACR,GAIFY,eAAgBlB,MAAOY,EAAcO,EAAiBC,KACpD,IAEE,aADuBjB,EAAAA,EAAIC,KAAK,oCAAqC,CAAEQ,OAAMO,UAASC,eACtEf,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2CAA4CA,GACpDA,CACR,IAKSe,EAAqB,CAEhCC,YAAatB,UACX,IAEE,aADuBG,EAAAA,EAAIM,IAAI,0BACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,uCAAwCA,GAChDA,CACR,GAIFiB,cAAevB,MAAOE,EAAciB,EAAiBK,KACnD,IAEE,aADuBrB,EAAAA,EAAIC,KAAK,+BAAgC,CAAEF,OAAMiB,UAASK,cACjEnB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,sCAAuCA,GAC/CA,CACR,GAIFmB,oBAAqBzB,UACnB,IAEE,aADuBG,EAAAA,EAAIM,IAAI,yBAAyBiB,gBACxCrB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,oCAAqCA,GAC7CA,CACR,IAKSqB,EAAkB,CAE7BC,gBAAiB5B,UACf,IAEE,aADuBG,EAAAA,EAAIM,IAAI,uBACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,qCAAsCA,GAC9CA,CACR,GAIFuB,iBAAkB7B,UAChB,IAEE,aADuBG,EAAAA,EAAIC,KAAK,sBAAuB,CAAE0B,eACzCzB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,8BAA+BA,GACvCA,CACR,GAIFyB,oBAAqB/B,UACnB,IAEE,aADuBG,EAAAA,EAAIC,KAAK,8BAA+B,CAAE4B,eACjD3B,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,mCAAoCA,GAC5CA,CACR,GAIF2B,aAAcjC,UACZ,IAEE,aADuBG,EAAAA,EAAIM,IAAI,kBAAkByB,MACjC7B,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0CAA2CA,GACnDA,CACR,IAKS6B,EAAgB,CAE3BC,WAAYpC,UACV,IAEE,aADuBG,EAAAA,EAAIC,KAAK,kBAAmBiC,IACnChC,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2BAA4BA,GACpCA,CACR,GAIFgC,eAAgBtC,UACd,IAEE,aADuBG,EAAAA,EAAIM,IAAI,YAAY8B,cAC3BlC,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,+BAAgCA,GACxCA,CACR,GAIFkC,aAAcxC,UACZ,IAEE,aADuBG,EAAAA,EAAIM,IAAI,aACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,6BAA8BA,GACtCA,CACR,GAIFmC,UAAWzC,UACT,IAEE,aADuBG,EAAAA,EAAIuC,IAAI,YAAYH,YAC3BlC,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,uBAAwBA,GAChCA,CACR,GAIFqC,WAAY3C,UACV,IAEE,aADuBG,EAAAA,EAAIuC,IAAI,YAAYH,aAC3BlC,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,yBAA0BA,GAClCA,CACR,IAKSsC,EAAiB,CAE5BC,cAAe7C,UACb,IAEE,aADuBG,EAAAA,EAAIC,KAAK,mBAAoB0C,IACpCzC,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,GAIFyC,mBAAoB/C,eAAOgC,GAA6D,IAA1CgB,EAAYC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAAGG,EAAaH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAC9E,IAEE,aADuB9C,EAAAA,EAAIM,IAAI,aAAauB,mBAA2BgB,WAAcI,MACrE/C,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,mCAAoCA,GAC5CA,CACR,CACF,EAGA+C,gBAAiBrD,UACf,IAEE,aADuBG,EAAAA,EAAIM,IAAI,cACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,gCAAiCA,GACzCA,CACR,GAIFgD,cAAetD,MAAOgC,EAAmBc,KACvC,IAEE,aADuB3C,EAAAA,EAAIuC,IAAI,aAAaV,IAAac,IACzCzC,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,GAIFiD,cAAevD,UACb,IAEE,aADuBG,EAAAA,EAAIqD,OAAO,aAAaxB,MAC/B3B,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,IAKSmD,EAAwB,CAEnCC,WAAY1D,UACV,IAEE,aADuBG,EAAAA,EAAIM,IAAI,4BACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,yCAA0CA,GAClDA,CACR,GAIFqD,eAAgB3D,MAAO4D,EAAiBzC,KACtC,IAEE,aADuBhB,EAAAA,EAAIC,KAAK,6BAA8B,CAAEwD,UAASzC,aACzDd,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,6BAA8BA,GACtCA,CACR,GAIFuD,cAAe7D,UACb,IAEE,aADuBG,EAAAA,EAAIuC,IAAI,0BAA2B,CAAEoB,aAC5CzD,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,yCAA0CA,GAClDA,CACR,IA4NSyD,EAAuB,CAElCC,gBAAiBhE,eAAOmB,GAAqF,IAApE8C,EAAchB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,YAAaiB,EAAmBjB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,WAC3F,IAEE,aADuB9C,EAAAA,EAAIC,KAAK,2BAA4B,CAAEe,UAAS8C,SAAQC,iBAC/D7D,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,mCAAoCA,GAC5CA,CACR,CACF,EAGA6D,mBAAoBnE,UAClB,IAEE,aADuBG,EAAAA,EAAIC,KAAK,sCAAuC,CAAEe,aACzDd,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,gCAAiCA,GACzCA,CACR,GAIF8D,kBAAmBpE,UACjB,IAEE,aADuBG,EAAAA,EAAIM,IAAI,4BACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,iCAAkCA,GAC1CA,CACR,GAIF+D,mBAAoBrE,UAClB,IAEE,aADuBG,EAAAA,EAAIM,IAAI,cAAc6D,MAC7BjE,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,kCAAmCA,GAC3CA,CACR,GAIFiE,cAAevE,MAAOwE,EAAerD,KACnC,IAEE,aADuBhB,EAAAA,EAAIC,KAAK,4BAA6B,CAAEoE,QAAOrD,aACtDd,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4BAA6BA,GACrCA,CACR,IA6ZSmE,EAAgC,CAE3CC,mBAAoB1E,MAAO2E,EAAcC,KACvC,IAEE,aADuBzE,EAAAA,EAAIC,KAAK,6BAA8B,CAAEuE,UAASC,iBACzDvE,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0CAA2CA,GACnDA,CACR,GAIFuE,gBAAiB7E,UACf,IAEE,aADuBG,EAAAA,EAAIM,IAAI,eACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,gCAAiCA,GACzCA,CACR,GAIFwE,8BAA+B9E,UAC7B,IAEE,aADuBG,EAAAA,EAAIM,IAAI,kCACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,+CAAgDA,GACxDA,CACR,GAIFyE,gBAAiB/E,UACf,IAEE,aADuBG,EAAAA,EAAIM,IAAI,cAAcuE,MAC7B3E,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2BAA2B0E,KAAO1E,GAC1CA,CACR,GAIFY,eAAgBlB,UACd,IAEE,aADuBG,EAAAA,EAAIC,KAAK,oBAAqB6E,IACrC5E,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2BAA4BA,GACpCA,CACR,GAIF4E,eAAgBlF,MAAOgF,EAAYC,KACjC,IAEE,aADuB9E,EAAAA,EAAIuC,IAAI,cAAcsC,IAAMC,IACnC5E,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2BAA2B0E,KAAO1E,GAC1CA,CACR,GAIF6E,kBAAmBnF,MAAOgF,EAAYI,KACpC,IAEE,aADuBjF,EAAAA,EAAIkF,MAAM,cAAcL,gBAAkBI,IACjD/E,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,uCAAuC0E,KAAO1E,GACtDA,CACR,GAIFgF,eAAgBtF,UACd,IAEE,aADuBG,EAAAA,EAAIqD,OAAO,cAAcwB,MAChC3E,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2BAA2B0E,KAAO1E,GAC1CA,CACR,IAKSiF,EAAkB,CAE7BC,YAAaxF,UACX,IAEE,aADuBG,EAAAA,EAAIM,IAAI,YAAa,CAAEgF,OAAQ,CAAEC,UACxCrF,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2BAA4BA,GACpCA,CACR,GAIFqF,WAAY3F,UACV,IAEE,aADuBG,EAAAA,EAAIM,IAAI,aAAaqB,MAC5BzB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,GAIFsF,cAAe5F,UACb,IAEE,aADuBG,EAAAA,EAAIC,KAAK,YAAayF,IAC7BxF,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,GAIFwF,cAAe9F,MAAO8B,EAAmB+D,KACvC,IAEE,aADuB1F,EAAAA,EAAIuC,IAAI,aAAaZ,IAAa+D,IACzCxF,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,GAIFyF,cAAe/F,UACb,IAEE,aADuBG,EAAAA,EAAIqD,OAAO,aAAa1B,MAC/BzB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,GAIF0F,eAAgBhG,MAAOiG,EAAYC,EAAiClE,KAClE,IACE,MAAMmE,EAAW,IAAIC,SACrBD,EAASE,OAAO,OAAQJ,GACxBE,EAASE,OAAO,SAAUH,GACtBlE,GACFmE,EAASE,OAAO,YAAarE,GAQ/B,aALuB7B,EAAAA,EAAIC,KAAK,mBAAoB+F,EAAU,CAC5DG,QAAS,CACP,eAAgB,0BAGJjG,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4BAA6BA,GACrCA,CACR,GAIFiG,eAAgBvG,iBAAwE,IAAjEkG,EAA+BjD,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,MAAOjB,EAAkBiB,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EAChF,IAEE,aADuBhD,EAAAA,EAAIC,KAAK,mBAAoB,CAAE8F,SAAQlE,eAC9C3B,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4BAA6BA,GACrCA,CACR,CACF,EAGAkG,OAAQxG,MAAO8B,EAAmB4D,KAChC,IAEE,aADuBvF,EAAAA,EAAIC,KAAK,aAAa0B,SAAkB,CAAE4D,SACjDrF,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,+BAAgCA,GACxCA,CACR,GAIFmG,UAAWzG,MAAO8B,EAAmB4D,KACnC,IAEE,aADuBvF,EAAAA,EAAIqD,OAAO,aAAa1B,UAAkB4D,MACjDrF,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,mCAAoCA,GAC5CA,CACR,IAKSoG,EAAiB,CAE5BlG,WAAYR,UACV,IAGE,aADuBG,EAAAA,EAAIM,IAAI,qBACfJ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,kCAAmCA,GAC3CA,CACR,GAIFqG,cAAe3G,UACb,IAGE,aADuBG,EAAAA,EAAIC,KAAK,oBAAqB,CAAEwG,cACvCvG,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,G", "sources": ["services/index.ts"], "sourcesContent": ["import api from './api';\n\n// AI Content Generation Service\nexport const aiContentService = {\n  // Generate content based on prompt\n  generateContent: async (prompt: string, type: string) => {\n    try {\n      const response = await api.post('/ai/generate', { prompt, type });\n      return response.data;\n    } catch (error) {\n      console.error('Error generating content:', error);\n      throw error;\n    }\n  },\n  \n  // Get content generation history\n  getHistory: async () => {\n    try {\n      const response = await api.get('/ai/history');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching content history:', error);\n      throw error;\n    }\n  },\n  \n  // Save generated content\n  saveContent: async (contentId: string, name: string) => {\n    try {\n      const response = await api.post('/ai/save', { contentId, name });\n      return response.data;\n    } catch (error) {\n      console.error('Error saving content:', error);\n      throw error;\n    }\n  }\n};\n\n// Personalization Service\nexport const personalizationService = {\n  // Get personalization variables\n  getVariables: async () => {\n    try {\n      const response = await api.get('/personalization/variables');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching personalization variables:', error);\n      throw error;\n    }\n  },\n  \n  // Create personalization variable\n  createVariable: async (name: string, defaultValue: string) => {\n    try {\n      const response = await api.post('/personalization/variables/create', { name, defaultValue });\n      return response.data;\n    } catch (error) {\n      console.error('Error creating personalization variable:', error);\n      throw error;\n    }\n  },\n  \n  // Get personalization templates\n  getTemplates: async () => {\n    try {\n      const response = await api.get('/personalization/templates');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching personalization templates:', error);\n      throw error;\n    }\n  },\n  \n  // Create personalization template\n  createTemplate: async (name: string, content: string, variables: string[]) => {\n    try {\n      const response = await api.post('/personalization/templates/create', { name, content, variables });\n      return response.data;\n    } catch (error) {\n      console.error('Error creating personalization template:', error);\n      throw error;\n    }\n  }\n};\n\n// Interactive Elements Service\nexport const interactiveService = {\n  // Get interactive elements\n  getElements: async () => {\n    try {\n      const response = await api.get('/interactive/elements');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching interactive elements:', error);\n      throw error;\n    }\n  },\n  \n  // Create interactive element\n  createElement: async (type: string, content: string, settings: any) => {\n    try {\n      const response = await api.post('/interactive/elements/create', { type, content, settings });\n      return response.data;\n    } catch (error) {\n      console.error('Error creating interactive element:', error);\n      throw error;\n    }\n  },\n  \n  // Get element analytics\n  getElementAnalytics: async (elementId: string) => {\n    try {\n      const response = await api.get(`/interactive/elements/${elementId}/analytics`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching element analytics:', error);\n      throw error;\n    }\n  }\n};\n\n// Send Time Optimization Service\nexport const sendTimeService = {\n  // Get optimal send times\n  getOptimalTimes: async () => {\n    try {\n      const response = await api.get('/send-time/optimal');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching optimal send times:', error);\n      throw error;\n    }\n  },\n  \n  // Optimize send time for a contact\n  optimizeSendTime: async (contactId: string) => {\n    try {\n      const response = await api.post('/send-time/optimize', { contactId });\n      return response.data;\n    } catch (error) {\n      console.error('Error optimizing send time:', error);\n      throw error;\n    }\n  },\n  \n  // Run bulk optimization\n  runBulkOptimization: async (segmentId?: string) => {\n    try {\n      const response = await api.post('/send-time/run-optimization', { segmentId });\n      return response.data;\n    } catch (error) {\n      console.error('Error running bulk optimization:', error);\n      throw error;\n    }\n  },\n  \n  // Get optimization job status\n  getJobStatus: async (jobId: string) => {\n    try {\n      const response = await api.get(`/send-time/job/${jobId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching optimization job status:', error);\n      throw error;\n    }\n  }\n};\n\n// A/B Testing Service\nexport const abTestService = {\n  // Create A/B test\n  createTest: async (testData: any) => {\n    try {\n      const response = await api.post('/ab-test/create', testData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating A/B test:', error);\n      throw error;\n    }\n  },\n  \n  // Get test results\n  getTestResults: async (testId: string) => {\n    try {\n      const response = await api.get(`/ab-test/${testId}/results`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching test results:', error);\n      throw error;\n    }\n  },\n  \n  // Get user's tests\n  getUserTests: async () => {\n    try {\n      const response = await api.get('/ab-test');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user tests:', error);\n      throw error;\n    }\n  },\n  \n  // Start test\n  startTest: async (testId: string) => {\n    try {\n      const response = await api.put(`/ab-test/${testId}/start`);\n      return response.data;\n    } catch (error) {\n      console.error('Error starting test:', error);\n      throw error;\n    }\n  },\n  \n  // Cancel test\n  cancelTest: async (testId: string) => {\n    try {\n      const response = await api.put(`/ab-test/${testId}/cancel`);\n      return response.data;\n    } catch (error) {\n      console.error('Error cancelling test:', error);\n      throw error;\n    }\n  }\n};\n\n// Segmentation Service\nexport const segmentService = {\n  // Create segment\n  createSegment: async (segmentData: any) => {\n    try {\n      const response = await api.post('/segments/create', segmentData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating segment:', error);\n      throw error;\n    }\n  },\n  \n  // Get segment contacts\n  getSegmentContacts: async (segmentId: string, page: number = 1, limit: number = 20) => {\n    try {\n      const response = await api.get(`/segments/${segmentId}/contacts?page=${page}&limit=${limit}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching segment contacts:', error);\n      throw error;\n    }\n  },\n  \n  // Get user's segments\n  getUserSegments: async () => {\n    try {\n      const response = await api.get('/segments');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user segments:', error);\n      throw error;\n    }\n  },\n  \n  // Update segment\n  updateSegment: async (segmentId: string, segmentData: any) => {\n    try {\n      const response = await api.put(`/segments/${segmentId}`, segmentData);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating segment:', error);\n      throw error;\n    }\n  },\n  \n  // Delete segment\n  deleteSegment: async (segmentId: string) => {\n    try {\n      const response = await api.delete(`/segments/${segmentId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting segment:', error);\n      throw error;\n    }\n  }\n};\n\n// Deliverability Service\nexport const deliverabilityService = {\n  // Get deliverability metrics\n  getMetrics: async () => {\n    try {\n      const response = await api.get('/deliverability/metrics');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching deliverability metrics:', error);\n      throw error;\n    }\n  },\n  \n  // Check spam score\n  checkSpamScore: async (subject: string, content: string) => {\n    try {\n      const response = await api.post('/deliverability/spam-score', { subject, content });\n      return response.data;\n    } catch (error) {\n      console.error('Error checking spam score:', error);\n      throw error;\n    }\n  },\n  \n  // Update deliverability metrics\n  updateMetrics: async (metrics: any) => {\n    try {\n      const response = await api.put('/deliverability/metrics', { metrics });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating deliverability metrics:', error);\n      throw error;\n    }\n  }\n};\n\n// Journey Service\nexport const journeyService = {\n  // Create journey\n  createJourney: async (journeyData: any) => {\n    try {\n      const response = await api.post('/journeys/create', journeyData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating journey:', error);\n      throw error;\n    }\n  },\n  \n  // Get journey statistics\n  getJourneyStatistics: async (journeyId: string) => {\n    try {\n      const response = await api.get(`/journeys/${journeyId}/statistics`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching journey statistics:', error);\n      throw error;\n    }\n  },\n  \n  // Get user's journeys\n  getUserJourneys: async () => {\n    try {\n      const response = await api.get('/journeys');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user journeys:', error);\n      throw error;\n    }\n  },\n  \n  // Update journey\n  updateJourney: async (journeyId: string, journeyData: any) => {\n    try {\n      const response = await api.put(`/journeys/${journeyId}`, journeyData);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating journey:', error);\n      throw error;\n    }\n  },\n  \n  // Activate journey\n  activateJourney: async (journeyId: string) => {\n    try {\n      const response = await api.put(`/journeys/${journeyId}/activate`);\n      return response.data;\n    } catch (error) {\n      console.error('Error activating journey:', error);\n      throw error;\n    }\n  },\n  \n  // Pause journey\n  pauseJourney: async (journeyId: string) => {\n    try {\n      const response = await api.put(`/journeys/${journeyId}/pause`);\n      return response.data;\n    } catch (error) {\n      console.error('Error pausing journey:', error);\n      throw error;\n    }\n  },\n  \n  // Delete journey\n  deleteJourney: async (journeyId: string) => {\n    try {\n      const response = await api.delete(`/journeys/${journeyId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting journey:', error);\n      throw error;\n    }\n  },\n\n  // Get journey stats (alias for getJourneyStatistics for backward compatibility)\n  getJourneyStats: async (journeyId: string) => {\n    try {\n      const response = await api.get(`/journeys/${journeyId}/statistics`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching journey statistics:', error);\n      throw error;\n    }\n  },\n\n  // Update journey status\n  updateJourneyStatus: async (journeyId: string, status: string) => {\n    try {\n      const response = await api.put(`/journeys/${journeyId}/status`, { status });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating journey status:', error);\n      throw error;\n    }\n  }\n};\n\n// Integration Service\nexport const integrationService = {\n  // Get available integrations\n  getAvailableIntegrations: async () => {\n    try {\n      const response = await api.get('/integrations/available');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching available integrations:', error);\n      throw error;\n    }\n  },\n  \n  // Connect integration\n  connectIntegration: async (integrationId: string, credentials: any, settings?: any) => {\n    try {\n      const response = await api.post('/integrations/connect', { integrationId, credentials, settings });\n      return response.data;\n    } catch (error) {\n      console.error('Error connecting integration:', error);\n      throw error;\n    }\n  },\n  \n  // Get user's integration connections\n  getUserIntegrationConnections: async () => {\n    try {\n      const response = await api.get('/integrations/connections');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user integration connections:', error);\n      throw error;\n    }\n  },\n  \n  // Update integration settings\n  updateIntegrationSettings: async (connectionId: string, settings: any) => {\n    try {\n      const response = await api.put(`/integrations/connections/${connectionId}/settings`, { settings });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating integration settings:', error);\n      throw error;\n    }\n  },\n  \n  // Disconnect integration\n  disconnectIntegration: async (connectionId: string) => {\n    try {\n      const response = await api.put(`/integrations/connections/${connectionId}/disconnect`);\n      return response.data;\n    } catch (error) {\n      console.error('Error disconnecting integration:', error);\n      throw error;\n    }\n  },\n  \n  // Sync integration data\n  syncIntegrationData: async (connectionId: string) => {\n    try {\n      const response = await api.post(`/integrations/connections/${connectionId}/sync`);\n      return response.data;\n    } catch (error) {\n      console.error('Error syncing integration data:', error);\n      throw error;\n    }\n  },\n\n  // Get connection status\n  getConnectionStatus: async (integrationId: string) => {\n    try {\n      const response = await api.get(`/integrations/${integrationId}/status`);\n      return response.data;\n    } catch (error) {\n      console.error('Error getting connection status:', error);\n      throw error;\n    }\n  },\n\n  // Create connection\n  createConnection: async (connectionData: any) => {\n    try {\n      const response = await api.post('/integrations/connections', connectionData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating connection:', error);\n      throw error;\n    }\n  },\n\n  // Test connection\n  testConnection: async (connectionId: string) => {\n    try {\n      const response = await api.post(`/integrations/connections/${connectionId}/test`);\n      return response.data;\n    } catch (error) {\n      console.error('Error testing connection:', error);\n      throw error;\n    }\n  },\n\n  // Delete connection\n  deleteConnection: async (connectionId: string) => {\n    try {\n      const response = await api.delete(`/integrations/connections/${connectionId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting connection:', error);\n      throw error;\n    }\n  }\n};\n\n// Mobile Preview Service\nexport const mobilePreviewService = {\n  // Generate mobile preview\n  generatePreview: async (content: string, device: string = 'iphone_13', orientation: string = 'portrait') => {\n    try {\n      const response = await api.post('/mobile-preview/generate', { content, device, orientation });\n      return response.data;\n    } catch (error) {\n      console.error('Error generating mobile preview:', error);\n      throw error;\n    }\n  },\n  \n  // Test responsiveness\n  testResponsiveness: async (content: string) => {\n    try {\n      const response = await api.post('/mobile-preview/test-responsiveness', { content });\n      return response.data;\n    } catch (error) {\n      console.error('Error testing responsiveness:', error);\n      throw error;\n    }\n  },\n\n  // Get preview history\n  getPreviewHistory: async () => {\n    try {\n      const response = await api.get('/mobile-preview/history');\n      return response.data;\n    } catch (error) {\n      console.error('Error getting preview history:', error);\n      throw error;\n    }\n  },\n\n  // Get template content\n  getTemplateContent: async (templateId: string) => {\n    try {\n      const response = await api.get(`/templates/${templateId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error getting template content:', error);\n      throw error;\n    }\n  },\n\n  // Send test email\n  sendTestEmail: async (email: string, content: string) => {\n    try {\n      const response = await api.post('/mobile-preview/send-test', { email, content });\n      return response.data;\n    } catch (error) {\n      console.error('Error sending test email:', error);\n      throw error;\n    }\n  }\n};\n\n// Accessibility Service\nexport const accessibilityService = {\n  // Check accessibility\n  checkAccessibility: async (content: string) => {\n    try {\n      const response = await api.post('/accessibility/check', { content });\n      return response.data;\n    } catch (error) {\n      console.error('Error checking accessibility:', error);\n      throw error;\n    }\n  },\n  \n  // Fix accessibility issues\n  fixAccessibilityIssues: async (content: string, issues: string[]) => {\n    try {\n      const response = await api.post('/accessibility/fix', { content, issues });\n      return response.data;\n    } catch (error) {\n      console.error('Error fixing accessibility issues:', error);\n      throw error;\n    }\n  },\n\n  // Get template content\n  getTemplateContent: async (templateId: string) => {\n    try {\n      const response = await api.get(`/templates/${templateId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error getting template content:', error);\n      throw error;\n    }\n  },\n\n  // Get fixed content\n  getFixedContent: async (content: string) => {\n    try {\n      const response = await api.post('/accessibility/get-fixed-content', { content });\n      return response.data;\n    } catch (error) {\n      console.error('Error getting fixed content:', error);\n      throw error;\n    }\n  }\n};\n\n// Analytics Service\nexport const analyticsService = {\n  // Get campaign performance\n  getCampaignPerformance: async (campaignId: string) => {\n    try {\n      const response = await api.get(`/analytics/campaigns/${campaignId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching campaign performance:', error);\n      throw error;\n    }\n  },\n  \n  // Get attribution report\n  getAttributionReport: async (period: string = 'last_30_days') => {\n    try {\n      const response = await api.get(`/analytics/attribution?period=${period}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching attribution report:', error);\n      throw error;\n    }\n  },\n  \n  // Update campaign analytics\n  updateCampaignAnalytics: async (campaignId: string, metrics: any) => {\n    try {\n      const response = await api.put(`/analytics/campaigns/${campaignId}`, { metrics });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating campaign analytics:', error);\n      throw error;\n    }\n  },\n  \n  // Get user's campaign analytics\n  getUserCampaignAnalytics: async () => {\n    try {\n      const response = await api.get('/analytics/campaigns');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user campaign analytics:', error);\n      throw error;\n    }\n  },\n\n  // Get analytics data\n  getAnalytics: async (dateRange: string, campaignId?: string) => {\n    try {\n      const response = await api.get(`/analytics?dateRange=${dateRange}${campaignId ? `&campaignId=${campaignId}` : ''}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching analytics data:', error);\n      throw error;\n    }\n  },\n\n  // Get campaigns\n  getCampaigns: async () => {\n    try {\n      const response = await api.get('/campaigns');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching campaigns:', error);\n      throw error;\n    }\n  },\n\n  // Export analytics\n  exportAnalytics: async (dateRange: string, campaignId?: string, format: string = 'csv') => {\n    try {\n      const response = await api.post('/analytics/export', { dateRange, campaignId, format });\n      return response.data;\n    } catch (error) {\n      console.error('Error exporting analytics:', error);\n      throw error;\n    }\n  }\n};\n\n// Template Service\nexport const templateService = {\n  // Get template recommendations\n  getTemplateRecommendations: async (goal: string, industry?: string, segmentId?: string) => {\n    try {\n      const response = await api.post('/templates/recommendations', { goal, industry, segmentId });\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching template recommendations:', error);\n      throw error;\n    }\n  },\n  \n  // Create template\n  createTemplate: async (templateData: any) => {\n    try {\n      const response = await api.post('/templates/create', templateData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating template:', error);\n      throw error;\n    }\n  },\n  \n  // Get user's templates\n  getUserTemplates: async () => {\n    try {\n      const response = await api.get('/templates');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user templates:', error);\n      throw error;\n    }\n  },\n  \n  // Get template by ID\n  getTemplateById: async (id: string) => {\n    try {\n      const response = await api.get(`/templates/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching template:', error);\n      throw error;\n    }\n  },\n  \n  // Update template performance\n  updateTemplatePerformance: async (id: string, performance: any) => {\n    try {\n      const response = await api.put(`/templates/${id}/performance`, { performance });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating template performance:', error);\n      throw error;\n    }\n  }\n};\n\n// Schedule Service\nexport const scheduleService = {\n  // Create schedule\n  createSchedule: async (scheduleData: any) => {\n    try {\n      const response = await api.post('/schedules/create', scheduleData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating schedule:', error);\n      throw error;\n    }\n  },\n  \n  // Get all schedules\n  getAllSchedules: async () => {\n    try {\n      const response = await api.get('/schedules');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching schedules:', error);\n      throw error;\n    }\n  },\n  \n  // Get user's schedules\n  getUserSchedules: async () => {\n    try {\n      const response = await api.get('/schedules');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user schedules:', error);\n      throw error;\n    }\n  },\n  \n  // Get schedule by ID\n  getScheduleById: async (id: string) => {\n    try {\n      const response = await api.get(`/schedules/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching schedule:', error);\n      throw error;\n    }\n  },\n  \n  // Update schedule\n  updateSchedule: async (id: string, scheduleData: any) => {\n    try {\n      const response = await api.put(`/schedules/${id}`, scheduleData);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating schedule:', error);\n      throw error;\n    }\n  },\n  \n  // Activate schedule\n  activateSchedule: async (id: string) => {\n    try {\n      const response = await api.put(`/schedules/${id}/activate`);\n      return response.data;\n    } catch (error) {\n      console.error('Error activating schedule:', error);\n      throw error;\n    }\n  },\n  \n  // Pause schedule\n  pauseSchedule: async (id: string) => {\n    try {\n      const response = await api.put(`/schedules/${id}/pause`);\n      return response.data;\n    } catch (error) {\n      console.error('Error pausing schedule:', error);\n      throw error;\n    }\n  },\n  \n  // Delete schedule\n  deleteSchedule: async (id: string) => {\n    try {\n      const response = await api.delete(`/schedules/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting schedule:', error);\n      throw error;\n    }\n  }\n};\n\n// Export Service\nexport const exportService = {\n  // Export contacts\n  exportContacts: async (segmentId?: string, format: string = 'csv') => {\n    try {\n      const response = await api.post('/export/contacts', { segmentId, format });\n      return response.data;\n    } catch (error) {\n      console.error('Error exporting contacts:', error);\n      throw error;\n    }\n  },\n  \n  // Import contacts\n  importContacts: async (file: string, format: string, segmentId?: string) => {\n    try {\n      const response = await api.post('/export/import', { file, format, segmentId });\n      return response.data;\n    } catch (error) {\n      console.error('Error importing contacts:', error);\n      throw error;\n    }\n  },\n  \n  // Download export file\n  downloadExportFile: async (filename: string) => {\n    try {\n      const response = await api.get(`/export/download/${filename}`, { responseType: 'blob' });\n      return response.data;\n    } catch (error) {\n      console.error('Error downloading export file:', error);\n      throw error;\n    }\n  },\n  \n  // Bulk export\n  bulkExport: async (items: any[], format: string = 'zip') => {\n    try {\n      const response = await api.post('/export/bulk', { items, format });\n      return response.data;\n    } catch (error) {\n      console.error('Error performing bulk export:', error);\n      throw error;\n    }\n  }\n};\n\n// Data Export Service\nexport const dataExportService = {\n  // Get export history\n  getExportHistory: async () => {\n    try {\n      const response = await api.get('/data-export/history');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching export history:', error);\n      throw error;\n    }\n  },\n  \n  // Create export\n  createExport: async (exportConfig: any) => {\n    try {\n      const response = await api.post('/data-export/create', exportConfig);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating export:', error);\n      throw error;\n    }\n  },\n  \n  // Download export\n  downloadExport: async (exportId: string) => {\n    try {\n      const response = await api.get(`/data-export/download/${exportId}`, {\n        responseType: 'blob'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error downloading export:', error);\n      throw error;\n    }\n  },\n  \n  // Export rename to match import\n  exportContacts: async (segmentId?: string, format: string = 'csv') => {\n    try {\n      const response = await api.post('/export/contacts', { segmentId, format });\n      return response.data;\n    } catch (error) {\n      console.error('Error exporting contacts:', error);\n      throw error;\n    }\n  },\n  \n  // Import contacts\n  importContacts: async (file: string, format: string, segmentId?: string) => {\n    try {\n      const response = await api.post('/export/import', { file, format, segmentId });\n      return response.data;\n    } catch (error) {\n      console.error('Error importing contacts:', error);\n      throw error;\n    }\n  },\n  \n  // Download export file\n  downloadExportFile: async (filename: string) => {\n    try {\n      const response = await api.get(`/export/download/${filename}`, { responseType: 'blob' });\n      return response.data;\n    } catch (error) {\n      console.error('Error downloading export file:', error);\n      throw error;\n    }\n  },\n  \n  // Bulk export\n  bulkExport: async (items: any[], format: string = 'zip') => {\n    try {\n      const response = await api.post('/export/bulk', { items, format });\n      return response.data;\n    } catch (error) {\n      console.error('Error performing bulk export:', error);\n      throw error;\n    }\n  }\n};\n\n// Scheduling Service (renamed from scheduleService for backward compatibility)\nexport const schedulingService = scheduleService;\n\n// Template Recommendation Service\nexport const templateRecommendationService = {\n  // Get recommendations based on context/preferences\n  getRecommendations: async (context: any, preferences: any) => {\n    try {\n      const response = await api.post('/templates/recommendations', { context, preferences });\n      return response.data;\n    } catch (error) {\n      console.error('Error getting template recommendations:', error);\n      throw error;\n    }\n  },\n\n  // Get all templates (user + system)\n  getAllTemplates: async () => {\n    try {\n      const response = await api.get('/templates'); // Assuming '/' maps to the combined endpoint\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching all templates:', error);\n      throw error;\n    }\n  },\n\n  // Get system templates grouped by category\n  getSystemTemplatesCategorized: async () => {\n    try {\n      const response = await api.get('/templates/system/categorized');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching categorized system templates:', error);\n      throw error;\n    }\n  },\n\n  // Get a specific template by ID\n  getTemplateById: async (id: string) => {\n    try {\n      const response = await api.get(`/templates/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error fetching template ${id}:`, error);\n      throw error;\n    }\n  },\n\n  // Create a new template\n  createTemplate: async (templateData: any) => {\n    try {\n      const response = await api.post('/templates/create', templateData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating template:', error);\n      throw error;\n    }\n  },\n\n  // Update an existing template\n  updateTemplate: async (id: string, templateData: any) => {\n    try {\n      const response = await api.put(`/templates/${id}`, templateData);\n      return response.data;\n    } catch (error) {\n      console.error(`Error updating template ${id}:`, error);\n      throw error;\n    }\n  },\n\n  // Update template performance metrics\n  updatePerformance: async (id: string, performanceData: any) => {\n    try {\n      const response = await api.patch(`/templates/${id}/performance`, performanceData);\n      return response.data;\n    } catch (error) {\n      console.error(`Error updating template performance ${id}:`, error);\n      throw error;\n    }\n  },\n\n  // Delete a template (if functionality exists and is needed)\n  deleteTemplate: async (id: string) => {\n    try {\n      const response = await api.delete(`/templates/${id}`);\n      return response.data;\n    } catch (error) { \n      console.error(`Error deleting template ${id}:`, error);\n      throw error;\n    }\n  }\n};\n\n// Contacts Service\nexport const contactsService = {\n  // Get all contacts with optional tag filtering\n  getContacts: async (tag?: string) => {\n    try {\n      const response = await api.get('/contacts', { params: { tag } });\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching contacts:', error);\n      throw error;\n    }\n  },\n\n  // Get a single contact\n  getContact: async (contactId: string) => {\n    try {\n      const response = await api.get(`/contacts/${contactId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching contact:', error);\n      throw error;\n    }\n  },\n\n  // Create a new contact\n  createContact: async (contactData: { email: string; name: string; tags?: string[] }) => {\n    try {\n      const response = await api.post('/contacts', contactData);\n      return response.data;\n    } catch (error) {\n      console.error('Error creating contact:', error);\n      throw error;\n    }\n  },\n\n  // Update a contact\n  updateContact: async (contactId: string, contactData: { email?: string; name?: string; tags?: string[] }) => {\n    try {\n      const response = await api.put(`/contacts/${contactId}`, contactData);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating contact:', error);\n      throw error;\n    }\n  },\n\n  // Delete a contact\n  deleteContact: async (contactId: string) => {\n    try {\n      const response = await api.delete(`/contacts/${contactId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting contact:', error);\n      throw error;\n    }\n  },\n\n  // Import contacts from file\n  importContacts: async (file: File, format: 'csv' | 'json' | 'xlsx', segmentId?: string) => {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('format', format);\n      if (segmentId) {\n        formData.append('segmentId', segmentId);\n      }\n      \n      const response = await api.post('/contacts/import', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error importing contacts:', error);\n      throw error;\n    }\n  },\n\n  // Export contacts\n  exportContacts: async (format: 'csv' | 'json' | 'xlsx' = 'csv', segmentId?: string) => {\n    try {\n      const response = await api.post('/contacts/export', { format, segmentId });\n      return response.data;\n    } catch (error) {\n      console.error('Error exporting contacts:', error);\n      throw error;\n    }\n  },\n\n  // Add tag to contact\n  addTag: async (contactId: string, tag: string) => {\n    try {\n      const response = await api.post(`/contacts/${contactId}/tags`, { tag });\n      return response.data;\n    } catch (error) {\n      console.error('Error adding tag to contact:', error);\n      throw error;\n    }\n  },\n\n  // Remove tag from contact\n  removeTag: async (contactId: string, tag: string) => {\n    try {\n      const response = await api.delete(`/contacts/${contactId}/tags/${tag}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error removing tag from contact:', error);\n      throw error;\n    }\n  }\n};\n\n// Billing Service\nexport const billingService = {\n  // Get billing history\n  getHistory: async () => {\n    try {\n      // Assumes GET /api/v1/billing/history returns { data: [...] }\n      const response = await api.get('/billing/history');\n      return response.data; // Return the actual data part of the response\n    } catch (error) {\n      console.error('Error fetching billing history:', error);\n      throw error;\n    }\n  },\n\n  // Purchase flows\n  purchaseFlows: async (quantity: number) => {\n    try {\n      // Assumes POST /api/v1/billing/purchase returns { data: { transaction: {...} } }\n      const response = await api.post('/billing/purchase', { quantity });\n      return response.data; // Return the actual data part of the response\n    } catch (error) {\n      console.error('Error purchasing flows:', error);\n      throw error;\n    }\n  },\n};\n"], "names": ["aiContentService", "generateContent", "async", "prompt", "type", "api", "post", "data", "error", "console", "getHistory", "get", "saveContent", "contentId", "name", "personalizationService", "getVariables", "createVariable", "defaultValue", "getTemplates", "createTemplate", "content", "variables", "interactiveService", "getElements", "createElement", "settings", "getElementAnalytics", "elementId", "sendTimeService", "getOptimalTimes", "optimizeSendTime", "contactId", "runBulkOptimization", "segmentId", "getJobStatus", "jobId", "abTestService", "createTest", "testData", "getTestResults", "testId", "getUserTests", "startTest", "put", "cancelTest", "segmentService", "createSegment", "segmentData", "getSegmentContacts", "page", "arguments", "length", "undefined", "limit", "getUserSegments", "updateSegment", "deleteSegment", "delete", "deliverabilityService", "getMetrics", "checkSpamScore", "subject", "updateMetrics", "metrics", "mobilePreviewService", "generatePreview", "device", "orientation", "testResponsiveness", "getPreviewHistory", "getTemplateContent", "templateId", "sendTestEmail", "email", "templateRecommendationService", "getRecommendations", "context", "preferences", "getAllTemplates", "getSystemTemplatesCategorized", "getTemplateById", "id", "templateData", "updateTemplate", "updatePerformance", "performanceData", "patch", "deleteTemplate", "contactsService", "getContacts", "params", "tag", "getContact", "createContact", "contactData", "updateContact", "deleteContact", "importContacts", "file", "format", "formData", "FormData", "append", "headers", "exportContacts", "addTag", "removeTag", "billingService", "purchaseFlows", "quantity"], "sourceRoot": ""}