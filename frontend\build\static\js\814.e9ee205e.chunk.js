"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[814],{9814:(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});r(5043);var a=r(552),s=r(579);const o=()=>(0,s.jsx)("div",{className:"p-4 md:p-6 lg:p-8 bg-primary-bg text-text-primary min-h-screen",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-text-primary mb-6",children:"Data Export & Import"}),(0,s.jsxs)(a.Zp,{className:"bg-secondary-bg border border-border-color p-6",children:[(0,s.jsx)("p",{className:"text-text-secondary mb-4",children:"This feature is under development. The Data Export & Import tool will allow you to export your data for analysis and import contacts from other platforms."}),(0,s.jsx)(a.Fc,{type:"info",message:"Coming soon! Check back for updates on this feature.",className:"mt-4"})]})]})})}}]);
//# sourceMappingURL=814.e9ee205e.chunk.js.map