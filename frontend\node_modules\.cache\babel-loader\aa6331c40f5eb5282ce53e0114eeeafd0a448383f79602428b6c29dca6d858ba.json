{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\MjmlEditor.tsx\",\n  _s = $RefreshSig$();\nimport 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\n\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';\nimport grapesjs from 'grapesjs';\n// @ts-ignore - grapesjs-mjml lacks official types\nimport grapesjsMjml from 'grapesjs-mjml';\n\n// Define the Ref type for exposing editor methods\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MjmlEditor = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  initialMjml = '',\n  initialHtml = '',\n  onSave,\n  height = '70vh'\n}, ref) => {\n  _s();\n  const editorRef = useRef(null);\n  const grapesEditor = useRef(null);\n\n  // Initialize GrapesJS Editor\n  useEffect(() => {\n    if (!editorRef.current || grapesEditor.current) return; // Initialize only once\n\n    console.log(\"[MjmlEditor] Initializing editor with props:\", {\n      hasMjml: !!initialMjml,\n      mjmlLength: (initialMjml === null || initialMjml === void 0 ? void 0 : initialMjml.length) || 0,\n      hasHtml: !!initialHtml,\n      htmlLength: (initialHtml === null || initialHtml === void 0 ? void 0 : initialHtml.length) || 0\n    });\n    const editor = grapesjs.init({\n      container: editorRef.current,\n      fromElement: false,\n      // Don't load from existing HTML/CSS in the container\n      height: String(height),\n      width: 'auto',\n      storageManager: false,\n      // Disable default storage manager\n      plugins: [grapesjsMjml],\n      pluginsOpts: {\n        'grapesjs-mjml': {\n          // MJML plugin options (optional)\n          // columnsPadding: '0px',\n          useXmlParser: true,\n          // Use the faster XML parser\n          resetBlocks: false // Try keeping default GrapesJS blocks\n          // ... other options\n        }\n      }\n      // Optional: Configure panels, blocks, styles etc.\n    });\n    grapesEditor.current = editor;\n\n    // Load initial content\n    if (initialMjml) {\n      console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\n      try {\n        editor.setComponents(initialMjml); // Use setComponents for MJML\n        console.log(\"[MjmlEditor] Successfully loaded MJML content\");\n      } catch (e) {\n        console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\n        // Fallback to HTML if MJML fails?\n        if (initialHtml) {\n          console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\n          editor.setComponents(initialHtml); // Use setComponents for HTML as well\n          console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\n        }\n      }\n    } else if (initialHtml) {\n      console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\n      editor.setComponents(initialHtml);\n      console.log(\"[MjmlEditor] Successfully loaded HTML content\");\n    } else {\n      // Load default MJML template if nothing is provided\n      console.log(\"[MjmlEditor] No content provided, loading default template\");\n      editor.setComponents(`\n          <mjml>\n            <mj-body>\n              <mj-section>\n                <mj-column>\n                  <mj-text>Start designing your email!</mj-text>\n                </mj-column>\n              </mj-section>\n            </mj-body>\n          </mjml>\n        `);\n    }\n\n    // Attach save listener\n    editor.on('change:changesCount', () => {\n      if (onSave && editor) {\n        try {\n          // Simplify code retrieval: Prioritize commands, fallback to getHtml()\n          let finalMjml = '';\n          let finalHtml = '';\n          try {\n            // Try the specific mjml command first\n            const mjmlCode = editor.runCommand('mjml-get-code');\n            if (mjmlCode && typeof mjmlCode === 'object') {\n              // Command should return object {mjml, html}\n              finalMjml = mjmlCode.mjml || '';\n              finalHtml = mjmlCode.html || '';\n              console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\n            }\n          } catch (cmdErr) {\n            console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\n          }\n\n          // If command failed or didn't return expected structure, use fallbacks\n          if (!finalMjml && !finalHtml) {\n            finalMjml = editor.getHtml() || ''; // Often gets MJML\n            try {\n              // Try getting HTML via command\n              finalHtml = editor.runCommand('gjs-get-html') || '';\n            } catch (htmlCmdErr) {\n              console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\n            }\n            // As a last resort for HTML, maybe just use the component HTML (less reliable)\n            if (!finalHtml) {\n              finalHtml = editor.getHtml({\n                component: editor.getWrapper()\n              }) || '';\n            }\n            console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\n          }\n          console.log(\"[MjmlEditor] Attempting to call onSave...\");\n          // Call onSave as long as the editor instance exists and the prop was passed\n          // Even if mjml/html strings are empty, let the parent decide what to do\n          onSave(finalMjml, finalHtml);\n          console.log(\"[MjmlEditor] onSave callback executed.\");\n        } catch (error) {\n          console.error(\"Error during editor change listener:\", error);\n        }\n      }\n    });\n    return () => {\n      if (grapesEditor.current) {\n        // Clean up panels, commands, etc. specific to this instance if necessary\n        grapesEditor.current.destroy();\n        grapesEditor.current = null;\n      }\n    };\n  }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\n\n  // Expose save method via ref\n  useImperativeHandle(ref, () => ({\n    save: async () => {\n      let generatedCode = {\n        mjml: '',\n        html: ''\n      }; // Initialize with empty strings\n      if (grapesEditor.current) {\n        try {\n          // Try the primary command\n          const result = grapesEditor.current.runCommand('mjml-get-code');\n          // Check if the command returned the expected object structure\n          if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\n            generatedCode.mjml = result.mjml || '';\n            generatedCode.html = result.html || '';\n            console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", {\n              mjml: generatedCode.mjml.substring(0, 50) + '...',\n              html: generatedCode.html.substring(0, 50) + '...'\n            });\n          } else {\n            console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\n            // Throw an error to trigger the catch block for fallback logic\n            throw new Error(\"Command returned unexpected structure\");\n          }\n        } catch (cmdErr) {\n          console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\n          try {\n            // Fallback attempts\n            const editor = grapesEditor.current;\n            const rawMjml = editor.getHtml() || '';\n            const generatedHtml = editor.runCommand('gjs-get-html') || '';\n            if (rawMjml || generatedHtml) {\n              // Use if *either* fallback worked\n              generatedCode.mjml = rawMjml;\n              generatedCode.html = generatedHtml;\n              console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\n            } else {\n              console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\n            }\n          } catch (fallbackErr) {\n            console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\n          }\n        }\n      } else {\n        console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\n      }\n\n      // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\n      await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\n\n      // Re-fetch the HTML specifically after the delay, as it might have updated\n      if (grapesEditor.current && !generatedCode.html.trim()) {\n        // Only re-fetch if HTML was initially empty\n        console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\n        try {\n          const potentiallyUpdatedHtml = grapesEditor.current.runCommand('gjs-get-html') || grapesEditor.current.getHtml({\n            component: grapesEditor.current.getWrapper()\n          }) || '';\n          if (potentiallyUpdatedHtml.trim()) {\n            console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\n            generatedCode.html = potentiallyUpdatedHtml;\n          } else {\n            console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\n          }\n        } catch (refetchErr) {\n          console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\n        }\n      }\n\n      // ALWAYS return the potentially updated generatedCode object\n      return generatedCode;\n    },\n    getEditor: () => grapesEditor.current\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: editorRef,\n    style: {\n      height: height\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 222,\n    columnNumber: 12\n  }, this);\n}, \"qr1qkDeKnw5h2q20lqEB8SlN9eE=\")), \"qr1qkDeKnw5h2q20lqEB8SlN9eE=\");\n\n// Assign display name for debugging\n_c2 = MjmlEditor;\nMjmlEditor.displayName = 'MjmlEditor';\nexport default MjmlEditor;\nvar _c, _c2;\n$RefreshReg$(_c, \"MjmlEditor$forwardRef\");\n$RefreshReg$(_c2, \"MjmlEditor\");", "map": {"version": 3, "names": ["React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "<PERSON><PERSON><PERSON>", "grapesjsMjml", "jsxDEV", "_jsxDEV", "MjmlEditor", "_s", "_c", "initialMjml", "initialHtml", "onSave", "height", "ref", "editor<PERSON><PERSON>", "grapesEditor", "current", "console", "log", "hasMjml", "mjm<PERSON><PERSON><PERSON><PERSON>", "length", "hasHtml", "htmlLength", "editor", "init", "container", "fromElement", "String", "width", "storageManager", "plugins", "pluginsOpts", "useXmlParser", "resetBlocks", "substring", "setComponents", "e", "error", "on", "finalMjml", "finalHtml", "mjmlCode", "runCommand", "mjml", "html", "cmdErr", "warn", "getHtml", "htmlCmdErr", "component", "getWrapper", "destroy", "save", "generatedCode", "result", "Error", "rawMjml", "generatedHtml", "fallbackErr", "Promise", "resolve", "setTimeout", "trim", "potentiallyUpdatedHtml", "refetchErr", "getEditor", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/MjmlEditor.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n// @ts-ignore - grapesjs-mjml lacks official types\r\nimport grapesjsMjml from 'grapesjs-mjml';\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface MjmlEditorRef {\r\n  save: () => Promise<{ mjml: string; html: string }>;\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface MjmlEditorProps {\r\n  initialMjml?: string;\r\n  initialHtml?: string; // Added to potentially load HTML if MJML is missing\r\n  onSave?: (mjml: string, html: string) => void;\r\n  height?: string | number;\r\n}\r\n\r\nconst MjmlEditor = forwardRef<MjmlEditorRef, MjmlEditorProps>(\r\n  ({ initialMjml = '', initialHtml = '', onSave, height = '70vh' }, ref) => {\r\n    const editorRef = useRef<HTMLDivElement>(null);\r\n    const grapesEditor = useRef<Editor | null>(null);\r\n\r\n    // Initialize GrapesJS Editor\r\n    useEffect(() => {\r\n      if (!editorRef.current || grapesEditor.current) return; // Initialize only once\r\n\r\n      console.log(\"[MjmlEditor] Initializing editor with props:\", {\r\n        hasMjml: !!initialMjml,\r\n        mjmlLength: initialMjml?.length || 0,\r\n        hasHtml: !!initialHtml,\r\n        htmlLength: initialHtml?.length || 0\r\n      });\r\n\r\n      const editor = grapesjs.init({\r\n        container: editorRef.current,\r\n        fromElement: false, // Don't load from existing HTML/CSS in the container\r\n        height: String(height),\r\n        width: 'auto',\r\n        storageManager: false, // Disable default storage manager\r\n        plugins: [grapesjsMjml],\r\n        pluginsOpts: {\r\n          'grapesjs-mjml': {\r\n            // MJML plugin options (optional)\r\n            // columnsPadding: '0px',\r\n             useXmlParser: true, // Use the faster XML parser\r\n             resetBlocks: false, // Try keeping default GrapesJS blocks\r\n             // ... other options\r\n          }\r\n        },\r\n        // Optional: Configure panels, blocks, styles etc.\r\n      });\r\n\r\n      grapesEditor.current = editor;\r\n\r\n      // Load initial content\r\n      if (initialMjml) {\r\n         console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\r\n         try {\r\n             editor.setComponents(initialMjml); // Use setComponents for MJML\r\n             console.log(\"[MjmlEditor] Successfully loaded MJML content\");\r\n         } catch (e) {\r\n             console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\r\n             // Fallback to HTML if MJML fails?\r\n             if (initialHtml) {\r\n                console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\r\n                editor.setComponents(initialHtml); // Use setComponents for HTML as well\r\n                console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\r\n             }\r\n         }\r\n      } else if (initialHtml) {\r\n         console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\r\n         editor.setComponents(initialHtml);\r\n         console.log(\"[MjmlEditor] Successfully loaded HTML content\");\r\n      } else {\r\n        // Load default MJML template if nothing is provided\r\n        console.log(\"[MjmlEditor] No content provided, loading default template\");\r\n        editor.setComponents(`\r\n          <mjml>\r\n            <mj-body>\r\n              <mj-section>\r\n                <mj-column>\r\n                  <mj-text>Start designing your email!</mj-text>\r\n                </mj-column>\r\n              </mj-section>\r\n            </mj-body>\r\n          </mjml>\r\n        `);\r\n      }\r\n\r\n\r\n      // Attach save listener\r\n      editor.on('change:changesCount', () => {\r\n        if (onSave && editor) {\r\n           try {\r\n              // Simplify code retrieval: Prioritize commands, fallback to getHtml()\r\n              let finalMjml = '';\r\n              let finalHtml = '';\r\n\r\n              try {\r\n                // Try the specific mjml command first\r\n                const mjmlCode = editor.runCommand('mjml-get-code');\r\n                if (mjmlCode && typeof mjmlCode === 'object') { // Command should return object {mjml, html}\r\n                  finalMjml = mjmlCode.mjml || '';\r\n                  finalHtml = mjmlCode.html || '';\r\n                  console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\r\n                }\r\n              } catch (cmdErr) {\r\n                 console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\r\n              }\r\n\r\n              // If command failed or didn't return expected structure, use fallbacks\r\n              if (!finalMjml && !finalHtml) {\r\n                  finalMjml = editor.getHtml() || ''; // Often gets MJML\r\n                  try {\r\n                    // Try getting HTML via command\r\n                    finalHtml = editor.runCommand('gjs-get-html') || ''; \r\n                  } catch(htmlCmdErr) {\r\n                     console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\r\n                  }\r\n                  // As a last resort for HTML, maybe just use the component HTML (less reliable)\r\n                  if (!finalHtml) {\r\n                    finalHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                  }\r\n                  console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\r\n              }\r\n              \r\n              console.log(\"[MjmlEditor] Attempting to call onSave...\");\r\n              // Call onSave as long as the editor instance exists and the prop was passed\r\n              // Even if mjml/html strings are empty, let the parent decide what to do\r\n              onSave(finalMjml, finalHtml);\r\n              console.log(\"[MjmlEditor] onSave callback executed.\");\r\n\r\n           } catch (error) {\r\n               console.error(\"Error during editor change listener:\", error);\r\n           }\r\n        }\r\n      });\r\n\r\n      return () => {\r\n        if (grapesEditor.current) {\r\n           // Clean up panels, commands, etc. specific to this instance if necessary\r\n           grapesEditor.current.destroy();\r\n           grapesEditor.current = null;\r\n        }\r\n      };\r\n    }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\r\n\r\n    // Expose save method via ref\r\n    useImperativeHandle(ref, () => ({\r\n      save: async () => {\r\n        let generatedCode = { mjml: '', html: '' }; // Initialize with empty strings\r\n        if (grapesEditor.current) {\r\n           try {\r\n               // Try the primary command\r\n               const result = grapesEditor.current.runCommand('mjml-get-code');\r\n               // Check if the command returned the expected object structure\r\n               if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\r\n                 generatedCode.mjml = result.mjml || '';\r\n                 generatedCode.html = result.html || '';\r\n                 console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", { mjml: generatedCode.mjml.substring(0,50)+'...', html: generatedCode.html.substring(0,50)+'...' });\r\n               } else {\r\n                  console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\r\n                  // Throw an error to trigger the catch block for fallback logic\r\n                  throw new Error(\"Command returned unexpected structure\"); \r\n               }\r\n           } catch (cmdErr) {\r\n               console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\r\n               try {\r\n                 // Fallback attempts\r\n                 const editor = grapesEditor.current;\r\n                 const rawMjml = editor.getHtml() || '';\r\n                 const generatedHtml = editor.runCommand('gjs-get-html') || '';\r\n                  if (rawMjml || generatedHtml) { // Use if *either* fallback worked\r\n                       generatedCode.mjml = rawMjml;\r\n                       generatedCode.html = generatedHtml;\r\n                       console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\r\n                   } else {\r\n                        console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\r\n                   }\r\n               } catch (fallbackErr) {\r\n                  console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\r\n               }\r\n           }\r\n        } else {\r\n          console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\r\n        }\r\n\r\n        // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\r\n        await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\r\n        \r\n        // Re-fetch the HTML specifically after the delay, as it might have updated\r\n        if (grapesEditor.current && !generatedCode.html.trim()) { // Only re-fetch if HTML was initially empty\r\n            console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\r\n            try {\r\n                 const potentiallyUpdatedHtml = grapesEditor.current.runCommand('gjs-get-html') || grapesEditor.current.getHtml({ component: grapesEditor.current.getWrapper() }) || '';\r\n                 if (potentiallyUpdatedHtml.trim()) {\r\n                     console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\r\n                     generatedCode.html = potentiallyUpdatedHtml;\r\n                 } else {\r\n                    console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\r\n                 }\r\n            } catch (refetchErr) {\r\n                console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\r\n            }\r\n        }\r\n\r\n        // ALWAYS return the potentially updated generatedCode object\r\n        return generatedCode; \r\n      },\r\n       getEditor: () => grapesEditor.current,\r\n    }));\r\n\r\n    return <div ref={editorRef} style={{ height: height }} />;\r\n  }\r\n);\r\n\r\n// Assign display name for debugging\r\nMjmlEditor.displayName = 'MjmlEditor';\r\n\r\nexport default MjmlEditor;"], "mappings": ";;AAAA,OAAO,kCAAkC,CAAC,CAAC;;AAE3C,OAAOA,KAAK,IACVC,UAAU,EACVC,SAAS,EACTC,mBAAmB,EACnBC,MAAM,QACD,OAAO;AAEd,OAAOC,QAAQ,MAAkB,UAAU;AAC3C;AACA,OAAOC,YAAY,MAAM,eAAe;;AAExC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAaA,MAAMC,UAAU,gBAAAC,EAAA,cAAGT,UAAU,CAAAU,EAAA,GAAAD,EAAA,CAC3B,CAAC;EAAEE,WAAW,GAAG,EAAE;EAAEC,WAAW,GAAG,EAAE;EAAEC,MAAM;EAAEC,MAAM,GAAG;AAAO,CAAC,EAAEC,GAAG,KAAK;EAAAN,EAAA;EACxE,MAAMO,SAAS,GAAGb,MAAM,CAAiB,IAAI,CAAC;EAC9C,MAAMc,YAAY,GAAGd,MAAM,CAAgB,IAAI,CAAC;;EAEhD;EACAF,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,SAAS,CAACE,OAAO,IAAID,YAAY,CAACC,OAAO,EAAE,OAAO,CAAC;;IAExDC,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DC,OAAO,EAAE,CAAC,CAACV,WAAW;MACtBW,UAAU,EAAE,CAAAX,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,MAAM,KAAI,CAAC;MACpCC,OAAO,EAAE,CAAC,CAACZ,WAAW;MACtBa,UAAU,EAAE,CAAAb,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEW,MAAM,KAAI;IACrC,CAAC,CAAC;IAEF,MAAMG,MAAM,GAAGtB,QAAQ,CAACuB,IAAI,CAAC;MAC3BC,SAAS,EAAEZ,SAAS,CAACE,OAAO;MAC5BW,WAAW,EAAE,KAAK;MAAE;MACpBf,MAAM,EAAEgB,MAAM,CAAChB,MAAM,CAAC;MACtBiB,KAAK,EAAE,MAAM;MACbC,cAAc,EAAE,KAAK;MAAE;MACvBC,OAAO,EAAE,CAAC5B,YAAY,CAAC;MACvB6B,WAAW,EAAE;QACX,eAAe,EAAE;UACf;UACA;UACCC,YAAY,EAAE,IAAI;UAAE;UACpBC,WAAW,EAAE,KAAK,CAAE;UACpB;QACH;MACF;MACA;IACF,CAAC,CAAC;IAEFnB,YAAY,CAACC,OAAO,GAAGQ,MAAM;;IAE7B;IACA,IAAIf,WAAW,EAAE;MACdQ,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAET,WAAW,CAAC0B,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;MACxF,IAAI;QACAX,MAAM,CAACY,aAAa,CAAC3B,WAAW,CAAC,CAAC,CAAC;QACnCQ,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAChE,CAAC,CAAC,OAAOmB,CAAC,EAAE;QACRpB,OAAO,CAACqB,KAAK,CAAC,0CAA0C,EAAED,CAAC,CAAC;QAC5D;QACA,IAAI3B,WAAW,EAAE;UACdO,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChEM,MAAM,CAACY,aAAa,CAAC1B,WAAW,CAAC,CAAC,CAAC;UACnCO,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QACnE;MACJ;IACH,CAAC,MAAM,IAAIR,WAAW,EAAE;MACrBO,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAER,WAAW,CAACyB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;MAC5GX,MAAM,CAACY,aAAa,CAAC1B,WAAW,CAAC;MACjCO,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC/D,CAAC,MAAM;MACL;MACAD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzEM,MAAM,CAACY,aAAa,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC;IACJ;;IAGA;IACAZ,MAAM,CAACe,EAAE,CAAC,qBAAqB,EAAE,MAAM;MACrC,IAAI5B,MAAM,IAAIa,MAAM,EAAE;QACnB,IAAI;UACD;UACA,IAAIgB,SAAS,GAAG,EAAE;UAClB,IAAIC,SAAS,GAAG,EAAE;UAElB,IAAI;YACF;YACA,MAAMC,QAAQ,GAAGlB,MAAM,CAACmB,UAAU,CAAC,eAAe,CAAC;YACnD,IAAID,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAAE;cAC9CF,SAAS,GAAGE,QAAQ,CAACE,IAAI,IAAI,EAAE;cAC/BH,SAAS,GAAGC,QAAQ,CAACG,IAAI,IAAI,EAAE;cAC/B5B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;YAClE;UACF,CAAC,CAAC,OAAO4B,MAAM,EAAE;YACd7B,OAAO,CAAC8B,IAAI,CAAC,yDAAyD,EAAED,MAAM,CAAC;UAClF;;UAEA;UACA,IAAI,CAACN,SAAS,IAAI,CAACC,SAAS,EAAE;YAC1BD,SAAS,GAAGhB,MAAM,CAACwB,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACpC,IAAI;cACF;cACAP,SAAS,GAAGjB,MAAM,CAACmB,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE;YACrD,CAAC,CAAC,OAAMM,UAAU,EAAE;cACjBhC,OAAO,CAAC8B,IAAI,CAAC,gCAAgC,EAAEE,UAAU,CAAC;YAC7D;YACA;YACA,IAAI,CAACR,SAAS,EAAE;cACdA,SAAS,GAAGjB,MAAM,CAACwB,OAAO,CAAC;gBAAEE,SAAS,EAAE1B,MAAM,CAAC2B,UAAU,CAAC;cAAE,CAAC,CAAC,IAAI,EAAE;YACtE;YACAlC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACvE;UAEAD,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD;UACA;UACAP,MAAM,CAAC6B,SAAS,EAAEC,SAAS,CAAC;UAC5BxB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QAExD,CAAC,CAAC,OAAOoB,KAAK,EAAE;UACZrB,OAAO,CAACqB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAChE;MACH;IACF,CAAC,CAAC;IAEF,OAAO,MAAM;MACX,IAAIvB,YAAY,CAACC,OAAO,EAAE;QACvB;QACAD,YAAY,CAACC,OAAO,CAACoC,OAAO,CAAC,CAAC;QAC9BrC,YAAY,CAACC,OAAO,GAAG,IAAI;MAC9B;IACF,CAAC;EACH,CAAC,EAAE,CAACP,WAAW,EAAEC,WAAW,EAAEE,MAAM,EAAED,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEhD;EACAX,mBAAmB,CAACa,GAAG,EAAE,OAAO;IAC9BwC,IAAI,EAAE,MAAAA,CAAA,KAAY;MAChB,IAAIC,aAAa,GAAG;QAAEV,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC,CAAC;MAC5C,IAAI9B,YAAY,CAACC,OAAO,EAAE;QACvB,IAAI;UACA;UACA,MAAMuC,MAAM,GAAGxC,YAAY,CAACC,OAAO,CAAC2B,UAAU,CAAC,eAAe,CAAC;UAC/D;UACA,IAAIY,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,MAAM,IAAIA,MAAM,IAAI,MAAM,IAAIA,MAAM,EAAE;YAChFD,aAAa,CAACV,IAAI,GAAGW,MAAM,CAACX,IAAI,IAAI,EAAE;YACtCU,aAAa,CAACT,IAAI,GAAGU,MAAM,CAACV,IAAI,IAAI,EAAE;YACtC5B,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAE;cAAE0B,IAAI,EAAEU,aAAa,CAACV,IAAI,CAACT,SAAS,CAAC,CAAC,EAAC,EAAE,CAAC,GAAC,KAAK;cAAEU,IAAI,EAAES,aAAa,CAACT,IAAI,CAACV,SAAS,CAAC,CAAC,EAAC,EAAE,CAAC,GAAC;YAAM,CAAC,CAAC;UACvK,CAAC,MAAM;YACJlB,OAAO,CAAC8B,IAAI,CAAC,2EAA2E,CAAC;YACzF;YACA,MAAM,IAAIS,KAAK,CAAC,uCAAuC,CAAC;UAC3D;QACJ,CAAC,CAAC,OAAOV,MAAM,EAAE;UACb7B,OAAO,CAAC8B,IAAI,CAAC,8DAA8D,EAAED,MAAM,CAAC;UACpF,IAAI;YACF;YACA,MAAMtB,MAAM,GAAGT,YAAY,CAACC,OAAO;YACnC,MAAMyC,OAAO,GAAGjC,MAAM,CAACwB,OAAO,CAAC,CAAC,IAAI,EAAE;YACtC,MAAMU,aAAa,GAAGlC,MAAM,CAACmB,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE;YAC5D,IAAIc,OAAO,IAAIC,aAAa,EAAE;cAAE;cAC3BJ,aAAa,CAACV,IAAI,GAAGa,OAAO;cAC5BH,aAAa,CAACT,IAAI,GAAGa,aAAa;cAClCzC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;YAClF,CAAC,MAAM;cACFD,OAAO,CAACqB,KAAK,CAAC,yEAAyE,CAAC;YAC7F;UACJ,CAAC,CAAC,OAAOqB,WAAW,EAAE;YACnB1C,OAAO,CAACqB,KAAK,CAAC,6DAA6D,EAAEqB,WAAW,CAAC;UAC5F;QACJ;MACH,CAAC,MAAM;QACL1C,OAAO,CAACqB,KAAK,CAAC,kDAAkD,CAAC;MACnE;;MAEA;MACA,MAAM,IAAIsB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;;MAExD;MACA,IAAI9C,YAAY,CAACC,OAAO,IAAI,CAACsC,aAAa,CAACT,IAAI,CAACkB,IAAI,CAAC,CAAC,EAAE;QAAE;QACtD9C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;QACzE,IAAI;UACC,MAAM8C,sBAAsB,GAAGjD,YAAY,CAACC,OAAO,CAAC2B,UAAU,CAAC,cAAc,CAAC,IAAI5B,YAAY,CAACC,OAAO,CAACgC,OAAO,CAAC;YAAEE,SAAS,EAAEnC,YAAY,CAACC,OAAO,CAACmC,UAAU,CAAC;UAAE,CAAC,CAAC,IAAI,EAAE;UACtK,IAAIa,sBAAsB,CAACD,IAAI,CAAC,CAAC,EAAE;YAC/B9C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzEoC,aAAa,CAACT,IAAI,GAAGmB,sBAAsB;UAC/C,CAAC,MAAM;YACJ/C,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;UAC1E;QACL,CAAC,CAAC,OAAO+C,UAAU,EAAE;UACjBhD,OAAO,CAACqB,KAAK,CAAC,gEAAgE,EAAE2B,UAAU,CAAC;QAC/F;MACJ;;MAEA;MACA,OAAOX,aAAa;IACtB,CAAC;IACAY,SAAS,EAAEA,CAAA,KAAMnD,YAAY,CAACC;EACjC,CAAC,CAAC,CAAC;EAEH,oBAAOX,OAAA;IAAKQ,GAAG,EAAEC,SAAU;IAACqD,KAAK,EAAE;MAAEvD,MAAM,EAAEA;IAAO;EAAE;IAAAwD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3D,CAAC,iCACH,CAAC;;AAED;AAAAC,GAAA,GAvMMlE,UAAU;AAwMhBA,UAAU,CAACmE,WAAW,GAAG,YAAY;AAErC,eAAenE,UAAU;AAAC,IAAAE,EAAA,EAAAgE,GAAA;AAAAE,YAAA,CAAAlE,EAAA;AAAAkE,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}