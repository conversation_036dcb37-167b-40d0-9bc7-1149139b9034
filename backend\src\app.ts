// --- Initialize Email Queue Worker ---
import './queue'; // Import to initialize the queue and start the worker process

import cors from 'cors';
import dotenv from 'dotenv';
import express, {
  NextFunction,
  Request,
  Response,
} from 'express';
import {
  createProxyMiddleware,
} from 'http-proxy-middleware'; // <-- Add proxy import
import mongoose from 'mongoose';

import {
  authenticateJWT,
} from './middleware/auth'; // <-- Add auth middleware import
import routes from './routes';
import authRoutes from './routes/auth.routes';
import awsSnsWebhooks
  from './routes/awsSnsWebhooks'; // <-- Import the SNS webhook router
import blockRoutes from './routes/blockRoutes'; // <-- Add block routes import
import campaignRoutes from './routes/campaign.routes';
import contactRoutes from './routes/contact.routes';
// import dashboardRoutes from './routes/dashboard.routes'; // Temporarily commented out
// import domainRoutes from './routes/domain.routes'; // Temporarily commented out
import imageRoutes from './routes/image.routes';
import integrationRoutes from './routes/integrations/index';
import segmentRoutes from './routes/segments/index'; // Corrected path
// import stripeRoutes from './routes/stripe.routes'; // Temporarily commented out
import supportRoutes from './routes/support.routes';
import templateRoutes
  from './routes/templateRoutes'; // <-- Add template routes import

// Load environment variables VERY FIRST
dotenv.config();

console.log('[App] Email queue worker initialized via import.');
// --- End Queue Worker Init ---

// Create Express app
const app = express();

// --- Add VERY FIRST Logging Middleware ---
app.use((req, res, next) => {
  console.log(`[Entry Point] Request Received: ${req.method} ${req.originalUrl}`);
  next(); // Pass control to the next middleware/route
});
// --- End VERY FIRST Logging Middleware ---

// Middleware
// Explicitly configure CORS options
app.use(cors({
  origin: [
    'http://localhost:3001', // Your current frontend origin
    'http://localhost:5173'  // The origin seen previously (include if needed)
    // Add any other origins for production/staging if necessary
  ],
  credentials: true // Allow cookies/authorization headers
}));

// Add text body parser for SNS webhooks (which send text/plain)
app.use('/webhooks', express.text({ type: 'text/plain' }));

// AWS SNS Webhook routes - Mount BEFORE body parsing middleware
app.use('/webhooks', awsSnsWebhooks);

// Body parsing middleware - Mount AFTER webhooks that need raw body
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// --- Add Request Logging Middleware VERY EARLY ---
app.use((req, res, next) => {
  console.log(`Request Received: ${req.method} ${req.originalUrl}`);
  // Optional: Log headers if needed for debugging (can be verbose)
  // console.log('Request Headers:', JSON.stringify(req.headers));
  next(); // Pass control to the next middleware/route
});
// --- End Request Logging Middleware ---


// Connect to MongoDB
const mongoUri = process.env.MONGODB_URI;
if (!mongoUri) {
  console.error('MONGODB_URI is not defined in environment variables');
  process.exit(1); // Exit if DB connection string is missing
}

mongoose.connect(mongoUri)
  .then(() => {
    console.log('Connected to MongoDB'); // Log successful connection
  })
  .catch((err) => {
    console.error('MongoDB connection error:', err); // Log connection error
    process.exit(1); // Exit if DB connection fails on startup
  });

// --- New Email Generator Routes ---
// Revert back to standard mounting
app.use('/api/templates', authenticateJWT, templateRoutes);
app.use('/api/blocks', authenticateJWT, blockRoutes);

// --- AI Service Proxy ---
const aiServiceUrl = process.env.AI_SERVICE_URL || 'http://localhost:5000';
app.use('/api/ai', authenticateJWT, createProxyMiddleware({
  target: aiServiceUrl,
  changeOrigin: true,
  pathRewrite: {
    '^/api/ai': '/'
  }
}));
// --- End New Routes & Proxy ---

// API routes - Mounted AFTER logging middleware
app.use('/api/v1', routes);
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/campaigns', campaignRoutes);
app.use('/api/v1/contacts', contactRoutes);
// app.use('/api/v1/dashboard', dashboardRoutes); // Temporarily commented out
// app.use('/api/v1/domains', domainRoutes); // Temporarily commented out
app.use('/api/v1/integrations', integrationRoutes);
app.use('/api/v1/images', imageRoutes);
app.use('/api/v1/segments', segmentRoutes); // Using corrected import
// app.use('/api/v1/stripe', stripeRoutes); // Temporarily commented out
app.use('/api/v1/support', supportRoutes);

// Health check route
app.get('/health', (req: Request, res: Response) => {
  console.log('Health check endpoint hit'); // Add log here too
  res.status(200).json({ status: 'ok' });
});

// --- Error handling middleware (Keep this LAST) ---
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('Unhandled Error:', err.stack || err);
  const errorMessage = process.env.NODE_ENV === 'production' ? 'Internal Server Error' : err.message;
  res.status(500).json({
    success: false,
    message: errorMessage,
  });
});
// --- End Error handling middleware ---


// Export app (needed by server.ts)
export default app;

