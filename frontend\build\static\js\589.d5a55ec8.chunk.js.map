{"version": 3, "file": "static/js/589.d5a55ec8.chunk.js", "mappings": "oNAeA,MAudA,EAvd0BA,KAAO,IAADC,EAC9B,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,MACT,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,OAEVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAc,OACvCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAwB,CACxDO,UAAW,EACXC,OAAQ,EACRC,QAAS,EACTC,QAAS,EACTC,aAAc,EACdC,WAAY,EACZC,SAAU,EACVC,UAAW,EACXC,gBAAiB,EACjBC,WAAY,EACZC,gBAAiB,EACjBC,SAAU,GACVC,cAAe,CACbC,QAAS,EACTC,OAAQ,EACRC,OAAQ,GAEVC,gBAAiB,CACf,gBAAiB,EACjB,iBAAkB,EAClB,OAAU,EACV,UAAa,EACb,MAAS,GAEXC,YAAa,CACXC,QAAS,EACTC,UAAW,EACXC,QAAS,EACTC,MAAO,MAGJC,EAAcC,IAAmB9B,EAAAA,EAAAA,WAAS,IAsCjD+B,EAAAA,EAAAA,YAAU,KACR,IAAIC,EAAoC,KAGxC,MAAMC,EAAYC,iBAAkC,IAA3BC,EAAaC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAChCD,IACFjC,GAAW,GACXH,EAAY,MACZO,EAAa,CACXC,UAAW,EAAGC,OAAQ,EAAGC,QAAS,EAAGC,QAAS,EAAGC,aAAc,EAC/DC,WAAY,EAAGC,SAAU,EAAGC,UAAW,EAAGC,gBAAiB,EAC3DC,WAAY,EAAGC,gBAAiB,EAAGC,SAAU,GAC7CC,cAAe,CAAEC,QAAS,EAAGC,OAAQ,EAAGC,OAAQ,GAChDC,gBAAiB,CAAE,gBAAiB,EAAG,iBAAkB,EAAG,OAAU,EAAG,UAAa,EAAG,MAAS,GAClGC,YAAa,CAAEC,QAAS,EAAGC,UAAW,EAAGC,QAAS,EAAGC,MAAO,KAE9DxB,EAAS,KAEXmC,QAAQC,IAAI,uBAAuBL,EAAgB,gBAAkB,2BAA2B3C,KAEhG,IACE,MAAMiD,EAAQC,aAAaC,QAAQ,SACnC,IAAKF,EACH,MAAM,IAAIG,MAAM,kCAIlB,GAAIT,IAAkBrC,EAAU,CAC9B,MACM+C,SADyBC,EAAAA,EAAIC,IAAI,cAAcvD,MACfwD,KACtC,IAAIH,EAAaI,UAAWJ,EAAaG,KAAKlD,SAW5C,OAHAM,EAASyC,EAAaK,SAAW,sBACjChD,GAAW,QACP8B,GAAYmB,cAAcnB,IAT9BjC,EAAY8C,EAAaG,KAAKlD,UAGY,SAAtC+C,EAAaG,KAAKlD,SAASsD,QAA2D,cAAtCP,EAAaG,KAAKlD,SAASsD,QAC5EC,EAAmBZ,EAAOI,EAAaG,KAAKlD,SAQnD,KAA+B,SAApBA,EAASsD,QAAyC,cAApBtD,EAASsD,OAE/CC,EAAmBZ,EAAO3C,GAGtBqC,GAAejC,GAAW,EAGnC,CAAE,MAAOoD,GAAW,IAADC,EAAAC,EACjBjB,QAAQpC,MAAM,iCAAiCgC,EAAgB,UAAY,WAAYmB,GACvF,MAAMG,GAA2B,QAAZF,EAAAD,EAAII,gBAAQ,IAAAH,GAAM,QAANC,EAAZD,EAAcP,YAAI,IAAAQ,OAAN,EAAZA,EAAoBN,UAAWI,EAAIJ,SAAW,2BACnE9C,EAASqD,GACLtB,GAAejC,GAAW,GAC1B8B,GAAYmB,cAAcnB,EAChC,CACF,EAGMqB,EAAqBnB,MAAOO,EAAekB,KAC5C,IACK,MACMC,SAD0Bd,EAAAA,EAAIC,IAAI,uBAAuBvD,MACvBwD,KACxC,GAAIY,EAAcX,SAAWW,EAAcZ,KAAM,CAAC,IAADa,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC/C,MAAMC,EAAmBrB,EAAcZ,KACvCT,QAAQC,IAAI,uEAAwEyC,GACpF3E,EAAa,CACXC,UAAkE,QAAzDsD,EAA4B,QAA5BC,EAAEmB,EAAiB1E,iBAAS,IAAAuD,EAAAA,EAAIH,EAAgBuB,iBAAS,IAAArB,EAAAA,EAAI,EACtErD,OAA4D,QAAtDuD,EAAyB,QAAzBC,EAAEiB,EAAiBzE,cAAM,IAAAwD,EAAAA,EAAIL,EAAgBwB,iBAAS,IAAApB,EAAAA,EAAI,EAChEtD,QAA+D,QAAxDwD,EAA0B,QAA1BC,EAAEe,EAAiBxE,eAAO,IAAAyD,EAAAA,EAAIP,EAAgByB,kBAAU,IAAAnB,EAAAA,EAAI,EACnEvD,QAAgE,QAAzDyD,EAA0B,QAA1BC,EAAEa,EAAiBvE,eAAO,IAAA0D,EAAAA,EAAIT,EAAgB0B,mBAAW,IAAAlB,EAAAA,EAAI,EACpExD,aAA+E,QAAnE0D,EAA+B,QAA/BC,EAAEW,EAAiBtE,oBAAY,IAAA2D,EAAAA,EAAIX,EAAgB2B,wBAAgB,IAAAjB,EAAAA,EAAI,EACnFzD,WAAyE,QAA/D2D,EAA6B,QAA7BC,EAAES,EAAiBrE,kBAAU,IAAA4D,EAAAA,EAAIb,EAAgB4B,sBAAc,IAAAhB,EAAAA,EAAI,EAC7E1D,SAA+D,QAAvD4D,EAA2B,QAA3BC,EAAEO,EAAiBpE,gBAAQ,IAAA6D,EAAAA,EAAIf,EAAgB9C,gBAAQ,IAAA4D,EAAAA,EAAI,EACnE3D,UAAkE,QAAzD6D,EAA4B,QAA5BC,EAAEK,EAAiBnE,iBAAS,IAAA8D,EAAAA,EAAIjB,EAAgB7C,iBAAS,IAAA6D,EAAAA,EAAI,EACtE5D,gBAAiD,QAAlC8D,EAAEI,EAAiBlE,uBAAe,IAAA8D,EAAAA,EAAI,EACrD7D,WAAuC,QAA7B8D,EAAEG,EAAiBjE,kBAAU,IAAA8D,EAAAA,EAAKnB,EAAgB6B,qBAAwB7B,EAAgB0B,YAAc1B,EAAgB6B,qBAAuB,IAAO,EAChKvE,gBAAiD,QAAlC8D,EAAEE,EAAiBhE,uBAAe,IAAA8D,EAAAA,EAAKpB,EAAgB6B,sBAAyD,QAAjCR,EAACrB,EAAgB2B,wBAAgB,IAAAN,EAAAA,EAAI,GAAKrB,EAAgB6B,qBAAuB,IAAO,EACtLtE,SAAU+D,EAAiB/D,UAAY,GACvCC,cAAe8D,EAAiB9D,eAAiB,CAAEC,QAAS,EAAGC,OAAQ,EAAGC,OAAQ,GAClFC,gBAAiB0D,EAAiB1D,iBAAmB,CAAC,EACtDC,YAAayD,EAAiBzD,aAAe,CAAEC,QAAS,EAAGC,UAAW,EAAGC,QAAS,EAAGC,MAAO,IAEhG,CACL,CAAE,MAAO6D,GACJlD,QAAQpC,MAAM,gDAAiDsF,EAEpE,CAAC,QAEOxF,GAASC,GAAW,EAC5B,GAIL+B,GAAU,GAOV,OAHAD,EAAa0D,aAAY,IAAMzD,GAAU,IADpB,KAId,KACDD,IACFO,QAAQC,IAAI,iDAAiDhD,KAC7D2D,cAAcnB,GAChB,CACD,GACA,CAACxC,IAeJ,OAAIS,GAEA0F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+EAKhB9F,EAemB,SAApBA,EAASsD,QAAyC,cAApBtD,EAASsD,QAEvC0C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CACJC,KAAK,UACL9C,QAAQ,+DACR0C,UAAU,UAEZD,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CAACC,QAASA,IAAMtG,EAAS,cAAciG,SAAC,0BAQnDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,wBAAuBC,SAAA,CAAE/F,EAASqG,KAAK,mBACrDR,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAqBC,SAAC,qCAGrCF,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CAACG,QAAQ,YAAYF,QAASA,IAAMtG,EAAS,cAAciG,SAAC,0BAMrEC,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACT,UAAU,OAAMC,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,SAAC,uBACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,aAAiB,IAAE/F,EAASwG,YACvCR,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,UAAc,IAAE/F,EAASyG,SAAS,KAAMzG,EAAS0G,UAAU,QACtEV,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,yBAA6B,IAtE9BY,KAClB,IAAKA,EAAY,MAAO,IAExB,OADa,IAAIC,KAAKD,GACVE,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WACR,EA6DgDC,CAAWnH,EAASoH,aAAepH,EAASqH,WAAa,QACnGrB,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,gBAAoB,IAA+B,QAA9BtG,EAACO,EAAS0F,4BAAoB,IAAAjG,EAAAA,EAAI,kBAMxEuG,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACT,UAAU,OAAMC,SAAA,EACpBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,SAAC,iBAEzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sCAAqCC,SAAA,EAElDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,eACrCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAAmBC,SAAExF,EAAUE,gBAG9CuF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,YACrCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAAmBC,SAAExF,EAAUG,aAG9CsF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,aACrCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAAmBC,SAAExF,EAAUI,cAG9CqF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,aACrCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAAmBC,SAAExF,EAAUK,cAG9CoF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,kBACrCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAAmBC,SAAExF,EAAUM,mBAG9CmF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,gBACrCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAAmBC,SAAExF,EAAUO,uBAMlDkF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EAEzDC,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAR,SAAA,EACHF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,SAAC,sBACxCxF,EAAUa,SAASmB,OAAS,GAC3ByD,EAAAA,EAAAA,MAAAsB,EAAAA,SAAA,CAAAvB,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACtBhE,EAAexB,EAAUa,SAAWb,EAAUa,SAASmG,MAAM,EAAG,IAAIC,KAAI,CAACC,EAAMC,KAC/E1B,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCF,EAAAA,EAAAA,KAAA,KAAG8B,KAAMF,EAAKG,IAAKC,OAAO,SAASC,IAAI,sBAAsBhC,UAAU,gCAAgCiC,MAAON,EAAKG,IAAI7B,SACpH0B,EAAKG,SAGV/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAsCC,UACnDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,UAASC,SAAA,CAAE0B,EAAKO,OAAO,iBAPjCN,OAYbnH,EAAUa,SAASmB,OAAS,IAC3BsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CACLC,QAASA,IAAMpE,GAAiBD,GAAcgE,SAE7ChE,EAAe,YAAc,oBAMtC8D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gBAAeC,SAAC,gCAKjCC,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAR,SAAA,EACHF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,SAAC,qBACzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,aACNC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAExF,EAAUc,cAAcC,QAAQ,KAAG2G,KAAKC,MAAO3H,EAAUc,cAAcC,QAAUf,EAAUG,OAAU,KAAK,SAC1ImF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,gCACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAO3H,EAAUc,cAAcC,QAAUf,EAAUG,OAAU,oBAM7FsF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,YACNC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAExF,EAAUc,cAAcE,OAAO,KAAG0G,KAAKC,MAAO3H,EAAUc,cAAcE,OAAShB,EAAUG,OAAU,KAAK,SACxImF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,+BACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAO3H,EAAUc,cAAcE,OAAShB,EAAUG,OAAU,oBAM5FsF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,YACNC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAExF,EAAUc,cAAcG,OAAO,KAAGyG,KAAKC,MAAO3H,EAAUc,cAAcG,OAASjB,EAAUG,OAAU,KAAK,SACxImF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAO3H,EAAUc,cAAcG,OAASjB,EAAUG,OAAU,6BAUlGsF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAR,SAAA,EACHF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,SAAC,uBACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBsC,OAAOC,QAAQ/H,EAAUkB,iBAAiB+F,KAAIe,IAAA,IAAEC,EAAUC,GAAqBF,EAAA,OAC9EvC,EAAAA,EAAAA,MAAA,OAAoBF,UAAU,oCAAmCC,SAAA,EAC/DF,EAAAA,EAAAA,KAAA,QAAAE,SAAOyC,KACPxC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAE0C,EAAM,KAAGR,KAAKC,MAAOO,EAAQlI,EAAUG,OAAU,KAAK,SACtFmF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAOO,EAAQlI,EAAUG,OAAU,kBAPzD8H,EAWJ,UAMZxC,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAAAR,SAAA,EACHF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,SAAC,mBACzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,wBACNC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAExF,EAAUmB,YAAYC,QAAQ,KAAGsG,KAAKC,MAAO3H,EAAUmB,YAAYC,QAAUpB,EAAUG,OAAU,KAAK,SACtImF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAO3H,EAAUmB,YAAYC,QAAUpB,EAAUG,OAAU,oBAM3FsF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,0BACNC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAExF,EAAUmB,YAAYE,UAAU,KAAGqG,KAAKC,MAAO3H,EAAUmB,YAAYE,UAAYrB,EAAUG,OAAU,KAAK,SAC1ImF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAO3H,EAAUmB,YAAYE,UAAYrB,EAAUG,OAAU,oBAM7FsF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,wBACNC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAExF,EAAUmB,YAAYG,QAAQ,KAAGoG,KAAKC,MAAO3H,EAAUmB,YAAYG,QAAUtB,EAAUG,OAAU,KAAK,SACtImF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,8BACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAO3H,EAAUmB,YAAYG,QAAUtB,EAAUG,OAAU,oBAM3FsF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,sBACNC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,eAAcC,SAAA,CAAExF,EAAUmB,YAAYI,MAAM,KAAGmG,KAAKC,MAAO3H,EAAUmB,YAAYI,MAAQvB,EAAUG,OAAU,KAAK,SAClImF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDF,EAAAA,EAAAA,KAAA,OACEC,UAAU,iCACVqC,MAAO,CAAEC,MAAO,GAAGH,KAAKC,MAAO3H,EAAUmB,YAAYI,MAAQvB,EAAUG,OAAU,gCAvP/FsF,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CACJC,KAAK,QACL9C,QAAQ,qBACR0C,UAAU,UAEZD,EAAAA,EAAAA,KAACM,EAAAA,EAAM,CAACC,QAASA,IAAMtG,EAAS,cAAciG,SAAC,wBAyP7C,C", "sources": ["pages/campaigns/CampaignAnalytics.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\nimport api from 'services/api';\n\nconst CampaignAnalytics = () => {\n  const { id } = useParams<{ id: string }>();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  const [campaign, setCampaign] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [analytics, setAnalytics] = useState<AnalyticsData>({\n    delivered: 0,\n    opened: 0,\n    clicked: 0,\n    bounced: 0,\n    unsubscribed: 0,\n    complaints: 0,\n    openRate: 0,\n    clickRate: 0,\n    clickToOpenRate: 0,\n    bounceRate: 0,\n    unsubscribeRate: 0,\n    topLinks: [],\n    opensByDevice: {\n      desktop: 0,\n      mobile: 0,\n      tablet: 0\n    },\n    opensByLocation: {\n      'United States': 0,\n      'United Kingdom': 0,\n      'Canada': 0,\n      'Australia': 0,\n      'Other': 0\n    },\n    opensByTime: {\n      morning: 0,\n      afternoon: 0,\n      evening: 0,\n      night: 0\n    }\n  });\n  const [showAllLinks, setShowAllLinks] = useState(false);\n\n  // Define types for analytics data\n  interface LinkData {\n    url: string;\n    clicks: number;\n  }\n\n  interface AnalyticsData {\n    delivered: number;\n    opened: number;\n    clicked: number;\n    bounced: number;\n    unsubscribed: number;\n    complaints: number;\n    openRate: number;\n    clickRate: number;\n    clickToOpenRate: number;\n    bounceRate: number;\n    unsubscribeRate: number;\n    topLinks: LinkData[];\n    opensByDevice: {\n      desktop: number;\n      mobile: number;\n      tablet: number;\n    };\n    opensByLocation: {\n      [key: string]: number;\n    };\n    opensByTime: {\n      morning: number;\n      afternoon: number;\n      evening: number;\n      night: number;\n    };\n  }\n\n  // Fetch campaign on component mount and set up polling\n  useEffect(() => {\n    let intervalId: NodeJS.Timeout | null = null;\n\n    // Define the fetching function\n    const fetchData = async (isInitialLoad = false) => {\n      if (isInitialLoad) {\n        setLoading(true);\n        setCampaign(null);\n        setAnalytics({\n          delivered: 0, opened: 0, clicked: 0, bounced: 0, unsubscribed: 0,\n          complaints: 0, openRate: 0, clickRate: 0, clickToOpenRate: 0,\n          bounceRate: 0, unsubscribeRate: 0, topLinks: [], \n          opensByDevice: { desktop: 0, mobile: 0, tablet: 0 },\n          opensByLocation: { 'United States': 0, 'United Kingdom': 0, 'Canada': 0, 'Australia': 0, 'Other': 0 },\n          opensByTime: { morning: 0, afternoon: 0, evening: 0, night: 0 }\n        });\n        setError('');\n      }\n      console.log(`[CampaignAnalytics] ${isInitialLoad ? 'Initial fetch' : 'Polling fetch'} for ID: ${id}`);\n\n      try {\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Authentication token not found');\n        }\n\n        // Fetch campaign data only on initial load or if campaign state is null\n        if (isInitialLoad || !campaign) { \n          const campaignResponse = await api.get(`/campaigns/${id}`);\n          const campaignData = campaignResponse.data;\n          if (campaignData.success && campaignData.data.campaign) {\n            setCampaign(campaignData.data.campaign);\n            // Fetch analytics immediately after setting campaign for the first time\n            // Or if the campaign status allows for analytics\n            if (campaignData.data.campaign.status === 'sent' || campaignData.data.campaign.status === 'completed') {\n               fetchAnalyticsData(token, campaignData.data.campaign); // Pass campaign data\n            }\n          } else {\n            setError(campaignData.message || 'Campaign not found');\n            setLoading(false);\n            if (intervalId) clearInterval(intervalId); // Stop polling if campaign not found\n            return; // Stop fetch process\n          }\n        } else if (campaign.status === 'sent' || campaign.status === 'completed') {\n           // If not initial load, just fetch analytics if campaign exists and status allows\n           fetchAnalyticsData(token, campaign); // Pass existing campaign state\n        } else {\n           // If campaign exists but status is not sent/completed, stop loading\n           if (isInitialLoad) setLoading(false);\n        }\n\n      } catch (err: any) {\n        console.error(`Error fetching campaign data (${isInitialLoad ? 'initial' : 'poll'}):`, err);\n        const errorMessage = err.response?.data?.message || err.message || 'Failed to fetch campaign';\n        setError(errorMessage);\n        if (isInitialLoad) setLoading(false);\n        if (intervalId) clearInterval(intervalId); // Stop polling on error\n      }\n    };\n    \n    // Separate function to fetch just the analytics part\n    const fetchAnalyticsData = async (token: string, currentCampaign: any) => {\n         try {\n              const analyticsResponse = await api.get(`/analytics/campaign/${id}`);\n              const analyticsData = analyticsResponse.data;\n              if (analyticsData.success && analyticsData.data) {\n                const backendAnalytics = analyticsData.data;\n                console.log(\"[CampaignAnalytics] Received analytics data object for setAnalytics:\", backendAnalytics);\n                setAnalytics({\n                  delivered: backendAnalytics.delivered ?? currentCampaign.sentCount ?? 0,\n                  opened: backendAnalytics.opened ?? currentCampaign.openCount ?? 0,\n                  clicked: backendAnalytics.clicked ?? currentCampaign.clickCount ?? 0,\n                  bounced: backendAnalytics.bounced ?? currentCampaign.bounceCount ?? 0,\n                  unsubscribed: backendAnalytics.unsubscribed ?? currentCampaign.unsubscribeCount ?? 0,\n                  complaints: backendAnalytics.complaints ?? currentCampaign.complaintCount ?? 0,\n                  openRate: backendAnalytics.openRate ?? currentCampaign.openRate ?? 0,\n                  clickRate: backendAnalytics.clickRate ?? currentCampaign.clickRate ?? 0,\n                  clickToOpenRate: backendAnalytics.clickToOpenRate ?? 0,\n                  bounceRate: backendAnalytics.bounceRate ?? (currentCampaign.recipientCountActual ? (currentCampaign.bounceCount / currentCampaign.recipientCountActual * 100) : 0),\n                  unsubscribeRate: backendAnalytics.unsubscribeRate ?? (currentCampaign.recipientCountActual ? ((currentCampaign.unsubscribeCount ?? 0) / currentCampaign.recipientCountActual * 100) : 0),\n                  topLinks: backendAnalytics.topLinks || [],\n                  opensByDevice: backendAnalytics.opensByDevice || { desktop: 0, mobile: 0, tablet: 0 }, // Simplified fallback\n                  opensByLocation: backendAnalytics.opensByLocation || {}, // Simplified fallback\n                  opensByTime: backendAnalytics.opensByTime || { morning: 0, afternoon: 0, evening: 0, night: 0 } // Simplified fallback\n                });\n              }\n         } catch (analyticsError) {\n              console.error('Error fetching analytics data (poll/initial):', analyticsError);\n              // Optionally set an error state specific to analytics polling\n         } finally {\n             // Only stop initial loading indicator after first full fetch attempt\n             if (loading) setLoading(false);\n         }\n    };\n\n    // Initial fetch\n    fetchData(true);\n\n    // Set up polling interval (5 minutes)\n    const pollInterval = 5 * 60 * 1000; \n    intervalId = setInterval(() => fetchData(false), pollInterval);\n\n    // Cleanup function\n    return () => {\n      if (intervalId) {\n        console.log(`[CampaignAnalytics] Clearing interval for ID: ${id}`);\n        clearInterval(intervalId);\n      }\n    };\n  }, [id]); // Re-run effect only when campaign ID changes\n\n  // Format date for display\n  const formatDate = (dateString: string) => {\n    if (!dateString) return '-';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  if (!campaign) {\n    return (\n      <div>\n        <Alert\n          type=\"error\"\n          message=\"Campaign not found\"\n          className=\"mb-6\"\n        />\n        <Button onClick={() => navigate('/campaigns')}>\n          Back to Campaigns\n        </Button>\n      </div>\n    );\n  }\n\n  if (campaign.status !== 'sent' && campaign.status !== 'completed') {\n    return (\n      <div>\n        <Alert\n          type=\"warning\"\n          message=\"Analytics are only available for sent or completed campaigns\"\n          className=\"mb-6\"\n        />\n        <Button onClick={() => navigate('/campaigns')}>\n          Back to Campaigns\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-xl font-semibold\">{campaign.name} - Analytics</h2>\n          <p className=\"text-text-secondary\">Campaign performance metrics</p>\n        </div>\n\n        <Button variant=\"secondary\" onClick={() => navigate('/campaigns')}>\n          Back to Campaigns\n        </Button>\n      </div>\n\n      {/* Campaign Overview */}\n      <Card className=\"mb-6\">\n        <h3 className=\"text-lg font-medium mb-4\">Campaign Overview</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <p><strong>Subject:</strong> {campaign.subject}</p>\n            <p><strong>From:</strong> {campaign.fromName} &lt;{campaign.fromEmail}&gt;</p>\n            <p><strong>Sent/Completed Date:</strong> {formatDate(campaign.completedAt || campaign.startedAt || '')}</p>\n            <p><strong>Recipients:</strong> {campaign.recipientCountActual ?? 'N/A'}</p>\n          </div>\n        </div>\n      </Card>\n\n      {/* Key Metrics */}\n      <Card className=\"mb-6\">\n        <h3 className=\"text-lg font-medium mb-4\">Key Metrics</h3>\n        {/* Use Flexbox for horizontal layout, allow wrapping */}\n        <div className=\"flex flex-wrap gap-4 justify-around\">\n          {/* Adjusted padding and text size for smaller boxes */}\n          <div className=\"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]\">\n            <p className=\"text-xs text-gray-400\">Delivered</p>\n            <p className=\"text-xl font-bold\">{analytics.delivered}</p>\n          </div>\n\n          <div className=\"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]\">\n            <p className=\"text-xs text-gray-400\">Opened</p>\n            <p className=\"text-xl font-bold\">{analytics.opened}</p>\n          </div>\n\n          <div className=\"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]\">\n            <p className=\"text-xs text-gray-400\">Clicked</p>\n            <p className=\"text-xl font-bold\">{analytics.clicked}</p>\n          </div>\n\n          <div className=\"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]\">\n            <p className=\"text-xs text-gray-400\">Bounced</p>\n            <p className=\"text-xl font-bold\">{analytics.bounced}</p>\n          </div>\n\n          <div className=\"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]\">\n            <p className=\"text-xs text-gray-400\">Unsubscribed</p>\n            <p className=\"text-xl font-bold\">{analytics.unsubscribed}</p>\n          </div>\n\n          <div className=\"bg-gray-800 p-3 rounded-md text-center flex-1 min-w-[100px]\">\n            <p className=\"text-xs text-gray-400\">Complaints</p>\n            <p className=\"text-xl font-bold\">{analytics.complaints}</p>\n          </div>\n        </div>\n      </Card>\n\n      {/* Engagement Breakdown */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n        {/* Top Clicked Links */}\n        <Card>\n          <h3 className=\"text-lg font-medium mb-4\">Top Clicked Links</h3>\n          {analytics.topLinks.length > 0 ? (\n            <>\n              <div className=\"space-y-3\">\n                {(showAllLinks ? analytics.topLinks : analytics.topLinks.slice(0, 5)).map((link, index) => (\n                  <div key={index} className=\"flex justify-between items-center\">\n                    <div className=\"truncate max-w-xs\">\n                      <a href={link.url} target=\"_blank\" rel=\"noopener noreferrer\" className=\"text-blue-400 hover:underline\" title={link.url}>\n                        {link.url}\n                      </a>\n                    </div>\n                    <div className=\"flex items-center ml-2 flex-shrink-0\">\n                      <span className=\"text-sm\">{link.clicks} clicks</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n              {analytics.topLinks.length > 5 && (\n                <div className=\"mt-4 text-center\">\n                  <Button \n                    onClick={() => setShowAllLinks(!showAllLinks)}\n                  >\n                    {showAllLinks ? 'View Less' : 'View More'}\n                  </Button>\n                </div>\n              )}\n            </>\n          ) : (\n            <p className=\"text-gray-400\">No link clicks recorded</p>\n          )}\n        </Card>\n\n        {/* Opens by Device */}\n        <Card>\n          <h3 className=\"text-lg font-medium mb-4\">Opens by Device</h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between items-center\">\n              <span>Desktop</span>\n              <div className=\"flex items-center\">\n                <span className=\"text-sm mr-2\">{analytics.opensByDevice.desktop} ({Math.floor((analytics.opensByDevice.desktop / analytics.opened) * 100)}%)</span>\n                <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-green-500 h-2 rounded-full\"\n                    style={{ width: `${Math.floor((analytics.opensByDevice.desktop / analytics.opened) * 100)}%` }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span>Mobile</span>\n              <div className=\"flex items-center\">\n                <span className=\"text-sm mr-2\">{analytics.opensByDevice.mobile} ({Math.floor((analytics.opensByDevice.mobile / analytics.opened) * 100)}%)</span>\n                <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-blue-500 h-2 rounded-full\"\n                    style={{ width: `${Math.floor((analytics.opensByDevice.mobile / analytics.opened) * 100)}%` }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span>Tablet</span>\n              <div className=\"flex items-center\">\n                <span className=\"text-sm mr-2\">{analytics.opensByDevice.tablet} ({Math.floor((analytics.opensByDevice.tablet / analytics.opened) * 100)}%)</span>\n                <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-purple-500 h-2 rounded-full\"\n                    style={{ width: `${Math.floor((analytics.opensByDevice.tablet / analytics.opened) * 100)}%` }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      {/* Additional Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Opens by Location */}\n        <Card>\n          <h3 className=\"text-lg font-medium mb-4\">Opens by Location</h3>\n          <div className=\"space-y-3\">\n            {Object.entries(analytics.opensByLocation).map(([location, count]: [string, any]) => (\n              <div key={location} className=\"flex justify-between items-center\">\n                <span>{location}</span>\n                <div className=\"flex items-center\">\n                  <span className=\"text-sm mr-2\">{count} ({Math.floor((count / analytics.opened) * 100)}%)</span>\n                  <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                    <div\n                      className=\"bg-yellow-500 h-2 rounded-full\"\n                      style={{ width: `${Math.floor((count / analytics.opened) * 100)}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </Card>\n\n        {/* Opens by Time */}\n        <Card>\n          <h3 className=\"text-lg font-medium mb-4\">Opens by Time</h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between items-center\">\n              <span>Morning (6am-12pm)</span>\n              <div className=\"flex items-center\">\n                <span className=\"text-sm mr-2\">{analytics.opensByTime.morning} ({Math.floor((analytics.opensByTime.morning / analytics.opened) * 100)}%)</span>\n                <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-yellow-400 h-2 rounded-full\"\n                    style={{ width: `${Math.floor((analytics.opensByTime.morning / analytics.opened) * 100)}%` }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span>Afternoon (12pm-6pm)</span>\n              <div className=\"flex items-center\">\n                <span className=\"text-sm mr-2\">{analytics.opensByTime.afternoon} ({Math.floor((analytics.opensByTime.afternoon / analytics.opened) * 100)}%)</span>\n                <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-orange-500 h-2 rounded-full\"\n                    style={{ width: `${Math.floor((analytics.opensByTime.afternoon / analytics.opened) * 100)}%` }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span>Evening (6pm-12am)</span>\n              <div className=\"flex items-center\">\n                <span className=\"text-sm mr-2\">{analytics.opensByTime.evening} ({Math.floor((analytics.opensByTime.evening / analytics.opened) * 100)}%)</span>\n                <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-red-500 h-2 rounded-full\"\n                    style={{ width: `${Math.floor((analytics.opensByTime.evening / analytics.opened) * 100)}%` }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span>Night (12am-6am)</span>\n              <div className=\"flex items-center\">\n                <span className=\"text-sm mr-2\">{analytics.opensByTime.night} ({Math.floor((analytics.opensByTime.night / analytics.opened) * 100)}%)</span>\n                <div className=\"w-24 bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-indigo-500 h-2 rounded-full\"\n                    style={{ width: `${Math.floor((analytics.opensByTime.night / analytics.opened) * 100)}%` }}\n                  ></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default CampaignAnalytics;\n"], "names": ["CampaignAnalytics", "_campaign$recipientCo", "id", "useParams", "user", "useAuth", "navigate", "useNavigate", "campaign", "setCampaign", "useState", "loading", "setLoading", "error", "setError", "analytics", "setAnalytics", "delivered", "opened", "clicked", "bounced", "unsubscribed", "complaints", "openRate", "clickRate", "clickToOpenRate", "bounceRate", "unsubscribeRate", "topLinks", "opensByDevice", "desktop", "mobile", "tablet", "opensByLocation", "opensByTime", "morning", "afternoon", "evening", "night", "showAllLinks", "setShowAllLinks", "useEffect", "intervalId", "fetchData", "async", "isInitialLoad", "arguments", "length", "undefined", "console", "log", "token", "localStorage", "getItem", "Error", "campaignData", "api", "get", "data", "success", "message", "clearInterval", "status", "fetchAnalyticsData", "err", "_err$response", "_err$response$data", "errorMessage", "response", "currentCampaign", "analyticsData", "_ref", "_backendAnalytics$del", "_ref2", "_backendAnalytics$ope", "_ref3", "_backendAnalytics$cli", "_ref4", "_backendAnalytics$bou", "_ref5", "_backendAnalytics$uns", "_ref6", "_backendAnalytics$com", "_ref7", "_backendAnalytics$ope2", "_ref8", "_backendAnalytics$cli2", "_backendAnalytics$cli3", "_backendAnalytics$bou2", "_backendAnalytics$uns2", "_currentCampaign$unsu", "backendAnalytics", "sentCount", "openCount", "clickCount", "bounceCount", "unsubscribeCount", "complaintCount", "recipientCountActual", "analyticsError", "setInterval", "_jsx", "className", "children", "_jsxs", "<PERSON><PERSON>", "type", "<PERSON><PERSON>", "onClick", "name", "variant", "Card", "subject", "fromName", "fromEmail", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "formatDate", "completedAt", "startedAt", "_Fragment", "slice", "map", "link", "index", "href", "url", "target", "rel", "title", "clicks", "Math", "floor", "style", "width", "Object", "entries", "_ref9", "location", "count"], "sourceRoot": ""}