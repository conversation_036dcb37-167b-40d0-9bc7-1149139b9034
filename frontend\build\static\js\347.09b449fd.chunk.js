"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[347],{6728:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var a=s(5043),n=s(552),r=s(579);const l=["UTC","America/New_York","America/Chicago","America/Denver","America/Los_Angeles","Europe/London","Europe/Paris"],i=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],d=()=>{var e,t;const[s,d]=(0,a.useState)(!0),[c,o]=(0,a.useState)(!1),[m,u]=(0,a.useState)(null),[x,h]=(0,a.useState)([]),[p,y]=(0,a.useState)(null),[g,j]=(0,a.useState)(!1),[b,v]=(0,a.useState)({name:"",description:"",emailTemplateId:"",segmentId:"",scheduleType:"one-time",sendDate:"",sendTime:"09:00",timezone:"UTC",recurringType:"daily",recurringDays:[],recurringMonthDay:"1",endDate:""}),f=[{id:"template1",name:"Welcome Template"},{id:"template2",name:"Newsletter Template"}],N=[{id:"segment1",name:"New Subscribers"},{id:"segment2",name:"Active Users"}];(0,a.useEffect)((()=>{(async()=>{d(!0),u(null);try{await new Promise((e=>setTimeout(e,1e3)));const e=[{id:"sch_1",name:"Welcome Email Send",description:"Sends welcome email daily",type:"recurring",schedule:{frequency:"daily",time:"09:00",startDate:"2024-01-01"},emailTemplateId:"template1",segmentId:"segment1",status:"active",nextRun:new Date(Date.now()+864e5).toISOString(),createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()},{id:"sch_2",name:"Newsletter Blast",description:"Sends monthly newsletter",type:"recurring",schedule:{frequency:"monthly",time:"10:00",startDate:"2024-01-15",recurringMonthDay:"15"},emailTemplateId:"template2",segmentId:"segment2",status:"paused",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()}];h(e)}catch(e){u(e.message||"An error occurred while fetching schedules")}finally{d(!1)}})()}),[]);const D=e=>{const{name:t,value:s}=e.target;v((e=>({...e,[t]:s})))},w=e=>{if(!e)return"N/A";try{const t={year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric"};return new Date(e).toLocaleDateString(void 0,t)}catch(t){return e}};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"flex justify-between items-center mb-6",children:m&&(0,r.jsx)(n.Fc,{type:"error",message:m,className:"mb-4",onClose:()=>u(null)})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)(n.Zp,{className:"bg-secondary-bg border border-border-color",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4 p-4 border-b border-border-color",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-text-primary",children:"Schedules"}),(0,r.jsx)(n.$n,{onClick:()=>{j(!g),y(null)},variant:"primary",size:"sm",children:g?"Cancel":"Create New"})]}),(0,r.jsx)("div",{className:"p-4 space-y-3 max-h-[60vh] overflow-y-auto pr-2",children:s&&0===x.length?(0,r.jsxs)("div",{className:"flex justify-center items-center h-32 text-text-secondary",children:[(0,r.jsxs)("svg",{className:"animate-spin h-6 w-6 mr-2",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading schedules..."]}):0===x.length?(0,r.jsx)("p",{className:"text-sm text-text-secondary text-center py-4",children:"No schedules found. Create your first schedule."}):x.map((e=>{(e=>{switch(null===e||void 0===e?void 0:e.toLowerCase()){case"active":case"scheduled":return{variant:"success",text:"Active"};case"paused":return{variant:"warning",text:"Paused"};case"completed":case"sent":return{variant:"info",text:"Completed"};case"draft":return{variant:"secondary",text:"Draft"};case"error":return{variant:"danger",text:"Error"};default:;}})(e.status);return(0,r.jsxs)("div",{className:"border rounded-md p-3 cursor-pointer transition-colors hover:border-accent-blue-dark "+((null===p||void 0===p?void 0:p.id)===e.id?"border-accent-blue bg-accent-blue-muted":"border-border-color hover:bg-secondary-bg-hover"),onClick:()=>{y(e),j(!1)},children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-1",children:[(0,r.jsx)("span",{className:"font-medium text-text-primary truncate pr-2",children:e.name}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full "+("active"===e.status?"bg-green-100 text-green-800":"paused"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:e.status})]}),(0,r.jsx)("p",{className:"text-xs text-text-secondary truncate",children:e.description||"No description"}),(0,r.jsxs)("p",{className:"text-xs text-text-secondary mt-1",children:["Next Run: ",w(e.nextRun)]})]},e.id)}))})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:g||null===p?(0,r.jsx)(n.Zp,{title:g?"Create New Schedule":"Select a schedule to view details",children:g?(0,r.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),b.name&&b.emailTemplateId&&b.sendDate){o(!0),u(null);try{const e={...b,schedule:{frequency:"recurring"===b.scheduleType?b.recurringType||"daily":"one-time",startDate:b.sendDate,time:b.sendTime,days:"weekly"===b.recurringType?b.recurringDays:void 0,recurringMonthDay:"monthly"===b.recurringType?parseInt(b.recurringMonthDay||"1"):void 0,endDate:b.endDate||void 0},recurringType:void 0,recurringDays:void 0,recurringMonthDay:void 0};await new Promise((e=>setTimeout(e,1e3)));const t={...e,type:b.scheduleType,id:`sch_${Date.now()}`,status:"active",createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),nextRun:new Date(Date.now()+36e5).toISOString()};h((e=>[...e,t])),j(!1),v({name:"",description:"",emailTemplateId:"",segmentId:"",scheduleType:"one-time",sendDate:"",sendTime:"09:00",timezone:"UTC",recurringType:"daily",recurringDays:[],recurringMonthDay:"1",endDate:""}),y(t)}catch(t){u(t.message||"An error occurred while creating schedule")}finally{o(!1)}}else u("Please fill in Name, Template, and Send Date")},className:"space-y-4 p-4",children:[(0,r.jsx)(n.pd,{label:"Schedule Name",id:"name",name:"name",value:b.name,onChange:D,required:!0,placeholder:"e.g., Weekly Newsletter Send"}),(0,r.jsx)(n.pd,{label:"Description (Optional)",id:"description",name:"description",value:b.description,onChange:D,placeholder:"Briefly describe this schedule's purpose"}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"emailTemplateId",className:"form-label",children:"Email Template *"}),(0,r.jsxs)("select",{id:"emailTemplateId",name:"emailTemplateId",value:b.emailTemplateId,onChange:D,required:!0,className:"form-select w-full mt-1",children:[(0,r.jsx)("option",{value:"",disabled:!0,children:"Select Template..."}),f.map((e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id)))]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"segmentId",className:"form-label",children:"Target Segment (Optional)"}),(0,r.jsxs)("select",{id:"segmentId",name:"segmentId",value:b.segmentId||"",onChange:D,className:"form-select w-full mt-1",children:[(0,r.jsx)("option",{value:"",children:"All Contacts"}),N.map((e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id)))]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"scheduleType",className:"form-label",children:"Schedule Type"}),(0,r.jsxs)("select",{id:"scheduleType",name:"scheduleType",value:b.scheduleType,onChange:D,className:"form-select w-full mt-1",children:[(0,r.jsx)("option",{value:"one-time",children:"One-Time"}),(0,r.jsx)("option",{value:"recurring",children:"Recurring"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(n.pd,{label:"one-time"===b.scheduleType?"Send Date":"Start Date",id:"sendDate",name:"sendDate",type:"date",value:b.sendDate,onChange:D,required:!0}),(0,r.jsx)(n.pd,{label:"Send Time",id:"sendTime",name:"sendTime",type:"time",value:b.sendTime,onChange:D,required:!0})]}),"recurring"===b.scheduleType&&(0,r.jsxs)("div",{className:"space-y-4 p-4 border border-border-color rounded-md bg-primary-bg",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"recurringType",className:"form-label",children:"Frequency"}),(0,r.jsxs)("select",{id:"recurringType",name:"recurringType",value:b.recurringType||"",onChange:D,className:"form-select w-full mt-1",children:[(0,r.jsx)("option",{value:"",disabled:!0,children:"Select Frequency..."}),(0,r.jsx)("option",{value:"daily",children:"Daily"}),(0,r.jsx)("option",{value:"weekly",children:"Weekly"}),(0,r.jsx)("option",{value:"monthly",children:"Monthly"})]})]}),"weekly"===b.recurringType&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-text-secondary mb-1",children:"Days of Week"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:i.map((e=>(0,r.jsx)(n.$n,{type:"button",variant:(b.recurringDays||[]).includes(e)?"primary":"secondary",size:"sm",onClick:()=>(e=>{const t=b.recurringDays||[],s=t.includes(e)?t.filter((t=>t!==e)):[...t,e];v((e=>({...e,recurringDays:s})))})(e),children:e.substring(0,3)},e)))})]}),"monthly"===b.recurringType&&(0,r.jsx)(n.pd,{label:"Day of Month",id:"recurringMonthDay",name:"recurringMonthDay",type:"number",value:b.recurringMonthDay||"",onChange:D}),(0,r.jsx)(n.pd,{label:"End Date (Optional)",id:"endDate",name:"endDate",type:"date",value:b.endDate||"",onChange:D})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{htmlFor:"timezone",className:"form-label",children:"Timezone"}),(0,r.jsx)("select",{id:"timezone",name:"timezone",value:b.timezone,onChange:D,className:"form-select w-full mt-1",children:l.map((e=>(0,r.jsx)("option",{value:e,children:e},e)))})]}),(0,r.jsxs)("div",{className:"flex justify-end pt-4 border-t border-border-color",children:[(0,r.jsx)(n.$n,{type:"button",variant:"secondary",onClick:()=>j(!1),className:"mr-3",children:"Cancel"}),(0,r.jsx)(n.$n,{type:"submit",variant:"primary",disabled:c,children:c?"Creating...":"Create Schedule"})]})]}):(0,r.jsx)("div",{className:"p-4 text-center text-text-secondary",children:s?"Loading schedules...":"Select a schedule from the list on the left or create a new one."})}):p&&(0,r.jsxs)(n.Zp,{title:`Schedule: ${p.name}`,children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-4 border-b border-border-color",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-text-primary truncate pr-4",children:p.name}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)(n.$n,{variant:"danger",size:"sm",onClick:()=>(async e=>{if(!window.confirm("Are you sure you want to delete this schedule?"))return;const t=[...x];h((t=>t.filter((t=>t.id!==e)))),(null===p||void 0===p?void 0:p.id)===e&&y(null);try{await new Promise((e=>setTimeout(e,500)))}catch(s){if(u(s.message||"An error occurred while deleting schedule"),h(t),(null===p||void 0===p?void 0:p.id)===e){const s=t.find((t=>t.id===e));y(s||null)}}})(p.id),children:"Delete"})})]}),(0,r.jsxs)("div",{className:"p-6 space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Status: "}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-semibold rounded-full "+("active"===p.status?"bg-green-100 text-green-800":"paused"===p.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:p.status})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Description:"})," ",p.description||"N/A"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Type:"})," ",p.type]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Template:"})," ",(null===(e=f.find((e=>e.id===p.emailTemplateId)))||void 0===e?void 0:e.name)||p.emailTemplateId]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Segment:"})," ",(null===(t=N.find((e=>e.id===p.segmentId)))||void 0===t?void 0:t.name)||"All Contacts"]}),(0,r.jsxs)("div",{className:"pt-3 mt-3 border-t border-border-color",children:[(0,r.jsx)("h3",{className:"text-md font-semibold text-text-primary mb-2",children:"Schedule Details"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Frequency:"})," ",p.schedule.frequency]}),p.schedule.time&&(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Time:"})," ",p.schedule.time]}),p.schedule.days&&p.schedule.days.length>0&&(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Days:"})," ",p.schedule.days.join(", ")]}),p.schedule.recurringMonthDay&&(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Day of Month:"})," ",p.schedule.recurringMonthDay]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Start Date:"})," ",w(p.schedule.startDate)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"End Date:"})," ",w(p.schedule.endDate)]})]}),(0,r.jsxs)("div",{className:"pt-3 mt-3 border-t border-border-color",children:[(0,r.jsx)("h3",{className:"text-md font-semibold text-text-primary mb-2",children:"Run Info"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Last Run:"})," ",w(p.lastRun)]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"text-sm font-medium text-text-secondary",children:"Next Run:"})," ",w(p.nextRun)]})]}),(0,r.jsxs)("p",{className:"text-xs text-text-secondary pt-3 mt-3 border-t border-border-color",children:["Created: ",w(p.createdAt)," | Updated: ",w(p.updatedAt)]})]})]})})]})]})}}}]);
//# sourceMappingURL=347.09b449fd.chunk.js.map