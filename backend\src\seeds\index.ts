import dotenv from 'dotenv';

import { seedBlocks } from './blockSeeder';
import seedIntegrations from './integrationSeeder';
import { seedTemplates } from './templateSeeder';

// Load environment variables
dotenv.config();

/**
 * Run all seeders in sequence
 */
const runAllSeeders = async () => {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Run all seeders in sequence
    await seedIntegrations();
    await seedBlocks();
    await seedTemplates();
    
    console.log('✅ All database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during seeding process:', error);
    process.exit(1);
  }
};

// Run if executed directly
if (require.main === module) {
  runAllSeeders();
}

export default runAllSeeders; 