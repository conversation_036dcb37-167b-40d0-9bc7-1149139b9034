{"version": 3, "file": "static/js/17.9fb730f9.chunk.js", "mappings": "+OAuCA,MAkeA,EAle+BA,KAAO,IAADC,EAAAC,EAAAC,EACnC,MAAM,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,OAEVC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAqB,KAChDC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAC5BK,EAASC,IAAcN,EAAAA,EAAAA,UAA0B,KACjDO,EAAWC,IAAgBR,EAAAA,EAAAA,WAAS,IACpCS,EAAkBC,IAAuBV,EAAAA,EAAAA,UAAwB,OACjEW,EAAgBC,IAAqBZ,EAAAA,EAAAA,UAA0B,OAC/Da,EAAeC,IAAoBd,EAAAA,EAAAA,WAAS,IAC5Ce,EAASC,IAAchB,EAAAA,EAAAA,WAAS,IAGhCiB,EAAmBC,IAAwBlB,EAAAA,EAAAA,WAAS,IACpDmB,EAAoBC,IAAyBpB,EAAAA,EAAAA,UAA0B,OAM9EqB,EAAAA,EAAAA,YAAU,KACaC,eAAeC,QAAQ,oBAE1CD,eAAeE,WAAW,mBAC1BC,OAAOC,SAASC,UAEhBC,GACF,GACC,KAGHP,EAAAA,EAAAA,YAAU,KAER,MAAMQ,EAAkBC,CAAAA,SAAAA,aAAAA,WAAAA,GAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,EAAAA,yBAAAA,wBAAAA,kBAAAA,mEAAYC,sBAAwB,wBACtDC,GAAiBC,EAAAA,EAAAA,IAAGJ,EAAiB,CACvCK,iBAAiB,IA2CrB,OAxCAC,QAAQC,IAAI,kDAAkDP,QAE9DG,EAAOK,GAAG,WAAW,KACjBF,QAAQC,IAAI,4CAA4CJ,EAAOM,KAAK,IAGxEN,EAAOK,GAAG,cAAeE,IACrBJ,QAAQC,IAAI,uCAAuCG,IAAS,IAGhEP,EAAOK,GAAG,iBAAkBG,IACxBL,QAAQhC,MAAM,0CAA2CqC,EAAI,IAIjER,EAAOK,GAAG,wBAAyBI,IAOjCN,QAAQC,IAAI,gDAAiDK,GAC7D1C,GAAa2C,GACXA,EAAcC,KAAIC,GAChBA,EAASC,MAAQJ,EAAKH,GAClB,IACKM,EACHE,OAAQL,EAAKK,UAETL,EAAKM,WAAa,CAAEA,UAAWN,EAAKM,cACpCN,EAAKO,aAAe,CAAEA,YAAaP,EAAKO,gBACxCP,EAAKQ,QAAU,CAAEA,OAAQR,EAAKQ,SAEpCL,KAEP,IAII,KACLT,QAAQC,IAAI,0CACZJ,EAAOkB,YAAY,CACpB,GACA,IAIH,MAAMtB,EAAgBuB,UACpB,IAEE,MAAMC,QAAiBC,EAAAA,EAAYC,eACnCnB,QAAQC,IAAI,sBAAuBgB,EAASX,KAAK3C,WAGjDC,EAAaqD,EAASX,KAAK3C,UAC7B,CAAE,MAAO0C,GAAW,IAADe,EAAAC,EACjBrB,QAAQhC,MAAM,2BAA4BqC,GAC1CpC,GAAqB,QAAZmD,EAAAf,EAAIY,gBAAQ,IAAAG,GAAM,QAANC,EAAZD,EAAcd,YAAI,IAAAe,OAAN,EAAZA,EAAoBC,UAAW,4BACxC1D,EAAa,GACf,CAAC,QACCG,GAAW,EACb,GAqGIwD,EAAcC,GACL,IAAIC,KAAKD,GACVE,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,YAKNC,EAAuBrB,IAC3B,OAAQA,GACN,IAAK,QAUL,QACE,MAAO,cATT,IAAK,YACH,MAAO,cACT,IAAK,UACH,MAAO,gBACT,IAAK,OACH,MAAO,eACT,IAAK,SACH,MAAO,aAGX,EAGF,OAEEsB,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yCAAwCD,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,qBACtCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,sBAAqBD,SAAC,oCAGrCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBD,SAAA,EAC7BE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASA,IAAM/E,EAAS,2BAA2B0E,SAAC,kBAGhFE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLE,QAASA,IAAM/E,EAAS,qBACxBgF,WAAe,OAAJlF,QAAI,IAAJA,GAAY,QAARH,EAAJG,EAAMmF,cAAM,IAAAtF,GAAZA,EAAcuD,SAAiC,WAAvBpD,EAAKmF,OAAO/B,OAC/CgC,MAAY,OAAJpF,QAAI,IAAJA,GAAY,QAARF,EAAJE,EAAMmF,cAAM,IAAArF,GAAZA,EAAcsD,QAAiC,WAAvBpD,EAAKmF,OAAO/B,OAAgD,wBAA1B,wBAAkDwB,SACrH,0BAMJnE,IACCqE,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CACJC,KAAK,QACLvB,QAAStD,EACT8E,QAASA,IAAM7E,EAAS,IACxBmE,UAAU,SAIblE,IACCmE,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CACJC,KAAK,UACLvB,QAASpD,EACT4E,QAASA,IAAM3E,EAAW,IAC1BiE,UAAU,SAKR,OAAJ7E,QAAI,IAAJA,GAAY,QAARD,EAAJC,EAAMmF,cAAM,IAAApF,GAAZA,EAAcqD,QAAiC,WAAvBpD,EAAKmF,OAAO/B,OAWlC,MAVF0B,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CACJC,KAAK,UACLvB,SACEW,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,cAAaD,SAAC,2DAC3BE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,OAAMD,SAAC,8DAGxBC,UAAU,UAIdC,EAAAA,EAAAA,KAACU,EAAAA,EAAI,CAAAZ,SACFrE,GACCuE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCD,UACpDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,+EAEM,IAArBzE,EAAUqF,QACZf,EAAAA,EAAAA,MAAA,OAAKG,UAAU,mBAAkBD,SAAA,EAC/BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,SAAC,kBAC/BE,EAAAA,EAAAA,KAAA,MAAID,UAAU,2BAA0BD,SAAC,sBACzCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,2BAA0BD,SAAC,gFAGxCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACE,QAASA,IAAM/E,EAAS,qBAAqB0E,SAAC,mCAKxDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,kBAAiBD,UAC9BF,EAAAA,EAAAA,MAAA,SAAOG,UAAU,eAAcD,SAAA,EAC7BE,EAAAA,EAAAA,KAAA,SAAAF,UACEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,UACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,aACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,YACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,gBACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,aACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,oBACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,iBACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,cARE,iBAWVE,EAAAA,EAAAA,KAAA,SAAAF,SACGxE,EAAU6C,KAAKC,IAAkB,IAAAwC,EAAAC,EAAAC,EAAA,OAChClB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAAF,SAAK1B,EAAS2C,QACdf,EAAAA,EAAAA,KAAA,MAAAF,SAAK1B,EAAS4C,WACdhB,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAAA,QAAMD,UAAW,6BAA6BJ,EAAoBvB,EAASE,UAAUwB,SAClF1B,EAASE,OAAO2C,OAAO,GAAGC,cAAgB9C,EAASE,OAAO6C,MAAM,QAGrEnB,EAAAA,EAAAA,KAAA,MAAAF,SAAkC,QAAlCc,EAAKxC,EAASgD,4BAAoB,IAAAR,EAAAA,EAAI,OACtCZ,EAAAA,EAAAA,KAAA,MAAAF,SAAKZ,EAAWd,EAASiD,cACzBrB,EAAAA,EAAAA,KAAA,MAAAF,SACG1B,EAASkD,aACc,SAApBlD,EAASE,QAAyC,cAApBF,EAASE,OACrCY,EAAWd,EAASK,QAAUL,EAASI,aAAe,IACtDU,EAAWd,EAASkD,cACtBlD,EAASK,OACPS,EAAWd,EAASK,SACpBuB,EAAAA,EAAAA,KAAA,QAAMD,UAAU,sBAAqBD,SAAC,SAG9CE,EAAAA,EAAAA,KAAA,MAAAF,SACuB,SAApB1B,EAASE,QAAyC,cAApBF,EAASE,QACtCsB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,UAASD,SAAA,EACtBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CAAK,UAA0B,QAAnBe,EAACzC,EAASmD,iBAAS,IAAAV,EAAAA,EAAI,MACnCjB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CAAK,WAA4B,QAApBgB,EAAC1C,EAASoD,kBAAU,IAAAV,EAAAA,EAAI,SAGvCd,EAAAA,EAAAA,KAAA,QAAMD,UAAU,sBAAqBD,SAAC,SAG1CE,EAAAA,EAAAA,KAAA,MAAAF,UACEF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBD,SAAA,EAC7BE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAELC,QAAQ,YACRuB,KAAK,KACLtB,QAASA,KACL,MAAMuB,EAAWtD,EAASC,IAC1BV,QAAQC,IAAI,qCAAqC8D,KACzB,UAApBtD,EAASE,QAA0C,cAApBF,EAASE,OACxClD,EAAS,mBAAmBsG,KAE5BtG,EAAS,qBAAqBsG,IAClC,EAEJtB,SAA8B,YAApBhC,EAASE,OAAqBwB,SAGnB,UAApB1B,EAASE,QAA0C,cAApBF,EAASE,OAAyB,OAAS,QAfvE,kBAkBgB,SAApBF,EAASE,QAAyC,cAApBF,EAASE,UACvC0B,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAELC,QAAQ,YACRuB,KAAK,KACLtB,QAASA,IAAM/E,EAAS,wBAAwBgD,EAASC,OAAOyB,SACjE,aAJK,iBAQa,UAApB1B,EAASE,SACRsB,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAELC,QAAQ,YACRuB,KAAK,KACLtB,QAASA,IAAM/E,EAAS,yBAAyBgD,EAASC,OAAOyB,SAClE,cAJK,mBAONE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAELwB,KAAK,KACLtB,QAASA,IA1Rd/B,KACrBhC,EAAkBgC,GAClB9B,GAAiB,EAAK,EAwRmBqF,CAAcvD,GAAU0B,SACxC,YAHK,eAQW,UAApB1B,EAASE,SACR0B,EAAAA,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,UACEE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAELC,QAAQ,YACRuB,KAAK,KACLtB,QAASA,IAhOV/B,KACzBxB,EAAsBwB,GACtB1B,GAAqB,EAAK,EA8NekF,CAAkBxD,GACjCkC,MAAM,yBAAwBR,SAC/B,YALK,wBAUVE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAELC,QAAQ,SACRuB,KAAK,KACLtB,QAASA,KAAM0B,OAvTd/D,EAuT8BM,EAASC,IAtT9DnC,EAAoB4B,QACpB9B,GAAa,GAFU8B,KAuT4C,EAC7CsC,SAA8B,YAApBhC,EAASE,OAAqBwB,SACzC,UALK,qBA9FH1B,EAASC,IAwGb,cAQjB2B,EAAAA,EAAAA,KAAC8B,EAAAA,EAAY,CACXC,OAAQhG,EACRuE,MAAM,kBACNrB,QAAQ,+EACR+C,YAAY,SACZC,UA/TuBtD,UAC3B,GAAK1C,EAEL,UAEQ4C,EAAAA,EAAYqD,eAAejG,GAGjCV,EAAaD,EAAU6G,QAAO/D,GAAYA,EAASC,MAAQpC,KAG3DH,EAAW,iCACXF,EAAS,GACX,CAAE,MAAOoC,GAAW,IAADoE,EAAAC,EACjB1E,QAAQhC,MAAM,2BAA4BqC,GAC1CpC,GAAqB,QAAZwG,EAAApE,EAAIY,gBAAQ,IAAAwD,GAAM,QAANC,EAAZD,EAAcnE,YAAI,IAAAoE,OAAN,EAAZA,EAAoBpD,UAAW,4BAC1C,CAAC,QAECjD,GAAa,GACbE,EAAoB,KACtB,GA4SIoG,SAAUA,KACRtG,GAAa,GACbE,EAAoB,KAAK,KAK7B8D,EAAAA,EAAAA,KAAC8B,EAAAA,EAAY,CACXC,OAAQ1F,EACRiE,MAAM,gBACNrB,SACEW,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,KAAAF,SAAG,kDACHF,EAAAA,EAAAA,MAAA,KAAGG,UAAU,qBAAoBD,SAAA,EAACE,EAAAA,EAAAA,KAAA,UAAAF,SAAQ,UAAc,mEACvD3D,IACCyD,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kCAAiCD,SAAA,EAC9CF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGE,EAAAA,EAAAA,KAAA,UAAAF,SAAQ,UAAc,IAAE3D,EAAe4E,SAC1CnB,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGE,EAAAA,EAAAA,KAAA,UAAAF,SAAQ,aAAiB,IAAE3D,EAAe6E,YAC7CpB,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGE,EAAAA,EAAAA,KAAA,UAAAF,SAAQ,UAAc,IAAE3D,EAAeoG,SAAS,KAAMpG,EAAeqG,UAAU,WAGtFxC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,6BAA4BD,SAAC,oFAG9CkC,YAAazF,EAAU,aAAe,gBACtC0F,UAjUqBtD,UACzB,GAAKxC,EAEL,IACEK,GAAW,GACXZ,EAAS,IAET+B,QAAQC,IAAI,4BAA6BzB,EAAekC,WAElDQ,EAAAA,EAAY4D,aAAatG,EAAekC,KAI9C9C,GAAa2C,GACXA,EAAcC,KAAIuE,GAChBA,EAAErE,MAAQlC,EAAekC,IAAM,IAAKqE,EAAGpE,OAAQ,WAAcoE,MAKjE5G,GACE8D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,KAAAF,UAAGF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAAQ,aAAW3D,EAAe4E,KAAK,0BAC1Cf,EAAAA,EAAAA,KAAA,KAAGD,UAAU,eAAcD,SAAC,yCAIlC,CAAE,MAAO9B,GAAW,IAAD2E,EAAAC,EACjBjF,QAAQhC,MAAM,0BAA2BqC,GACzCpC,GAAqB,QAAZ+G,EAAA3E,EAAIY,gBAAQ,IAAA+D,GAAM,QAANC,EAAZD,EAAc1E,YAAI,IAAA2E,OAAN,EAAZA,EAAoB3D,UAAW,0BAG1C,CAAC,QACCzC,GAAW,GACXF,GAAiB,GACjBF,EAAkB,KACpB,GA8RIkG,SAAUA,KACRhG,GAAiB,GACjBF,EAAkB,KAAK,KAK3B4D,EAAAA,EAAAA,KAAC6C,EAAAA,EAAqB,CACpBd,OAAQtF,EACRgE,QAASA,IAAM/D,GAAqB,GACpC0B,SAAUzB,EACVmG,YA/RyB7D,IAC7BvC,GAAqB,GACrBE,EAAsB,MACtBd,EAAWmD,GAEPtC,GACFpB,GAAawH,GAAQA,EAAK5E,KAAIuE,GAC5BA,EAAErE,MAAQ1B,EAAmB0B,IAAM,IAAKqE,EAAGpE,OAAQ,YAAagD,cAAc,IAAIlC,MAAO4D,eAAkBN,KAE/G,MAyRA,C,mECpfJ,MA2EA,EA3EkDO,IAQ3C,IAR4C,OACjDlB,EAAM,MACNzB,EAAK,QACLrB,EAAO,YACP+C,EAAc,UAAS,WACvBkB,EAAa,SAAQ,UACrBjB,EAAS,SACTK,GACDW,EACC,MAAME,GAAWC,EAAAA,EAAAA,QAAuB,MAoCxC,OAjCAvG,EAAAA,EAAAA,YAAU,KACR,MAAMwG,EAAsBC,IACtBH,EAASI,UAAYJ,EAASI,QAAQC,SAASF,EAAMG,SACvDnB,GACF,EAOF,OAJIP,GACF2B,SAASC,iBAAiB,YAAaN,GAGlC,KACLK,SAASE,oBAAoB,YAAaP,EAAmB,CAC9D,GACA,CAACtB,EAAQO,KAGZzF,EAAAA,EAAAA,YAAU,KACR,MAAMgH,EAAgBP,IACF,WAAdA,EAAMQ,KACRxB,GACF,EAOF,OAJIP,GACF2B,SAASC,iBAAiB,UAAWE,GAGhC,KACLH,SAASE,oBAAoB,UAAWC,EAAa,CACtD,GACA,CAAC9B,EAAQO,IAEPP,GAGH/B,EAAAA,EAAAA,KAAA,OAAKD,UAAU,6EAA4ED,UACzFF,EAAAA,EAAAA,MAAA,OACEmE,IAAKZ,EACLpD,UAAU,mFAAkFD,SAAA,EAE5FE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wCAAuCD,SAAEQ,KACvDN,EAAAA,EAAAA,KAAA,OAAKD,UAAU,qBAAoBD,SAAEb,KAErCW,EAAAA,EAAAA,MAAA,OAAKG,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLC,QAAQ,YACRC,QAASmC,EAASxC,SAEjBoD,KAEHlD,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLC,QAAQ,SACRC,QAAS8B,EAAUnC,SAElBkC,YAtBS,IA0BZ,C,2GCjEV,MAqHA,EArHoEiB,IAK7D,IAL8D,OACnElB,EAAM,QACNtB,EAAO,SACPrC,EAAQ,YACR0E,GACDG,EACC,MAAOe,EAAmBC,IAAwBzI,EAAAA,EAAAA,UAAiB,KAC5DC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,KAGnCqB,EAAAA,EAAAA,YAAU,KACR,GAAIkF,GAAU3D,EAAU,CACtB,IAAI8F,EAAkB,GACtB,GAAI9F,EAASkD,aACX,IAEE,MAAM6C,EAAe,IAAI/E,KAAKhB,EAASkD,cAEvC,GAAK8C,MAAMD,EAAaE,WAatB1G,QAAQ2G,KAAK,kDAAmDlG,EAASkD,kBAbvC,CAKlC,MAAMhC,EAAO6E,EAAaI,cACpBhF,GAAS4E,EAAaK,WAAa,GAAGC,WAAWC,SAAS,EAAG,KAC7DlF,EAAM2E,EAAaQ,UAAUF,WAAWC,SAAS,EAAG,KACpDE,EAAQT,EAAaU,WAAWJ,WAAWC,SAAS,EAAG,KAE7DR,EAAkB,GAAG5E,KAAQC,KAASC,KAAOoF,KAD7BT,EAAaW,aAAaL,WAAWC,SAAS,EAAG,OAEjE/G,QAAQC,IAAI,4CAA4CQ,EAASkD,8BAA8B4C,IACjG,CAGF,CAAE,MAAOa,GACPpH,QAAQhC,MAAM,wDAAyDoJ,EACzE,CAIF,IAAKb,EAAiB,CACpB,MAAMc,EAAc,IAAI5F,KAAKA,KAAK6F,MAAQ,MAO1Cf,EAAkB,GALLc,EAAYT,kBACVS,EAAYR,WAAa,GAAGC,WAAWC,SAAS,EAAG,QACtDM,EAAYL,UAAUF,WAAWC,SAAS,EAAG,QAC3CM,EAAYH,WAAWJ,WAAWC,SAAS,EAAG,QAC5CM,EAAYF,aAAaL,WAAWC,SAAS,EAAG,OAEhE/G,QAAQC,IAAI,wDAAwDsG,IACtE,CAEAD,EAAqBC,GACrBtI,EAAS,GACX,IACC,CAACmG,EAAQ3D,IAEZ,MAqBM8G,EAAcA,KAClBxJ,GAAW,GACXE,EAAS,IACTqI,EAAqB,IACrBxD,GAAS,EAGX,OACEb,EAAAA,EAAAA,MAACuF,EAAAA,EAAK,CAACpD,OAAQA,EAAQtB,QAASyE,EAAa5E,MAAO,uBAA8B,OAARlC,QAAQ,IAARA,OAAQ,EAARA,EAAU2C,OAAQ,KAAKjB,SAAA,EAC/FF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,CACvBnE,IAASqE,EAAAA,EAAAA,KAACO,EAAAA,EAAK,CAACC,KAAK,QAAQvB,QAAStD,EAAO8E,QAASA,IAAM7E,EAAS,OAEtEoE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,sBAAqBD,SAAC,8DAEnCE,EAAAA,EAAAA,KAACoF,EAAAA,EAAK,CACJtH,GAAG,yBACHiD,KAAK,yBACLP,KAAK,iBACL6E,MAAOrB,EACPsB,SAAWP,GAAMd,EAAqBc,EAAEtB,OAAO4B,OAC/CE,MAAM,wBACNC,UAAQ,EACRzF,UAAU,YAEZC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,iDAAgDD,SAAC,0CAGhEF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,8BAA6BD,SAAA,EAC1CE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAAS+E,EAAa9E,SAAU3E,EAAQqE,SAAC,YAGrEE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACE,QApDSxB,UACrB,GAAKP,GAAa4F,EAAlB,CAKAtI,GAAW,GACXE,EAAS,IACT,IACE,MAAM6J,EAAoB,IAAIrG,KAAK4E,GAAmBhB,oBAChDnE,EAAAA,EAAY6G,iBAAiBtH,EAASC,IAAKoH,GACjD3C,EAAY,aAAa1E,EAAS2C,oCAAoC,IAAI3B,KAAK4E,GAAmB2B,oBACpG,CAAE,MAAO3H,GAAW,IAADe,EAAAC,EACjBrB,QAAQhC,MAAM,6BAA8BqC,GAC5CpC,GAAqB,QAAZmD,EAAAf,EAAIY,gBAAQ,IAAAG,GAAM,QAANC,EAAZD,EAAcd,YAAI,IAAAe,OAAN,EAAZA,EAAoBC,UAAW,+BAC1C,CAAC,QACCvD,GAAW,EACb,CAbA,MAFEE,EAAS,uCAeX,EAmCqCwE,SAAU3E,IAAYuI,EAAkBlE,SACtErE,EAAU,gBAAkB,2BAG3B,C,uFCpHL,MAAM0J,EAA8BlC,IASpC,IATqC,OAC1ClB,EAAM,QACNtB,EAAO,MACPH,EAAK,SACLR,EAAQ,UACRmC,EAAS,YACTD,EAAc,UAAS,eACvB4D,EAAiB,UAAS,WAC1B1C,EAAa,UACdD,EACC,OACEjD,EAAAA,EAAAA,KAAC6F,EAAAA,EAAU,CAACC,QAAM,EAACC,KAAMhE,EAAQiE,GAAIC,EAAAA,SAASnG,UAC5CF,EAAAA,EAAAA,MAACsG,EAAAA,GAAM,CAACF,GAAG,MAAMjG,UAAU,gBAAgBU,QAASA,EAAQX,SAAA,EAC1DE,EAAAA,EAAAA,KAAC6F,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,YACVC,QAAQ,cACRC,MAAM,uBACNC,UAAU,cACVC,QAAQ,YAAW3G,UAEnBE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,4CAGjBC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gCAA+BD,UAC5CE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,8DAA6DD,UAC1EE,EAAAA,EAAAA,KAAC6F,EAAAA,EAAWM,MAAK,CACfH,GAAIC,EAAAA,SACJG,MAAM,wBACNC,UAAU,qBACVC,QAAQ,wBACRC,MAAM,uBACNC,UAAU,wBACVC,QAAQ,qBAAoB3G,UAE5BF,EAAAA,EAAAA,MAACsG,EAAAA,GAAOQ,MAAK,CAAC3G,UAAU,uHAAsHD,SAAA,EAC5IE,EAAAA,EAAAA,KAACkG,EAAAA,GAAOS,MAAK,CACXX,GAAG,KACHjG,UAAU,gDAA+CD,SAExDQ,KAEHN,EAAAA,EAAAA,KAAA,OAAKD,UAAU,6BAA4BD,SACxCA,KAGHF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kCAAiCD,SAAA,EAC9CE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAAQ,YAAYC,QAASM,EAAQX,SAC1CoD,IAEFjB,IACCjC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAAS0F,EAAgBzF,QAAS8B,EAAUnC,SACjDkC,oBASN,C", "sources": ["pages/campaigns/CampaignList.tsx", "components/ConfirmModal.tsx", "components/ScheduleCampaignModal.tsx", "components/Modal.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport ConfirmModal from 'components/ConfirmModal';\nimport ScheduleCampaignModal from 'components/ScheduleCampaignModal';\n// import Layout from '../../components/Layout'; // Removed Layout import\nimport { useAuth } from 'contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { campaignAPI } from 'services/api';\nimport {\n  io,\n  Socket,\n} from 'socket.io-client';\n\n// Define the shape of the campaign data more explicitly\ninterface Campaign {\n  _id: string;\n  name: string;\n  subject: string;\n  status: string; // Keep as string, but could use specific union type if preferred\n  recipientCountActual?: number; // Use optional if not always present initially\n  createdAt: string;\n  scheduledFor?: string;\n  sentAt?: string;\n  completedAt?: string; // Added missing field\n  openCount?: number;\n  clickCount?: number;\n  openRate?: number;\n  clickRate?: number;\n  fromName?: string; // Added for send modal\n  fromEmail?: string; // Added for send modal\n  // Add other fields if needed by the component\n}\n\nconst CampaignList: React.FC = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  // Use the Campaign interface for state type\n  const [campaigns, setCampaigns] = useState<Campaign[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState<React.ReactNode>('');\n  const [modalOpen, setModalOpen] = useState(false);\n  const [campaignToDelete, setCampaignToDelete] = useState<string | null>(null);\n  const [campaignToSend, setCampaignToSend] = useState<Campaign | null>(null);\n  const [sendModalOpen, setSendModalOpen] = useState(false);\n  const [sending, setSending] = useState(false);\n\n  // State for the schedule modal\n  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);\n  const [campaignToSchedule, setCampaignToSchedule] = useState<Campaign | null>(null);\n\n  // No more mock campaigns\n  const mockCampaigns: any[] = [];\n\n  // Fetch campaigns on component mount and handle reload flag\n  useEffect(() => {\n    const shouldReload = sessionStorage.getItem('reloadCampaigns');\n    if (shouldReload) {\n      sessionStorage.removeItem('reloadCampaigns');\n      window.location.reload(); // Consider a less disruptive update if possible\n    } else {\n      loadCampaigns();\n    }\n  }, []); // Run only on mount\n\n  // --- WebSocket Connection and Event Listener ---\n  useEffect(() => {\n    // Connect to the Socket.IO server (use env variable for URL in production)\n    const socketServerUrl = process.env.REACT_APP_SOCKET_URL || 'http://localhost:3000'; \n    const socket: Socket = io(socketServerUrl, {\n        withCredentials: true // Important if using auth/cookies with sockets\n    });\n\n    console.log(`[CampaignList] Attempting to connect socket to ${socketServerUrl}...`);\n\n    socket.on('connect', () => {\n        console.log(`[CampaignList] Socket connected with ID: ${socket.id}`);\n    });\n\n    socket.on('disconnect', (reason) => {\n        console.log(`[CampaignList] Socket disconnected: ${reason}`);\n    });\n\n    socket.on('connect_error', (err) => {\n        console.error(`[CampaignList] Socket connection error:`, err);\n    });\n\n    // Listener for campaign status updates\n    socket.on('campaignStatusUpdate', (data: {\n         id: string;\n         status: string;\n         startedAt?: string; // Add optional timestamp fields\n         completedAt?: string;\n         sentAt?: string; // Include sentAt if backend might send it\n    }) => {\n      console.log('[CampaignList] Received campaignStatusUpdate:', data);\n      setCampaigns(prevCampaigns =>\n        prevCampaigns.map(campaign =>\n          campaign._id === data.id\n            ? { \n                ...campaign, \n                status: data.status, \n                // Update timestamps if provided in the event data\n                ...(data.startedAt && { startedAt: data.startedAt }),\n                ...(data.completedAt && { completedAt: data.completedAt }),\n                ...(data.sentAt && { sentAt: data.sentAt })\n              } \n            : campaign\n        )\n      );\n    });\n\n    // Cleanup function: disconnect socket when component unmounts\n    return () => {\n      console.log('[CampaignList] Disconnecting socket...');\n      socket.disconnect();\n    };\n  }, []); // Run only once on component mount\n  // --- End WebSocket Setup ---\n\n  // Function to load campaigns from API\n  const loadCampaigns = async () => {\n    try {\n      // Get campaigns from API\n      const response = await campaignAPI.getCampaigns();\n      console.log('Campaigns from API:', response.data.campaigns);\n\n      // Set campaigns directly\n      setCampaigns(response.data.campaigns);\n    } catch (err: any) {\n      console.error('Error loading campaigns:', err);\n      setError(err.response?.data?.message || 'Failed to load campaigns');\n      setCampaigns([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to open delete confirmation modal\n  const openDeleteModal = (id: string) => {\n    setCampaignToDelete(id);\n    setModalOpen(true);\n  };\n\n  // Function to open send confirmation modal\n  const openSendModal = (campaign: Campaign) => {\n    setCampaignToSend(campaign);\n    setSendModalOpen(true);\n  };\n\n  // Function to delete a campaign\n  const handleDeleteCampaign = async () => {\n    if (!campaignToDelete) return;\n\n    try {\n      // Call API to delete campaign\n      await campaignAPI.deleteCampaign(campaignToDelete);\n\n      // Update state to reflect the deletion\n      setCampaigns(campaigns.filter(campaign => campaign._id !== campaignToDelete));\n\n      // Show success message\n      setSuccess('Campaign deleted successfully');\n      setError(''); // Clear any existing errors\n    } catch (err: any) {\n      console.error('Error deleting campaign:', err);\n      setError(err.response?.data?.message || 'Failed to delete campaign');\n    } finally {\n      // Close modal and reset campaignToDelete\n      setModalOpen(false);\n      setCampaignToDelete(null);\n    }\n  };\n\n  // Function to send a campaign - Minor update to use Campaign type\n  const handleSendCampaign = async () => {\n    if (!campaignToSend) return;\n\n    try {\n      setSending(true);\n      setError('');\n\n      console.log('Sending campaign with ID:', campaignToSend._id);\n      // API call remains the same, response handling might not be needed now for status\n      await campaignAPI.sendCampaign(campaignToSend._id);\n\n      // OPTIONAL: Immediately update status locally for responsiveness,\n      // but rely on WebSocket for the final authoritative update.\n      setCampaigns(prevCampaigns =>\n        prevCampaigns.map(c =>\n          c._id === campaignToSend._id ? { ...c, status: 'sending' } : c\n        )\n      );\n\n      // Success message can be simpler now\n      setSuccess(\n        <div>\n          <p><strong>Campaign \"{campaignToSend.name}\" send initiated!</strong></p>\n          <p className=\"mt-2 text-sm\">Status will update automatically.</p>\n        </div>\n      );\n\n    } catch (err: any) {\n      console.error('Error sending campaign:', err);\n      setError(err.response?.data?.message || 'Failed to send campaign');\n      // Optionally revert optimistic update on error\n      // loadCampaigns(); // Or revert specific campaign status\n    } finally {\n      setSending(false);\n      setSendModalOpen(false);\n      setCampaignToSend(null);\n    }\n  };\n\n  // Function to open the schedule modal\n  const openScheduleModal = (campaign: Campaign) => {\n    setCampaignToSchedule(campaign);\n    setScheduleModalOpen(true);\n  };\n\n  // Callback function when scheduling is successful\n  const handleScheduleSuccess = (message: string) => {\n    setScheduleModalOpen(false);\n    setCampaignToSchedule(null);\n    setSuccess(message);\n    // Optimistically update the campaign status locally\n    if (campaignToSchedule) {\n      setCampaigns(prev => prev.map(c => \n        c._id === campaignToSchedule._id ? { ...c, status: 'scheduled', scheduledFor: new Date().toISOString() } : c // Update status and add placeholder date\n      ));\n    }\n    // Alternatively, call loadCampaigns() for a full refresh, but local update is faster UI\n    // loadCampaigns(); \n  };\n\n  // Format date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Get status badge class\n  const getStatusBadgeClass = (status: string) => {\n    switch (status) {\n      case 'draft':\n        return 'bg-gray-700';\n      case 'scheduled':\n        return 'bg-blue-800';\n      case 'sending':\n        return 'bg-yellow-800';\n      case 'sent':\n        return 'bg-green-800';\n      case 'failed':\n        return 'bg-red-800';\n      default:\n        return 'bg-gray-700';\n    }\n  };\n\n  return (\n    // <Layout title=\"Campaigns\">\n    <>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-xl font-semibold\">Email Campaigns</h2>\n          <p className=\"text-text-secondary\">Manage your email campaigns</p>\n        </div>\n\n        <div className=\"flex space-x-2\">\n          <Button variant=\"secondary\" onClick={() => navigate('/campaigns/domain-setup')}>\n            Domain Setup\n          </Button>\n          <Button\n            onClick={() => navigate('/campaigns/create')}\n            disabled={!user?.domain?.status || user.domain.status !== 'active'}\n            title={!user?.domain?.status || user.domain.status !== 'active' ? 'Verify a domain first' : 'Create a new campaign'}\n          >\n            Create Campaign\n          </Button>\n        </div>\n      </div>\n\n      {error && (\n        <Alert\n          type=\"error\"\n          message={error}\n          onClose={() => setError('')}\n          className=\"mb-6\"\n        />\n      )}\n\n      {success && (\n        <Alert\n          type=\"success\"\n          message={success}\n          onClose={() => setSuccess('')}\n          className=\"mb-6\"\n        />\n      )}\n\n      {/* Domain verification check */}\n      {!user?.domain?.status || user.domain.status !== 'active' ? (\n        <Alert\n          type=\"warning\"\n          message={\n            <div>\n              <p className=\"font-medium\">You need to verify a domain before creating campaigns</p>\n              <p className=\"mt-1\">Please go to Domain Setup to verify your domain first.</p>\n            </div>\n          }\n          className=\"mb-6\"\n        />\n      ) : null}\n\n      <Card>\n        {loading ? (\n          <div className=\"flex justify-center items-center py-8\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n          </div>\n        ) : campaigns.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-4\">📧</div>\n            <h3 className=\"text-xl font-medium mb-2\">No Campaigns Yet</h3>\n            <p className=\"text-text-secondary mb-4\">\n              Create your first email campaign to start sending emails to your contacts.\n            </p>\n            <Button onClick={() => navigate('/campaigns/create')}>\n              Create Your First Campaign\n            </Button>\n          </div>\n        ) : (\n          <div className=\"table-container\">\n            <table className=\"table w-full\">\n              <thead>\n                <tr key=\"header-row\">\n                  <th>Name</th>\n                  <th>Subject</th>\n                  <th>Status</th>\n                  <th>Recipients</th>\n                  <th>Created</th>\n                  <th>Scheduled/Sent</th>\n                  <th>Performance</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {campaigns.map((campaign: Campaign) => (\n                  <tr key={campaign._id}>\n                    <td>{campaign.name}</td>\n                    <td>{campaign.subject}</td>\n                    <td>\n                      <span className={`px-2 py-1 text-xs rounded ${getStatusBadgeClass(campaign.status)}`}>\n                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}\n                      </span>\n                    </td>\n                    <td>{campaign.recipientCountActual ?? '-'}</td>\n                    <td>{formatDate(campaign.createdAt)}</td>\n                    <td>\n                      {campaign.scheduledFor\n                        ? campaign.status === 'sent' || campaign.status === 'completed'\n                          ? formatDate(campaign.sentAt || campaign.completedAt || '')\n                          : formatDate(campaign.scheduledFor)\n                        : campaign.sentAt\n                          ? formatDate(campaign.sentAt)\n                          : <span className=\"text-text-secondary\">-</span>\n                      }\n                    </td>\n                    <td>\n                      {campaign.status === 'sent' || campaign.status === 'completed' ? (\n                        <div className=\"text-sm\">\n                          <div>Opens: {campaign.openCount ?? 0}</div>\n                          <div>Clicks: {campaign.clickCount ?? 0}</div>\n                        </div>\n                      ) : (\n                        <span className=\"text-text-secondary\">-</span>\n                      )}\n                    </td>\n                    <td>\n                      <div className=\"flex space-x-2\">\n                        <Button\n                          key=\"edit-view-btn\"\n                          variant=\"secondary\"\n                          size=\"sm\"\n                          onClick={() => {\n                              const targetId = campaign._id;\n                              console.log(`[CampaignList] Navigating for ID: ${targetId}`); // Log ID before navigate\n                              if (campaign.status === 'draft' || campaign.status === 'scheduled') {\n                                  navigate(`/campaigns/edit/${targetId}`);\n                              } else {\n                                  navigate(`/campaign-summary/${targetId}`); \n                              }\n                          }}\n                          disabled={campaign.status === 'sending'}\n                        >\n                          {/* Keep label conditional */} \n                          {campaign.status === 'draft' || campaign.status === 'scheduled' ? 'Edit' : 'View'}\n                        </Button>\n\n                        {(campaign.status === 'sent' || campaign.status === 'completed') && (\n                          <Button\n                            key=\"analytics-btn\"\n                            variant=\"secondary\"\n                            size=\"sm\"\n                            onClick={() => navigate(`/campaigns/analytics/${campaign._id}`)}\n                          >\n                            Analytics\n                          </Button>\n                        )}\n                        {campaign.status === 'draft' && (\n                          <>\n                            <Button\n                              key=\"recipients-btn\"\n                              variant=\"secondary\"\n                              size=\"sm\"\n                              onClick={() => navigate(`/campaigns/recipients/${campaign._id}`)}\n                            >\n                              Recipients\n                            </Button>\n                            <Button\n                              key=\"send-btn\"\n                              size=\"sm\"\n                              onClick={() => openSendModal(campaign)}\n                            >\n                              Send Now\n                            </Button>\n                          </>\n                        )}\n                        {campaign.status === 'draft' && (\n                          <>\n                            <Button\n                              key=\"schedule-btn-list\"\n                              variant=\"secondary\"\n                              size=\"sm\"\n                              onClick={() => openScheduleModal(campaign)}\n                              title=\"Schedule this campaign\"\n                            >\n                              Schedule\n                            </Button>\n                          </>\n                        )}\n                        <Button\n                          key=\"delete-btn\"\n                          variant=\"danger\"\n                          size=\"sm\"\n                          onClick={() => openDeleteModal(campaign._id)}\n                          disabled={campaign.status === 'sending'}\n                        >\n                          Delete\n                        </Button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </Card>\n      {/* Delete Confirmation Modal */}\n      <ConfirmModal\n        isOpen={modalOpen}\n        title=\"Delete Campaign\"\n        message=\"Are you sure you want to delete this campaign? This action cannot be undone.\"\n        confirmText=\"Delete\"\n        onConfirm={handleDeleteCampaign}\n        onCancel={() => {\n          setModalOpen(false);\n          setCampaignToDelete(null);\n        }}\n      />\n\n      {/* Send Campaign Confirmation Modal */}\n      <ConfirmModal\n        isOpen={sendModalOpen}\n        title=\"Send Campaign\"\n        message={\n          <div>\n            <p>Are you sure you want to send this campaign?</p>\n            <p className=\"mt-2 text-blue-400\"><strong>Note:</strong> This will send real emails to your recipients using AWS SES.</p>\n            {campaignToSend && (\n              <div className=\"mt-4 bg-gray-900 p-3 rounded-md\">\n                <p><strong>Name:</strong> {campaignToSend.name}</p>\n                <p><strong>Subject:</strong> {campaignToSend.subject}</p>\n                <p><strong>From:</strong> {campaignToSend.fromName} &lt;{campaignToSend.fromEmail}&gt;</p>\n              </div>\n            )}\n            <p className=\"mt-4 text-gray-400 text-sm\">Make sure your campaign content is ready and has been tested before sending.</p>\n          </div>\n        }\n        confirmText={sending ? \"Sending...\" : \"Send Campaign\"}\n        onConfirm={handleSendCampaign}\n        onCancel={() => {\n          setSendModalOpen(false);\n          setCampaignToSend(null);\n        }}\n      />\n\n      {/* Schedule Campaign Modal */}\n      <ScheduleCampaignModal\n        isOpen={scheduleModalOpen}\n        onClose={() => setScheduleModalOpen(false)}\n        campaign={campaignToSchedule}\n        onScheduled={handleScheduleSuccess}\n      />\n    </>\n    // </Layout>\n  );\n};\n\nexport default CampaignList;\n", "import React, {\n  useEffect,\n  useRef,\n} from 'react';\n\nimport Button from './Button';\n\ninterface ConfirmModalProps {\n  isOpen: boolean;\n  title: string;\n  message: React.ReactNode;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n}\n\nconst ConfirmModal: React.FC<ConfirmModalProps> = ({\n  isOpen,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  onConfirm,\n  onCancel,\n}) => {\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  // Close modal when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {\n        onCancel();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isOpen, onCancel]);\n\n  // Handle escape key press\n  useEffect(() => {\n    const handleEscKey = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onCancel();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscKey);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscKey);\n    };\n  }, [isOpen, onCancel]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\n      <div\n        ref={modalRef}\n        className=\"bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6 w-full max-w-md mx-4\"\n      >\n        <h3 className=\"text-xl font-semibold mb-4 text-white\">{title}</h3>\n        <div className=\"mb-6 text-gray-300\">{message}</div>\n\n        <div className=\"flex justify-end space-x-3\">\n          <Button\n            variant=\"secondary\"\n            onClick={onCancel}\n          >\n            {cancelText}\n          </Button>\n          <Button\n            variant=\"danger\"\n            onClick={onConfirm}\n          >\n            {confirmText}\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfirmModal;\n", "import React, {\r\n  useEffect,\r\n  useState,\r\n} from 'react';\r\n\r\nimport { campaignAPI } from 'services/api';\r\n\r\nimport Alert from './Alert';\r\nimport Button from './Button';\r\nimport Input from './Input';\r\nimport { Modal } from './Modal';\r\n\r\ninterface ScheduleCampaignModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  campaign: { \r\n    _id: string; \r\n    name: string; \r\n    scheduledFor?: string; // Add optional field\r\n  } | null; // Basic campaign info needed\r\n  onScheduled: (message: string) => void; // Callback on successful schedule\r\n}\r\n\r\nconst ScheduleCampaignModal: React.FC<ScheduleCampaignModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  campaign,\r\n  onScheduled,\r\n}) => {\r\n  const [scheduledDateTime, setScheduledDateTime] = useState<string>('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n\r\n  // Set default schedule time when modal opens or campaign changes\r\n  useEffect(() => {\r\n    if (isOpen && campaign) {\r\n      let initialDateTime = '';\r\n      if (campaign.scheduledFor) {\r\n        try {\r\n          // Attempt to parse the existing schedule time\r\n          const existingDate = new Date(campaign.scheduledFor);\r\n          // Check if the date is valid\r\n          if (!isNaN(existingDate.getTime())) {\r\n             // Format for datetime-local input (YYYY-MM-DDTHH:mm)\r\n             // Important: Adjust for local timezone offset if needed, \r\n             // otherwise this might display UTC time in the input.\r\n             // A simpler approach is to format based on local components.\r\n            const year = existingDate.getFullYear();\r\n            const month = (existingDate.getMonth() + 1).toString().padStart(2, '0'); // months are 0-indexed\r\n            const day = existingDate.getDate().toString().padStart(2, '0');\r\n            const hours = existingDate.getHours().toString().padStart(2, '0');\r\n            const minutes = existingDate.getMinutes().toString().padStart(2, '0');\r\n            initialDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;\r\n            console.log(`[ScheduleModal] Using existing schedule: ${campaign.scheduledFor} -> Formatted: ${initialDateTime}`);\r\n          } else {\r\n            console.warn('[ScheduleModal] Invalid existing schedule date:', campaign.scheduledFor);\r\n          }\r\n        } catch (e) {\r\n          console.error('[ScheduleModal] Error parsing existing schedule date:', e);\r\n        }\r\n      }\r\n      \r\n      // If no valid existing schedule, default to 1 hour from now in local time\r\n      if (!initialDateTime) {\r\n        const defaultDate = new Date(Date.now() + 60 * 60 * 1000);\r\n        // Format the default local time correctly for the input\r\n        const year = defaultDate.getFullYear();\r\n        const month = (defaultDate.getMonth() + 1).toString().padStart(2, '0'); \r\n        const day = defaultDate.getDate().toString().padStart(2, '0');\r\n        const hours = defaultDate.getHours().toString().padStart(2, '0');\r\n        const minutes = defaultDate.getMinutes().toString().padStart(2, '0');\r\n        initialDateTime = `${year}-${month}-${day}T${hours}:${minutes}`; \r\n        console.log(`[ScheduleModal] Setting default local schedule time: ${initialDateTime}`);\r\n      }\r\n      \r\n      setScheduledDateTime(initialDateTime);\r\n      setError(''); // Clear previous errors\r\n    }\r\n  }, [isOpen, campaign]);\r\n\r\n  const handleSchedule = async () => {\r\n    if (!campaign || !scheduledDateTime) {\r\n      setError('Please select a valid date and time.');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    try {\r\n      const scheduleTimestamp = new Date(scheduledDateTime).toISOString();\r\n      await campaignAPI.scheduleCampaign(campaign._id, scheduleTimestamp);\r\n      onScheduled(`Campaign \"${campaign.name}\" scheduled successfully for ${new Date(scheduledDateTime).toLocaleString()}.`);\r\n    } catch (err: any) {\r\n      console.error('Error scheduling campaign:', err);\r\n      setError(err.response?.data?.message || 'Failed to schedule campaign.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle modal close - reset state\r\n  const handleClose = () => {\r\n    setLoading(false);\r\n    setError('');\r\n    setScheduledDateTime('');\r\n    onClose(); // Call the parent's close handler\r\n  };\r\n\r\n  return (\r\n    <Modal isOpen={isOpen} onClose={handleClose} title={`Schedule Campaign: ${campaign?.name || ''}`}>\r\n      <div className=\"space-y-4\">\r\n        {error && <Alert type=\"error\" message={error} onClose={() => setError('')} />}\r\n        \r\n        <p className='text-text-secondary'>Select the date and time to start sending this campaign.</p>\r\n\r\n        <Input\r\n          id=\"scheduledDateTimeModal\"\r\n          name=\"scheduledDateTimeModal\"\r\n          type=\"datetime-local\"\r\n          value={scheduledDateTime}\r\n          onChange={(e) => setScheduledDateTime(e.target.value)}\r\n          label=\"Scheduled Date & Time\"\r\n          required\r\n          className=\"w-full\" // Use full width inside modal\r\n        />\r\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 -mt-2\">Your local timezone will be used.</p>\r\n        \r\n      </div>\r\n      <div className=\"mt-6 flex justify-end gap-3\">\r\n        <Button variant=\"secondary\" onClick={handleClose} disabled={loading}>\r\n          Cancel\r\n        </Button>\r\n        <Button onClick={handleSchedule} disabled={loading || !scheduledDateTime}>\r\n          {loading ? 'Scheduling...' : 'Schedule Campaign'}\r\n        </Button>\r\n      </div>\r\n    </Modal>\r\n  );\r\n};\r\n\r\nexport default ScheduleCampaignModal; ", "import React, { Fragment } from 'react';\r\n\r\nimport {\r\n  Dialog,\r\n  Transition,\r\n} from '@headlessui/react';\r\n\r\nimport Button from './Button'; // Assuming Button component exists\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: React.ReactNode;\r\n  onConfirm?: () => void;\r\n  confirmText?: string;\r\n  confirmVariant?: 'primary' | 'secondary' | 'danger';\r\n  cancelText?: string;\r\n}\r\n\r\nexport const Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  onConfirm,\r\n  confirmText = 'Confirm',\r\n  confirmVariant = 'primary',\r\n  cancelText = 'Cancel',\r\n}) => {\r\n  return (\r\n    <Transition appear show={isOpen} as={Fragment}>\r\n      <Dialog as=\"div\" className=\"relative z-10\" onClose={onClose}>\r\n        <Transition.Child\r\n          as={Fragment}\r\n          enter=\"ease-out duration-300\"\r\n          enterFrom=\"opacity-0\"\r\n          enterTo=\"opacity-100\"\r\n          leave=\"ease-in duration-200\"\r\n          leaveFrom=\"opacity-100\"\r\n          leaveTo=\"opacity-0\"\r\n        >\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" />\r\n        </Transition.Child>\r\n\r\n        <div className=\"fixed inset-0 overflow-y-auto\">\r\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n            <Transition.Child\r\n              as={Fragment}\r\n              enter=\"ease-out duration-300\"\r\n              enterFrom=\"opacity-0 scale-95\"\r\n              enterTo=\"opacity-100 scale-100\"\r\n              leave=\"ease-in duration-200\"\r\n              leaveFrom=\"opacity-100 scale-100\"\r\n              leaveTo=\"opacity-0 scale-95\"\r\n            >\r\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all\">\r\n                <Dialog.Title\r\n                  as=\"h3\"\r\n                  className=\"text-lg font-medium leading-6 text-white mb-4\"\r\n                >\r\n                  {title}\r\n                </Dialog.Title>\r\n                <div className=\"mt-2 text-sm text-gray-300\">\r\n                  {children}\r\n                </div>\r\n\r\n                <div className=\"mt-6 flex justify-end space-x-3\">\r\n                  <Button variant=\"secondary\" onClick={onClose}>\r\n                    {cancelText}\r\n                  </Button>\r\n                  {onConfirm && (\r\n                    <Button variant={confirmVariant} onClick={onConfirm}>\r\n                      {confirmText}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </Dialog.Panel>\r\n            </Transition.Child>\r\n          </div>\r\n        </div>\r\n      </Dialog>\r\n    </Transition>\r\n  );\r\n}; "], "names": ["CampaignList", "_user$domain", "_user$domain2", "_user$domain3", "user", "useAuth", "navigate", "useNavigate", "campaigns", "setCampaigns", "useState", "loading", "setLoading", "error", "setError", "success", "setSuccess", "modalOpen", "setModalOpen", "campaignToDelete", "setCampaignToDelete", "campaignToSend", "setCampaignToSend", "sendModalOpen", "setSendModalOpen", "sending", "setSending", "scheduleModalOpen", "setScheduleModalOpen", "campaignToSchedule", "setCampaignToSchedule", "useEffect", "sessionStorage", "getItem", "removeItem", "window", "location", "reload", "loadCampaigns", "socketServerUrl", "process", "REACT_APP_SOCKET_URL", "socket", "io", "withCredentials", "console", "log", "on", "id", "reason", "err", "data", "prevCampaigns", "map", "campaign", "_id", "status", "startedAt", "completedAt", "sentAt", "disconnect", "async", "response", "campaignAPI", "getCampaigns", "_err$response", "_err$response$data", "message", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getStatusBadgeClass", "_jsxs", "_Fragment", "children", "className", "_jsx", "<PERSON><PERSON>", "variant", "onClick", "disabled", "domain", "title", "<PERSON><PERSON>", "type", "onClose", "Card", "length", "_campaign$recipientCo", "_campaign$openCount", "_campaign$clickCount", "name", "subject", "char<PERSON>t", "toUpperCase", "slice", "recipientCountActual", "createdAt", "scheduledFor", "openCount", "clickCount", "size", "targetId", "openSendModal", "openScheduleModal", "openDeleteModal", "ConfirmModal", "isOpen", "confirmText", "onConfirm", "deleteCampaign", "filter", "_err$response2", "_err$response2$data", "onCancel", "fromName", "fromEmail", "sendCampaign", "c", "_err$response3", "_err$response3$data", "ScheduleCampaignModal", "onScheduled", "prev", "toISOString", "_ref", "cancelText", "modalRef", "useRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleEscKey", "key", "ref", "scheduledDateTime", "setScheduledDateTime", "initialDateTime", "existingDate", "isNaN", "getTime", "warn", "getFullYear", "getMonth", "toString", "padStart", "getDate", "hours", "getHours", "getMinutes", "e", "defaultDate", "now", "handleClose", "Modal", "Input", "value", "onChange", "label", "required", "scheduleTimestamp", "scheduleCampaign", "toLocaleString", "confirmVariant", "Transition", "appear", "show", "as", "Fragment", "Dialog", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Panel", "Title"], "sourceRoot": ""}