"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[967],{8967:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(5043),r=s(9291),n=s(8417),l=s(1411),i=s(4741),d=s(9579),c=s(579);const o=()=>{const[e,t]=(0,a.useState)([]),[s,o]=(0,a.useState)(!0),[m,u]=(0,a.useState)(null),[x,g]=(0,a.useState)(null),[h,p]=(0,a.useState)([]),[y,f]=(0,a.useState)(!1),[b,v]=(0,a.useState)(1),[j,N]=(0,a.useState)(0),[w,C]=(0,a.useState)({name:"",description:"",rules:[{field:"email",operator:"contains",value:""}]}),S=[{id:"email",name:"Email",type:"string"},{id:"firstName",name:"First Name",type:"string"},{id:"lastName",name:"Last Name",type:"string"},{id:"company",name:"Company",type:"string"},{id:"lastOpened",name:"Last Opened Date",type:"date"},{id:"lastClicked",name:"Last Clicked Date",type:"date"},{id:"tags",name:"Tags",type:"tag"},{id:"status",name:"Status",type:"status"},{id:"source",name:"Source",type:"string"},{id:"country",name:"Country",type:"string"}],A=e=>{const t=S.find((t=>t.id===e));switch((null===t||void 0===t?void 0:t.type)||"string"){case"string":case"tag":case"status":return[{id:"equals",name:"Equals"},{id:"contains",name:"Contains"},{id:"startsWith",name:"Starts With"},{id:"endsWith",name:"Ends With"},{id:"notEquals",name:"Does Not Equal"},{id:"doesNotContain",name:"Does Not Contain"}];case"date":return[{id:"before",name:"Before (YYYY-MM-DD)"},{id:"after",name:"After (YYYY-MM-DD)"},{id:"moreThan",name:"More Than X Days Ago"},{id:"lessThan",name:"Less Than X Days Ago"}];default:return[{id:"equals",name:"Equals"},{id:"contains",name:"Contains"}]}};(0,a.useEffect)((()=>{(async()=>{o(!0),u(null);try{const e=await d.Zg.getUserSegments();e.success?t(e.data):u(e.message||"Failed to fetch segments")}catch(e){u(e.message||"An error occurred while fetching segments")}finally{o(!1)}})()}),[]),(0,a.useEffect)((()=>{if(!x)return p([]),void N(0);(async()=>{f(!0);try{const e=await d.Zg.getSegmentContacts(x.id,b,20);e.success?(p(e.data.contacts),N(e.data.total)):(console.error("Failed to fetch segment contacts:",e.message),u("Failed to fetch contacts for the selected segment."),p([]),N(0))}catch(e){console.error("Error fetching segment contacts:",e),u(e.message||"An error occurred while fetching contacts."),p([]),N(0)}finally{f(!1)}})()}),[x,b]);const k=(e,t,s)=>{const a=w.rules.map(((a,r)=>{if(r===e){const e={...a,[t]:s};return"field"===t&&(e.operator=A(s)[0].id),e}return a}));C({...w,rules:a})},D=Math.ceil(j/20),E=e=>{e>=1&&e<=D&&v(e)};return(0,c.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,c.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,c.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"Segment Builder"})}),(0,c.jsx)("p",{className:"text-text-secondary mb-6",children:"Create dynamic contact segments based on various criteria."}),m&&(0,c.jsx)(r.A,{type:"error",message:m,onClose:()=>u(null),className:"mb-6"}),(0,c.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,c.jsxs)(l.A,{className:"lg:col-span-1",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Create New Segment"}),(0,c.jsxs)("form",{onSubmit:async s=>{if(s.preventDefault(),w.name.trim())if(w.rules.some((e=>!e.value.trim())))u("All rule values must be filled");else{o(!0),u(null);try{const s=await d.Zg.createSegment(w);s.success?(t([...e,s.data]),C({name:"",description:"",rules:[{field:"email",operator:"contains",value:""}]})):u(s.message||"Failed to create segment")}catch(a){u(a.message||"An error occurred while creating segment")}finally{o(!1)}}else u("Segment name is required")},className:"p-4 space-y-4",children:[(0,c.jsx)(i.A,{label:"Segment Name",id:"segmentName",name:"segmentName",value:w.name,onChange:e=>C({...w,name:e.target.value}),placeholder:"e.g., Engaged Newsletter Subscribers",required:!0}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"segmentDescription",className:"block text-sm font-medium text-text-secondary mb-1",children:"Description (Optional)"}),(0,c.jsx)("textarea",{id:"segmentDescription",name:"segmentDescription",rows:2,value:w.description,onChange:e=>C({...w,description:e.target.value}),placeholder:"Brief description of the segment",className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500"})]}),(0,c.jsxs)("div",{className:"space-y-3 pt-2",children:[(0,c.jsx)("h3",{className:"text-md font-medium text-text-secondary",children:"Rules (All must be true - AND logic)"}),w.rules.map(((e,t)=>(0,c.jsxs)("div",{className:"p-3 border border-gray-700 rounded-md space-y-2 relative bg-background",children:[w.rules.length>1&&(0,c.jsx)("button",{type:"button",onClick:()=>(e=>{if(w.rules.length<=1)return void u("A segment must have at least one rule.");const t=w.rules.filter(((t,s)=>s!==e));C({...w,rules:t})})(t),className:"absolute top-1 right-1 text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-700 transition-colors",title:"Remove rule",children:(0,c.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:(0,c.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})})}),(0,c.jsxs)("div",{className:"grid grid-cols-3 gap-2",children:[(0,c.jsxs)("div",{className:"col-span-1",children:[(0,c.jsx)("label",{htmlFor:`rule-field-${t}`,className:"block text-xs font-medium text-text-secondary mb-1",children:"Field"}),(0,c.jsx)("select",{id:`rule-field-${t}`,name:`rule-field-${t}`,value:e.field,onChange:e=>k(t,"field",e.target.value),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:S.map((e=>(0,c.jsx)("option",{value:e.id,children:e.name},e.id)))})]}),(0,c.jsxs)("div",{className:"col-span-1",children:[(0,c.jsx)("label",{htmlFor:`rule-operator-${t}`,className:"block text-xs font-medium text-text-secondary mb-1",children:"Operator"}),(0,c.jsx)("select",{id:`rule-operator-${t}`,name:`rule-operator-${t}`,value:e.operator,onChange:e=>k(t,"operator",e.target.value),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:A(e.field).map((e=>(0,c.jsx)("option",{value:e.id,children:e.name},e.id)))})]}),(0,c.jsx)("div",{className:"col-span-1",children:(0,c.jsx)(i.A,{label:"Value",id:`rule-value-${t}`,name:`rule-value-${t}`,value:e.value,onChange:e=>k(t,"value",e.target.value),required:!0,placeholder:"Enter value..."})})]})]},t))),(0,c.jsx)(n.A,{type:"button",variant:"secondary",size:"sm",onClick:()=>{C({...w,rules:[...w.rules,{field:"email",operator:A("email")[0].id,value:""}]})},children:"+ Add Rule"})]}),(0,c.jsx)("div",{className:"flex justify-end pt-2",children:(0,c.jsx)(n.A,{type:"submit",disabled:s||0===w.rules.length,children:s?"Creating...":"Create Segment"})})]})]}),(0,c.jsxs)(l.A,{className:"lg:col-span-1",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Existing Segments"}),s&&0===e.length?(0,c.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"Loading segments..."}):0===e.length?(0,c.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"No segments created yet."}):(0,c.jsx)("ul",{className:"divide-y divide-gray-700 max-h-[500px] overflow-y-auto",children:e.map((e=>(0,c.jsxs)("li",{className:"p-4 hover:bg-gray-700 cursor-pointer "+((null===x||void 0===x?void 0:x.id)===e.id?"bg-gray-700":""),onClick:()=>(e=>{(null===x||void 0===x?void 0:x.id)!==e.id&&(g(e),v(1))})(e),children:[(0,c.jsxs)("div",{className:"flex justify-between items-center",children:[(0,c.jsx)("p",{className:"text-sm font-medium text-text-primary truncate flex-1 mr-2",children:e.name}),void 0!==e.contactCount&&(0,c.jsxs)("span",{className:"text-xs text-blue-400 bg-blue-900/50 px-2 py-0.5 rounded-full",children:[e.contactCount," Contacts"]})]}),(0,c.jsx)("p",{className:"text-xs text-text-secondary mt-1 truncate",children:e.description})]},e.id)))})]}),(0,c.jsxs)(l.A,{className:"lg:col-span-1",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Segment Details & Contacts"}),(0,c.jsx)("div",{className:"p-4",children:x?(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex justify-between items-start",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"text-md font-medium text-text-primary mb-1",children:x.name}),(0,c.jsx)("p",{className:"text-sm text-text-secondary mb-3",children:x.description||"No description"})]}),(0,c.jsx)(n.A,{variant:"danger",size:"sm",onClick:async()=>{if(x&&window.confirm(`Are you sure you want to delete the segment "${x.name}"? This action cannot be undone.`)){o(!0),u(null);try{const e=await d.Zg.deleteSegment(x.id);e.success?(t((e=>e.filter((e=>e.id!==x.id)))),g(null),p([]),N(0)):u(e.message||"Failed to delete segment")}catch(e){u(e.message||"An error occurred while deleting the segment"),console.error("Error deleting segment:",e)}finally{o(!1)}}},disabled:s,children:s?"Deleting...":"Delete"})]}),(0,c.jsx)("h4",{className:"text-sm font-medium text-text-secondary mb-1 mt-2",children:"Rules:"}),(0,c.jsx)("ul",{className:"text-xs text-text-secondary space-y-1 bg-secondary-bg p-3 rounded-md border border-gray-700",children:x.rules.map(((e,t)=>(0,c.jsx)("li",{className:"font-mono",children:`${e.field} ${e.operator} "${e.value}"`},t)))})]}),(0,c.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-700",children:[(0,c.jsxs)("h3",{className:"text-md font-medium text-text-primary mb-2",children:["Contacts in Segment (",j,")"]}),y?(0,c.jsx)("div",{className:"text-center text-text-secondary py-4",children:"Loading contacts..."}):0===h.length?(0,c.jsx)("p",{className:"text-sm text-text-secondary italic text-center py-4",children:"No contacts match this segment."}):(0,c.jsxs)(c.Fragment,{children:[" ",(0,c.jsx)("ul",{className:"divide-y divide-gray-700 max-h-60 overflow-y-auto pr-2 mb-4 border border-gray-700 rounded-md",children:h.map((e=>(0,c.jsx)("li",{className:"py-2 px-3",children:(0,c.jsx)("p",{className:"text-sm text-text-primary truncate",title:e.email,children:e.email})},e.id)))}),D>1&&(0,c.jsxs)("div",{className:"flex justify-between items-center mt-2 text-sm text-text-secondary",children:[(0,c.jsxs)("span",{children:["Page ",b," of ",D]}),(0,c.jsxs)("div",{className:"space-x-1",children:[(0,c.jsx)(n.A,{size:"sm",variant:"secondary",onClick:()=>E(b-1),disabled:b<=1||y,children:"Previous"}),(0,c.jsx)(n.A,{size:"sm",variant:"secondary",onClick:()=>E(b+1),disabled:b>=D||y,children:"Next"})]})]})]})]})]}):(0,c.jsx)("p",{className:"text-text-secondary italic text-center",children:"Select a segment from the list to see details and contacts."})})]})]})]})}}}]);
//# sourceMappingURL=967.4589c43d.chunk.js.map