"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[33],{1830:(e,a,s)=>{s.d(a,{A:()=>r});var t=s(5043),n=s(8417),i=s(579);const r=e=>{let{isOpen:a,title:s,message:r,confirmText:d="Confirm",cancelText:c="Cancel",onConfirm:l,onCancel:o}=e;const m=(0,t.useRef)(null);return(0,t.useEffect)((()=>{const e=e=>{m.current&&!m.current.contains(e.target)&&o()};return a&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[a,o]),(0,t.useEffect)((()=>{const e=e=>{"Escape"===e.key&&o()};return a&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[a,o]),a?(0,i.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,i.jsxs)("div",{ref:m,className:"bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6 w-full max-w-md mx-4",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:s}),(0,i.jsx)("div",{className:"mb-6 text-gray-300",children:r}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)(n.A,{variant:"secondary",onClick:o,children:c}),(0,i.jsx)(n.A,{variant:"danger",onClick:l,children:d})]})]})}):null}},6033:(e,a,s)=>{s.r(a),s.d(a,{default:()=>u});var t=s(5043),n=s(9291),i=s(8417),r=s(1411),d=s(1830),c=s(4741),l=s(9066),o=s(8231),m=s(6291),p=s(579);const u=()=>{const{id:e}=(0,o.g)(),{user:a}=(0,l.A)(),s=(0,o.Zp)();(0,t.useEffect)((()=>{if(!e||"undefined"===e)return console.log("Invalid campaign ID, redirecting to campaigns list"),void s("/campaigns");console.log("Campaign ID in CampaignRecipients:",e)}),[e,s]);const[u,x]=(0,t.useState)(null),[g,h]=(0,t.useState)([]),[f,j]=(0,t.useState)(!0),[v,b]=(0,t.useState)(!1),[y,C]=(0,t.useState)(""),[N,w]=(0,t.useState)(""),[A,R]=(0,t.useState)(""),[k,E]=(0,t.useState)(""),[S,T]=(0,t.useState)(!1),[J,I]=(0,t.useState)(null),[D,F]=(0,t.useState)(!1);(0,t.useEffect)((()=>{(async()=>{if(e&&"undefined"!==e)try{const a=await m.J.getCampaign(e);x(a.data.campaign);const s=await m.J.getCampaignRecipients(e);h(s.data.campaignRecipients||[]),j(!1)}catch(t){var a,s;console.error("Error fetching campaign or recipients:",t),C((null===(a=t.response)||void 0===a||null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to fetch data"),j(!1)}})()}),[e]);if(f)return(0,p.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,p.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})});if(!u)return(0,p.jsxs)("div",{children:[(0,p.jsx)(n.A,{type:"error",message:"Campaign not found",className:"mb-6"}),(0,p.jsx)(i.A,{onClick:()=>s("/campaigns"),children:"Back to Campaigns"})]});const L="sending"===u.status||"completed"===u.status||"sent"===u.status;return(0,p.jsxs)(r.A,{children:[(0,p.jsxs)("div",{className:"mb-6",children:[(0,p.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("h2",{className:"text-xl font-semibold",children:"Campaign Recipients"}),(0,p.jsx)("p",{className:"text-text-secondary",children:u.name})]}),(0,p.jsx)("div",{children:(0,p.jsx)(i.A,{variant:"secondary",onClick:()=>s("/campaigns"),title:"Go back to the campaigns dashboard",children:"Back to Dashboard"})})]}),y&&(0,p.jsx)(n.A,{type:"error",message:y,onClose:()=>C(""),className:"mb-4"}),N&&(0,p.jsx)(n.A,{type:"success",message:N,className:"mb-4"}),L&&(0,p.jsx)(n.A,{type:"warning",message:"This campaign has already been sent. You cannot add or remove recipients.",className:"mb-4"}),(0,p.jsxs)("div",{className:"mb-6",children:[(0,p.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,p.jsx)("h3",{className:"text-lg font-medium",children:"Add Recipients"}),(0,p.jsxs)("div",{className:"text-sm text-text-secondary",children:["Total Recipients: ",(0,p.jsx)("span",{className:"font-medium",children:u.recipients||0})]})]}),!L&&(0,p.jsx)(p.Fragment,{children:(0,p.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),A.trim())try{b(!0),C("");await m.J.addRecipientToCampaign(e,A);const a=await m.J.getCampaignRecipients(e);h(a.data.campaignRecipients||[]);const s=await m.J.getCampaign(e);x(s.data.campaign),w(`Recipient ${A} added successfully`),R(""),setTimeout((()=>{w("")}),3e3)}catch(n){var s,t;console.error("Error adding recipient:",n),C((null===(s=n.response)||void 0===s||null===(t=s.data)||void 0===t?void 0:t.message)||"Failed to add recipient")}finally{b(!1)}else C("Please enter an email address")},className:"flex space-x-2 mb-4",children:[(0,p.jsx)("div",{className:"flex-1",children:(0,p.jsx)(c.A,{id:"email",name:"email",type:"email",placeholder:"Enter email address",value:A,onChange:e=>R(e.target.value),disabled:v})}),(0,p.jsx)(i.A,{type:"submit",disabled:v||!A.trim(),children:"Add"}),(0,p.jsx)(i.A,{type:"button",variant:"secondary",onClick:()=>T(!0),disabled:v,children:"Bulk Add"})]})})]}),(0,p.jsxs)("div",{children:[(0,p.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Recipients"}),0===g.length?(0,p.jsx)("div",{className:"text-center py-8 bg-gray-800 rounded-md",children:(0,p.jsx)("p",{className:"text-text-secondary",children:"No recipients added yet"})}):(0,p.jsx)("div",{className:"table-container",children:(0,p.jsxs)("table",{className:"table w-full",children:[(0,p.jsx)("thead",{children:(0,p.jsxs)("tr",{children:[(0,p.jsx)("th",{children:"Email"}),(0,p.jsx)("th",{children:"Name"}),(0,p.jsx)("th",{children:"Status"}),(0,p.jsx)("th",{children:"Added"}),!L&&(0,p.jsx)("th",{children:"Actions"})]},"header-row")}),(0,p.jsx)("tbody",{children:g.map((e=>(0,p.jsxs)("tr",{children:[(0,p.jsx)("td",{children:e.email}),(0,p.jsx)("td",{children:e.recipientId&&"object"===typeof e.recipientId?`${e.recipientId.firstName||""} ${e.recipientId.lastName||""}`.trim():"-"}),(0,p.jsx)("td",{children:(0,p.jsx)("span",{className:"px-2 py-1 text-xs rounded "+("pending"===e.status?"bg-gray-700":"sent"===e.status?"bg-green-800":"delivered"===e.status?"bg-blue-800":"opened"===e.status?"bg-purple-800":"clicked"===e.status?"bg-yellow-800":"bounced"===e.status||"complained"===e.status?"bg-red-800":"bg-gray-700"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,p.jsx)("td",{children:new Date(e.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}),!L&&(0,p.jsx)("td",{children:(0,p.jsx)(i.A,{variant:"danger",size:"sm",onClick:()=>{I(e._id),F(!0)},disabled:v,children:"Remove"})})]},e._id)))})]})})]})]}),(0,p.jsx)(d.A,{isOpen:S,title:"Add Multiple Recipients",message:(0,p.jsxs)("div",{children:[(0,p.jsx)("p",{className:"mb-2",children:"Enter email addresses separated by commas, spaces, or new lines:"}),(0,p.jsx)("textarea",{className:"w-full h-40 p-2 bg-gray-800 border border-gray-700 rounded-md text-white",value:k,onChange:e=>E(e.target.value),placeholder:"<EMAIL>, <EMAIL>"})]}),confirmText:v?"Adding...":"Add Recipients",onConfirm:async()=>{if(k.trim())try{b(!0),C("");const a=k.split(/[,\n\s]+/).map((e=>e.trim())).filter((e=>e.length>0)),s=await m.J.addBulkRecipientsToCampaign(e,a),t=await m.J.getCampaignRecipients(e);h(t.data.campaignRecipients||[]);const n=await m.J.getCampaign(e);x(n.data.campaign),w(`${s.data.added.length} recipients added successfully`),E(""),T(!1),setTimeout((()=>{w("")}),3e3)}catch(t){var a,s;console.error("Error adding bulk recipients:",t),C((null===(a=t.response)||void 0===a||null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to add recipients")}finally{b(!1)}else C("Please enter email addresses")},onCancel:()=>{T(!1),E("")}}),(0,p.jsx)(d.A,{isOpen:D,title:"Remove Recipient",message:"Are you sure you want to remove this recipient from the campaign?",confirmText:v?"Removing...":"Remove",onConfirm:async()=>{if(J)try{b(!0),C(""),await m.J.removeRecipientFromCampaign(e,J),h(g.filter((e=>e._id!==J)));const a=await m.J.getCampaign(e);x(a.data.campaign),w("Recipient removed successfully"),setTimeout((()=>{w("")}),3e3)}catch(t){var a,s;console.error("Error removing recipient:",t),C((null===(a=t.response)||void 0===a||null===(s=a.data)||void 0===s?void 0:s.message)||"Failed to remove recipient")}finally{b(!1),F(!1),I(null)}},onCancel:()=>{F(!1),I(null)}})]})}}}]);
//# sourceMappingURL=33.8f2e4e66.chunk.js.map