{"ast": null, "code": "import React,{useState}from'react';import{Link,useLocation}from'react-router-dom';import{useAuth}from'../contexts/AuthContext';// Placeholder Icon component - Replace with actual icons\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Icon=_ref=>{let{name,className}=_ref;return/*#__PURE__*/_jsx(\"i\",{className:`placeholder-icon-${name} inline-block w-5 h-5 ${className||''}`,\"aria-hidden\":\"true\"});};const Sidebar=()=>{var _user$name,_user$name$charAt;const{pathname}=useLocation();const{user,logout}=useAuth();const[isAdvancedOpen,setIsAdvancedOpen]=useState(false);// --- Navigation Items --- \nconst mainNavItems=[{path:'/',name:'Dashboard',icon:'home'},{path:'/campaigns',name:'Campaigns',icon:'mail'},{path:'/email-templates',name:'Templates',icon:'template'},{path:'/contacts',name:'Contacts',icon:'users'},{path:'/analytics',name:'Analytics',icon:'chart-pie'},{path:'/automations',name:'Automations',icon:'arrows-circuit'},{path:'/settings',name:'Settings',icon:'cog'},{path:'/billing',name:'Billing',icon:'credit-card'},{path:'/support',name:'Help & Support',icon:'question-mark-circle'}];const advancedNavItems=[{path:'/ai-content-generator',name:'AI Content',icon:'sparkles'},{path:'/personalization-editor',name:'Personalization',icon:'pencil-alt'},{path:'/interactive-elements',name:'Interactive',icon:'cursor-click'},{path:'/send-time-optimization',name:'Send Time Opt.',icon:'clock'},{path:'/ab-testing',name:'A/B Testing',icon:'beaker'},{path:'/segment-builder',name:'Segments',icon:'view-boards'},{path:'/deliverability-dashboard',name:'Deliverability',icon:'shield-check'},{path:'/mobile-preview',name:'Mobile Preview',icon:'device-mobile'},{path:'/scheduling-automation',name:'Scheduling',icon:'calendar'},{path:'/journey-builder',name:'Journey Builder',icon:'map'},{path:'/integration-marketplace',name:'Integrations',icon:'puzzle'},{path:'/template-recommendations',name:'Recommendations',icon:'light-bulb'},{path:'/data-export-import',name:'Data Export/Import',icon:'database'}];// ------------------------\nconst handleLogout=async()=>{try{await logout();}catch(error){console.error('Logout failed:',error);}};const renderNavLink=item=>{const isActive=item.path==='/'?pathname==='/':pathname.startsWith(item.path);// Futuristic Nav Link Styles\nconst baseClasses=\"flex items-center px-3 py-2 rounded-md transition-colors duration-150 ease-in-out group text-sm font-medium relative\";// Active: Use accent coral text and subtle dark background\nconst activeClasses=\"bg-neutral-light text-accent-coral font-semibold shadow-inner shadow-black/30\";// Inactive: Use primary text color, slightly lighter dark bg hover\nconst inactiveClasses=\"text-text-primary hover:text-white hover:bg-neutral-light\";return/*#__PURE__*/_jsxs(Link,{to:item.path,className:`${baseClasses} ${isActive?activeClasses:inactiveClasses}`,children:[/*#__PURE__*/_jsx(\"span\",{className:`mr-3 flex-shrink-0 w-5 h-5 ${isActive?'text-accent-coral':'text-text-secondary group-hover:text-text-primary'}`,children:/*#__PURE__*/_jsx(Icon,{name:item.icon})}),/*#__PURE__*/_jsx(\"span\",{title:item.name,children:item.name}),isActive&&/*#__PURE__*/_jsx(\"span\",{className:\"absolute left-0 top-0 bottom-0 w-1 bg-accent-coral rounded-r-full\"})]},item.path);};return(/*#__PURE__*/// Main Sidebar Container: Charcoal background, subtle border\n_jsxs(\"div\",{className:\"h-screen w-64 bg-neutral-base text-text-primary flex flex-col border-r border-border shadow-lg z-20 font-sans\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"pl-2 pr-4 border-b border-border h-16 flex pt-2\",children:/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex items-center group\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-primary-blue group-hover:text-accent-coral transition-colors\",children:[/*#__PURE__*/_jsx(Icon,{name:\"paper-airplane\",className:\"w-8 h-8\"}),\" \"]}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold ml-3 /* Increased text size */ bg-gradient-to-r from-primary-blue to-accent-coral  bg-clip-text text-transparent  shadow-md /* Added subtle shadow */ group-hover:scale-105 group-hover:tracking-wide /* Added hover tracking */ transition-all duration-200 ease-in-out\"/* Use transition-all */,children:\"Driftly\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-neutral-light hover:scrollbar-thumb-border scrollbar-track-transparent scrollbar-thumb-rounded\",children:/*#__PURE__*/_jsxs(\"nav\",{className:\"px-3 space-y-1\",children:[mainNavItems.map(renderNavLink),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setIsAdvancedOpen(!isAdvancedOpen),className:\"flex items-center justify-between w-full px-3 py-2 text-text-primary hover:text-white hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-left mt-4 text-sm font-medium group\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-3 text-text-secondary group-hover:text-text-primary\",children:/*#__PURE__*/_jsx(Icon,{name:\"view-grid-add\"})}),/*#__PURE__*/_jsx(\"span\",{children:\"Advanced Features\"})]}),/*#__PURE__*/_jsx(\"svg\",{className:`w-5 h-5 transition-transform text-text-secondary group-hover:text-text-primary ${isAdvancedOpen?'transform rotate-180':''}`,xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 20 20\",fill:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",clipRule:\"evenodd\"})})]}),isAdvancedOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"pl-4 mt-1 ml-3 py-2 text-sm text-text-secondary italic\",children:\"Coming soon...\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-3 border-t border-border\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-9 h-9 rounded-full bg-primary-blue flex items-center justify-center text-sm font-medium text-white\",children:(user===null||user===void 0?void 0:(_user$name=user.name)===null||_user$name===void 0?void 0:(_user$name$charAt=_user$name.charAt(0))===null||_user$name$charAt===void 0?void 0:_user$name$charAt.toUpperCase())||'U'}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-2 overflow-hidden\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-text-primary truncate\",children:(user===null||user===void 0?void 0:user.name)||'User'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-text-secondary truncate\",children:(user===null||user===void 0?void 0:user.email)||'<EMAIL>'})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleLogout,className:\"w-full flex items-center px-3 py-2 text-text-secondary hover:text-text-primary hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-sm font-medium group\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-3 text-text-secondary group-hover:text-text-primary\",children:/*#__PURE__*/_jsx(Icon,{name:\"logout\"})}),/*#__PURE__*/_jsx(\"span\",{children:\"Logout\"})]})]})]}));};export default Sidebar;", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "Icon", "_ref", "name", "className", "Sidebar", "_user$name", "_user$name$charAt", "pathname", "user", "logout", "isAdvancedOpen", "setIsAdvancedOpen", "mainNavItems", "path", "icon", "advancedNavItems", "handleLogout", "error", "console", "renderNavLink", "item", "isActive", "startsWith", "baseClasses", "activeClasses", "inactiveClasses", "to", "children", "title", "map", "onClick", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "char<PERSON>t", "toUpperCase", "email"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/Sidebar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport {\n  Link,\n  useLocation,\n} from 'react-router-dom';\n\nimport { useAuth } from '../contexts/AuthContext';\n\n// Placeholder Icon component - Replace with actual icons\nconst Icon = ({ name, className }: { name: string; className?: string }) => (\n  <i className={`placeholder-icon-${name} inline-block w-5 h-5 ${className || ''}`} aria-hidden=\"true\"></i>\n);\n\nconst Sidebar: React.FC = () => {\n  const { pathname } = useLocation();\n  const { user, logout } = useAuth();\n  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);\n\n  // --- Navigation Items --- \n  const mainNavItems = [\n    { path: '/', name: 'Dashboard', icon: 'home' }, \n    { path: '/campaigns', name: 'Campaigns', icon: 'mail' },\n    { path: '/email-templates', name: 'Templates', icon: 'template' },\n    { path: '/contacts', name: 'Contacts', icon: 'users' },\n    { path: '/analytics', name: 'Analytics', icon: 'chart-pie' },\n    { path: '/automations', name: 'Automations', icon: 'arrows-circuit' }, \n    { path: '/settings', name: 'Settings', icon: 'cog' },             \n    { path: '/billing', name: 'Billing', icon: 'credit-card' },     \n    { path: '/support', name: 'Help & Support', icon: 'question-mark-circle' }, \n  ];\n\n  const advancedNavItems = [\n    { path: '/ai-content-generator', name: 'AI Content', icon: 'sparkles' }, \n    { path: '/personalization-editor', name: 'Personalization', icon: 'pencil-alt' }, \n    { path: '/interactive-elements', name: 'Interactive', icon: 'cursor-click' }, \n    { path: '/send-time-optimization', name: 'Send Time Opt.', icon: 'clock' }, \n    { path: '/ab-testing', name: 'A/B Testing', icon: 'beaker' }, \n    { path: '/segment-builder', name: 'Segments', icon: 'view-boards' }, \n    { path: '/deliverability-dashboard', name: 'Deliverability', icon: 'shield-check' }, \n    { path: '/mobile-preview', name: 'Mobile Preview', icon: 'device-mobile' }, \n    { path: '/scheduling-automation', name: 'Scheduling', icon: 'calendar' }, \n    { path: '/journey-builder', name: 'Journey Builder', icon: 'map' },\n    { path: '/integration-marketplace', name: 'Integrations', icon: 'puzzle' },\n    { path: '/template-recommendations', name: 'Recommendations', icon: 'light-bulb' },\n    { path: '/data-export-import', name: 'Data Export/Import', icon: 'database' },\n  ];\n  // ------------------------\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const renderNavLink = (item: any) => {\n    const isActive = item.path === '/'\n      ? pathname === '/' \n      : pathname.startsWith(item.path);\n      \n    // Futuristic Nav Link Styles\n    const baseClasses = \"flex items-center px-3 py-2 rounded-md transition-colors duration-150 ease-in-out group text-sm font-medium relative\";\n    // Active: Use accent coral text and subtle dark background\n    const activeClasses = \"bg-neutral-light text-accent-coral font-semibold shadow-inner shadow-black/30\"; \n    // Inactive: Use primary text color, slightly lighter dark bg hover\n    const inactiveClasses = \"text-text-primary hover:text-white hover:bg-neutral-light\";\n\n    return (\n      <Link\n        key={item.path}\n        to={item.path}\n        className={`${baseClasses} ${isActive ? activeClasses : inactiveClasses}`}\n      >\n        {/* Icon: Coral when active, light gray otherwise */}\n        <span className={`mr-3 flex-shrink-0 w-5 h-5 ${isActive ? 'text-accent-coral' : 'text-text-secondary group-hover:text-text-primary'}`}>\n           <Icon name={item.icon} />\n        </span>\n        <span title={item.name}>{item.name}</span> \n        {/* Optional: Add subtle glow/marker for active state */} \n        {isActive && <span className=\"absolute left-0 top-0 bottom-0 w-1 bg-accent-coral rounded-r-full\"></span>}\n      </Link>\n    );\n  };\n\n  return (\n    // Main Sidebar Container: Charcoal background, subtle border\n    <div className=\"h-screen w-64 bg-neutral-base text-text-primary flex flex-col border-r border-border shadow-lg z-20 font-sans\">\n      {/* Header: Align left, adjust padding, increase size */}\n      <div className=\"pl-2 pr-4 border-b border-border h-16 flex pt-2\">\n        <Link to=\"/\" className=\"flex items-center group\">\n           {/* Removed Logo Image Placeholder */}\n           <span className=\"text-primary-blue group-hover:text-accent-coral transition-colors\">\n             <Icon name=\"paper-airplane\" className=\"w-8 h-8\" /> {/* Increased icon size */}\n           </span>\n           <h1 \n              className=\"text-3xl font-bold ml-3 /* Increased text size */\n                         bg-gradient-to-r from-primary-blue to-accent-coral \n                         bg-clip-text text-transparent \n                         shadow-md /* Added subtle shadow */\n                         group-hover:scale-105 group-hover:tracking-wide /* Added hover tracking */\n                         transition-all duration-200 ease-in-out\" /* Use transition-all */\n           >\n              Driftly\n           </h1>\n        </Link>\n      </div>\n      \n      {/* Navigation Area */}\n      <div className=\"flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-neutral-light hover:scrollbar-thumb-border scrollbar-track-transparent scrollbar-thumb-rounded\">\n        <nav className=\"px-3 space-y-1\">\n          {mainNavItems.map(renderNavLink)}\n\n          {/* Advanced Features Toggle - Change base color, remove icon hover effects */}\n          <button\n            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}\n            className=\"flex items-center justify-between w-full px-3 py-2 text-text-primary hover:text-white hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-left mt-4 text-sm font-medium group\"\n          >\n            <div className=\"flex items-center\">\n              {/* Change icon color to match inactive nav link icon */}\n              <span className=\"mr-3 text-text-secondary group-hover:text-text-primary\">\n                <Icon name=\"view-grid-add\" />\n              </span>\n              <span>Advanced Features</span>\n            </div>\n             {/* Change icon color to match inactive nav link icon */}\n            <svg \n              className={`w-5 h-5 transition-transform text-text-secondary group-hover:text-text-primary ${isAdvancedOpen ? 'transform rotate-180' : ''}`}\n              xmlns=\"http://www.w3.org/2000/svg\" \n              viewBox=\"0 0 20 20\" \n              fill=\"currentColor\"\n            >\n              <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n\n          {/* Advanced Features List - Replace with Coming Soon */}\n          {isAdvancedOpen && (\n            <div className=\"pl-4 mt-1 ml-3 py-2 text-sm text-text-secondary italic\">\n              Coming soon...\n            </div>\n          )}\n        </nav>\n      </div>\n      \n      {/* User/Logout Section */} \n      <div className=\"p-3 border-t border-border\">\n        <div className=\"flex items-center mb-3\">\n          {/* Avatar: Use primary blue or subtle gray? */}\n          <div className=\"w-9 h-9 rounded-full bg-primary-blue flex items-center justify-center text-sm font-medium text-white\">\n              {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n          </div>\n          <div className=\"ml-2 overflow-hidden\">\n            <p className=\"text-sm font-medium text-text-primary truncate\">{user?.name || 'User'}</p>\n            <p className=\"text-xs text-text-secondary truncate\">{user?.email || '<EMAIL>'}</p>\n          </div>\n        </div>\n        {/* Logout Button */} \n        <button\n          onClick={handleLogout}\n          className=\"w-full flex items-center px-3 py-2 text-text-secondary hover:text-text-primary hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-sm font-medium group\"\n        >\n          <span className=\"mr-3 text-text-secondary group-hover:text-text-primary\">\n            <Icon name=\"logout\" />\n          </span>\n          <span>Logout</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAEvC,OACEC,IAAI,CACJC,WAAW,KACN,kBAAkB,CAEzB,OAASC,OAAO,KAAQ,yBAAyB,CAEjD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,IAAI,CAAGC,IAAA,MAAC,CAAEC,IAAI,CAAEC,SAAgD,CAAC,CAAAF,IAAA,oBACrEJ,IAAA,MAAGM,SAAS,CAAE,oBAAoBD,IAAI,yBAAyBC,SAAS,EAAI,EAAE,EAAG,CAAC,cAAY,MAAM,CAAI,CAAC,EAC1G,CAED,KAAM,CAAAC,OAAiB,CAAGA,CAAA,GAAM,KAAAC,UAAA,CAAAC,iBAAA,CAC9B,KAAM,CAAEC,QAAS,CAAC,CAAGb,WAAW,CAAC,CAAC,CAClC,KAAM,CAAEc,IAAI,CAAEC,MAAO,CAAC,CAAGd,OAAO,CAAC,CAAC,CAClC,KAAM,CAACe,cAAc,CAAEC,iBAAiB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAE3D;AACA,KAAM,CAAAoB,YAAY,CAAG,CACnB,CAAEC,IAAI,CAAE,GAAG,CAAEX,IAAI,CAAE,WAAW,CAAEY,IAAI,CAAE,MAAO,CAAC,CAC9C,CAAED,IAAI,CAAE,YAAY,CAAEX,IAAI,CAAE,WAAW,CAAEY,IAAI,CAAE,MAAO,CAAC,CACvD,CAAED,IAAI,CAAE,kBAAkB,CAAEX,IAAI,CAAE,WAAW,CAAEY,IAAI,CAAE,UAAW,CAAC,CACjE,CAAED,IAAI,CAAE,WAAW,CAAEX,IAAI,CAAE,UAAU,CAAEY,IAAI,CAAE,OAAQ,CAAC,CACtD,CAAED,IAAI,CAAE,YAAY,CAAEX,IAAI,CAAE,WAAW,CAAEY,IAAI,CAAE,WAAY,CAAC,CAC5D,CAAED,IAAI,CAAE,cAAc,CAAEX,IAAI,CAAE,aAAa,CAAEY,IAAI,CAAE,gBAAiB,CAAC,CACrE,CAAED,IAAI,CAAE,WAAW,CAAEX,IAAI,CAAE,UAAU,CAAEY,IAAI,CAAE,KAAM,CAAC,CACpD,CAAED,IAAI,CAAE,UAAU,CAAEX,IAAI,CAAE,SAAS,CAAEY,IAAI,CAAE,aAAc,CAAC,CAC1D,CAAED,IAAI,CAAE,UAAU,CAAEX,IAAI,CAAE,gBAAgB,CAAEY,IAAI,CAAE,sBAAuB,CAAC,CAC3E,CAED,KAAM,CAAAC,gBAAgB,CAAG,CACvB,CAAEF,IAAI,CAAE,uBAAuB,CAAEX,IAAI,CAAE,YAAY,CAAEY,IAAI,CAAE,UAAW,CAAC,CACvE,CAAED,IAAI,CAAE,yBAAyB,CAAEX,IAAI,CAAE,iBAAiB,CAAEY,IAAI,CAAE,YAAa,CAAC,CAChF,CAAED,IAAI,CAAE,uBAAuB,CAAEX,IAAI,CAAE,aAAa,CAAEY,IAAI,CAAE,cAAe,CAAC,CAC5E,CAAED,IAAI,CAAE,yBAAyB,CAAEX,IAAI,CAAE,gBAAgB,CAAEY,IAAI,CAAE,OAAQ,CAAC,CAC1E,CAAED,IAAI,CAAE,aAAa,CAAEX,IAAI,CAAE,aAAa,CAAEY,IAAI,CAAE,QAAS,CAAC,CAC5D,CAAED,IAAI,CAAE,kBAAkB,CAAEX,IAAI,CAAE,UAAU,CAAEY,IAAI,CAAE,aAAc,CAAC,CACnE,CAAED,IAAI,CAAE,2BAA2B,CAAEX,IAAI,CAAE,gBAAgB,CAAEY,IAAI,CAAE,cAAe,CAAC,CACnF,CAAED,IAAI,CAAE,iBAAiB,CAAEX,IAAI,CAAE,gBAAgB,CAAEY,IAAI,CAAE,eAAgB,CAAC,CAC1E,CAAED,IAAI,CAAE,wBAAwB,CAAEX,IAAI,CAAE,YAAY,CAAEY,IAAI,CAAE,UAAW,CAAC,CACxE,CAAED,IAAI,CAAE,kBAAkB,CAAEX,IAAI,CAAE,iBAAiB,CAAEY,IAAI,CAAE,KAAM,CAAC,CAClE,CAAED,IAAI,CAAE,0BAA0B,CAAEX,IAAI,CAAE,cAAc,CAAEY,IAAI,CAAE,QAAS,CAAC,CAC1E,CAAED,IAAI,CAAE,2BAA2B,CAAEX,IAAI,CAAE,iBAAiB,CAAEY,IAAI,CAAE,YAAa,CAAC,CAClF,CAAED,IAAI,CAAE,qBAAqB,CAAEX,IAAI,CAAE,oBAAoB,CAAEY,IAAI,CAAE,UAAW,CAAC,CAC9E,CACD;AAEA,KAAM,CAAAE,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAP,MAAM,CAAC,CAAC,CAChB,CAAE,MAAOQ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACxC,CACF,CAAC,CAED,KAAM,CAAAE,aAAa,CAAIC,IAAS,EAAK,CACnC,KAAM,CAAAC,QAAQ,CAAGD,IAAI,CAACP,IAAI,GAAK,GAAG,CAC9BN,QAAQ,GAAK,GAAG,CAChBA,QAAQ,CAACe,UAAU,CAACF,IAAI,CAACP,IAAI,CAAC,CAElC;AACA,KAAM,CAAAU,WAAW,CAAG,sHAAsH,CAC1I;AACA,KAAM,CAAAC,aAAa,CAAG,+EAA+E,CACrG;AACA,KAAM,CAAAC,eAAe,CAAG,2DAA2D,CAEnF,mBACE1B,KAAA,CAACN,IAAI,EAEHiC,EAAE,CAAEN,IAAI,CAACP,IAAK,CACdV,SAAS,CAAE,GAAGoB,WAAW,IAAIF,QAAQ,CAAGG,aAAa,CAAGC,eAAe,EAAG,CAAAE,QAAA,eAG1E9B,IAAA,SAAMM,SAAS,CAAE,8BAA8BkB,QAAQ,CAAG,mBAAmB,CAAG,mDAAmD,EAAG,CAAAM,QAAA,cACnI9B,IAAA,CAACG,IAAI,EAACE,IAAI,CAAEkB,IAAI,CAACN,IAAK,CAAE,CAAC,CACtB,CAAC,cACPjB,IAAA,SAAM+B,KAAK,CAAER,IAAI,CAAClB,IAAK,CAAAyB,QAAA,CAAEP,IAAI,CAAClB,IAAI,CAAO,CAAC,CAEzCmB,QAAQ,eAAIxB,IAAA,SAAMM,SAAS,CAAC,mEAAmE,CAAO,CAAC,GAVnGiB,IAAI,CAACP,IAWN,CAAC,CAEX,CAAC,CAED,oBACE;AACAd,KAAA,QAAKI,SAAS,CAAC,+GAA+G,CAAAwB,QAAA,eAE5H9B,IAAA,QAAKM,SAAS,CAAC,iDAAiD,CAAAwB,QAAA,cAC9D5B,KAAA,CAACN,IAAI,EAACiC,EAAE,CAAC,GAAG,CAACvB,SAAS,CAAC,yBAAyB,CAAAwB,QAAA,eAE7C5B,KAAA,SAAMI,SAAS,CAAC,mEAAmE,CAAAwB,QAAA,eACjF9B,IAAA,CAACG,IAAI,EAACE,IAAI,CAAC,gBAAgB,CAACC,SAAS,CAAC,SAAS,CAAE,CAAC,IAAC,EAC/C,CAAC,cACPN,IAAA,OACGM,SAAS,CAAC,6RAK0C,yBAAAwB,QAAA,CACtD,SAED,CAAI,CAAC,EACF,CAAC,CACJ,CAAC,cAGN9B,IAAA,QAAKM,SAAS,CAAC,2JAA2J,CAAAwB,QAAA,cACxK5B,KAAA,QAAKI,SAAS,CAAC,gBAAgB,CAAAwB,QAAA,EAC5Bf,YAAY,CAACiB,GAAG,CAACV,aAAa,CAAC,cAGhCpB,KAAA,WACE+B,OAAO,CAAEA,CAAA,GAAMnB,iBAAiB,CAAC,CAACD,cAAc,CAAE,CAClDP,SAAS,CAAC,6MAA6M,CAAAwB,QAAA,eAEvN5B,KAAA,QAAKI,SAAS,CAAC,mBAAmB,CAAAwB,QAAA,eAEhC9B,IAAA,SAAMM,SAAS,CAAC,wDAAwD,CAAAwB,QAAA,cACtE9B,IAAA,CAACG,IAAI,EAACE,IAAI,CAAC,eAAe,CAAE,CAAC,CACzB,CAAC,cACPL,IAAA,SAAA8B,QAAA,CAAM,mBAAiB,CAAM,CAAC,EAC3B,CAAC,cAEN9B,IAAA,QACEM,SAAS,CAAE,kFAAkFO,cAAc,CAAG,sBAAsB,CAAG,EAAE,EAAG,CAC5IqB,KAAK,CAAC,4BAA4B,CAClCC,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,cAAc,CAAAN,QAAA,cAEnB9B,IAAA,SAAMqC,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oHAAoH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAClK,CAAC,EACA,CAAC,CAGR1B,cAAc,eACbb,IAAA,QAAKM,SAAS,CAAC,wDAAwD,CAAAwB,QAAA,CAAC,gBAExE,CAAK,CACN,EACE,CAAC,CACH,CAAC,cAGN5B,KAAA,QAAKI,SAAS,CAAC,4BAA4B,CAAAwB,QAAA,eACzC5B,KAAA,QAAKI,SAAS,CAAC,wBAAwB,CAAAwB,QAAA,eAErC9B,IAAA,QAAKM,SAAS,CAAC,sGAAsG,CAAAwB,QAAA,CAChH,CAAAnB,IAAI,SAAJA,IAAI,kBAAAH,UAAA,CAAJG,IAAI,CAAEN,IAAI,UAAAG,UAAA,kBAAAC,iBAAA,CAAVD,UAAA,CAAYgC,MAAM,CAAC,CAAC,CAAC,UAAA/B,iBAAA,iBAArBA,iBAAA,CAAuBgC,WAAW,CAAC,CAAC,GAAI,GAAG,CAC3C,CAAC,cACNvC,KAAA,QAAKI,SAAS,CAAC,sBAAsB,CAAAwB,QAAA,eACnC9B,IAAA,MAAGM,SAAS,CAAC,gDAAgD,CAAAwB,QAAA,CAAE,CAAAnB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEN,IAAI,GAAI,MAAM,CAAI,CAAC,cACxFL,IAAA,MAAGM,SAAS,CAAC,sCAAsC,CAAAwB,QAAA,CAAE,CAAAnB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE+B,KAAK,GAAI,kBAAkB,CAAI,CAAC,EACxF,CAAC,EACH,CAAC,cAENxC,KAAA,WACE+B,OAAO,CAAEd,YAAa,CACtBb,SAAS,CAAC,uLAAuL,CAAAwB,QAAA,eAEjM9B,IAAA,SAAMM,SAAS,CAAC,wDAAwD,CAAAwB,QAAA,cACtE9B,IAAA,CAACG,IAAI,EAACE,IAAI,CAAC,QAAQ,CAAE,CAAC,CAClB,CAAC,cACPL,IAAA,SAAA8B,QAAA,CAAM,QAAM,CAAM,CAAC,EACb,CAAC,EACN,CAAC,EACH,CAAC,EAEV,CAAC,CAED,cAAe,CAAAvB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}