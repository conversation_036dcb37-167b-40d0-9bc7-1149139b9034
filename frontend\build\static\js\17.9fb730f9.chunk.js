"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[17],{17:(e,t,s)=>{s.r(t),s.d(t,{default:()=>g});var a=s(5043),n=s(9291),i=s(8417),l=s(1411),d=s(1830),c=s(4289),r=s(9066),o=s(8231),m=s(6291),u=s(2104),h=s(579);const g=()=>{var e,t,s;const{user:g}=(0,r.A)(),x=(0,o.Zp)(),[p,j]=(0,a.useState)([]),[v,f]=(0,a.useState)(!0),[y,C]=(0,a.useState)(""),[S,b]=(0,a.useState)(""),[A,N]=(0,a.useState)(!1),[w,_]=(0,a.useState)(null),[k,E]=(0,a.useState)(null),[T,F]=(0,a.useState)(!1),[D,$]=(0,a.useState)(!1),[L,O]=(0,a.useState)(!1),[P,R]=(0,a.useState)(null);(0,a.useEffect)((()=>{sessionStorage.getItem("reloadCampaigns")?(sessionStorage.removeItem("reloadCampaigns"),window.location.reload()):I()}),[]),(0,a.useEffect)((()=>{const e={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_AI_SERVICE_URL:"http://localhost:5000",REACT_APP_API_URL:"https://driftly-ab-175086977.us-east-2.elb.amazonaws.com/api/v1"}.REACT_APP_SOCKET_URL||"http://localhost:3000",t=(0,u.io)(e,{withCredentials:!0});return console.log(`[CampaignList] Attempting to connect socket to ${e}...`),t.on("connect",(()=>{console.log(`[CampaignList] Socket connected with ID: ${t.id}`)})),t.on("disconnect",(e=>{console.log(`[CampaignList] Socket disconnected: ${e}`)})),t.on("connect_error",(e=>{console.error("[CampaignList] Socket connection error:",e)})),t.on("campaignStatusUpdate",(e=>{console.log("[CampaignList] Received campaignStatusUpdate:",e),j((t=>t.map((t=>t._id===e.id?{...t,status:e.status,...e.startedAt&&{startedAt:e.startedAt},...e.completedAt&&{completedAt:e.completedAt},...e.sentAt&&{sentAt:e.sentAt}}:t))))})),()=>{console.log("[CampaignList] Disconnecting socket..."),t.disconnect()}}),[]);const I=async()=>{try{const e=await m.J.getCampaigns();console.log("Campaigns from API:",e.data.campaigns),j(e.data.campaigns)}catch(s){var e,t;console.error("Error loading campaigns:",s),C((null===(e=s.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to load campaigns"),j([])}finally{f(!1)}},M=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),z=e=>{switch(e){case"draft":default:return"bg-gray-700";case"scheduled":return"bg-blue-800";case"sending":return"bg-yellow-800";case"sent":return"bg-green-800";case"failed":return"bg-red-800"}};return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h2",{className:"text-xl font-semibold",children:"Email Campaigns"}),(0,h.jsx)("p",{className:"text-text-secondary",children:"Manage your email campaigns"})]}),(0,h.jsxs)("div",{className:"flex space-x-2",children:[(0,h.jsx)(i.A,{variant:"secondary",onClick:()=>x("/campaigns/domain-setup"),children:"Domain Setup"}),(0,h.jsx)(i.A,{onClick:()=>x("/campaigns/create"),disabled:!(null!==g&&void 0!==g&&null!==(e=g.domain)&&void 0!==e&&e.status)||"active"!==g.domain.status,title:null!==g&&void 0!==g&&null!==(t=g.domain)&&void 0!==t&&t.status&&"active"===g.domain.status?"Create a new campaign":"Verify a domain first",children:"Create Campaign"})]})]}),y&&(0,h.jsx)(n.A,{type:"error",message:y,onClose:()=>C(""),className:"mb-6"}),S&&(0,h.jsx)(n.A,{type:"success",message:S,onClose:()=>b(""),className:"mb-6"}),null!==g&&void 0!==g&&null!==(s=g.domain)&&void 0!==s&&s.status&&"active"===g.domain.status?null:(0,h.jsx)(n.A,{type:"warning",message:(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"font-medium",children:"You need to verify a domain before creating campaigns"}),(0,h.jsx)("p",{className:"mt-1",children:"Please go to Domain Setup to verify your domain first."})]}),className:"mb-6"}),(0,h.jsx)(l.A,{children:v?(0,h.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,h.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):0===p.length?(0,h.jsxs)("div",{className:"text-center py-8",children:[(0,h.jsx)("div",{className:"text-4xl mb-4",children:"\ud83d\udce7"}),(0,h.jsx)("h3",{className:"text-xl font-medium mb-2",children:"No Campaigns Yet"}),(0,h.jsx)("p",{className:"text-text-secondary mb-4",children:"Create your first email campaign to start sending emails to your contacts."}),(0,h.jsx)(i.A,{onClick:()=>x("/campaigns/create"),children:"Create Your First Campaign"})]}):(0,h.jsx)("div",{className:"table-container",children:(0,h.jsxs)("table",{className:"table w-full",children:[(0,h.jsx)("thead",{children:(0,h.jsxs)("tr",{children:[(0,h.jsx)("th",{children:"Name"}),(0,h.jsx)("th",{children:"Subject"}),(0,h.jsx)("th",{children:"Status"}),(0,h.jsx)("th",{children:"Recipients"}),(0,h.jsx)("th",{children:"Created"}),(0,h.jsx)("th",{children:"Scheduled/Sent"}),(0,h.jsx)("th",{children:"Performance"}),(0,h.jsx)("th",{children:"Actions"})]},"header-row")}),(0,h.jsx)("tbody",{children:p.map((e=>{var t,s,a;return(0,h.jsxs)("tr",{children:[(0,h.jsx)("td",{children:e.name}),(0,h.jsx)("td",{children:e.subject}),(0,h.jsx)("td",{children:(0,h.jsx)("span",{className:`px-2 py-1 text-xs rounded ${z(e.status)}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,h.jsx)("td",{children:null!==(t=e.recipientCountActual)&&void 0!==t?t:"-"}),(0,h.jsx)("td",{children:M(e.createdAt)}),(0,h.jsx)("td",{children:e.scheduledFor?"sent"===e.status||"completed"===e.status?M(e.sentAt||e.completedAt||""):M(e.scheduledFor):e.sentAt?M(e.sentAt):(0,h.jsx)("span",{className:"text-text-secondary",children:"-"})}),(0,h.jsx)("td",{children:"sent"===e.status||"completed"===e.status?(0,h.jsxs)("div",{className:"text-sm",children:[(0,h.jsxs)("div",{children:["Opens: ",null!==(s=e.openCount)&&void 0!==s?s:0]}),(0,h.jsxs)("div",{children:["Clicks: ",null!==(a=e.clickCount)&&void 0!==a?a:0]})]}):(0,h.jsx)("span",{className:"text-text-secondary",children:"-"})}),(0,h.jsx)("td",{children:(0,h.jsxs)("div",{className:"flex space-x-2",children:[(0,h.jsx)(i.A,{variant:"secondary",size:"sm",onClick:()=>{const t=e._id;console.log(`[CampaignList] Navigating for ID: ${t}`),"draft"===e.status||"scheduled"===e.status?x(`/campaigns/edit/${t}`):x(`/campaign-summary/${t}`)},disabled:"sending"===e.status,children:"draft"===e.status||"scheduled"===e.status?"Edit":"View"},"edit-view-btn"),("sent"===e.status||"completed"===e.status)&&(0,h.jsx)(i.A,{variant:"secondary",size:"sm",onClick:()=>x(`/campaigns/analytics/${e._id}`),children:"Analytics"},"analytics-btn"),"draft"===e.status&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(i.A,{variant:"secondary",size:"sm",onClick:()=>x(`/campaigns/recipients/${e._id}`),children:"Recipients"},"recipients-btn"),(0,h.jsx)(i.A,{size:"sm",onClick:()=>(e=>{E(e),F(!0)})(e),children:"Send Now"},"send-btn")]}),"draft"===e.status&&(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(i.A,{variant:"secondary",size:"sm",onClick:()=>(e=>{R(e),O(!0)})(e),title:"Schedule this campaign",children:"Schedule"},"schedule-btn-list")}),(0,h.jsx)(i.A,{variant:"danger",size:"sm",onClick:()=>{return t=e._id,_(t),void N(!0);var t},disabled:"sending"===e.status,children:"Delete"},"delete-btn")]})})]},e._id)}))})]})})}),(0,h.jsx)(d.A,{isOpen:A,title:"Delete Campaign",message:"Are you sure you want to delete this campaign? This action cannot be undone.",confirmText:"Delete",onConfirm:async()=>{if(w)try{await m.J.deleteCampaign(w),j(p.filter((e=>e._id!==w))),b("Campaign deleted successfully"),C("")}catch(s){var e,t;console.error("Error deleting campaign:",s),C((null===(e=s.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to delete campaign")}finally{N(!1),_(null)}},onCancel:()=>{N(!1),_(null)}}),(0,h.jsx)(d.A,{isOpen:T,title:"Send Campaign",message:(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{children:"Are you sure you want to send this campaign?"}),(0,h.jsxs)("p",{className:"mt-2 text-blue-400",children:[(0,h.jsx)("strong",{children:"Note:"})," This will send real emails to your recipients using AWS SES."]}),k&&(0,h.jsxs)("div",{className:"mt-4 bg-gray-900 p-3 rounded-md",children:[(0,h.jsxs)("p",{children:[(0,h.jsx)("strong",{children:"Name:"})," ",k.name]}),(0,h.jsxs)("p",{children:[(0,h.jsx)("strong",{children:"Subject:"})," ",k.subject]}),(0,h.jsxs)("p",{children:[(0,h.jsx)("strong",{children:"From:"})," ",k.fromName," <",k.fromEmail,">"]})]}),(0,h.jsx)("p",{className:"mt-4 text-gray-400 text-sm",children:"Make sure your campaign content is ready and has been tested before sending."})]}),confirmText:D?"Sending...":"Send Campaign",onConfirm:async()=>{if(k)try{$(!0),C(""),console.log("Sending campaign with ID:",k._id),await m.J.sendCampaign(k._id),j((e=>e.map((e=>e._id===k._id?{...e,status:"sending"}:e)))),b((0,h.jsxs)("div",{children:[(0,h.jsx)("p",{children:(0,h.jsxs)("strong",{children:['Campaign "',k.name,'" send initiated!']})}),(0,h.jsx)("p",{className:"mt-2 text-sm",children:"Status will update automatically."})]}))}catch(s){var e,t;console.error("Error sending campaign:",s),C((null===(e=s.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to send campaign")}finally{$(!1),F(!1),E(null)}},onCancel:()=>{F(!1),E(null)}}),(0,h.jsx)(c.A,{isOpen:L,onClose:()=>O(!1),campaign:P,onScheduled:e=>{O(!1),R(null),b(e),P&&j((e=>e.map((e=>e._id===P._id?{...e,status:"scheduled",scheduledFor:(new Date).toISOString()}:e))))}})]})}},1830:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(5043),n=s(8417),i=s(579);const l=e=>{let{isOpen:t,title:s,message:l,confirmText:d="Confirm",cancelText:c="Cancel",onConfirm:r,onCancel:o}=e;const m=(0,a.useRef)(null);return(0,a.useEffect)((()=>{const e=e=>{m.current&&!m.current.contains(e.target)&&o()};return t&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[t,o]),(0,a.useEffect)((()=>{const e=e=>{"Escape"===e.key&&o()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[t,o]),t?(0,i.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,i.jsxs)("div",{ref:m,className:"bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6 w-full max-w-md mx-4",children:[(0,i.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:s}),(0,i.jsx)("div",{className:"mb-6 text-gray-300",children:l}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,i.jsx)(n.A,{variant:"secondary",onClick:o,children:c}),(0,i.jsx)(n.A,{variant:"danger",onClick:r,children:d})]})]})}):null}},4289:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(5043),n=s(6291),i=s(9291),l=s(8417),d=s(4741),c=s(9774),r=s(579);const o=e=>{let{isOpen:t,onClose:s,campaign:o,onScheduled:m}=e;const[u,h]=(0,a.useState)(""),[g,x]=(0,a.useState)(!1),[p,j]=(0,a.useState)("");(0,a.useEffect)((()=>{if(t&&o){let t="";if(o.scheduledFor)try{const e=new Date(o.scheduledFor);if(isNaN(e.getTime()))console.warn("[ScheduleModal] Invalid existing schedule date:",o.scheduledFor);else{const s=e.getFullYear(),a=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),i=e.getHours().toString().padStart(2,"0");t=`${s}-${a}-${n}T${i}:${e.getMinutes().toString().padStart(2,"0")}`,console.log(`[ScheduleModal] Using existing schedule: ${o.scheduledFor} -> Formatted: ${t}`)}}catch(e){console.error("[ScheduleModal] Error parsing existing schedule date:",e)}if(!t){const e=new Date(Date.now()+36e5);t=`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}T${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`,console.log(`[ScheduleModal] Setting default local schedule time: ${t}`)}h(t),j("")}}),[t,o]);const v=()=>{x(!1),j(""),h(""),s()};return(0,r.jsxs)(c.a,{isOpen:t,onClose:v,title:`Schedule Campaign: ${(null===o||void 0===o?void 0:o.name)||""}`,children:[(0,r.jsxs)("div",{className:"space-y-4",children:[p&&(0,r.jsx)(i.A,{type:"error",message:p,onClose:()=>j("")}),(0,r.jsx)("p",{className:"text-text-secondary",children:"Select the date and time to start sending this campaign."}),(0,r.jsx)(d.A,{id:"scheduledDateTimeModal",name:"scheduledDateTimeModal",type:"datetime-local",value:u,onChange:e=>h(e.target.value),label:"Scheduled Date & Time",required:!0,className:"w-full"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 -mt-2",children:"Your local timezone will be used."})]}),(0,r.jsxs)("div",{className:"mt-6 flex justify-end gap-3",children:[(0,r.jsx)(l.A,{variant:"secondary",onClick:v,disabled:g,children:"Cancel"}),(0,r.jsx)(l.A,{onClick:async()=>{if(o&&u){x(!0),j("");try{const e=new Date(u).toISOString();await n.J.scheduleCampaign(o._id,e),m(`Campaign "${o.name}" scheduled successfully for ${new Date(u).toLocaleString()}.`)}catch(s){var e,t;console.error("Error scheduling campaign:",s),j((null===(e=s.response)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.message)||"Failed to schedule campaign.")}finally{x(!1)}}else j("Please select a valid date and time.")},disabled:g||!u,children:g?"Scheduling...":"Schedule Campaign"})]})]})}},9774:(e,t,s)=>{s.d(t,{a:()=>c});var a=s(5043),n=s(6018),i=s(6443),l=s(8417),d=s(579);const c=e=>{let{isOpen:t,onClose:s,title:c,children:r,onConfirm:o,confirmText:m="Confirm",confirmVariant:u="primary",cancelText:h="Cancel"}=e;return(0,d.jsx)(n.e,{appear:!0,show:t,as:a.Fragment,children:(0,d.jsxs)(i.lG,{as:"div",className:"relative z-10",onClose:s,children:[(0,d.jsx)(n.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50"})}),(0,d.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,d.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,d.jsx)(n.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,d.jsxs)(i.lG.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all",children:[(0,d.jsx)(i.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-white mb-4",children:c}),(0,d.jsx)("div",{className:"mt-2 text-sm text-gray-300",children:r}),(0,d.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,d.jsx)(l.A,{variant:"secondary",onClick:s,children:h}),o&&(0,d.jsx)(l.A,{variant:u,onClick:o,children:m})]})]})})})})]})})}}}]);
//# sourceMappingURL=17.9fb730f9.chunk.js.map