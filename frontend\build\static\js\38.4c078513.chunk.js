"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[38],{3038:(e,s,a)=>{a.r(s),a.d(s,{default:()=>o});var i=a(5043),l=a(9291),n=a(8417),t=a(1411),r=a(4741),d=a(8231),c=a(6291),m=a(579);const o=()=>{const{id:e}=(0,d.g)(),s=(0,d.Zp)(),[a,o]=((0,d.zy)(),(0,i.useState)(!1)),[u,h]=(0,i.useState)(""),[x,v]=(0,i.useState)(""),[b,y]=(0,i.useState)(null),[g,j]=(0,i.useState)({intervals:[24],unit:"hours"}),[p,f]=(0,i.useState)(1);(0,i.useEffect)((()=>{e&&(async()=>{try{o(!0);const s=await c.J.getCampaign(e);if(y(s),s.emailContents&&Array.isArray(s.emailContents)){const e=s.emailContents.filter((e=>e.html)).length;f(e),e>1&&j((s=>({...s,intervals:Array(e-1).fill(24)})))}o(!1)}catch(i){var s,a;h((null===(s=i.response)||void 0===s||null===(a=s.data)||void 0===a?void 0:a.message)||"Failed to fetch campaign details"),o(!1)}})()}),[e]);const N=e=>{j((s=>({...s,unit:e})))};return(0,m.jsxs)(t.A,{children:[u&&(0,m.jsx)(l.A,{type:"error",message:u,onClose:()=>h(""),className:"mb-4"}),x&&(0,m.jsx)(l.A,{type:"success",message:x,className:"mb-4"}),(0,m.jsxs)("div",{className:"mb-6",children:[(0,m.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Campaign Schedule"}),a?(0,m.jsx)("p",{className:"text-gray-500",children:"Loading campaign details..."}):p<=1?(0,m.jsxs)("div",{className:"bg-blue-50 border border-blue-100 rounded-md p-4 mb-6",children:[(0,m.jsx)("p",{className:"text-blue-800",children:"This campaign has only one email. No scheduling is needed."}),(0,m.jsx)(n.A,{className:"mt-4",onClick:()=>s(`/campaigns/${e}/summary`),children:"Continue to Summary"})]}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-md p-4 mb-6",children:(0,m.jsx)("p",{className:"text-gray-700 mb-2",children:"Set the delay between emails in your sequence. This determines how much time passes before each email is sent."})}),(0,m.jsx)("div",{className:"mb-6",children:(0,m.jsxs)("div",{className:"flex items-center mb-4",children:[(0,m.jsx)("span",{className:"mr-4 text-gray-700",children:"Time unit:"}),(0,m.jsxs)("div",{className:"flex space-x-2",children:[(0,m.jsx)(n.A,{size:"sm",variant:"hours"===g.unit?"primary":"secondary",onClick:()=>N("hours"),children:"Hours"}),(0,m.jsx)(n.A,{size:"sm",variant:"days"===g.unit?"primary":"secondary",onClick:()=>N("days"),children:"Days"})]})]})}),(0,m.jsx)("div",{className:"space-y-4",children:g.intervals.map(((e,s)=>(0,m.jsxs)("div",{className:"flex items-center",children:[(0,m.jsx)("div",{className:"w-20 flex-shrink-0",children:(0,m.jsx)("div",{className:"w-10 h-10 rounded-full bg-blue-600 text-white flex items-center justify-center font-semibold",children:s+1})}),(0,m.jsx)("div",{className:"flex-grow border-t-2 border-dashed border-gray-300"}),(0,m.jsx)("div",{className:"w-20 flex-shrink-0",children:(0,m.jsx)("div",{className:"w-10 h-10 rounded-full bg-blue-600 text-white flex items-center justify-center font-semibold",children:s+2})}),(0,m.jsx)("div",{className:"ml-4 w-40",children:(0,m.jsx)(r.A,{id:`interval-${s}`,name:`interval-${s}`,type:"number",value:e.toString(),onChange:e=>((e,s)=>{const a=parseInt(s)||0,i=Math.max(1,a),l=[...g.intervals];l[e]=i,j((e=>({...e,intervals:l})))})(s,e.target.value),label:"",className:"mb-0"})}),(0,m.jsx)("div",{className:"ml-2 text-gray-700",children:g.unit})]},s)))}),(0,m.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,m.jsx)(n.A,{variant:"secondary",onClick:()=>s(-1),disabled:a,children:"Back"}),(0,m.jsx)(n.A,{onClick:async()=>{try{o(!0),h("");const a={emailIntervals:g.intervals.map((e=>({delay:e,unit:g.unit})))};await c.J.updateCampaign(e,{schedule:a}),v("Campaign schedule saved successfully!"),o(!1),setTimeout((()=>{s(`/campaigns/${e}/summary`)}),1500)}catch(l){var a,i;h((null===(a=l.response)||void 0===a||null===(i=a.data)||void 0===i?void 0:i.message)||"Failed to save schedule"),o(!1)}},disabled:a,children:a?"Saving...":"Save & Continue"})]})]})]})]})}}}]);
//# sourceMappingURL=38.4c078513.chunk.js.map