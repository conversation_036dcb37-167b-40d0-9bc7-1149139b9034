import dotenv from 'dotenv';
import mongoose from 'mongoose';

import Template from '../models/template.model';

// Load environment variables
dotenv.config();

/**
 * <PERSON>ript to clear all existing templates from the database
 */
const clearTemplates = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/driftly');
    console.log('Connected to MongoDB');

    // Get the count of templates before deletion
    const count = await Template.countDocuments();
    console.log(`Found ${count} templates in the database`);

    // Delete all templates
    const result = await Template.deleteMany({});
    
    console.log(`✅ Successfully deleted ${result.deletedCount} templates`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ Error clearing templates:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
};

// Run the script if this file is executed directly
if (require.main === module) {
  clearTemplates();
}

export default clearTemplates; 