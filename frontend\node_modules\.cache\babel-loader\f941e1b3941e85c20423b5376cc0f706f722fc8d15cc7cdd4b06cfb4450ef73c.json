{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\MjmlEditor.tsx\",\n  _s = $RefreshSig$();\nimport 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\n\n// Optional: Import a preset like grapesjs-preset-newsletter if desired for more features\n// import 'grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.min.css';\n// import grapesjsNewsletter from 'grapesjs-preset-newsletter';\nimport React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';\nimport grapesjs from 'grapesjs';\n\n// @ts-ignore - grapesjs-mjml lacks official types // REMOVED\n// import grapesjsMjml from 'grapesjs-mjml'; // REMOVED\n\n// Define the Ref type for exposing editor methods\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Basic inline CSS styles for email compatibility\nconst emailBodyStyle = `\n  body {\n    margin: 0;\n    padding: 0;\n    background-color: #f4f4f4; /* Example background */\n    font-family: Arial, Helvetica, sans-serif;\n    line-height: 1.5;\n  }\n  table {\n    border-collapse: collapse;\n    table-layout: fixed; /* Helps Outlook */\n  }\n  td {\n    word-wrap: break-word;\n    vertical-align: top; /* Default alignment */\n  }\n  img {\n    border: 0;\n    outline: none;\n    text-decoration: none;\n    -ms-interpolation-mode: bicubic; /* Improve image rendering in Outlook */\n     max-width: 100%; /* Basic responsive image */\n     height: auto;\n  }\n  a {\n     color: #007bff; /* Example link color */\n     text-decoration: underline;\n  }\n  .container {\n     max-width: 600px; /* Common email width */\n     margin: 0 auto;\n     background-color: #ffffff;\n  }\n  /* Add more essential email styles */\n`;\nconst defaultHtmlContent = `\n<table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n  <tr>\n    <td align=\"center\">\n      <table class=\"container\" width=\"600\" border=\"0\" cellpadding=\"20\" cellspacing=\"0\">\n        <tr>\n          <td>\n             <div data-gjs-type=\"text\">Start designing your email here! Drag blocks from the left panel.</div>\n          </td>\n        </tr>\n      </table>\n    </td>\n  </tr>\n</table>\n`;\nconst HtmlEmailEditor = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(// RENAMED MjmlEditor\n_c = _s(({\n  initialHtml = '',\n  onSave,\n  height = '70vh'\n}, ref) => {\n  _s();\n  // REMOVED initialMjml\n  const editorRef = useRef(null);\n  const grapesEditor = useRef(null);\n\n  // Initialize GrapesJS Editor\n  useEffect(() => {\n    if (!editorRef.current) return; // Check for DOM element\n\n    // Always destroy and recreate the editor to ensure consistent behavior\n    if (grapesEditor.current) {\n      console.log(\"[HtmlEmailEditor] Cleaning up previous editor instance\"); // RENAMED\n      grapesEditor.current.destroy();\n      grapesEditor.current = null;\n    }\n    console.log(\"[HtmlEmailEditor] Initializing editor with props:\", {\n      // RENAMED\n      hasHtml: !!initialHtml,\n      htmlLength: (initialHtml === null || initialHtml === void 0 ? void 0 : initialHtml.length) || 0\n    });\n    try {\n      const editor = grapesjs.init({\n        container: editorRef.current,\n        fromElement: false,\n        height: String(height),\n        width: 'auto',\n        storageManager: false,\n        // Disable default storage manager\n        // plugins: [grapesjsMjml], // REMOVED MJML plugin\n        // pluginsOpts: { // REMOVED MJML options\n        //   'grapesjs-mjml': {\n        //     // MJML plugin options (optional)\n        //     // columnsPadding: '0px',\n        //      useXmlParser: true, // Use the faster XML parser\n        //      resetBlocks: false, // Try keeping default GrapesJS blocks\n        //      // ... other options\n        //   }\n        // },\n        // ADDED: Canvas styling for email context\n        canvas: {\n          styles: ['https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' // Example for icons if needed\n          // Add other necessary CSS links if needed\n          ],\n          // Add basic CSS directly to the iframe head for email-like rendering\n          frameStyle: emailBodyStyle\n        },\n        // ADDED: Block Manager configuration\n        blockManager: {\n          appendTo: '',\n          // Or specify a DOM element selector\n          blocks: [\n          // Basic Content Blocks\n          {\n            id: 'text',\n            label: 'Text',\n            icon: '<i class=\"fa fa-font\" style=\"font-size: 24px;\"></i>',\n            // Example Font Awesome icon\n            category: 'Content',\n            content: {\n              type: 'text',\n              content: '<p>Insert your text here</p>',\n              style: {\n                padding: '10px'\n              },\n              activeOnRender: true\n            }\n          }, {\n            id: 'image',\n            label: 'Image',\n            icon: '<i class=\"fa fa-image\" style=\"font-size: 24px;\"></i>',\n            category: 'Content',\n            select: true,\n            // Open asset manager on drop\n            content: {\n              type: 'image',\n              style: {\n                color: 'black',\n                width: '100%'\n              },\n              // Ensure images are responsive by default\n              activeOnRender: true\n            }\n            // Optional: Add trigger for custom image upload\n            // activate: () => editor.runCommand('open-assets', {\n            //   // your custom asset manager options\n            // }),\n          }, {\n            id: 'button',\n            label: 'Button',\n            icon: '<i class=\"fa fa-link\" style=\"font-size: 24px;\"></i>',\n            category: 'Content',\n            content: `<a href=\"#\" style=\"display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;\" data-gjs-type=\"link\">Button Text</a>`\n          }, {\n            id: 'divider',\n            label: 'Divider',\n            icon: '<i class=\"fa fa-minus\" style=\"font-size: 24px;\"></i>',\n            category: 'Layout',\n            content: '<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\"><tr><td style=\"padding: 10px 0;\"><hr style=\"border: none; border-top: 1px solid #cccccc;\"></td></tr></table>'\n          }, {\n            id: 'spacer',\n            label: 'Spacer',\n            icon: '<i class=\"fa fa-arrows-alt-v\" style=\"font-size: 24px;\"></i>',\n            category: 'Layout',\n            content: '<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\"><tr><td style=\"height: 20px;\">&nbsp;</td></tr></table>'\n          },\n          // Layout Blocks (using tables for email compatibility)\n          {\n            id: 'section',\n            // A basic wrapper section (like mj-section)\n            label: 'Section',\n            icon: '<i class=\"fa fa-object-group\" style=\"font-size: 24px;\"></i>',\n            category: 'Layout',\n            content: `<table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"section-wrapper\">\n                             <tr>\n                               <td align=\"center\" style=\"padding: 10px 0;\"> <!-- Outer padding -->\n                                 <table width=\"600\" border=\"0\" cellpadding=\"10\" cellspacing=\"0\" class=\"container\" style=\"background-color:#ffffff;\"> <!-- Inner container -->\n                                    <tr><td data-gjs-droppable=\"true\">Drop columns or content here</td></tr>\n                                 </table>\n                               </td>\n                             </tr>\n                           </table>`\n          }, {\n            id: 'column-1',\n            label: '1 Column',\n            icon: '<i class=\"fa fa-square\" style=\"font-size: 24px;\"></i>',\n            category: 'Layout',\n            content: `<table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\"><tr><td style=\"padding: 5px;\" data-gjs-droppable=\"true\"></td></tr></table>`\n          }, {\n            id: 'column-2',\n            label: '2 Columns',\n            icon: '<i class=\"fa fa-columns\" style=\"font-size: 24px;\"></i>',\n            category: 'Layout',\n            // Use VML for Outlook compatibility - more complex, basic table for now\n            content: `<table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n                              <tr>\n                                <td width=\"50%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\n                                <td width=\"50%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\n                              </tr>\n                           </table>`\n          }, {\n            id: 'column-3',\n            label: '3 Columns',\n            icon: '<i class=\"fa fa-th\" style=\"font-size: 24px;\"></i>',\n            category: 'Layout',\n            content: `<table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n                             <tr>\n                               <td width=\"33.33%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\n                               <td width=\"33.33%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\n                               <td width=\"33.33%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\n                             </tr>\n                           </table>`\n          }\n          // Add more complex pre-designed sections if needed\n          ]\n        },\n        // Optional: Configure panels to simplify UI (example: hide some default buttons)\n        // panelManager: {\n        //    panels: [ /* Define your panels here */ ],\n        //    // Example: Remove the export template button\n        //    // see: https://grapesjs.com/docs/modules/Panels.html#configuration\n        // },\n        // Optional: Configure Style Manager for email-safe CSS properties\n        styleManager: {\n          sectors: [{\n            // Example Sector for Typography\n            name: 'Typography',\n            open: false,\n            buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align'],\n            // Define email-safe options\n            properties: [{\n              name: 'Font',\n              property: 'font-family',\n              type: 'select',\n              defaults: 'Arial, Helvetica, sans-serif',\n              options: [/* web safe fonts */]\n            }, {\n              name: 'Align',\n              property: 'text-align',\n              type: 'radio',\n              defaults: 'left',\n              options: [/* left, center, right */]\n            }\n            // ... other typography props\n            ]\n          }, {\n            // Example Sector for Padding/Margin (use padding mostly for emails)\n            name: 'Spacing',\n            open: false,\n            buildProps: ['padding-top', 'padding-right', 'padding-bottom', 'padding-left'],\n            properties: [\n            // Define pixel-based inputs for simplicity\n            {\n              name: 'Top',\n              property: 'padding-top',\n              type: 'integer',\n              units: ['px'],\n              defaults: 0\n            }\n            // ... other padding props\n            ]\n          }\n          // ... add sectors for Background, Borders etc. with email-safe properties\n          ]\n        }\n        // Optional: Configure Asset Manager (for images)\n        // assetManager: {\n        //   // Configuration for your asset manager (upload endpoint, etc.)\n        // },\n      });\n\n      // Make sure the editor was initialized properly\n      if (!editor) {\n        console.error(\"[HtmlEmailEditor] Failed to initialize editor\"); // RENAMED\n        return;\n      }\n      grapesEditor.current = editor;\n\n      // REMOVED MJML Command registrations\n\n      // Use a small timeout to ensure editor is fully initialized\n      setTimeout(() => {\n        if (!grapesEditor.current) {\n          console.error(\"[HtmlEmailEditor] Editor instance not available after timeout\"); // RENAMED\n          return;\n        }\n\n        // Verify the editor's components API is available\n        if (!grapesEditor.current.setComponents) {\n          console.error(\"[HtmlEmailEditor] Editor's setComponents method is not available\"); // RENAMED\n          return;\n        }\n        try {\n          // Load initial HTML content\n          if (initialHtml) {\n            console.log(\"[HtmlEmailEditor] Loading initial HTML:\", initialHtml.substring(0, 100) + \"...\"); // RENAMED\n            try {\n              grapesEditor.current.setComponents(initialHtml);\n              console.log(\"[HtmlEmailEditor] Successfully loaded HTML content\"); // RENAMED\n            } catch (e) {\n              console.error(\"[HtmlEmailEditor] Error loading initial HTML:\", e); // RENAMED\n              // Load default if initial fails\n              grapesEditor.current.setComponents(defaultHtmlContent);\n            }\n          } else {\n            // Load default HTML template if nothing is provided\n            console.log(\"[HtmlEmailEditor] No content provided, loading default HTML template\"); // RENAMED\n            grapesEditor.current.setComponents(defaultHtmlContent);\n          }\n        } catch (error) {\n          console.error(\"[HtmlEmailEditor] Error in content loading phase:\", error); // RENAMED\n        }\n      }, 100);\n\n      // Declare timeout variable in outer scope so it's accessible in cleanup function\n      let saveTimeout;\n      let isSaving = false; // Flag to prevent multiple simultaneous save operations\n\n      // Attach save listener with debounce\n      editor.on('change:changesCount', () => {\n        // Only proceed if onSave exists, editor exists, and not already saving\n        if (onSave && editor && !isSaving && editor.getDirtyCount() > 0) {\n          // Clear existing timeout\n          if (saveTimeout) clearTimeout(saveTimeout);\n\n          // Set a new timeout\n          saveTimeout = setTimeout(() => {\n            try {\n              isSaving = true; // Set saving flag\n              const currentHtml = editor.getHtml() || '';\n              const currentCss = editor.getCss({\n                avoidPrototypes: true\n              }) || ''; // Get CSS\n\n              // Combine HTML and CSS for a self-contained email\n              // Inject CSS into a <style> tag in the <head>\n              let finalHtml = currentHtml;\n              if (currentCss) {\n                const styleTag = `<style type=\"text/css\">${currentCss}</style>`;\n                if (finalHtml.includes('</head>')) {\n                  finalHtml = finalHtml.replace('</head>', `${styleTag}</head>`);\n                } else if (finalHtml.includes('<body>')) {\n                  // Fallback if no head tag (less likely)\n                  finalHtml = finalHtml.replace('<body>', `<head>${styleTag}</head><body>`);\n                } else {\n                  // Fallback if no head or body (very unlikely for valid HTML)\n                  finalHtml = `<head>${styleTag}</head>${finalHtml}`;\n                }\n              }\n\n              // Optionally use Juice or similar inliner here if needed, but GrapesJS handles some inlining\n              // const inlinedHtml = juice(finalHtml); // Requires juice library\n\n              console.log(\"[HtmlEmailEditor] Content changed, attempting to call onSave...\"); // RENAMED\n\n              // Don't save if we have no meaningful content (adjust check as needed)\n              if (!finalHtml.replace(/<[^>]*>/g, '').trim() && !finalHtml.includes('<img')) {\n                console.log(\"[HtmlEmailEditor] No significant content to save, skipping save.\"); // RENAMED\n                isSaving = false;\n                return;\n              }\n              onSave(finalHtml); // Pass the combined HTML\n              editor.setDirtyCount(0); // Reset dirty count after successful save\n              console.log(\"[HtmlEmailEditor] onSave callback executed.\"); // RENAMED\n            } catch (error) {\n              console.error(\"[HtmlEmailEditor] Error during editor change listener:\", error); // RENAMED\n            } finally {\n              isSaving = false; // Reset flag whether save succeeded or failed\n            }\n          }, 1000); // Increased debounce to 1000ms\n        }\n      });\n\n      // Return cleanup function\n      return () => {\n        if (saveTimeout) clearTimeout(saveTimeout);\n        if (grapesEditor.current) {\n          try {\n            grapesEditor.current.destroy();\n          } catch (destroyError) {\n            console.error(\"[HtmlEmailEditor] Error during editor cleanup:\", destroyError); // RENAMED\n          }\n          grapesEditor.current = null;\n        }\n      };\n    } catch (initError) {\n      console.error(\"[HtmlEmailEditor] Critical error during editor initialization:\", initError); // RENAMED\n    }\n  }, [initialHtml, height, onSave]); // Rerun if initial content or dimensions change\n\n  // Expose save method via ref\n  useImperativeHandle(ref, () => ({\n    save: async () => {\n      let finalHtml = '';\n      if (grapesEditor.current) {\n        try {\n          const editor = grapesEditor.current;\n          const currentHtml = editor.getHtml() || '';\n          const currentCss = editor.getCss({\n            avoidPrototypes: true\n          }) || '';\n\n          // Combine HTML and CSS\n          finalHtml = currentHtml;\n          if (currentCss) {\n            const styleTag = `<style type=\"text/css\">${currentCss}</style>`;\n            if (finalHtml.includes('</head>')) {\n              finalHtml = finalHtml.replace('</head>', `${styleTag}</head>`);\n            } else if (finalHtml.includes('<body>')) {\n              finalHtml = finalHtml.replace('<body>', `<head>${styleTag}</head><body>`);\n            } else {\n              finalHtml = `<head>${styleTag}</head>${finalHtml}`;\n            }\n          }\n          // Optional: Add Juice inlining here too if needed for manual save\n          // finalHtml = juice(finalHtml);\n\n          console.log(\"[HtmlEmailEditor] Manual Save - HTML generated.\"); // RENAMED\n        } catch (saveErr) {\n          console.error(\"[HtmlEmailEditor] Manual Save - Error generating HTML:\", saveErr); // RENAMED\n        }\n      } else {\n        console.error(\"[HtmlEmailEditor] Manual Save - Editor not available.\"); // RENAMED\n      }\n\n      // Return only HTML\n      return {\n        html: finalHtml\n      };\n    },\n    getEditor: () => grapesEditor.current\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: editorRef,\n    style: {\n      height: height\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 437,\n    columnNumber: 12\n  }, this);\n}, \"qr1qkDeKnw5h2q20lqEB8SlN9eE=\")), \"qr1qkDeKnw5h2q20lqEB8SlN9eE=\");\n\n// Assign display name for debugging\n_c2 = HtmlEmailEditor;\nHtmlEmailEditor.displayName = 'HtmlEmailEditor'; // RENAMED\n\nexport default HtmlEmailEditor; // RENAMED\nvar _c, _c2;\n$RefreshReg$(_c, \"HtmlEmailEditor$forwardRef\");\n$RefreshReg$(_c2, \"HtmlEmailEditor\");", "map": {"version": 3, "names": ["React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "emailBodyStyle", "defaultHtmlContent", "HtmlEmailEditor", "_s", "_c", "initialHtml", "onSave", "height", "ref", "editor<PERSON><PERSON>", "grapesEditor", "current", "console", "log", "destroy", "hasHtml", "htmlLength", "length", "editor", "init", "container", "fromElement", "String", "width", "storageManager", "canvas", "styles", "frameStyle", "blockManager", "appendTo", "blocks", "id", "label", "icon", "category", "content", "type", "style", "padding", "activeOnRender", "select", "color", "styleManager", "sectors", "name", "open", "buildProps", "properties", "property", "defaults", "options", "units", "error", "setTimeout", "setComponents", "substring", "e", "saveTimeout", "isSaving", "on", "getDirtyCount", "clearTimeout", "currentHtml", "getHtml", "currentCss", "getCss", "avoidPrototypes", "finalHtml", "styleTag", "includes", "replace", "trim", "setDirtyCount", "destroyError", "initError", "save", "saveErr", "html", "getEditor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/MjmlEditor.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\n// Optional: Import a preset like grapesjs-preset-newsletter if desired for more features\r\n// import 'grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.min.css';\r\n// import grapesjsNewsletter from 'grapesjs-preset-newsletter';\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n\r\n// @ts-ignore - grapesjs-mjml lacks official types // REMOVED\r\n// import grapesjsMjml from 'grapesjs-mjml'; // REMOVED\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface HtmlEmailEditorRef { // RENAMED MjmlEditorRef\r\n  save: () => Promise<{ html: string }>; // UPDATED Return type\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface HtmlEmailEditorProps { // RENAMED MjmlEditorProps\r\n  initialHtml?: string; // Use initialHtml as the primary source\r\n  onSave?: (html: string) => void; // UPDATED Signature\r\n  height?: string | number;\r\n}\r\n\r\n// Basic inline CSS styles for email compatibility\r\nconst emailBodyStyle = `\r\n  body {\r\n    margin: 0;\r\n    padding: 0;\r\n    background-color: #f4f4f4; /* Example background */\r\n    font-family: Arial, Helvetica, sans-serif;\r\n    line-height: 1.5;\r\n  }\r\n  table {\r\n    border-collapse: collapse;\r\n    table-layout: fixed; /* Helps Outlook */\r\n  }\r\n  td {\r\n    word-wrap: break-word;\r\n    vertical-align: top; /* Default alignment */\r\n  }\r\n  img {\r\n    border: 0;\r\n    outline: none;\r\n    text-decoration: none;\r\n    -ms-interpolation-mode: bicubic; /* Improve image rendering in Outlook */\r\n     max-width: 100%; /* Basic responsive image */\r\n     height: auto;\r\n  }\r\n  a {\r\n     color: #007bff; /* Example link color */\r\n     text-decoration: underline;\r\n  }\r\n  .container {\r\n     max-width: 600px; /* Common email width */\r\n     margin: 0 auto;\r\n     background-color: #ffffff;\r\n  }\r\n  /* Add more essential email styles */\r\n`;\r\n\r\nconst defaultHtmlContent = `\r\n<table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\r\n  <tr>\r\n    <td align=\"center\">\r\n      <table class=\"container\" width=\"600\" border=\"0\" cellpadding=\"20\" cellspacing=\"0\">\r\n        <tr>\r\n          <td>\r\n             <div data-gjs-type=\"text\">Start designing your email here! Drag blocks from the left panel.</div>\r\n          </td>\r\n        </tr>\r\n      </table>\r\n    </td>\r\n  </tr>\r\n</table>\r\n`;\r\n\r\n\r\nconst HtmlEmailEditor = forwardRef<HtmlEmailEditorRef, HtmlEmailEditorProps>( // RENAMED MjmlEditor\r\n  ({ initialHtml = '', onSave, height = '70vh' }, ref) => { // REMOVED initialMjml\r\n    const editorRef = useRef<HTMLDivElement>(null);\r\n    const grapesEditor = useRef<Editor | null>(null);\r\n\r\n    // Initialize GrapesJS Editor\r\n    useEffect(() => {\r\n      if (!editorRef.current) return; // Check for DOM element\r\n\r\n      // Always destroy and recreate the editor to ensure consistent behavior\r\n      if (grapesEditor.current) {\r\n        console.log(\"[HtmlEmailEditor] Cleaning up previous editor instance\"); // RENAMED\r\n        grapesEditor.current.destroy();\r\n        grapesEditor.current = null;\r\n      }\r\n\r\n      console.log(\"[HtmlEmailEditor] Initializing editor with props:\", { // RENAMED\r\n        hasHtml: !!initialHtml,\r\n        htmlLength: initialHtml?.length || 0\r\n      });\r\n\r\n      try {\r\n        const editor = grapesjs.init({\r\n          container: editorRef.current,\r\n          fromElement: false,\r\n          height: String(height),\r\n          width: 'auto',\r\n          storageManager: false, // Disable default storage manager\r\n          // plugins: [grapesjsMjml], // REMOVED MJML plugin\r\n          // pluginsOpts: { // REMOVED MJML options\r\n          //   'grapesjs-mjml': {\r\n          //     // MJML plugin options (optional)\r\n          //     // columnsPadding: '0px',\r\n          //      useXmlParser: true, // Use the faster XML parser\r\n          //      resetBlocks: false, // Try keeping default GrapesJS blocks\r\n          //      // ... other options\r\n          //   }\r\n          // },\r\n          // ADDED: Canvas styling for email context\r\n          canvas: {\r\n            styles: [\r\n              'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css', // Example for icons if needed\r\n              // Add other necessary CSS links if needed\r\n            ],\r\n             // Add basic CSS directly to the iframe head for email-like rendering\r\n            frameStyle: emailBodyStyle,\r\n          },\r\n          // ADDED: Block Manager configuration\r\n          blockManager: {\r\n            appendTo: '', // Or specify a DOM element selector\r\n            blocks: [\r\n              // Basic Content Blocks\r\n              {\r\n                id: 'text',\r\n                label: 'Text',\r\n                icon: '<i class=\"fa fa-font\" style=\"font-size: 24px;\"></i>', // Example Font Awesome icon\r\n                category: 'Content',\r\n                content: {\r\n                  type: 'text',\r\n                  content: '<p>Insert your text here</p>',\r\n                  style: { padding: '10px' },\r\n                  activeOnRender: true,\r\n                },\r\n              },\r\n              {\r\n                id: 'image',\r\n                label: 'Image',\r\n                icon: '<i class=\"fa fa-image\" style=\"font-size: 24px;\"></i>',\r\n                category: 'Content',\r\n                select: true, // Open asset manager on drop\r\n                content: {\r\n                  type: 'image',\r\n                  style: { color: 'black', width: '100%' }, // Ensure images are responsive by default\r\n                  activeOnRender: true,\r\n                },\r\n                // Optional: Add trigger for custom image upload\r\n                // activate: () => editor.runCommand('open-assets', {\r\n                //   // your custom asset manager options\r\n                // }),\r\n              },\r\n              {\r\n                id: 'button',\r\n                label: 'Button',\r\n                icon: '<i class=\"fa fa-link\" style=\"font-size: 24px;\"></i>',\r\n                category: 'Content',\r\n                content: `<a href=\"#\" style=\"display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; text-align: center;\" data-gjs-type=\"link\">Button Text</a>`\r\n              },\r\n              {\r\n                id: 'divider',\r\n                label: 'Divider',\r\n                icon: '<i class=\"fa fa-minus\" style=\"font-size: 24px;\"></i>',\r\n                category: 'Layout',\r\n                content: '<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\"><tr><td style=\"padding: 10px 0;\"><hr style=\"border: none; border-top: 1px solid #cccccc;\"></td></tr></table>'\r\n              },\r\n              {\r\n                id: 'spacer',\r\n                label: 'Spacer',\r\n                icon: '<i class=\"fa fa-arrows-alt-v\" style=\"font-size: 24px;\"></i>',\r\n                category: 'Layout',\r\n                content: '<table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\"><tr><td style=\"height: 20px;\">&nbsp;</td></tr></table>'\r\n              },\r\n\r\n              // Layout Blocks (using tables for email compatibility)\r\n              {\r\n                id: 'section', // A basic wrapper section (like mj-section)\r\n                label: 'Section',\r\n                icon: '<i class=\"fa fa-object-group\" style=\"font-size: 24px;\"></i>',\r\n                category: 'Layout',\r\n                content: `<table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"section-wrapper\">\r\n                             <tr>\r\n                               <td align=\"center\" style=\"padding: 10px 0;\"> <!-- Outer padding -->\r\n                                 <table width=\"600\" border=\"0\" cellpadding=\"10\" cellspacing=\"0\" class=\"container\" style=\"background-color:#ffffff;\"> <!-- Inner container -->\r\n                                    <tr><td data-gjs-droppable=\"true\">Drop columns or content here</td></tr>\r\n                                 </table>\r\n                               </td>\r\n                             </tr>\r\n                           </table>`,\r\n              },\r\n              {\r\n                 id: 'column-1',\r\n                 label: '1 Column',\r\n                 icon: '<i class=\"fa fa-square\" style=\"font-size: 24px;\"></i>',\r\n                 category: 'Layout',\r\n                 content: `<table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\"><tr><td style=\"padding: 5px;\" data-gjs-droppable=\"true\"></td></tr></table>`,\r\n              },\r\n              {\r\n                 id: 'column-2',\r\n                 label: '2 Columns',\r\n                 icon: '<i class=\"fa fa-columns\" style=\"font-size: 24px;\"></i>',\r\n                 category: 'Layout',\r\n                 // Use VML for Outlook compatibility - more complex, basic table for now\r\n                 content: `<table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\r\n                              <tr>\r\n                                <td width=\"50%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\r\n                                <td width=\"50%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\r\n                              </tr>\r\n                           </table>`,\r\n              },\r\n               {\r\n                 id: 'column-3',\r\n                 label: '3 Columns',\r\n                 icon: '<i class=\"fa fa-th\" style=\"font-size: 24px;\"></i>',\r\n                 category: 'Layout',\r\n                 content: `<table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\r\n                             <tr>\r\n                               <td width=\"33.33%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\r\n                               <td width=\"33.33%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\r\n                               <td width=\"33.33%\" style=\"padding: 5px; vertical-align: top;\" data-gjs-droppable=\"true\"></td>\r\n                             </tr>\r\n                           </table>`,\r\n               },\r\n               // Add more complex pre-designed sections if needed\r\n            ],\r\n          },\r\n          // Optional: Configure panels to simplify UI (example: hide some default buttons)\r\n          // panelManager: {\r\n          //    panels: [ /* Define your panels here */ ],\r\n          //    // Example: Remove the export template button\r\n          //    // see: https://grapesjs.com/docs/modules/Panels.html#configuration\r\n          // },\r\n          // Optional: Configure Style Manager for email-safe CSS properties\r\n           styleManager: {\r\n             sectors: [\r\n              { // Example Sector for Typography\r\n                 name: 'Typography',\r\n                 open: false,\r\n                 buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align'],\r\n                 // Define email-safe options\r\n                 properties: [\r\n                   { name: 'Font', property: 'font-family', type: 'select', defaults: 'Arial, Helvetica, sans-serif', options: [ /* web safe fonts */ ]},\r\n                   { name: 'Align', property: 'text-align', type: 'radio', defaults: 'left', options: [ /* left, center, right */ ]},\r\n                    // ... other typography props\r\n                 ]\r\n               },\r\n                { // Example Sector for Padding/Margin (use padding mostly for emails)\r\n                 name: 'Spacing',\r\n                 open: false,\r\n                 buildProps: ['padding-top', 'padding-right', 'padding-bottom', 'padding-left'],\r\n                 properties: [ // Define pixel-based inputs for simplicity\r\n                   { name: 'Top', property: 'padding-top', type: 'integer', units: ['px'], defaults: 0 },\r\n                   // ... other padding props\r\n                 ]\r\n               },\r\n               // ... add sectors for Background, Borders etc. with email-safe properties\r\n             ]\r\n           },\r\n            // Optional: Configure Asset Manager (for images)\r\n            // assetManager: {\r\n            //   // Configuration for your asset manager (upload endpoint, etc.)\r\n            // },\r\n        });\r\n\r\n        // Make sure the editor was initialized properly\r\n        if (!editor) {\r\n          console.error(\"[HtmlEmailEditor] Failed to initialize editor\"); // RENAMED\r\n          return;\r\n        }\r\n\r\n        grapesEditor.current = editor;\r\n\r\n        // REMOVED MJML Command registrations\r\n\r\n        // Use a small timeout to ensure editor is fully initialized\r\n        setTimeout(() => {\r\n          if (!grapesEditor.current) {\r\n            console.error(\"[HtmlEmailEditor] Editor instance not available after timeout\"); // RENAMED\r\n            return;\r\n          }\r\n\r\n          // Verify the editor's components API is available\r\n          if (!grapesEditor.current.setComponents) {\r\n            console.error(\"[HtmlEmailEditor] Editor's setComponents method is not available\"); // RENAMED\r\n            return;\r\n          }\r\n\r\n          try {\r\n             // Load initial HTML content\r\n             if (initialHtml) {\r\n               console.log(\"[HtmlEmailEditor] Loading initial HTML:\", initialHtml.substring(0, 100) + \"...\"); // RENAMED\r\n               try {\r\n                 grapesEditor.current.setComponents(initialHtml);\r\n                 console.log(\"[HtmlEmailEditor] Successfully loaded HTML content\"); // RENAMED\r\n               } catch (e) {\r\n                 console.error(\"[HtmlEmailEditor] Error loading initial HTML:\", e); // RENAMED\r\n                 // Load default if initial fails\r\n                 grapesEditor.current.setComponents(defaultHtmlContent);\r\n               }\r\n             } else {\r\n               // Load default HTML template if nothing is provided\r\n               console.log(\"[HtmlEmailEditor] No content provided, loading default HTML template\"); // RENAMED\r\n               grapesEditor.current.setComponents(defaultHtmlContent);\r\n             }\r\n          } catch (error) {\r\n            console.error(\"[HtmlEmailEditor] Error in content loading phase:\", error); // RENAMED\r\n          }\r\n        }, 100);\r\n\r\n        // Declare timeout variable in outer scope so it's accessible in cleanup function\r\n        let saveTimeout: NodeJS.Timeout | undefined;\r\n        let isSaving = false; // Flag to prevent multiple simultaneous save operations\r\n\r\n        // Attach save listener with debounce\r\n        editor.on('change:changesCount', () => {\r\n           // Only proceed if onSave exists, editor exists, and not already saving\r\n          if (onSave && editor && !isSaving && editor.getDirtyCount() > 0) {\r\n            // Clear existing timeout\r\n            if (saveTimeout) clearTimeout(saveTimeout);\r\n\r\n            // Set a new timeout\r\n            saveTimeout = setTimeout(() => {\r\n              try {\r\n                isSaving = true; // Set saving flag\r\n                const currentHtml = editor.getHtml() || '';\r\n                const currentCss = editor.getCss({ avoidPrototypes: true }) || ''; // Get CSS\r\n\r\n                // Combine HTML and CSS for a self-contained email\r\n                // Inject CSS into a <style> tag in the <head>\r\n                let finalHtml = currentHtml;\r\n                if (currentCss) {\r\n                   const styleTag = `<style type=\"text/css\">${currentCss}</style>`;\r\n                   if (finalHtml.includes('</head>')) {\r\n                     finalHtml = finalHtml.replace('</head>', `${styleTag}</head>`);\r\n                   } else if (finalHtml.includes('<body>')) {\r\n                     // Fallback if no head tag (less likely)\r\n                     finalHtml = finalHtml.replace('<body>', `<head>${styleTag}</head><body>`);\r\n                   } else {\r\n                      // Fallback if no head or body (very unlikely for valid HTML)\r\n                      finalHtml = `<head>${styleTag}</head>${finalHtml}`;\r\n                   }\r\n                 }\r\n\r\n                // Optionally use Juice or similar inliner here if needed, but GrapesJS handles some inlining\r\n                // const inlinedHtml = juice(finalHtml); // Requires juice library\r\n\r\n                 console.log(\"[HtmlEmailEditor] Content changed, attempting to call onSave...\"); // RENAMED\r\n\r\n                // Don't save if we have no meaningful content (adjust check as needed)\r\n                 if (!finalHtml.replace(/<[^>]*>/g, '').trim() && !finalHtml.includes('<img')) {\r\n                   console.log(\"[HtmlEmailEditor] No significant content to save, skipping save.\"); // RENAMED\r\n                   isSaving = false;\r\n                   return;\r\n                 }\r\n\r\n                 onSave(finalHtml); // Pass the combined HTML\r\n                 editor.setDirtyCount(0); // Reset dirty count after successful save\r\n                 console.log(\"[HtmlEmailEditor] onSave callback executed.\"); // RENAMED\r\n\r\n             } catch (error) {\r\n                 console.error(\"[HtmlEmailEditor] Error during editor change listener:\", error); // RENAMED\r\n             } finally {\r\n                 isSaving = false; // Reset flag whether save succeeded or failed\r\n             }\r\n            }, 1000); // Increased debounce to 1000ms\r\n          }\r\n        });\r\n\r\n        // Return cleanup function\r\n        return () => {\r\n          if (saveTimeout) clearTimeout(saveTimeout);\r\n          if (grapesEditor.current) {\r\n             try {\r\n               grapesEditor.current.destroy();\r\n             } catch (destroyError) {\r\n               console.error(\"[HtmlEmailEditor] Error during editor cleanup:\", destroyError); // RENAMED\r\n             }\r\n             grapesEditor.current = null;\r\n          }\r\n        };\r\n      } catch (initError) {\r\n        console.error(\"[HtmlEmailEditor] Critical error during editor initialization:\", initError); // RENAMED\r\n      }\r\n    }, [initialHtml, height, onSave]); // Rerun if initial content or dimensions change\r\n\r\n    // Expose save method via ref\r\n    useImperativeHandle(ref, () => ({\r\n      save: async () => {\r\n        let finalHtml = '';\r\n        if (grapesEditor.current) {\r\n           try {\r\n               const editor = grapesEditor.current;\r\n               const currentHtml = editor.getHtml() || '';\r\n               const currentCss = editor.getCss({ avoidPrototypes: true }) || '';\r\n\r\n                // Combine HTML and CSS\r\n                finalHtml = currentHtml;\r\n                if (currentCss) {\r\n                   const styleTag = `<style type=\"text/css\">${currentCss}</style>`;\r\n                   if (finalHtml.includes('</head>')) {\r\n                     finalHtml = finalHtml.replace('</head>', `${styleTag}</head>`);\r\n                   } else if (finalHtml.includes('<body>')) {\r\n                     finalHtml = finalHtml.replace('<body>', `<head>${styleTag}</head><body>`);\r\n                   } else {\r\n                      finalHtml = `<head>${styleTag}</head>${finalHtml}`;\r\n                   }\r\n                 }\r\n                // Optional: Add Juice inlining here too if needed for manual save\r\n                // finalHtml = juice(finalHtml);\r\n\r\n                console.log(\"[HtmlEmailEditor] Manual Save - HTML generated.\"); // RENAMED\r\n\r\n           } catch (saveErr) {\r\n               console.error(\"[HtmlEmailEditor] Manual Save - Error generating HTML:\", saveErr); // RENAMED\r\n           }\r\n        } else {\r\n          console.error(\"[HtmlEmailEditor] Manual Save - Editor not available.\"); // RENAMED\r\n        }\r\n\r\n        // Return only HTML\r\n        return { html: finalHtml };\r\n      },\r\n       getEditor: () => grapesEditor.current,\r\n    }));\r\n\r\n    return <div ref={editorRef} style={{ height: height }} />;\r\n  }\r\n);\r\n\r\n// Assign display name for debugging\r\nHtmlEmailEditor.displayName = 'HtmlEmailEditor'; // RENAMED\r\n\r\nexport default HtmlEmailEditor; // RENAMED"], "mappings": ";;AAAA,OAAO,kCAAkC,CAAC,CAAC;;AAE3C;AACA;AACA;AACA,OAAOA,KAAK,IACVC,UAAU,EACVC,SAAS,EACTC,mBAAmB,EACnBC,MAAM,QACD,OAAO;AAEd,OAAOC,QAAQ,MAAkB,UAAU;;AAE3C;AACA;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAYA;AACA,MAAMC,cAAc,GAAG;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,kBAAkB,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAGD,MAAMC,eAAe,gBAAAC,EAAA,cAAGV,UAAU,CAA4C;AAAAW,EAAA,GAAAD,EAAA,CAC5E,CAAC;EAAEE,WAAW,GAAG,EAAE;EAAEC,MAAM;EAAEC,MAAM,GAAG;AAAO,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EAAE;EACxD,MAAMM,SAAS,GAAGb,MAAM,CAAiB,IAAI,CAAC;EAC9C,MAAMc,YAAY,GAAGd,MAAM,CAAgB,IAAI,CAAC;;EAEhD;EACAF,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,SAAS,CAACE,OAAO,EAAE,OAAO,CAAC;;IAEhC;IACA,IAAID,YAAY,CAACC,OAAO,EAAE;MACxBC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC,CAAC,CAAC;MACvEH,YAAY,CAACC,OAAO,CAACG,OAAO,CAAC,CAAC;MAC9BJ,YAAY,CAACC,OAAO,GAAG,IAAI;IAC7B;IAEAC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE;MAAE;MACjEE,OAAO,EAAE,CAAC,CAACV,WAAW;MACtBW,UAAU,EAAE,CAAAX,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,MAAM,KAAI;IACrC,CAAC,CAAC;IAEF,IAAI;MACF,MAAMC,MAAM,GAAGrB,QAAQ,CAACsB,IAAI,CAAC;QAC3BC,SAAS,EAAEX,SAAS,CAACE,OAAO;QAC5BU,WAAW,EAAE,KAAK;QAClBd,MAAM,EAAEe,MAAM,CAACf,MAAM,CAAC;QACtBgB,KAAK,EAAE,MAAM;QACbC,cAAc,EAAE,KAAK;QAAE;QACvB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAC,MAAM,EAAE;UACNC,MAAM,EAAE,CACN,2EAA2E,CAAE;UAC7E;UAAA,CACD;UACA;UACDC,UAAU,EAAE3B;QACd,CAAC;QACD;QACA4B,YAAY,EAAE;UACZC,QAAQ,EAAE,EAAE;UAAE;UACdC,MAAM,EAAE;UACN;UACA;YACEC,EAAE,EAAE,MAAM;YACVC,KAAK,EAAE,MAAM;YACbC,IAAI,EAAE,qDAAqD;YAAE;YAC7DC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE;cACPC,IAAI,EAAE,MAAM;cACZD,OAAO,EAAE,8BAA8B;cACvCE,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAC;cAC1BC,cAAc,EAAE;YAClB;UACF,CAAC,EACD;YACER,EAAE,EAAE,OAAO;YACXC,KAAK,EAAE,OAAO;YACdC,IAAI,EAAE,sDAAsD;YAC5DC,QAAQ,EAAE,SAAS;YACnBM,MAAM,EAAE,IAAI;YAAE;YACdL,OAAO,EAAE;cACPC,IAAI,EAAE,OAAO;cACbC,KAAK,EAAE;gBAAEI,KAAK,EAAE,OAAO;gBAAElB,KAAK,EAAE;cAAO,CAAC;cAAE;cAC1CgB,cAAc,EAAE;YAClB;YACA;YACA;YACA;YACA;UACF,CAAC,EACD;YACER,EAAE,EAAE,QAAQ;YACZC,KAAK,EAAE,QAAQ;YACfC,IAAI,EAAE,qDAAqD;YAC3DC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE;UACX,CAAC,EACD;YACEJ,EAAE,EAAE,SAAS;YACbC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE,sDAAsD;YAC5DC,QAAQ,EAAE,QAAQ;YAClBC,OAAO,EAAE;UACX,CAAC,EACD;YACEJ,EAAE,EAAE,QAAQ;YACZC,KAAK,EAAE,QAAQ;YACfC,IAAI,EAAE,6DAA6D;YACnEC,QAAQ,EAAE,QAAQ;YAClBC,OAAO,EAAE;UACX,CAAC;UAED;UACA;YACEJ,EAAE,EAAE,SAAS;YAAE;YACfC,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE,6DAA6D;YACnEC,QAAQ,EAAE,QAAQ;YAClBC,OAAO,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;UACc,CAAC,EACD;YACGJ,EAAE,EAAE,UAAU;YACdC,KAAK,EAAE,UAAU;YACjBC,IAAI,EAAE,uDAAuD;YAC7DC,QAAQ,EAAE,QAAQ;YAClBC,OAAO,EAAE;UACZ,CAAC,EACD;YACGJ,EAAE,EAAE,UAAU;YACdC,KAAK,EAAE,WAAW;YAClBC,IAAI,EAAE,wDAAwD;YAC9DC,QAAQ,EAAE,QAAQ;YAClB;YACAC,OAAO,EAAE;AAC1B;AACA;AACA;AACA;AACA;UACc,CAAC,EACA;YACEJ,EAAE,EAAE,UAAU;YACdC,KAAK,EAAE,WAAW;YAClBC,IAAI,EAAE,mDAAmD;YACzDC,QAAQ,EAAE,QAAQ;YAClBC,OAAO,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;UACe;UACA;UAAA;QAEL,CAAC;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACCO,YAAY,EAAE;UACZC,OAAO,EAAE,CACR;YAAE;YACCC,IAAI,EAAE,YAAY;YAClBC,IAAI,EAAE,KAAK;YACXC,UAAU,EAAE,CAAC,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,CAAC;YAC/G;YACAC,UAAU,EAAE,CACV;cAAEH,IAAI,EAAE,MAAM;cAAEI,QAAQ,EAAE,aAAa;cAAEZ,IAAI,EAAE,QAAQ;cAAEa,QAAQ,EAAE,8BAA8B;cAAEC,OAAO,EAAE,CAAE;YAAsB,CAAC,EACrI;cAAEN,IAAI,EAAE,OAAO;cAAEI,QAAQ,EAAE,YAAY;cAAEZ,IAAI,EAAE,OAAO;cAAEa,QAAQ,EAAE,MAAM;cAAEC,OAAO,EAAE,CAAE;YAA2B;YAC/G;YAAA;UAEL,CAAC,EACA;YAAE;YACDN,IAAI,EAAE,SAAS;YACfC,IAAI,EAAE,KAAK;YACXC,UAAU,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,CAAC;YAC9EC,UAAU,EAAE;YAAE;YACZ;cAAEH,IAAI,EAAE,KAAK;cAAEI,QAAQ,EAAE,aAAa;cAAEZ,IAAI,EAAE,SAAS;cAAEe,KAAK,EAAE,CAAC,IAAI,CAAC;cAAEF,QAAQ,EAAE;YAAE;YACpF;YAAA;UAEJ;UACA;UAAA;QAEJ;QACC;QACA;QACA;QACA;MACJ,CAAC,CAAC;;MAEF;MACA,IAAI,CAAC/B,MAAM,EAAE;QACXN,OAAO,CAACwC,KAAK,CAAC,+CAA+C,CAAC,CAAC,CAAC;QAChE;MACF;MAEA1C,YAAY,CAACC,OAAO,GAAGO,MAAM;;MAE7B;;MAEA;MACAmC,UAAU,CAAC,MAAM;QACf,IAAI,CAAC3C,YAAY,CAACC,OAAO,EAAE;UACzBC,OAAO,CAACwC,KAAK,CAAC,+DAA+D,CAAC,CAAC,CAAC;UAChF;QACF;;QAEA;QACA,IAAI,CAAC1C,YAAY,CAACC,OAAO,CAAC2C,aAAa,EAAE;UACvC1C,OAAO,CAACwC,KAAK,CAAC,kEAAkE,CAAC,CAAC,CAAC;UACnF;QACF;QAEA,IAAI;UACD;UACA,IAAI/C,WAAW,EAAE;YACfO,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAER,WAAW,CAACkD,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;YAC/F,IAAI;cACF7C,YAAY,CAACC,OAAO,CAAC2C,aAAa,CAACjD,WAAW,CAAC;cAC/CO,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,OAAO2C,CAAC,EAAE;cACV5C,OAAO,CAACwC,KAAK,CAAC,+CAA+C,EAAEI,CAAC,CAAC,CAAC,CAAC;cACnE;cACA9C,YAAY,CAACC,OAAO,CAAC2C,aAAa,CAACrD,kBAAkB,CAAC;YACxD;UACF,CAAC,MAAM;YACL;YACAW,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC,CAAC,CAAC;YACrFH,YAAY,CAACC,OAAO,CAAC2C,aAAa,CAACrD,kBAAkB,CAAC;UACxD;QACH,CAAC,CAAC,OAAOmD,KAAK,EAAE;UACdxC,OAAO,CAACwC,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC,CAAC,CAAC;QAC7E;MACF,CAAC,EAAE,GAAG,CAAC;;MAEP;MACA,IAAIK,WAAuC;MAC3C,IAAIC,QAAQ,GAAG,KAAK,CAAC,CAAC;;MAEtB;MACAxC,MAAM,CAACyC,EAAE,CAAC,qBAAqB,EAAE,MAAM;QACpC;QACD,IAAIrD,MAAM,IAAIY,MAAM,IAAI,CAACwC,QAAQ,IAAIxC,MAAM,CAAC0C,aAAa,CAAC,CAAC,GAAG,CAAC,EAAE;UAC/D;UACA,IAAIH,WAAW,EAAEI,YAAY,CAACJ,WAAW,CAAC;;UAE1C;UACAA,WAAW,GAAGJ,UAAU,CAAC,MAAM;YAC7B,IAAI;cACFK,QAAQ,GAAG,IAAI,CAAC,CAAC;cACjB,MAAMI,WAAW,GAAG5C,MAAM,CAAC6C,OAAO,CAAC,CAAC,IAAI,EAAE;cAC1C,MAAMC,UAAU,GAAG9C,MAAM,CAAC+C,MAAM,CAAC;gBAAEC,eAAe,EAAE;cAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;;cAEnE;cACA;cACA,IAAIC,SAAS,GAAGL,WAAW;cAC3B,IAAIE,UAAU,EAAE;gBACb,MAAMI,QAAQ,GAAG,0BAA0BJ,UAAU,UAAU;gBAC/D,IAAIG,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;kBACjCF,SAAS,GAAGA,SAAS,CAACG,OAAO,CAAC,SAAS,EAAE,GAAGF,QAAQ,SAAS,CAAC;gBAChE,CAAC,MAAM,IAAID,SAAS,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;kBACvC;kBACAF,SAAS,GAAGA,SAAS,CAACG,OAAO,CAAC,QAAQ,EAAE,SAASF,QAAQ,eAAe,CAAC;gBAC3E,CAAC,MAAM;kBACJ;kBACAD,SAAS,GAAG,SAASC,QAAQ,UAAUD,SAAS,EAAE;gBACrD;cACF;;cAED;cACA;;cAECvD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC,CAAC,CAAC;;cAEjF;cACC,IAAI,CAACsD,SAAS,CAACG,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,IAAI,CAACJ,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAC5EzD,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC,CAAC,CAAC;gBACjF6C,QAAQ,GAAG,KAAK;gBAChB;cACF;cAEApD,MAAM,CAAC6D,SAAS,CAAC,CAAC,CAAC;cACnBjD,MAAM,CAACsD,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;cACzB5D,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC,CAAC,CAAC;YAEhE,CAAC,CAAC,OAAOuC,KAAK,EAAE;cACZxC,OAAO,CAACwC,KAAK,CAAC,wDAAwD,EAAEA,KAAK,CAAC,CAAC,CAAC;YACpF,CAAC,SAAS;cACNM,QAAQ,GAAG,KAAK,CAAC,CAAC;YACtB;UACD,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;QACZ;MACF,CAAC,CAAC;;MAEF;MACA,OAAO,MAAM;QACX,IAAID,WAAW,EAAEI,YAAY,CAACJ,WAAW,CAAC;QAC1C,IAAI/C,YAAY,CAACC,OAAO,EAAE;UACvB,IAAI;YACFD,YAAY,CAACC,OAAO,CAACG,OAAO,CAAC,CAAC;UAChC,CAAC,CAAC,OAAO2D,YAAY,EAAE;YACrB7D,OAAO,CAACwC,KAAK,CAAC,gDAAgD,EAAEqB,YAAY,CAAC,CAAC,CAAC;UACjF;UACA/D,YAAY,CAACC,OAAO,GAAG,IAAI;QAC9B;MACF,CAAC;IACH,CAAC,CAAC,OAAO+D,SAAS,EAAE;MAClB9D,OAAO,CAACwC,KAAK,CAAC,gEAAgE,EAAEsB,SAAS,CAAC,CAAC,CAAC;IAC9F;EACF,CAAC,EAAE,CAACrE,WAAW,EAAEE,MAAM,EAAED,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEnC;EACAX,mBAAmB,CAACa,GAAG,EAAE,OAAO;IAC9BmE,IAAI,EAAE,MAAAA,CAAA,KAAY;MAChB,IAAIR,SAAS,GAAG,EAAE;MAClB,IAAIzD,YAAY,CAACC,OAAO,EAAE;QACvB,IAAI;UACA,MAAMO,MAAM,GAAGR,YAAY,CAACC,OAAO;UACnC,MAAMmD,WAAW,GAAG5C,MAAM,CAAC6C,OAAO,CAAC,CAAC,IAAI,EAAE;UAC1C,MAAMC,UAAU,GAAG9C,MAAM,CAAC+C,MAAM,CAAC;YAAEC,eAAe,EAAE;UAAK,CAAC,CAAC,IAAI,EAAE;;UAEhE;UACAC,SAAS,GAAGL,WAAW;UACvB,IAAIE,UAAU,EAAE;YACb,MAAMI,QAAQ,GAAG,0BAA0BJ,UAAU,UAAU;YAC/D,IAAIG,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAE;cACjCF,SAAS,GAAGA,SAAS,CAACG,OAAO,CAAC,SAAS,EAAE,GAAGF,QAAQ,SAAS,CAAC;YAChE,CAAC,MAAM,IAAID,SAAS,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;cACvCF,SAAS,GAAGA,SAAS,CAACG,OAAO,CAAC,QAAQ,EAAE,SAASF,QAAQ,eAAe,CAAC;YAC3E,CAAC,MAAM;cACJD,SAAS,GAAG,SAASC,QAAQ,UAAUD,SAAS,EAAE;YACrD;UACF;UACD;UACA;;UAEAvD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC,CAAC,CAAC;QAErE,CAAC,CAAC,OAAO+D,OAAO,EAAE;UACdhE,OAAO,CAACwC,KAAK,CAAC,wDAAwD,EAAEwB,OAAO,CAAC,CAAC,CAAC;QACtF;MACH,CAAC,MAAM;QACLhE,OAAO,CAACwC,KAAK,CAAC,uDAAuD,CAAC,CAAC,CAAC;MAC1E;;MAEA;MACA,OAAO;QAAEyB,IAAI,EAAEV;MAAU,CAAC;IAC5B,CAAC;IACAW,SAAS,EAAEA,CAAA,KAAMpE,YAAY,CAACC;EACjC,CAAC,CAAC,CAAC;EAEH,oBAAOZ,OAAA;IAAKS,GAAG,EAAEC,SAAU;IAAC4B,KAAK,EAAE;MAAE9B,MAAM,EAAEA;IAAO;EAAE;IAAAwE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3D,CAAC,iCACH,CAAC;;AAED;AAAAC,GAAA,GArWMjF,eAAe;AAsWrBA,eAAe,CAACkF,WAAW,GAAG,iBAAiB,CAAC,CAAC;;AAEjD,eAAelF,eAAe,CAAC,CAAC;AAAA,IAAAE,EAAA,EAAA+E,GAAA;AAAAE,YAAA,CAAAjF,EAAA;AAAAiF,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}