{"ast": null, "code": "/**\r\n * EmailPreview component for Driftly Email Generator\r\n * Displays real-time preview of the email template\r\n */import React,{useEffect}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EmailPreview=_ref=>{let{html,mode,iframeRef,className=''}=_ref;// Update iframe content when HTML changes\nuseEffect(()=>{if(iframeRef.current&&iframeRef.current.contentDocument){const doc=iframeRef.current.contentDocument;// Use a timeout to ensure the previous write is cleared if updates are rapid\nconst timeoutId=setTimeout(()=>{doc.open();doc.write(html);// Write the new HTML content\ndoc.close();},50);// Short delay like 50ms\n// Cleanup function to clear the timeout if the component unmounts or html changes again quickly\nreturn()=>clearTimeout(timeoutId);}},[html,iframeRef]);// Rerun effect if html or iframeRef changes\nreturn/*#__PURE__*/_jsxs(\"div\",{className:`email-preview flex-1 overflow-hidden p-4 ${className}`,children:[\" \",/*#__PURE__*/_jsxs(\"div\",{className:`preview-container ${mode} flex items-center justify-center h-full`,children:[\" \",/*#__PURE__*/_jsx(\"iframe\",{ref:iframeRef,title:\"Email Preview\",className:\"preview-iframe bg-white shadow-md\"// Assuming styles defined in editor.css\n,sandbox:\"allow-same-origin allow-scripts\"// allow-scripts might be needed for some complex MJML components, but increases risk. Consider allow-same-origin only if possible.\n,style:{width:mode==='desktop'?'100%':'375px',// Common mobile width\nmaxWidth:'100%',// Ensure it doesn't overflow its container\nheight:'100%',// Use full height of container\nborder:'1px solid #e2e8f0',borderRadius:'4px',margin:'0 auto',display:'block'}})]})]});};export default EmailPreview;", "map": {"version": 3, "names": ["React", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "EmailPreview", "_ref", "html", "mode", "iframeRef", "className", "current", "contentDocument", "doc", "timeoutId", "setTimeout", "open", "write", "close", "clearTimeout", "children", "ref", "title", "sandbox", "style", "width", "max<PERSON><PERSON><PERSON>", "height", "border", "borderRadius", "margin", "display"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/EmailPreview.tsx"], "sourcesContent": ["/**\r\n * EmailPreview component for Driftly Email Generator\r\n * Displays real-time preview of the email template\r\n */\r\n\r\nimport React, {\r\n  RefObject,\r\n  useEffect,\r\n} from 'react';\r\n\r\ninterface EmailPreviewProps {\r\n  html: string;\r\n  mode: 'desktop' | 'mobile';\r\n  iframeRef: RefObject<HTMLIFrameElement>;\r\n  className?: string; // Add optional className prop\r\n}\r\n\r\nconst EmailPreview: React.FC<EmailPreviewProps> = ({ html, mode, iframeRef, className = '' }) => {\r\n  // Update iframe content when HTML changes\r\n  useEffect(() => {\r\n    if (iframeRef.current && iframeRef.current.contentDocument) {\r\n      const doc = iframeRef.current.contentDocument;\r\n      // Use a timeout to ensure the previous write is cleared if updates are rapid\r\n      const timeoutId = setTimeout(() => {\r\n        doc.open();\r\n        doc.write(html); // Write the new HTML content\r\n        doc.close();\r\n      }, 50); // Short delay like 50ms\r\n\r\n      // Cleanup function to clear the timeout if the component unmounts or html changes again quickly\r\n      return () => clearTimeout(timeoutId);\r\n    }\r\n  }, [html, iframeRef]); // Rerun effect if html or iframeRef changes\r\n\r\n  return (\r\n    <div className={`email-preview flex-1 overflow-hidden p-4 ${className}`}> {/* Add custom className support */}\r\n      <div className={`preview-container ${mode} flex items-center justify-center h-full`}> {/* Assuming styles defined in editor.css */}\r\n        <iframe\r\n          ref={iframeRef}\r\n          title=\"Email Preview\"\r\n          className=\"preview-iframe bg-white shadow-md\" // Assuming styles defined in editor.css\r\n          sandbox=\"allow-same-origin allow-scripts\" // allow-scripts might be needed for some complex MJML components, but increases risk. Consider allow-same-origin only if possible.\r\n          style={{\r\n            width: mode === 'desktop' ? '100%' : '375px', // Common mobile width\r\n            maxWidth: '100%', // Ensure it doesn't overflow its container\r\n            height: '100%', // Use full height of container\r\n            border: '1px solid #e2e8f0',\r\n            borderRadius: '4px',\r\n            margin: '0 auto',\r\n            display: 'block'\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EmailPreview; "], "mappings": "AAAA;AACA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,EAEVC,SAAS,KACJ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBASf,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAA+C,IAA9C,CAAEC,IAAI,CAAEC,IAAI,CAAEC,SAAS,CAAEC,SAAS,CAAG,EAAG,CAAC,CAAAJ,IAAA,CAC1F;AACAN,SAAS,CAAC,IAAM,CACd,GAAIS,SAAS,CAACE,OAAO,EAAIF,SAAS,CAACE,OAAO,CAACC,eAAe,CAAE,CAC1D,KAAM,CAAAC,GAAG,CAAGJ,SAAS,CAACE,OAAO,CAACC,eAAe,CAC7C;AACA,KAAM,CAAAE,SAAS,CAAGC,UAAU,CAAC,IAAM,CACjCF,GAAG,CAACG,IAAI,CAAC,CAAC,CACVH,GAAG,CAACI,KAAK,CAACV,IAAI,CAAC,CAAE;AACjBM,GAAG,CAACK,KAAK,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACA,MAAO,IAAMC,YAAY,CAACL,SAAS,CAAC,CACtC,CACF,CAAC,CAAE,CAACP,IAAI,CAAEE,SAAS,CAAC,CAAC,CAAE;AAEvB,mBACEL,KAAA,QAAKM,SAAS,CAAE,4CAA4CA,SAAS,EAAG,CAAAU,QAAA,EAAC,GAAC,cACxEhB,KAAA,QAAKM,SAAS,CAAE,qBAAqBF,IAAI,0CAA2C,CAAAY,QAAA,EAAC,GAAC,cACpFlB,IAAA,WACEmB,GAAG,CAAEZ,SAAU,CACfa,KAAK,CAAC,eAAe,CACrBZ,SAAS,CAAC,mCAAoC;AAAA,CAC9Ca,OAAO,CAAC,iCAAkC;AAAA,CAC1CC,KAAK,CAAE,CACLC,KAAK,CAAEjB,IAAI,GAAK,SAAS,CAAG,MAAM,CAAG,OAAO,CAAE;AAC9CkB,QAAQ,CAAE,MAAM,CAAE;AAClBC,MAAM,CAAE,MAAM,CAAE;AAChBC,MAAM,CAAE,mBAAmB,CAC3BC,YAAY,CAAE,KAAK,CACnBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,OACX,CAAE,CACH,CAAC,EACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA1B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}