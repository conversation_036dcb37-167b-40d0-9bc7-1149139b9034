{"version": 3, "file": "static/js/814.e9ee205e.chunk.js", "mappings": "+JAOA,MAsBA,EAtBmCA,KAE/BC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iEAAgEC,UAC7EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SAAC,0BAE1DC,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CAACH,UAAU,iDAAgDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BAA0BC,SAAC,gKAIxCF,EAAAA,EAAAA,KAACK,EAAAA,GAAK,CACJC,KAAK,OACLC,QAAQ,uDACRN,UAAU,gB", "sources": ["pages/DataExportImport.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n} from '../components';\r\n\r\nconst DataExportImport: React.FC = () => {\r\n  return (\r\n    <div className=\"p-4 md:p-6 lg:p-8 bg-primary-bg text-text-primary min-h-screen\">\r\n      <div className=\"max-w-4xl mx-auto\">\r\n        <h1 className=\"text-3xl font-bold text-text-primary mb-6\">Data Export & Import</h1>\r\n        \r\n        <Card className=\"bg-secondary-bg border border-border-color p-6\">\r\n          <p className=\"text-text-secondary mb-4\">\r\n            This feature is under development. The Data Export & Import tool will allow you to export your data for analysis and import contacts from other platforms.\r\n          </p>\r\n          \r\n          <Alert \r\n            type=\"info\" \r\n            message=\"Coming soon! Check back for updates on this feature.\"\r\n            className=\"mt-4\"\r\n          />\r\n        </Card>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DataExportImport; "], "names": ["DataExportImport", "_jsx", "className", "children", "_jsxs", "Card", "<PERSON><PERSON>", "type", "message"], "sourceRoot": ""}