{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\EmailEditor.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Enhanced EmailEditor component for Driftly Email Generator\n * Provides advanced drag-and-drop interface for template creation and editing\n * Features: Real-time preview, responsive design, advanced block library, undo/redo\n */\n\nimport React, { useCallback, useEffect, useRef, useState } from 'react';\nimport mjml2html from 'mjml-browser';\nimport { useDrag, useDrop } from 'react-dnd';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport api from '../services/api'; // Corrected import path\n// Types - Adjust path as needed\n\nimport BlockEditor from './BlockEditor';\n// Components - Adjust paths as needed\nimport BlockLibrary from './BlockLibrary';\nimport EmailPreview from './EmailPreview';\nimport SaveTemplateModal from './SaveTemplateModal';\n\n// Styles - Ensure this is imported in your main application entry point\n// e.g., import '../styles/editor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_API_URL || '/api'; // Use environment variable\n\n// Define Drag Item Types\nconst ItemTypes = {\n  BLOCK: 'block',\n  LIBRARY_BLOCK: 'library_block'\n};\n// Near the top of the file, add a template cache mechanism:\n// Cache for storing generated HTML previews\nconst templateCache = new Map();\n\n// Cache TTL (Time To Live) in milliseconds - 5 minutes\nconst CACHE_TTL = 5 * 60 * 1000;\n\n// Add this function before the EmailEditor component definition\n// Function to generate a cache key based on blocks\nconst generateCacheKey = blocks => {\n  return blocks.map(block => {\n    const {\n      instanceId,\n      content\n    } = block;\n    // Include only essential data in cache key\n    return `${block.blockId || block._id}:${instanceId}:${JSON.stringify(content)}`;\n  }).join('|');\n};\n\n// Function to clear stale cache entries\nconst clearStaleCache = () => {\n  const now = Date.now();\n  templateCache.forEach((entry, key) => {\n    if (now - entry.timestamp > CACHE_TTL) {\n      templateCache.delete(key);\n    }\n  });\n};\n\n// Enhanced state management with undo/redo functionality\n\n// --- EmailEditor Component ---\n\nconst EmailEditor = () => {\n  _s();\n  var _editorBlocks$selecte;\n  const {\n    templateId\n  } = useParams();\n  const navigate = useNavigate();\n\n  // Core State\n  const [template, setTemplate] = useState({});\n  const [editorBlocks, setEditorBlocks] = useState([]);\n  const [availableBlocks, setAvailableBlocks] = useState([]);\n  const [selectedBlockIndex, setSelectedBlockIndex] = useState(null);\n  const [previewHtml, setPreviewHtml] = useState('');\n  const [previewMode, setPreviewMode] = useState('desktop');\n  const [isSaving, setIsSaving] = useState(false);\n  const [showSaveModal, setShowSaveModal] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [saveError, setSaveError] = useState(null);\n  const [userPreferences, setUserPreferences] = useState(null);\n  const [templateName, setTemplateName] = useState('');\n\n  // Enhanced UI State\n  const [isDragActive, setIsDragActive] = useState(false);\n  const [showBlockLibrary, setShowBlockLibrary] = useState(true);\n  const [showPreview, setShowPreview] = useState(true);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);\n  const [viewMode, setViewMode] = useState('split');\n\n  // Undo/Redo State\n  const [undoRedoState, setUndoRedoState] = useState({\n    history: [],\n    currentIndex: -1,\n    maxHistorySize: 50\n  });\n\n  // Performance optimization - memoized values\n  const canUndo = useMemo(() => undoRedoState.currentIndex > 0, [undoRedoState.currentIndex]);\n  const canRedo = useMemo(() => undoRedoState.currentIndex < undoRedoState.history.length - 1, [undoRedoState.currentIndex, undoRedoState.history.length]);\n\n  // Refs\n  const previewIframeRef = useRef(null);\n  const editorCanvasRef = useRef(null);\n\n  // --- Undo/Redo Functions --- //\n  const saveToHistory = useCallback((blocks, selectedIndex) => {\n    setUndoRedoState(prev => {\n      const newState = {\n        blocks: JSON.parse(JSON.stringify(blocks)),\n        // Deep clone\n        selectedBlockIndex: selectedIndex,\n        timestamp: Date.now()\n      };\n\n      // Remove any future history if we're not at the end\n      const newHistory = prev.history.slice(0, prev.currentIndex + 1);\n      newHistory.push(newState);\n\n      // Limit history size\n      if (newHistory.length > prev.maxHistorySize) {\n        newHistory.shift();\n      }\n      return {\n        ...prev,\n        history: newHistory,\n        currentIndex: newHistory.length - 1\n      };\n    });\n  }, []);\n  const undo = useCallback(() => {\n    if (!canUndo) return;\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex - 1;\n      const state = prev.history[newIndex];\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canUndo]);\n  const redo = useCallback(() => {\n    if (!canRedo) return;\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex + 1;\n      const state = prev.history[newIndex];\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canRedo]);\n\n  // Keyboard shortcuts for undo/redo\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {\n        e.preventDefault();\n        undo();\n      } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || e.key === 'z' && e.shiftKey)) {\n        e.preventDefault();\n        redo();\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [undo, redo]);\n\n  // --- Data Fetching --- //\n  useEffect(() => {\n    let isMounted = true;\n    const fetchData = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Fetch available blocks first (always needed)\n        console.log(`Fetching blocks from ${API_URL}/blocks`);\n        // Use the shared api instance with BlockListResponse type\n        const blocksPromise = api.get('/blocks');\n\n        // Fetch user preferences to potentially apply brand colors/fonts later\n        console.log(`Fetching user preferences from ${API_URL}/user/preferences`);\n        // Use the shared api instance with correct inline type\n        const prefsPromise = api.get(`/user/preferences`);\n\n        // Fetch template data if ID exists\n        const templatePromise = templateId\n        // Use the shared api instance with TemplateDetailResponse type\n        ? api.get(`/templates/${templateId}`) : Promise.resolve(null);\n        const [blocksResponse, prefsResponse, templateResponse] = await Promise.all([blocksPromise, prefsPromise, templatePromise]);\n\n        // Access blocks correctly from response.data.data\n        const fetchedAvailableBlocks = blocksResponse.data.data || [];\n        console.log(`Fetched ${fetchedAvailableBlocks.length} available blocks.`);\n        setAvailableBlocks(fetchedAvailableBlocks);\n\n        // Process user preferences\n        const userPreferences = prefsResponse.data.preferences; // Store preferences\n        // TODO: Apply user preferences (e.g., brand colors, fonts)\n\n        if (templateResponse && templateResponse.data.template) {\n          const templateData = templateResponse.data.template; // Correct path now\n          console.log('[EmailEditor] Fetched template data:', JSON.stringify(templateData, null, 2)); // Log fetched data\n\n          // Apply brand colors/fonts from preferences if not set on template\n          setTemplate({\n            ...templateData,\n            brandColors: templateData.brandColors || (userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.brandColors),\n            defaultFonts: templateData.defaultFonts || (userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.defaultFonts)\n          });\n\n          // Populate editor blocks based on blockIds and fetched definitions\n          console.log('[EmailEditor] Attempting to populate blocks. Available block defs:', fetchedAvailableBlocks.length);\n          console.log('[EmailEditor] Template blockIds:', templateData.blockIds);\n          console.log('[EmailEditor] Template blocks data:', templateData.blocks);\n          if (templateData.blockIds && templateData.blockIds.length > 0 && fetchedAvailableBlocks.length > 0) {\n            console.log('[EmailEditor] Conditions met, proceeding to map blockIds.');\n            const populatedBlocks = templateData.blockIds.map((id, index) => {\n              var _templateData$blocks;\n              console.log(`[EmailEditor] Mapping blockId: ${id} at index: ${index}`);\n              // Match ID from templateData.blockIds with fetchedAvailableBlocks\n              const foundBlockDef = fetchedAvailableBlocks.find(b => b.blockId === id || b._id === id); // Check both blockId and _id\n              console.log(`[EmailEditor] ... Definition found for ${id}?`, foundBlockDef ? 'Yes' : 'No');\n              if (!foundBlockDef) {\n                console.warn(`[EmailEditor] Block definition not found for available block matching ID: ${id}. Skipping.`); // Refined warning\n                return null;\n              }\n\n              // Get content for this specific instance from templateData.blocks\n              const instanceBlockData = (_templateData$blocks = templateData.blocks) === null || _templateData$blocks === void 0 ? void 0 : _templateData$blocks[index]; // Get the block data saved for this instance\n              const instanceContent = (instanceBlockData === null || instanceBlockData === void 0 ? void 0 : instanceBlockData.content) || foundBlockDef.content || {}; // Use instance content, fallback to definition's default\n              console.log(`[EmailEditor] ... Instance content for ${id}:`, instanceContent);\n              const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`; // Unique key for React\n\n              // Return the combined block object for the editor state\n              const blockForState = {\n                ...foundBlockDef,\n                // Base definition (MJML, name, category etc.)\n                content: {\n                  ...instanceContent\n                },\n                // Specific content for this instance\n                instanceId // Unique ID for this instance in the editor\n              };\n              console.log(`[EmailEditor] ... Created block object for state for ${id}:`, blockForState);\n              return blockForState;\n            }).filter(b => b !== null);\n            console.log(\"[EmailEditor] Populated blocks array before setting state:\", populatedBlocks);\n            setEditorBlocks(populatedBlocks);\n          } else {\n            var _templateData$blockId;\n            console.log(\"[EmailEditor] Condition for populating blocks NOT met:\", {\n              hasBlockIds: !!templateData.blockIds,\n              blockIdsLength: (_templateData$blockId = templateData.blockIds) === null || _templateData$blockId === void 0 ? void 0 : _templateData$blockId.length,\n              hasFetchedBlocks: !!fetchedAvailableBlocks,\n              fetchedBlocksLength: fetchedAvailableBlocks === null || fetchedAvailableBlocks === void 0 ? void 0 : fetchedAvailableBlocks.length\n            });\n            setEditorBlocks([]); // Sets blocks to empty\n          }\n        } else {\n          // New template: initialize with preferences\n          console.log('Initializing new template state with preferences.');\n          setTemplate({\n            templateName: 'Untitled Template',\n            subject: 'Your Subject Here',\n            brandColors: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.brandColors,\n            defaultFonts: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.defaultFonts,\n            // Initialize other necessary fields if needed\n            userId: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.userId // Important for saving later\n          });\n          setEditorBlocks([]);\n        }\n      } catch (err) {\n        console.error('Error loading editor data:', err);\n        let errorMsg = 'Failed to load editor data.'; // Default message\n\n        if (err.response) {\n          var _err$config, _err$config$url;\n          // Check if it's the template fetch that failed with 404\n          if ((_err$config = err.config) !== null && _err$config !== void 0 && (_err$config$url = _err$config.url) !== null && _err$config$url !== void 0 && _err$config$url.includes(`/templates/${templateId}`) && err.response.status === 404) {\n            errorMsg = `Template with ID ${templateId} not found. It may have been deleted.`;\n            // Optionally, redirect the user or clear the template state\n            // navigate('/templates'); // Example redirect\n            setTemplate({}); // Clear any partial template data\n            setEditorBlocks([]);\n          } else {\n            var _err$response$data, _err$response$data2;\n            // Use the error message from the response if available, otherwise use the generic message\n            errorMsg = ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || ((_err$response$data2 = err.response.data) === null || _err$response$data2 === void 0 ? void 0 : _err$response$data2.message) || err.message || errorMsg;\n          }\n        } else {\n          // Network error or other issue\n          errorMsg = err.message || errorMsg;\n        }\n        setError(errorMsg);\n        // Fallback logic: Check if availableBlocks is empty (meaning block fetch might have also failed)\n        if (availableBlocks.length === 0) {\n          console.warn(\"Falling back to empty block list as blocks couldn't be fetched or list is empty.\");\n          setAvailableBlocks([]); // Ensure it's an empty array if blocks failed\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchData();\n    return () => {\n      isMounted = false;\n    };\n    // eslint-disable-next-line\n  }, [templateId]); // Rerun only when templateId changes\n\n  // --- MJML Generation & Preview Update --- //\n  const generateMjmlFromBlocks = useCallback(blockList => {\n    var _template$brandColors, _template$defaultFont;\n    // If there are no blocks, return early with an empty string\n    if (!blockList.length) return '';\n\n    // Generate a cache key for this block configuration\n    const cacheKey = generateCacheKey(blockList);\n\n    // Check if we have a cached version\n    if (templateCache.has(cacheKey)) {\n      const cached = templateCache.get(cacheKey);\n      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {\n        console.log(\"[EmailEditor] Using cached MJML\");\n        return cached.mjml;\n      }\n    }\n\n    // Periodically clean up old cache entries\n    clearStaleCache();\n\n    // Process blocks in chunks if there are many\n    const chunkSize = 5;\n    let mjmlBodyContent = '';\n    for (let i = 0; i < blockList.length; i += chunkSize) {\n      const chunk = blockList.slice(i, i + chunkSize);\n      const chunkContent = chunk.map(block => {\n        let blockMjml = block.mjml || '';\n        const content = block.content || {};\n\n        // More robust replacement logic\n        Object.keys(content).forEach(key => {\n          const value = content[key];\n          const placeholder = `{{${key}}}`; // Standard placeholder\n\n          // Only process if value exists\n          if (value !== undefined && value !== null) {\n            // Handle arrays specifically (e.g., nav links, social icons)\n            if (Array.isArray(value)) {\n              // Existing array handling code...\n              if (key === 'nav_links' || key === 'navLinks') {\n                const linksHtml = value.map(link => `<mj-navbar-link href=\"${link.url || '#'}\" color=\"#1E3A8A\">${link.name || 'Link'}</mj-navbar-link>`).join('\\n');\n                blockMjml = blockMjml.replace('{{navLinksArea}}', linksHtml);\n              } else if (key === 'social_icons' || key === 'socialLinks') {\n                const iconsHtml = value.map(icon => {\n                  var _icon$platform;\n                  return `<mj-social-element name=\"${((_icon$platform = icon.platform) === null || _icon$platform === void 0 ? void 0 : _icon$platform.toLowerCase()) || 'share'}\" href=\"${icon.url || '#'}\"></mj-social-element>`;\n                }).join('\\n');\n                blockMjml = blockMjml.replace('{{socialIconsArea}}', iconsHtml);\n              }\n            } else {\n              // More efficient single replacement (no array of placeholders)\n              const stringValue = String(value);\n              blockMjml = blockMjml.replaceAll(placeholder, stringValue);\n\n              // Only try alternative formats if there's a good chance they exist\n              if (blockMjml.includes('{{')) {\n                // Convert camelCase to snake_case for placeholder check if needed\n                const camelCasePlaceholder = `{{${key.replace(/_([a-z])/g, g => g[1].toUpperCase())}}}`;\n                const snakeCasePlaceholder = `{{${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)}}}`;\n                blockMjml = blockMjml.replaceAll(camelCasePlaceholder, stringValue);\n                blockMjml = blockMjml.replaceAll(snakeCasePlaceholder, stringValue);\n              }\n            }\n          }\n        });\n\n        // More efficient placeholder cleanup - only if needed\n        if (blockMjml.includes('{{')) {\n          blockMjml = blockMjml.replace(/\\{\\{[\\w.-]+\\}\\}/g, '');\n        }\n        return blockMjml;\n      }).join('\\n');\n      mjmlBodyContent += chunkContent;\n    }\n\n    // Use template state for colors/fonts, falling back to defaults\n    const brandColors = (_template$brandColors = template === null || template === void 0 ? void 0 : template.brandColors) !== null && _template$brandColors !== void 0 ? _template$brandColors : {};\n    const defaultFonts = (_template$defaultFont = template === null || template === void 0 ? void 0 : template.defaultFonts) !== null && _template$defaultFont !== void 0 ? _template$defaultFont : {};\n    const primaryColor = brandColors.primary || '#4F46E5';\n    const backgroundColor = brandColors.background || '#f3f4f6';\n    const textColor = brandColors.text || '#111827';\n    const headingFont = defaultFonts.heading || 'Arial, sans-serif';\n    const bodyFont = defaultFonts.body || 'Arial, sans-serif';\n\n    // Construct the full MJML document\n    const fullMjml = `\n<mjml>\n  <mj-head>\n    <mj-title>${(template === null || template === void 0 ? void 0 : template.subject) || 'Email Template'}</mj-title>\n    <mj-font name=\"Roboto\" href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700\" />\n    <mj-attributes>\n      <mj-all padding=\"0px\" font-family=\"${bodyFont}\" />\n      <mj-text padding=\"10px 25px\" font-size=\"14px\" line-height=\"1.5\" color=\"${textColor}\" />\n      <mj-section padding=\"10px 0\" />\n      <mj-column padding=\"5px\" />\n      <mj-button background-color=\"${primaryColor}\" color=\"#ffffff\" font-weight=\"bold\" border-radius=\"4px\" padding=\"10px 20px\" />\n      <mj-image padding=\"0px\" />\n    </mj-attributes>\n    <mj-style inline=\"inline\">\n      /* Inline styles */\n      a { color: ${primaryColor} !important; text-decoration: none !important; }\n    </mj-style>\n     <mj-style>\n      /* Embedded styles */\n      .hover-link:hover { text-decoration: underline !important; }\n    </mj-style>\n  </mj-head>\n  <mj-body background-color=\"${backgroundColor}\">\n    ${mjmlBodyContent}\n  </mj-body>\n</mjml>`;\n\n    // Store in cache\n    templateCache.set(cacheKey, {\n      mjml: fullMjml,\n      html: '',\n      // Will be populated on first HTML conversion\n      timestamp: Date.now()\n    });\n    return fullMjml;\n  }, [template]);\n  useEffect(() => {\n    const generatePreview = () => {\n      if (!editorBlocks || editorBlocks.length === 0) {\n        setPreviewHtml('<div style=\"display: flex; justify-content: center; align-items: center; height: 100%; color: grey; padding: 20px; text-align: center;\">Drag blocks here to build your email</div>');\n        return;\n      }\n      try {\n        // Generate cache key\n        const cacheKey = generateCacheKey(editorBlocks);\n\n        // Check for valid cached HTML\n        if (templateCache.has(cacheKey)) {\n          const cached = templateCache.get(cacheKey);\n          if (cached && cached.html && Date.now() - cached.timestamp < CACHE_TTL) {\n            console.log(\"[EmailEditor Preview] Using cached HTML\");\n            setPreviewHtml(cached.html);\n            return;\n          }\n        }\n\n        // If we get here, generate a new preview\n        const mjmlString = generateMjmlFromBlocks(editorBlocks);\n        console.log(\"[EmailEditor Preview] Generating fresh HTML preview\");\n\n        // Use a web worker for MJML conversion if possible\n        if (window.Worker) {\n          // Note: You would need to create a separate mjml-worker.js file\n          // This is just demonstrating the concept\n          const workerTimeout = setTimeout(() => {\n            // Fallback if worker takes too long\n            try {\n              const {\n                html,\n                errors\n              } = mjml2html(mjmlString, {\n                validationLevel: 'soft'\n              });\n              setPreviewHtml(html);\n              // Update cache\n              const cacheKey = generateCacheKey(editorBlocks);\n              if (templateCache.has(cacheKey)) {\n                const entry = templateCache.get(cacheKey);\n                if (entry) {\n                  entry.html = html;\n                  entry.timestamp = Date.now();\n                }\n              }\n            } catch (fallbackErr) {\n              console.error('[EmailEditor Preview] Error in fallback conversion:', fallbackErr);\n              setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${(fallbackErr === null || fallbackErr === void 0 ? void 0 : fallbackErr.message) || 'Unknown error'}</div>`);\n            }\n          }, 1000); // 1 second timeout\n\n          // This is just conceptual - actual implementation would need the worker file\n          // const worker = new Worker('/mjml-worker.js');\n          // worker.postMessage(mjmlString);\n          // worker.onmessage = (e) => {\n          //   clearTimeout(workerTimeout);\n          //   const { html, errors } = e.data;\n          //   setPreviewHtml(html);\n          //   // Update cache\n          //   updateHtmlCache(cacheKey, html);\n          // };\n        } else {\n          // Direct conversion when Web Workers aren't available\n          const {\n            html,\n            errors\n          } = mjml2html(mjmlString, {\n            validationLevel: 'soft'\n          });\n          if (errors && errors.length > 0) {\n            console.warn('[EmailEditor Preview] MJML Validation:', errors.length, 'issues');\n          }\n          setPreviewHtml(html);\n\n          // Update cache\n          if (templateCache.has(cacheKey)) {\n            const entry = templateCache.get(cacheKey);\n            if (entry) {\n              entry.html = html;\n              entry.timestamp = Date.now();\n            }\n          }\n        }\n      } catch (err) {\n        console.error('[EmailEditor Preview] Error generating preview:', err);\n        setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${(err === null || err === void 0 ? void 0 : err.message) || 'Unknown error'}</div>`);\n      }\n    };\n\n    // Debounce the preview generation to avoid too many updates\n    const debounceTimeout = setTimeout(generatePreview, 300);\n    return () => clearTimeout(debounceTimeout);\n  }, [editorBlocks, generateMjmlFromBlocks]);\n\n  // --- Enhanced DND Callbacks --- //\n  const moveBlock = useCallback((dragIndex, hoverIndex) => {\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = [...prevBlocks];\n      const [draggedBlock] = newBlocks.splice(dragIndex, 1);\n      newBlocks.splice(hoverIndex, 0, draggedBlock);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, selectedBlockIndex === dragIndex ? hoverIndex : selectedBlockIndex);\n      return newBlocks;\n    });\n\n    // Adjust selected index\n    if (selectedBlockIndex === dragIndex) {\n      setSelectedBlockIndex(hoverIndex);\n    } else if (selectedBlockIndex !== null) {\n      if (dragIndex < hoverIndex && selectedBlockIndex > dragIndex && selectedBlockIndex <= hoverIndex) {\n        setSelectedBlockIndex(s => s !== null ? s - 1 : null);\n      } else if (dragIndex > hoverIndex && selectedBlockIndex >= hoverIndex && selectedBlockIndex < dragIndex) {\n        setSelectedBlockIndex(s => s !== null ? s + 1 : null);\n      }\n    }\n  }, [selectedBlockIndex, saveToHistory]);\n  const dropBlockFromLibrary = useCallback((block, index) => {\n    // Cast the block to ensure it has all required properties\n    const blockWithRequiredProps = {\n      ...JSON.parse(JSON.stringify(block)),\n      blockId: block.blockId || block._id || `block-${Date.now()}`,\n      // Ensure blockId is never undefined\n      content: block.content ? JSON.parse(JSON.stringify(block.content)) : {},\n      instanceId: `${block.blockId || block._id || 'new'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n    };\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = [...prevBlocks];\n      newBlocks.splice(index, 0, blockWithRequiredProps);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, index);\n      return newBlocks;\n    });\n    setSelectedBlockIndex(index); // Select the newly dropped block\n  }, [saveToHistory]);\n  const removeBlock = useCallback(index => {\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = prevBlocks.filter((_, i) => i !== index);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, selectedBlockIndex === index ? null : selectedBlockIndex !== null && selectedBlockIndex > index ? selectedBlockIndex - 1 : selectedBlockIndex);\n      return newBlocks;\n    });\n    if (selectedBlockIndex === index) {\n      setSelectedBlockIndex(null);\n    } else if (selectedBlockIndex !== null && selectedBlockIndex > index) {\n      setSelectedBlockIndex(prevIndex => prevIndex !== null ? prevIndex - 1 : null);\n    }\n  }, [selectedBlockIndex, saveToHistory]);\n  const duplicateBlock = useCallback(index => {\n    setEditorBlocks(prevBlocks => {\n      const blockToDuplicate = prevBlocks[index];\n      const duplicatedBlock = {\n        ...JSON.parse(JSON.stringify(blockToDuplicate)),\n        instanceId: `${blockToDuplicate.blockId || blockToDuplicate._id || 'dup'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n      };\n      const newBlocks = [...prevBlocks];\n      newBlocks.splice(index + 1, 0, duplicatedBlock);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, index + 1);\n      return newBlocks;\n    });\n    setSelectedBlockIndex(index + 1); // Select the duplicated block\n  }, [saveToHistory]);\n  const updateBlockContent = useCallback((index, updatedContent) => {\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = prevBlocks.map((block, i) => i === index ? {\n        ...block,\n        content: {\n          ...(block.content || {}),\n          ...updatedContent\n        }\n      } : block);\n\n      // Save to history for undo/redo (debounced to avoid too many history entries)\n      const debouncedSave = setTimeout(() => {\n        saveToHistory(newBlocks, selectedBlockIndex);\n      }, 1000);\n      return newBlocks;\n    });\n  }, [saveToHistory, selectedBlockIndex]);\n\n  // --- Save Handler --- //\n  const handleSave = async (templateNameToSave, isPublicStatus = false) => {\n    setIsSaving(true);\n    setSaveError(null);\n    try {\n      const mjml = generateMjmlFromBlocks(editorBlocks);\n      const {\n        html,\n        errors: conversionErrors\n      } = mjml2html(mjml);\n      if (conversionErrors && conversionErrors.length > 0) {\n        console.warn('MJML conversion errors detected during save:', conversionErrors);\n        // Consider not saving if critical errors occurred\n      }\n      const blockIds = editorBlocks.map(b => String(b.blockId || b._id)) // Ensure string ID\n      .filter(Boolean); // Filter out any potential undefined/null\n\n      const payload = {\n        templateId: template === null || template === void 0 ? void 0 : template._id,\n        templateName: templateNameToSave,\n        mjml,\n        html,\n        blockIds,\n        subject: (template === null || template === void 0 ? void 0 : template.subject) || 'Untitled Template',\n        tags: (template === null || template === void 0 ? void 0 : template.tags) || [],\n        isPublic: isPublicStatus,\n        description: (template === null || template === void 0 ? void 0 : template.description) || '',\n        aiPrompt: (template === null || template === void 0 ? void 0 : template.aiPrompt) || '',\n        isAiGenerated: (template === null || template === void 0 ? void 0 : template.isAiGenerated) || false\n      };\n\n      // Add userId if it's a new template - CRITICAL: Ensure req.user is populated in backend\n      // This assumes the backend will get the userId from the authenticated request (authenticateJWT)\n      // if (!payload.templateId) {\n      //   payload.userId = template?.userId; // Might be set from prefs initially\n      // }\n\n      const response = await api.post('/templates/save', payload);\n      const savedTemplate = response.data.template;\n      setTemplate(savedTemplate);\n\n      // Re-sync editor blocks if necessary (e.g., if backend modifies content/IDs)\n      // This part needs careful implementation if backend modifies block content on save\n      if (savedTemplate.blockIds && availableBlocks.length > 0) {\n        const populatedBlocks = savedTemplate.blockIds.map((id, index) => {\n          var _savedTemplate$blocks, _savedTemplate$blocks2;\n          const foundBlockDef = availableBlocks.find(b => b.blockId === id || b._id === id);\n          const instanceContent = ((_savedTemplate$blocks = savedTemplate.blocks) === null || _savedTemplate$blocks === void 0 ? void 0 : (_savedTemplate$blocks2 = _savedTemplate$blocks[index]) === null || _savedTemplate$blocks2 === void 0 ? void 0 : _savedTemplate$blocks2.content) || (foundBlockDef === null || foundBlockDef === void 0 ? void 0 : foundBlockDef.content) || {};\n          const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`;\n          return foundBlockDef ? {\n            ...foundBlockDef,\n            content: {\n              ...instanceContent\n            },\n            instanceId\n          } : null;\n        }).filter(b => b !== null);\n        setEditorBlocks(populatedBlocks);\n      }\n      setShowSaveModal(false);\n      console.log('Template saved successfully!');\n      if (!templateId && savedTemplate._id) {\n        navigate(`/email-editor/${savedTemplate._id}`, {\n          replace: true\n        });\n      }\n      return savedTemplate;\n    } catch (err) {\n      var _err$response, _err$response$data3;\n      console.error('Error saving template:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data3 = _err$response.data) === null || _err$response$data3 === void 0 ? void 0 : _err$response$data3.error) || err.message || 'Failed to save template.';\n      setSaveError(errorMessage);\n      return null;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // --- Drop Target for Canvas --- //\n  const [{\n    isOverCanvas\n  }, drop] = useDrop(() => ({\n    accept: [ItemTypes.BLOCK, ItemTypes.LIBRARY_BLOCK],\n    drop: (item, monitor) => {\n      if (monitor.didDrop()) return;\n      const editorDiv = editorCanvasRef.current;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset || !editorDiv) return;\n      const hoverIndex = findDropIndex(clientOffset.y, editorDiv);\n      if (item.type === ItemTypes.LIBRARY_BLOCK) {\n        dropBlockFromLibrary(item.block, hoverIndex);\n      }\n      // Reordering is handled by DraggableBlock's hover\n    },\n    collect: monitor => ({\n      isOverCanvas: monitor.isOver({\n        shallow: true\n      })\n    })\n  }), [editorBlocks, moveBlock, dropBlockFromLibrary]); // Ensure correct dependencies\n\n  // Helper function to find the correct drop index\n  const findDropIndex = (clientY, container) => {\n    if (clientY === undefined) return editorBlocks.length;\n    const containerRect = container.getBoundingClientRect();\n    const offsetY = clientY - containerRect.top + container.scrollTop;\n    let calculatedIndex = editorBlocks.length;\n    const children = Array.from(container.children);\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (!child.classList || !child.classList.contains('draggable-block')) continue;\n      const childTop = child.offsetTop;\n      const childHeight = child.offsetHeight;\n      const middleY = childTop + childHeight / 2;\n      if (offsetY < middleY) {\n        calculatedIndex = i;\n        break;\n      }\n    }\n    return calculatedIndex;\n  };\n\n  // Cleanup cache when component unmounts\n  useEffect(() => {\n    return () => {\n      // Clear the entire cache when this editor instance is unmounted\n      templateCache.clear();\n    };\n  }, []);\n\n  // Add a cleanup function for memory management\n  const cleanupUnusedResources = useCallback(() => {\n    // Clear any unused thumbnails from memory if browser gets low on memory\n    if ('memory' in performance) {\n      const memoryInfo = performance.memory;\n      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {\n        console.log('[EmailEditor] Memory pressure detected, cleaning up resources');\n        // Force a garbage collection if possible\n        clearStaleCache();\n        // You could also unload any non-visible thumbnails or other large objects\n      }\n    }\n  }, []);\n\n  // Listen for low memory events\n  useEffect(() => {\n    if ('onmemorywarning' in window) {\n      window.addEventListener('memorywarning', cleanupUnusedResources);\n      return () => {\n        window.removeEventListener('memorywarning', cleanupUnusedResources);\n      };\n    }\n  }, [cleanupUnusedResources]);\n\n  // --- Render Logic --- //\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-screen text-gray-600 text-lg\",\n      children: \"Loading Editor...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 843,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Critical error on initial load\n  if (error && !template) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 m-4 text-red-700 bg-red-100 border border-red-400 rounded\",\n      children: [\"Error loading editor configuration: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 848,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"email-editor flex flex-col h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-toolbar flex items-center justify-between px-4 py-2 bg-white border-b border-gray-200 shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-left flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-800\",\n          children: (template === null || template === void 0 ? void 0 : template.templateName) || 'Untitled Template'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: undo,\n            disabled: !canUndo,\n            className: \"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            title: \"Undo (Ctrl+Z)\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 862,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: redo,\n            disabled: !canRedo,\n            className: \"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            title: \"Redo (Ctrl+Y)\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-center flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-mode-toggle flex items-center bg-gray-100 rounded-lg p-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setViewMode('edit'),\n            className: `toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${viewMode === 'edit' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 15\n            }, this), \"Edit\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setViewMode('split'),\n            className: `toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${viewMode === 'split' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 906,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 905,\n              columnNumber: 15\n            }, this), \"Split\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setViewMode('preview'),\n            className: `toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${viewMode === 'preview' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 15\n            }, this), \"Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 887,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 885,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-right flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center bg-gray-100 rounded-lg p-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setPreviewMode('desktop'),\n            className: `px-3 py-1 rounded-md text-sm font-medium transition-colors ${previewMode === 'desktop' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: \"Desktop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setPreviewMode('mobile'),\n            className: `px-3 py-1 rounded-md text-sm font-medium transition-colors ${previewMode === 'mobile' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: \"Mobile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 927,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setTemplate(prev => ({\n              ...(prev || {}),\n              templateName: (prev === null || prev === void 0 ? void 0 : prev.templateName) || 'Untitled Template'\n            }));\n            setSaveError(null);\n            setShowSaveModal(true);\n          },\n          disabled: isSaving,\n          className: \"save-button px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors disabled:bg-indigo-400 disabled:cursor-not-allowed\",\n          children: isSaving ? 'Saving...' : 'Save Template'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 947,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 925,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 854,\n      columnNumber: 7\n    }, this), error && templateId && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 text-sm text-red-700 bg-red-100 border-b border-red-300 text-center\",\n      children: [\"Error: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 963,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `editor-content flex flex-1 overflow-hidden`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"block-library-panel w-64 bg-white border-r border-gray-200 flex flex-col overflow-hidden shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"px-4 py-3 text-base font-semibold text-gray-800 border-b border-gray-200 whitespace-nowrap\",\n          children: \"Block Library\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BlockLibrary, {\n          blocks: availableBlocks,\n          onAddBlock: block => dropBlockFromLibrary(block, editorBlocks.length) // Click adds to end\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 968,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: drop,\n        className: `editor-workspace flex-1 flex flex-col overflow-hidden bg-gray-200 ${isOverCanvas ? 'outline-2 outline-dashed outline-indigo-500' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: editorCanvasRef,\n          className: \"blocks-container flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto\",\n          children: editorBlocks.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-blocks flex items-center justify-center h-full text-gray-500 text-center p-8 border-2 border-dashed border-gray-300 rounded-lg min-h-[200px]\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Drag blocks from the library here\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 981,\n                columnNumber: 53\n              }, this), \"or click a block in the library to add it.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 981,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 980,\n            columnNumber: 15\n          }, this) : editorBlocks.map((block, index) => /*#__PURE__*/_jsxDEV(DraggableBlock, {\n            // Use unique instanceId\n            block: block,\n            index: index,\n            moveBlock: moveBlock,\n            removeBlock: removeBlock,\n            duplicateBlock: duplicateBlock,\n            isSelected: selectedBlockIndex === index,\n            onClick: () => setSelectedBlockIndex(index)\n          }, block.instanceId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 978,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"right-panel w-80 md:w-96 lg:w-[500px] border-l border-gray-300 flex flex-col shrink-0 bg-white shadow-lg\",\n        children: selectedBlockIndex === null || editorBlocks.length === 0 ?\n        /*#__PURE__*/\n        // Show Preview when no block is selected OR if editor is empty\n        _jsxDEV(\"div\", {\n          className: \"preview-panel flex-1 flex flex-col overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200\",\n            children: \"Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(EmailPreview, {\n            html: previewHtml,\n            mode: previewMode,\n            iframeRef: previewIframeRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1004,\n          columnNumber: 17\n        }, this) :\n        /*#__PURE__*/\n        // Show Block Editor when a block is selected\n        _jsxDEV(\"div\", {\n          className: \"block-editor-panel flex-1 flex flex-col overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200 flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Edit: \", ((_editorBlocks$selecte = editorBlocks[selectedBlockIndex]) === null || _editorBlocks$selecte === void 0 ? void 0 : _editorBlocks$selecte.name) || 'Block']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1016,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedBlockIndex(null),\n              className: \"text-sm text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100\",\n              title: \"Close Editor\",\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1017,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1015,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto\",\n            children: editorBlocks[selectedBlockIndex] &&\n            /*#__PURE__*/\n            // Check block exists before rendering\n            _jsxDEV(BlockEditor, {\n              block: editorBlocks[selectedBlockIndex],\n              onUpdate: content => {\n                if (selectedBlockIndex !== null) {\n                  updateBlockContent(selectedBlockIndex, content);\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1027,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1014,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1001,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 966,\n      columnNumber: 7\n    }, this), showSaveModal && /*#__PURE__*/_jsxDEV(SaveTemplateModal, {\n      initialName: (template === null || template === void 0 ? void 0 : template.templateName) || 'Untitled Template',\n      onSave: handleSave,\n      onCancel: () => {\n        setShowSaveModal(false);\n        setSaveError(null);\n      },\n      isSaving: isSaving,\n      error: saveError // Pass save error to modal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1045,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 852,\n    columnNumber: 5\n  }, this);\n};\n\n// Draggable Block Component (Internal to EmailEditor)\n_s(EmailEditor, \"g/JfYLZL7+7SpgSwW+wdzhU6Qfw=\", false, function () {\n  return [useParams, useNavigate, useDrop];\n});\n_c = EmailEditor;\nconst DraggableBlock = ({\n  block,\n  index,\n  moveBlock,\n  removeBlock,\n  isSelected,\n  onClick\n}) => {\n  _s2();\n  const ref = useRef(null);\n  const [{\n    handlerId\n  }, drop] = useDrop(() => ({\n    accept: ItemTypes.BLOCK,\n    hover: (item, monitor) => {\n      if (!ref.current) return;\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) return;\n      const hoverBoundingRect = ref.current.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n      const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;\n      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;\n      moveBlock(dragIndex, hoverIndex);\n      item.index = hoverIndex; // Mutate monitor item for performance\n    },\n    collect: monitor => ({\n      handlerId: monitor.getHandlerId()\n    })\n  }), [index, moveBlock]); // Dependencies for useDrop hover logic\n\n  const [{\n    isDragging\n  }, drag] = useDrag(() => ({\n    type: ItemTypes.BLOCK,\n    item: {\n      index,\n      id: block.instanceId,\n      type: ItemTypes.BLOCK\n    },\n    // Return item as a function\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [index, block.instanceId]); // Dependencies for useDrag\n\n  drag(drop(ref)); // Combine drag and drop refs\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: ref,\n    \"data-handler-id\": handlerId,\n    className: `draggable-block bg-white border rounded-md mb-4 cursor-move shadow-sm ${isDragging ? 'opacity-40 border-blue-500' : 'hover:border-blue-400 hover:shadow-md'} ${isSelected ? 'border-blue-500 ring-2 ring-blue-300 ring-offset-1' : 'border-gray-200'}` // styles/editor.css\n    ,\n    onClick: onClick,\n    style: {\n      opacity: isDragging ? 0.4 : 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-header flex items-center justify-between px-3 py-1.5 border-b border-gray-200 bg-gray-50 rounded-t-md text-xs\",\n      children: [\" \", /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block-type font-medium text-gray-700 truncate pr-2\",\n        title: block.name,\n        children: [block.name, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-400\",\n          children: [\"(\", block.category, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1122,\n          columnNumber: 110\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"block-actions flex items-center space-x-1\",\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n          onClick: e => {\n            e.stopPropagation();\n            console.log(\"Duplicate block:\", index); // TODO: Implement duplicate functionality\n          },\n          title: \"Duplicate Block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 20\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1135,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"remove-block p-1 text-gray-400 hover:text-red-600 transition-colors\" // styles/editor.css\n          ,\n          onClick: e => {\n            e.stopPropagation();\n            removeBlock(index);\n          },\n          title: \"Remove Block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1151,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1150,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1140,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-content-preview p-3 text-sm text-gray-600 bg-white rounded-b-md min-h-[50px]\",\n      children: [(() => {\n        // Display appropriate preview based on block type\n        if (block.blockId === 'header/simple-nav') {\n          return 'Navigation Header';\n        } else if (block.blockId === 'layout/hero') {\n          var _block$content;\n          return ((_block$content = block.content) === null || _block$content === void 0 ? void 0 : _block$content.heroHeadline) || 'Hero Section';\n        } else if (block.blockId === 'content/headline') {\n          var _block$content2;\n          return ((_block$content2 = block.content) === null || _block$content2 === void 0 ? void 0 : _block$content2.headline) || 'Headline';\n        } else if (block.blockId === 'product/grid') {\n          var _block$content3, _block$content4, _block$content5;\n          return 'Product Grid: ' + [(_block$content3 = block.content) === null || _block$content3 === void 0 ? void 0 : _block$content3.prod1_name, (_block$content4 = block.content) === null || _block$content4 === void 0 ? void 0 : _block$content4.prod2_name, (_block$content5 = block.content) === null || _block$content5 === void 0 ? void 0 : _block$content5.prod3_name].filter(Boolean).join(', ');\n        } else if (block.blockId === 'cta/button') {\n          var _block$content6;\n          return ((_block$content6 = block.content) === null || _block$content6 === void 0 ? void 0 : _block$content6.buttonText) || 'Button';\n        } else if (block.blockId === 'footer/standard') {\n          var _block$content7;\n          return (_block$content7 = block.content) !== null && _block$content7 !== void 0 && _block$content7.companyName ? `Footer: ${block.content.companyName}` : 'Standard Footer';\n        } else {\n          var _block$content8, _block$content9, _block$content9$body, _block$content10;\n          // Fallback to original logic\n          return ((_block$content8 = block.content) === null || _block$content8 === void 0 ? void 0 : _block$content8.headline) || ((_block$content9 = block.content) === null || _block$content9 === void 0 ? void 0 : (_block$content9$body = _block$content9.body) === null || _block$content9$body === void 0 ? void 0 : _block$content9$body.substring(0, 50)) + ((_block$content10 = block.content) !== null && _block$content10 !== void 0 && _block$content10.body && block.content.body.length > 50 ? '...' : '') || block.name;\n        }\n      })(), block.thumbnail && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: block.thumbnail,\n        alt: `${block.name} thumbnail`,\n        className: \"mx-auto h-12 mt-2 opacity-75 object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1184,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1157,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1114,\n    columnNumber: 5\n  }, this);\n};\n_s2(DraggableBlock, \"Bm9lse0dSUrMRW3+tCDIsw+uDQ8=\", false, function () {\n  return [useDrop, useDrag];\n});\n_c2 = DraggableBlock;\nexport { EmailEditor };\nexport default EmailEditor;\nvar _c, _c2;\n$RefreshReg$(_c, \"EmailEditor\");\n$RefreshReg$(_c2, \"DraggableBlock\");", "map": {"version": 3, "names": ["React", "useCallback", "useEffect", "useRef", "useState", "mjml2html", "useDrag", "useDrop", "useNavigate", "useParams", "api", "BlockEditor", "BlockLibrary", "EmailPreview", "SaveTemplateModal", "jsxDEV", "_jsxDEV", "API_URL", "process", "env", "REACT_APP_API_URL", "ItemTypes", "BLOCK", "LIBRARY_BLOCK", "templateCache", "Map", "CACHE_TTL", "generate<PERSON>ache<PERSON>ey", "blocks", "map", "block", "instanceId", "content", "blockId", "_id", "JSON", "stringify", "join", "clearStaleCache", "now", "Date", "for<PERSON>ach", "entry", "key", "timestamp", "delete", "EmailEditor", "_s", "_editorBlocks$selecte", "templateId", "navigate", "template", "setTemplate", "<PERSON><PERSON><PERSON><PERSON>", "setEditorBlocks", "availableBlocks", "setAvailableBlocks", "selectedBlockIndex", "setSelectedBlockIndex", "previewHtml", "setPreviewHtml", "previewMode", "setPreviewMode", "isSaving", "setIsSaving", "showSaveModal", "setShowSaveModal", "isLoading", "setIsLoading", "error", "setError", "saveError", "setSaveError", "userPreferences", "setUserPreferences", "templateName", "setTemplateName", "isDragActive", "setIsDragActive", "showBlockLibrary", "setShowBlockLibrary", "showPreview", "setShowPreview", "isGeneratingPreview", "setIsGeneratingPreview", "viewMode", "setViewMode", "undoRedoState", "setUndoRedoState", "history", "currentIndex", "maxHistorySize", "canUndo", "useMemo", "canRedo", "length", "previewIframeRef", "editorCanvasRef", "saveToHistory", "selectedIndex", "prev", "newState", "parse", "newHistory", "slice", "push", "shift", "undo", "newIndex", "state", "redo", "handleKeyDown", "e", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "preventDefault", "window", "addEventListener", "removeEventListener", "isMounted", "fetchData", "console", "log", "blocksPromise", "get", "prefsPromise", "templatePromise", "Promise", "resolve", "blocksResponse", "prefsResponse", "templateResponse", "all", "fetchedAvailableBlocks", "data", "preferences", "templateData", "brandColors", "defaultFonts", "blockIds", "populatedBlocks", "id", "index", "_templateData$blocks", "foundBlockDef", "find", "b", "warn", "instanceBlockData", "instanceContent", "Math", "random", "toString", "substring", "blockForState", "filter", "_templateData$blockId", "hasBlockIds", "blockIdsLength", "hasFetchedBlocks", "fetchedBlocksLength", "subject", "userId", "err", "errorMsg", "response", "_err$config", "_err$config$url", "config", "url", "includes", "status", "_err$response$data", "_err$response$data2", "message", "generateMjmlFromBlocks", "blockList", "_template$brandColors", "_template$defaultFont", "cache<PERSON>ey", "has", "cached", "mjml", "chunkSize", "mjml<PERSON>ody<PERSON><PERSON>nt", "i", "chunk", "chunkContent", "blockMjml", "Object", "keys", "value", "placeholder", "undefined", "Array", "isArray", "linksHtml", "link", "name", "replace", "iconsHtml", "icon", "_icon$platform", "platform", "toLowerCase", "stringValue", "String", "replaceAll", "camelCasePlaceholder", "g", "toUpperCase", "snakeCasePlaceholder", "letter", "primaryColor", "primary", "backgroundColor", "background", "textColor", "text", "headingFont", "heading", "bodyFont", "body", "fullMjml", "set", "html", "generatePreview", "mjmlString", "Worker", "workerTimeout", "setTimeout", "errors", "validationLevel", "fallbackErr", "debounceTimeout", "clearTimeout", "moveBlock", "dragIndex", "hoverIndex", "prevBlocks", "newBlocks", "<PERSON><PERSON><PERSON>", "splice", "s", "dropBlockFromLibrary", "blockWithRequiredProps", "removeBlock", "_", "prevIndex", "duplicateBlock", "blockToDuplicate", "duplicatedBlock", "updateBlockContent", "updatedContent", "debouncedSave", "handleSave", "templateNameToSave", "isPublicStatus", "conversionErrors", "Boolean", "payload", "tags", "isPublic", "description", "aiPrompt", "isAiGenerated", "post", "savedTemplate", "_savedTemplate$blocks", "_savedTemplate$blocks2", "_err$response", "_err$response$data3", "errorMessage", "isOverCanvas", "drop", "accept", "item", "monitor", "didDrop", "editorD<PERSON>", "current", "clientOffset", "getClientOffset", "findDropIndex", "y", "type", "collect", "isOver", "shallow", "clientY", "container", "containerRect", "getBoundingClientRect", "offsetY", "top", "scrollTop", "calculatedIndex", "children", "from", "child", "classList", "contains", "childTop", "offsetTop", "childHeight", "offsetHeight", "middleY", "clear", "cleanupUnusedResources", "performance", "memoryInfo", "memory", "usedJSHeapSize", "jsHeapSizeLimit", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onAddBlock", "ref", "DraggableBlock", "isSelected", "mode", "iframeRef", "onUpdate", "initialName", "onSave", "onCancel", "_c", "_s2", "handlerId", "hover", "hoverBoundingRect", "hoverMiddleY", "bottom", "hoverClientY", "getHandlerId", "isDragging", "drag", "style", "opacity", "category", "stopPropagation", "xmlns", "_block$content", "heroHeadline", "_block$content2", "headline", "_block$content3", "_block$content4", "_block$content5", "prod1_name", "prod2_name", "prod3_name", "_block$content6", "buttonText", "_block$content7", "companyName", "_block$content8", "_block$content9", "_block$content9$body", "_block$content10", "thumbnail", "src", "alt", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/EmailEditor.tsx"], "sourcesContent": ["/**\n * Enhanced EmailEditor component for Driftly Email Generator\n * Provides advanced drag-and-drop interface for template creation and editing\n * Features: Real-time preview, responsive design, advanced block library, undo/redo\n */\n\nimport React, {\n  useCallback,\n  useEffect,\n  useRef,\n  useState,\n} from 'react';\n\nimport mjml2html from 'mjml-browser';\nimport {\n  DropTargetMonitor,\n  useDrag,\n  useDrop,\n} from 'react-dnd';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\n\nimport api from '../services/api'; // Corrected import path\n// Types - Adjust path as needed\nimport {\n  Block,\n  BlockContent,\n  BlockListResponse,\n  Template,\n  TemplateDetailResponse,\n  TemplateSaveResponse,\n  UserPreference,\n} from '../types/editor';\nimport BlockEditor from './BlockEditor';\n// Components - Adjust paths as needed\nimport BlockLibrary from './BlockLibrary';\nimport EmailPreview from './EmailPreview';\nimport SaveTemplateModal from './SaveTemplateModal';\n\n// Styles - Ensure this is imported in your main application entry point\n// e.g., import '../styles/editor.css';\n\nconst API_URL = process.env.REACT_APP_API_URL || '/api'; // Use environment variable\n\n// Define Drag Item Types\nconst ItemTypes = {\n  BLOCK: 'block',\n  LIBRARY_BLOCK: 'library_block'\n};\n\ninterface EditorDragItem {\n  index: number;\n  type: typeof ItemTypes.BLOCK;\n  id: string; // Unique instance ID of the block being dragged\n}\n\ninterface LibraryDragItem {\n  block: Block; // The block definition from the library\n  type: typeof ItemTypes.LIBRARY_BLOCK;\n}\n\n// Near the top of the file, add a template cache mechanism:\n// Cache for storing generated HTML previews\nconst templateCache = new Map<string, {\n  html: string;\n  timestamp: number;\n  mjml: string;\n}>();\n\n// Cache TTL (Time To Live) in milliseconds - 5 minutes\nconst CACHE_TTL = 5 * 60 * 1000;\n\n// Add this function before the EmailEditor component definition\n// Function to generate a cache key based on blocks\nconst generateCacheKey = (blocks: Block[]): string => {\n  return blocks.map(block => {\n    const { instanceId, content } = block;\n    // Include only essential data in cache key\n    return `${block.blockId || block._id}:${instanceId}:${JSON.stringify(content)}`;\n  }).join('|');\n};\n\n// Function to clear stale cache entries\nconst clearStaleCache = () => {\n  const now = Date.now();\n  templateCache.forEach((entry, key) => {\n    if (now - entry.timestamp > CACHE_TTL) {\n      templateCache.delete(key);\n    }\n  });\n};\n\n// Enhanced state management with undo/redo functionality\ninterface EditorState {\n  blocks: Block[];\n  selectedBlockIndex: number | null;\n  timestamp: number;\n}\n\ninterface UndoRedoState {\n  history: EditorState[];\n  currentIndex: number;\n  maxHistorySize: number;\n}\n\n// --- EmailEditor Component ---\n\nconst EmailEditor: React.FC = () => {\n  const { templateId } = useParams<{ templateId?: string }>();\n  const navigate = useNavigate();\n\n  // Core State\n  const [template, setTemplate] = useState<Partial<Template>>({});\n  const [editorBlocks, setEditorBlocks] = useState<Block[]>([]);\n  const [availableBlocks, setAvailableBlocks] = useState<Block[]>([]);\n  const [selectedBlockIndex, setSelectedBlockIndex] = useState<number | null>(null);\n  const [previewHtml, setPreviewHtml] = useState<string>('');\n  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');\n  const [isSaving, setIsSaving] = useState<boolean>(false);\n  const [showSaveModal, setShowSaveModal] = useState<boolean>(false);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [saveError, setSaveError] = useState<string | null>(null);\n  const [userPreferences, setUserPreferences] = useState<UserPreference | null>(null);\n  const [templateName, setTemplateName] = useState('');\n\n  // Enhanced UI State\n  const [isDragActive, setIsDragActive] = useState<boolean>(false);\n  const [showBlockLibrary, setShowBlockLibrary] = useState<boolean>(true);\n  const [showPreview, setShowPreview] = useState<boolean>(true);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState<boolean>(false);\n  const [viewMode, setViewMode] = useState<'edit' | 'preview' | 'split'>('split');\n\n  // Undo/Redo State\n  const [undoRedoState, setUndoRedoState] = useState<UndoRedoState>({\n    history: [],\n    currentIndex: -1,\n    maxHistorySize: 50\n  });\n\n  // Performance optimization - memoized values\n  const canUndo = useMemo(() => undoRedoState.currentIndex > 0, [undoRedoState.currentIndex]);\n  const canRedo = useMemo(() => undoRedoState.currentIndex < undoRedoState.history.length - 1, [undoRedoState.currentIndex, undoRedoState.history.length]);\n\n  // Refs\n  const previewIframeRef = useRef<HTMLIFrameElement>(null);\n  const editorCanvasRef = useRef<HTMLDivElement>(null);\n\n  // --- Undo/Redo Functions --- //\n  const saveToHistory = useCallback((blocks: Block[], selectedIndex: number | null) => {\n    setUndoRedoState(prev => {\n      const newState: EditorState = {\n        blocks: JSON.parse(JSON.stringify(blocks)), // Deep clone\n        selectedBlockIndex: selectedIndex,\n        timestamp: Date.now()\n      };\n\n      // Remove any future history if we're not at the end\n      const newHistory = prev.history.slice(0, prev.currentIndex + 1);\n      newHistory.push(newState);\n\n      // Limit history size\n      if (newHistory.length > prev.maxHistorySize) {\n        newHistory.shift();\n      }\n\n      return {\n        ...prev,\n        history: newHistory,\n        currentIndex: newHistory.length - 1\n      };\n    });\n  }, []);\n\n  const undo = useCallback(() => {\n    if (!canUndo) return;\n\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex - 1;\n      const state = prev.history[newIndex];\n\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canUndo]);\n\n  const redo = useCallback(() => {\n    if (!canRedo) return;\n\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex + 1;\n      const state = prev.history[newIndex];\n\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canRedo]);\n\n  // Keyboard shortcuts for undo/redo\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {\n        e.preventDefault();\n        undo();\n      } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {\n        e.preventDefault();\n        redo();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [undo, redo]);\n\n  // --- Data Fetching --- //\n  useEffect(() => {\n    let isMounted = true;\n    const fetchData = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Fetch available blocks first (always needed)\n        console.log(`Fetching blocks from ${API_URL}/blocks`);\n        // Use the shared api instance with BlockListResponse type\n        const blocksPromise = api.get<BlockListResponse>('/blocks');\n\n        // Fetch user preferences to potentially apply brand colors/fonts later\n        console.log(`Fetching user preferences from ${API_URL}/user/preferences`);\n         // Use the shared api instance with correct inline type\n        const prefsPromise = api.get<{ preferences: UserPreference }>(`/user/preferences`);\n\n        // Fetch template data if ID exists\n        const templatePromise = templateId\n          // Use the shared api instance with TemplateDetailResponse type\n          ? api.get<TemplateDetailResponse>(`/templates/${templateId}`)\n          : Promise.resolve(null);\n\n        const [blocksResponse, prefsResponse, templateResponse] = await Promise.all([\n          blocksPromise,\n          prefsPromise,\n          templatePromise\n        ]);\n\n        // Access blocks correctly from response.data.data\n        const fetchedAvailableBlocks = blocksResponse.data.data || [];\n        console.log(`Fetched ${fetchedAvailableBlocks.length} available blocks.`);\n        setAvailableBlocks(fetchedAvailableBlocks);\n\n        // Process user preferences\n        const userPreferences = prefsResponse.data.preferences; // Store preferences\n        // TODO: Apply user preferences (e.g., brand colors, fonts)\n\n        if (templateResponse && templateResponse.data.template) {\n          const templateData = templateResponse.data.template; // Correct path now\n          console.log('[EmailEditor] Fetched template data:', JSON.stringify(templateData, null, 2)); // Log fetched data\n\n          // Apply brand colors/fonts from preferences if not set on template\n          setTemplate({\n              ...templateData,\n              brandColors: templateData.brandColors || userPreferences?.brandColors,\n              defaultFonts: templateData.defaultFonts || userPreferences?.defaultFonts,\n          });\n\n          // Populate editor blocks based on blockIds and fetched definitions\n          console.log('[EmailEditor] Attempting to populate blocks. Available block defs:', fetchedAvailableBlocks.length);\n          console.log('[EmailEditor] Template blockIds:', templateData.blockIds);\n          console.log('[EmailEditor] Template blocks data:', templateData.blocks);\n\n          if (templateData.blockIds && templateData.blockIds.length > 0 && fetchedAvailableBlocks.length > 0) {\n            console.log('[EmailEditor] Conditions met, proceeding to map blockIds.');\n            const populatedBlocks = templateData.blockIds.map((id: string, index: number) => {\n              console.log(`[EmailEditor] Mapping blockId: ${id} at index: ${index}`);\n              // Match ID from templateData.blockIds with fetchedAvailableBlocks\n              const foundBlockDef = fetchedAvailableBlocks.find((b: Block) => b.blockId === id || b._id === id); // Check both blockId and _id\n              console.log(`[EmailEditor] ... Definition found for ${id}?`, foundBlockDef ? 'Yes' : 'No');\n\n              if (!foundBlockDef) {\n                console.warn(`[EmailEditor] Block definition not found for available block matching ID: ${id}. Skipping.`); // Refined warning\n                return null;\n              }\n\n              // Get content for this specific instance from templateData.blocks\n              const instanceBlockData = templateData.blocks?.[index]; // Get the block data saved for this instance\n              const instanceContent = instanceBlockData?.content || foundBlockDef.content || {}; // Use instance content, fallback to definition's default\n              console.log(`[EmailEditor] ... Instance content for ${id}:`, instanceContent);\n\n              const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`; // Unique key for React\n\n              // Return the combined block object for the editor state\n              const blockForState = {\n                  ...foundBlockDef, // Base definition (MJML, name, category etc.)\n                  content: { ...instanceContent }, // Specific content for this instance\n                  instanceId // Unique ID for this instance in the editor\n              };\n              console.log(`[EmailEditor] ... Created block object for state for ${id}:`, blockForState);\n              return blockForState;\n            }).filter(b => b !== null);\n\n            console.log(\"[EmailEditor] Populated blocks array before setting state:\", populatedBlocks);\n            setEditorBlocks(populatedBlocks as Block[]);\n          } else {\n            console.log(\"[EmailEditor] Condition for populating blocks NOT met:\", {\n                hasBlockIds: !!templateData.blockIds,\n                blockIdsLength: templateData.blockIds?.length,\n                hasFetchedBlocks: !!fetchedAvailableBlocks,\n                fetchedBlocksLength: fetchedAvailableBlocks?.length\n            });\n            setEditorBlocks([]); // Sets blocks to empty\n          }\n        } else {\n          // New template: initialize with preferences\n          console.log('Initializing new template state with preferences.');\n          setTemplate({\n            templateName: 'Untitled Template',\n            subject: 'Your Subject Here',\n            brandColors: userPreferences?.brandColors,\n            defaultFonts: userPreferences?.defaultFonts,\n            // Initialize other necessary fields if needed\n            userId: userPreferences?.userId // Important for saving later\n          });\n          setEditorBlocks([]);\n        }\n      } catch (err: any) {\n        console.error('Error loading editor data:', err);\n        let errorMsg = 'Failed to load editor data.'; // Default message\n\n        if (err.response) {\n          // Check if it's the template fetch that failed with 404\n          if (err.config?.url?.includes(`/templates/${templateId}`) && err.response.status === 404) {\n            errorMsg = `Template with ID ${templateId} not found. It may have been deleted.`;\n            // Optionally, redirect the user or clear the template state\n            // navigate('/templates'); // Example redirect\n            setTemplate({}); // Clear any partial template data\n            setEditorBlocks([]);\n          } else {\n            // Use the error message from the response if available, otherwise use the generic message\n            errorMsg = err.response.data?.error || err.response.data?.message || err.message || errorMsg;\n          }\n        } else {\n          // Network error or other issue\n          errorMsg = err.message || errorMsg;\n        }\n\n        setError(errorMsg);\n        // Fallback logic: Check if availableBlocks is empty (meaning block fetch might have also failed)\n        if (availableBlocks.length === 0) {\n            console.warn(\"Falling back to empty block list as blocks couldn't be fetched or list is empty.\");\n            setAvailableBlocks([]); // Ensure it's an empty array if blocks failed\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchData();\n\n    return () => {\n      isMounted = false;\n    };\n  // eslint-disable-next-line\n  }, [templateId]); // Rerun only when templateId changes\n\n  // --- MJML Generation & Preview Update --- //\n  const generateMjmlFromBlocks = useCallback((blockList: Block[]): string => {\n    // If there are no blocks, return early with an empty string\n    if (!blockList.length) return '';\n\n    // Generate a cache key for this block configuration\n    const cacheKey = generateCacheKey(blockList);\n\n    // Check if we have a cached version\n    if (templateCache.has(cacheKey)) {\n      const cached = templateCache.get(cacheKey);\n      if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {\n        console.log(\"[EmailEditor] Using cached MJML\");\n        return cached.mjml;\n      }\n    }\n\n    // Periodically clean up old cache entries\n    clearStaleCache();\n\n    // Process blocks in chunks if there are many\n    const chunkSize = 5;\n    let mjmlBodyContent = '';\n\n    for (let i = 0; i < blockList.length; i += chunkSize) {\n      const chunk = blockList.slice(i, i + chunkSize);\n      const chunkContent = chunk.map(block => {\n        let blockMjml = block.mjml || '';\n        const content = block.content || {};\n\n        // More robust replacement logic\n        Object.keys(content).forEach(key => {\n          const value = content[key];\n          const placeholder = `{{${key}}}`; // Standard placeholder\n\n          // Only process if value exists\n          if (value !== undefined && value !== null) {\n            // Handle arrays specifically (e.g., nav links, social icons)\n            if (Array.isArray(value)) {\n              // Existing array handling code...\n              if (key === 'nav_links' || key === 'navLinks') {\n                const linksHtml = value.map((link: any) =>\n                  `<mj-navbar-link href=\"${link.url || '#'}\" color=\"#1E3A8A\">${link.name || 'Link'}</mj-navbar-link>`\n                ).join('\\n');\n                blockMjml = blockMjml.replace('{{navLinksArea}}', linksHtml);\n              } else if (key === 'social_icons' || key === 'socialLinks') {\n                const iconsHtml = value.map((icon: any) =>\n                  `<mj-social-element name=\"${icon.platform?.toLowerCase() || 'share'}\" href=\"${icon.url || '#'}\"></mj-social-element>`\n                ).join('\\n');\n                blockMjml = blockMjml.replace('{{socialIconsArea}}', iconsHtml);\n              }\n            } else {\n              // More efficient single replacement (no array of placeholders)\n              const stringValue = String(value);\n              blockMjml = blockMjml.replaceAll(placeholder, stringValue);\n\n              // Only try alternative formats if there's a good chance they exist\n              if (blockMjml.includes('{{')) {\n                // Convert camelCase to snake_case for placeholder check if needed\n                const camelCasePlaceholder = `{{${key.replace(/_([a-z])/g, g => g[1].toUpperCase())}}}`;\n                const snakeCasePlaceholder = `{{${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)}}}`;\n\n                blockMjml = blockMjml.replaceAll(camelCasePlaceholder, stringValue);\n                blockMjml = blockMjml.replaceAll(snakeCasePlaceholder, stringValue);\n              }\n            }\n          }\n        });\n\n        // More efficient placeholder cleanup - only if needed\n        if (blockMjml.includes('{{')) {\n          blockMjml = blockMjml.replace(/\\{\\{[\\w.-]+\\}\\}/g, '');\n        }\n\n        return blockMjml;\n      }).join('\\n');\n\n      mjmlBodyContent += chunkContent;\n    }\n\n    // Use template state for colors/fonts, falling back to defaults\n    const brandColors = template?.brandColors ?? {};\n    const defaultFonts = template?.defaultFonts ?? {};\n    const primaryColor = brandColors.primary || '#4F46E5';\n    const backgroundColor = brandColors.background || '#f3f4f6';\n    const textColor = brandColors.text || '#111827';\n    const headingFont = defaultFonts.heading || 'Arial, sans-serif';\n    const bodyFont = defaultFonts.body || 'Arial, sans-serif';\n\n    // Construct the full MJML document\n    const fullMjml = `\n<mjml>\n  <mj-head>\n    <mj-title>${template?.subject || 'Email Template'}</mj-title>\n    <mj-font name=\"Roboto\" href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700\" />\n    <mj-attributes>\n      <mj-all padding=\"0px\" font-family=\"${bodyFont}\" />\n      <mj-text padding=\"10px 25px\" font-size=\"14px\" line-height=\"1.5\" color=\"${textColor}\" />\n      <mj-section padding=\"10px 0\" />\n      <mj-column padding=\"5px\" />\n      <mj-button background-color=\"${primaryColor}\" color=\"#ffffff\" font-weight=\"bold\" border-radius=\"4px\" padding=\"10px 20px\" />\n      <mj-image padding=\"0px\" />\n    </mj-attributes>\n    <mj-style inline=\"inline\">\n      /* Inline styles */\n      a { color: ${primaryColor} !important; text-decoration: none !important; }\n    </mj-style>\n     <mj-style>\n      /* Embedded styles */\n      .hover-link:hover { text-decoration: underline !important; }\n    </mj-style>\n  </mj-head>\n  <mj-body background-color=\"${backgroundColor}\">\n    ${mjmlBodyContent}\n  </mj-body>\n</mjml>`;\n\n    // Store in cache\n    templateCache.set(cacheKey, {\n      mjml: fullMjml,\n      html: '', // Will be populated on first HTML conversion\n      timestamp: Date.now()\n    });\n\n    return fullMjml;\n  }, [template]);\n\n  useEffect(() => {\n    const generatePreview = () => {\n      if (!editorBlocks || editorBlocks.length === 0) {\n        setPreviewHtml('<div style=\"display: flex; justify-content: center; align-items: center; height: 100%; color: grey; padding: 20px; text-align: center;\">Drag blocks here to build your email</div>');\n        return;\n      }\n\n      try {\n        // Generate cache key\n        const cacheKey = generateCacheKey(editorBlocks);\n\n        // Check for valid cached HTML\n        if (templateCache.has(cacheKey)) {\n          const cached = templateCache.get(cacheKey);\n          if (cached && cached.html && (Date.now() - cached.timestamp) < CACHE_TTL) {\n            console.log(\"[EmailEditor Preview] Using cached HTML\");\n            setPreviewHtml(cached.html);\n            return;\n          }\n        }\n\n        // If we get here, generate a new preview\n        const mjmlString = generateMjmlFromBlocks(editorBlocks);\n        console.log(\"[EmailEditor Preview] Generating fresh HTML preview\");\n\n        // Use a web worker for MJML conversion if possible\n        if (window.Worker) {\n          // Note: You would need to create a separate mjml-worker.js file\n          // This is just demonstrating the concept\n          const workerTimeout = setTimeout(() => {\n            // Fallback if worker takes too long\n            try {\n              const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });\n              setPreviewHtml(html);\n              // Update cache\n              const cacheKey = generateCacheKey(editorBlocks);\n              if (templateCache.has(cacheKey)) {\n                const entry = templateCache.get(cacheKey);\n                if (entry) {\n                  entry.html = html;\n                  entry.timestamp = Date.now();\n                }\n              }\n            } catch (fallbackErr: any) {\n              console.error('[EmailEditor Preview] Error in fallback conversion:', fallbackErr);\n              setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${fallbackErr?.message || 'Unknown error'}</div>`);\n            }\n          }, 1000); // 1 second timeout\n\n          // This is just conceptual - actual implementation would need the worker file\n          // const worker = new Worker('/mjml-worker.js');\n          // worker.postMessage(mjmlString);\n          // worker.onmessage = (e) => {\n          //   clearTimeout(workerTimeout);\n          //   const { html, errors } = e.data;\n          //   setPreviewHtml(html);\n          //   // Update cache\n          //   updateHtmlCache(cacheKey, html);\n          // };\n        } else {\n          // Direct conversion when Web Workers aren't available\n          const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });\n          if (errors && errors.length > 0) {\n            console.warn('[EmailEditor Preview] MJML Validation:', errors.length, 'issues');\n          }\n          setPreviewHtml(html);\n\n          // Update cache\n          if (templateCache.has(cacheKey)) {\n            const entry = templateCache.get(cacheKey);\n            if (entry) {\n              entry.html = html;\n              entry.timestamp = Date.now();\n            }\n          }\n        }\n      } catch (err: any) {\n        console.error('[EmailEditor Preview] Error generating preview:', err);\n        setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${err?.message || 'Unknown error'}</div>`);\n      }\n    };\n\n    // Debounce the preview generation to avoid too many updates\n    const debounceTimeout = setTimeout(generatePreview, 300);\n    return () => clearTimeout(debounceTimeout);\n  }, [editorBlocks, generateMjmlFromBlocks]);\n\n  // --- Enhanced DND Callbacks --- //\n  const moveBlock = useCallback((dragIndex: number, hoverIndex: number) => {\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = [...prevBlocks];\n          const [draggedBlock] = newBlocks.splice(dragIndex, 1);\n          newBlocks.splice(hoverIndex, 0, draggedBlock);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, selectedBlockIndex === dragIndex ? hoverIndex : selectedBlockIndex);\n\n          return newBlocks;\n      });\n\n      // Adjust selected index\n      if (selectedBlockIndex === dragIndex) {\n          setSelectedBlockIndex(hoverIndex);\n      } else if (selectedBlockIndex !== null) {\n          if (dragIndex < hoverIndex && selectedBlockIndex > dragIndex && selectedBlockIndex <= hoverIndex) {\n              setSelectedBlockIndex(s => (s !== null ? s - 1 : null));\n          } else if (dragIndex > hoverIndex && selectedBlockIndex >= hoverIndex && selectedBlockIndex < dragIndex) {\n              setSelectedBlockIndex(s => (s !== null ? s + 1 : null));\n          }\n      }\n  }, [selectedBlockIndex, saveToHistory]);\n\n  const dropBlockFromLibrary = useCallback((block: any, index: number) => {\n      // Cast the block to ensure it has all required properties\n      const blockWithRequiredProps = {\n          ...JSON.parse(JSON.stringify(block)),\n          blockId: block.blockId || block._id || `block-${Date.now()}`, // Ensure blockId is never undefined\n          content: block.content ? JSON.parse(JSON.stringify(block.content)) : {},\n          instanceId: `${block.blockId || block._id || 'new'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n      };\n\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = [...prevBlocks];\n          newBlocks.splice(index, 0, blockWithRequiredProps as Block);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, index);\n\n          return newBlocks;\n      });\n      setSelectedBlockIndex(index); // Select the newly dropped block\n  }, [saveToHistory]);\n\n  const removeBlock = useCallback((index: number) => {\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = prevBlocks.filter((_, i) => i !== index);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, selectedBlockIndex === index ? null :\n            (selectedBlockIndex !== null && selectedBlockIndex > index ? selectedBlockIndex - 1 : selectedBlockIndex));\n\n          return newBlocks;\n      });\n\n      if (selectedBlockIndex === index) {\n          setSelectedBlockIndex(null);\n      } else if (selectedBlockIndex !== null && selectedBlockIndex > index) {\n          setSelectedBlockIndex(prevIndex => (prevIndex !== null ? prevIndex - 1 : null));\n      }\n  }, [selectedBlockIndex, saveToHistory]);\n\n  const duplicateBlock = useCallback((index: number) => {\n      setEditorBlocks(prevBlocks => {\n          const blockToDuplicate = prevBlocks[index];\n          const duplicatedBlock = {\n              ...JSON.parse(JSON.stringify(blockToDuplicate)),\n              instanceId: `${blockToDuplicate.blockId || blockToDuplicate._id || 'dup'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n          };\n\n          const newBlocks = [...prevBlocks];\n          newBlocks.splice(index + 1, 0, duplicatedBlock);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, index + 1);\n\n          return newBlocks;\n      });\n      setSelectedBlockIndex(index + 1); // Select the duplicated block\n  }, [saveToHistory]);\n\n  const updateBlockContent = useCallback((index: number, updatedContent: Partial<BlockContent>) => {\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = prevBlocks.map((block, i) =>\n              i === index\n                  ? { ...block, content: { ...(block.content || {}), ...updatedContent } }\n                  : block\n          );\n\n          // Save to history for undo/redo (debounced to avoid too many history entries)\n          const debouncedSave = setTimeout(() => {\n              saveToHistory(newBlocks, selectedBlockIndex);\n          }, 1000);\n\n          return newBlocks;\n      });\n  }, [saveToHistory, selectedBlockIndex]);\n\n  // --- Save Handler --- //\n  const handleSave = async (templateNameToSave: string, isPublicStatus: boolean = false) => {\n    setIsSaving(true);\n    setSaveError(null);\n    try {\n      const mjml = generateMjmlFromBlocks(editorBlocks);\n      const { html, errors: conversionErrors } = mjml2html(mjml);\n      if (conversionErrors && conversionErrors.length > 0) {\n        console.warn('MJML conversion errors detected during save:', conversionErrors);\n        // Consider not saving if critical errors occurred\n      }\n\n      const blockIds = editorBlocks\n        .map(b => String(b.blockId || b._id)) // Ensure string ID\n        .filter(Boolean); // Filter out any potential undefined/null\n\n      const payload: Partial<Template> & { templateId?: string; blockIds: string[] } = {\n        templateId: template?._id,\n        templateName: templateNameToSave,\n        mjml,\n        html,\n        blockIds,\n        subject: template?.subject || 'Untitled Template',\n        tags: template?.tags || [],\n        isPublic: isPublicStatus,\n        description: template?.description || '',\n        aiPrompt: template?.aiPrompt || '',\n        isAiGenerated: template?.isAiGenerated || false\n      };\n\n      // Add userId if it's a new template - CRITICAL: Ensure req.user is populated in backend\n      // This assumes the backend will get the userId from the authenticated request (authenticateJWT)\n      // if (!payload.templateId) {\n      //   payload.userId = template?.userId; // Might be set from prefs initially\n      // }\n\n      const response = await api.post<TemplateSaveResponse>('/templates/save', payload);\n      const savedTemplate = response.data.template;\n      setTemplate(savedTemplate);\n\n      // Re-sync editor blocks if necessary (e.g., if backend modifies content/IDs)\n      // This part needs careful implementation if backend modifies block content on save\n      if (savedTemplate.blockIds && availableBlocks.length > 0) {\n         const populatedBlocks = savedTemplate.blockIds.map((id, index) => {\n            const foundBlockDef = availableBlocks.find(b => b.blockId === id || b._id === id);\n            const instanceContent = savedTemplate.blocks?.[index]?.content || foundBlockDef?.content || {};\n            const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`;\n            return foundBlockDef ? { ...foundBlockDef, content: { ...instanceContent }, instanceId } : null;\n         }).filter((b) => b !== null) as Block[];\n         setEditorBlocks(populatedBlocks);\n      }\n\n      setShowSaveModal(false);\n      console.log('Template saved successfully!');\n      if (!templateId && savedTemplate._id) {\n        navigate(`/email-editor/${savedTemplate._id}`, { replace: true });\n      }\n      return savedTemplate;\n    } catch (err: any) {\n      console.error('Error saving template:', err);\n      const errorMessage = err.response?.data?.error || err.message || 'Failed to save template.';\n      setSaveError(errorMessage);\n      return null;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // --- Drop Target for Canvas --- //\n  const [{ isOverCanvas }, drop] = useDrop(() => ({\n      accept: [ItemTypes.BLOCK, ItemTypes.LIBRARY_BLOCK],\n      drop: (item: EditorDragItem | LibraryDragItem, monitor) => {\n          if (monitor.didDrop()) return;\n\n          const editorDiv = editorCanvasRef.current;\n          const clientOffset = monitor.getClientOffset();\n          if (!clientOffset || !editorDiv) return;\n\n          const hoverIndex = findDropIndex(clientOffset.y, editorDiv);\n\n          if (item.type === ItemTypes.LIBRARY_BLOCK) {\n            dropBlockFromLibrary((item as LibraryDragItem).block, hoverIndex);\n          }\n          // Reordering is handled by DraggableBlock's hover\n      },\n      collect: monitor => ({\n        isOverCanvas: monitor.isOver({ shallow: true }),\n      }),\n  }), [editorBlocks, moveBlock, dropBlockFromLibrary]); // Ensure correct dependencies\n\n  // Helper function to find the correct drop index\n  const findDropIndex = (clientY: number | undefined, container: HTMLDivElement): number => {\n      if (clientY === undefined) return editorBlocks.length;\n\n      const containerRect = container.getBoundingClientRect();\n      const offsetY = clientY - containerRect.top + container.scrollTop;\n\n      let calculatedIndex = editorBlocks.length;\n      const children = Array.from(container.children) as HTMLElement[];\n\n      for (let i = 0; i < children.length; i++) {\n          const child = children[i];\n          if (!child.classList || !child.classList.contains('draggable-block')) continue;\n\n          const childTop = child.offsetTop;\n          const childHeight = child.offsetHeight;\n          const middleY = childTop + childHeight / 2;\n\n          if (offsetY < middleY) {\n              calculatedIndex = i;\n              break;\n          }\n      }\n      return calculatedIndex;\n  };\n\n  // Cleanup cache when component unmounts\n  useEffect(() => {\n    return () => {\n      // Clear the entire cache when this editor instance is unmounted\n      templateCache.clear();\n    };\n  }, []);\n\n  // Add a cleanup function for memory management\n  const cleanupUnusedResources = useCallback(() => {\n    // Clear any unused thumbnails from memory if browser gets low on memory\n    if ('memory' in performance) {\n      const memoryInfo = (performance as any).memory;\n      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {\n        console.log('[EmailEditor] Memory pressure detected, cleaning up resources');\n        // Force a garbage collection if possible\n        clearStaleCache();\n        // You could also unload any non-visible thumbnails or other large objects\n      }\n    }\n  }, []);\n\n  // Listen for low memory events\n  useEffect(() => {\n    if ('onmemorywarning' in window) {\n      (window as any).addEventListener('memorywarning', cleanupUnusedResources);\n      return () => {\n        (window as any).removeEventListener('memorywarning', cleanupUnusedResources);\n      };\n    }\n  }, [cleanupUnusedResources]);\n\n  // --- Render Logic --- //\n  if (isLoading) {\n    return <div className=\"flex justify-center items-center h-screen text-gray-600 text-lg\">Loading Editor...</div>;\n  }\n\n  // Critical error on initial load\n  if (error && !template) {\n    return <div className=\"p-4 m-4 text-red-700 bg-red-100 border border-red-400 rounded\">Error loading editor configuration: {error}</div>;\n  }\n\n  return (\n    <div className=\"email-editor flex flex-col h-screen bg-gray-100\">\n      {/* Enhanced Toolbar */}\n      <div className=\"editor-toolbar flex items-center justify-between px-4 py-2 bg-white border-b border-gray-200 shadow-sm\">\n        <div className=\"toolbar-left flex items-center space-x-4\">\n          <h1 className=\"text-lg font-semibold text-gray-800\">\n            {template?.templateName || 'Untitled Template'}\n          </h1>\n\n          {/* Undo/Redo Controls */}\n          <div className=\"flex items-center space-x-1\">\n            <button\n              onClick={undo}\n              disabled={!canUndo}\n              className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              title=\"Undo (Ctrl+Z)\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\" />\n              </svg>\n            </button>\n            <button\n              onClick={redo}\n              disabled={!canRedo}\n              className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              title=\"Redo (Ctrl+Y)\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"toolbar-center flex items-center\">\n          {/* View Mode Toggle */}\n          <div className=\"preview-mode-toggle flex items-center bg-gray-100 rounded-lg p-1\">\n            <button\n              onClick={() => setViewMode('edit')}\n              className={`toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                viewMode === 'edit' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n              </svg>\n              Edit\n            </button>\n            <button\n              onClick={() => setViewMode('split')}\n              className={`toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                viewMode === 'split' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2\" />\n              </svg>\n              Split\n            </button>\n            <button\n              onClick={() => setViewMode('preview')}\n              className={`toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                viewMode === 'preview' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n              </svg>\n              Preview\n            </button>\n          </div>\n        </div>\n\n        <div className=\"toolbar-right flex items-center space-x-3\">\n          {/* Preview Mode Toggle */}\n          <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n            <button\n              onClick={() => setPreviewMode('desktop')}\n              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                previewMode === 'desktop' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Desktop\n            </button>\n            <button\n              onClick={() => setPreviewMode('mobile')}\n              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                previewMode === 'mobile' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Mobile\n            </button>\n          </div>\n\n          {/* Save Button */}\n          <button\n            onClick={() => {\n              setTemplate(prev => ({ ...(prev || {}), templateName: prev?.templateName || 'Untitled Template' } as Template));\n              setSaveError(null);\n              setShowSaveModal(true);\n            }}\n            disabled={isSaving}\n            className=\"save-button px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors disabled:bg-indigo-400 disabled:cursor-not-allowed\"\n          >\n            {isSaving ? 'Saving...' : 'Save Template'}\n          </button>\n        </div>\n      </div>\n\n      {/* Display non-critical error (e.g., failed suggestion) */}\n      {error && templateId &&\n          <div className=\"p-2 text-sm text-red-700 bg-red-100 border-b border-red-300 text-center\">Error: {error}</div>\n      }\n\n      <div className={`editor-content flex flex-1 overflow-hidden`}>\n        {/* Block Library Panel */}\n        <div className=\"block-library-panel w-64 bg-white border-r border-gray-200 flex flex-col overflow-hidden shrink-0\">\n          <h3 className=\"px-4 py-3 text-base font-semibold text-gray-800 border-b border-gray-200 whitespace-nowrap\">Block Library</h3>\n          <BlockLibrary\n            blocks={availableBlocks}\n            onAddBlock={(block) => dropBlockFromLibrary(block, editorBlocks.length)} // Click adds to end\n          />\n        </div>\n\n        {/* Editor Workspace / Canvas */}\n        <div ref={drop} className={`editor-workspace flex-1 flex flex-col overflow-hidden bg-gray-200 ${isOverCanvas ? 'outline-2 outline-dashed outline-indigo-500' : ''}`}>\n          <div ref={editorCanvasRef} className=\"blocks-container flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto\">\n            {editorBlocks.length === 0 ? (\n              <div className=\"empty-blocks flex items-center justify-center h-full text-gray-500 text-center p-8 border-2 border-dashed border-gray-300 rounded-lg min-h-[200px]\">\n                <p>Drag blocks from the library here<br/>or click a block in the library to add it.</p>\n              </div>\n            ) : (\n              editorBlocks.map((block, index) => (\n                <DraggableBlock\n                  key={block.instanceId} // Use unique instanceId\n                  block={block}\n                  index={index}\n                  moveBlock={moveBlock}\n                  removeBlock={removeBlock}\n                  duplicateBlock={duplicateBlock}\n                  isSelected={selectedBlockIndex === index}\n                  onClick={() => setSelectedBlockIndex(index)}\n                />\n              ))\n            )}\n          </div>\n        </div>\n\n        {/* Right Panel (Toggles Preview/Editor) */}\n        <div className=\"right-panel w-80 md:w-96 lg:w-[500px] border-l border-gray-300 flex flex-col shrink-0 bg-white shadow-lg\">\n            {(selectedBlockIndex === null || editorBlocks.length === 0) ? (\n                // Show Preview when no block is selected OR if editor is empty\n                <div className=\"preview-panel flex-1 flex flex-col overflow-hidden\">\n                    <h3 className=\"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200\">Preview</h3>\n                    <EmailPreview\n                        html={previewHtml}\n                        mode={previewMode}\n                        iframeRef={previewIframeRef}\n                    />\n                </div>\n            ) : (\n                // Show Block Editor when a block is selected\n                <div className=\"block-editor-panel flex-1 flex flex-col overflow-hidden\">\n                    <h3 className=\"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200 flex justify-between items-center\">\n                      <span>Edit: {editorBlocks[selectedBlockIndex]?.name || 'Block'}</span>\n                      <button\n                        onClick={() => setSelectedBlockIndex(null)}\n                        className=\"text-sm text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100\"\n                        title=\"Close Editor\"\n                      >\n                        ✕\n                      </button>\n                    </h3>\n                    <div className=\"flex-1 overflow-y-auto\">\n                        {editorBlocks[selectedBlockIndex] && ( // Check block exists before rendering\n                            <BlockEditor\n                                block={editorBlocks[selectedBlockIndex]}\n                                onUpdate={(content) => {\n                                    if (selectedBlockIndex !== null) {\n                                        updateBlockContent(selectedBlockIndex, content);\n                                    }\n                                }}\n                            />\n                        )}\n                    </div>\n                </div>\n            )}\n        </div>\n\n      </div>\n\n      {/* Save Modal */}\n      {showSaveModal && (\n        <SaveTemplateModal\n          initialName={template?.templateName || 'Untitled Template'}\n          onSave={handleSave}\n          onCancel={() => { setShowSaveModal(false); setSaveError(null); }}\n          isSaving={isSaving}\n          error={saveError} // Pass save error to modal\n        />\n      )}\n    </div>\n  );\n};\n\n// Draggable Block Component (Internal to EmailEditor)\ninterface DraggableBlockProps {\n  block: Block;\n  index: number;\n  moveBlock: (dragIndex: number, hoverIndex: number) => void;\n  removeBlock: (index: number) => void;\n  duplicateBlock: (index: number) => void;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nconst DraggableBlock: React.FC<DraggableBlockProps> = ({\n  block,\n  index,\n  moveBlock,\n  removeBlock,\n  isSelected,\n  onClick\n}) => {\n  const ref = useRef<HTMLDivElement>(null);\n\n  const [{ handlerId }, drop] = useDrop<EditorDragItem, void, { handlerId: string | symbol | null }>(() => ({\n    accept: ItemTypes.BLOCK,\n    hover: (item: EditorDragItem, monitor: DropTargetMonitor<EditorDragItem, void>) => {\n      if (!ref.current) return;\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) return;\n\n      const hoverBoundingRect = ref.current.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n      const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n\n      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;\n      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;\n\n      moveBlock(dragIndex, hoverIndex);\n      item.index = hoverIndex; // Mutate monitor item for performance\n    },\n    collect: (monitor) => ({\n        handlerId: monitor.getHandlerId(),\n    }),\n  }), [index, moveBlock]); // Dependencies for useDrop hover logic\n\n  const [{ isDragging }, drag] = useDrag(() => ({\n    type: ItemTypes.BLOCK,\n    item: { index, id: block.instanceId, type: ItemTypes.BLOCK }, // Return item as a function\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [index, block.instanceId]); // Dependencies for useDrag\n\n  drag(drop(ref)); // Combine drag and drop refs\n\n  return (\n    <div\n      ref={ref}\n      data-handler-id={handlerId}\n      className={`draggable-block bg-white border rounded-md mb-4 cursor-move shadow-sm ${isDragging ? 'opacity-40 border-blue-500' : 'hover:border-blue-400 hover:shadow-md'} ${isSelected ? 'border-blue-500 ring-2 ring-blue-300 ring-offset-1' : 'border-gray-200'}`} // styles/editor.css\n      onClick={onClick}\n      style={{ opacity: isDragging ? 0.4 : 1 }}\n    >\n      <div className=\"block-header flex items-center justify-between px-3 py-1.5 border-b border-gray-200 bg-gray-50 rounded-t-md text-xs\"> {/* styles/editor.css */}\n        <span className=\"block-type font-medium text-gray-700 truncate pr-2\" title={block.name}>{block.name} <span className=\"text-gray-400\">({block.category})</span></span>\n        <div className=\"block-actions flex items-center space-x-1\"> {/* styles/editor.css */}\n           {/* Duplicate Button Placeholder */}\n           <button\n            type=\"button\"\n            className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\n            onClick={(e) => {\n                e.stopPropagation();\n                console.log(\"Duplicate block:\", index); // TODO: Implement duplicate functionality\n            }}\n            title=\"Duplicate Block\"\n           >\n               {/* Heroicon: duplicate */}\n               <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={2}>\n                   <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n               </svg>\n           </button>\n           {/* Remove Button */}\n          <button\n            type=\"button\"\n            className=\"remove-block p-1 text-gray-400 hover:text-red-600 transition-colors\" // styles/editor.css\n            onClick={(e) => {\n              e.stopPropagation();\n              removeBlock(index);\n            }}\n            title=\"Remove Block\"\n          >\n            {/* Heroicon: x */}\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={2}>\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n      {/* Simplified preview inside the draggable block */}\n      <div className=\"block-content-preview p-3 text-sm text-gray-600 bg-white rounded-b-md min-h-[50px]\">\n        {(() => {\n          // Display appropriate preview based on block type\n          if (block.blockId === 'header/simple-nav') {\n            return 'Navigation Header';\n          } else if (block.blockId === 'layout/hero') {\n            return block.content?.heroHeadline || 'Hero Section';\n          } else if (block.blockId === 'content/headline') {\n            return block.content?.headline || 'Headline';\n          } else if (block.blockId === 'product/grid') {\n            return 'Product Grid: ' + [\n              block.content?.prod1_name,\n              block.content?.prod2_name,\n              block.content?.prod3_name\n            ].filter(Boolean).join(', ');\n          } else if (block.blockId === 'cta/button') {\n            return block.content?.buttonText || 'Button';\n          } else if (block.blockId === 'footer/standard') {\n            return block.content?.companyName ? `Footer: ${block.content.companyName}` : 'Standard Footer';\n          } else {\n            // Fallback to original logic\n            return block.content?.headline ||\n                   block.content?.body?.substring(0, 50) +\n                   (block.content?.body && block.content.body.length > 50 ? '...' : '') ||\n                   block.name;\n          }\n        })()}\n        {block.thumbnail && <img src={block.thumbnail} alt={`${block.name} thumbnail`} className=\"mx-auto h-12 mt-2 opacity-75 object-contain\"/>}\n      </div>\n    </div>\n  );\n};\n\nexport { EmailEditor };\nexport default EmailEditor;"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IACVC,WAAW,EACXC,SAAS,EACTC,MAAM,EACNC,QAAQ,QACH,OAAO;AAEd,OAAOC,SAAS,MAAM,cAAc;AACpC,SAEEC,OAAO,EACPC,OAAO,QACF,WAAW;AAClB,SACEC,WAAW,EACXC,SAAS,QACJ,kBAAkB;AAEzB,OAAOC,GAAG,MAAM,iBAAiB,CAAC,CAAC;AACnC;;AAUA,OAAOC,WAAW,MAAM,eAAe;AACvC;AACA,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,qBAAqB;;AAEnD;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,MAAM,CAAC,CAAC;;AAEzD;AACA,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE;AACjB,CAAC;AAaD;AACA;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAI1B,CAAC;;AAEJ;AACA,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;;AAE/B;AACA;AACA,MAAMC,gBAAgB,GAAIC,MAAe,IAAa;EACpD,OAAOA,MAAM,CAACC,GAAG,CAACC,KAAK,IAAI;IACzB,MAAM;MAAEC,UAAU;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IACrC;IACA,OAAO,GAAGA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAIH,UAAU,IAAII,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,EAAE;EACjF,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;AACd,CAAC;;AAED;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtBf,aAAa,CAACiB,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;IACpC,IAAIJ,GAAG,GAAGG,KAAK,CAACE,SAAS,GAAGlB,SAAS,EAAE;MACrCF,aAAa,CAACqB,MAAM,CAACF,GAAG,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;;AAaA;;AAEA,MAAMG,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAClC,MAAM;IAAEC;EAAW,CAAC,GAAGxC,SAAS,CAA0B,CAAC;EAC3D,MAAMyC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAoB,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAU,EAAE,CAAC;EAC7D,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAU,EAAE,CAAC;EACnE,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAgB,IAAI,CAAC;EACjF,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAuB,SAAS,CAAC;EAC/E,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAU,KAAK,CAAC;EACxD,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAU,IAAI,CAAC;EACzD,MAAM,CAACiE,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAwB,IAAI,CAAC;EACnF,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5E,QAAQ,CAAU,IAAI,CAAC;EACvE,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAU,IAAI,CAAC;EAC7D,MAAM,CAAC+E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhF,QAAQ,CAAU,KAAK,CAAC;EAC9E,MAAM,CAACiF,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAA+B,OAAO,CAAC;;EAE/E;EACA,MAAM,CAACmF,aAAa,EAAEC,gBAAgB,CAAC,GAAGpF,QAAQ,CAAgB;IAChEqF,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,CAAC,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAGC,OAAO,CAAC,MAAMN,aAAa,CAACG,YAAY,GAAG,CAAC,EAAE,CAACH,aAAa,CAACG,YAAY,CAAC,CAAC;EAC3F,MAAMI,OAAO,GAAGD,OAAO,CAAC,MAAMN,aAAa,CAACG,YAAY,GAAGH,aAAa,CAACE,OAAO,CAACM,MAAM,GAAG,CAAC,EAAE,CAACR,aAAa,CAACG,YAAY,EAAEH,aAAa,CAACE,OAAO,CAACM,MAAM,CAAC,CAAC;;EAExJ;EACA,MAAMC,gBAAgB,GAAG7F,MAAM,CAAoB,IAAI,CAAC;EACxD,MAAM8F,eAAe,GAAG9F,MAAM,CAAiB,IAAI,CAAC;;EAEpD;EACA,MAAM+F,aAAa,GAAGjG,WAAW,CAAC,CAAC2B,MAAe,EAAEuE,aAA4B,KAAK;IACnFX,gBAAgB,CAACY,IAAI,IAAI;MACvB,MAAMC,QAAqB,GAAG;QAC5BzE,MAAM,EAAEO,IAAI,CAACmE,KAAK,CAACnE,IAAI,CAACC,SAAS,CAACR,MAAM,CAAC,CAAC;QAAE;QAC5C6B,kBAAkB,EAAE0C,aAAa;QACjCvD,SAAS,EAAEJ,IAAI,CAACD,GAAG,CAAC;MACtB,CAAC;;MAED;MACA,MAAMgE,UAAU,GAAGH,IAAI,CAACX,OAAO,CAACe,KAAK,CAAC,CAAC,EAAEJ,IAAI,CAACV,YAAY,GAAG,CAAC,CAAC;MAC/Da,UAAU,CAACE,IAAI,CAACJ,QAAQ,CAAC;;MAEzB;MACA,IAAIE,UAAU,CAACR,MAAM,GAAGK,IAAI,CAACT,cAAc,EAAE;QAC3CY,UAAU,CAACG,KAAK,CAAC,CAAC;MACpB;MAEA,OAAO;QACL,GAAGN,IAAI;QACPX,OAAO,EAAEc,UAAU;QACnBb,YAAY,EAAEa,UAAU,CAACR,MAAM,GAAG;MACpC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,IAAI,GAAG1G,WAAW,CAAC,MAAM;IAC7B,IAAI,CAAC2F,OAAO,EAAE;IAEdJ,gBAAgB,CAACY,IAAI,IAAI;MACvB,MAAMQ,QAAQ,GAAGR,IAAI,CAACV,YAAY,GAAG,CAAC;MACtC,MAAMmB,KAAK,GAAGT,IAAI,CAACX,OAAO,CAACmB,QAAQ,CAAC;MAEpC,IAAIC,KAAK,EAAE;QACTvD,eAAe,CAACuD,KAAK,CAACjF,MAAM,CAAC;QAC7B8B,qBAAqB,CAACmD,KAAK,CAACpD,kBAAkB,CAAC;MACjD;MAEA,OAAO;QACL,GAAG2C,IAAI;QACPV,YAAY,EAAEkB;MAChB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChB,OAAO,CAAC,CAAC;EAEb,MAAMkB,IAAI,GAAG7G,WAAW,CAAC,MAAM;IAC7B,IAAI,CAAC6F,OAAO,EAAE;IAEdN,gBAAgB,CAACY,IAAI,IAAI;MACvB,MAAMQ,QAAQ,GAAGR,IAAI,CAACV,YAAY,GAAG,CAAC;MACtC,MAAMmB,KAAK,GAAGT,IAAI,CAACX,OAAO,CAACmB,QAAQ,CAAC;MAEpC,IAAIC,KAAK,EAAE;QACTvD,eAAe,CAACuD,KAAK,CAACjF,MAAM,CAAC;QAC7B8B,qBAAqB,CAACmD,KAAK,CAACpD,kBAAkB,CAAC;MACjD;MAEA,OAAO;QACL,GAAG2C,IAAI;QACPV,YAAY,EAAEkB;MAChB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACd,OAAO,CAAC,CAAC;;EAEb;EACA5F,SAAS,CAAC,MAAM;IACd,MAAM6G,aAAa,GAAIC,CAAgB,IAAK;MAC1C,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACrE,GAAG,KAAK,GAAG,IAAI,CAACqE,CAAC,CAACG,QAAQ,EAAE;QAC5DH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBT,IAAI,CAAC,CAAC;MACR,CAAC,MAAM,IAAI,CAACK,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,MAAMF,CAAC,CAACrE,GAAG,KAAK,GAAG,IAAKqE,CAAC,CAACrE,GAAG,KAAK,GAAG,IAAIqE,CAAC,CAACG,QAAS,CAAC,EAAE;QACvFH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBN,IAAI,CAAC,CAAC;MACR;IACF,CAAC;IAEDO,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;IACjD,OAAO,MAAMM,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;EACnE,CAAC,EAAE,CAACJ,IAAI,EAAEG,IAAI,CAAC,CAAC;;EAEhB;EACA5G,SAAS,CAAC,MAAM;IACd,IAAIsH,SAAS,GAAG,IAAI;IACpB,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BrD,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACF;QACAoD,OAAO,CAACC,GAAG,CAAC,wBAAwB1G,OAAO,SAAS,CAAC;QACrD;QACA,MAAM2G,aAAa,GAAGlH,GAAG,CAACmH,GAAG,CAAoB,SAAS,CAAC;;QAE3D;QACAH,OAAO,CAACC,GAAG,CAAC,kCAAkC1G,OAAO,mBAAmB,CAAC;QACxE;QACD,MAAM6G,YAAY,GAAGpH,GAAG,CAACmH,GAAG,CAAkC,mBAAmB,CAAC;;QAElF;QACA,MAAME,eAAe,GAAG9E;QACtB;QAAA,EACEvC,GAAG,CAACmH,GAAG,CAAyB,cAAc5E,UAAU,EAAE,CAAC,GAC3D+E,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;QAEzB,MAAM,CAACC,cAAc,EAAEC,aAAa,EAAEC,gBAAgB,CAAC,GAAG,MAAMJ,OAAO,CAACK,GAAG,CAAC,CAC1ET,aAAa,EACbE,YAAY,EACZC,eAAe,CAChB,CAAC;;QAEF;QACA,MAAMO,sBAAsB,GAAGJ,cAAc,CAACK,IAAI,CAACA,IAAI,IAAI,EAAE;QAC7Db,OAAO,CAACC,GAAG,CAAC,WAAWW,sBAAsB,CAACvC,MAAM,oBAAoB,CAAC;QACzEvC,kBAAkB,CAAC8E,sBAAsB,CAAC;;QAE1C;QACA,MAAM7D,eAAe,GAAG0D,aAAa,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC;QACxD;;QAEA,IAAIJ,gBAAgB,IAAIA,gBAAgB,CAACG,IAAI,CAACpF,QAAQ,EAAE;UACtD,MAAMsF,YAAY,GAAGL,gBAAgB,CAACG,IAAI,CAACpF,QAAQ,CAAC,CAAC;UACrDuE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAExF,IAAI,CAACC,SAAS,CAACqG,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE5F;UACArF,WAAW,CAAC;YACR,GAAGqF,YAAY;YACfC,WAAW,EAAED,YAAY,CAACC,WAAW,KAAIjE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiE,WAAW;YACrEC,YAAY,EAAEF,YAAY,CAACE,YAAY,KAAIlE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkE,YAAY;UAC5E,CAAC,CAAC;;UAEF;UACAjB,OAAO,CAACC,GAAG,CAAC,oEAAoE,EAAEW,sBAAsB,CAACvC,MAAM,CAAC;UAChH2B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEc,YAAY,CAACG,QAAQ,CAAC;UACtElB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEc,YAAY,CAAC7G,MAAM,CAAC;UAEvE,IAAI6G,YAAY,CAACG,QAAQ,IAAIH,YAAY,CAACG,QAAQ,CAAC7C,MAAM,GAAG,CAAC,IAAIuC,sBAAsB,CAACvC,MAAM,GAAG,CAAC,EAAE;YAClG2B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;YACxE,MAAMkB,eAAe,GAAGJ,YAAY,CAACG,QAAQ,CAAC/G,GAAG,CAAC,CAACiH,EAAU,EAAEC,KAAa,KAAK;cAAA,IAAAC,oBAAA;cAC/EtB,OAAO,CAACC,GAAG,CAAC,kCAAkCmB,EAAE,cAAcC,KAAK,EAAE,CAAC;cACtE;cACA,MAAME,aAAa,GAAGX,sBAAsB,CAACY,IAAI,CAAEC,CAAQ,IAAKA,CAAC,CAAClH,OAAO,KAAK6G,EAAE,IAAIK,CAAC,CAACjH,GAAG,KAAK4G,EAAE,CAAC,CAAC,CAAC;cACnGpB,OAAO,CAACC,GAAG,CAAC,0CAA0CmB,EAAE,GAAG,EAAEG,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC;cAE1F,IAAI,CAACA,aAAa,EAAE;gBAClBvB,OAAO,CAAC0B,IAAI,CAAC,6EAA6EN,EAAE,aAAa,CAAC,CAAC,CAAC;gBAC5G,OAAO,IAAI;cACb;;cAEA;cACA,MAAMO,iBAAiB,IAAAL,oBAAA,GAAGP,YAAY,CAAC7G,MAAM,cAAAoH,oBAAA,uBAAnBA,oBAAA,CAAsBD,KAAK,CAAC,CAAC,CAAC;cACxD,MAAMO,eAAe,GAAG,CAAAD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAErH,OAAO,KAAIiH,aAAa,CAACjH,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;cACnF0F,OAAO,CAACC,GAAG,CAAC,0CAA0CmB,EAAE,GAAG,EAAEQ,eAAe,CAAC;cAE7E,MAAMvH,UAAU,GAAG,GAAG+G,EAAE,IAAIC,KAAK,IAAIvG,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIgH,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;cAE9F;cACA,MAAMC,aAAa,GAAG;gBAClB,GAAGV,aAAa;gBAAE;gBAClBjH,OAAO,EAAE;kBAAE,GAAGsH;gBAAgB,CAAC;gBAAE;gBACjCvH,UAAU,CAAC;cACf,CAAC;cACD2F,OAAO,CAACC,GAAG,CAAC,wDAAwDmB,EAAE,GAAG,EAAEa,aAAa,CAAC;cACzF,OAAOA,aAAa;YACtB,CAAC,CAAC,CAACC,MAAM,CAACT,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC;YAE1BzB,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEkB,eAAe,CAAC;YAC1FvF,eAAe,CAACuF,eAA0B,CAAC;UAC7C,CAAC,MAAM;YAAA,IAAAgB,qBAAA;YACLnC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;cAClEmC,WAAW,EAAE,CAAC,CAACrB,YAAY,CAACG,QAAQ;cACpCmB,cAAc,GAAAF,qBAAA,GAAEpB,YAAY,CAACG,QAAQ,cAAAiB,qBAAA,uBAArBA,qBAAA,CAAuB9D,MAAM;cAC7CiE,gBAAgB,EAAE,CAAC,CAAC1B,sBAAsB;cAC1C2B,mBAAmB,EAAE3B,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEvC;YACjD,CAAC,CAAC;YACFzC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;UACvB;QACF,CAAC,MAAM;UACL;UACAoE,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChEvE,WAAW,CAAC;YACVuB,YAAY,EAAE,mBAAmB;YACjCuF,OAAO,EAAE,mBAAmB;YAC5BxB,WAAW,EAAEjE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiE,WAAW;YACzCC,YAAY,EAAElE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkE,YAAY;YAC3C;YACAwB,MAAM,EAAE1F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0F,MAAM,CAAC;UAClC,CAAC,CAAC;UACF7G,eAAe,CAAC,EAAE,CAAC;QACrB;MACF,CAAC,CAAC,OAAO8G,GAAQ,EAAE;QACjB1C,OAAO,CAACrD,KAAK,CAAC,4BAA4B,EAAE+F,GAAG,CAAC;QAChD,IAAIC,QAAQ,GAAG,6BAA6B,CAAC,CAAC;;QAE9C,IAAID,GAAG,CAACE,QAAQ,EAAE;UAAA,IAAAC,WAAA,EAAAC,eAAA;UAChB;UACA,IAAI,CAAAD,WAAA,GAAAH,GAAG,CAACK,MAAM,cAAAF,WAAA,gBAAAC,eAAA,GAAVD,WAAA,CAAYG,GAAG,cAAAF,eAAA,eAAfA,eAAA,CAAiBG,QAAQ,CAAC,cAAc1H,UAAU,EAAE,CAAC,IAAImH,GAAG,CAACE,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;YACxFP,QAAQ,GAAG,oBAAoBpH,UAAU,uCAAuC;YAChF;YACA;YACAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjBE,eAAe,CAAC,EAAE,CAAC;UACrB,CAAC,MAAM;YAAA,IAAAuH,kBAAA,EAAAC,mBAAA;YACL;YACAT,QAAQ,GAAG,EAAAQ,kBAAA,GAAAT,GAAG,CAACE,QAAQ,CAAC/B,IAAI,cAAAsC,kBAAA,uBAAjBA,kBAAA,CAAmBxG,KAAK,OAAAyG,mBAAA,GAAIV,GAAG,CAACE,QAAQ,CAAC/B,IAAI,cAAAuC,mBAAA,uBAAjBA,mBAAA,CAAmBC,OAAO,KAAIX,GAAG,CAACW,OAAO,IAAIV,QAAQ;UAC9F;QACF,CAAC,MAAM;UACL;UACAA,QAAQ,GAAGD,GAAG,CAACW,OAAO,IAAIV,QAAQ;QACpC;QAEA/F,QAAQ,CAAC+F,QAAQ,CAAC;QAClB;QACA,IAAI9G,eAAe,CAACwC,MAAM,KAAK,CAAC,EAAE;UAC9B2B,OAAO,CAAC0B,IAAI,CAAC,kFAAkF,CAAC;UAChG5F,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC,SAAS;QACRY,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDqD,SAAS,CAAC,CAAC;IAEX,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;IACH;EACA,CAAC,EAAE,CAACvE,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA,MAAM+H,sBAAsB,GAAG/K,WAAW,CAAEgL,SAAkB,IAAa;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IACzE;IACA,IAAI,CAACF,SAAS,CAAClF,MAAM,EAAE,OAAO,EAAE;;IAEhC;IACA,MAAMqF,QAAQ,GAAGzJ,gBAAgB,CAACsJ,SAAS,CAAC;;IAE5C;IACA,IAAIzJ,aAAa,CAAC6J,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC/B,MAAME,MAAM,GAAG9J,aAAa,CAACqG,GAAG,CAACuD,QAAQ,CAAC;MAC1C,IAAIE,MAAM,IAAK9I,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG+I,MAAM,CAAC1I,SAAS,GAAIlB,SAAS,EAAE;QACzDgG,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO2D,MAAM,CAACC,IAAI;MACpB;IACF;;IAEA;IACAjJ,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAMkJ,SAAS,GAAG,CAAC;IACnB,IAAIC,eAAe,GAAG,EAAE;IAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAAClF,MAAM,EAAE2F,CAAC,IAAIF,SAAS,EAAE;MACpD,MAAMG,KAAK,GAAGV,SAAS,CAACzE,KAAK,CAACkF,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC;MAC/C,MAAMI,YAAY,GAAGD,KAAK,CAAC9J,GAAG,CAACC,KAAK,IAAI;QACtC,IAAI+J,SAAS,GAAG/J,KAAK,CAACyJ,IAAI,IAAI,EAAE;QAChC,MAAMvJ,OAAO,GAAGF,KAAK,CAACE,OAAO,IAAI,CAAC,CAAC;;QAEnC;QACA8J,MAAM,CAACC,IAAI,CAAC/J,OAAO,CAAC,CAACS,OAAO,CAACE,GAAG,IAAI;UAClC,MAAMqJ,KAAK,GAAGhK,OAAO,CAACW,GAAG,CAAC;UAC1B,MAAMsJ,WAAW,GAAG,KAAKtJ,GAAG,IAAI,CAAC,CAAC;;UAElC;UACA,IAAIqJ,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;YACzC;YACA,IAAIG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;cACxB;cACA,IAAIrJ,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,UAAU,EAAE;gBAC7C,MAAM0J,SAAS,GAAGL,KAAK,CAACnK,GAAG,CAAEyK,IAAS,IACpC,yBAAyBA,IAAI,CAAC5B,GAAG,IAAI,GAAG,qBAAqB4B,IAAI,CAACC,IAAI,IAAI,MAAM,mBAClF,CAAC,CAAClK,IAAI,CAAC,IAAI,CAAC;gBACZwJ,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,kBAAkB,EAAEH,SAAS,CAAC;cAC9D,CAAC,MAAM,IAAI1J,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,aAAa,EAAE;gBAC1D,MAAM8J,SAAS,GAAGT,KAAK,CAACnK,GAAG,CAAE6K,IAAS;kBAAA,IAAAC,cAAA;kBAAA,OACpC,4BAA4B,EAAAA,cAAA,GAAAD,IAAI,CAACE,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,KAAI,OAAO,WAAWH,IAAI,CAAChC,GAAG,IAAI,GAAG,wBAAwB;gBAAA,CACvH,CAAC,CAACrI,IAAI,CAAC,IAAI,CAAC;gBACZwJ,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,qBAAqB,EAAEC,SAAS,CAAC;cACjE;YACF,CAAC,MAAM;cACL;cACA,MAAMK,WAAW,GAAGC,MAAM,CAACf,KAAK,CAAC;cACjCH,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACf,WAAW,EAAEa,WAAW,CAAC;;cAE1D;cACA,IAAIjB,SAAS,CAAClB,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC5B;gBACA,MAAMsC,oBAAoB,GAAG,KAAKtK,GAAG,CAAC6J,OAAO,CAAC,WAAW,EAAEU,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI;gBACvF,MAAMC,oBAAoB,GAAG,KAAKzK,GAAG,CAAC6J,OAAO,CAAC,QAAQ,EAAEa,MAAM,IAAI,IAAIA,MAAM,CAACR,WAAW,CAAC,CAAC,EAAE,CAAC,IAAI;gBAEjGhB,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACC,oBAAoB,EAAEH,WAAW,CAAC;gBACnEjB,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACI,oBAAoB,EAAEN,WAAW,CAAC;cACrE;YACF;UACF;QACF,CAAC,CAAC;;QAEF;QACA,IAAIjB,SAAS,CAAClB,QAAQ,CAAC,IAAI,CAAC,EAAE;UAC5BkB,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QACvD;QAEA,OAAOX,SAAS;MAClB,CAAC,CAAC,CAACxJ,IAAI,CAAC,IAAI,CAAC;MAEboJ,eAAe,IAAIG,YAAY;IACjC;;IAEA;IACA,MAAMlD,WAAW,IAAAwC,qBAAA,GAAG/H,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuF,WAAW,cAAAwC,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;IAC/C,MAAMvC,YAAY,IAAAwC,qBAAA,GAAGhI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwF,YAAY,cAAAwC,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;IACjD,MAAMmC,YAAY,GAAG5E,WAAW,CAAC6E,OAAO,IAAI,SAAS;IACrD,MAAMC,eAAe,GAAG9E,WAAW,CAAC+E,UAAU,IAAI,SAAS;IAC3D,MAAMC,SAAS,GAAGhF,WAAW,CAACiF,IAAI,IAAI,SAAS;IAC/C,MAAMC,WAAW,GAAGjF,YAAY,CAACkF,OAAO,IAAI,mBAAmB;IAC/D,MAAMC,QAAQ,GAAGnF,YAAY,CAACoF,IAAI,IAAI,mBAAmB;;IAEzD;IACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA,gBAAgB,CAAA7K,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+G,OAAO,KAAI,gBAAgB;AACrD;AACA;AACA,2CAA2C4D,QAAQ;AACnD,+EAA+EJ,SAAS;AACxF;AACA;AACA,qCAAqCJ,YAAY;AACjD;AACA;AACA;AACA;AACA,mBAAmBA,YAAY;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+BE,eAAe;AAC9C,MAAM/B,eAAe;AACrB;AACA,QAAQ;;IAEJ;IACAjK,aAAa,CAACyM,GAAG,CAAC7C,QAAQ,EAAE;MAC1BG,IAAI,EAAEyC,QAAQ;MACdE,IAAI,EAAE,EAAE;MAAE;MACVtL,SAAS,EAAEJ,IAAI,CAACD,GAAG,CAAC;IACtB,CAAC,CAAC;IAEF,OAAOyL,QAAQ;EACjB,CAAC,EAAE,CAAC7K,QAAQ,CAAC,CAAC;EAEdjD,SAAS,CAAC,MAAM;IACd,MAAMiO,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI,CAAC9K,YAAY,IAAIA,YAAY,CAAC0C,MAAM,KAAK,CAAC,EAAE;QAC9CnC,cAAc,CAAC,oLAAoL,CAAC;QACpM;MACF;MAEA,IAAI;QACF;QACA,MAAMwH,QAAQ,GAAGzJ,gBAAgB,CAAC0B,YAAY,CAAC;;QAE/C;QACA,IAAI7B,aAAa,CAAC6J,GAAG,CAACD,QAAQ,CAAC,EAAE;UAC/B,MAAME,MAAM,GAAG9J,aAAa,CAACqG,GAAG,CAACuD,QAAQ,CAAC;UAC1C,IAAIE,MAAM,IAAIA,MAAM,CAAC4C,IAAI,IAAK1L,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG+I,MAAM,CAAC1I,SAAS,GAAIlB,SAAS,EAAE;YACxEgG,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtD/D,cAAc,CAAC0H,MAAM,CAAC4C,IAAI,CAAC;YAC3B;UACF;QACF;;QAEA;QACA,MAAME,UAAU,GAAGpD,sBAAsB,CAAC3H,YAAY,CAAC;QACvDqE,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;QAElE;QACA,IAAIN,MAAM,CAACgH,MAAM,EAAE;UACjB;UACA;UACA,MAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;YACrC;YACA,IAAI;cACF,MAAM;gBAAEL,IAAI;gBAAEM;cAAO,CAAC,GAAGnO,SAAS,CAAC+N,UAAU,EAAE;gBAAEK,eAAe,EAAE;cAAO,CAAC,CAAC;cAC3E7K,cAAc,CAACsK,IAAI,CAAC;cACpB;cACA,MAAM9C,QAAQ,GAAGzJ,gBAAgB,CAAC0B,YAAY,CAAC;cAC/C,IAAI7B,aAAa,CAAC6J,GAAG,CAACD,QAAQ,CAAC,EAAE;gBAC/B,MAAM1I,KAAK,GAAGlB,aAAa,CAACqG,GAAG,CAACuD,QAAQ,CAAC;gBACzC,IAAI1I,KAAK,EAAE;kBACTA,KAAK,CAACwL,IAAI,GAAGA,IAAI;kBACjBxL,KAAK,CAACE,SAAS,GAAGJ,IAAI,CAACD,GAAG,CAAC,CAAC;gBAC9B;cACF;YACF,CAAC,CAAC,OAAOmM,WAAgB,EAAE;cACzBhH,OAAO,CAACrD,KAAK,CAAC,qDAAqD,EAAEqK,WAAW,CAAC;cACjF9K,cAAc,CAAC,0DAA0D,CAAA8K,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE3D,OAAO,KAAI,eAAe,QAAQ,CAAC;YAC3H;UACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;UAEV;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,MAAM;UACL;UACA,MAAM;YAAEmD,IAAI;YAAEM;UAAO,CAAC,GAAGnO,SAAS,CAAC+N,UAAU,EAAE;YAAEK,eAAe,EAAE;UAAO,CAAC,CAAC;UAC3E,IAAID,MAAM,IAAIA,MAAM,CAACzI,MAAM,GAAG,CAAC,EAAE;YAC/B2B,OAAO,CAAC0B,IAAI,CAAC,wCAAwC,EAAEoF,MAAM,CAACzI,MAAM,EAAE,QAAQ,CAAC;UACjF;UACAnC,cAAc,CAACsK,IAAI,CAAC;;UAEpB;UACA,IAAI1M,aAAa,CAAC6J,GAAG,CAACD,QAAQ,CAAC,EAAE;YAC/B,MAAM1I,KAAK,GAAGlB,aAAa,CAACqG,GAAG,CAACuD,QAAQ,CAAC;YACzC,IAAI1I,KAAK,EAAE;cACTA,KAAK,CAACwL,IAAI,GAAGA,IAAI;cACjBxL,KAAK,CAACE,SAAS,GAAGJ,IAAI,CAACD,GAAG,CAAC,CAAC;YAC9B;UACF;QACF;MACF,CAAC,CAAC,OAAO6H,GAAQ,EAAE;QACjB1C,OAAO,CAACrD,KAAK,CAAC,iDAAiD,EAAE+F,GAAG,CAAC;QACrExG,cAAc,CAAC,0DAA0D,CAAAwG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEW,OAAO,KAAI,eAAe,QAAQ,CAAC;MACnH;IACF,CAAC;;IAED;IACA,MAAM4D,eAAe,GAAGJ,UAAU,CAACJ,eAAe,EAAE,GAAG,CAAC;IACxD,OAAO,MAAMS,YAAY,CAACD,eAAe,CAAC;EAC5C,CAAC,EAAE,CAACtL,YAAY,EAAE2H,sBAAsB,CAAC,CAAC;;EAE1C;EACA,MAAM6D,SAAS,GAAG5O,WAAW,CAAC,CAAC6O,SAAiB,EAAEC,UAAkB,KAAK;IACrEzL,eAAe,CAAC0L,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjC,MAAM,CAACE,YAAY,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACL,SAAS,EAAE,CAAC,CAAC;MACrDG,SAAS,CAACE,MAAM,CAACJ,UAAU,EAAE,CAAC,EAAEG,YAAY,CAAC;;MAE7C;MACAhJ,aAAa,CAAC+I,SAAS,EAAExL,kBAAkB,KAAKqL,SAAS,GAAGC,UAAU,GAAGtL,kBAAkB,CAAC;MAE5F,OAAOwL,SAAS;IACpB,CAAC,CAAC;;IAEF;IACA,IAAIxL,kBAAkB,KAAKqL,SAAS,EAAE;MAClCpL,qBAAqB,CAACqL,UAAU,CAAC;IACrC,CAAC,MAAM,IAAItL,kBAAkB,KAAK,IAAI,EAAE;MACpC,IAAIqL,SAAS,GAAGC,UAAU,IAAItL,kBAAkB,GAAGqL,SAAS,IAAIrL,kBAAkB,IAAIsL,UAAU,EAAE;QAC9FrL,qBAAqB,CAAC0L,CAAC,IAAKA,CAAC,KAAK,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAK,CAAC;MAC3D,CAAC,MAAM,IAAIN,SAAS,GAAGC,UAAU,IAAItL,kBAAkB,IAAIsL,UAAU,IAAItL,kBAAkB,GAAGqL,SAAS,EAAE;QACrGpL,qBAAqB,CAAC0L,CAAC,IAAKA,CAAC,KAAK,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAK,CAAC;MAC3D;IACJ;EACJ,CAAC,EAAE,CAAC3L,kBAAkB,EAAEyC,aAAa,CAAC,CAAC;EAEvC,MAAMmJ,oBAAoB,GAAGpP,WAAW,CAAC,CAAC6B,KAAU,EAAEiH,KAAa,KAAK;IACpE;IACA,MAAMuG,sBAAsB,GAAG;MAC3B,GAAGnN,IAAI,CAACmE,KAAK,CAACnE,IAAI,CAACC,SAAS,CAACN,KAAK,CAAC,CAAC;MACpCG,OAAO,EAAEH,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAI,SAASM,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;MAAE;MAC9DP,OAAO,EAAEF,KAAK,CAACE,OAAO,GAAGG,IAAI,CAACmE,KAAK,CAACnE,IAAI,CAACC,SAAS,CAACN,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACvED,UAAU,EAAE,GAAGD,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAI,KAAK,IAAIM,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIgH,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;IAC/G,CAAC;IAEDpG,eAAe,CAAC0L,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjCC,SAAS,CAACE,MAAM,CAACpG,KAAK,EAAE,CAAC,EAAEuG,sBAA+B,CAAC;;MAE3D;MACApJ,aAAa,CAAC+I,SAAS,EAAElG,KAAK,CAAC;MAE/B,OAAOkG,SAAS;IACpB,CAAC,CAAC;IACFvL,qBAAqB,CAACqF,KAAK,CAAC,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC7C,aAAa,CAAC,CAAC;EAEnB,MAAMqJ,WAAW,GAAGtP,WAAW,CAAE8I,KAAa,IAAK;IAC/CzF,eAAe,CAAC0L,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAGD,UAAU,CAACpF,MAAM,CAAC,CAAC4F,CAAC,EAAE9D,CAAC,KAAKA,CAAC,KAAK3C,KAAK,CAAC;;MAE1D;MACA7C,aAAa,CAAC+I,SAAS,EAAExL,kBAAkB,KAAKsF,KAAK,GAAG,IAAI,GACzDtF,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,GAAGsF,KAAK,GAAGtF,kBAAkB,GAAG,CAAC,GAAGA,kBAAmB,CAAC;MAE5G,OAAOwL,SAAS;IACpB,CAAC,CAAC;IAEF,IAAIxL,kBAAkB,KAAKsF,KAAK,EAAE;MAC9BrF,qBAAqB,CAAC,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAID,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,GAAGsF,KAAK,EAAE;MAClErF,qBAAqB,CAAC+L,SAAS,IAAKA,SAAS,KAAK,IAAI,GAAGA,SAAS,GAAG,CAAC,GAAG,IAAK,CAAC;IACnF;EACJ,CAAC,EAAE,CAAChM,kBAAkB,EAAEyC,aAAa,CAAC,CAAC;EAEvC,MAAMwJ,cAAc,GAAGzP,WAAW,CAAE8I,KAAa,IAAK;IAClDzF,eAAe,CAAC0L,UAAU,IAAI;MAC1B,MAAMW,gBAAgB,GAAGX,UAAU,CAACjG,KAAK,CAAC;MAC1C,MAAM6G,eAAe,GAAG;QACpB,GAAGzN,IAAI,CAACmE,KAAK,CAACnE,IAAI,CAACC,SAAS,CAACuN,gBAAgB,CAAC,CAAC;QAC/C5N,UAAU,EAAE,GAAG4N,gBAAgB,CAAC1N,OAAO,IAAI0N,gBAAgB,CAACzN,GAAG,IAAI,KAAK,IAAIM,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIgH,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MACrI,CAAC;MAED,MAAMuF,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjCC,SAAS,CAACE,MAAM,CAACpG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE6G,eAAe,CAAC;;MAE/C;MACA1J,aAAa,CAAC+I,SAAS,EAAElG,KAAK,GAAG,CAAC,CAAC;MAEnC,OAAOkG,SAAS;IACpB,CAAC,CAAC;IACFvL,qBAAqB,CAACqF,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC7C,aAAa,CAAC,CAAC;EAEnB,MAAM2J,kBAAkB,GAAG5P,WAAW,CAAC,CAAC8I,KAAa,EAAE+G,cAAqC,KAAK;IAC7FxM,eAAe,CAAC0L,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAGD,UAAU,CAACnN,GAAG,CAAC,CAACC,KAAK,EAAE4J,CAAC,KACtCA,CAAC,KAAK3C,KAAK,GACL;QAAE,GAAGjH,KAAK;QAAEE,OAAO,EAAE;UAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,CAAC,CAAC,CAAC;UAAE,GAAG8N;QAAe;MAAE,CAAC,GACtEhO,KACV,CAAC;;MAED;MACA,MAAMiO,aAAa,GAAGxB,UAAU,CAAC,MAAM;QACnCrI,aAAa,CAAC+I,SAAS,EAAExL,kBAAkB,CAAC;MAChD,CAAC,EAAE,IAAI,CAAC;MAER,OAAOwL,SAAS;IACpB,CAAC,CAAC;EACN,CAAC,EAAE,CAAC/I,aAAa,EAAEzC,kBAAkB,CAAC,CAAC;;EAEvC;EACA,MAAMuM,UAAU,GAAG,MAAAA,CAAOC,kBAA0B,EAAEC,cAAuB,GAAG,KAAK,KAAK;IACxFlM,WAAW,CAAC,IAAI,CAAC;IACjBQ,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAM+G,IAAI,GAAGP,sBAAsB,CAAC3H,YAAY,CAAC;MACjD,MAAM;QAAE6K,IAAI;QAAEM,MAAM,EAAE2B;MAAiB,CAAC,GAAG9P,SAAS,CAACkL,IAAI,CAAC;MAC1D,IAAI4E,gBAAgB,IAAIA,gBAAgB,CAACpK,MAAM,GAAG,CAAC,EAAE;QACnD2B,OAAO,CAAC0B,IAAI,CAAC,8CAA8C,EAAE+G,gBAAgB,CAAC;QAC9E;MACF;MAEA,MAAMvH,QAAQ,GAAGvF,YAAY,CAC1BxB,GAAG,CAACsH,CAAC,IAAI4D,MAAM,CAAC5D,CAAC,CAAClH,OAAO,IAAIkH,CAAC,CAACjH,GAAG,CAAC,CAAC,CAAC;MAAA,CACrC0H,MAAM,CAACwG,OAAO,CAAC,CAAC,CAAC;;MAEpB,MAAMC,OAAwE,GAAG;QAC/EpN,UAAU,EAAEE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjB,GAAG;QACzByC,YAAY,EAAEsL,kBAAkB;QAChC1E,IAAI;QACJ2C,IAAI;QACJtF,QAAQ;QACRsB,OAAO,EAAE,CAAA/G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+G,OAAO,KAAI,mBAAmB;QACjDoG,IAAI,EAAE,CAAAnN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmN,IAAI,KAAI,EAAE;QAC1BC,QAAQ,EAAEL,cAAc;QACxBM,WAAW,EAAE,CAAArN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqN,WAAW,KAAI,EAAE;QACxCC,QAAQ,EAAE,CAAAtN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsN,QAAQ,KAAI,EAAE;QAClCC,aAAa,EAAE,CAAAvN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuN,aAAa,KAAI;MAC5C,CAAC;;MAED;MACA;MACA;MACA;MACA;;MAEA,MAAMpG,QAAQ,GAAG,MAAM5J,GAAG,CAACiQ,IAAI,CAAuB,iBAAiB,EAAEN,OAAO,CAAC;MACjF,MAAMO,aAAa,GAAGtG,QAAQ,CAAC/B,IAAI,CAACpF,QAAQ;MAC5CC,WAAW,CAACwN,aAAa,CAAC;;MAE1B;MACA;MACA,IAAIA,aAAa,CAAChI,QAAQ,IAAIrF,eAAe,CAACwC,MAAM,GAAG,CAAC,EAAE;QACvD,MAAM8C,eAAe,GAAG+H,aAAa,CAAChI,QAAQ,CAAC/G,GAAG,CAAC,CAACiH,EAAE,EAAEC,KAAK,KAAK;UAAA,IAAA8H,qBAAA,EAAAC,sBAAA;UAC/D,MAAM7H,aAAa,GAAG1F,eAAe,CAAC2F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClH,OAAO,KAAK6G,EAAE,IAAIK,CAAC,CAACjH,GAAG,KAAK4G,EAAE,CAAC;UACjF,MAAMQ,eAAe,GAAG,EAAAuH,qBAAA,GAAAD,aAAa,CAAChP,MAAM,cAAAiP,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAuB9H,KAAK,CAAC,cAAA+H,sBAAA,uBAA7BA,sBAAA,CAA+B9O,OAAO,MAAIiH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEjH,OAAO,KAAI,CAAC,CAAC;UAC9F,MAAMD,UAAU,GAAG,GAAG+G,EAAE,IAAIC,KAAK,IAAIvG,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIgH,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE;UAC5F,OAAOT,aAAa,GAAG;YAAE,GAAGA,aAAa;YAAEjH,OAAO,EAAE;cAAE,GAAGsH;YAAgB,CAAC;YAAEvH;UAAW,CAAC,GAAG,IAAI;QAClG,CAAC,CAAC,CAAC6H,MAAM,CAAET,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAY;QACvC7F,eAAe,CAACuF,eAAe,CAAC;MACnC;MAEA3E,gBAAgB,CAAC,KAAK,CAAC;MACvBwD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAAC1E,UAAU,IAAI2N,aAAa,CAAC1O,GAAG,EAAE;QACpCgB,QAAQ,CAAC,iBAAiB0N,aAAa,CAAC1O,GAAG,EAAE,EAAE;UAAEsK,OAAO,EAAE;QAAK,CAAC,CAAC;MACnE;MACA,OAAOoE,aAAa;IACtB,CAAC,CAAC,OAAOxG,GAAQ,EAAE;MAAA,IAAA2G,aAAA,EAAAC,mBAAA;MACjBtJ,OAAO,CAACrD,KAAK,CAAC,wBAAwB,EAAE+F,GAAG,CAAC;MAC5C,MAAM6G,YAAY,GAAG,EAAAF,aAAA,GAAA3G,GAAG,CAACE,QAAQ,cAAAyG,aAAA,wBAAAC,mBAAA,GAAZD,aAAA,CAAcxI,IAAI,cAAAyI,mBAAA,uBAAlBA,mBAAA,CAAoB3M,KAAK,KAAI+F,GAAG,CAACW,OAAO,IAAI,0BAA0B;MAC3FvG,YAAY,CAACyM,YAAY,CAAC;MAC1B,OAAO,IAAI;IACb,CAAC,SAAS;MACRjN,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM,CAAC;IAAEkN;EAAa,CAAC,EAAEC,IAAI,CAAC,GAAG5Q,OAAO,CAAC,OAAO;IAC5C6Q,MAAM,EAAE,CAAC/P,SAAS,CAACC,KAAK,EAAED,SAAS,CAACE,aAAa,CAAC;IAClD4P,IAAI,EAAEA,CAACE,IAAsC,EAAEC,OAAO,KAAK;MACvD,IAAIA,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE;MAEvB,MAAMC,SAAS,GAAGvL,eAAe,CAACwL,OAAO;MACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACK,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACD,YAAY,IAAI,CAACF,SAAS,EAAE;MAEjC,MAAMzC,UAAU,GAAG6C,aAAa,CAACF,YAAY,CAACG,CAAC,EAAEL,SAAS,CAAC;MAE3D,IAAIH,IAAI,CAACS,IAAI,KAAKzQ,SAAS,CAACE,aAAa,EAAE;QACzC8N,oBAAoB,CAAEgC,IAAI,CAAqBvP,KAAK,EAAEiN,UAAU,CAAC;MACnE;MACA;IACJ,CAAC;IACDgD,OAAO,EAAET,OAAO,KAAK;MACnBJ,YAAY,EAAEI,OAAO,CAACU,MAAM,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC;IAChD,CAAC;EACL,CAAC,CAAC,EAAE,CAAC5O,YAAY,EAAEwL,SAAS,EAAEQ,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAMuC,aAAa,GAAGA,CAACM,OAA2B,EAAEC,SAAyB,KAAa;IACtF,IAAID,OAAO,KAAKhG,SAAS,EAAE,OAAO7I,YAAY,CAAC0C,MAAM;IAErD,MAAMqM,aAAa,GAAGD,SAAS,CAACE,qBAAqB,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGJ,OAAO,GAAGE,aAAa,CAACG,GAAG,GAAGJ,SAAS,CAACK,SAAS;IAEjE,IAAIC,eAAe,GAAGpP,YAAY,CAAC0C,MAAM;IACzC,MAAM2M,QAAQ,GAAGvG,KAAK,CAACwG,IAAI,CAACR,SAAS,CAACO,QAAQ,CAAkB;IAEhE,KAAK,IAAIhH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgH,QAAQ,CAAC3M,MAAM,EAAE2F,CAAC,EAAE,EAAE;MACtC,MAAMkH,KAAK,GAAGF,QAAQ,CAAChH,CAAC,CAAC;MACzB,IAAI,CAACkH,KAAK,CAACC,SAAS,IAAI,CAACD,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MAEtE,MAAMC,QAAQ,GAAGH,KAAK,CAACI,SAAS;MAChC,MAAMC,WAAW,GAAGL,KAAK,CAACM,YAAY;MACtC,MAAMC,OAAO,GAAGJ,QAAQ,GAAGE,WAAW,GAAG,CAAC;MAE1C,IAAIX,OAAO,GAAGa,OAAO,EAAE;QACnBV,eAAe,GAAG/G,CAAC;QACnB;MACJ;IACJ;IACA,OAAO+G,eAAe;EAC1B,CAAC;;EAED;EACAvS,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACAsB,aAAa,CAAC4R,KAAK,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,sBAAsB,GAAGpT,WAAW,CAAC,MAAM;IAC/C;IACA,IAAI,QAAQ,IAAIqT,WAAW,EAAE;MAC3B,MAAMC,UAAU,GAAID,WAAW,CAASE,MAAM;MAC9C,IAAID,UAAU,CAACE,cAAc,GAAGF,UAAU,CAACG,eAAe,GAAG,GAAG,EAAE;QAChEhM,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAC5E;QACArF,eAAe,CAAC,CAAC;QACjB;MACF;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApC,SAAS,CAAC,MAAM;IACd,IAAI,iBAAiB,IAAImH,MAAM,EAAE;MAC9BA,MAAM,CAASC,gBAAgB,CAAC,eAAe,EAAE+L,sBAAsB,CAAC;MACzE,OAAO,MAAM;QACVhM,MAAM,CAASE,mBAAmB,CAAC,eAAe,EAAE8L,sBAAsB,CAAC;MAC9E,CAAC;IACH;EACF,CAAC,EAAE,CAACA,sBAAsB,CAAC,CAAC;;EAE5B;EACA,IAAIlP,SAAS,EAAE;IACb,oBAAOnD,OAAA;MAAK2S,SAAS,EAAC,iEAAiE;MAAAjB,QAAA,EAAC;IAAiB;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjH;;EAEA;EACA,IAAI1P,KAAK,IAAI,CAAClB,QAAQ,EAAE;IACtB,oBAAOnC,OAAA;MAAK2S,SAAS,EAAC,+DAA+D;MAAAjB,QAAA,GAAC,sCAAoC,EAACrO,KAAK;IAAA;MAAAuP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACzI;EAEA,oBACE/S,OAAA;IAAK2S,SAAS,EAAC,iDAAiD;IAAAjB,QAAA,gBAE9D1R,OAAA;MAAK2S,SAAS,EAAC,wGAAwG;MAAAjB,QAAA,gBACrH1R,OAAA;QAAK2S,SAAS,EAAC,0CAA0C;QAAAjB,QAAA,gBACvD1R,OAAA;UAAI2S,SAAS,EAAC,qCAAqC;UAAAjB,QAAA,EAChD,CAAAvP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,YAAY,KAAI;QAAmB;UAAAiP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAGL/S,OAAA;UAAK2S,SAAS,EAAC,6BAA6B;UAAAjB,QAAA,gBAC1C1R,OAAA;YACEgT,OAAO,EAAErN,IAAK;YACdsN,QAAQ,EAAE,CAACrO,OAAQ;YACnB+N,SAAS,EAAC,yGAAyG;YACnHO,KAAK,EAAC,eAAe;YAAAxB,QAAA,eAErB1R,OAAA;cAAK2S,SAAS,EAAC,SAAS;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eAC5E1R,OAAA;gBAAMsT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA0C;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT/S,OAAA;YACEgT,OAAO,EAAElN,IAAK;YACdmN,QAAQ,EAAE,CAACnO,OAAQ;YACnB6N,SAAS,EAAC,yGAAyG;YACnHO,KAAK,EAAC,eAAe;YAAAxB,QAAA,eAErB1R,OAAA;cAAK2S,SAAS,EAAC,SAAS;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eAC5E1R,OAAA;gBAAMsT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA+C;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/S,OAAA;QAAK2S,SAAS,EAAC,kCAAkC;QAAAjB,QAAA,eAE/C1R,OAAA;UAAK2S,SAAS,EAAC,kEAAkE;UAAAjB,QAAA,gBAC/E1R,OAAA;YACEgT,OAAO,EAAEA,CAAA,KAAM1O,WAAW,CAAC,MAAM,CAAE;YACnCqO,SAAS,EAAE,8FACTtO,QAAQ,KAAK,MAAM,GAAG,oCAAoC,GAAG,mCAAmC,EAC/F;YAAAqN,QAAA,gBAEH1R,OAAA;cAAK2S,SAAS,EAAC,cAAc;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eACjF1R,OAAA;gBAAMsT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAwH;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7L,CAAC,QAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/S,OAAA;YACEgT,OAAO,EAAEA,CAAA,KAAM1O,WAAW,CAAC,OAAO,CAAE;YACpCqO,SAAS,EAAE,8FACTtO,QAAQ,KAAK,OAAO,GAAG,oCAAoC,GAAG,mCAAmC,EAChG;YAAAqN,QAAA,gBAEH1R,OAAA;cAAK2S,SAAS,EAAC,cAAc;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eACjF1R,OAAA;gBAAMsT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAwM;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7Q,CAAC,SAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/S,OAAA;YACEgT,OAAO,EAAEA,CAAA,KAAM1O,WAAW,CAAC,SAAS,CAAE;YACtCqO,SAAS,EAAE,8FACTtO,QAAQ,KAAK,SAAS,GAAG,oCAAoC,GAAG,mCAAmC,EAClG;YAAAqN,QAAA,gBAEH1R,OAAA;cAAK2S,SAAS,EAAC,cAAc;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,gBACjF1R,OAAA;gBAAMsT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAkC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1G/S,OAAA;gBAAMsT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyH;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9L,CAAC,WAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/S,OAAA;QAAK2S,SAAS,EAAC,2CAA2C;QAAAjB,QAAA,gBAExD1R,OAAA;UAAK2S,SAAS,EAAC,8CAA8C;UAAAjB,QAAA,gBAC3D1R,OAAA;YACEgT,OAAO,EAAEA,CAAA,KAAMlQ,cAAc,CAAC,SAAS,CAAE;YACzC6P,SAAS,EAAE,8DACT9P,WAAW,KAAK,SAAS,GAAG,oCAAoC,GAAG,mCAAmC,EACrG;YAAA6O,QAAA,EACJ;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT/S,OAAA;YACEgT,OAAO,EAAEA,CAAA,KAAMlQ,cAAc,CAAC,QAAQ,CAAE;YACxC6P,SAAS,EAAE,8DACT9P,WAAW,KAAK,QAAQ,GAAG,oCAAoC,GAAG,mCAAmC,EACpG;YAAA6O,QAAA,EACJ;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN/S,OAAA;UACEgT,OAAO,EAAEA,CAAA,KAAM;YACb5Q,WAAW,CAACgD,IAAI,KAAK;cAAE,IAAIA,IAAI,IAAI,CAAC,CAAC,CAAC;cAAEzB,YAAY,EAAE,CAAAyB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEzB,YAAY,KAAI;YAAoB,CAAC,CAAa,CAAC;YAC/GH,YAAY,CAAC,IAAI,CAAC;YAClBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACF+P,QAAQ,EAAElQ,QAAS;UACnB4P,SAAS,EAAC,kPAAkP;UAAAjB,QAAA,EAE3P3O,QAAQ,GAAG,WAAW,GAAG;QAAe;UAAA6P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL1P,KAAK,IAAIpB,UAAU,iBAChBjC,OAAA;MAAK2S,SAAS,EAAC,yEAAyE;MAAAjB,QAAA,GAAC,SAAO,EAACrO,KAAK;IAAA;MAAAuP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGjH/S,OAAA;MAAK2S,SAAS,EAAE,4CAA6C;MAAAjB,QAAA,gBAE3D1R,OAAA;QAAK2S,SAAS,EAAC,mGAAmG;QAAAjB,QAAA,gBAChH1R,OAAA;UAAI2S,SAAS,EAAC,4FAA4F;UAAAjB,QAAA,EAAC;QAAa;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7H/S,OAAA,CAACJ,YAAY;UACXgB,MAAM,EAAE2B,eAAgB;UACxBmR,UAAU,EAAG5S,KAAK,IAAKuN,oBAAoB,CAACvN,KAAK,EAAEuB,YAAY,CAAC0C,MAAM,CAAE,CAAC;QAAA;UAAA6N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN/S,OAAA;QAAK2T,GAAG,EAAExD,IAAK;QAACwC,SAAS,EAAE,qEAAqEzC,YAAY,GAAG,6CAA6C,GAAG,EAAE,EAAG;QAAAwB,QAAA,eAClK1R,OAAA;UAAK2T,GAAG,EAAE1O,eAAgB;UAAC0N,SAAS,EAAC,2DAA2D;UAAAjB,QAAA,EAC7FrP,YAAY,CAAC0C,MAAM,KAAK,CAAC,gBACxB/E,OAAA;YAAK2S,SAAS,EAAC,oJAAoJ;YAAAjB,QAAA,eACjK1R,OAAA;cAAA0R,QAAA,GAAG,mCAAiC,eAAA1R,OAAA;gBAAA4S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,8CAA0C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,GAEN1Q,YAAY,CAACxB,GAAG,CAAC,CAACC,KAAK,EAAEiH,KAAK,kBAC5B/H,OAAA,CAAC4T,cAAc;YACU;YACvB9S,KAAK,EAAEA,KAAM;YACbiH,KAAK,EAAEA,KAAM;YACb8F,SAAS,EAAEA,SAAU;YACrBU,WAAW,EAAEA,WAAY;YACzBG,cAAc,EAAEA,cAAe;YAC/BmF,UAAU,EAAEpR,kBAAkB,KAAKsF,KAAM;YACzCiL,OAAO,EAAEA,CAAA,KAAMtQ,qBAAqB,CAACqF,KAAK;UAAE,GAPvCjH,KAAK,CAACC,UAAU;YAAA6R,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQtB,CACF;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/S,OAAA;QAAK2S,SAAS,EAAC,0GAA0G;QAAAjB,QAAA,EACnHjP,kBAAkB,KAAK,IAAI,IAAIJ,YAAY,CAAC0C,MAAM,KAAK,CAAC;QAAA;QACtD;QACA/E,OAAA;UAAK2S,SAAS,EAAC,oDAAoD;UAAAjB,QAAA,gBAC/D1R,OAAA;YAAI2S,SAAS,EAAC,0EAA0E;YAAAjB,QAAA,EAAC;UAAO;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrG/S,OAAA,CAACH,YAAY;YACTqN,IAAI,EAAEvK,WAAY;YAClBmR,IAAI,EAAEjR,WAAY;YAClBkR,SAAS,EAAE/O;UAAiB;YAAA4N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;QAAA;QAEN;QACA/S,OAAA;UAAK2S,SAAS,EAAC,yDAAyD;UAAAjB,QAAA,gBACpE1R,OAAA;YAAI2S,SAAS,EAAC,4GAA4G;YAAAjB,QAAA,gBACxH1R,OAAA;cAAA0R,QAAA,GAAM,QAAM,EAAC,EAAA1P,qBAAA,GAAAK,YAAY,CAACI,kBAAkB,CAAC,cAAAT,qBAAA,uBAAhCA,qBAAA,CAAkCuJ,IAAI,KAAI,OAAO;YAAA;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE/S,OAAA;cACEgT,OAAO,EAAEA,CAAA,KAAMtQ,qBAAqB,CAAC,IAAI,CAAE;cAC3CiQ,SAAS,EAAC,yEAAyE;cACnFO,KAAK,EAAC,cAAc;cAAAxB,QAAA,EACrB;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACL/S,OAAA;YAAK2S,SAAS,EAAC,wBAAwB;YAAAjB,QAAA,EAClCrP,YAAY,CAACI,kBAAkB,CAAC;YAAA;YAAM;YACnCzC,OAAA,CAACL,WAAW;cACRmB,KAAK,EAAEuB,YAAY,CAACI,kBAAkB,CAAE;cACxCuR,QAAQ,EAAGhT,OAAO,IAAK;gBACnB,IAAIyB,kBAAkB,KAAK,IAAI,EAAE;kBAC7BoM,kBAAkB,CAACpM,kBAAkB,EAAEzB,OAAO,CAAC;gBACnD;cACJ;YAAE;cAAA4R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,EAGL9P,aAAa,iBACZjD,OAAA,CAACF,iBAAiB;MAChBmU,WAAW,EAAE,CAAA9R,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,YAAY,KAAI,mBAAoB;MAC3DuQ,MAAM,EAAElF,UAAW;MACnBmF,QAAQ,EAAEA,CAAA,KAAM;QAAEjR,gBAAgB,CAAC,KAAK,CAAC;QAAEM,YAAY,CAAC,IAAI,CAAC;MAAE,CAAE;MACjET,QAAQ,EAAEA,QAAS;MACnBM,KAAK,EAAEE,SAAU,CAAC;IAAA;MAAAqP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAhR,EAAA,CAn7BMD,WAAqB;EAAA,QACFrC,SAAS,EACfD,WAAW,EA0oBKD,OAAO;AAAA;AAAA6U,EAAA,GA5oBpCtS,WAAqB;AA87B3B,MAAM8R,cAA6C,GAAGA,CAAC;EACrD9S,KAAK;EACLiH,KAAK;EACL8F,SAAS;EACTU,WAAW;EACXsF,UAAU;EACVb;AACF,CAAC,KAAK;EAAAqB,GAAA;EACJ,MAAMV,GAAG,GAAGxU,MAAM,CAAiB,IAAI,CAAC;EAExC,MAAM,CAAC;IAAEmV;EAAU,CAAC,EAAEnE,IAAI,CAAC,GAAG5Q,OAAO,CAA8D,OAAO;IACxG6Q,MAAM,EAAE/P,SAAS,CAACC,KAAK;IACvBiU,KAAK,EAAEA,CAAClE,IAAoB,EAAEC,OAAgD,KAAK;MACjF,IAAI,CAACqD,GAAG,CAAClD,OAAO,EAAE;MAClB,MAAM3C,SAAS,GAAGuC,IAAI,CAACtI,KAAK;MAC5B,MAAMgG,UAAU,GAAGhG,KAAK;MACxB,IAAI+F,SAAS,KAAKC,UAAU,EAAE;MAE9B,MAAMyG,iBAAiB,GAAGb,GAAG,CAAClD,OAAO,CAACY,qBAAqB,CAAC,CAAC;MAC7D,MAAMoD,YAAY,GAAG,CAACD,iBAAiB,CAACE,MAAM,GAAGF,iBAAiB,CAACjD,GAAG,IAAI,CAAC;MAC3E,MAAMb,YAAY,GAAGJ,OAAO,CAACK,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACD,YAAY,EAAE;MACnB,MAAMiE,YAAY,GAAGjE,YAAY,CAACG,CAAC,GAAG2D,iBAAiB,CAACjD,GAAG;MAE3D,IAAIzD,SAAS,GAAGC,UAAU,IAAI4G,YAAY,GAAGF,YAAY,EAAE;MAC3D,IAAI3G,SAAS,GAAGC,UAAU,IAAI4G,YAAY,GAAGF,YAAY,EAAE;MAE3D5G,SAAS,CAACC,SAAS,EAAEC,UAAU,CAAC;MAChCsC,IAAI,CAACtI,KAAK,GAAGgG,UAAU,CAAC,CAAC;IAC3B,CAAC;IACDgD,OAAO,EAAGT,OAAO,KAAM;MACnBgE,SAAS,EAAEhE,OAAO,CAACsE,YAAY,CAAC;IACpC,CAAC;EACH,CAAC,CAAC,EAAE,CAAC7M,KAAK,EAAE8F,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEzB,MAAM,CAAC;IAAEgH;EAAW,CAAC,EAAEC,IAAI,CAAC,GAAGxV,OAAO,CAAC,OAAO;IAC5CwR,IAAI,EAAEzQ,SAAS,CAACC,KAAK;IACrB+P,IAAI,EAAE;MAAEtI,KAAK;MAAED,EAAE,EAAEhH,KAAK,CAACC,UAAU;MAAE+P,IAAI,EAAEzQ,SAAS,CAACC;IAAM,CAAC;IAAE;IAC9DyQ,OAAO,EAAGT,OAAO,KAAM;MACrBuE,UAAU,EAAEvE,OAAO,CAACuE,UAAU,CAAC;IACjC,CAAC;EACH,CAAC,CAAC,EAAE,CAAC9M,KAAK,EAAEjH,KAAK,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEhC+T,IAAI,CAAC3E,IAAI,CAACwD,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEjB,oBACE3T,OAAA;IACE2T,GAAG,EAAEA,GAAI;IACT,mBAAiBW,SAAU;IAC3B3B,SAAS,EAAE,yEAAyEkC,UAAU,GAAG,4BAA4B,GAAG,uCAAuC,IAAIhB,UAAU,GAAG,oDAAoD,GAAG,iBAAiB,EAAG,CAAC;IAAA;IACpQb,OAAO,EAAEA,OAAQ;IACjB+B,KAAK,EAAE;MAAEC,OAAO,EAAEH,UAAU,GAAG,GAAG,GAAG;IAAE,CAAE;IAAAnD,QAAA,gBAEzC1R,OAAA;MAAK2S,SAAS,EAAC,qHAAqH;MAAAjB,QAAA,GAAC,GAAC,eACpI1R,OAAA;QAAM2S,SAAS,EAAC,oDAAoD;QAACO,KAAK,EAAEpS,KAAK,CAACyK,IAAK;QAAAmG,QAAA,GAAE5Q,KAAK,CAACyK,IAAI,EAAC,GAAC,eAAAvL,OAAA;UAAM2S,SAAS,EAAC,eAAe;UAAAjB,QAAA,GAAC,GAAC,EAAC5Q,KAAK,CAACmU,QAAQ,EAAC,GAAC;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrK/S,OAAA;QAAK2S,SAAS,EAAC,2CAA2C;QAAAjB,QAAA,GAAC,GAAC,eAEzD1R,OAAA;UACC8Q,IAAI,EAAC,QAAQ;UACb6B,SAAS,EAAC,yDAAyD;UACnEK,OAAO,EAAGhN,CAAC,IAAK;YACZA,CAAC,CAACkP,eAAe,CAAC,CAAC;YACnBxO,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEoB,KAAK,CAAC,CAAC,CAAC;UAC5C,CAAE;UACFmL,KAAK,EAAC,iBAAiB;UAAAxB,QAAA,eAGpB1R,OAAA;YAAKmV,KAAK,EAAC,4BAA4B;YAACxC,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAACI,WAAW,EAAE,CAAE;YAAA9B,QAAA,eAC7H1R,OAAA;cAAMsT,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACE,CAAC,EAAC;YAAuH;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9K;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEV/S,OAAA;UACE8Q,IAAI,EAAC,QAAQ;UACb6B,SAAS,EAAC,qEAAqE,CAAC;UAAA;UAChFK,OAAO,EAAGhN,CAAC,IAAK;YACdA,CAAC,CAACkP,eAAe,CAAC,CAAC;YACnB3G,WAAW,CAACxG,KAAK,CAAC;UACpB,CAAE;UACFmL,KAAK,EAAC,cAAc;UAAAxB,QAAA,eAGpB1R,OAAA;YAAKmV,KAAK,EAAC,4BAA4B;YAACxC,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAACI,WAAW,EAAE,CAAE;YAAA9B,QAAA,eAC7H1R,OAAA;cAAMsT,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACE,CAAC,EAAC;YAAsB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/S,OAAA;MAAK2S,SAAS,EAAC,oFAAoF;MAAAjB,QAAA,GAChG,CAAC,MAAM;QACN;QACA,IAAI5Q,KAAK,CAACG,OAAO,KAAK,mBAAmB,EAAE;UACzC,OAAO,mBAAmB;QAC5B,CAAC,MAAM,IAAIH,KAAK,CAACG,OAAO,KAAK,aAAa,EAAE;UAAA,IAAAmU,cAAA;UAC1C,OAAO,EAAAA,cAAA,GAAAtU,KAAK,CAACE,OAAO,cAAAoU,cAAA,uBAAbA,cAAA,CAAeC,YAAY,KAAI,cAAc;QACtD,CAAC,MAAM,IAAIvU,KAAK,CAACG,OAAO,KAAK,kBAAkB,EAAE;UAAA,IAAAqU,eAAA;UAC/C,OAAO,EAAAA,eAAA,GAAAxU,KAAK,CAACE,OAAO,cAAAsU,eAAA,uBAAbA,eAAA,CAAeC,QAAQ,KAAI,UAAU;QAC9C,CAAC,MAAM,IAAIzU,KAAK,CAACG,OAAO,KAAK,cAAc,EAAE;UAAA,IAAAuU,eAAA,EAAAC,eAAA,EAAAC,eAAA;UAC3C,OAAO,gBAAgB,GAAG,EAAAF,eAAA,GACxB1U,KAAK,CAACE,OAAO,cAAAwU,eAAA,uBAAbA,eAAA,CAAeG,UAAU,GAAAF,eAAA,GACzB3U,KAAK,CAACE,OAAO,cAAAyU,eAAA,uBAAbA,eAAA,CAAeG,UAAU,GAAAF,eAAA,GACzB5U,KAAK,CAACE,OAAO,cAAA0U,eAAA,uBAAbA,eAAA,CAAeG,UAAU,CAC1B,CAACjN,MAAM,CAACwG,OAAO,CAAC,CAAC/N,IAAI,CAAC,IAAI,CAAC;QAC9B,CAAC,MAAM,IAAIP,KAAK,CAACG,OAAO,KAAK,YAAY,EAAE;UAAA,IAAA6U,eAAA;UACzC,OAAO,EAAAA,eAAA,GAAAhV,KAAK,CAACE,OAAO,cAAA8U,eAAA,uBAAbA,eAAA,CAAeC,UAAU,KAAI,QAAQ;QAC9C,CAAC,MAAM,IAAIjV,KAAK,CAACG,OAAO,KAAK,iBAAiB,EAAE;UAAA,IAAA+U,eAAA;UAC9C,OAAO,CAAAA,eAAA,GAAAlV,KAAK,CAACE,OAAO,cAAAgV,eAAA,eAAbA,eAAA,CAAeC,WAAW,GAAG,WAAWnV,KAAK,CAACE,OAAO,CAACiV,WAAW,EAAE,GAAG,iBAAiB;QAChG,CAAC,MAAM;UAAA,IAAAC,eAAA,EAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA;UACL;UACA,OAAO,EAAAH,eAAA,GAAApV,KAAK,CAACE,OAAO,cAAAkV,eAAA,uBAAbA,eAAA,CAAeX,QAAQ,KACvB,EAAAY,eAAA,GAAArV,KAAK,CAACE,OAAO,cAAAmV,eAAA,wBAAAC,oBAAA,GAAbD,eAAA,CAAepJ,IAAI,cAAAqJ,oBAAA,uBAAnBA,oBAAA,CAAqB1N,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KACpC,CAAA2N,gBAAA,GAAAvV,KAAK,CAACE,OAAO,cAAAqV,gBAAA,eAAbA,gBAAA,CAAetJ,IAAI,IAAIjM,KAAK,CAACE,OAAO,CAAC+L,IAAI,CAAChI,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC,IACpEjE,KAAK,CAACyK,IAAI;QACnB;MACF,CAAC,EAAE,CAAC,EACHzK,KAAK,CAACwV,SAAS,iBAAItW,OAAA;QAAKuW,GAAG,EAAEzV,KAAK,CAACwV,SAAU;QAACE,GAAG,EAAE,GAAG1V,KAAK,CAACyK,IAAI,YAAa;QAACoH,SAAS,EAAC;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsB,GAAA,CAxHIT,cAA6C;EAAA,QAUnBrU,OAAO,EAyBND,OAAO;AAAA;AAAAmX,GAAA,GAnClC7C,cAA6C;AA0HnD,SAAS9R,WAAW;AACpB,eAAeA,WAAW;AAAC,IAAAsS,EAAA,EAAAqC,GAAA;AAAAC,YAAA,CAAAtC,EAAA;AAAAsC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}