{"version": 3, "file": "static/js/343.b29228ec.chunk.js", "mappings": "yJAAA,MACA,GAAiBA,WADoB,qBAAXC,QAA0BA,OAAOD,YAAcC,OAAOD,WAAWE,KAAKD,SCAhG,IAAIE,EACJ,MAAMC,EAAQ,IAAIC,WAAW,ICA7B,MAAMC,EAAY,GAClB,IAAK,IAAIC,EAAI,EAAGA,EAAI,MAAOA,EACvBD,EAAUE,MAAMD,EAAI,KAAOE,SAAS,IAAIC,MAAM,IAE3C,SAASC,EAAgBC,GAAiB,IAAZC,EAAMC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EAC1C,OAAQR,EAAUM,EAAIC,EAAS,IAC3BP,EAAUM,EAAIC,EAAS,IACvBP,EAAUM,EAAIC,EAAS,IACvBP,EAAUM,EAAIC,EAAS,IACvB,IACAP,EAAUM,EAAIC,EAAS,IACvBP,EAAUM,EAAIC,EAAS,IACvB,IACAP,EAAUM,EAAIC,EAAS,IACvBP,EAAUM,EAAIC,EAAS,IACvB,IACAP,EAAUM,EAAIC,EAAS,IACvBP,EAAUM,EAAIC,EAAS,IACvB,IACAP,EAAUM,EAAIC,EAAS,KACvBP,EAAUM,EAAIC,EAAS,KACvBP,EAAUM,EAAIC,EAAS,KACvBP,EAAUM,EAAIC,EAAS,KACvBP,EAAUM,EAAIC,EAAS,KACvBP,EAAUM,EAAIC,EAAS,MAAMI,aACrC,CCAA,QAvBA,SAAYC,EAASC,EAAKN,GACtB,GAAIO,EAAOpB,aAAemB,IAAQD,EAC9B,OAAOE,EAAOpB,aAGlB,MAAMqB,GADNH,EAAUA,GAAW,CAAC,GACDI,QAAUJ,EAAQK,SFN5B,WACX,IAAKpB,EAAiB,CAClB,GAAsB,qBAAXF,SAA2BA,OAAOE,gBACzC,MAAM,IAAIqB,MAAM,4GAEpBrB,EAAkBF,OAAOE,gBAAgBD,KAAKD,OAClD,CACA,OAAOE,EAAgBC,EAC3B,CEFsDmB,GAClD,GAAIF,EAAKN,OAAS,GACd,MAAM,IAAIS,MAAM,qCAIpB,GAFAH,EAAK,GAAgB,GAAVA,EAAK,GAAa,GAC7BA,EAAK,GAAgB,GAAVA,EAAK,GAAa,IACzBF,EAAK,CAEL,IADAN,EAASA,GAAU,GACN,GAAKA,EAAS,GAAKM,EAAIJ,OAChC,MAAM,IAAIU,WAAW,mBAAmBZ,KAAUA,EAAS,8BAE/D,IAAK,IAAIN,EAAI,EAAGA,EAAI,KAAMA,EACtBY,EAAIN,EAASN,GAAKc,EAAKd,GAE3B,OAAOY,CACX,CACA,OAAOR,EAAgBU,EAC3B,E,qDCKA,MA+fA,EA/f8BK,KAE5B,MAAOC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAuB,KAEtDC,EAAiBC,IAAsBF,EAAAA,EAAAA,WAAS,IAGhDG,EAAmBC,IAAwBJ,EAAAA,EAAAA,UAAS,KACpDK,EAAsBC,IAA2BN,EAAAA,EAAAA,UAAgC,WACjFO,EAAoBC,IAAyBR,EAAAA,EAAAA,UAAyB,KACtES,EAAoBC,IAAyBV,EAAAA,EAAAA,WAAS,IAGtDW,EAAoBC,IAAyBZ,EAAAA,EAAAA,UAA0B,KACvEa,EAAkBC,IAAuBd,EAAAA,EAAAA,WAAS,IAClDe,EAAgBC,IAAqBhB,EAAAA,EAAAA,UAAwB,OAG7DiB,EAAmBC,IAAwBlB,EAAAA,EAAAA,WAAS,IACpDmB,EAAoBC,IAAyBpB,EAAAA,EAAAA,UAAwB,OAErEqB,EAAOC,IAAYtB,EAAAA,EAAAA,UAAS,KAC5BuB,EAASC,IAAcxB,EAAAA,EAAAA,UAAS,KAChCyB,EAAmBC,IAAwB1B,EAAAA,EAAAA,UAA4B,OACvE2B,EAAeC,IAAoB5B,EAAAA,EAAAA,WAAS,IAGnD6B,EAAAA,EAAAA,YAAU,KAEeC,WACrBhB,GAAoB,GACpBE,EAAkB,MAClB,IAEE,MAAMe,QAAiBC,EAAAA,GAA8BC,kBACrD,GAAIF,GAAYA,EAASG,KAAM,CAE7B,MAAMC,EAAsCJ,EAASG,KAAKE,KAAKC,IAAS,CACtEC,GAAID,EAAKE,IACTC,KAAMH,EAAKI,cAAgBJ,EAAKG,MAAQ,wBAE1C5B,EAAsBuB,EACxB,MAEEO,QAAQC,KAAK,+CAAgDZ,GAC7Df,EAAkB,gDAClBJ,EAAsB,GAE1B,CAAE,MAAOgC,GAAM,IAADC,EAAAC,EACZJ,QAAQrB,MAAM,mCAAoCuB,GAElD,MAAMG,GAAoC,QAArBF,EAACD,EAAYb,gBAAQ,IAAAc,GAAM,QAANC,EAArBD,EAAuBX,YAAI,IAAAY,OAAN,EAArBA,EAA6BE,UAAYJ,EAAYI,SAAW,kCACrFhC,EAAkB+B,GAClBnC,EAAsB,GACxB,CAAC,QACCE,GAAoB,EACtB,GAGFmC,EAAgB,GACf,IAEH,MA2CMC,EAAqBC,IACzB,IAAIC,EACJ,MAAMC,EAASC,IAGbF,EADW,cAATD,EACQ,CACRb,GAAIe,EACJF,KAAM,YACNI,WAAY,MAGJ,CACRjB,GAAIe,EACJF,KAAM,QACNK,SAAU,EACVC,KAAM,QAIVjD,EAAsB,IAAID,EAAoB6C,IAC9C1C,GAAsB,EAAM,EASxBgD,EAAuBA,CAACL,EAAgBM,KAC5CnD,GAAsBoD,GACpBA,EAAUxB,KAAIyB,IACZ,GAAIA,EAAKvB,KAAOe,EAAQ,CAOtB,MALoB,IAAKQ,KAASF,EAMpC,CACA,OAAOE,CAAI,KAEd,EAsBH,OAEEC,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yCAAwCD,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,qBACtCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,sBAAqBD,SAAC,2DAGrCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAASA,IAAMlE,GAAmB,GAAM8D,SAAC,yBAKlDzC,IACC2C,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CACJlB,KAAK,UACLH,QAASzB,EACT+C,QAASA,IAAM9C,EAAW,IAC1ByC,UAAU,SAIU,IAAvBnE,EAAYZ,QACXgF,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAAAP,UACHF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,mBAAkBD,SAAA,EAC/BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,gBAAeD,SAAC,kBAC/BE,EAAAA,EAAAA,KAAA,MAAID,UAAU,2BAA0BD,SAAC,wBACzCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,2BAA0BD,SAAC,8EAGxCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAASA,IAAMlE,GAAmB,GAAM8D,SAAC,uCAMrDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uDAAsDD,SAClElE,EAAYsC,KAAIoC,IACfV,EAAAA,EAAAA,MAACS,EAAAA,EAAI,CAAAP,SAAA,EACHF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,wCAAuCD,SAAA,EACpDE,EAAAA,EAAAA,KAAA,MAAID,UAAU,sBAAqBD,SAAEQ,EAAWhC,QAChD0B,EAAAA,EAAAA,KAAA,QAAMD,UAAW,8BACO,WAAtBO,EAAWC,OAAsB,eAAiB,eACjDT,SACsB,WAAtBQ,EAAWC,OAAsB,SAAW,iBAIjDX,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBD,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,uBAAsBD,SAAA,EACnCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,sBAAqBD,SAAC,aACtCE,EAAAA,EAAAA,KAAA,QAAAF,SACGQ,EAAWE,SAASC,MAAMC,QAAOf,GAAsB,cAAdA,EAAKV,OAAsBjE,aAGzE4E,EAAAA,EAAAA,MAAA,OAAKG,UAAU,uBAAsBD,SAAA,EACnCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,sBAAqBD,SAAC,cACtCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,aAAYD,SACO,eAAhCQ,EAAWE,SAASG,QAA2B,cAAgB,eAGpEf,EAAAA,EAAAA,MAAA,OAAKG,UAAU,uBAAsBD,SAAA,EACnCE,EAAAA,EAAAA,KAAA,QAAMD,UAAU,sBAAqBD,SAAC,cACtCE,EAAAA,EAAAA,KAAA,QAAAF,SAAOQ,EAAWM,iBAItBhB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBD,SAAA,EAC7BE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACY,QAAQ,YAAYC,KAAK,KAAKZ,QAASA,KAAMa,OAxFnC3C,EAwF0DkC,EAAWlC,QAvFnGvC,EAAeD,EAAYsC,KAAIoC,GACzBA,EAAWlC,KAAOA,EACb,IACFkC,EACHC,OAA8B,WAAtBD,EAAWC,OAAsB,WAAa,UAGnDD,KARqBlC,KAwFyE,EAAA0B,SAClE,WAAtBQ,EAAWC,OAAsB,aAAe,cAEnDP,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACa,KAAK,KAAKZ,QAASA,IA9EhBI,KACvB9C,EAAqB8C,GACrB5C,GAAiB,GACjBN,EAAS,GAAG,EA2EiC4D,CAAgBV,GAAYR,SAAC,cAjCvDQ,EAAWlC,QA2C3BrC,IACCiE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iFAAgFD,UAC7FF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iDAAgDD,SAAA,EAC7DE,EAAAA,EAAAA,KAAA,MAAID,UAAU,6BAA4BD,SAAC,sBAE1C3C,IACC6C,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CACJlB,KAAK,QACLH,QAAS3B,EACTiD,QAASA,IAAMhD,EAAS,IACxB2C,UAAU,UAIdC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,OAAMD,UACnBE,EAAAA,EAAAA,KAACiB,EAAAA,EAAK,CACJ7C,GAAG,iBACHE,KAAK,iBACL4C,MAAM,kBACNC,MAAOlF,EACPmF,SAAWC,GAAMnF,EAAqBmF,EAAEC,OAAOH,OAC/CI,UAAQ,OAIZ3B,EAAAA,EAAAA,MAAA,OAAKG,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,aAAYD,SAAC,aAC9BF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,YAAWD,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,SACEf,KAAK,QACLb,GAAG,gBACHE,KAAK,UACL6C,MAAM,SACNK,QAAkC,WAAzBrF,EACTiF,SAAUA,IAAMhF,EAAwB,UACxC2D,UAAU,UAEZC,EAAAA,EAAAA,KAAA,SAAOyB,QAAQ,gBAAe3B,SAAC,yCAEjCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,SACEf,KAAK,QACLb,GAAG,oBACHE,KAAK,UACL6C,MAAM,aACNK,QAAkC,eAAzBrF,EACTiF,SAAUA,IAAMhF,EAAwB,cACxC2D,UAAU,UAEZC,EAAAA,EAAAA,KAAA,SAAOyB,QAAQ,oBAAmB3B,SAAC,8DAMzCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,OAAMD,SAAA,EAACE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BAClCC,EAAAA,EAAAA,KAAA,MAAID,UAAU,2BAA0BD,SAAC,oBACzCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBD,SAAA,EAACE,EAAAA,EAAAA,KAAA,MAAID,UAAU,yBACb,IAA9B1D,EAAmBrB,QAClBgF,EAAAA,EAAAA,KAAA,KAAGD,UAAU,kCAAiCD,SAAC,yBAE/CzD,EAAmB6B,KAAI,CAACyB,EAAM+B,KAAK,IAAAC,EAAA,OACjC/B,EAAAA,EAAAA,MAAA,OAAmBG,UAAU,kFAAiFD,SAAA,EAACE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BAC3HH,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMG,UAAU,cAAaD,SAAA,CAAC,QAAM4B,EAAQ,EAAE,QAC/B,cAAd/B,EAAKV,OACJW,EAAAA,EAAAA,MAAA,QAAMG,UAAU,8BAA6BD,SAAA,EAACE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BAC1DC,EAAAA,EAAAA,KAAA,QAAAF,SAAM,iBACNE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLY,QAAQ,YACRC,KAAK,KACLZ,QAASA,KACPhD,EAAsByC,EAAKvB,IAC3BpB,GAAqB,EAAK,EAC1B8C,SAGCH,EAA+BN,YACmD,QAAjFsC,EAAAlF,EAAmBmF,MAAKC,GAAKA,EAAEzD,KAAQuB,EAA+BN,oBAAW,IAAAsC,OAAA,EAAjFA,EAAmFrD,OAAQ,mBAC3F,uBAKK,UAAdqB,EAAKV,OACJW,EAAAA,EAAAA,MAAA,QAAMG,UAAU,8BAA6BD,SAAA,EAACE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BAC1DC,EAAAA,EAAAA,KAAA,QAAAF,SAAM,cACNE,EAAAA,EAAAA,KAACiB,EAAAA,EAAK,CACJ7C,GAAI,YAAYuB,EAAKvB,KACrBE,KAAM,YAAYqB,EAAKvB,KACvBa,KAAK,SACLkC,MAAOW,OAAQnC,EAA2BL,UAC1C8B,SAAWC,IACT,MAAMU,EAAcC,SAASX,EAAEC,OAAOH,MAAO,KAAO,EAEpD3B,EAAqBG,EAAKvB,GAAI,CAAEkB,SAAU2C,KAAKC,IAAI,EAAGH,IAAe,EAEvEhC,UAAU,0BAEZH,EAAAA,EAAAA,MAAA,UACEuB,MAAQxB,EAA2BJ,KACnC6B,SAAWC,GAAM7B,EAAqBG,EAAKvB,GAAI,CAAEmB,KAAM8B,EAAEC,OAAOH,QAChEpB,UAAU,qDAAoDD,SAAA,EAE9DE,EAAAA,EAAAA,KAAA,UAAQmB,MAAM,UAASrB,SAAC,aACxBE,EAAAA,EAAAA,KAAA,UAAQmB,MAAM,QAAOrB,SAAC,WACtBE,EAAAA,EAAAA,KAAA,UAAQmB,MAAM,OAAMrB,SAAC,mBAM7BE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLY,QAAQ,SACRC,KAAK,KACLZ,QAASA,KAAMiC,OA/OLhD,EA+O4BQ,EAAKvB,QA9O/D9B,EAAsBD,EAAmBqE,QAAOf,GAAQA,EAAKvB,KAAOe,KADtCA,KA+OqC,EAC/CY,UAAU,OAAMD,SACjB,YAvDOH,EAAKvB,GA0DT,QAMZwB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,WAAUD,SAAA,EAACE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BACtCC,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLY,QAAQ,YACRd,UAAU,SACVG,QAASA,IAAM1D,GAAuBD,GAAoBuD,SAC3D,eAGAvD,IACCqD,EAAAA,EAAAA,MAAA,OAAKG,UAAU,6FAA4FD,SAAA,EAACE,EAAAA,EAAAA,KAAA,MAAID,UAAU,0BACxHH,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAAA,UACEE,QAASA,IAAMlB,EAAkB,aACjCe,UAAU,oDAAmDD,SAC9D,kBAIHE,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAAA,UACEE,QAASA,IAAMlB,EAAkB,SACjCe,UAAU,oDAAmDD,SAC9D,gCAWbF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACY,QAAQ,YAAYX,QAASA,IAAMlE,GAAmB,GAAO8D,SAAC,YAGtEE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAnWWkC,KAC7B,IAAKnG,EAEH,YADAmB,EAAS,+BAIX,GAAkC,IAA9Bf,EAAmBrB,OAErB,YADAoC,EAAS,2CAGXA,EAAS,IAGT,MAOMiF,EAAkC,CACpCjE,GARUxC,EAAYZ,OAAS,EAAIiH,KAAKC,OAAOtG,EAAYsC,KAAIoE,GAAKA,EAAElE,MAAO,EAAI,EASjFE,KAAMrC,EACNsE,OAAQ,WACRC,SAToC,CACpCG,QAASxE,EACTsE,MAAOpE,GAQPuE,SAAS,IAAI2B,MAAOC,cAAcC,MAAM,KAAK,IAGjD5G,EAAe,IAAID,EAAayG,IAEhC/E,EAAW,oCACXtB,GAAmB,GAEnBE,EAAqB,IACrBE,EAAwB,UACxBE,EAAsB,IAEtBoG,YAAW,KACTpF,EAAW,GAAG,GACb,IAAK,EA4T0CwC,SAAC,mBAShDrC,GAAiBF,IAChByC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iFAAgFD,UAC7FF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iDAAgDD,SAAA,EAC7DF,EAAAA,EAAAA,MAAA,MAAIG,UAAU,6BAA4BD,SAAA,CAAC,oBAAkBvC,EAAkBe,QAE9EnB,IACC6C,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CACJlB,KAAK,QACLH,QAAS3B,EACTiD,QAASA,IAAMhD,EAAS,IACxB2C,UAAU,UAKdC,EAAAA,EAAAA,KAAA,KAAGD,UAAU,uCAAsCD,SAAC,oCAEpDF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACY,QAAQ,YAAYX,QAASA,IAAMxC,GAAiB,GAAOoC,SAAC,YAGpEF,EAAAA,EAAAA,MAACK,EAAAA,EAAO,CAAAH,SAAA,CAAsC,IAAiC,0BAStF/C,IACCiD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mFAAkFD,UAC/FF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,4EAA2ED,SAAA,EACxFE,EAAAA,EAAAA,KAAA,MAAID,UAAU,6BAA4BD,SAAC,0BAE1CnD,IAAoBqD,EAAAA,EAAAA,KAAA,KAAAF,SAAG,yBACvBjD,IAAkBmD,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAAClB,KAAK,QAAQH,QAASjC,EAAgBuD,QAASA,IAAMtD,EAAkB,SAEjGkD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iCAAgCD,UAC3CnD,IAAqBE,IACrBmD,EAAAA,EAAAA,KAAA,MAAID,UAAU,YAAWD,SACtBrD,EAAmBzB,OAAS,EAC3ByB,EAAmByB,KAAIyE,IACrB3C,EAAAA,EAAAA,KAAA,MAAAF,UACEF,EAAAA,EAAAA,MAAA,UACEG,UAAU,uEACVG,QAASA,KACHjD,GAEFuC,EAAqBvC,EAAoB,CAAEoC,WAAYsD,EAASvE,KAElEpB,GAAqB,GACrBE,EAAsB,KAAK,EAC3B4C,SAAA,CAED6C,EAASrE,KAAK,KAACsB,EAAAA,EAAAA,MAAA,QAAMG,UAAU,8BAA6BD,SAAA,CAAC,QAAM6C,EAASvE,GAAG,WAZ3EuE,EAASvE,OAiBpB4B,EAAAA,EAAAA,KAAA,KAAGD,UAAU,kCAAiCD,SAAC,8BAMvDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mBAAkBD,UAC/BE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLY,QAAQ,YACRX,QAASA,KACPlD,GAAqB,GACrBE,EAAsB,KAAK,EAC3B4C,SACH,oBAQX,C", "sources": ["../node_modules/uuid/dist/esm-browser/native.js", "../node_modules/uuid/dist/esm-browser/rng.js", "../node_modules/uuid/dist/esm-browser/stringify.js", "../node_modules/uuid/dist/esm-browser/v4.js", "pages/Automations.tsx"], "sourcesContent": ["const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default { randomUUID };\n", "let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n", "import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n", "import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport { templateRecommendationService } from 'services';\nimport { v4 as uuidv4 } from 'uuid';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\n// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from '../components/Card';\nimport Input from '../components/Input';\n// Import the new types\nimport {\n  Automation,\n  AutomationTriggerType,\n  AutomationWorkflow,\n  WorkflowDelayNode,\n  WorkflowNode,\n  WorkflowSendEmailNode,\n} from '../types/automations';\n\n// Define a simple Template type for simulation\ninterface EmailTemplate {\n  id: string;\n  name: string;\n  // Add other relevant template fields if needed\n}\n\nconst Automations: React.FC = () => {\n  // Use the Automation type for the list of automations\n  const [automations, setAutomations] = useState<Automation[]>([]);\n  \n  const [showCreateModal, setShowCreateModal] = useState(false);\n  \n  // State for the new automation being built in the modal\n  const [newAutomationName, setNewAutomationName] = useState('');\n  const [newAutomationTrigger, setNewAutomationTrigger] = useState<AutomationTriggerType>('manual');\n  const [newAutomationNodes, setNewAutomationNodes] = useState<WorkflowNode[]>([]);\n  const [showAddStepOptions, setShowAddStepOptions] = useState(false); // State to show/hide node type selection\n  \n  // State for Templates\n  const [availableTemplates, setAvailableTemplates] = useState<EmailTemplate[]>([]);\n  const [templatesLoading, setTemplatesLoading] = useState(false);\n  const [templatesError, setTemplatesError] = useState<string | null>(null);\n\n  // State for Template Selection Modal\n  const [showTemplateModal, setShowTemplateModal] = useState(false);\n  const [selectingForNodeId, setSelectingForNodeId] = useState<string | null>(null);\n\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [editingAutomation, setEditingAutomation] = useState<Automation | null>(null); // State for the automation being edited\n  const [showEditModal, setShowEditModal] = useState(false); // State for edit modal visibility\n  \n  // --- Effects ---\n  useEffect(() => {\n    // Fetch real templates when component mounts\n    const fetchTemplates = async () => {\n      setTemplatesLoading(true);\n      setTemplatesError(null);\n      try {\n        // Use the service to fetch templates\n        const response = await templateRecommendationService.getAllTemplates();\n        if (response && response.data) {\n          // Assuming response.data is an array of templates with _id and name\n          const formattedTemplates: EmailTemplate[] = response.data.map((tmpl: any) => ({\n            id: tmpl._id, // Use _id from backend\n            name: tmpl.templateName || tmpl.name || 'Untitled Template' // Adjust based on actual field name\n          }));\n          setAvailableTemplates(formattedTemplates);\n        } else {\n          // Handle case where data is not as expected\n          console.warn('Received unexpected template data structure:', response);\n          setTemplatesError('Failed to load templates: Unexpected format.');\n          setAvailableTemplates([]); // Set to empty array\n        }\n      } catch (err) { // Catch any error\n        console.error('Failed to fetch email templates:', err);\n        // Try to get a specific message, otherwise use a generic one\n        const errorMessage = (err as any).response?.data?.message || (err as any).message || 'Failed to load email templates.';\n        setTemplatesError(errorMessage);\n        setAvailableTemplates([]); // Ensure it's empty on error\n      } finally {\n        setTemplatesLoading(false);\n      }\n    };\n\n    fetchTemplates();\n  }, []);\n\n  const handleCreateAutomation = () => {\n    if (!newAutomationName) {\n      setError('Automation name is required');\n      return;\n    }\n    // Basic validation: Ensure there's at least one step\n    if (newAutomationNodes.length === 0) {\n      setError('Automation must have at least one step.');\n      return;\n    }\n    setError(''); // Clear error if validation passes\n    \n    // This would be an actual API call in the real app\n    const newId = automations.length > 0 ? Math.max(...automations.map(a => a.id)) + 1 : 1;\n    \n    const newWorkflow: AutomationWorkflow = {\n        trigger: newAutomationTrigger,\n        nodes: newAutomationNodes\n    };\n\n    const newAutomationObject: Automation = {\n        id: newId,\n        name: newAutomationName,\n        status: 'inactive', // Default to inactive\n        workflow: newWorkflow,\n        created: new Date().toISOString().split('T')[0]\n    };\n    \n    setAutomations([...automations, newAutomationObject]);\n    \n    setSuccess('Automation created successfully!');\n    setShowCreateModal(false);\n    // Reset all creation states\n    setNewAutomationName('');\n    setNewAutomationTrigger('manual');\n    setNewAutomationNodes([]); \n    \n    setTimeout(() => {\n      setSuccess('');\n    }, 3000);\n  };\n  \n  // Function to add a new node to the workflow\n  const addNodeToWorkflow = (type: 'sendEmail' | 'delay') => {\n    let newNode: WorkflowNode;\n    const nodeId = uuidv4(); // Generate unique ID\n\n    if (type === 'sendEmail') {\n      newNode = {\n        id: nodeId,\n        type: 'sendEmail',\n        templateId: null // Default to no template selected\n      };\n    } else { // type === 'delay'\n      newNode = {\n        id: nodeId,\n        type: 'delay',\n        duration: 1, // Default to 1 day\n        unit: 'days'\n      };\n    }\n\n    setNewAutomationNodes([...newAutomationNodes, newNode]);\n    setShowAddStepOptions(false); // Hide options after adding\n  };\n  \n  // Function to remove a node from the workflow\n  const removeNodeFromWorkflow = (nodeId: string) => {\n    setNewAutomationNodes(newAutomationNodes.filter(node => node.id !== nodeId));\n  };\n  \n  // Function to update a specific node in the workflow\n  const updateNodeInWorkflow = (nodeId: string, updates: Partial<WorkflowNode>) => {\n    setNewAutomationNodes(prevNodes => \n      prevNodes.map(node => {\n        if (node.id === nodeId) {\n          // Create a new object with updates, ensuring type correctness\n          const updatedNode = { ...node, ...updates };\n          // We might need more sophisticated type checking here if updates \n          // could potentially change the node 'type' or mix properties,\n          // but for simple field updates this should generally be okay.\n          // A more robust solution would check node.type and apply specific updates.\n          return updatedNode as WorkflowNode; // Assert the type for now\n        }\n        return node;\n      })\n    );\n  };\n  \n  const toggleAutomationStatus = (id: number) => {\n    setAutomations(automations.map(automation => {\n      if (automation.id === id) {\n        return {\n          ...automation,\n          status: automation.status === 'active' ? 'inactive' : 'active'\n        };\n      }\n      return automation;\n    }));\n  };\n  \n  // Function to handle clicking the Edit button\n  const handleEditClick = (automation: Automation) => {\n    setEditingAutomation(automation);\n    setShowEditModal(true);\n    setError(''); // Clear any previous errors\n  };\n  \n  return (\n    // <Layout title=\"Automations\">\n    <>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-xl font-semibold\">Email Sequences</h2>\n          <p className=\"text-text-secondary\">Create automated email sequences for your contacts</p>\n        </div>\n        \n        <Button onClick={() => setShowCreateModal(true)}>\n          Create Automation\n        </Button>\n      </div>\n      \n      {success && (\n        <Alert\n          type=\"success\"\n          message={success}\n          onClose={() => setSuccess('')}\n          className=\"mb-6\"\n        />\n      )}\n      \n      {automations.length === 0 ? (\n        <Card>\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-4\">📧</div>\n            <h3 className=\"text-xl font-medium mb-2\">No Automations Yet</h3>\n            <p className=\"text-text-secondary mb-4\">\n              Create your first automation to start sending automated email sequences.\n            </p>\n            <Button onClick={() => setShowCreateModal(true)}>\n              Create Your First Automation\n            </Button>\n          </div>\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {automations.map(automation => (\n            <Card key={automation.id}>\n              <div className=\"flex justify-between items-start mb-4\">\n                <h3 className=\"text-lg font-medium\">{automation.name}</h3>\n                <span className={`px-2 py-1 text-xs rounded ${\n                  automation.status === 'active' ? 'bg-green-800' : 'bg-gray-700'\n                }`}>\n                  {automation.status === 'active' ? 'Active' : 'Inactive'}\n                </span>\n              </div>\n              \n              <div className=\"space-y-2 mb-6\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-text-secondary\">Emails:</span>\n                  <span>\n                    {automation.workflow.nodes.filter(node => node.type === 'sendEmail').length}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-text-secondary\">Trigger:</span>\n                  <span className=\"capitalize\">\n                    {automation.workflow.trigger === 'newContact' ? 'New Contact' : 'Manual'}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-text-secondary\">Created:</span>\n                  <span>{automation.created}</span>\n                </div>\n              </div>\n              \n              <div className=\"flex space-x-2\">\n                <Button variant=\"secondary\" size=\"sm\" onClick={() => toggleAutomationStatus(automation.id)}>\n                  {automation.status === 'active' ? 'Deactivate' : 'Activate'}\n                </Button>\n                <Button size=\"sm\" onClick={() => handleEditClick(automation)}>\n                  Edit\n                </Button>\n              </div>\n            </Card>\n          ))}\n        </div>\n      )}\n      \n      {/* Create Automation Modal */}\n      {showCreateModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\">\n            <h2 className=\"text-xl font-semibold mb-4\">Create Automation</h2>\n            \n            {error && (\n              <Alert\n                type=\"error\"\n                message={error}\n                onClose={() => setError('')}\n                className=\"mb-4\"\n              />\n            )}\n            \n            <div className=\"mb-4\">\n              <Input\n                id=\"automationName\"\n                name=\"automationName\"\n                label=\"Automation Name\"\n                value={newAutomationName}\n                onChange={(e) => setNewAutomationName(e.target.value)}\n                required\n              />\n            </div>\n            \n            <div className=\"mb-6\">\n              <label className=\"form-label\">Trigger</label>\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    id=\"triggerManual\"\n                    name=\"trigger\"\n                    value=\"manual\"\n                    checked={newAutomationTrigger === 'manual'}\n                    onChange={() => setNewAutomationTrigger('manual')}\n                    className=\"mr-2\"\n                  />\n                  <label htmlFor=\"triggerManual\">Manual (start sequence manually)</label>\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"radio\"\n                    id=\"triggerNewContact\"\n                    name=\"trigger\"\n                    value=\"newContact\"\n                    checked={newAutomationTrigger === 'newContact'}\n                    onChange={() => setNewAutomationTrigger('newContact')}\n                    className=\"mr-2\"\n                  />\n                  <label htmlFor=\"triggerNewContact\">New Contact (start when a new contact is added)</label>\n                </div>\n              </div>\n            </div>\n\n            {/* Workflow Steps Builder */}\n            <div className=\"mb-6\"><hr className=\"my-4 border-gray-600\"/>\n              <h3 className=\"text-lg font-medium mb-3\">Workflow Steps</h3>\n              <div className=\"space-y-3 mb-4\"><hr className=\"my-4 border-gray-600\"/>\n                {newAutomationNodes.length === 0 ? (\n                  <p className=\"text-text-secondary text-center\">(No steps added yet)</p>\n                ) : (\n                  newAutomationNodes.map((node, index) => (\n                    <div key={node.id} className=\"p-3 bg-primary rounded border border-gray-600 flex justify-between items-center\"><hr className=\"my-4 border-gray-600\"/>\n                      <div>\n                        <span className=\"font-medium\">Step {index + 1}: </span>\n                        {node.type === 'sendEmail' && (\n                          <span className=\"flex items-center space-x-2\"><hr className=\"my-4 border-gray-600\"/>\n                            <span>Send Email:</span>\n                            <Button \n                              variant=\"secondary\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectingForNodeId(node.id);\n                                setShowTemplateModal(true);\n                              }}\n                            >\n                              {/* Find template name from availableTemplates or show placeholder */}\n                              { (node as WorkflowSendEmailNode).templateId \n                                ? availableTemplates.find(t => t.id === (node as WorkflowSendEmailNode).templateId)?.name || 'Invalid Template'\n                                : 'Select Template'\n                              }\n                            </Button>\n                          </span>\n                        )}\n                        {node.type === 'delay' && (\n                          <span className=\"flex items-center space-x-2\"><hr className=\"my-4 border-gray-600\"/>\n                            <span>Wait for</span>\n                            <Input\n                              id={`duration-${node.id}`}\n                              name={`duration-${node.id}`}\n                              type=\"number\"\n                              value={String((node as WorkflowDelayNode).duration)}\n                              onChange={(e) => {\n                                const newDuration = parseInt(e.target.value, 10) || 1;\n                                // Ensure duration is at least 1\n                                updateNodeInWorkflow(node.id, { duration: Math.max(1, newDuration) })\n                              }}\n                              className=\"w-16 p-1 text-center\"\n                            />\n                            <select\n                              value={(node as WorkflowDelayNode).unit}\n                              onChange={(e) => updateNodeInWorkflow(node.id, { unit: e.target.value as WorkflowDelayNode['unit'] })}\n                              className=\"p-1 bg-secondary-bg border border-gray-500 rounded\"\n                            >\n                              <option value=\"minutes\">Minutes</option>\n                              <option value=\"hours\">Hours</option>\n                              <option value=\"days\">Days</option>\n                            </select>\n                          </span>\n                        )}\n                      </div>\n                      {/* Remove Node Button */}\n                      <Button \n                        variant=\"danger\" \n                        size=\"sm\" \n                        onClick={() => removeNodeFromWorkflow(node.id)}\n                        className=\"ml-2\"\n                      >\n                        &times; {/* Times symbol for Remove */}\n                      </Button>\n                    </div>\n                  ))\n                )}\n              </div>\n\n              {/* Add Step Button and Options */}\n              <div className=\"relative\"><hr className=\"my-4 border-gray-600\"/>\n                <Button \n                  variant=\"secondary\"\n                  className=\"w-full\" \n                  onClick={() => setShowAddStepOptions(!showAddStepOptions)}\n                >\n                  + Add Step\n                </Button>\n                {showAddStepOptions && (\n                  <div className=\"absolute left-0 right-0 mt-2 bg-secondary-bg border border-gray-600 rounded shadow-lg z-10\"><hr className=\"my-4 border-gray-600\"/>\n                    <ul>\n                      <li>\n                        <button \n                          onClick={() => addNodeToWorkflow('sendEmail')}\n                          className=\"block w-full text-left px-4 py-2 hover:bg-primary\"\n                        >\n                          Send Email\n                        </button>\n                      </li>\n                      <li>\n                        <button \n                          onClick={() => addNodeToWorkflow('delay')}\n                          className=\"block w-full text-left px-4 py-2 hover:bg-primary\"\n                        >\n                          Wait / Delay\n                        </button>\n                      </li>\n                      {/* // TODO: Add other node types here later (Condition, Tag, etc.) */}\n                    </ul>\n                  </div>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-2\">\n              <Button variant=\"secondary\" onClick={() => setShowCreateModal(false)}>\n                Cancel\n              </Button>\n              <Button onClick={handleCreateAutomation}>\n                Create\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Automation Modal */}\n      {showEditModal && editingAutomation && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\">\n            <h2 className=\"text-xl font-semibold mb-4\">Edit Automation: {editingAutomation.name}</h2>\n\n            {error && (\n              <Alert\n                type=\"error\"\n                message={error}\n                onClose={() => setError('')}\n                className=\"mb-4\"\n              />\n            )}\n\n            {/* TODO: Add form fields for editing automation details */}\n            <p className=\"text-center text-text-secondary my-8\">Edit form fields will go here.</p>\n\n            <div className=\"flex justify-end space-x-2\">\n              <Button variant=\"secondary\" onClick={() => setShowEditModal(false)}>\n                Cancel\n              </Button>\n              <Button /*onClick={handleUpdateAutomation}*/ > {/* TODO: Add update handler */}\n                Save Changes\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Template Selection Modal */}\n      {showTemplateModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-[60]\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-lg w-full max-h-[80vh] flex flex-col\">\n            <h2 className=\"text-xl font-semibold mb-4\">Select Email Template</h2>\n            \n            {templatesLoading && <p>Loading templates...</p>}\n            {templatesError && <Alert type=\"error\" message={templatesError} onClose={() => setTemplatesError(null)} />}\n\n            <div className=\"flex-grow overflow-y-auto mb-4\">\n              {!templatesLoading && !templatesError && (\n                <ul className=\"space-y-2\">\n                  {availableTemplates.length > 0 ? (\n                    availableTemplates.map(template => (\n                      <li key={template.id}>\n                        <button \n                          className=\"w-full text-left p-3 hover:bg-primary rounded border border-gray-700\" \n                          onClick={() => {\n                            if (selectingForNodeId) {\n                              // Update the node with the selected template ID\n                              updateNodeInWorkflow(selectingForNodeId, { templateId: template.id });\n                            }\n                            setShowTemplateModal(false);\n                            setSelectingForNodeId(null);\n                          }}\n                        >\n                          {template.name} <span className=\"text-xs text-text-secondary\">(ID: {template.id})</span>\n                        </button>\n                      </li>\n                    ))\n                  ) : (\n                    <p className=\"text-text-secondary text-center\">(No templates found)</p>\n                  )}\n                </ul>\n              )}\n            </div>\n            \n            <div className=\"flex justify-end\">\n              <Button \n                variant=\"secondary\" \n                onClick={() => {\n                  setShowTemplateModal(false);\n                  setSelectingForNodeId(null);\n                }}\n              >\n                Cancel\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n    // </Layout>\n  );\n};\n\nexport default Automations;\n"], "names": ["randomUUID", "crypto", "bind", "getRandomValues", "rnds8", "Uint8Array", "byteToHex", "i", "push", "toString", "slice", "unsafeStringify", "arr", "offset", "arguments", "length", "undefined", "toLowerCase", "options", "buf", "native", "rnds", "random", "rng", "Error", "RangeError", "Automations", "automations", "setAutomations", "useState", "showCreateModal", "setShowCreateModal", "newAutomationName", "setNewAutomationName", "newAutomationTrigger", "setNewAutomationTrigger", "newAutomationNodes", "setNewAutomationNodes", "showAddStepOptions", "setShowAddStepOptions", "availableTemplates", "setAvailableTemplates", "templatesLoading", "setTemplatesLoading", "templatesError", "setTemplatesError", "showTemplateModal", "setShowTemplateModal", "selectingForNodeId", "setSelectingForNodeId", "error", "setError", "success", "setSuccess", "editingAutomation", "setEditingAutomation", "showEditModal", "setShowEditModal", "useEffect", "async", "response", "templateRecommendationService", "getAllTemplates", "data", "formattedTemplates", "map", "tmpl", "id", "_id", "name", "templateName", "console", "warn", "err", "_response", "_response$data", "errorMessage", "message", "fetchTemplates", "addNodeToWorkflow", "type", "newNode", "nodeId", "uuidv4", "templateId", "duration", "unit", "updateNodeInWorkflow", "updates", "prevNodes", "node", "_jsxs", "_Fragment", "children", "className", "_jsx", "<PERSON><PERSON>", "onClick", "<PERSON><PERSON>", "onClose", "Card", "automation", "status", "workflow", "nodes", "filter", "trigger", "created", "variant", "size", "toggleAutomationStatus", "handleEditClick", "Input", "label", "value", "onChange", "e", "target", "required", "checked", "htmlFor", "index", "_availableTemplates$f", "find", "t", "String", "newDuration", "parseInt", "Math", "max", "removeNodeFromWorkflow", "handleCreateAutomation", "newAutomationObject", "a", "Date", "toISOString", "split", "setTimeout", "template"], "sourceRoot": ""}