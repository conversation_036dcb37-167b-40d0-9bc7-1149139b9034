{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\n// Placeholder Icon component - Replace with actual icons\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Icon = ({\n  name,\n  className\n}) => /*#__PURE__*/_jsxDEV(\"i\", {\n  className: `placeholder-icon-${name} inline-block w-5 h-5 ${className || ''}`,\n  \"aria-hidden\": \"true\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 12,\n  columnNumber: 3\n}, this);\n_c = Icon;\nconst Sidebar = () => {\n  _s();\n  var _user$name, _user$name$charAt;\n  const {\n    pathname\n  } = useLocation();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);\n\n  // --- Navigation Items --- \n  const mainNavItems = [{\n    path: '/',\n    name: 'Dashboard',\n    icon: 'home'\n  }, {\n    path: '/campaigns',\n    name: 'Campaigns',\n    icon: 'mail'\n  }, {\n    path: '/email-templates',\n    name: 'Templates',\n    icon: 'template'\n  }, {\n    path: '/contacts',\n    name: 'Contacts',\n    icon: 'users'\n  }, {\n    path: '/analytics',\n    name: 'Analytics',\n    icon: 'chart-pie'\n  }, {\n    path: '/automations',\n    name: 'Automations',\n    icon: 'arrows-circuit'\n  }, {\n    path: '/settings',\n    name: 'Settings',\n    icon: 'cog'\n  }, {\n    path: '/billing',\n    name: 'Billing',\n    icon: 'credit-card'\n  }, {\n    path: '/support',\n    name: 'Help & Support',\n    icon: 'question-mark-circle'\n  }];\n  const advancedNavItems = [{\n    path: '/ai-content-generator',\n    name: 'AI Content',\n    icon: 'sparkles'\n  }, {\n    path: '/personalization-editor',\n    name: 'Personalization',\n    icon: 'pencil-alt'\n  }, {\n    path: '/interactive-elements',\n    name: 'Interactive',\n    icon: 'cursor-click'\n  }, {\n    path: '/send-time-optimization',\n    name: 'Send Time Opt.',\n    icon: 'clock'\n  }, {\n    path: '/ab-testing',\n    name: 'A/B Testing',\n    icon: 'beaker'\n  }, {\n    path: '/segment-builder',\n    name: 'Segments',\n    icon: 'view-boards'\n  }, {\n    path: '/deliverability-dashboard',\n    name: 'Deliverability',\n    icon: 'shield-check'\n  }, {\n    path: '/mobile-preview',\n    name: 'Mobile Preview',\n    icon: 'device-mobile'\n  }, {\n    path: '/scheduling-automation',\n    name: 'Scheduling',\n    icon: 'calendar'\n  }, {\n    path: '/journey-builder',\n    name: 'Journey Builder',\n    icon: 'map'\n  }, {\n    path: '/integration-marketplace',\n    name: 'Integrations',\n    icon: 'puzzle'\n  }, {\n    path: '/template-recommendations',\n    name: 'Recommendations',\n    icon: 'light-bulb'\n  }, {\n    path: '/data-export-import',\n    name: 'Data Export/Import',\n    icon: 'database'\n  }];\n  // ------------------------\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n  const renderNavLink = item => {\n    const isActive = item.path === '/' ? pathname === '/' : pathname.startsWith(item.path);\n\n    // Futuristic Nav Link Styles\n    const baseClasses = \"flex items-center px-3 py-2 rounded-md transition-colors duration-150 ease-in-out group text-sm font-medium relative\";\n    // Active: Use accent coral text and subtle dark background\n    const activeClasses = \"bg-neutral-light text-accent-coral font-semibold shadow-inner shadow-black/30\";\n    // Inactive: Use primary text color, slightly lighter dark bg hover\n    const inactiveClasses = \"text-text-primary hover:text-white hover:bg-neutral-light\";\n    return /*#__PURE__*/_jsxDEV(Link, {\n      to: item.path,\n      className: `${baseClasses} ${isActive ? activeClasses : inactiveClasses}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: `mr-3 flex-shrink-0 w-5 h-5 ${isActive ? 'text-accent-coral' : 'text-text-secondary group-hover:text-text-primary'}`,\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          name: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 12\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        title: item.name,\n        children: item.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), isActive && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"absolute left-0 top-0 bottom-0 w-1 bg-accent-coral rounded-r-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 22\n      }, this)]\n    }, item.path, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  };\n  return (\n    /*#__PURE__*/\n    // Main Sidebar Container: Charcoal background, subtle border\n    _jsxDEV(\"div\", {\n      className: \"h-screen w-64 bg-neutral-base text-text-primary flex flex-col border-r border-border shadow-lg z-20 font-sans\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pl-2 pr-4 border-b border-border h-16 flex pt-2\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"flex items-center group\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-primary-blue group-hover:text-accent-coral transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"paper-airplane\",\n              className: \"w-8 h-8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 14\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 12\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold ml-3 /* Increased text size */ bg-gradient-to-r from-primary-blue to-accent-coral  bg-clip-text text-transparent  shadow-md /* Added subtle shadow */ group-hover:scale-105 group-hover:tracking-wide /* Added hover tracking */ transition-all duration-200 ease-in-out\" /* Use transition-all */,\n            children: \"Driftly\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 12\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-neutral-light hover:scrollbar-thumb-border scrollbar-track-transparent scrollbar-thumb-rounded\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"px-3 space-y-1\",\n          children: [mainNavItems.map(renderNavLink), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsAdvancedOpen(!isAdvancedOpen),\n            className: \"flex items-center justify-between w-full px-3 py-2 text-text-primary hover:text-white hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-left mt-4 text-sm font-medium group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-3 text-text-secondary group-hover:text-text-primary\",\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"view-grid-add\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Advanced Features\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: `w-5 h-5 transition-transform text-text-secondary group-hover:text-text-primary ${isAdvancedOpen ? 'transform rotate-180' : ''}`,\n              xmlns: \"http://www.w3.org/2000/svg\",\n              viewBox: \"0 0 20 20\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                fillRule: \"evenodd\",\n                d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n                clipRule: \"evenodd\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 11\n          }, this), isAdvancedOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pl-4 mt-1 ml-3 py-2 text-sm text-text-secondary italic\",\n            children: \"Coming soon...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 border-t border-border\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-9 h-9 rounded-full bg-primary-blue flex items-center justify-center text-sm font-medium text-white\",\n            children: (user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$charAt = _user$name.charAt(0)) === null || _user$name$charAt === void 0 ? void 0 : _user$name$charAt.toUpperCase()) || 'U'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-2 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-text-primary truncate\",\n              children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-text-secondary truncate\",\n              children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"w-full flex items-center px-3 py-2 text-text-secondary hover:text-text-primary hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-sm font-medium group\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-3 text-text-secondary group-hover:text-text-primary\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 5\n    }, this)\n  );\n};\n_s(Sidebar, \"e1VEaWUws2c54liF34mzoFwQLls=\", false, function () {\n  return [useLocation, useAuth];\n});\n_c2 = Sidebar;\nexport default Sidebar;\nvar _c, _c2;\n$RefreshReg$(_c, \"Icon\");\n$RefreshReg$(_c2, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Icon", "name", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Sidebar", "_s", "_user$name", "_user$name$charAt", "pathname", "user", "logout", "isAdvancedOpen", "setIsAdvancedOpen", "mainNavItems", "path", "icon", "advancedNavItems", "handleLogout", "error", "console", "renderNavLink", "item", "isActive", "startsWith", "baseClasses", "activeClasses", "inactiveClasses", "to", "children", "title", "map", "onClick", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "char<PERSON>t", "toUpperCase", "email", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/Sidebar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport {\n  Link,\n  useLocation,\n} from 'react-router-dom';\n\nimport { useAuth } from '../contexts/AuthContext';\n\n// Placeholder Icon component - Replace with actual icons\nconst Icon = ({ name, className }: { name: string; className?: string }) => (\n  <i className={`placeholder-icon-${name} inline-block w-5 h-5 ${className || ''}`} aria-hidden=\"true\"></i>\n);\n\nconst Sidebar: React.FC = () => {\n  const { pathname } = useLocation();\n  const { user, logout } = useAuth();\n  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);\n\n  // --- Navigation Items --- \n  const mainNavItems = [\n    { path: '/', name: 'Dashboard', icon: 'home' }, \n    { path: '/campaigns', name: 'Campaigns', icon: 'mail' },\n    { path: '/email-templates', name: 'Templates', icon: 'template' },\n    { path: '/contacts', name: 'Contacts', icon: 'users' },\n    { path: '/analytics', name: 'Analytics', icon: 'chart-pie' },\n    { path: '/automations', name: 'Automations', icon: 'arrows-circuit' }, \n    { path: '/settings', name: 'Settings', icon: 'cog' },             \n    { path: '/billing', name: 'Billing', icon: 'credit-card' },     \n    { path: '/support', name: 'Help & Support', icon: 'question-mark-circle' }, \n  ];\n\n  const advancedNavItems = [\n    { path: '/ai-content-generator', name: 'AI Content', icon: 'sparkles' }, \n    { path: '/personalization-editor', name: 'Personalization', icon: 'pencil-alt' }, \n    { path: '/interactive-elements', name: 'Interactive', icon: 'cursor-click' }, \n    { path: '/send-time-optimization', name: 'Send Time Opt.', icon: 'clock' }, \n    { path: '/ab-testing', name: 'A/B Testing', icon: 'beaker' }, \n    { path: '/segment-builder', name: 'Segments', icon: 'view-boards' }, \n    { path: '/deliverability-dashboard', name: 'Deliverability', icon: 'shield-check' }, \n    { path: '/mobile-preview', name: 'Mobile Preview', icon: 'device-mobile' }, \n    { path: '/scheduling-automation', name: 'Scheduling', icon: 'calendar' }, \n    { path: '/journey-builder', name: 'Journey Builder', icon: 'map' },\n    { path: '/integration-marketplace', name: 'Integrations', icon: 'puzzle' },\n    { path: '/template-recommendations', name: 'Recommendations', icon: 'light-bulb' },\n    { path: '/data-export-import', name: 'Data Export/Import', icon: 'database' },\n  ];\n  // ------------------------\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const renderNavLink = (item: any) => {\n    const isActive = item.path === '/'\n      ? pathname === '/' \n      : pathname.startsWith(item.path);\n      \n    // Futuristic Nav Link Styles\n    const baseClasses = \"flex items-center px-3 py-2 rounded-md transition-colors duration-150 ease-in-out group text-sm font-medium relative\";\n    // Active: Use accent coral text and subtle dark background\n    const activeClasses = \"bg-neutral-light text-accent-coral font-semibold shadow-inner shadow-black/30\"; \n    // Inactive: Use primary text color, slightly lighter dark bg hover\n    const inactiveClasses = \"text-text-primary hover:text-white hover:bg-neutral-light\";\n\n    return (\n      <Link\n        key={item.path}\n        to={item.path}\n        className={`${baseClasses} ${isActive ? activeClasses : inactiveClasses}`}\n      >\n        {/* Icon: Coral when active, light gray otherwise */}\n        <span className={`mr-3 flex-shrink-0 w-5 h-5 ${isActive ? 'text-accent-coral' : 'text-text-secondary group-hover:text-text-primary'}`}>\n           <Icon name={item.icon} />\n        </span>\n        <span title={item.name}>{item.name}</span> \n        {/* Optional: Add subtle glow/marker for active state */} \n        {isActive && <span className=\"absolute left-0 top-0 bottom-0 w-1 bg-accent-coral rounded-r-full\"></span>}\n      </Link>\n    );\n  };\n\n  return (\n    // Main Sidebar Container: Charcoal background, subtle border\n    <div className=\"h-screen w-64 bg-neutral-base text-text-primary flex flex-col border-r border-border shadow-lg z-20 font-sans\">\n      {/* Header: Align left, adjust padding, increase size */}\n      <div className=\"pl-2 pr-4 border-b border-border h-16 flex pt-2\">\n        <Link to=\"/\" className=\"flex items-center group\">\n           {/* Removed Logo Image Placeholder */}\n           <span className=\"text-primary-blue group-hover:text-accent-coral transition-colors\">\n             <Icon name=\"paper-airplane\" className=\"w-8 h-8\" /> {/* Increased icon size */}\n           </span>\n           <h1 \n              className=\"text-3xl font-bold ml-3 /* Increased text size */\n                         bg-gradient-to-r from-primary-blue to-accent-coral \n                         bg-clip-text text-transparent \n                         shadow-md /* Added subtle shadow */\n                         group-hover:scale-105 group-hover:tracking-wide /* Added hover tracking */\n                         transition-all duration-200 ease-in-out\" /* Use transition-all */\n           >\n              Driftly\n           </h1>\n        </Link>\n      </div>\n      \n      {/* Navigation Area */}\n      <div className=\"flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-neutral-light hover:scrollbar-thumb-border scrollbar-track-transparent scrollbar-thumb-rounded\">\n        <nav className=\"px-3 space-y-1\">\n          {mainNavItems.map(renderNavLink)}\n\n          {/* Advanced Features Toggle - Change base color, remove icon hover effects */}\n          <button\n            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}\n            className=\"flex items-center justify-between w-full px-3 py-2 text-text-primary hover:text-white hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-left mt-4 text-sm font-medium group\"\n          >\n            <div className=\"flex items-center\">\n              {/* Change icon color to match inactive nav link icon */}\n              <span className=\"mr-3 text-text-secondary group-hover:text-text-primary\">\n                <Icon name=\"view-grid-add\" />\n              </span>\n              <span>Advanced Features</span>\n            </div>\n             {/* Change icon color to match inactive nav link icon */}\n            <svg \n              className={`w-5 h-5 transition-transform text-text-secondary group-hover:text-text-primary ${isAdvancedOpen ? 'transform rotate-180' : ''}`}\n              xmlns=\"http://www.w3.org/2000/svg\" \n              viewBox=\"0 0 20 20\" \n              fill=\"currentColor\"\n            >\n              <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n\n          {/* Advanced Features List - Replace with Coming Soon */}\n          {isAdvancedOpen && (\n            <div className=\"pl-4 mt-1 ml-3 py-2 text-sm text-text-secondary italic\">\n              Coming soon...\n            </div>\n          )}\n        </nav>\n      </div>\n      \n      {/* User/Logout Section */} \n      <div className=\"p-3 border-t border-border\">\n        <div className=\"flex items-center mb-3\">\n          {/* Avatar: Use primary blue or subtle gray? */}\n          <div className=\"w-9 h-9 rounded-full bg-primary-blue flex items-center justify-center text-sm font-medium text-white\">\n              {user?.name?.charAt(0)?.toUpperCase() || 'U'}\n          </div>\n          <div className=\"ml-2 overflow-hidden\">\n            <p className=\"text-sm font-medium text-text-primary truncate\">{user?.name || 'User'}</p>\n            <p className=\"text-xs text-text-secondary truncate\">{user?.email || '<EMAIL>'}</p>\n          </div>\n        </div>\n        {/* Logout Button */} \n        <button\n          onClick={handleLogout}\n          className=\"w-full flex items-center px-3 py-2 text-text-secondary hover:text-text-primary hover:bg-neutral-light rounded-md transition-colors duration-150 ease-in-out text-sm font-medium group\"\n        >\n          <span className=\"mr-3 text-text-secondary group-hover:text-text-primary\">\n            <Icon name=\"logout\" />\n          </span>\n          <span>Logout</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,SACEC,IAAI,EACJC,WAAW,QACN,kBAAkB;AAEzB,SAASC,OAAO,QAAQ,yBAAyB;;AAEjD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,IAAI,GAAGA,CAAC;EAAEC,IAAI;EAAEC;AAAgD,CAAC,kBACrEH,OAAA;EAAGG,SAAS,EAAE,oBAAoBD,IAAI,yBAAyBC,SAAS,IAAI,EAAE,EAAG;EAAC,eAAY;AAAM;EAAAC,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAI,CACzG;AAACC,EAAA,GAFIP,IAAI;AAIV,MAAMQ,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,iBAAA;EAC9B,MAAM;IAAEC;EAAS,CAAC,GAAGhB,WAAW,CAAC,CAAC;EAClC,MAAM;IAAEiB,IAAI;IAAEC;EAAO,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAClC,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;;EAE3D;EACA,MAAMuB,YAAY,GAAG,CACnB;IAAEC,IAAI,EAAE,GAAG;IAAEjB,IAAI,EAAE,WAAW;IAAEkB,IAAI,EAAE;EAAO,CAAC,EAC9C;IAAED,IAAI,EAAE,YAAY;IAAEjB,IAAI,EAAE,WAAW;IAAEkB,IAAI,EAAE;EAAO,CAAC,EACvD;IAAED,IAAI,EAAE,kBAAkB;IAAEjB,IAAI,EAAE,WAAW;IAAEkB,IAAI,EAAE;EAAW,CAAC,EACjE;IAAED,IAAI,EAAE,WAAW;IAAEjB,IAAI,EAAE,UAAU;IAAEkB,IAAI,EAAE;EAAQ,CAAC,EACtD;IAAED,IAAI,EAAE,YAAY;IAAEjB,IAAI,EAAE,WAAW;IAAEkB,IAAI,EAAE;EAAY,CAAC,EAC5D;IAAED,IAAI,EAAE,cAAc;IAAEjB,IAAI,EAAE,aAAa;IAAEkB,IAAI,EAAE;EAAiB,CAAC,EACrE;IAAED,IAAI,EAAE,WAAW;IAAEjB,IAAI,EAAE,UAAU;IAAEkB,IAAI,EAAE;EAAM,CAAC,EACpD;IAAED,IAAI,EAAE,UAAU;IAAEjB,IAAI,EAAE,SAAS;IAAEkB,IAAI,EAAE;EAAc,CAAC,EAC1D;IAAED,IAAI,EAAE,UAAU;IAAEjB,IAAI,EAAE,gBAAgB;IAAEkB,IAAI,EAAE;EAAuB,CAAC,CAC3E;EAED,MAAMC,gBAAgB,GAAG,CACvB;IAAEF,IAAI,EAAE,uBAAuB;IAAEjB,IAAI,EAAE,YAAY;IAAEkB,IAAI,EAAE;EAAW,CAAC,EACvE;IAAED,IAAI,EAAE,yBAAyB;IAAEjB,IAAI,EAAE,iBAAiB;IAAEkB,IAAI,EAAE;EAAa,CAAC,EAChF;IAAED,IAAI,EAAE,uBAAuB;IAAEjB,IAAI,EAAE,aAAa;IAAEkB,IAAI,EAAE;EAAe,CAAC,EAC5E;IAAED,IAAI,EAAE,yBAAyB;IAAEjB,IAAI,EAAE,gBAAgB;IAAEkB,IAAI,EAAE;EAAQ,CAAC,EAC1E;IAAED,IAAI,EAAE,aAAa;IAAEjB,IAAI,EAAE,aAAa;IAAEkB,IAAI,EAAE;EAAS,CAAC,EAC5D;IAAED,IAAI,EAAE,kBAAkB;IAAEjB,IAAI,EAAE,UAAU;IAAEkB,IAAI,EAAE;EAAc,CAAC,EACnE;IAAED,IAAI,EAAE,2BAA2B;IAAEjB,IAAI,EAAE,gBAAgB;IAAEkB,IAAI,EAAE;EAAe,CAAC,EACnF;IAAED,IAAI,EAAE,iBAAiB;IAAEjB,IAAI,EAAE,gBAAgB;IAAEkB,IAAI,EAAE;EAAgB,CAAC,EAC1E;IAAED,IAAI,EAAE,wBAAwB;IAAEjB,IAAI,EAAE,YAAY;IAAEkB,IAAI,EAAE;EAAW,CAAC,EACxE;IAAED,IAAI,EAAE,kBAAkB;IAAEjB,IAAI,EAAE,iBAAiB;IAAEkB,IAAI,EAAE;EAAM,CAAC,EAClE;IAAED,IAAI,EAAE,0BAA0B;IAAEjB,IAAI,EAAE,cAAc;IAAEkB,IAAI,EAAE;EAAS,CAAC,EAC1E;IAAED,IAAI,EAAE,2BAA2B;IAAEjB,IAAI,EAAE,iBAAiB;IAAEkB,IAAI,EAAE;EAAa,CAAC,EAClF;IAAED,IAAI,EAAE,qBAAqB;IAAEjB,IAAI,EAAE,oBAAoB;IAAEkB,IAAI,EAAE;EAAW,CAAC,CAC9E;EACD;;EAEA,MAAME,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMP,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,MAAME,aAAa,GAAIC,IAAS,IAAK;IACnC,MAAMC,QAAQ,GAAGD,IAAI,CAACP,IAAI,KAAK,GAAG,GAC9BN,QAAQ,KAAK,GAAG,GAChBA,QAAQ,CAACe,UAAU,CAACF,IAAI,CAACP,IAAI,CAAC;;IAElC;IACA,MAAMU,WAAW,GAAG,sHAAsH;IAC1I;IACA,MAAMC,aAAa,GAAG,+EAA+E;IACrG;IACA,MAAMC,eAAe,GAAG,2DAA2D;IAEnF,oBACE/B,OAAA,CAACJ,IAAI;MAEHoC,EAAE,EAAEN,IAAI,CAACP,IAAK;MACdhB,SAAS,EAAE,GAAG0B,WAAW,IAAIF,QAAQ,GAAGG,aAAa,GAAGC,eAAe,EAAG;MAAAE,QAAA,gBAG1EjC,OAAA;QAAMG,SAAS,EAAE,8BAA8BwB,QAAQ,GAAG,mBAAmB,GAAG,mDAAmD,EAAG;QAAAM,QAAA,eACnIjC,OAAA,CAACC,IAAI;UAACC,IAAI,EAAEwB,IAAI,CAACN;QAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACPP,OAAA;QAAMkC,KAAK,EAAER,IAAI,CAACxB,IAAK;QAAA+B,QAAA,EAAEP,IAAI,CAACxB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EAEzCoB,QAAQ,iBAAI3B,OAAA;QAAMG,SAAS,EAAC;MAAmE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA,GAVnGmB,IAAI,CAACP,IAAI;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAWV,CAAC;EAEX,CAAC;EAED;IAAA;IACE;IACAP,OAAA;MAAKG,SAAS,EAAC,+GAA+G;MAAA8B,QAAA,gBAE5HjC,OAAA;QAAKG,SAAS,EAAC,iDAAiD;QAAA8B,QAAA,eAC9DjC,OAAA,CAACJ,IAAI;UAACoC,EAAE,EAAC,GAAG;UAAC7B,SAAS,EAAC,yBAAyB;UAAA8B,QAAA,gBAE7CjC,OAAA;YAAMG,SAAS,EAAC,mEAAmE;YAAA8B,QAAA,gBACjFjC,OAAA,CAACC,IAAI;cAACC,IAAI,EAAC,gBAAgB;cAACC,SAAS,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,KAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACPP,OAAA;YACGG,SAAS,EAAC,6RAKyC,CAAC;YAAA8B,QAAA,EACtD;UAED;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNP,OAAA;QAAKG,SAAS,EAAC,2JAA2J;QAAA8B,QAAA,eACxKjC,OAAA;UAAKG,SAAS,EAAC,gBAAgB;UAAA8B,QAAA,GAC5Bf,YAAY,CAACiB,GAAG,CAACV,aAAa,CAAC,eAGhCzB,OAAA;YACEoC,OAAO,EAAEA,CAAA,KAAMnB,iBAAiB,CAAC,CAACD,cAAc,CAAE;YAClDb,SAAS,EAAC,6MAA6M;YAAA8B,QAAA,gBAEvNjC,OAAA;cAAKG,SAAS,EAAC,mBAAmB;cAAA8B,QAAA,gBAEhCjC,OAAA;gBAAMG,SAAS,EAAC,wDAAwD;gBAAA8B,QAAA,eACtEjC,OAAA,CAACC,IAAI;kBAACC,IAAI,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACPP,OAAA;gBAAAiC,QAAA,EAAM;cAAiB;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eAENP,OAAA;cACEG,SAAS,EAAE,kFAAkFa,cAAc,GAAG,sBAAsB,GAAG,EAAE,EAAG;cAC5IqB,KAAK,EAAC,4BAA4B;cAClCC,OAAO,EAAC,WAAW;cACnBC,IAAI,EAAC,cAAc;cAAAN,QAAA,eAEnBjC,OAAA;gBAAMwC,QAAQ,EAAC,SAAS;gBAACC,CAAC,EAAC,oHAAoH;gBAACC,QAAQ,EAAC;cAAS;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,EAGRS,cAAc,iBACbhB,OAAA;YAAKG,SAAS,EAAC,wDAAwD;YAAA8B,QAAA,EAAC;UAExE;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNP,OAAA;QAAKG,SAAS,EAAC,4BAA4B;QAAA8B,QAAA,gBACzCjC,OAAA;UAAKG,SAAS,EAAC,wBAAwB;UAAA8B,QAAA,gBAErCjC,OAAA;YAAKG,SAAS,EAAC,sGAAsG;YAAA8B,QAAA,EAChH,CAAAnB,IAAI,aAAJA,IAAI,wBAAAH,UAAA,GAAJG,IAAI,CAAEZ,IAAI,cAAAS,UAAA,wBAAAC,iBAAA,GAAVD,UAAA,CAAYgC,MAAM,CAAC,CAAC,CAAC,cAAA/B,iBAAA,uBAArBA,iBAAA,CAAuBgC,WAAW,CAAC,CAAC,KAAI;UAAG;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNP,OAAA;YAAKG,SAAS,EAAC,sBAAsB;YAAA8B,QAAA,gBACnCjC,OAAA;cAAGG,SAAS,EAAC,gDAAgD;cAAA8B,QAAA,EAAE,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEZ,IAAI,KAAI;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxFP,OAAA;cAAGG,SAAS,EAAC,sCAAsC;cAAA8B,QAAA,EAAE,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,KAAK,KAAI;YAAkB;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENP,OAAA;UACEoC,OAAO,EAAEd,YAAa;UACtBnB,SAAS,EAAC,uLAAuL;UAAA8B,QAAA,gBAEjMjC,OAAA;YAAMG,SAAS,EAAC,wDAAwD;YAAA8B,QAAA,eACtEjC,OAAA,CAACC,IAAI;cAACC,IAAI,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACPP,OAAA;YAAAiC,QAAA,EAAM;UAAM;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;AAEV,CAAC;AAACG,EAAA,CA7JID,OAAiB;EAAA,QACAZ,WAAW,EACPC,OAAO;AAAA;AAAAgD,GAAA,GAF5BrC,OAAiB;AA+JvB,eAAeA,OAAO;AAAC,IAAAD,EAAA,EAAAsC,GAAA;AAAAC,YAAA,CAAAvC,EAAA;AAAAuC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}