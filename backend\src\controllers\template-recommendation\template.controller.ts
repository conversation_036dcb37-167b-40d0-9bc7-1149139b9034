import {
  NextFunction,
  Request,
  Response,
} from 'express';

// import { Document, Types } from 'mongoose'; // Removed explicit Mongoose imports
import TemplateModel
  from '../../models/template.model'; // Use the main template model
import { createError } from '../../utils/error.util';

// import { ITemplate } from '../../types'; // Removed import from central types

// Get template recommendations
export const getTemplateRecommendations = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { context, preferences } = req.body;
    // ** Placeholder for recommendation logic **
    // 1. Analyze context (e.g., campaign goal, audience segment)
    // 2. Consider user preferences (e.g., industry, style)
    // 3. Query template database/performance data
    // 4. Return ranked list of suitable templates
    const recommendations = await TemplateModel.find({ // Use renamed model
        // Example query: find system templates in a relevant category
        isSystem: true, // Assuming model has isSystem
        category: context?.category || 'Promotional' 
    }).limit(5);

    res.status(200).json({ success: true, data: recommendations });

  } catch (error: any) {
    console.error('Error getting template recommendations:', error);
    next(createError(500, (error as Error).message || 'Failed to get recommendations'));
  }
};

// Create template
export const createTemplate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, description, content, category, isSystem = false } = req.body;
    const userId = req.user?.id;

    // Only allow admins to create system templates
    // if (isSystem && req.user?.role !== 'admin') { 
    //   return next(createError(403, 'Only admins can create system templates'));
    // }

    const newTemplateData = {
      name,
      description,
      content,
      category,
      userId: isSystem ? undefined : userId,
      isSystem,
      performanceMetrics: { // Initialize performance metrics
        opens: 0,
        clicks: 0,
        conversions: 0,
        usageCount: 0
      }
    };

    const newTemplate = new TemplateModel(newTemplateData); // Use renamed model

    await newTemplate.save();
    res.status(201).json({
      success: true,
      data: {
        id: newTemplate._id,
        name: newTemplate.name,
        category: newTemplate.category,
        createdAt: newTemplate.createdAt
      }
    });
  } catch (error: any) {
    console.error('Error creating template:', error);
    next(createError(500, (error as Error).message || 'Failed to create template'));
  }
};

// Get user's templates
export const getUserTemplates = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return next(createError(401, 'User not authenticated'));
    }

    const templates = await TemplateModel.find({ // Use renamed model
        $or: [ { userId: userId }, { isSystem: true } ]
    }).sort({ createdAt: -1 });
    
    res.status(200).json({
      success: true,
      data: templates.map((template: any) => ({
        id: template._id,
        name: template.name,
        description: template.description,
        category: template.category,
        thumbnail: template.thumbnail, 
        isSystem: template.isSystem,
        performance: template.performanceMetrics, // Use performanceMetrics
        createdAt: template.createdAt
      }))
    });
  } catch (error: any) {
    console.error('Error getting user templates:', error);
    next(createError(500, (error as Error).message || 'Failed to get templates'));
  }
};

// Get template by ID
export const getTemplateById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    console.log(`[Get Template] Received request for ID: ${id}`);

    // Add console.log for debugging
    console.log(`[Get Template] Finding template with ID: ${id}`);
    const template = await TemplateModel.findById(id); // Use renamed model
    console.log(`[Get Template] Result of findById(${id}):`, template ? {
      id: template._id,
      name: template.name,
      category: template.category,
      hasMjmlContent: !!template.mjmlContent,
      contentLength: template.content?.length || 0
    } : 'Not found');

    if (!template) {
      return next(createError(404, 'Template not found'));
    }

    // Allow access to system templates OR user's own templates
    if (!(template as any).isSystem && (template as any).userId?.toString() !== userId) {
       if (!userId) {
           return next(createError(401, 'User not authenticated'));
       }
       return next(createError(403, 'User not authorized to view this template'));
    }

    // Ensure the template object is sent directly under a 'template' key
    // matching the Frontend's TemplateDetailResponse type
    res.status(200).json({
      success: true,
      template: template // Send the fetched template object directly
    });
  } catch (error: any) {
    console.error('Error getting template by ID:', error);
    next(createError(500, (error as Error).message || 'Failed to get template'));
  }
};

// Get System Templates Grouped by Category
export const getSystemTemplatesCategorized = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Fetch all system templates
    const systemTemplates = await TemplateModel.find({ isSystem: true }).sort({ createdAt: -1 });

    // Group templates by category
    const categorizedTemplates = systemTemplates.reduce((acc: { [key: string]: any[] }, template) => {
      // Determine the category, default to 'other' if missing
      const category = template.category || 'other'; 
      if (!acc[category]) {
        acc[category] = [];
      }
      // Add relevant template data (adjust as needed for frontend display)
      acc[category].push({
        id: template._id,
        name: template.name,
        description: template.description,
        thumbnail: template.thumbnail, // Assuming thumbnail exists
        category: category, // Use the determined category
        isSystem: template.isSystem, // Also include isSystem flag
        createdAt: template.createdAt // Include createdAt if needed
        // Add other fields if necessary e.g., performance
      });
      return acc;
    }, {});

    res.status(200).json({
      success: true,
      data: categorizedTemplates,
    });

  } catch (error: any) {
    console.error('Error getting categorized system templates:', error);
    next(createError(500, (error as Error).message || 'Failed to get categorized templates'));
  }
};

// Update template by ID (or create copy if system template)
export const updateTemplate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const { name, description, content, category, thumbnail } = req.body;

    if (!userId) {
      return next(createError(401, 'User not authenticated'));
    }

    const originalTemplate = await TemplateModel.findById(id);

    if (!originalTemplate) {
      return next(createError(404, 'Template not found'));
    }

    // --- Logic for Copy-on-Edit --- 
    if ((originalTemplate as any).isSystem) {
      // It's a system template, create a new one for the user based on it
      console.log(`Creating copy of system template ${id} for user ${userId}`);
      const newTemplateData = {
        name: name || originalTemplate.name, // Use submitted name or original
        description: description || (originalTemplate as any).description,
        content: content, // Always use the submitted content from the editor
        category: category || originalTemplate.category,
        thumbnail: thumbnail || `https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name || originalTemplate.name)}`,
        userId: userId, // Assign to current user
        isSystem: false, // Mark as NOT a system template
        performanceMetrics: { // Reset performance for the user copy
          opens: 0,
          clicks: 0,
          conversions: 0,
          usageCount: 0
        },
        // Copy tags if needed, or start fresh
        tags: (originalTemplate as any).tags || [] 
      };
      
      const createdTemplate = await TemplateModel.create(newTemplateData);
      console.log(`Created new template ${createdTemplate._id} from system template ${id}`);

      res.status(201).json({
        success: true,
        message: 'Template copied and saved successfully',
        data: { // Return data about the NEW template
          id: createdTemplate._id,
          name: createdTemplate.name,
        }
      });

    } else {
      // It's a user's own template, update it directly
      // Ensure the user owns the template before updating
      if ((originalTemplate as any).userId?.toString() !== userId) {
        return next(createError(403, 'User not authorized to update this template'));
      }
      
      console.log(`Updating user template ${id}`);
      originalTemplate.set({
        name: name ?? originalTemplate.name,
        description: description ?? (originalTemplate as any).description,
        content: content ?? originalTemplate.content,
        category: category ?? originalTemplate.category,
        thumbnail: thumbnail ?? originalTemplate.thumbnail,
        // Ensure userId and isSystem remain unchanged for user templates
        userId: (originalTemplate as any).userId,
        isSystem: (originalTemplate as any).isSystem,
      });
      
      const updatedTemplate = await originalTemplate.save();
      console.log(`Updated template ${updatedTemplate._id}`);
      
      res.status(200).json({
        success: true,
        message: 'Template updated successfully',
        data: {
          id: updatedTemplate._id,
          name: updatedTemplate.name,
        }
      });
    }

  } catch (error: any) {
    console.error('Error updating/copying template:', error);
    // Handle potential validation errors specifically during creation/update
    if (error.name === 'ValidationError') {
        return next(createError(400, `Validation Error: ${error.message}`));
    }
    next(createError(500, (error as Error).message || 'Failed to update template'));
  }
};

// Update template performance
export const updateTemplatePerformance = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { opens, clicks, conversions, usageCount } = req.body;
    // Note: This endpoint might be called internally by campaign tracking, 
    // so auth might not be strictly required, or use a system token.
    // For now, let's assume it's protected.
    const userId = req.user?.id;
     if (!userId) {
       return next(createError(401, 'User not authenticated - How is performance being updated?'));
     }

    const template = await TemplateModel.findById(id); // Use renamed model
    if (!template) {
      return next(createError(404, 'Template not found'));
    }

    // Use 'as any' to access performanceMetrics
    const metrics = (template as any).performanceMetrics;

    // Check if performanceMetrics exists before updating
    if (!metrics) {
      // Initialize if it doesn't exist (should ideally be handled at creation)
      (template as any).performanceMetrics = { opens: 0, clicks: 0, conversions: 0, usageCount: 0 };
    } 

    // Update performance metrics using the metrics variable or directly with 'as any'
    if (opens !== undefined) (template as any).performanceMetrics.opens += opens;
    if (clicks !== undefined) (template as any).performanceMetrics.clicks += clicks;
    if (conversions !== undefined) (template as any).performanceMetrics.conversions += conversions;
    if (usageCount !== undefined) (template as any).performanceMetrics.usageCount += usageCount;

    await template.save();
    res.status(200).json({ success: true, message: 'Template performance updated' });

  } catch (error: any) {
    console.error('Error updating template performance:', error);
    next(createError(500, (error as Error).message || 'Failed to update performance'));
  }
};

// Delete a template
export const deleteTemplate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      return next(createError(401, 'User not authenticated'));
    }

    const template = await TemplateModel.findById(id);
    
    if (!template) {
      return next(createError(404, 'Template not found'));
    }

    // Only allow deletion of user's own templates, not system templates
    if ((template as any).isSystem) {
      return next(createError(403, 'System templates cannot be deleted'));
    }

    // Ensure the user owns the template before deleting
    if ((template as any).userId?.toString() !== userId) {
      return next(createError(403, 'User not authorized to delete this template'));
    }
    
    await TemplateModel.findByIdAndDelete(id);
    
    res.status(200).json({
      success: true,
      message: 'Template deleted successfully',
    });
  } catch (error: any) {
    console.error('Error deleting template:', error);
    next(createError(500, (error as Error).message || 'Failed to delete template'));
  }
};

