{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\EmailEditor.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Enhanced EmailEditor component for Driftly Email Generator\n * Provides advanced drag-and-drop interface for template creation and editing\n * Features: Real-time preview, responsive design, advanced block library, undo/redo\n */\n\nimport React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';\nimport mjml2html from 'mjml-browser';\nimport { useDrag, useDrop } from 'react-dnd';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport api from '../services/api'; // Corrected import path\n// Types - Adjust path as needed\n\nimport BlockEditor from './BlockEditor';\n// Components - Adjust paths as needed\nimport BlockLibrary from './BlockLibrary';\nimport EmailPreview from './EmailPreview';\nimport SaveTemplateModal from './SaveTemplateModal';\n\n// Styles - Ensure this is imported in your main application entry point\n// e.g., import '../styles/editor.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_URL = process.env.REACT_APP_API_URL || '/api'; // Use environment variable\n\n// Define Drag Item Types\nconst ItemTypes = {\n  BLOCK: 'block',\n  LIBRARY_BLOCK: 'library_block'\n};\n// Near the top of the file, add a template cache mechanism:\n// Cache for storing generated HTML previews\nconst templateCache = new Map();\n\n// Cache TTL (Time To Live) in milliseconds - 5 minutes\nconst CACHE_TTL = 5 * 60 * 1000;\n\n// Add this function before the EmailEditor component definition\n// Function to generate a cache key based on blocks\nconst generateCacheKey = blocks => {\n  return blocks.map(block => {\n    const {\n      instanceId,\n      content\n    } = block;\n    // Include only essential data in cache key\n    return `${block.blockId || block._id}:${instanceId}:${JSON.stringify(content)}`;\n  }).join('|');\n};\n\n// Function to clear stale cache entries\nconst clearStaleCache = () => {\n  const now = Date.now();\n  templateCache.forEach((entry, key) => {\n    if (now - entry.timestamp > CACHE_TTL) {\n      templateCache.delete(key);\n    }\n  });\n};\n\n// Enhanced state management with undo/redo functionality\n\n// --- EmailEditor Component ---\n\nconst EmailEditor = () => {\n  _s();\n  var _editorBlocks$selecte;\n  const {\n    templateId\n  } = useParams();\n  const navigate = useNavigate();\n\n  // Core State\n  const [template, setTemplate] = useState({});\n  const [editorBlocks, setEditorBlocks] = useState([]);\n  const [availableBlocks, setAvailableBlocks] = useState([]);\n  const [selectedBlockIndex, setSelectedBlockIndex] = useState(null);\n  const [previewHtml, setPreviewHtml] = useState('');\n  const [previewMode, setPreviewMode] = useState('desktop');\n  const [isSaving, setIsSaving] = useState(false);\n  const [showSaveModal, setShowSaveModal] = useState(false);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [saveError, setSaveError] = useState(null);\n  const [userPreferences, setUserPreferences] = useState(null);\n  const [templateName, setTemplateName] = useState('');\n\n  // Enhanced UI State\n  const [isDragActive, setIsDragActive] = useState(false);\n  const [showBlockLibrary, setShowBlockLibrary] = useState(true);\n  const [showPreview, setShowPreview] = useState(true);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);\n  const [viewMode, setViewMode] = useState('split');\n\n  // Undo/Redo State\n  const [undoRedoState, setUndoRedoState] = useState({\n    history: [],\n    currentIndex: -1,\n    maxHistorySize: 50\n  });\n\n  // Performance optimization - memoized values\n  const canUndo = useMemo(() => undoRedoState.currentIndex > 0, [undoRedoState.currentIndex]);\n  const canRedo = useMemo(() => undoRedoState.currentIndex < undoRedoState.history.length - 1, [undoRedoState.currentIndex, undoRedoState.history.length]);\n\n  // Refs\n  const previewIframeRef = useRef(null);\n  const editorCanvasRef = useRef(null);\n\n  // --- Undo/Redo Functions --- //\n  const saveToHistory = useCallback((blocks, selectedIndex) => {\n    setUndoRedoState(prev => {\n      const newState = {\n        blocks: JSON.parse(JSON.stringify(blocks)),\n        // Deep clone\n        selectedBlockIndex: selectedIndex,\n        timestamp: Date.now()\n      };\n\n      // Remove any future history if we're not at the end\n      const newHistory = prev.history.slice(0, prev.currentIndex + 1);\n      newHistory.push(newState);\n\n      // Limit history size\n      if (newHistory.length > prev.maxHistorySize) {\n        newHistory.shift();\n      }\n      return {\n        ...prev,\n        history: newHistory,\n        currentIndex: newHistory.length - 1\n      };\n    });\n  }, []);\n  const undo = useCallback(() => {\n    if (!canUndo) return;\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex - 1;\n      const state = prev.history[newIndex];\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canUndo]);\n  const redo = useCallback(() => {\n    if (!canRedo) return;\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex + 1;\n      const state = prev.history[newIndex];\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canRedo]);\n\n  // Keyboard shortcuts for undo/redo\n  useEffect(() => {\n    const handleKeyDown = e => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {\n        e.preventDefault();\n        undo();\n      } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || e.key === 'z' && e.shiftKey)) {\n        e.preventDefault();\n        redo();\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [undo, redo]);\n\n  // --- Data Fetching --- //\n  useEffect(() => {\n    let isMounted = true;\n    const fetchData = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Fetch available blocks first (always needed)\n        console.log(`Fetching blocks from ${API_URL}/blocks`);\n        // Use the shared api instance with BlockListResponse type\n        const blocksPromise = api.get('/blocks');\n\n        // Fetch user preferences to potentially apply brand colors/fonts later\n        console.log(`Fetching user preferences from ${API_URL}/user/preferences`);\n        // Use the shared api instance with correct inline type\n        const prefsPromise = api.get(`/user/preferences`);\n\n        // Fetch template data if ID exists\n        const templatePromise = templateId\n        // Use the shared api instance with TemplateDetailResponse type\n        ? api.get(`/templates/${templateId}`) : Promise.resolve(null);\n        const [blocksResponse, prefsResponse, templateResponse] = await Promise.all([blocksPromise, prefsPromise, templatePromise]);\n\n        // Access blocks correctly from response.data.data\n        const fetchedAvailableBlocks = blocksResponse.data.data || [];\n        console.log(`Fetched ${fetchedAvailableBlocks.length} available blocks.`);\n        setAvailableBlocks(fetchedAvailableBlocks);\n\n        // Process user preferences\n        const userPreferences = prefsResponse.data.preferences; // Store preferences\n        // TODO: Apply user preferences (e.g., brand colors, fonts)\n\n        if (templateResponse && templateResponse.data.template) {\n          const templateData = templateResponse.data.template; // Correct path now\n          console.log('[EmailEditor] Fetched template data:', JSON.stringify(templateData, null, 2)); // Log fetched data\n\n          // Apply brand colors/fonts from preferences if not set on template\n          setTemplate({\n            ...templateData,\n            brandColors: templateData.brandColors || (userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.brandColors),\n            defaultFonts: templateData.defaultFonts || (userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.defaultFonts)\n          });\n\n          // Populate editor blocks based on blockIds and fetched definitions\n          console.log('[EmailEditor] Attempting to populate blocks. Available block defs:', fetchedAvailableBlocks.length);\n          console.log('[EmailEditor] Template blockIds:', templateData.blockIds);\n          console.log('[EmailEditor] Template blocks data:', templateData.blocks);\n          if (templateData.blockIds && templateData.blockIds.length > 0 && fetchedAvailableBlocks.length > 0) {\n            console.log('[EmailEditor] Conditions met, proceeding to map blockIds.');\n            const populatedBlocks = templateData.blockIds.map((id, index) => {\n              var _templateData$blocks;\n              console.log(`[EmailEditor] Mapping blockId: ${id} at index: ${index}`);\n              // Match ID from templateData.blockIds with fetchedAvailableBlocks\n              const foundBlockDef = fetchedAvailableBlocks.find(b => b.blockId === id || b._id === id); // Check both blockId and _id\n              console.log(`[EmailEditor] ... Definition found for ${id}?`, foundBlockDef ? 'Yes' : 'No');\n              if (!foundBlockDef) {\n                console.warn(`[EmailEditor] Block definition not found for available block matching ID: ${id}. Skipping.`); // Refined warning\n                return null;\n              }\n\n              // Get content for this specific instance from templateData.blocks\n              const instanceBlockData = (_templateData$blocks = templateData.blocks) === null || _templateData$blocks === void 0 ? void 0 : _templateData$blocks[index]; // Get the block data saved for this instance\n              const instanceContent = (instanceBlockData === null || instanceBlockData === void 0 ? void 0 : instanceBlockData.content) || foundBlockDef.content || {}; // Use instance content, fallback to definition's default\n              console.log(`[EmailEditor] ... Instance content for ${id}:`, instanceContent);\n              const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`; // Unique key for React\n\n              // Return the combined block object for the editor state\n              const blockForState = {\n                ...foundBlockDef,\n                // Base definition (MJML, name, category etc.)\n                content: {\n                  ...instanceContent\n                },\n                // Specific content for this instance\n                instanceId // Unique ID for this instance in the editor\n              };\n              console.log(`[EmailEditor] ... Created block object for state for ${id}:`, blockForState);\n              return blockForState;\n            }).filter(b => b !== null);\n            console.log(\"[EmailEditor] Populated blocks array before setting state:\", populatedBlocks);\n            setEditorBlocks(populatedBlocks);\n          } else {\n            var _templateData$blockId;\n            console.log(\"[EmailEditor] Condition for populating blocks NOT met:\", {\n              hasBlockIds: !!templateData.blockIds,\n              blockIdsLength: (_templateData$blockId = templateData.blockIds) === null || _templateData$blockId === void 0 ? void 0 : _templateData$blockId.length,\n              hasFetchedBlocks: !!fetchedAvailableBlocks,\n              fetchedBlocksLength: fetchedAvailableBlocks === null || fetchedAvailableBlocks === void 0 ? void 0 : fetchedAvailableBlocks.length\n            });\n            setEditorBlocks([]); // Sets blocks to empty\n          }\n        } else {\n          // New template: initialize with preferences\n          console.log('Initializing new template state with preferences.');\n          setTemplate({\n            templateName: 'Untitled Template',\n            subject: 'Your Subject Here',\n            brandColors: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.brandColors,\n            defaultFonts: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.defaultFonts,\n            // Initialize other necessary fields if needed\n            userId: userPreferences === null || userPreferences === void 0 ? void 0 : userPreferences.userId // Important for saving later\n          });\n          setEditorBlocks([]);\n        }\n      } catch (err) {\n        console.error('Error loading editor data:', err);\n        let errorMsg = 'Failed to load editor data.'; // Default message\n\n        if (err.response) {\n          var _err$config, _err$config$url;\n          // Check if it's the template fetch that failed with 404\n          if ((_err$config = err.config) !== null && _err$config !== void 0 && (_err$config$url = _err$config.url) !== null && _err$config$url !== void 0 && _err$config$url.includes(`/templates/${templateId}`) && err.response.status === 404) {\n            errorMsg = `Template with ID ${templateId} not found. It may have been deleted.`;\n            // Optionally, redirect the user or clear the template state\n            // navigate('/templates'); // Example redirect\n            setTemplate({}); // Clear any partial template data\n            setEditorBlocks([]);\n          } else {\n            var _err$response$data, _err$response$data2;\n            // Use the error message from the response if available, otherwise use the generic message\n            errorMsg = ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || ((_err$response$data2 = err.response.data) === null || _err$response$data2 === void 0 ? void 0 : _err$response$data2.message) || err.message || errorMsg;\n          }\n        } else {\n          // Network error or other issue\n          errorMsg = err.message || errorMsg;\n        }\n        setError(errorMsg);\n        // Fallback logic: Check if availableBlocks is empty (meaning block fetch might have also failed)\n        if (availableBlocks.length === 0) {\n          console.warn(\"Falling back to empty block list as blocks couldn't be fetched or list is empty.\");\n          setAvailableBlocks([]); // Ensure it's an empty array if blocks failed\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchData();\n    return () => {\n      isMounted = false;\n    };\n    // eslint-disable-next-line\n  }, [templateId]); // Rerun only when templateId changes\n\n  // --- MJML Generation & Preview Update --- //\n  const generateMjmlFromBlocks = useCallback(blockList => {\n    var _template$brandColors, _template$defaultFont;\n    // If there are no blocks, return early with an empty string\n    if (!blockList.length) return '';\n\n    // Generate a cache key for this block configuration\n    const cacheKey = generateCacheKey(blockList);\n\n    // Check if we have a cached version\n    if (templateCache.has(cacheKey)) {\n      const cached = templateCache.get(cacheKey);\n      if (cached && Date.now() - cached.timestamp < CACHE_TTL) {\n        console.log(\"[EmailEditor] Using cached MJML\");\n        return cached.mjml;\n      }\n    }\n\n    // Periodically clean up old cache entries\n    clearStaleCache();\n\n    // Process blocks in chunks if there are many\n    const chunkSize = 5;\n    let mjmlBodyContent = '';\n    for (let i = 0; i < blockList.length; i += chunkSize) {\n      const chunk = blockList.slice(i, i + chunkSize);\n      const chunkContent = chunk.map(block => {\n        let blockMjml = block.mjml || '';\n        const content = block.content || {};\n\n        // More robust replacement logic\n        Object.keys(content).forEach(key => {\n          const value = content[key];\n          const placeholder = `{{${key}}}`; // Standard placeholder\n\n          // Only process if value exists\n          if (value !== undefined && value !== null) {\n            // Handle arrays specifically (e.g., nav links, social icons)\n            if (Array.isArray(value)) {\n              // Existing array handling code...\n              if (key === 'nav_links' || key === 'navLinks') {\n                const linksHtml = value.map(link => `<mj-navbar-link href=\"${link.url || '#'}\" color=\"#1E3A8A\">${link.name || 'Link'}</mj-navbar-link>`).join('\\n');\n                blockMjml = blockMjml.replace('{{navLinksArea}}', linksHtml);\n              } else if (key === 'social_icons' || key === 'socialLinks') {\n                const iconsHtml = value.map(icon => {\n                  var _icon$platform;\n                  return `<mj-social-element name=\"${((_icon$platform = icon.platform) === null || _icon$platform === void 0 ? void 0 : _icon$platform.toLowerCase()) || 'share'}\" href=\"${icon.url || '#'}\"></mj-social-element>`;\n                }).join('\\n');\n                blockMjml = blockMjml.replace('{{socialIconsArea}}', iconsHtml);\n              }\n            } else {\n              // More efficient single replacement (no array of placeholders)\n              const stringValue = String(value);\n              blockMjml = blockMjml.replaceAll(placeholder, stringValue);\n\n              // Only try alternative formats if there's a good chance they exist\n              if (blockMjml.includes('{{')) {\n                // Convert camelCase to snake_case for placeholder check if needed\n                const camelCasePlaceholder = `{{${key.replace(/_([a-z])/g, g => g[1].toUpperCase())}}}`;\n                const snakeCasePlaceholder = `{{${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)}}}`;\n                blockMjml = blockMjml.replaceAll(camelCasePlaceholder, stringValue);\n                blockMjml = blockMjml.replaceAll(snakeCasePlaceholder, stringValue);\n              }\n            }\n          }\n        });\n\n        // More efficient placeholder cleanup - only if needed\n        if (blockMjml.includes('{{')) {\n          blockMjml = blockMjml.replace(/\\{\\{[\\w.-]+\\}\\}/g, '');\n        }\n        return blockMjml;\n      }).join('\\n');\n      mjmlBodyContent += chunkContent;\n    }\n\n    // Use template state for colors/fonts, falling back to defaults\n    const brandColors = (_template$brandColors = template === null || template === void 0 ? void 0 : template.brandColors) !== null && _template$brandColors !== void 0 ? _template$brandColors : {};\n    const defaultFonts = (_template$defaultFont = template === null || template === void 0 ? void 0 : template.defaultFonts) !== null && _template$defaultFont !== void 0 ? _template$defaultFont : {};\n    const primaryColor = brandColors.primary || '#4F46E5';\n    const backgroundColor = brandColors.background || '#f3f4f6';\n    const textColor = brandColors.text || '#111827';\n    const headingFont = defaultFonts.heading || 'Arial, sans-serif';\n    const bodyFont = defaultFonts.body || 'Arial, sans-serif';\n\n    // Construct the full MJML document\n    const fullMjml = `\n<mjml>\n  <mj-head>\n    <mj-title>${(template === null || template === void 0 ? void 0 : template.subject) || 'Email Template'}</mj-title>\n    <mj-font name=\"Roboto\" href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700\" />\n    <mj-attributes>\n      <mj-all padding=\"0px\" font-family=\"${bodyFont}\" />\n      <mj-text padding=\"10px 25px\" font-size=\"14px\" line-height=\"1.5\" color=\"${textColor}\" />\n      <mj-section padding=\"10px 0\" />\n      <mj-column padding=\"5px\" />\n      <mj-button background-color=\"${primaryColor}\" color=\"#ffffff\" font-weight=\"bold\" border-radius=\"4px\" padding=\"10px 20px\" />\n      <mj-image padding=\"0px\" />\n    </mj-attributes>\n    <mj-style inline=\"inline\">\n      /* Inline styles */\n      a { color: ${primaryColor} !important; text-decoration: none !important; }\n    </mj-style>\n     <mj-style>\n      /* Embedded styles */\n      .hover-link:hover { text-decoration: underline !important; }\n    </mj-style>\n  </mj-head>\n  <mj-body background-color=\"${backgroundColor}\">\n    ${mjmlBodyContent}\n  </mj-body>\n</mjml>`;\n\n    // Store in cache\n    templateCache.set(cacheKey, {\n      mjml: fullMjml,\n      html: '',\n      // Will be populated on first HTML conversion\n      timestamp: Date.now()\n    });\n    return fullMjml;\n  }, [template]);\n  useEffect(() => {\n    const generatePreview = () => {\n      if (!editorBlocks || editorBlocks.length === 0) {\n        setPreviewHtml('<div style=\"display: flex; justify-content: center; align-items: center; height: 100%; color: grey; padding: 20px; text-align: center;\">Drag blocks here to build your email</div>');\n        return;\n      }\n      try {\n        // Generate cache key\n        const cacheKey = generateCacheKey(editorBlocks);\n\n        // Check for valid cached HTML\n        if (templateCache.has(cacheKey)) {\n          const cached = templateCache.get(cacheKey);\n          if (cached && cached.html && Date.now() - cached.timestamp < CACHE_TTL) {\n            console.log(\"[EmailEditor Preview] Using cached HTML\");\n            setPreviewHtml(cached.html);\n            return;\n          }\n        }\n\n        // If we get here, generate a new preview\n        const mjmlString = generateMjmlFromBlocks(editorBlocks);\n        console.log(\"[EmailEditor Preview] Generating fresh HTML preview\");\n\n        // Use a web worker for MJML conversion if possible\n        if (window.Worker) {\n          // Note: You would need to create a separate mjml-worker.js file\n          // This is just demonstrating the concept\n          const workerTimeout = setTimeout(() => {\n            // Fallback if worker takes too long\n            try {\n              const {\n                html,\n                errors\n              } = mjml2html(mjmlString, {\n                validationLevel: 'soft'\n              });\n              setPreviewHtml(html);\n              // Update cache\n              const cacheKey = generateCacheKey(editorBlocks);\n              if (templateCache.has(cacheKey)) {\n                const entry = templateCache.get(cacheKey);\n                if (entry) {\n                  entry.html = html;\n                  entry.timestamp = Date.now();\n                }\n              }\n            } catch (fallbackErr) {\n              console.error('[EmailEditor Preview] Error in fallback conversion:', fallbackErr);\n              setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${(fallbackErr === null || fallbackErr === void 0 ? void 0 : fallbackErr.message) || 'Unknown error'}</div>`);\n            }\n          }, 1000); // 1 second timeout\n\n          // This is just conceptual - actual implementation would need the worker file\n          // const worker = new Worker('/mjml-worker.js');\n          // worker.postMessage(mjmlString);\n          // worker.onmessage = (e) => {\n          //   clearTimeout(workerTimeout);\n          //   const { html, errors } = e.data;\n          //   setPreviewHtml(html);\n          //   // Update cache\n          //   updateHtmlCache(cacheKey, html);\n          // };\n        } else {\n          // Direct conversion when Web Workers aren't available\n          const {\n            html,\n            errors\n          } = mjml2html(mjmlString, {\n            validationLevel: 'soft'\n          });\n          if (errors && errors.length > 0) {\n            console.warn('[EmailEditor Preview] MJML Validation:', errors.length, 'issues');\n          }\n          setPreviewHtml(html);\n\n          // Update cache\n          if (templateCache.has(cacheKey)) {\n            const entry = templateCache.get(cacheKey);\n            if (entry) {\n              entry.html = html;\n              entry.timestamp = Date.now();\n            }\n          }\n        }\n      } catch (err) {\n        console.error('[EmailEditor Preview] Error generating preview:', err);\n        setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${(err === null || err === void 0 ? void 0 : err.message) || 'Unknown error'}</div>`);\n      }\n    };\n\n    // Debounce the preview generation to avoid too many updates\n    const debounceTimeout = setTimeout(generatePreview, 300);\n    return () => clearTimeout(debounceTimeout);\n  }, [editorBlocks, generateMjmlFromBlocks]);\n\n  // --- Enhanced DND Callbacks --- //\n  const moveBlock = useCallback((dragIndex, hoverIndex) => {\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = [...prevBlocks];\n      const [draggedBlock] = newBlocks.splice(dragIndex, 1);\n      newBlocks.splice(hoverIndex, 0, draggedBlock);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, selectedBlockIndex === dragIndex ? hoverIndex : selectedBlockIndex);\n      return newBlocks;\n    });\n\n    // Adjust selected index\n    if (selectedBlockIndex === dragIndex) {\n      setSelectedBlockIndex(hoverIndex);\n    } else if (selectedBlockIndex !== null) {\n      if (dragIndex < hoverIndex && selectedBlockIndex > dragIndex && selectedBlockIndex <= hoverIndex) {\n        setSelectedBlockIndex(s => s !== null ? s - 1 : null);\n      } else if (dragIndex > hoverIndex && selectedBlockIndex >= hoverIndex && selectedBlockIndex < dragIndex) {\n        setSelectedBlockIndex(s => s !== null ? s + 1 : null);\n      }\n    }\n  }, [selectedBlockIndex, saveToHistory]);\n  const dropBlockFromLibrary = useCallback((block, index) => {\n    // Cast the block to ensure it has all required properties\n    const blockWithRequiredProps = {\n      ...JSON.parse(JSON.stringify(block)),\n      blockId: block.blockId || block._id || `block-${Date.now()}`,\n      // Ensure blockId is never undefined\n      content: block.content ? JSON.parse(JSON.stringify(block.content)) : {},\n      instanceId: `${block.blockId || block._id || 'new'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n    };\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = [...prevBlocks];\n      newBlocks.splice(index, 0, blockWithRequiredProps);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, index);\n      return newBlocks;\n    });\n    setSelectedBlockIndex(index); // Select the newly dropped block\n  }, [saveToHistory]);\n  const removeBlock = useCallback(index => {\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = prevBlocks.filter((_, i) => i !== index);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, selectedBlockIndex === index ? null : selectedBlockIndex !== null && selectedBlockIndex > index ? selectedBlockIndex - 1 : selectedBlockIndex);\n      return newBlocks;\n    });\n    if (selectedBlockIndex === index) {\n      setSelectedBlockIndex(null);\n    } else if (selectedBlockIndex !== null && selectedBlockIndex > index) {\n      setSelectedBlockIndex(prevIndex => prevIndex !== null ? prevIndex - 1 : null);\n    }\n  }, [selectedBlockIndex, saveToHistory]);\n  const duplicateBlock = useCallback(index => {\n    setEditorBlocks(prevBlocks => {\n      const blockToDuplicate = prevBlocks[index];\n      const duplicatedBlock = {\n        ...JSON.parse(JSON.stringify(blockToDuplicate)),\n        instanceId: `${blockToDuplicate.blockId || blockToDuplicate._id || 'dup'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n      };\n      const newBlocks = [...prevBlocks];\n      newBlocks.splice(index + 1, 0, duplicatedBlock);\n\n      // Save to history for undo/redo\n      saveToHistory(newBlocks, index + 1);\n      return newBlocks;\n    });\n    setSelectedBlockIndex(index + 1); // Select the duplicated block\n  }, [saveToHistory]);\n  const updateBlockContent = useCallback((index, updatedContent) => {\n    setEditorBlocks(prevBlocks => {\n      const newBlocks = prevBlocks.map((block, i) => i === index ? {\n        ...block,\n        content: {\n          ...(block.content || {}),\n          ...updatedContent\n        }\n      } : block);\n\n      // Save to history for undo/redo (debounced to avoid too many history entries)\n      const debouncedSave = setTimeout(() => {\n        saveToHistory(newBlocks, selectedBlockIndex);\n      }, 1000);\n      return newBlocks;\n    });\n  }, [saveToHistory, selectedBlockIndex]);\n\n  // --- Save Handler --- //\n  const handleSave = async (templateNameToSave, isPublicStatus = false) => {\n    setIsSaving(true);\n    setSaveError(null);\n    try {\n      const mjml = generateMjmlFromBlocks(editorBlocks);\n      const {\n        html,\n        errors: conversionErrors\n      } = mjml2html(mjml);\n      if (conversionErrors && conversionErrors.length > 0) {\n        console.warn('MJML conversion errors detected during save:', conversionErrors);\n        // Consider not saving if critical errors occurred\n      }\n      const blockIds = editorBlocks.map(b => String(b.blockId || b._id)) // Ensure string ID\n      .filter(Boolean); // Filter out any potential undefined/null\n\n      const payload = {\n        templateId: template === null || template === void 0 ? void 0 : template._id,\n        templateName: templateNameToSave,\n        mjml,\n        html,\n        blockIds,\n        subject: (template === null || template === void 0 ? void 0 : template.subject) || 'Untitled Template',\n        tags: (template === null || template === void 0 ? void 0 : template.tags) || [],\n        isPublic: isPublicStatus,\n        description: (template === null || template === void 0 ? void 0 : template.description) || '',\n        aiPrompt: (template === null || template === void 0 ? void 0 : template.aiPrompt) || '',\n        isAiGenerated: (template === null || template === void 0 ? void 0 : template.isAiGenerated) || false\n      };\n\n      // Add userId if it's a new template - CRITICAL: Ensure req.user is populated in backend\n      // This assumes the backend will get the userId from the authenticated request (authenticateJWT)\n      // if (!payload.templateId) {\n      //   payload.userId = template?.userId; // Might be set from prefs initially\n      // }\n\n      const response = await api.post('/templates/save', payload);\n      const savedTemplate = response.data.template;\n      setTemplate(savedTemplate);\n\n      // Re-sync editor blocks if necessary (e.g., if backend modifies content/IDs)\n      // This part needs careful implementation if backend modifies block content on save\n      if (savedTemplate.blockIds && availableBlocks.length > 0) {\n        const populatedBlocks = savedTemplate.blockIds.map((id, index) => {\n          var _savedTemplate$blocks, _savedTemplate$blocks2;\n          const foundBlockDef = availableBlocks.find(b => b.blockId === id || b._id === id);\n          const instanceContent = ((_savedTemplate$blocks = savedTemplate.blocks) === null || _savedTemplate$blocks === void 0 ? void 0 : (_savedTemplate$blocks2 = _savedTemplate$blocks[index]) === null || _savedTemplate$blocks2 === void 0 ? void 0 : _savedTemplate$blocks2.content) || (foundBlockDef === null || foundBlockDef === void 0 ? void 0 : foundBlockDef.content) || {};\n          const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`;\n          return foundBlockDef ? {\n            ...foundBlockDef,\n            content: {\n              ...instanceContent\n            },\n            instanceId\n          } : null;\n        }).filter(b => b !== null);\n        setEditorBlocks(populatedBlocks);\n      }\n      setShowSaveModal(false);\n      console.log('Template saved successfully!');\n      if (!templateId && savedTemplate._id) {\n        navigate(`/email-editor/${savedTemplate._id}`, {\n          replace: true\n        });\n      }\n      return savedTemplate;\n    } catch (err) {\n      var _err$response, _err$response$data3;\n      console.error('Error saving template:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data3 = _err$response.data) === null || _err$response$data3 === void 0 ? void 0 : _err$response$data3.error) || err.message || 'Failed to save template.';\n      setSaveError(errorMessage);\n      return null;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // --- Drop Target for Canvas --- //\n  const [{\n    isOverCanvas\n  }, drop] = useDrop(() => ({\n    accept: [ItemTypes.BLOCK, ItemTypes.LIBRARY_BLOCK],\n    drop: (item, monitor) => {\n      if (monitor.didDrop()) return;\n      const editorDiv = editorCanvasRef.current;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset || !editorDiv) return;\n      const hoverIndex = findDropIndex(clientOffset.y, editorDiv);\n      if (item.type === ItemTypes.LIBRARY_BLOCK) {\n        dropBlockFromLibrary(item.block, hoverIndex);\n      }\n      // Reordering is handled by DraggableBlock's hover\n    },\n    collect: monitor => ({\n      isOverCanvas: monitor.isOver({\n        shallow: true\n      })\n    })\n  }), [editorBlocks, moveBlock, dropBlockFromLibrary]); // Ensure correct dependencies\n\n  // Helper function to find the correct drop index\n  const findDropIndex = (clientY, container) => {\n    if (clientY === undefined) return editorBlocks.length;\n    const containerRect = container.getBoundingClientRect();\n    const offsetY = clientY - containerRect.top + container.scrollTop;\n    let calculatedIndex = editorBlocks.length;\n    const children = Array.from(container.children);\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if (!child.classList || !child.classList.contains('draggable-block')) continue;\n      const childTop = child.offsetTop;\n      const childHeight = child.offsetHeight;\n      const middleY = childTop + childHeight / 2;\n      if (offsetY < middleY) {\n        calculatedIndex = i;\n        break;\n      }\n    }\n    return calculatedIndex;\n  };\n\n  // Cleanup cache when component unmounts\n  useEffect(() => {\n    return () => {\n      // Clear the entire cache when this editor instance is unmounted\n      templateCache.clear();\n    };\n  }, []);\n\n  // Add a cleanup function for memory management\n  const cleanupUnusedResources = useCallback(() => {\n    // Clear any unused thumbnails from memory if browser gets low on memory\n    if ('memory' in performance) {\n      const memoryInfo = performance.memory;\n      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {\n        console.log('[EmailEditor] Memory pressure detected, cleaning up resources');\n        // Force a garbage collection if possible\n        clearStaleCache();\n        // You could also unload any non-visible thumbnails or other large objects\n      }\n    }\n  }, []);\n\n  // Listen for low memory events\n  useEffect(() => {\n    if ('onmemorywarning' in window) {\n      window.addEventListener('memorywarning', cleanupUnusedResources);\n      return () => {\n        window.removeEventListener('memorywarning', cleanupUnusedResources);\n      };\n    }\n  }, [cleanupUnusedResources]);\n\n  // --- Render Logic --- //\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-screen text-gray-600 text-lg\",\n      children: \"Loading Editor...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 844,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Critical error on initial load\n  if (error && !template) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 m-4 text-red-700 bg-red-100 border border-red-400 rounded\",\n      children: [\"Error loading editor configuration: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 849,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"email-editor flex flex-col h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"editor-toolbar flex items-center justify-between px-4 py-2 bg-white border-b border-gray-200 shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-left flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-800\",\n          children: (template === null || template === void 0 ? void 0 : template.templateName) || 'Untitled Template'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: undo,\n            disabled: !canUndo,\n            className: \"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            title: \"Undo (Ctrl+Z)\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: redo,\n            disabled: !canRedo,\n            className: \"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n            title: \"Redo (Ctrl+Y)\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 873,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 862,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-center flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"preview-mode-toggle flex items-center bg-gray-100 rounded-lg p-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setViewMode('edit'),\n            className: `toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${viewMode === 'edit' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 896,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 15\n            }, this), \"Edit\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 889,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setViewMode('split'),\n            className: `toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${viewMode === 'split' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this), \"Split\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 900,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setViewMode('preview'),\n            className: `toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${viewMode === 'preview' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 15\n            }, this), \"Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 888,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 886,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-right flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center bg-gray-100 rounded-lg p-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setPreviewMode('desktop'),\n            className: `px-3 py-1 rounded-md text-sm font-medium transition-colors ${previewMode === 'desktop' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: \"Desktop\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 929,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setPreviewMode('mobile'),\n            className: `px-3 py-1 rounded-md text-sm font-medium transition-colors ${previewMode === 'mobile' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'}`,\n            children: \"Mobile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 928,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            setTemplate(prev => ({\n              ...(prev || {}),\n              templateName: (prev === null || prev === void 0 ? void 0 : prev.templateName) || 'Untitled Template'\n            }));\n            setSaveError(null);\n            setShowSaveModal(true);\n          },\n          disabled: isSaving,\n          className: \"save-button px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors disabled:bg-indigo-400 disabled:cursor-not-allowed\",\n          children: isSaving ? 'Saving...' : 'Save Template'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 948,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 855,\n      columnNumber: 7\n    }, this), error && templateId && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 text-sm text-red-700 bg-red-100 border-b border-red-300 text-center\",\n      children: [\"Error: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 964,\n      columnNumber: 11\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `editor-content flex flex-1 overflow-hidden`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"block-library-panel w-64 bg-white border-r border-gray-200 flex flex-col overflow-hidden shrink-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"px-4 py-3 text-base font-semibold text-gray-800 border-b border-gray-200 whitespace-nowrap\",\n          children: \"Block Library\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 970,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BlockLibrary, {\n          blocks: availableBlocks,\n          onAddBlock: block => dropBlockFromLibrary(block, editorBlocks.length) // Click adds to end\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 971,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 969,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: drop,\n        className: `editor-workspace flex-1 flex flex-col overflow-hidden bg-gray-200 ${isOverCanvas ? 'outline-2 outline-dashed outline-indigo-500' : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: editorCanvasRef,\n          className: \"blocks-container flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto\",\n          children: editorBlocks.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"empty-blocks flex items-center justify-center h-full text-gray-500 text-center p-8 border-2 border-dashed border-gray-300 rounded-lg min-h-[200px]\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Drag blocks from the library here\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 53\n              }, this), \"or click a block in the library to add it.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 981,\n            columnNumber: 15\n          }, this) : editorBlocks.map((block, index) => /*#__PURE__*/_jsxDEV(DraggableBlock, {\n            // Use unique instanceId\n            block: block,\n            index: index,\n            moveBlock: moveBlock,\n            removeBlock: removeBlock,\n            duplicateBlock: duplicateBlock,\n            isSelected: selectedBlockIndex === index,\n            onClick: () => setSelectedBlockIndex(index)\n          }, block.instanceId, false, {\n            fileName: _jsxFileName,\n            lineNumber: 986,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 979,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 978,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"right-panel w-80 md:w-96 lg:w-[500px] border-l border-gray-300 flex flex-col shrink-0 bg-white shadow-lg\",\n        children: selectedBlockIndex === null || editorBlocks.length === 0 ?\n        /*#__PURE__*/\n        // Show Preview when no block is selected OR if editor is empty\n        _jsxDEV(\"div\", {\n          className: \"preview-panel flex-1 flex flex-col overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200\",\n            children: \"Preview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(EmailPreview, {\n            html: previewHtml,\n            mode: previewMode,\n            iframeRef: previewIframeRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1005,\n          columnNumber: 17\n        }, this) :\n        /*#__PURE__*/\n        // Show Block Editor when a block is selected\n        _jsxDEV(\"div\", {\n          className: \"block-editor-panel flex-1 flex flex-col overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200 flex justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Edit: \", ((_editorBlocks$selecte = editorBlocks[selectedBlockIndex]) === null || _editorBlocks$selecte === void 0 ? void 0 : _editorBlocks$selecte.name) || 'Block']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1017,\n              columnNumber: 23\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedBlockIndex(null),\n              className: \"text-sm text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100\",\n              title: \"Close Editor\",\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1016,\n            columnNumber: 21\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 overflow-y-auto\",\n            children: editorBlocks[selectedBlockIndex] &&\n            /*#__PURE__*/\n            // Check block exists before rendering\n            _jsxDEV(BlockEditor, {\n              block: editorBlocks[selectedBlockIndex],\n              onUpdate: content => {\n                if (selectedBlockIndex !== null) {\n                  updateBlockContent(selectedBlockIndex, content);\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1026,\n            columnNumber: 21\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1015,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1002,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 967,\n      columnNumber: 7\n    }, this), showSaveModal && /*#__PURE__*/_jsxDEV(SaveTemplateModal, {\n      initialName: (template === null || template === void 0 ? void 0 : template.templateName) || 'Untitled Template',\n      onSave: handleSave,\n      onCancel: () => {\n        setShowSaveModal(false);\n        setSaveError(null);\n      },\n      isSaving: isSaving,\n      error: saveError // Pass save error to modal\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1046,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 853,\n    columnNumber: 5\n  }, this);\n};\n\n// Draggable Block Component (Internal to EmailEditor)\n_s(EmailEditor, \"g/JfYLZL7+7SpgSwW+wdzhU6Qfw=\", false, function () {\n  return [useParams, useNavigate, useDrop];\n});\n_c = EmailEditor;\nconst DraggableBlock = ({\n  block,\n  index,\n  moveBlock,\n  removeBlock,\n  duplicateBlock,\n  isSelected,\n  onClick\n}) => {\n  _s2();\n  const ref = useRef(null);\n  const [{\n    handlerId\n  }, drop] = useDrop(() => ({\n    accept: ItemTypes.BLOCK,\n    hover: (item, monitor) => {\n      if (!ref.current) return;\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) return;\n      const hoverBoundingRect = ref.current.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n      const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;\n      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;\n      moveBlock(dragIndex, hoverIndex);\n      item.index = hoverIndex; // Mutate monitor item for performance\n    },\n    collect: monitor => ({\n      handlerId: monitor.getHandlerId()\n    })\n  }), [index, moveBlock]); // Dependencies for useDrop hover logic\n\n  const [{\n    isDragging\n  }, drag] = useDrag(() => ({\n    type: ItemTypes.BLOCK,\n    item: {\n      index,\n      id: block.instanceId,\n      type: ItemTypes.BLOCK\n    },\n    // Return item as a function\n    collect: monitor => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [index, block.instanceId]); // Dependencies for useDrag\n\n  drag(drop(ref)); // Combine drag and drop refs\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: ref,\n    \"data-handler-id\": handlerId,\n    className: `draggable-block bg-white border rounded-md mb-4 cursor-move shadow-sm ${isDragging ? 'opacity-40 border-blue-500' : 'hover:border-blue-400 hover:shadow-md'} ${isSelected ? 'border-blue-500 ring-2 ring-blue-300 ring-offset-1' : 'border-gray-200'}` // styles/editor.css\n    ,\n    onClick: onClick,\n    style: {\n      opacity: isDragging ? 0.4 : 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-header flex items-center justify-between px-3 py-1.5 border-b border-gray-200 bg-gray-50 rounded-t-md text-xs\",\n      children: [\" \", /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block-type font-medium text-gray-700 truncate pr-2\",\n        title: block.name,\n        children: [block.name, \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-400\",\n          children: [\"(\", block.category, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1124,\n          columnNumber: 110\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"block-actions flex items-center space-x-1\",\n        children: [\" \", /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"p-1 text-gray-400 hover:text-blue-600 transition-colors\",\n          onClick: e => {\n            e.stopPropagation();\n            duplicateBlock(index);\n          },\n          title: \"Duplicate Block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1138,\n              columnNumber: 20\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1137,\n            columnNumber: 16\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1127,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"remove-block p-1 text-gray-400 hover:text-red-600 transition-colors\" // styles/editor.css\n          ,\n          onClick: e => {\n            e.stopPropagation();\n            removeBlock(index);\n          },\n          title: \"Remove Block\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-4 w-4\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            strokeWidth: 2,\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1153,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"block-content-preview p-3 text-sm text-gray-600 bg-white rounded-b-md min-h-[50px]\",\n      children: [(() => {\n        // Display appropriate preview based on block type\n        if (block.blockId === 'header/simple-nav') {\n          return 'Navigation Header';\n        } else if (block.blockId === 'layout/hero') {\n          var _block$content;\n          return ((_block$content = block.content) === null || _block$content === void 0 ? void 0 : _block$content.heroHeadline) || 'Hero Section';\n        } else if (block.blockId === 'content/headline') {\n          var _block$content2;\n          return ((_block$content2 = block.content) === null || _block$content2 === void 0 ? void 0 : _block$content2.headline) || 'Headline';\n        } else if (block.blockId === 'product/grid') {\n          var _block$content3, _block$content4, _block$content5;\n          return 'Product Grid: ' + [(_block$content3 = block.content) === null || _block$content3 === void 0 ? void 0 : _block$content3.prod1_name, (_block$content4 = block.content) === null || _block$content4 === void 0 ? void 0 : _block$content4.prod2_name, (_block$content5 = block.content) === null || _block$content5 === void 0 ? void 0 : _block$content5.prod3_name].filter(Boolean).join(', ');\n        } else if (block.blockId === 'cta/button') {\n          var _block$content6;\n          return ((_block$content6 = block.content) === null || _block$content6 === void 0 ? void 0 : _block$content6.buttonText) || 'Button';\n        } else if (block.blockId === 'footer/standard') {\n          var _block$content7;\n          return (_block$content7 = block.content) !== null && _block$content7 !== void 0 && _block$content7.companyName ? `Footer: ${block.content.companyName}` : 'Standard Footer';\n        } else {\n          var _block$content8, _block$content9, _block$content9$body, _block$content10;\n          // Fallback to original logic\n          return ((_block$content8 = block.content) === null || _block$content8 === void 0 ? void 0 : _block$content8.headline) || ((_block$content9 = block.content) === null || _block$content9 === void 0 ? void 0 : (_block$content9$body = _block$content9.body) === null || _block$content9$body === void 0 ? void 0 : _block$content9$body.substring(0, 50)) + ((_block$content10 = block.content) !== null && _block$content10 !== void 0 && _block$content10.body && block.content.body.length > 50 ? '...' : '') || block.name;\n        }\n      })(), block.thumbnail && /*#__PURE__*/_jsxDEV(\"img\", {\n        src: block.thumbnail,\n        alt: `${block.name} thumbnail`,\n        className: \"mx-auto h-12 mt-2 opacity-75 object-contain\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1186,\n        columnNumber: 29\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1159,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1116,\n    columnNumber: 5\n  }, this);\n};\n_s2(DraggableBlock, \"Bm9lse0dSUrMRW3+tCDIsw+uDQ8=\", false, function () {\n  return [useDrop, useDrag];\n});\n_c2 = DraggableBlock;\nexport { EmailEditor };\nexport default EmailEditor;\nvar _c, _c2;\n$RefreshReg$(_c, \"EmailEditor\");\n$RefreshReg$(_c2, \"DraggableBlock\");", "map": {"version": 3, "names": ["React", "useCallback", "useEffect", "useMemo", "useRef", "useState", "mjml2html", "useDrag", "useDrop", "useNavigate", "useParams", "api", "BlockEditor", "BlockLibrary", "EmailPreview", "SaveTemplateModal", "jsxDEV", "_jsxDEV", "API_URL", "process", "env", "REACT_APP_API_URL", "ItemTypes", "BLOCK", "LIBRARY_BLOCK", "templateCache", "Map", "CACHE_TTL", "generate<PERSON>ache<PERSON>ey", "blocks", "map", "block", "instanceId", "content", "blockId", "_id", "JSON", "stringify", "join", "clearStaleCache", "now", "Date", "for<PERSON>ach", "entry", "key", "timestamp", "delete", "EmailEditor", "_s", "_editorBlocks$selecte", "templateId", "navigate", "template", "setTemplate", "<PERSON><PERSON><PERSON><PERSON>", "setEditorBlocks", "availableBlocks", "setAvailableBlocks", "selectedBlockIndex", "setSelectedBlockIndex", "previewHtml", "setPreviewHtml", "previewMode", "setPreviewMode", "isSaving", "setIsSaving", "showSaveModal", "setShowSaveModal", "isLoading", "setIsLoading", "error", "setError", "saveError", "setSaveError", "userPreferences", "setUserPreferences", "templateName", "setTemplateName", "isDragActive", "setIsDragActive", "showBlockLibrary", "setShowBlockLibrary", "showPreview", "setShowPreview", "isGeneratingPreview", "setIsGeneratingPreview", "viewMode", "setViewMode", "undoRedoState", "setUndoRedoState", "history", "currentIndex", "maxHistorySize", "canUndo", "canRedo", "length", "previewIframeRef", "editorCanvasRef", "saveToHistory", "selectedIndex", "prev", "newState", "parse", "newHistory", "slice", "push", "shift", "undo", "newIndex", "state", "redo", "handleKeyDown", "e", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "preventDefault", "window", "addEventListener", "removeEventListener", "isMounted", "fetchData", "console", "log", "blocksPromise", "get", "prefsPromise", "templatePromise", "Promise", "resolve", "blocksResponse", "prefsResponse", "templateResponse", "all", "fetchedAvailableBlocks", "data", "preferences", "templateData", "brandColors", "defaultFonts", "blockIds", "populatedBlocks", "id", "index", "_templateData$blocks", "foundBlockDef", "find", "b", "warn", "instanceBlockData", "instanceContent", "Math", "random", "toString", "substring", "blockForState", "filter", "_templateData$blockId", "hasBlockIds", "blockIdsLength", "hasFetchedBlocks", "fetchedBlocksLength", "subject", "userId", "err", "errorMsg", "response", "_err$config", "_err$config$url", "config", "url", "includes", "status", "_err$response$data", "_err$response$data2", "message", "generateMjmlFromBlocks", "blockList", "_template$brandColors", "_template$defaultFont", "cache<PERSON>ey", "has", "cached", "mjml", "chunkSize", "mjml<PERSON>ody<PERSON><PERSON>nt", "i", "chunk", "chunkContent", "blockMjml", "Object", "keys", "value", "placeholder", "undefined", "Array", "isArray", "linksHtml", "link", "name", "replace", "iconsHtml", "icon", "_icon$platform", "platform", "toLowerCase", "stringValue", "String", "replaceAll", "camelCasePlaceholder", "g", "toUpperCase", "snakeCasePlaceholder", "letter", "primaryColor", "primary", "backgroundColor", "background", "textColor", "text", "headingFont", "heading", "bodyFont", "body", "fullMjml", "set", "html", "generatePreview", "mjmlString", "Worker", "workerTimeout", "setTimeout", "errors", "validationLevel", "fallbackErr", "debounceTimeout", "clearTimeout", "moveBlock", "dragIndex", "hoverIndex", "prevBlocks", "newBlocks", "<PERSON><PERSON><PERSON>", "splice", "s", "dropBlockFromLibrary", "blockWithRequiredProps", "removeBlock", "_", "prevIndex", "duplicateBlock", "blockToDuplicate", "duplicatedBlock", "updateBlockContent", "updatedContent", "debouncedSave", "handleSave", "templateNameToSave", "isPublicStatus", "conversionErrors", "Boolean", "payload", "tags", "isPublic", "description", "aiPrompt", "isAiGenerated", "post", "savedTemplate", "_savedTemplate$blocks", "_savedTemplate$blocks2", "_err$response", "_err$response$data3", "errorMessage", "isOverCanvas", "drop", "accept", "item", "monitor", "didDrop", "editorD<PERSON>", "current", "clientOffset", "getClientOffset", "findDropIndex", "y", "type", "collect", "isOver", "shallow", "clientY", "container", "containerRect", "getBoundingClientRect", "offsetY", "top", "scrollTop", "calculatedIndex", "children", "from", "child", "classList", "contains", "childTop", "offsetTop", "childHeight", "offsetHeight", "middleY", "clear", "cleanupUnusedResources", "performance", "memoryInfo", "memory", "usedJSHeapSize", "jsHeapSizeLimit", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "title", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onAddBlock", "ref", "DraggableBlock", "isSelected", "mode", "iframeRef", "onUpdate", "initialName", "onSave", "onCancel", "_c", "_s2", "handlerId", "hover", "hoverBoundingRect", "hoverMiddleY", "bottom", "hoverClientY", "getHandlerId", "isDragging", "drag", "style", "opacity", "category", "stopPropagation", "xmlns", "_block$content", "heroHeadline", "_block$content2", "headline", "_block$content3", "_block$content4", "_block$content5", "prod1_name", "prod2_name", "prod3_name", "_block$content6", "buttonText", "_block$content7", "companyName", "_block$content8", "_block$content9", "_block$content9$body", "_block$content10", "thumbnail", "src", "alt", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/EmailEditor.tsx"], "sourcesContent": ["/**\n * Enhanced EmailEditor component for Driftly Email Generator\n * Provides advanced drag-and-drop interface for template creation and editing\n * Features: Real-time preview, responsive design, advanced block library, undo/redo\n */\n\nimport React, {\n  useCallback,\n  useEffect,\n  useMemo,\n  useRef,\n  useState,\n} from 'react';\n\nimport mjml2html from 'mjml-browser';\nimport {\n  DropTargetMonitor,\n  useDrag,\n  useDrop,\n} from 'react-dnd';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\n\nimport api from '../services/api'; // Corrected import path\n// Types - Adjust path as needed\nimport {\n  Block,\n  BlockContent,\n  BlockListResponse,\n  Template,\n  TemplateDetailResponse,\n  TemplateSaveResponse,\n  UserPreference,\n} from '../types/editor';\nimport BlockEditor from './BlockEditor';\n// Components - Adjust paths as needed\nimport BlockLibrary from './BlockLibrary';\nimport EmailPreview from './EmailPreview';\nimport SaveTemplateModal from './SaveTemplateModal';\n\n// Styles - Ensure this is imported in your main application entry point\n// e.g., import '../styles/editor.css';\n\nconst API_URL = process.env.REACT_APP_API_URL || '/api'; // Use environment variable\n\n// Define Drag Item Types\nconst ItemTypes = {\n  BLOCK: 'block',\n  LIBRARY_BLOCK: 'library_block'\n};\n\ninterface EditorDragItem {\n  index: number;\n  type: typeof ItemTypes.BLOCK;\n  id: string; // Unique instance ID of the block being dragged\n}\n\ninterface LibraryDragItem {\n  block: Block; // The block definition from the library\n  type: typeof ItemTypes.LIBRARY_BLOCK;\n}\n\n// Near the top of the file, add a template cache mechanism:\n// Cache for storing generated HTML previews\nconst templateCache = new Map<string, {\n  html: string;\n  timestamp: number;\n  mjml: string;\n}>();\n\n// Cache TTL (Time To Live) in milliseconds - 5 minutes\nconst CACHE_TTL = 5 * 60 * 1000;\n\n// Add this function before the EmailEditor component definition\n// Function to generate a cache key based on blocks\nconst generateCacheKey = (blocks: Block[]): string => {\n  return blocks.map(block => {\n    const { instanceId, content } = block;\n    // Include only essential data in cache key\n    return `${block.blockId || block._id}:${instanceId}:${JSON.stringify(content)}`;\n  }).join('|');\n};\n\n// Function to clear stale cache entries\nconst clearStaleCache = () => {\n  const now = Date.now();\n  templateCache.forEach((entry, key) => {\n    if (now - entry.timestamp > CACHE_TTL) {\n      templateCache.delete(key);\n    }\n  });\n};\n\n// Enhanced state management with undo/redo functionality\ninterface EditorState {\n  blocks: Block[];\n  selectedBlockIndex: number | null;\n  timestamp: number;\n}\n\ninterface UndoRedoState {\n  history: EditorState[];\n  currentIndex: number;\n  maxHistorySize: number;\n}\n\n// --- EmailEditor Component ---\n\nconst EmailEditor: React.FC = () => {\n  const { templateId } = useParams<{ templateId?: string }>();\n  const navigate = useNavigate();\n\n  // Core State\n  const [template, setTemplate] = useState<Partial<Template>>({});\n  const [editorBlocks, setEditorBlocks] = useState<Block[]>([]);\n  const [availableBlocks, setAvailableBlocks] = useState<Block[]>([]);\n  const [selectedBlockIndex, setSelectedBlockIndex] = useState<number | null>(null);\n  const [previewHtml, setPreviewHtml] = useState<string>('');\n  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');\n  const [isSaving, setIsSaving] = useState<boolean>(false);\n  const [showSaveModal, setShowSaveModal] = useState<boolean>(false);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [saveError, setSaveError] = useState<string | null>(null);\n  const [userPreferences, setUserPreferences] = useState<UserPreference | null>(null);\n  const [templateName, setTemplateName] = useState('');\n\n  // Enhanced UI State\n  const [isDragActive, setIsDragActive] = useState<boolean>(false);\n  const [showBlockLibrary, setShowBlockLibrary] = useState<boolean>(true);\n  const [showPreview, setShowPreview] = useState<boolean>(true);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState<boolean>(false);\n  const [viewMode, setViewMode] = useState<'edit' | 'preview' | 'split'>('split');\n\n  // Undo/Redo State\n  const [undoRedoState, setUndoRedoState] = useState<UndoRedoState>({\n    history: [],\n    currentIndex: -1,\n    maxHistorySize: 50\n  });\n\n  // Performance optimization - memoized values\n  const canUndo = useMemo(() => undoRedoState.currentIndex > 0, [undoRedoState.currentIndex]);\n  const canRedo = useMemo(() => undoRedoState.currentIndex < undoRedoState.history.length - 1, [undoRedoState.currentIndex, undoRedoState.history.length]);\n\n  // Refs\n  const previewIframeRef = useRef<HTMLIFrameElement>(null);\n  const editorCanvasRef = useRef<HTMLDivElement>(null);\n\n  // --- Undo/Redo Functions --- //\n  const saveToHistory = useCallback((blocks: Block[], selectedIndex: number | null) => {\n    setUndoRedoState(prev => {\n      const newState: EditorState = {\n        blocks: JSON.parse(JSON.stringify(blocks)), // Deep clone\n        selectedBlockIndex: selectedIndex,\n        timestamp: Date.now()\n      };\n\n      // Remove any future history if we're not at the end\n      const newHistory = prev.history.slice(0, prev.currentIndex + 1);\n      newHistory.push(newState);\n\n      // Limit history size\n      if (newHistory.length > prev.maxHistorySize) {\n        newHistory.shift();\n      }\n\n      return {\n        ...prev,\n        history: newHistory,\n        currentIndex: newHistory.length - 1\n      };\n    });\n  }, []);\n\n  const undo = useCallback(() => {\n    if (!canUndo) return;\n\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex - 1;\n      const state = prev.history[newIndex];\n\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canUndo]);\n\n  const redo = useCallback(() => {\n    if (!canRedo) return;\n\n    setUndoRedoState(prev => {\n      const newIndex = prev.currentIndex + 1;\n      const state = prev.history[newIndex];\n\n      if (state) {\n        setEditorBlocks(state.blocks);\n        setSelectedBlockIndex(state.selectedBlockIndex);\n      }\n\n      return {\n        ...prev,\n        currentIndex: newIndex\n      };\n    });\n  }, [canRedo]);\n\n  // Keyboard shortcuts for undo/redo\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {\n        e.preventDefault();\n        undo();\n      } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {\n        e.preventDefault();\n        redo();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [undo, redo]);\n\n  // --- Data Fetching --- //\n  useEffect(() => {\n    let isMounted = true;\n    const fetchData = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Fetch available blocks first (always needed)\n        console.log(`Fetching blocks from ${API_URL}/blocks`);\n        // Use the shared api instance with BlockListResponse type\n        const blocksPromise = api.get<BlockListResponse>('/blocks');\n\n        // Fetch user preferences to potentially apply brand colors/fonts later\n        console.log(`Fetching user preferences from ${API_URL}/user/preferences`);\n         // Use the shared api instance with correct inline type\n        const prefsPromise = api.get<{ preferences: UserPreference }>(`/user/preferences`);\n\n        // Fetch template data if ID exists\n        const templatePromise = templateId\n          // Use the shared api instance with TemplateDetailResponse type\n          ? api.get<TemplateDetailResponse>(`/templates/${templateId}`)\n          : Promise.resolve(null);\n\n        const [blocksResponse, prefsResponse, templateResponse] = await Promise.all([\n          blocksPromise,\n          prefsPromise,\n          templatePromise\n        ]);\n\n        // Access blocks correctly from response.data.data\n        const fetchedAvailableBlocks = blocksResponse.data.data || [];\n        console.log(`Fetched ${fetchedAvailableBlocks.length} available blocks.`);\n        setAvailableBlocks(fetchedAvailableBlocks);\n\n        // Process user preferences\n        const userPreferences = prefsResponse.data.preferences; // Store preferences\n        // TODO: Apply user preferences (e.g., brand colors, fonts)\n\n        if (templateResponse && templateResponse.data.template) {\n          const templateData = templateResponse.data.template; // Correct path now\n          console.log('[EmailEditor] Fetched template data:', JSON.stringify(templateData, null, 2)); // Log fetched data\n\n          // Apply brand colors/fonts from preferences if not set on template\n          setTemplate({\n              ...templateData,\n              brandColors: templateData.brandColors || userPreferences?.brandColors,\n              defaultFonts: templateData.defaultFonts || userPreferences?.defaultFonts,\n          });\n\n          // Populate editor blocks based on blockIds and fetched definitions\n          console.log('[EmailEditor] Attempting to populate blocks. Available block defs:', fetchedAvailableBlocks.length);\n          console.log('[EmailEditor] Template blockIds:', templateData.blockIds);\n          console.log('[EmailEditor] Template blocks data:', templateData.blocks);\n\n          if (templateData.blockIds && templateData.blockIds.length > 0 && fetchedAvailableBlocks.length > 0) {\n            console.log('[EmailEditor] Conditions met, proceeding to map blockIds.');\n            const populatedBlocks = templateData.blockIds.map((id: string, index: number) => {\n              console.log(`[EmailEditor] Mapping blockId: ${id} at index: ${index}`);\n              // Match ID from templateData.blockIds with fetchedAvailableBlocks\n              const foundBlockDef = fetchedAvailableBlocks.find((b: Block) => b.blockId === id || b._id === id); // Check both blockId and _id\n              console.log(`[EmailEditor] ... Definition found for ${id}?`, foundBlockDef ? 'Yes' : 'No');\n\n              if (!foundBlockDef) {\n                console.warn(`[EmailEditor] Block definition not found for available block matching ID: ${id}. Skipping.`); // Refined warning\n                return null;\n              }\n\n              // Get content for this specific instance from templateData.blocks\n              const instanceBlockData = templateData.blocks?.[index]; // Get the block data saved for this instance\n              const instanceContent = instanceBlockData?.content || foundBlockDef.content || {}; // Use instance content, fallback to definition's default\n              console.log(`[EmailEditor] ... Instance content for ${id}:`, instanceContent);\n\n              const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`; // Unique key for React\n\n              // Return the combined block object for the editor state\n              const blockForState = {\n                  ...foundBlockDef, // Base definition (MJML, name, category etc.)\n                  content: { ...instanceContent }, // Specific content for this instance\n                  instanceId // Unique ID for this instance in the editor\n              };\n              console.log(`[EmailEditor] ... Created block object for state for ${id}:`, blockForState);\n              return blockForState;\n            }).filter(b => b !== null);\n\n            console.log(\"[EmailEditor] Populated blocks array before setting state:\", populatedBlocks);\n            setEditorBlocks(populatedBlocks as Block[]);\n          } else {\n            console.log(\"[EmailEditor] Condition for populating blocks NOT met:\", {\n                hasBlockIds: !!templateData.blockIds,\n                blockIdsLength: templateData.blockIds?.length,\n                hasFetchedBlocks: !!fetchedAvailableBlocks,\n                fetchedBlocksLength: fetchedAvailableBlocks?.length\n            });\n            setEditorBlocks([]); // Sets blocks to empty\n          }\n        } else {\n          // New template: initialize with preferences\n          console.log('Initializing new template state with preferences.');\n          setTemplate({\n            templateName: 'Untitled Template',\n            subject: 'Your Subject Here',\n            brandColors: userPreferences?.brandColors,\n            defaultFonts: userPreferences?.defaultFonts,\n            // Initialize other necessary fields if needed\n            userId: userPreferences?.userId // Important for saving later\n          });\n          setEditorBlocks([]);\n        }\n      } catch (err: any) {\n        console.error('Error loading editor data:', err);\n        let errorMsg = 'Failed to load editor data.'; // Default message\n\n        if (err.response) {\n          // Check if it's the template fetch that failed with 404\n          if (err.config?.url?.includes(`/templates/${templateId}`) && err.response.status === 404) {\n            errorMsg = `Template with ID ${templateId} not found. It may have been deleted.`;\n            // Optionally, redirect the user or clear the template state\n            // navigate('/templates'); // Example redirect\n            setTemplate({}); // Clear any partial template data\n            setEditorBlocks([]);\n          } else {\n            // Use the error message from the response if available, otherwise use the generic message\n            errorMsg = err.response.data?.error || err.response.data?.message || err.message || errorMsg;\n          }\n        } else {\n          // Network error or other issue\n          errorMsg = err.message || errorMsg;\n        }\n\n        setError(errorMsg);\n        // Fallback logic: Check if availableBlocks is empty (meaning block fetch might have also failed)\n        if (availableBlocks.length === 0) {\n            console.warn(\"Falling back to empty block list as blocks couldn't be fetched or list is empty.\");\n            setAvailableBlocks([]); // Ensure it's an empty array if blocks failed\n        }\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchData();\n\n    return () => {\n      isMounted = false;\n    };\n  // eslint-disable-next-line\n  }, [templateId]); // Rerun only when templateId changes\n\n  // --- MJML Generation & Preview Update --- //\n  const generateMjmlFromBlocks = useCallback((blockList: Block[]): string => {\n    // If there are no blocks, return early with an empty string\n    if (!blockList.length) return '';\n\n    // Generate a cache key for this block configuration\n    const cacheKey = generateCacheKey(blockList);\n\n    // Check if we have a cached version\n    if (templateCache.has(cacheKey)) {\n      const cached = templateCache.get(cacheKey);\n      if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {\n        console.log(\"[EmailEditor] Using cached MJML\");\n        return cached.mjml;\n      }\n    }\n\n    // Periodically clean up old cache entries\n    clearStaleCache();\n\n    // Process blocks in chunks if there are many\n    const chunkSize = 5;\n    let mjmlBodyContent = '';\n\n    for (let i = 0; i < blockList.length; i += chunkSize) {\n      const chunk = blockList.slice(i, i + chunkSize);\n      const chunkContent = chunk.map(block => {\n        let blockMjml = block.mjml || '';\n        const content = block.content || {};\n\n        // More robust replacement logic\n        Object.keys(content).forEach(key => {\n          const value = content[key];\n          const placeholder = `{{${key}}}`; // Standard placeholder\n\n          // Only process if value exists\n          if (value !== undefined && value !== null) {\n            // Handle arrays specifically (e.g., nav links, social icons)\n            if (Array.isArray(value)) {\n              // Existing array handling code...\n              if (key === 'nav_links' || key === 'navLinks') {\n                const linksHtml = value.map((link: any) =>\n                  `<mj-navbar-link href=\"${link.url || '#'}\" color=\"#1E3A8A\">${link.name || 'Link'}</mj-navbar-link>`\n                ).join('\\n');\n                blockMjml = blockMjml.replace('{{navLinksArea}}', linksHtml);\n              } else if (key === 'social_icons' || key === 'socialLinks') {\n                const iconsHtml = value.map((icon: any) =>\n                  `<mj-social-element name=\"${icon.platform?.toLowerCase() || 'share'}\" href=\"${icon.url || '#'}\"></mj-social-element>`\n                ).join('\\n');\n                blockMjml = blockMjml.replace('{{socialIconsArea}}', iconsHtml);\n              }\n            } else {\n              // More efficient single replacement (no array of placeholders)\n              const stringValue = String(value);\n              blockMjml = blockMjml.replaceAll(placeholder, stringValue);\n\n              // Only try alternative formats if there's a good chance they exist\n              if (blockMjml.includes('{{')) {\n                // Convert camelCase to snake_case for placeholder check if needed\n                const camelCasePlaceholder = `{{${key.replace(/_([a-z])/g, g => g[1].toUpperCase())}}}`;\n                const snakeCasePlaceholder = `{{${key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)}}}`;\n\n                blockMjml = blockMjml.replaceAll(camelCasePlaceholder, stringValue);\n                blockMjml = blockMjml.replaceAll(snakeCasePlaceholder, stringValue);\n              }\n            }\n          }\n        });\n\n        // More efficient placeholder cleanup - only if needed\n        if (blockMjml.includes('{{')) {\n          blockMjml = blockMjml.replace(/\\{\\{[\\w.-]+\\}\\}/g, '');\n        }\n\n        return blockMjml;\n      }).join('\\n');\n\n      mjmlBodyContent += chunkContent;\n    }\n\n    // Use template state for colors/fonts, falling back to defaults\n    const brandColors = template?.brandColors ?? {};\n    const defaultFonts = template?.defaultFonts ?? {};\n    const primaryColor = brandColors.primary || '#4F46E5';\n    const backgroundColor = brandColors.background || '#f3f4f6';\n    const textColor = brandColors.text || '#111827';\n    const headingFont = defaultFonts.heading || 'Arial, sans-serif';\n    const bodyFont = defaultFonts.body || 'Arial, sans-serif';\n\n    // Construct the full MJML document\n    const fullMjml = `\n<mjml>\n  <mj-head>\n    <mj-title>${template?.subject || 'Email Template'}</mj-title>\n    <mj-font name=\"Roboto\" href=\"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700\" />\n    <mj-attributes>\n      <mj-all padding=\"0px\" font-family=\"${bodyFont}\" />\n      <mj-text padding=\"10px 25px\" font-size=\"14px\" line-height=\"1.5\" color=\"${textColor}\" />\n      <mj-section padding=\"10px 0\" />\n      <mj-column padding=\"5px\" />\n      <mj-button background-color=\"${primaryColor}\" color=\"#ffffff\" font-weight=\"bold\" border-radius=\"4px\" padding=\"10px 20px\" />\n      <mj-image padding=\"0px\" />\n    </mj-attributes>\n    <mj-style inline=\"inline\">\n      /* Inline styles */\n      a { color: ${primaryColor} !important; text-decoration: none !important; }\n    </mj-style>\n     <mj-style>\n      /* Embedded styles */\n      .hover-link:hover { text-decoration: underline !important; }\n    </mj-style>\n  </mj-head>\n  <mj-body background-color=\"${backgroundColor}\">\n    ${mjmlBodyContent}\n  </mj-body>\n</mjml>`;\n\n    // Store in cache\n    templateCache.set(cacheKey, {\n      mjml: fullMjml,\n      html: '', // Will be populated on first HTML conversion\n      timestamp: Date.now()\n    });\n\n    return fullMjml;\n  }, [template]);\n\n  useEffect(() => {\n    const generatePreview = () => {\n      if (!editorBlocks || editorBlocks.length === 0) {\n        setPreviewHtml('<div style=\"display: flex; justify-content: center; align-items: center; height: 100%; color: grey; padding: 20px; text-align: center;\">Drag blocks here to build your email</div>');\n        return;\n      }\n\n      try {\n        // Generate cache key\n        const cacheKey = generateCacheKey(editorBlocks);\n\n        // Check for valid cached HTML\n        if (templateCache.has(cacheKey)) {\n          const cached = templateCache.get(cacheKey);\n          if (cached && cached.html && (Date.now() - cached.timestamp) < CACHE_TTL) {\n            console.log(\"[EmailEditor Preview] Using cached HTML\");\n            setPreviewHtml(cached.html);\n            return;\n          }\n        }\n\n        // If we get here, generate a new preview\n        const mjmlString = generateMjmlFromBlocks(editorBlocks);\n        console.log(\"[EmailEditor Preview] Generating fresh HTML preview\");\n\n        // Use a web worker for MJML conversion if possible\n        if (window.Worker) {\n          // Note: You would need to create a separate mjml-worker.js file\n          // This is just demonstrating the concept\n          const workerTimeout = setTimeout(() => {\n            // Fallback if worker takes too long\n            try {\n              const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });\n              setPreviewHtml(html);\n              // Update cache\n              const cacheKey = generateCacheKey(editorBlocks);\n              if (templateCache.has(cacheKey)) {\n                const entry = templateCache.get(cacheKey);\n                if (entry) {\n                  entry.html = html;\n                  entry.timestamp = Date.now();\n                }\n              }\n            } catch (fallbackErr: any) {\n              console.error('[EmailEditor Preview] Error in fallback conversion:', fallbackErr);\n              setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${fallbackErr?.message || 'Unknown error'}</div>`);\n            }\n          }, 1000); // 1 second timeout\n\n          // This is just conceptual - actual implementation would need the worker file\n          // const worker = new Worker('/mjml-worker.js');\n          // worker.postMessage(mjmlString);\n          // worker.onmessage = (e) => {\n          //   clearTimeout(workerTimeout);\n          //   const { html, errors } = e.data;\n          //   setPreviewHtml(html);\n          //   // Update cache\n          //   updateHtmlCache(cacheKey, html);\n          // };\n        } else {\n          // Direct conversion when Web Workers aren't available\n          const { html, errors } = mjml2html(mjmlString, { validationLevel: 'soft' });\n          if (errors && errors.length > 0) {\n            console.warn('[EmailEditor Preview] MJML Validation:', errors.length, 'issues');\n          }\n          setPreviewHtml(html);\n\n          // Update cache\n          if (templateCache.has(cacheKey)) {\n            const entry = templateCache.get(cacheKey);\n            if (entry) {\n              entry.html = html;\n              entry.timestamp = Date.now();\n            }\n          }\n        }\n      } catch (err: any) {\n        console.error('[EmailEditor Preview] Error generating preview:', err);\n        setPreviewHtml(`<div style=\"padding: 20px; color: red;\">Preview Error: ${err?.message || 'Unknown error'}</div>`);\n      }\n    };\n\n    // Debounce the preview generation to avoid too many updates\n    const debounceTimeout = setTimeout(generatePreview, 300);\n    return () => clearTimeout(debounceTimeout);\n  }, [editorBlocks, generateMjmlFromBlocks]);\n\n  // --- Enhanced DND Callbacks --- //\n  const moveBlock = useCallback((dragIndex: number, hoverIndex: number) => {\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = [...prevBlocks];\n          const [draggedBlock] = newBlocks.splice(dragIndex, 1);\n          newBlocks.splice(hoverIndex, 0, draggedBlock);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, selectedBlockIndex === dragIndex ? hoverIndex : selectedBlockIndex);\n\n          return newBlocks;\n      });\n\n      // Adjust selected index\n      if (selectedBlockIndex === dragIndex) {\n          setSelectedBlockIndex(hoverIndex);\n      } else if (selectedBlockIndex !== null) {\n          if (dragIndex < hoverIndex && selectedBlockIndex > dragIndex && selectedBlockIndex <= hoverIndex) {\n              setSelectedBlockIndex(s => (s !== null ? s - 1 : null));\n          } else if (dragIndex > hoverIndex && selectedBlockIndex >= hoverIndex && selectedBlockIndex < dragIndex) {\n              setSelectedBlockIndex(s => (s !== null ? s + 1 : null));\n          }\n      }\n  }, [selectedBlockIndex, saveToHistory]);\n\n  const dropBlockFromLibrary = useCallback((block: any, index: number) => {\n      // Cast the block to ensure it has all required properties\n      const blockWithRequiredProps = {\n          ...JSON.parse(JSON.stringify(block)),\n          blockId: block.blockId || block._id || `block-${Date.now()}`, // Ensure blockId is never undefined\n          content: block.content ? JSON.parse(JSON.stringify(block.content)) : {},\n          instanceId: `${block.blockId || block._id || 'new'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n      };\n\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = [...prevBlocks];\n          newBlocks.splice(index, 0, blockWithRequiredProps as Block);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, index);\n\n          return newBlocks;\n      });\n      setSelectedBlockIndex(index); // Select the newly dropped block\n  }, [saveToHistory]);\n\n  const removeBlock = useCallback((index: number) => {\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = prevBlocks.filter((_, i) => i !== index);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, selectedBlockIndex === index ? null :\n            (selectedBlockIndex !== null && selectedBlockIndex > index ? selectedBlockIndex - 1 : selectedBlockIndex));\n\n          return newBlocks;\n      });\n\n      if (selectedBlockIndex === index) {\n          setSelectedBlockIndex(null);\n      } else if (selectedBlockIndex !== null && selectedBlockIndex > index) {\n          setSelectedBlockIndex(prevIndex => (prevIndex !== null ? prevIndex - 1 : null));\n      }\n  }, [selectedBlockIndex, saveToHistory]);\n\n  const duplicateBlock = useCallback((index: number) => {\n      setEditorBlocks(prevBlocks => {\n          const blockToDuplicate = prevBlocks[index];\n          const duplicatedBlock = {\n              ...JSON.parse(JSON.stringify(blockToDuplicate)),\n              instanceId: `${blockToDuplicate.blockId || blockToDuplicate._id || 'dup'}-${Date.now()}-${Math.random().toString(36).substring(7)}`\n          };\n\n          const newBlocks = [...prevBlocks];\n          newBlocks.splice(index + 1, 0, duplicatedBlock);\n\n          // Save to history for undo/redo\n          saveToHistory(newBlocks, index + 1);\n\n          return newBlocks;\n      });\n      setSelectedBlockIndex(index + 1); // Select the duplicated block\n  }, [saveToHistory]);\n\n  const updateBlockContent = useCallback((index: number, updatedContent: Partial<BlockContent>) => {\n      setEditorBlocks(prevBlocks => {\n          const newBlocks = prevBlocks.map((block, i) =>\n              i === index\n                  ? { ...block, content: { ...(block.content || {}), ...updatedContent } }\n                  : block\n          );\n\n          // Save to history for undo/redo (debounced to avoid too many history entries)\n          const debouncedSave = setTimeout(() => {\n              saveToHistory(newBlocks, selectedBlockIndex);\n          }, 1000);\n\n          return newBlocks;\n      });\n  }, [saveToHistory, selectedBlockIndex]);\n\n  // --- Save Handler --- //\n  const handleSave = async (templateNameToSave: string, isPublicStatus: boolean = false) => {\n    setIsSaving(true);\n    setSaveError(null);\n    try {\n      const mjml = generateMjmlFromBlocks(editorBlocks);\n      const { html, errors: conversionErrors } = mjml2html(mjml);\n      if (conversionErrors && conversionErrors.length > 0) {\n        console.warn('MJML conversion errors detected during save:', conversionErrors);\n        // Consider not saving if critical errors occurred\n      }\n\n      const blockIds = editorBlocks\n        .map(b => String(b.blockId || b._id)) // Ensure string ID\n        .filter(Boolean); // Filter out any potential undefined/null\n\n      const payload: Partial<Template> & { templateId?: string; blockIds: string[] } = {\n        templateId: template?._id,\n        templateName: templateNameToSave,\n        mjml,\n        html,\n        blockIds,\n        subject: template?.subject || 'Untitled Template',\n        tags: template?.tags || [],\n        isPublic: isPublicStatus,\n        description: template?.description || '',\n        aiPrompt: template?.aiPrompt || '',\n        isAiGenerated: template?.isAiGenerated || false\n      };\n\n      // Add userId if it's a new template - CRITICAL: Ensure req.user is populated in backend\n      // This assumes the backend will get the userId from the authenticated request (authenticateJWT)\n      // if (!payload.templateId) {\n      //   payload.userId = template?.userId; // Might be set from prefs initially\n      // }\n\n      const response = await api.post<TemplateSaveResponse>('/templates/save', payload);\n      const savedTemplate = response.data.template;\n      setTemplate(savedTemplate);\n\n      // Re-sync editor blocks if necessary (e.g., if backend modifies content/IDs)\n      // This part needs careful implementation if backend modifies block content on save\n      if (savedTemplate.blockIds && availableBlocks.length > 0) {\n         const populatedBlocks = savedTemplate.blockIds.map((id, index) => {\n            const foundBlockDef = availableBlocks.find(b => b.blockId === id || b._id === id);\n            const instanceContent = savedTemplate.blocks?.[index]?.content || foundBlockDef?.content || {};\n            const instanceId = `${id}-${index}-${Date.now()}-${Math.random().toString(36).substring(7)}`;\n            return foundBlockDef ? { ...foundBlockDef, content: { ...instanceContent }, instanceId } : null;\n         }).filter((b) => b !== null) as Block[];\n         setEditorBlocks(populatedBlocks);\n      }\n\n      setShowSaveModal(false);\n      console.log('Template saved successfully!');\n      if (!templateId && savedTemplate._id) {\n        navigate(`/email-editor/${savedTemplate._id}`, { replace: true });\n      }\n      return savedTemplate;\n    } catch (err: any) {\n      console.error('Error saving template:', err);\n      const errorMessage = err.response?.data?.error || err.message || 'Failed to save template.';\n      setSaveError(errorMessage);\n      return null;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // --- Drop Target for Canvas --- //\n  const [{ isOverCanvas }, drop] = useDrop(() => ({\n      accept: [ItemTypes.BLOCK, ItemTypes.LIBRARY_BLOCK],\n      drop: (item: EditorDragItem | LibraryDragItem, monitor) => {\n          if (monitor.didDrop()) return;\n\n          const editorDiv = editorCanvasRef.current;\n          const clientOffset = monitor.getClientOffset();\n          if (!clientOffset || !editorDiv) return;\n\n          const hoverIndex = findDropIndex(clientOffset.y, editorDiv);\n\n          if (item.type === ItemTypes.LIBRARY_BLOCK) {\n            dropBlockFromLibrary((item as LibraryDragItem).block, hoverIndex);\n          }\n          // Reordering is handled by DraggableBlock's hover\n      },\n      collect: monitor => ({\n        isOverCanvas: monitor.isOver({ shallow: true }),\n      }),\n  }), [editorBlocks, moveBlock, dropBlockFromLibrary]); // Ensure correct dependencies\n\n  // Helper function to find the correct drop index\n  const findDropIndex = (clientY: number | undefined, container: HTMLDivElement): number => {\n      if (clientY === undefined) return editorBlocks.length;\n\n      const containerRect = container.getBoundingClientRect();\n      const offsetY = clientY - containerRect.top + container.scrollTop;\n\n      let calculatedIndex = editorBlocks.length;\n      const children = Array.from(container.children) as HTMLElement[];\n\n      for (let i = 0; i < children.length; i++) {\n          const child = children[i];\n          if (!child.classList || !child.classList.contains('draggable-block')) continue;\n\n          const childTop = child.offsetTop;\n          const childHeight = child.offsetHeight;\n          const middleY = childTop + childHeight / 2;\n\n          if (offsetY < middleY) {\n              calculatedIndex = i;\n              break;\n          }\n      }\n      return calculatedIndex;\n  };\n\n  // Cleanup cache when component unmounts\n  useEffect(() => {\n    return () => {\n      // Clear the entire cache when this editor instance is unmounted\n      templateCache.clear();\n    };\n  }, []);\n\n  // Add a cleanup function for memory management\n  const cleanupUnusedResources = useCallback(() => {\n    // Clear any unused thumbnails from memory if browser gets low on memory\n    if ('memory' in performance) {\n      const memoryInfo = (performance as any).memory;\n      if (memoryInfo.usedJSHeapSize > memoryInfo.jsHeapSizeLimit * 0.8) {\n        console.log('[EmailEditor] Memory pressure detected, cleaning up resources');\n        // Force a garbage collection if possible\n        clearStaleCache();\n        // You could also unload any non-visible thumbnails or other large objects\n      }\n    }\n  }, []);\n\n  // Listen for low memory events\n  useEffect(() => {\n    if ('onmemorywarning' in window) {\n      (window as any).addEventListener('memorywarning', cleanupUnusedResources);\n      return () => {\n        (window as any).removeEventListener('memorywarning', cleanupUnusedResources);\n      };\n    }\n  }, [cleanupUnusedResources]);\n\n  // --- Render Logic --- //\n  if (isLoading) {\n    return <div className=\"flex justify-center items-center h-screen text-gray-600 text-lg\">Loading Editor...</div>;\n  }\n\n  // Critical error on initial load\n  if (error && !template) {\n    return <div className=\"p-4 m-4 text-red-700 bg-red-100 border border-red-400 rounded\">Error loading editor configuration: {error}</div>;\n  }\n\n  return (\n    <div className=\"email-editor flex flex-col h-screen bg-gray-100\">\n      {/* Enhanced Toolbar */}\n      <div className=\"editor-toolbar flex items-center justify-between px-4 py-2 bg-white border-b border-gray-200 shadow-sm\">\n        <div className=\"toolbar-left flex items-center space-x-4\">\n          <h1 className=\"text-lg font-semibold text-gray-800\">\n            {template?.templateName || 'Untitled Template'}\n          </h1>\n\n          {/* Undo/Redo Controls */}\n          <div className=\"flex items-center space-x-1\">\n            <button\n              onClick={undo}\n              disabled={!canUndo}\n              className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              title=\"Undo (Ctrl+Z)\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6\" />\n              </svg>\n            </button>\n            <button\n              onClick={redo}\n              disabled={!canRedo}\n              className=\"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              title=\"Redo (Ctrl+Y)\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"toolbar-center flex items-center\">\n          {/* View Mode Toggle */}\n          <div className=\"preview-mode-toggle flex items-center bg-gray-100 rounded-lg p-1\">\n            <button\n              onClick={() => setViewMode('edit')}\n              className={`toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                viewMode === 'edit' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n              </svg>\n              Edit\n            </button>\n            <button\n              onClick={() => setViewMode('split')}\n              className={`toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                viewMode === 'split' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2\" />\n              </svg>\n              Split\n            </button>\n            <button\n              onClick={() => setViewMode('preview')}\n              className={`toggle-button flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                viewMode === 'preview' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n              </svg>\n              Preview\n            </button>\n          </div>\n        </div>\n\n        <div className=\"toolbar-right flex items-center space-x-3\">\n          {/* Preview Mode Toggle */}\n          <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n            <button\n              onClick={() => setPreviewMode('desktop')}\n              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                previewMode === 'desktop' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Desktop\n            </button>\n            <button\n              onClick={() => setPreviewMode('mobile')}\n              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${\n                previewMode === 'mobile' ? 'bg-white text-indigo-600 shadow-sm' : 'text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              Mobile\n            </button>\n          </div>\n\n          {/* Save Button */}\n          <button\n            onClick={() => {\n              setTemplate(prev => ({ ...(prev || {}), templateName: prev?.templateName || 'Untitled Template' } as Template));\n              setSaveError(null);\n              setShowSaveModal(true);\n            }}\n            disabled={isSaving}\n            className=\"save-button px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors disabled:bg-indigo-400 disabled:cursor-not-allowed\"\n          >\n            {isSaving ? 'Saving...' : 'Save Template'}\n          </button>\n        </div>\n      </div>\n\n      {/* Display non-critical error (e.g., failed suggestion) */}\n      {error && templateId &&\n          <div className=\"p-2 text-sm text-red-700 bg-red-100 border-b border-red-300 text-center\">Error: {error}</div>\n      }\n\n      <div className={`editor-content flex flex-1 overflow-hidden`}>\n        {/* Block Library Panel */}\n        <div className=\"block-library-panel w-64 bg-white border-r border-gray-200 flex flex-col overflow-hidden shrink-0\">\n          <h3 className=\"px-4 py-3 text-base font-semibold text-gray-800 border-b border-gray-200 whitespace-nowrap\">Block Library</h3>\n          <BlockLibrary\n            blocks={availableBlocks}\n            onAddBlock={(block) => dropBlockFromLibrary(block, editorBlocks.length)} // Click adds to end\n          />\n        </div>\n\n        {/* Editor Workspace / Canvas */}\n        <div ref={drop} className={`editor-workspace flex-1 flex flex-col overflow-hidden bg-gray-200 ${isOverCanvas ? 'outline-2 outline-dashed outline-indigo-500' : ''}`}>\n          <div ref={editorCanvasRef} className=\"blocks-container flex-1 p-4 md:p-6 lg:p-8 overflow-y-auto\">\n            {editorBlocks.length === 0 ? (\n              <div className=\"empty-blocks flex items-center justify-center h-full text-gray-500 text-center p-8 border-2 border-dashed border-gray-300 rounded-lg min-h-[200px]\">\n                <p>Drag blocks from the library here<br/>or click a block in the library to add it.</p>\n              </div>\n            ) : (\n              editorBlocks.map((block, index) => (\n                <DraggableBlock\n                  key={block.instanceId} // Use unique instanceId\n                  block={block}\n                  index={index}\n                  moveBlock={moveBlock}\n                  removeBlock={removeBlock}\n                  duplicateBlock={duplicateBlock}\n                  isSelected={selectedBlockIndex === index}\n                  onClick={() => setSelectedBlockIndex(index)}\n                />\n              ))\n            )}\n          </div>\n        </div>\n\n        {/* Right Panel (Toggles Preview/Editor) */}\n        <div className=\"right-panel w-80 md:w-96 lg:w-[500px] border-l border-gray-300 flex flex-col shrink-0 bg-white shadow-lg\">\n            {(selectedBlockIndex === null || editorBlocks.length === 0) ? (\n                // Show Preview when no block is selected OR if editor is empty\n                <div className=\"preview-panel flex-1 flex flex-col overflow-hidden\">\n                    <h3 className=\"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200\">Preview</h3>\n                    <EmailPreview\n                        html={previewHtml}\n                        mode={previewMode}\n                        iframeRef={previewIframeRef}\n                    />\n                </div>\n            ) : (\n                // Show Block Editor when a block is selected\n                <div className=\"block-editor-panel flex-1 flex flex-col overflow-hidden\">\n                    <h3 className=\"px-4 py-3 text-base font-semibold text-gray-700 border-b border-gray-200 flex justify-between items-center\">\n                      <span>Edit: {editorBlocks[selectedBlockIndex]?.name || 'Block'}</span>\n                      <button\n                        onClick={() => setSelectedBlockIndex(null)}\n                        className=\"text-sm text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100\"\n                        title=\"Close Editor\"\n                      >\n                        ✕\n                      </button>\n                    </h3>\n                    <div className=\"flex-1 overflow-y-auto\">\n                        {editorBlocks[selectedBlockIndex] && ( // Check block exists before rendering\n                            <BlockEditor\n                                block={editorBlocks[selectedBlockIndex]}\n                                onUpdate={(content) => {\n                                    if (selectedBlockIndex !== null) {\n                                        updateBlockContent(selectedBlockIndex, content);\n                                    }\n                                }}\n                            />\n                        )}\n                    </div>\n                </div>\n            )}\n        </div>\n\n      </div>\n\n      {/* Save Modal */}\n      {showSaveModal && (\n        <SaveTemplateModal\n          initialName={template?.templateName || 'Untitled Template'}\n          onSave={handleSave}\n          onCancel={() => { setShowSaveModal(false); setSaveError(null); }}\n          isSaving={isSaving}\n          error={saveError} // Pass save error to modal\n        />\n      )}\n    </div>\n  );\n};\n\n// Draggable Block Component (Internal to EmailEditor)\ninterface DraggableBlockProps {\n  block: Block;\n  index: number;\n  moveBlock: (dragIndex: number, hoverIndex: number) => void;\n  removeBlock: (index: number) => void;\n  duplicateBlock: (index: number) => void;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nconst DraggableBlock: React.FC<DraggableBlockProps> = ({\n  block,\n  index,\n  moveBlock,\n  removeBlock,\n  duplicateBlock,\n  isSelected,\n  onClick\n}) => {\n  const ref = useRef<HTMLDivElement>(null);\n\n  const [{ handlerId }, drop] = useDrop<EditorDragItem, void, { handlerId: string | symbol | null }>(() => ({\n    accept: ItemTypes.BLOCK,\n    hover: (item: EditorDragItem, monitor: DropTargetMonitor<EditorDragItem, void>) => {\n      if (!ref.current) return;\n      const dragIndex = item.index;\n      const hoverIndex = index;\n      if (dragIndex === hoverIndex) return;\n\n      const hoverBoundingRect = ref.current.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n      const hoverClientY = clientOffset.y - hoverBoundingRect.top;\n\n      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;\n      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;\n\n      moveBlock(dragIndex, hoverIndex);\n      item.index = hoverIndex; // Mutate monitor item for performance\n    },\n    collect: (monitor) => ({\n        handlerId: monitor.getHandlerId(),\n    }),\n  }), [index, moveBlock]); // Dependencies for useDrop hover logic\n\n  const [{ isDragging }, drag] = useDrag(() => ({\n    type: ItemTypes.BLOCK,\n    item: { index, id: block.instanceId, type: ItemTypes.BLOCK }, // Return item as a function\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging()\n    })\n  }), [index, block.instanceId]); // Dependencies for useDrag\n\n  drag(drop(ref)); // Combine drag and drop refs\n\n  return (\n    <div\n      ref={ref}\n      data-handler-id={handlerId}\n      className={`draggable-block bg-white border rounded-md mb-4 cursor-move shadow-sm ${isDragging ? 'opacity-40 border-blue-500' : 'hover:border-blue-400 hover:shadow-md'} ${isSelected ? 'border-blue-500 ring-2 ring-blue-300 ring-offset-1' : 'border-gray-200'}`} // styles/editor.css\n      onClick={onClick}\n      style={{ opacity: isDragging ? 0.4 : 1 }}\n    >\n      <div className=\"block-header flex items-center justify-between px-3 py-1.5 border-b border-gray-200 bg-gray-50 rounded-t-md text-xs\"> {/* styles/editor.css */}\n        <span className=\"block-type font-medium text-gray-700 truncate pr-2\" title={block.name}>{block.name} <span className=\"text-gray-400\">({block.category})</span></span>\n        <div className=\"block-actions flex items-center space-x-1\"> {/* styles/editor.css */}\n           {/* Duplicate Button */}\n           <button\n            type=\"button\"\n            className=\"p-1 text-gray-400 hover:text-blue-600 transition-colors\"\n            onClick={(e) => {\n                e.stopPropagation();\n                duplicateBlock(index);\n            }}\n            title=\"Duplicate Block\"\n           >\n               {/* Heroicon: duplicate */}\n               <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={2}>\n                   <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\" />\n               </svg>\n           </button>\n           {/* Remove Button */}\n          <button\n            type=\"button\"\n            className=\"remove-block p-1 text-gray-400 hover:text-red-600 transition-colors\" // styles/editor.css\n            onClick={(e) => {\n              e.stopPropagation();\n              removeBlock(index);\n            }}\n            title=\"Remove Block\"\n          >\n            {/* Heroicon: x */}\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={2}>\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n      {/* Simplified preview inside the draggable block */}\n      <div className=\"block-content-preview p-3 text-sm text-gray-600 bg-white rounded-b-md min-h-[50px]\">\n        {(() => {\n          // Display appropriate preview based on block type\n          if (block.blockId === 'header/simple-nav') {\n            return 'Navigation Header';\n          } else if (block.blockId === 'layout/hero') {\n            return block.content?.heroHeadline || 'Hero Section';\n          } else if (block.blockId === 'content/headline') {\n            return block.content?.headline || 'Headline';\n          } else if (block.blockId === 'product/grid') {\n            return 'Product Grid: ' + [\n              block.content?.prod1_name,\n              block.content?.prod2_name,\n              block.content?.prod3_name\n            ].filter(Boolean).join(', ');\n          } else if (block.blockId === 'cta/button') {\n            return block.content?.buttonText || 'Button';\n          } else if (block.blockId === 'footer/standard') {\n            return block.content?.companyName ? `Footer: ${block.content.companyName}` : 'Standard Footer';\n          } else {\n            // Fallback to original logic\n            return block.content?.headline ||\n                   block.content?.body?.substring(0, 50) +\n                   (block.content?.body && block.content.body.length > 50 ? '...' : '') ||\n                   block.name;\n          }\n        })()}\n        {block.thumbnail && <img src={block.thumbnail} alt={`${block.name} thumbnail`} className=\"mx-auto h-12 mt-2 opacity-75 object-contain\"/>}\n      </div>\n    </div>\n  );\n};\n\nexport { EmailEditor };\nexport default EmailEditor;"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IACVC,WAAW,EACXC,SAAS,EACTC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,OAAO;AAEd,OAAOC,SAAS,MAAM,cAAc;AACpC,SAEEC,OAAO,EACPC,OAAO,QACF,WAAW;AAClB,SACEC,WAAW,EACXC,SAAS,QACJ,kBAAkB;AAEzB,OAAOC,GAAG,MAAM,iBAAiB,CAAC,CAAC;AACnC;;AAUA,OAAOC,WAAW,MAAM,eAAe;AACvC;AACA,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,qBAAqB;;AAEnD;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,MAAM,CAAC,CAAC;;AAEzD;AACA,MAAMC,SAAS,GAAG;EAChBC,KAAK,EAAE,OAAO;EACdC,aAAa,EAAE;AACjB,CAAC;AAaD;AACA;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAI1B,CAAC;;AAEJ;AACA,MAAMC,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;;AAE/B;AACA;AACA,MAAMC,gBAAgB,GAAIC,MAAe,IAAa;EACpD,OAAOA,MAAM,CAACC,GAAG,CAACC,KAAK,IAAI;IACzB,MAAM;MAAEC,UAAU;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IACrC;IACA,OAAO,GAAGA,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAIH,UAAU,IAAII,IAAI,CAACC,SAAS,CAACJ,OAAO,CAAC,EAAE;EACjF,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;AACd,CAAC;;AAED;AACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC5B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;EACtBf,aAAa,CAACiB,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;IACpC,IAAIJ,GAAG,GAAGG,KAAK,CAACE,SAAS,GAAGlB,SAAS,EAAE;MACrCF,aAAa,CAACqB,MAAM,CAACF,GAAG,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;;AAaA;;AAEA,MAAMG,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAClC,MAAM;IAAEC;EAAW,CAAC,GAAGxC,SAAS,CAA0B,CAAC;EAC3D,MAAMyC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAoB,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAU,EAAE,CAAC;EAC7D,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAU,EAAE,CAAC;EACnE,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAgB,IAAI,CAAC;EACjF,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAuB,SAAS,CAAC;EAC/E,MAAM,CAAC2D,QAAQ,EAAEC,WAAW,CAAC,GAAG5D,QAAQ,CAAU,KAAK,CAAC;EACxD,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAU,KAAK,CAAC;EAClE,MAAM,CAAC+D,SAAS,EAAEC,YAAY,CAAC,GAAGhE,QAAQ,CAAU,IAAI,CAAC;EACzD,MAAM,CAACiE,KAAK,EAAEC,QAAQ,CAAC,GAAGlE,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAgB,IAAI,CAAC;EAC/D,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAwB,IAAI,CAAC;EACnF,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;;EAEpD;EACA,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAU,KAAK,CAAC;EAChE,MAAM,CAAC2E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5E,QAAQ,CAAU,IAAI,CAAC;EACvE,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAU,IAAI,CAAC;EAC7D,MAAM,CAAC+E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhF,QAAQ,CAAU,KAAK,CAAC;EAC9E,MAAM,CAACiF,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAA+B,OAAO,CAAC;;EAE/E;EACA,MAAM,CAACmF,aAAa,EAAEC,gBAAgB,CAAC,GAAGpF,QAAQ,CAAgB;IAChEqF,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,CAAC,CAAC;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG1F,OAAO,CAAC,MAAMqF,aAAa,CAACG,YAAY,GAAG,CAAC,EAAE,CAACH,aAAa,CAACG,YAAY,CAAC,CAAC;EAC3F,MAAMG,OAAO,GAAG3F,OAAO,CAAC,MAAMqF,aAAa,CAACG,YAAY,GAAGH,aAAa,CAACE,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE,CAACP,aAAa,CAACG,YAAY,EAAEH,aAAa,CAACE,OAAO,CAACK,MAAM,CAAC,CAAC;;EAExJ;EACA,MAAMC,gBAAgB,GAAG5F,MAAM,CAAoB,IAAI,CAAC;EACxD,MAAM6F,eAAe,GAAG7F,MAAM,CAAiB,IAAI,CAAC;;EAEpD;EACA,MAAM8F,aAAa,GAAGjG,WAAW,CAAC,CAAC4B,MAAe,EAAEsE,aAA4B,KAAK;IACnFV,gBAAgB,CAACW,IAAI,IAAI;MACvB,MAAMC,QAAqB,GAAG;QAC5BxE,MAAM,EAAEO,IAAI,CAACkE,KAAK,CAAClE,IAAI,CAACC,SAAS,CAACR,MAAM,CAAC,CAAC;QAAE;QAC5C6B,kBAAkB,EAAEyC,aAAa;QACjCtD,SAAS,EAAEJ,IAAI,CAACD,GAAG,CAAC;MACtB,CAAC;;MAED;MACA,MAAM+D,UAAU,GAAGH,IAAI,CAACV,OAAO,CAACc,KAAK,CAAC,CAAC,EAAEJ,IAAI,CAACT,YAAY,GAAG,CAAC,CAAC;MAC/DY,UAAU,CAACE,IAAI,CAACJ,QAAQ,CAAC;;MAEzB;MACA,IAAIE,UAAU,CAACR,MAAM,GAAGK,IAAI,CAACR,cAAc,EAAE;QAC3CW,UAAU,CAACG,KAAK,CAAC,CAAC;MACpB;MAEA,OAAO;QACL,GAAGN,IAAI;QACPV,OAAO,EAAEa,UAAU;QACnBZ,YAAY,EAAEY,UAAU,CAACR,MAAM,GAAG;MACpC,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,IAAI,GAAG1G,WAAW,CAAC,MAAM;IAC7B,IAAI,CAAC4F,OAAO,EAAE;IAEdJ,gBAAgB,CAACW,IAAI,IAAI;MACvB,MAAMQ,QAAQ,GAAGR,IAAI,CAACT,YAAY,GAAG,CAAC;MACtC,MAAMkB,KAAK,GAAGT,IAAI,CAACV,OAAO,CAACkB,QAAQ,CAAC;MAEpC,IAAIC,KAAK,EAAE;QACTtD,eAAe,CAACsD,KAAK,CAAChF,MAAM,CAAC;QAC7B8B,qBAAqB,CAACkD,KAAK,CAACnD,kBAAkB,CAAC;MACjD;MAEA,OAAO;QACL,GAAG0C,IAAI;QACPT,YAAY,EAAEiB;MAChB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACf,OAAO,CAAC,CAAC;EAEb,MAAMiB,IAAI,GAAG7G,WAAW,CAAC,MAAM;IAC7B,IAAI,CAAC6F,OAAO,EAAE;IAEdL,gBAAgB,CAACW,IAAI,IAAI;MACvB,MAAMQ,QAAQ,GAAGR,IAAI,CAACT,YAAY,GAAG,CAAC;MACtC,MAAMkB,KAAK,GAAGT,IAAI,CAACV,OAAO,CAACkB,QAAQ,CAAC;MAEpC,IAAIC,KAAK,EAAE;QACTtD,eAAe,CAACsD,KAAK,CAAChF,MAAM,CAAC;QAC7B8B,qBAAqB,CAACkD,KAAK,CAACnD,kBAAkB,CAAC;MACjD;MAEA,OAAO;QACL,GAAG0C,IAAI;QACPT,YAAY,EAAEiB;MAChB,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACd,OAAO,CAAC,CAAC;;EAEb;EACA5F,SAAS,CAAC,MAAM;IACd,MAAM6G,aAAa,GAAIC,CAAgB,IAAK;MAC1C,IAAI,CAACA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,KAAKF,CAAC,CAACpE,GAAG,KAAK,GAAG,IAAI,CAACoE,CAAC,CAACG,QAAQ,EAAE;QAC5DH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBT,IAAI,CAAC,CAAC;MACR,CAAC,MAAM,IAAI,CAACK,CAAC,CAACC,OAAO,IAAID,CAAC,CAACE,OAAO,MAAMF,CAAC,CAACpE,GAAG,KAAK,GAAG,IAAKoE,CAAC,CAACpE,GAAG,KAAK,GAAG,IAAIoE,CAAC,CAACG,QAAS,CAAC,EAAE;QACvFH,CAAC,CAACI,cAAc,CAAC,CAAC;QAClBN,IAAI,CAAC,CAAC;MACR;IACF,CAAC;IAEDO,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;IACjD,OAAO,MAAMM,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;EACnE,CAAC,EAAE,CAACJ,IAAI,EAAEG,IAAI,CAAC,CAAC;;EAEhB;EACA5G,SAAS,CAAC,MAAM;IACd,IAAIsH,SAAS,GAAG,IAAI;IACpB,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BpD,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACF;QACAmD,OAAO,CAACC,GAAG,CAAC,wBAAwBzG,OAAO,SAAS,CAAC;QACrD;QACA,MAAM0G,aAAa,GAAGjH,GAAG,CAACkH,GAAG,CAAoB,SAAS,CAAC;;QAE3D;QACAH,OAAO,CAACC,GAAG,CAAC,kCAAkCzG,OAAO,mBAAmB,CAAC;QACxE;QACD,MAAM4G,YAAY,GAAGnH,GAAG,CAACkH,GAAG,CAAkC,mBAAmB,CAAC;;QAElF;QACA,MAAME,eAAe,GAAG7E;QACtB;QAAA,EACEvC,GAAG,CAACkH,GAAG,CAAyB,cAAc3E,UAAU,EAAE,CAAC,GAC3D8E,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;QAEzB,MAAM,CAACC,cAAc,EAAEC,aAAa,EAAEC,gBAAgB,CAAC,GAAG,MAAMJ,OAAO,CAACK,GAAG,CAAC,CAC1ET,aAAa,EACbE,YAAY,EACZC,eAAe,CAChB,CAAC;;QAEF;QACA,MAAMO,sBAAsB,GAAGJ,cAAc,CAACK,IAAI,CAACA,IAAI,IAAI,EAAE;QAC7Db,OAAO,CAACC,GAAG,CAAC,WAAWW,sBAAsB,CAACvC,MAAM,oBAAoB,CAAC;QACzEtC,kBAAkB,CAAC6E,sBAAsB,CAAC;;QAE1C;QACA,MAAM5D,eAAe,GAAGyD,aAAa,CAACI,IAAI,CAACC,WAAW,CAAC,CAAC;QACxD;;QAEA,IAAIJ,gBAAgB,IAAIA,gBAAgB,CAACG,IAAI,CAACnF,QAAQ,EAAE;UACtD,MAAMqF,YAAY,GAAGL,gBAAgB,CAACG,IAAI,CAACnF,QAAQ,CAAC,CAAC;UACrDsE,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEvF,IAAI,CAACC,SAAS,CAACoG,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;UAE5F;UACApF,WAAW,CAAC;YACR,GAAGoF,YAAY;YACfC,WAAW,EAAED,YAAY,CAACC,WAAW,KAAIhE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgE,WAAW;YACrEC,YAAY,EAAEF,YAAY,CAACE,YAAY,KAAIjE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiE,YAAY;UAC5E,CAAC,CAAC;;UAEF;UACAjB,OAAO,CAACC,GAAG,CAAC,oEAAoE,EAAEW,sBAAsB,CAACvC,MAAM,CAAC;UAChH2B,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEc,YAAY,CAACG,QAAQ,CAAC;UACtElB,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEc,YAAY,CAAC5G,MAAM,CAAC;UAEvE,IAAI4G,YAAY,CAACG,QAAQ,IAAIH,YAAY,CAACG,QAAQ,CAAC7C,MAAM,GAAG,CAAC,IAAIuC,sBAAsB,CAACvC,MAAM,GAAG,CAAC,EAAE;YAClG2B,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;YACxE,MAAMkB,eAAe,GAAGJ,YAAY,CAACG,QAAQ,CAAC9G,GAAG,CAAC,CAACgH,EAAU,EAAEC,KAAa,KAAK;cAAA,IAAAC,oBAAA;cAC/EtB,OAAO,CAACC,GAAG,CAAC,kCAAkCmB,EAAE,cAAcC,KAAK,EAAE,CAAC;cACtE;cACA,MAAME,aAAa,GAAGX,sBAAsB,CAACY,IAAI,CAAEC,CAAQ,IAAKA,CAAC,CAACjH,OAAO,KAAK4G,EAAE,IAAIK,CAAC,CAAChH,GAAG,KAAK2G,EAAE,CAAC,CAAC,CAAC;cACnGpB,OAAO,CAACC,GAAG,CAAC,0CAA0CmB,EAAE,GAAG,EAAEG,aAAa,GAAG,KAAK,GAAG,IAAI,CAAC;cAE1F,IAAI,CAACA,aAAa,EAAE;gBAClBvB,OAAO,CAAC0B,IAAI,CAAC,6EAA6EN,EAAE,aAAa,CAAC,CAAC,CAAC;gBAC5G,OAAO,IAAI;cACb;;cAEA;cACA,MAAMO,iBAAiB,IAAAL,oBAAA,GAAGP,YAAY,CAAC5G,MAAM,cAAAmH,oBAAA,uBAAnBA,oBAAA,CAAsBD,KAAK,CAAC,CAAC,CAAC;cACxD,MAAMO,eAAe,GAAG,CAAAD,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEpH,OAAO,KAAIgH,aAAa,CAAChH,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;cACnFyF,OAAO,CAACC,GAAG,CAAC,0CAA0CmB,EAAE,GAAG,EAAEQ,eAAe,CAAC;cAE7E,MAAMtH,UAAU,GAAG,GAAG8G,EAAE,IAAIC,KAAK,IAAItG,IAAI,CAACD,GAAG,CAAC,CAAC,IAAI+G,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;;cAE9F;cACA,MAAMC,aAAa,GAAG;gBAClB,GAAGV,aAAa;gBAAE;gBAClBhH,OAAO,EAAE;kBAAE,GAAGqH;gBAAgB,CAAC;gBAAE;gBACjCtH,UAAU,CAAC;cACf,CAAC;cACD0F,OAAO,CAACC,GAAG,CAAC,wDAAwDmB,EAAE,GAAG,EAAEa,aAAa,CAAC;cACzF,OAAOA,aAAa;YACtB,CAAC,CAAC,CAACC,MAAM,CAACT,CAAC,IAAIA,CAAC,KAAK,IAAI,CAAC;YAE1BzB,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAEkB,eAAe,CAAC;YAC1FtF,eAAe,CAACsF,eAA0B,CAAC;UAC7C,CAAC,MAAM;YAAA,IAAAgB,qBAAA;YACLnC,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;cAClEmC,WAAW,EAAE,CAAC,CAACrB,YAAY,CAACG,QAAQ;cACpCmB,cAAc,GAAAF,qBAAA,GAAEpB,YAAY,CAACG,QAAQ,cAAAiB,qBAAA,uBAArBA,qBAAA,CAAuB9D,MAAM;cAC7CiE,gBAAgB,EAAE,CAAC,CAAC1B,sBAAsB;cAC1C2B,mBAAmB,EAAE3B,sBAAsB,aAAtBA,sBAAsB,uBAAtBA,sBAAsB,CAAEvC;YACjD,CAAC,CAAC;YACFxC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;UACvB;QACF,CAAC,MAAM;UACL;UACAmE,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAChEtE,WAAW,CAAC;YACVuB,YAAY,EAAE,mBAAmB;YACjCsF,OAAO,EAAE,mBAAmB;YAC5BxB,WAAW,EAAEhE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgE,WAAW;YACzCC,YAAY,EAAEjE,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiE,YAAY;YAC3C;YACAwB,MAAM,EAAEzF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyF,MAAM,CAAC;UAClC,CAAC,CAAC;UACF5G,eAAe,CAAC,EAAE,CAAC;QACrB;MACF,CAAC,CAAC,OAAO6G,GAAQ,EAAE;QACjB1C,OAAO,CAACpD,KAAK,CAAC,4BAA4B,EAAE8F,GAAG,CAAC;QAChD,IAAIC,QAAQ,GAAG,6BAA6B,CAAC,CAAC;;QAE9C,IAAID,GAAG,CAACE,QAAQ,EAAE;UAAA,IAAAC,WAAA,EAAAC,eAAA;UAChB;UACA,IAAI,CAAAD,WAAA,GAAAH,GAAG,CAACK,MAAM,cAAAF,WAAA,gBAAAC,eAAA,GAAVD,WAAA,CAAYG,GAAG,cAAAF,eAAA,eAAfA,eAAA,CAAiBG,QAAQ,CAAC,cAAczH,UAAU,EAAE,CAAC,IAAIkH,GAAG,CAACE,QAAQ,CAACM,MAAM,KAAK,GAAG,EAAE;YACxFP,QAAQ,GAAG,oBAAoBnH,UAAU,uCAAuC;YAChF;YACA;YACAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjBE,eAAe,CAAC,EAAE,CAAC;UACrB,CAAC,MAAM;YAAA,IAAAsH,kBAAA,EAAAC,mBAAA;YACL;YACAT,QAAQ,GAAG,EAAAQ,kBAAA,GAAAT,GAAG,CAACE,QAAQ,CAAC/B,IAAI,cAAAsC,kBAAA,uBAAjBA,kBAAA,CAAmBvG,KAAK,OAAAwG,mBAAA,GAAIV,GAAG,CAACE,QAAQ,CAAC/B,IAAI,cAAAuC,mBAAA,uBAAjBA,mBAAA,CAAmBC,OAAO,KAAIX,GAAG,CAACW,OAAO,IAAIV,QAAQ;UAC9F;QACF,CAAC,MAAM;UACL;UACAA,QAAQ,GAAGD,GAAG,CAACW,OAAO,IAAIV,QAAQ;QACpC;QAEA9F,QAAQ,CAAC8F,QAAQ,CAAC;QAClB;QACA,IAAI7G,eAAe,CAACuC,MAAM,KAAK,CAAC,EAAE;UAC9B2B,OAAO,CAAC0B,IAAI,CAAC,kFAAkF,CAAC;UAChG3F,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B;MACF,CAAC,SAAS;QACRY,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDoD,SAAS,CAAC,CAAC;IAEX,OAAO,MAAM;MACXD,SAAS,GAAG,KAAK;IACnB,CAAC;IACH;EACA,CAAC,EAAE,CAACtE,UAAU,CAAC,CAAC,CAAC,CAAC;;EAElB;EACA,MAAM8H,sBAAsB,GAAG/K,WAAW,CAAEgL,SAAkB,IAAa;IAAA,IAAAC,qBAAA,EAAAC,qBAAA;IACzE;IACA,IAAI,CAACF,SAAS,CAAClF,MAAM,EAAE,OAAO,EAAE;;IAEhC;IACA,MAAMqF,QAAQ,GAAGxJ,gBAAgB,CAACqJ,SAAS,CAAC;;IAE5C;IACA,IAAIxJ,aAAa,CAAC4J,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC/B,MAAME,MAAM,GAAG7J,aAAa,CAACoG,GAAG,CAACuD,QAAQ,CAAC;MAC1C,IAAIE,MAAM,IAAK7I,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG8I,MAAM,CAACzI,SAAS,GAAIlB,SAAS,EAAE;QACzD+F,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO2D,MAAM,CAACC,IAAI;MACpB;IACF;;IAEA;IACAhJ,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAMiJ,SAAS,GAAG,CAAC;IACnB,IAAIC,eAAe,GAAG,EAAE;IAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,CAAClF,MAAM,EAAE2F,CAAC,IAAIF,SAAS,EAAE;MACpD,MAAMG,KAAK,GAAGV,SAAS,CAACzE,KAAK,CAACkF,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC;MAC/C,MAAMI,YAAY,GAAGD,KAAK,CAAC7J,GAAG,CAACC,KAAK,IAAI;QACtC,IAAI8J,SAAS,GAAG9J,KAAK,CAACwJ,IAAI,IAAI,EAAE;QAChC,MAAMtJ,OAAO,GAAGF,KAAK,CAACE,OAAO,IAAI,CAAC,CAAC;;QAEnC;QACA6J,MAAM,CAACC,IAAI,CAAC9J,OAAO,CAAC,CAACS,OAAO,CAACE,GAAG,IAAI;UAClC,MAAMoJ,KAAK,GAAG/J,OAAO,CAACW,GAAG,CAAC;UAC1B,MAAMqJ,WAAW,GAAG,KAAKrJ,GAAG,IAAI,CAAC,CAAC;;UAElC;UACA,IAAIoJ,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;YACzC;YACA,IAAIG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;cACxB;cACA,IAAIpJ,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,UAAU,EAAE;gBAC7C,MAAMyJ,SAAS,GAAGL,KAAK,CAAClK,GAAG,CAAEwK,IAAS,IACpC,yBAAyBA,IAAI,CAAC5B,GAAG,IAAI,GAAG,qBAAqB4B,IAAI,CAACC,IAAI,IAAI,MAAM,mBAClF,CAAC,CAACjK,IAAI,CAAC,IAAI,CAAC;gBACZuJ,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,kBAAkB,EAAEH,SAAS,CAAC;cAC9D,CAAC,MAAM,IAAIzJ,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,aAAa,EAAE;gBAC1D,MAAM6J,SAAS,GAAGT,KAAK,CAAClK,GAAG,CAAE4K,IAAS;kBAAA,IAAAC,cAAA;kBAAA,OACpC,4BAA4B,EAAAA,cAAA,GAAAD,IAAI,CAACE,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAeE,WAAW,CAAC,CAAC,KAAI,OAAO,WAAWH,IAAI,CAAChC,GAAG,IAAI,GAAG,wBAAwB;gBAAA,CACvH,CAAC,CAACpI,IAAI,CAAC,IAAI,CAAC;gBACZuJ,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,qBAAqB,EAAEC,SAAS,CAAC;cACjE;YACF,CAAC,MAAM;cACL;cACA,MAAMK,WAAW,GAAGC,MAAM,CAACf,KAAK,CAAC;cACjCH,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACf,WAAW,EAAEa,WAAW,CAAC;;cAE1D;cACA,IAAIjB,SAAS,CAAClB,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC5B;gBACA,MAAMsC,oBAAoB,GAAG,KAAKrK,GAAG,CAAC4J,OAAO,CAAC,WAAW,EAAEU,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI;gBACvF,MAAMC,oBAAoB,GAAG,KAAKxK,GAAG,CAAC4J,OAAO,CAAC,QAAQ,EAAEa,MAAM,IAAI,IAAIA,MAAM,CAACR,WAAW,CAAC,CAAC,EAAE,CAAC,IAAI;gBAEjGhB,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACC,oBAAoB,EAAEH,WAAW,CAAC;gBACnEjB,SAAS,GAAGA,SAAS,CAACmB,UAAU,CAACI,oBAAoB,EAAEN,WAAW,CAAC;cACrE;YACF;UACF;QACF,CAAC,CAAC;;QAEF;QACA,IAAIjB,SAAS,CAAClB,QAAQ,CAAC,IAAI,CAAC,EAAE;UAC5BkB,SAAS,GAAGA,SAAS,CAACW,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC;QACvD;QAEA,OAAOX,SAAS;MAClB,CAAC,CAAC,CAACvJ,IAAI,CAAC,IAAI,CAAC;MAEbmJ,eAAe,IAAIG,YAAY;IACjC;;IAEA;IACA,MAAMlD,WAAW,IAAAwC,qBAAA,GAAG9H,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsF,WAAW,cAAAwC,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;IAC/C,MAAMvC,YAAY,IAAAwC,qBAAA,GAAG/H,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuF,YAAY,cAAAwC,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;IACjD,MAAMmC,YAAY,GAAG5E,WAAW,CAAC6E,OAAO,IAAI,SAAS;IACrD,MAAMC,eAAe,GAAG9E,WAAW,CAAC+E,UAAU,IAAI,SAAS;IAC3D,MAAMC,SAAS,GAAGhF,WAAW,CAACiF,IAAI,IAAI,SAAS;IAC/C,MAAMC,WAAW,GAAGjF,YAAY,CAACkF,OAAO,IAAI,mBAAmB;IAC/D,MAAMC,QAAQ,GAAGnF,YAAY,CAACoF,IAAI,IAAI,mBAAmB;;IAEzD;IACA,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA,gBAAgB,CAAA5K,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE8G,OAAO,KAAI,gBAAgB;AACrD;AACA;AACA,2CAA2C4D,QAAQ;AACnD,+EAA+EJ,SAAS;AACxF;AACA;AACA,qCAAqCJ,YAAY;AACjD;AACA;AACA;AACA;AACA,mBAAmBA,YAAY;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+BE,eAAe;AAC9C,MAAM/B,eAAe;AACrB;AACA,QAAQ;;IAEJ;IACAhK,aAAa,CAACwM,GAAG,CAAC7C,QAAQ,EAAE;MAC1BG,IAAI,EAAEyC,QAAQ;MACdE,IAAI,EAAE,EAAE;MAAE;MACVrL,SAAS,EAAEJ,IAAI,CAACD,GAAG,CAAC;IACtB,CAAC,CAAC;IAEF,OAAOwL,QAAQ;EACjB,CAAC,EAAE,CAAC5K,QAAQ,CAAC,CAAC;EAEdlD,SAAS,CAAC,MAAM;IACd,MAAMiO,eAAe,GAAGA,CAAA,KAAM;MAC5B,IAAI,CAAC7K,YAAY,IAAIA,YAAY,CAACyC,MAAM,KAAK,CAAC,EAAE;QAC9ClC,cAAc,CAAC,oLAAoL,CAAC;QACpM;MACF;MAEA,IAAI;QACF;QACA,MAAMuH,QAAQ,GAAGxJ,gBAAgB,CAAC0B,YAAY,CAAC;;QAE/C;QACA,IAAI7B,aAAa,CAAC4J,GAAG,CAACD,QAAQ,CAAC,EAAE;UAC/B,MAAME,MAAM,GAAG7J,aAAa,CAACoG,GAAG,CAACuD,QAAQ,CAAC;UAC1C,IAAIE,MAAM,IAAIA,MAAM,CAAC4C,IAAI,IAAKzL,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG8I,MAAM,CAACzI,SAAS,GAAIlB,SAAS,EAAE;YACxE+F,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;YACtD9D,cAAc,CAACyH,MAAM,CAAC4C,IAAI,CAAC;YAC3B;UACF;QACF;;QAEA;QACA,MAAME,UAAU,GAAGpD,sBAAsB,CAAC1H,YAAY,CAAC;QACvDoE,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;;QAElE;QACA,IAAIN,MAAM,CAACgH,MAAM,EAAE;UACjB;UACA;UACA,MAAMC,aAAa,GAAGC,UAAU,CAAC,MAAM;YACrC;YACA,IAAI;cACF,MAAM;gBAAEL,IAAI;gBAAEM;cAAO,CAAC,GAAGlO,SAAS,CAAC8N,UAAU,EAAE;gBAAEK,eAAe,EAAE;cAAO,CAAC,CAAC;cAC3E5K,cAAc,CAACqK,IAAI,CAAC;cACpB;cACA,MAAM9C,QAAQ,GAAGxJ,gBAAgB,CAAC0B,YAAY,CAAC;cAC/C,IAAI7B,aAAa,CAAC4J,GAAG,CAACD,QAAQ,CAAC,EAAE;gBAC/B,MAAMzI,KAAK,GAAGlB,aAAa,CAACoG,GAAG,CAACuD,QAAQ,CAAC;gBACzC,IAAIzI,KAAK,EAAE;kBACTA,KAAK,CAACuL,IAAI,GAAGA,IAAI;kBACjBvL,KAAK,CAACE,SAAS,GAAGJ,IAAI,CAACD,GAAG,CAAC,CAAC;gBAC9B;cACF;YACF,CAAC,CAAC,OAAOkM,WAAgB,EAAE;cACzBhH,OAAO,CAACpD,KAAK,CAAC,qDAAqD,EAAEoK,WAAW,CAAC;cACjF7K,cAAc,CAAC,0DAA0D,CAAA6K,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE3D,OAAO,KAAI,eAAe,QAAQ,CAAC;YAC3H;UACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;UAEV;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACF,CAAC,MAAM;UACL;UACA,MAAM;YAAEmD,IAAI;YAAEM;UAAO,CAAC,GAAGlO,SAAS,CAAC8N,UAAU,EAAE;YAAEK,eAAe,EAAE;UAAO,CAAC,CAAC;UAC3E,IAAID,MAAM,IAAIA,MAAM,CAACzI,MAAM,GAAG,CAAC,EAAE;YAC/B2B,OAAO,CAAC0B,IAAI,CAAC,wCAAwC,EAAEoF,MAAM,CAACzI,MAAM,EAAE,QAAQ,CAAC;UACjF;UACAlC,cAAc,CAACqK,IAAI,CAAC;;UAEpB;UACA,IAAIzM,aAAa,CAAC4J,GAAG,CAACD,QAAQ,CAAC,EAAE;YAC/B,MAAMzI,KAAK,GAAGlB,aAAa,CAACoG,GAAG,CAACuD,QAAQ,CAAC;YACzC,IAAIzI,KAAK,EAAE;cACTA,KAAK,CAACuL,IAAI,GAAGA,IAAI;cACjBvL,KAAK,CAACE,SAAS,GAAGJ,IAAI,CAACD,GAAG,CAAC,CAAC;YAC9B;UACF;QACF;MACF,CAAC,CAAC,OAAO4H,GAAQ,EAAE;QACjB1C,OAAO,CAACpD,KAAK,CAAC,iDAAiD,EAAE8F,GAAG,CAAC;QACrEvG,cAAc,CAAC,0DAA0D,CAAAuG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEW,OAAO,KAAI,eAAe,QAAQ,CAAC;MACnH;IACF,CAAC;;IAED;IACA,MAAM4D,eAAe,GAAGJ,UAAU,CAACJ,eAAe,EAAE,GAAG,CAAC;IACxD,OAAO,MAAMS,YAAY,CAACD,eAAe,CAAC;EAC5C,CAAC,EAAE,CAACrL,YAAY,EAAE0H,sBAAsB,CAAC,CAAC;;EAE1C;EACA,MAAM6D,SAAS,GAAG5O,WAAW,CAAC,CAAC6O,SAAiB,EAAEC,UAAkB,KAAK;IACrExL,eAAe,CAACyL,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjC,MAAM,CAACE,YAAY,CAAC,GAAGD,SAAS,CAACE,MAAM,CAACL,SAAS,EAAE,CAAC,CAAC;MACrDG,SAAS,CAACE,MAAM,CAACJ,UAAU,EAAE,CAAC,EAAEG,YAAY,CAAC;;MAE7C;MACAhJ,aAAa,CAAC+I,SAAS,EAAEvL,kBAAkB,KAAKoL,SAAS,GAAGC,UAAU,GAAGrL,kBAAkB,CAAC;MAE5F,OAAOuL,SAAS;IACpB,CAAC,CAAC;;IAEF;IACA,IAAIvL,kBAAkB,KAAKoL,SAAS,EAAE;MAClCnL,qBAAqB,CAACoL,UAAU,CAAC;IACrC,CAAC,MAAM,IAAIrL,kBAAkB,KAAK,IAAI,EAAE;MACpC,IAAIoL,SAAS,GAAGC,UAAU,IAAIrL,kBAAkB,GAAGoL,SAAS,IAAIpL,kBAAkB,IAAIqL,UAAU,EAAE;QAC9FpL,qBAAqB,CAACyL,CAAC,IAAKA,CAAC,KAAK,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAK,CAAC;MAC3D,CAAC,MAAM,IAAIN,SAAS,GAAGC,UAAU,IAAIrL,kBAAkB,IAAIqL,UAAU,IAAIrL,kBAAkB,GAAGoL,SAAS,EAAE;QACrGnL,qBAAqB,CAACyL,CAAC,IAAKA,CAAC,KAAK,IAAI,GAAGA,CAAC,GAAG,CAAC,GAAG,IAAK,CAAC;MAC3D;IACJ;EACJ,CAAC,EAAE,CAAC1L,kBAAkB,EAAEwC,aAAa,CAAC,CAAC;EAEvC,MAAMmJ,oBAAoB,GAAGpP,WAAW,CAAC,CAAC8B,KAAU,EAAEgH,KAAa,KAAK;IACpE;IACA,MAAMuG,sBAAsB,GAAG;MAC3B,GAAGlN,IAAI,CAACkE,KAAK,CAAClE,IAAI,CAACC,SAAS,CAACN,KAAK,CAAC,CAAC;MACpCG,OAAO,EAAEH,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAI,SAASM,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;MAAE;MAC9DP,OAAO,EAAEF,KAAK,CAACE,OAAO,GAAGG,IAAI,CAACkE,KAAK,CAAClE,IAAI,CAACC,SAAS,CAACN,KAAK,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;MACvED,UAAU,EAAE,GAAGD,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,GAAG,IAAI,KAAK,IAAIM,IAAI,CAACD,GAAG,CAAC,CAAC,IAAI+G,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;IAC/G,CAAC;IAEDnG,eAAe,CAACyL,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjCC,SAAS,CAACE,MAAM,CAACpG,KAAK,EAAE,CAAC,EAAEuG,sBAA+B,CAAC;;MAE3D;MACApJ,aAAa,CAAC+I,SAAS,EAAElG,KAAK,CAAC;MAE/B,OAAOkG,SAAS;IACpB,CAAC,CAAC;IACFtL,qBAAqB,CAACoF,KAAK,CAAC,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC7C,aAAa,CAAC,CAAC;EAEnB,MAAMqJ,WAAW,GAAGtP,WAAW,CAAE8I,KAAa,IAAK;IAC/CxF,eAAe,CAACyL,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAGD,UAAU,CAACpF,MAAM,CAAC,CAAC4F,CAAC,EAAE9D,CAAC,KAAKA,CAAC,KAAK3C,KAAK,CAAC;;MAE1D;MACA7C,aAAa,CAAC+I,SAAS,EAAEvL,kBAAkB,KAAKqF,KAAK,GAAG,IAAI,GACzDrF,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,GAAGqF,KAAK,GAAGrF,kBAAkB,GAAG,CAAC,GAAGA,kBAAmB,CAAC;MAE5G,OAAOuL,SAAS;IACpB,CAAC,CAAC;IAEF,IAAIvL,kBAAkB,KAAKqF,KAAK,EAAE;MAC9BpF,qBAAqB,CAAC,IAAI,CAAC;IAC/B,CAAC,MAAM,IAAID,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,GAAGqF,KAAK,EAAE;MAClEpF,qBAAqB,CAAC8L,SAAS,IAAKA,SAAS,KAAK,IAAI,GAAGA,SAAS,GAAG,CAAC,GAAG,IAAK,CAAC;IACnF;EACJ,CAAC,EAAE,CAAC/L,kBAAkB,EAAEwC,aAAa,CAAC,CAAC;EAEvC,MAAMwJ,cAAc,GAAGzP,WAAW,CAAE8I,KAAa,IAAK;IAClDxF,eAAe,CAACyL,UAAU,IAAI;MAC1B,MAAMW,gBAAgB,GAAGX,UAAU,CAACjG,KAAK,CAAC;MAC1C,MAAM6G,eAAe,GAAG;QACpB,GAAGxN,IAAI,CAACkE,KAAK,CAAClE,IAAI,CAACC,SAAS,CAACsN,gBAAgB,CAAC,CAAC;QAC/C3N,UAAU,EAAE,GAAG2N,gBAAgB,CAACzN,OAAO,IAAIyN,gBAAgB,CAACxN,GAAG,IAAI,KAAK,IAAIM,IAAI,CAACD,GAAG,CAAC,CAAC,IAAI+G,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;MACrI,CAAC;MAED,MAAMuF,SAAS,GAAG,CAAC,GAAGD,UAAU,CAAC;MACjCC,SAAS,CAACE,MAAM,CAACpG,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE6G,eAAe,CAAC;;MAE/C;MACA1J,aAAa,CAAC+I,SAAS,EAAElG,KAAK,GAAG,CAAC,CAAC;MAEnC,OAAOkG,SAAS;IACpB,CAAC,CAAC;IACFtL,qBAAqB,CAACoF,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC,EAAE,CAAC7C,aAAa,CAAC,CAAC;EAEnB,MAAM2J,kBAAkB,GAAG5P,WAAW,CAAC,CAAC8I,KAAa,EAAE+G,cAAqC,KAAK;IAC7FvM,eAAe,CAACyL,UAAU,IAAI;MAC1B,MAAMC,SAAS,GAAGD,UAAU,CAAClN,GAAG,CAAC,CAACC,KAAK,EAAE2J,CAAC,KACtCA,CAAC,KAAK3C,KAAK,GACL;QAAE,GAAGhH,KAAK;QAAEE,OAAO,EAAE;UAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,CAAC,CAAC,CAAC;UAAE,GAAG6N;QAAe;MAAE,CAAC,GACtE/N,KACV,CAAC;;MAED;MACA,MAAMgO,aAAa,GAAGxB,UAAU,CAAC,MAAM;QACnCrI,aAAa,CAAC+I,SAAS,EAAEvL,kBAAkB,CAAC;MAChD,CAAC,EAAE,IAAI,CAAC;MAER,OAAOuL,SAAS;IACpB,CAAC,CAAC;EACN,CAAC,EAAE,CAAC/I,aAAa,EAAExC,kBAAkB,CAAC,CAAC;;EAEvC;EACA,MAAMsM,UAAU,GAAG,MAAAA,CAAOC,kBAA0B,EAAEC,cAAuB,GAAG,KAAK,KAAK;IACxFjM,WAAW,CAAC,IAAI,CAAC;IACjBQ,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAM8G,IAAI,GAAGP,sBAAsB,CAAC1H,YAAY,CAAC;MACjD,MAAM;QAAE4K,IAAI;QAAEM,MAAM,EAAE2B;MAAiB,CAAC,GAAG7P,SAAS,CAACiL,IAAI,CAAC;MAC1D,IAAI4E,gBAAgB,IAAIA,gBAAgB,CAACpK,MAAM,GAAG,CAAC,EAAE;QACnD2B,OAAO,CAAC0B,IAAI,CAAC,8CAA8C,EAAE+G,gBAAgB,CAAC;QAC9E;MACF;MAEA,MAAMvH,QAAQ,GAAGtF,YAAY,CAC1BxB,GAAG,CAACqH,CAAC,IAAI4D,MAAM,CAAC5D,CAAC,CAACjH,OAAO,IAAIiH,CAAC,CAAChH,GAAG,CAAC,CAAC,CAAC;MAAA,CACrCyH,MAAM,CAACwG,OAAO,CAAC,CAAC,CAAC;;MAEpB,MAAMC,OAAwE,GAAG;QAC/EnN,UAAU,EAAEE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEjB,GAAG;QACzByC,YAAY,EAAEqL,kBAAkB;QAChC1E,IAAI;QACJ2C,IAAI;QACJtF,QAAQ;QACRsB,OAAO,EAAE,CAAA9G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE8G,OAAO,KAAI,mBAAmB;QACjDoG,IAAI,EAAE,CAAAlN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkN,IAAI,KAAI,EAAE;QAC1BC,QAAQ,EAAEL,cAAc;QACxBM,WAAW,EAAE,CAAApN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoN,WAAW,KAAI,EAAE;QACxCC,QAAQ,EAAE,CAAArN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqN,QAAQ,KAAI,EAAE;QAClCC,aAAa,EAAE,CAAAtN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsN,aAAa,KAAI;MAC5C,CAAC;;MAED;MACA;MACA;MACA;MACA;;MAEA,MAAMpG,QAAQ,GAAG,MAAM3J,GAAG,CAACgQ,IAAI,CAAuB,iBAAiB,EAAEN,OAAO,CAAC;MACjF,MAAMO,aAAa,GAAGtG,QAAQ,CAAC/B,IAAI,CAACnF,QAAQ;MAC5CC,WAAW,CAACuN,aAAa,CAAC;;MAE1B;MACA;MACA,IAAIA,aAAa,CAAChI,QAAQ,IAAIpF,eAAe,CAACuC,MAAM,GAAG,CAAC,EAAE;QACvD,MAAM8C,eAAe,GAAG+H,aAAa,CAAChI,QAAQ,CAAC9G,GAAG,CAAC,CAACgH,EAAE,EAAEC,KAAK,KAAK;UAAA,IAAA8H,qBAAA,EAAAC,sBAAA;UAC/D,MAAM7H,aAAa,GAAGzF,eAAe,CAAC0F,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjH,OAAO,KAAK4G,EAAE,IAAIK,CAAC,CAAChH,GAAG,KAAK2G,EAAE,CAAC;UACjF,MAAMQ,eAAe,GAAG,EAAAuH,qBAAA,GAAAD,aAAa,CAAC/O,MAAM,cAAAgP,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAuB9H,KAAK,CAAC,cAAA+H,sBAAA,uBAA7BA,sBAAA,CAA+B7O,OAAO,MAAIgH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEhH,OAAO,KAAI,CAAC,CAAC;UAC9F,MAAMD,UAAU,GAAG,GAAG8G,EAAE,IAAIC,KAAK,IAAItG,IAAI,CAACD,GAAG,CAAC,CAAC,IAAI+G,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,EAAE;UAC5F,OAAOT,aAAa,GAAG;YAAE,GAAGA,aAAa;YAAEhH,OAAO,EAAE;cAAE,GAAGqH;YAAgB,CAAC;YAAEtH;UAAW,CAAC,GAAG,IAAI;QAClG,CAAC,CAAC,CAAC4H,MAAM,CAAET,CAAC,IAAKA,CAAC,KAAK,IAAI,CAAY;QACvC5F,eAAe,CAACsF,eAAe,CAAC;MACnC;MAEA1E,gBAAgB,CAAC,KAAK,CAAC;MACvBuD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACzE,UAAU,IAAI0N,aAAa,CAACzO,GAAG,EAAE;QACpCgB,QAAQ,CAAC,iBAAiByN,aAAa,CAACzO,GAAG,EAAE,EAAE;UAAEqK,OAAO,EAAE;QAAK,CAAC,CAAC;MACnE;MACA,OAAOoE,aAAa;IACtB,CAAC,CAAC,OAAOxG,GAAQ,EAAE;MAAA,IAAA2G,aAAA,EAAAC,mBAAA;MACjBtJ,OAAO,CAACpD,KAAK,CAAC,wBAAwB,EAAE8F,GAAG,CAAC;MAC5C,MAAM6G,YAAY,GAAG,EAAAF,aAAA,GAAA3G,GAAG,CAACE,QAAQ,cAAAyG,aAAA,wBAAAC,mBAAA,GAAZD,aAAA,CAAcxI,IAAI,cAAAyI,mBAAA,uBAAlBA,mBAAA,CAAoB1M,KAAK,KAAI8F,GAAG,CAACW,OAAO,IAAI,0BAA0B;MAC3FtG,YAAY,CAACwM,YAAY,CAAC;MAC1B,OAAO,IAAI;IACb,CAAC,SAAS;MACRhN,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM,CAAC;IAAEiN;EAAa,CAAC,EAAEC,IAAI,CAAC,GAAG3Q,OAAO,CAAC,OAAO;IAC5C4Q,MAAM,EAAE,CAAC9P,SAAS,CAACC,KAAK,EAAED,SAAS,CAACE,aAAa,CAAC;IAClD2P,IAAI,EAAEA,CAACE,IAAsC,EAAEC,OAAO,KAAK;MACvD,IAAIA,OAAO,CAACC,OAAO,CAAC,CAAC,EAAE;MAEvB,MAAMC,SAAS,GAAGvL,eAAe,CAACwL,OAAO;MACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACK,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACD,YAAY,IAAI,CAACF,SAAS,EAAE;MAEjC,MAAMzC,UAAU,GAAG6C,aAAa,CAACF,YAAY,CAACG,CAAC,EAAEL,SAAS,CAAC;MAE3D,IAAIH,IAAI,CAACS,IAAI,KAAKxQ,SAAS,CAACE,aAAa,EAAE;QACzC6N,oBAAoB,CAAEgC,IAAI,CAAqBtP,KAAK,EAAEgN,UAAU,CAAC;MACnE;MACA;IACJ,CAAC;IACDgD,OAAO,EAAET,OAAO,KAAK;MACnBJ,YAAY,EAAEI,OAAO,CAACU,MAAM,CAAC;QAAEC,OAAO,EAAE;MAAK,CAAC;IAChD,CAAC;EACL,CAAC,CAAC,EAAE,CAAC3O,YAAY,EAAEuL,SAAS,EAAEQ,oBAAoB,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAMuC,aAAa,GAAGA,CAACM,OAA2B,EAAEC,SAAyB,KAAa;IACtF,IAAID,OAAO,KAAKhG,SAAS,EAAE,OAAO5I,YAAY,CAACyC,MAAM;IAErD,MAAMqM,aAAa,GAAGD,SAAS,CAACE,qBAAqB,CAAC,CAAC;IACvD,MAAMC,OAAO,GAAGJ,OAAO,GAAGE,aAAa,CAACG,GAAG,GAAGJ,SAAS,CAACK,SAAS;IAEjE,IAAIC,eAAe,GAAGnP,YAAY,CAACyC,MAAM;IACzC,MAAM2M,QAAQ,GAAGvG,KAAK,CAACwG,IAAI,CAACR,SAAS,CAACO,QAAQ,CAAkB;IAEhE,KAAK,IAAIhH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgH,QAAQ,CAAC3M,MAAM,EAAE2F,CAAC,EAAE,EAAE;MACtC,MAAMkH,KAAK,GAAGF,QAAQ,CAAChH,CAAC,CAAC;MACzB,IAAI,CAACkH,KAAK,CAACC,SAAS,IAAI,CAACD,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;MAEtE,MAAMC,QAAQ,GAAGH,KAAK,CAACI,SAAS;MAChC,MAAMC,WAAW,GAAGL,KAAK,CAACM,YAAY;MACtC,MAAMC,OAAO,GAAGJ,QAAQ,GAAGE,WAAW,GAAG,CAAC;MAE1C,IAAIX,OAAO,GAAGa,OAAO,EAAE;QACnBV,eAAe,GAAG/G,CAAC;QACnB;MACJ;IACJ;IACA,OAAO+G,eAAe;EAC1B,CAAC;;EAED;EACAvS,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACAuB,aAAa,CAAC2R,KAAK,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,sBAAsB,GAAGpT,WAAW,CAAC,MAAM;IAC/C;IACA,IAAI,QAAQ,IAAIqT,WAAW,EAAE;MAC3B,MAAMC,UAAU,GAAID,WAAW,CAASE,MAAM;MAC9C,IAAID,UAAU,CAACE,cAAc,GAAGF,UAAU,CAACG,eAAe,GAAG,GAAG,EAAE;QAChEhM,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;QAC5E;QACApF,eAAe,CAAC,CAAC;QACjB;MACF;IACF;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArC,SAAS,CAAC,MAAM;IACd,IAAI,iBAAiB,IAAImH,MAAM,EAAE;MAC9BA,MAAM,CAASC,gBAAgB,CAAC,eAAe,EAAE+L,sBAAsB,CAAC;MACzE,OAAO,MAAM;QACVhM,MAAM,CAASE,mBAAmB,CAAC,eAAe,EAAE8L,sBAAsB,CAAC;MAC9E,CAAC;IACH;EACF,CAAC,EAAE,CAACA,sBAAsB,CAAC,CAAC;;EAE5B;EACA,IAAIjP,SAAS,EAAE;IACb,oBAAOnD,OAAA;MAAK0S,SAAS,EAAC,iEAAiE;MAAAjB,QAAA,EAAC;IAAiB;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACjH;;EAEA;EACA,IAAIzP,KAAK,IAAI,CAAClB,QAAQ,EAAE;IACtB,oBAAOnC,OAAA;MAAK0S,SAAS,EAAC,+DAA+D;MAAAjB,QAAA,GAAC,sCAAoC,EAACpO,KAAK;IAAA;MAAAsP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACzI;EAEA,oBACE9S,OAAA;IAAK0S,SAAS,EAAC,iDAAiD;IAAAjB,QAAA,gBAE9DzR,OAAA;MAAK0S,SAAS,EAAC,wGAAwG;MAAAjB,QAAA,gBACrHzR,OAAA;QAAK0S,SAAS,EAAC,0CAA0C;QAAAjB,QAAA,gBACvDzR,OAAA;UAAI0S,SAAS,EAAC,qCAAqC;UAAAjB,QAAA,EAChD,CAAAtP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,YAAY,KAAI;QAAmB;UAAAgP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eAGL9S,OAAA;UAAK0S,SAAS,EAAC,6BAA6B;UAAAjB,QAAA,gBAC1CzR,OAAA;YACE+S,OAAO,EAAErN,IAAK;YACdsN,QAAQ,EAAE,CAACpO,OAAQ;YACnB8N,SAAS,EAAC,yGAAyG;YACnHO,KAAK,EAAC,eAAe;YAAAxB,QAAA,eAErBzR,OAAA;cAAK0S,SAAS,EAAC,SAAS;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eAC5EzR,OAAA;gBAAMqT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA0C;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACT9S,OAAA;YACE+S,OAAO,EAAElN,IAAK;YACdmN,QAAQ,EAAE,CAACnO,OAAQ;YACnB6N,SAAS,EAAC,yGAAyG;YACnHO,KAAK,EAAC,eAAe;YAAAxB,QAAA,eAErBzR,OAAA;cAAK0S,SAAS,EAAC,SAAS;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eAC5EzR,OAAA;gBAAMqT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA+C;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9S,OAAA;QAAK0S,SAAS,EAAC,kCAAkC;QAAAjB,QAAA,eAE/CzR,OAAA;UAAK0S,SAAS,EAAC,kEAAkE;UAAAjB,QAAA,gBAC/EzR,OAAA;YACE+S,OAAO,EAAEA,CAAA,KAAMzO,WAAW,CAAC,MAAM,CAAE;YACnCoO,SAAS,EAAE,8FACTrO,QAAQ,KAAK,MAAM,GAAG,oCAAoC,GAAG,mCAAmC,EAC/F;YAAAoN,QAAA,gBAEHzR,OAAA;cAAK0S,SAAS,EAAC,cAAc;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eACjFzR,OAAA;gBAAMqT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAwH;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7L,CAAC,QAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9S,OAAA;YACE+S,OAAO,EAAEA,CAAA,KAAMzO,WAAW,CAAC,OAAO,CAAE;YACpCoO,SAAS,EAAE,8FACTrO,QAAQ,KAAK,OAAO,GAAG,oCAAoC,GAAG,mCAAmC,EAChG;YAAAoN,QAAA,gBAEHzR,OAAA;cAAK0S,SAAS,EAAC,cAAc;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eACjFzR,OAAA;gBAAMqT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAwM;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7Q,CAAC,SAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9S,OAAA;YACE+S,OAAO,EAAEA,CAAA,KAAMzO,WAAW,CAAC,SAAS,CAAE;YACtCoO,SAAS,EAAE,8FACTrO,QAAQ,KAAK,SAAS,GAAG,oCAAoC,GAAG,mCAAmC,EAClG;YAAAoN,QAAA,gBAEHzR,OAAA;cAAK0S,SAAS,EAAC,cAAc;cAACQ,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,gBACjFzR,OAAA;gBAAMqT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAkC;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1G9S,OAAA;gBAAMqT,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyH;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9L,CAAC,WAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9S,OAAA;QAAK0S,SAAS,EAAC,2CAA2C;QAAAjB,QAAA,gBAExDzR,OAAA;UAAK0S,SAAS,EAAC,8CAA8C;UAAAjB,QAAA,gBAC3DzR,OAAA;YACE+S,OAAO,EAAEA,CAAA,KAAMjQ,cAAc,CAAC,SAAS,CAAE;YACzC4P,SAAS,EAAE,8DACT7P,WAAW,KAAK,SAAS,GAAG,oCAAoC,GAAG,mCAAmC,EACrG;YAAA4O,QAAA,EACJ;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9S,OAAA;YACE+S,OAAO,EAAEA,CAAA,KAAMjQ,cAAc,CAAC,QAAQ,CAAE;YACxC4P,SAAS,EAAE,8DACT7P,WAAW,KAAK,QAAQ,GAAG,oCAAoC,GAAG,mCAAmC,EACpG;YAAA4O,QAAA,EACJ;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9S,OAAA;UACE+S,OAAO,EAAEA,CAAA,KAAM;YACb3Q,WAAW,CAAC+C,IAAI,KAAK;cAAE,IAAIA,IAAI,IAAI,CAAC,CAAC,CAAC;cAAExB,YAAY,EAAE,CAAAwB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAExB,YAAY,KAAI;YAAoB,CAAC,CAAa,CAAC;YAC/GH,YAAY,CAAC,IAAI,CAAC;YAClBN,gBAAgB,CAAC,IAAI,CAAC;UACxB,CAAE;UACF8P,QAAQ,EAAEjQ,QAAS;UACnB2P,SAAS,EAAC,kPAAkP;UAAAjB,QAAA,EAE3P1O,QAAQ,GAAG,WAAW,GAAG;QAAe;UAAA4P,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLzP,KAAK,IAAIpB,UAAU,iBAChBjC,OAAA;MAAK0S,SAAS,EAAC,yEAAyE;MAAAjB,QAAA,GAAC,SAAO,EAACpO,KAAK;IAAA;MAAAsP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAGjH9S,OAAA;MAAK0S,SAAS,EAAE,4CAA6C;MAAAjB,QAAA,gBAE3DzR,OAAA;QAAK0S,SAAS,EAAC,mGAAmG;QAAAjB,QAAA,gBAChHzR,OAAA;UAAI0S,SAAS,EAAC,4FAA4F;UAAAjB,QAAA,EAAC;QAAa;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7H9S,OAAA,CAACJ,YAAY;UACXgB,MAAM,EAAE2B,eAAgB;UACxBkR,UAAU,EAAG3S,KAAK,IAAKsN,oBAAoB,CAACtN,KAAK,EAAEuB,YAAY,CAACyC,MAAM,CAAE,CAAC;QAAA;UAAA6N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN9S,OAAA;QAAK0T,GAAG,EAAExD,IAAK;QAACwC,SAAS,EAAE,qEAAqEzC,YAAY,GAAG,6CAA6C,GAAG,EAAE,EAAG;QAAAwB,QAAA,eAClKzR,OAAA;UAAK0T,GAAG,EAAE1O,eAAgB;UAAC0N,SAAS,EAAC,2DAA2D;UAAAjB,QAAA,EAC7FpP,YAAY,CAACyC,MAAM,KAAK,CAAC,gBACxB9E,OAAA;YAAK0S,SAAS,EAAC,oJAAoJ;YAAAjB,QAAA,eACjKzR,OAAA;cAAAyR,QAAA,GAAG,mCAAiC,eAAAzR,OAAA;gBAAA2S,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,8CAA0C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,GAENzQ,YAAY,CAACxB,GAAG,CAAC,CAACC,KAAK,EAAEgH,KAAK,kBAC5B9H,OAAA,CAAC2T,cAAc;YACU;YACvB7S,KAAK,EAAEA,KAAM;YACbgH,KAAK,EAAEA,KAAM;YACb8F,SAAS,EAAEA,SAAU;YACrBU,WAAW,EAAEA,WAAY;YACzBG,cAAc,EAAEA,cAAe;YAC/BmF,UAAU,EAAEnR,kBAAkB,KAAKqF,KAAM;YACzCiL,OAAO,EAAEA,CAAA,KAAMrQ,qBAAqB,CAACoF,KAAK;UAAE,GAPvChH,KAAK,CAACC,UAAU;YAAA4R,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAQtB,CACF;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9S,OAAA;QAAK0S,SAAS,EAAC,0GAA0G;QAAAjB,QAAA,EACnHhP,kBAAkB,KAAK,IAAI,IAAIJ,YAAY,CAACyC,MAAM,KAAK,CAAC;QAAA;QACtD;QACA9E,OAAA;UAAK0S,SAAS,EAAC,oDAAoD;UAAAjB,QAAA,gBAC/DzR,OAAA;YAAI0S,SAAS,EAAC,0EAA0E;YAAAjB,QAAA,EAAC;UAAO;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrG9S,OAAA,CAACH,YAAY;YACToN,IAAI,EAAEtK,WAAY;YAClBkR,IAAI,EAAEhR,WAAY;YAClBiR,SAAS,EAAE/O;UAAiB;YAAA4N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;QAAA;QAEN;QACA9S,OAAA;UAAK0S,SAAS,EAAC,yDAAyD;UAAAjB,QAAA,gBACpEzR,OAAA;YAAI0S,SAAS,EAAC,4GAA4G;YAAAjB,QAAA,gBACxHzR,OAAA;cAAAyR,QAAA,GAAM,QAAM,EAAC,EAAAzP,qBAAA,GAAAK,YAAY,CAACI,kBAAkB,CAAC,cAAAT,qBAAA,uBAAhCA,qBAAA,CAAkCsJ,IAAI,KAAI,OAAO;YAAA;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtE9S,OAAA;cACE+S,OAAO,EAAEA,CAAA,KAAMrQ,qBAAqB,CAAC,IAAI,CAAE;cAC3CgQ,SAAS,EAAC,yEAAyE;cACnFO,KAAK,EAAC,cAAc;cAAAxB,QAAA,EACrB;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACL9S,OAAA;YAAK0S,SAAS,EAAC,wBAAwB;YAAAjB,QAAA,EAClCpP,YAAY,CAACI,kBAAkB,CAAC;YAAA;YAAM;YACnCzC,OAAA,CAACL,WAAW;cACRmB,KAAK,EAAEuB,YAAY,CAACI,kBAAkB,CAAE;cACxCsR,QAAQ,EAAG/S,OAAO,IAAK;gBACnB,IAAIyB,kBAAkB,KAAK,IAAI,EAAE;kBAC7BmM,kBAAkB,CAACnM,kBAAkB,EAAEzB,OAAO,CAAC;gBACnD;cACJ;YAAE;cAAA2R,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC,EAGL7P,aAAa,iBACZjD,OAAA,CAACF,iBAAiB;MAChBkU,WAAW,EAAE,CAAA7R,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwB,YAAY,KAAI,mBAAoB;MAC3DsQ,MAAM,EAAElF,UAAW;MACnBmF,QAAQ,EAAEA,CAAA,KAAM;QAAEhR,gBAAgB,CAAC,KAAK,CAAC;QAAEM,YAAY,CAAC,IAAI,CAAC;MAAE,CAAE;MACjET,QAAQ,EAAEA,QAAS;MACnBM,KAAK,EAAEE,SAAU,CAAC;IAAA;MAAAoP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA/Q,EAAA,CAn7BMD,WAAqB;EAAA,QACFrC,SAAS,EACfD,WAAW,EA0oBKD,OAAO;AAAA;AAAA4U,EAAA,GA5oBpCrS,WAAqB;AA87B3B,MAAM6R,cAA6C,GAAGA,CAAC;EACrD7S,KAAK;EACLgH,KAAK;EACL8F,SAAS;EACTU,WAAW;EACXG,cAAc;EACdmF,UAAU;EACVb;AACF,CAAC,KAAK;EAAAqB,GAAA;EACJ,MAAMV,GAAG,GAAGvU,MAAM,CAAiB,IAAI,CAAC;EAExC,MAAM,CAAC;IAAEkV;EAAU,CAAC,EAAEnE,IAAI,CAAC,GAAG3Q,OAAO,CAA8D,OAAO;IACxG4Q,MAAM,EAAE9P,SAAS,CAACC,KAAK;IACvBgU,KAAK,EAAEA,CAAClE,IAAoB,EAAEC,OAAgD,KAAK;MACjF,IAAI,CAACqD,GAAG,CAAClD,OAAO,EAAE;MAClB,MAAM3C,SAAS,GAAGuC,IAAI,CAACtI,KAAK;MAC5B,MAAMgG,UAAU,GAAGhG,KAAK;MACxB,IAAI+F,SAAS,KAAKC,UAAU,EAAE;MAE9B,MAAMyG,iBAAiB,GAAGb,GAAG,CAAClD,OAAO,CAACY,qBAAqB,CAAC,CAAC;MAC7D,MAAMoD,YAAY,GAAG,CAACD,iBAAiB,CAACE,MAAM,GAAGF,iBAAiB,CAACjD,GAAG,IAAI,CAAC;MAC3E,MAAMb,YAAY,GAAGJ,OAAO,CAACK,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACD,YAAY,EAAE;MACnB,MAAMiE,YAAY,GAAGjE,YAAY,CAACG,CAAC,GAAG2D,iBAAiB,CAACjD,GAAG;MAE3D,IAAIzD,SAAS,GAAGC,UAAU,IAAI4G,YAAY,GAAGF,YAAY,EAAE;MAC3D,IAAI3G,SAAS,GAAGC,UAAU,IAAI4G,YAAY,GAAGF,YAAY,EAAE;MAE3D5G,SAAS,CAACC,SAAS,EAAEC,UAAU,CAAC;MAChCsC,IAAI,CAACtI,KAAK,GAAGgG,UAAU,CAAC,CAAC;IAC3B,CAAC;IACDgD,OAAO,EAAGT,OAAO,KAAM;MACnBgE,SAAS,EAAEhE,OAAO,CAACsE,YAAY,CAAC;IACpC,CAAC;EACH,CAAC,CAAC,EAAE,CAAC7M,KAAK,EAAE8F,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEzB,MAAM,CAAC;IAAEgH;EAAW,CAAC,EAAEC,IAAI,CAAC,GAAGvV,OAAO,CAAC,OAAO;IAC5CuR,IAAI,EAAExQ,SAAS,CAACC,KAAK;IACrB8P,IAAI,EAAE;MAAEtI,KAAK;MAAED,EAAE,EAAE/G,KAAK,CAACC,UAAU;MAAE8P,IAAI,EAAExQ,SAAS,CAACC;IAAM,CAAC;IAAE;IAC9DwQ,OAAO,EAAGT,OAAO,KAAM;MACrBuE,UAAU,EAAEvE,OAAO,CAACuE,UAAU,CAAC;IACjC,CAAC;EACH,CAAC,CAAC,EAAE,CAAC9M,KAAK,EAAEhH,KAAK,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEhC8T,IAAI,CAAC3E,IAAI,CAACwD,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEjB,oBACE1T,OAAA;IACE0T,GAAG,EAAEA,GAAI;IACT,mBAAiBW,SAAU;IAC3B3B,SAAS,EAAE,yEAAyEkC,UAAU,GAAG,4BAA4B,GAAG,uCAAuC,IAAIhB,UAAU,GAAG,oDAAoD,GAAG,iBAAiB,EAAG,CAAC;IAAA;IACpQb,OAAO,EAAEA,OAAQ;IACjB+B,KAAK,EAAE;MAAEC,OAAO,EAAEH,UAAU,GAAG,GAAG,GAAG;IAAE,CAAE;IAAAnD,QAAA,gBAEzCzR,OAAA;MAAK0S,SAAS,EAAC,qHAAqH;MAAAjB,QAAA,GAAC,GAAC,eACpIzR,OAAA;QAAM0S,SAAS,EAAC,oDAAoD;QAACO,KAAK,EAAEnS,KAAK,CAACwK,IAAK;QAAAmG,QAAA,GAAE3Q,KAAK,CAACwK,IAAI,EAAC,GAAC,eAAAtL,OAAA;UAAM0S,SAAS,EAAC,eAAe;UAAAjB,QAAA,GAAC,GAAC,EAAC3Q,KAAK,CAACkU,QAAQ,EAAC,GAAC;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrK9S,OAAA;QAAK0S,SAAS,EAAC,2CAA2C;QAAAjB,QAAA,GAAC,GAAC,eAEzDzR,OAAA;UACC6Q,IAAI,EAAC,QAAQ;UACb6B,SAAS,EAAC,yDAAyD;UACnEK,OAAO,EAAGhN,CAAC,IAAK;YACZA,CAAC,CAACkP,eAAe,CAAC,CAAC;YACnBxG,cAAc,CAAC3G,KAAK,CAAC;UACzB,CAAE;UACFmL,KAAK,EAAC,iBAAiB;UAAAxB,QAAA,eAGpBzR,OAAA;YAAKkV,KAAK,EAAC,4BAA4B;YAACxC,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAACI,WAAW,EAAE,CAAE;YAAA9B,QAAA,eAC7HzR,OAAA;cAAMqT,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACE,CAAC,EAAC;YAAuH;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9K;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEV9S,OAAA;UACE6Q,IAAI,EAAC,QAAQ;UACb6B,SAAS,EAAC,qEAAqE,CAAC;UAAA;UAChFK,OAAO,EAAGhN,CAAC,IAAK;YACdA,CAAC,CAACkP,eAAe,CAAC,CAAC;YACnB3G,WAAW,CAACxG,KAAK,CAAC;UACpB,CAAE;UACFmL,KAAK,EAAC,cAAc;UAAAxB,QAAA,eAGpBzR,OAAA;YAAKkV,KAAK,EAAC,4BAA4B;YAACxC,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAACI,WAAW,EAAE,CAAE;YAAA9B,QAAA,eAC7HzR,OAAA;cAAMqT,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACE,CAAC,EAAC;YAAsB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN9S,OAAA;MAAK0S,SAAS,EAAC,oFAAoF;MAAAjB,QAAA,GAChG,CAAC,MAAM;QACN;QACA,IAAI3Q,KAAK,CAACG,OAAO,KAAK,mBAAmB,EAAE;UACzC,OAAO,mBAAmB;QAC5B,CAAC,MAAM,IAAIH,KAAK,CAACG,OAAO,KAAK,aAAa,EAAE;UAAA,IAAAkU,cAAA;UAC1C,OAAO,EAAAA,cAAA,GAAArU,KAAK,CAACE,OAAO,cAAAmU,cAAA,uBAAbA,cAAA,CAAeC,YAAY,KAAI,cAAc;QACtD,CAAC,MAAM,IAAItU,KAAK,CAACG,OAAO,KAAK,kBAAkB,EAAE;UAAA,IAAAoU,eAAA;UAC/C,OAAO,EAAAA,eAAA,GAAAvU,KAAK,CAACE,OAAO,cAAAqU,eAAA,uBAAbA,eAAA,CAAeC,QAAQ,KAAI,UAAU;QAC9C,CAAC,MAAM,IAAIxU,KAAK,CAACG,OAAO,KAAK,cAAc,EAAE;UAAA,IAAAsU,eAAA,EAAAC,eAAA,EAAAC,eAAA;UAC3C,OAAO,gBAAgB,GAAG,EAAAF,eAAA,GACxBzU,KAAK,CAACE,OAAO,cAAAuU,eAAA,uBAAbA,eAAA,CAAeG,UAAU,GAAAF,eAAA,GACzB1U,KAAK,CAACE,OAAO,cAAAwU,eAAA,uBAAbA,eAAA,CAAeG,UAAU,GAAAF,eAAA,GACzB3U,KAAK,CAACE,OAAO,cAAAyU,eAAA,uBAAbA,eAAA,CAAeG,UAAU,CAC1B,CAACjN,MAAM,CAACwG,OAAO,CAAC,CAAC9N,IAAI,CAAC,IAAI,CAAC;QAC9B,CAAC,MAAM,IAAIP,KAAK,CAACG,OAAO,KAAK,YAAY,EAAE;UAAA,IAAA4U,eAAA;UACzC,OAAO,EAAAA,eAAA,GAAA/U,KAAK,CAACE,OAAO,cAAA6U,eAAA,uBAAbA,eAAA,CAAeC,UAAU,KAAI,QAAQ;QAC9C,CAAC,MAAM,IAAIhV,KAAK,CAACG,OAAO,KAAK,iBAAiB,EAAE;UAAA,IAAA8U,eAAA;UAC9C,OAAO,CAAAA,eAAA,GAAAjV,KAAK,CAACE,OAAO,cAAA+U,eAAA,eAAbA,eAAA,CAAeC,WAAW,GAAG,WAAWlV,KAAK,CAACE,OAAO,CAACgV,WAAW,EAAE,GAAG,iBAAiB;QAChG,CAAC,MAAM;UAAA,IAAAC,eAAA,EAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA;UACL;UACA,OAAO,EAAAH,eAAA,GAAAnV,KAAK,CAACE,OAAO,cAAAiV,eAAA,uBAAbA,eAAA,CAAeX,QAAQ,KACvB,EAAAY,eAAA,GAAApV,KAAK,CAACE,OAAO,cAAAkV,eAAA,wBAAAC,oBAAA,GAAbD,eAAA,CAAepJ,IAAI,cAAAqJ,oBAAA,uBAAnBA,oBAAA,CAAqB1N,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KACpC,CAAA2N,gBAAA,GAAAtV,KAAK,CAACE,OAAO,cAAAoV,gBAAA,eAAbA,gBAAA,CAAetJ,IAAI,IAAIhM,KAAK,CAACE,OAAO,CAAC8L,IAAI,CAAChI,MAAM,GAAG,EAAE,GAAG,KAAK,GAAG,EAAE,CAAC,IACpEhE,KAAK,CAACwK,IAAI;QACnB;MACF,CAAC,EAAE,CAAC,EACHxK,KAAK,CAACuV,SAAS,iBAAIrW,OAAA;QAAKsW,GAAG,EAAExV,KAAK,CAACuV,SAAU;QAACE,GAAG,EAAE,GAAGzV,KAAK,CAACwK,IAAI,YAAa;QAACoH,SAAS,EAAC;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACsB,GAAA,CAzHIT,cAA6C;EAAA,QAWnBpU,OAAO,EAyBND,OAAO;AAAA;AAAAkX,GAAA,GApClC7C,cAA6C;AA2HnD,SAAS7R,WAAW;AACpB,eAAeA,WAAW;AAAC,IAAAqS,EAAA,EAAAqC,GAAA;AAAAC,YAAA,CAAAtC,EAAA;AAAAsC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}