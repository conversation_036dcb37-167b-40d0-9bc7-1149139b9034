"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[368],{6368:(e,a,t)=>{t.r(a),t.d(a,{default:()=>d});var s=t(5043),r=t(9291),l=t(8417),n=t(1411),i=t(4741),c=t(9579),m=t(579);const d=()=>{const[e,a]=(0,s.useState)([]),[t,d]=(0,s.useState)([]),[o,x]=(0,s.useState)(!0),[p,u]=(0,s.useState)(null),[b,h]=(0,s.useState)({name:"",defaultValue:""}),[y,f]=(0,s.useState)({name:"",content:"",selectedVariables:[]}),[j,g]=(0,s.useState)("variables"),[v,N]=(0,s.useState)(""),[w,V]=(0,s.useState)({}),[C,A]=(0,s.useState)(null);(0,s.useEffect)((()=>{(async()=>{x(!0);try{const[e,t]=await Promise.all([c.hD.getVariables(),c.hD.getTemplates()]);e.success?a(Array.isArray(e.data)?e.data:[]):u(e.message||"Failed to fetch variables"),t.success?d(t.data):u((e=>e?`${e}; ${t.message}`:t.message||"Failed to fetch templates"))}catch(e){u(e.message||"Failed to fetch personalization data")}finally{x(!1)}})()}),[]);return(0,m.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,m.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,m.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"Personalization Editor"})}),(0,m.jsx)("p",{className:"text-text-secondary mb-6",children:"Create and manage personalization variables and templates."}),o?(0,m.jsx)("div",{className:"flex justify-center py-8",children:(0,m.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):(0,m.jsxs)(m.Fragment,{children:[p&&(0,m.jsx)(r.A,{type:"error",message:p,onClose:()=>u(null),className:"mb-4"}),(0,m.jsxs)(n.A,{className:"overflow-hidden",children:[(0,m.jsx)("div",{className:"border-b border-gray-700",children:(0,m.jsxs)("nav",{className:"-mb-px flex space-x-4 px-6",children:[(0,m.jsx)("button",{className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm "+("variables"===j?"border-primary text-primary":"border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500"),onClick:()=>g("variables"),children:"Variables"}),(0,m.jsx)("button",{className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm "+("templates"===j?"border-primary text-primary":"border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500"),onClick:()=>g("templates"),children:"Templates"})]})}),(0,m.jsx)("div",{className:"p-6",children:"variables"===j?(0,m.jsxs)("div",{children:[(0,m.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Manage Variables"}),(0,m.jsxs)(n.A,{className:"mb-6 p-4 border border-gray-700 rounded",children:[(0,m.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-3",children:"Create New Variable"}),(0,m.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),u(null),b.name.trim())try{const t=await c.hD.createVariable(b.name,b.defaultValue);t.success?(a([...e,t.data]),h({name:"",defaultValue:""})):u(t.message||"Failed to create variable")}catch(s){u(s.message||"An error occurred while creating variable")}else u("Variable name is required")},className:"space-y-4",children:[(0,m.jsx)(i.A,{label:"Variable Name",id:"variableName",name:"variableName",value:b.name,onChange:e=>h({...b,name:e.target.value}),placeholder:"e.g., firstName, companyName",required:!0}),(0,m.jsx)(i.A,{label:"Default Value (Optional)",id:"defaultValue",name:"defaultValue",value:b.defaultValue,onChange:e=>h({...b,defaultValue:e.target.value}),placeholder:"e.g., there, your company"}),(0,m.jsx)("div",{className:"flex justify-end",children:(0,m.jsx)(l.A,{type:"submit",children:"Create Variable"})})]})]}),(0,m.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-3",children:"Existing Variables"}),0===e.length?(0,m.jsx)("p",{className:"text-text-secondary",children:"No variables created yet."}):(0,m.jsx)("ul",{className:"space-y-2",children:e.map((e=>(0,m.jsxs)("li",{className:"bg-secondary-bg p-3 rounded-md flex justify-between items-center",children:[(0,m.jsx)("span",{className:"text-text-primary font-mono",children:`{{${e.name}}}`}),(0,m.jsxs)("span",{className:"text-sm text-text-secondary",children:['Default: "',e.defaultValue||"",'"']})]},e.id)))})]}):(0,m.jsxs)("div",{children:[(0,m.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Manage Templates"}),(0,m.jsxs)(n.A,{className:"mb-6 p-4 border border-gray-700 rounded",children:[(0,m.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-3",children:"Create New Template"}),(0,m.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),u(null),y.name.trim()&&y.content.trim())try{const e=await c.hD.createTemplate(y.name,y.content,y.selectedVariables);e.success?(d([...t,e.data]),f({name:"",content:"",selectedVariables:[]})):u(e.message||"Failed to create template")}catch(a){u(a.message||"An error occurred while creating template")}else u("Template name and content are required")},className:"space-y-4",children:[(0,m.jsx)(i.A,{label:"Template Name",id:"templateName",name:"templateName",value:y.name,onChange:e=>f({...y,name:e.target.value}),required:!0}),(0,m.jsxs)("div",{children:[(0,m.jsxs)("label",{htmlFor:"templateContent",className:"block text-sm font-medium text-text-secondary mb-1",children:["Template Content (use ","{{variableName}}"," for variables)"]}),(0,m.jsx)("textarea",{id:"templateContent",name:"templateContent",rows:6,value:y.content,onChange:e=>f({...y,content:e.target.value}),required:!0,className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500"})]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("label",{className:"block text-sm font-medium text-text-secondary mb-2",children:"Select Variables to Use:"}),(0,m.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map((e=>(0,m.jsxs)("label",{className:"flex items-center space-x-2 bg-secondary-bg px-3 py-1 rounded-full text-sm cursor-pointer",children:[(0,m.jsx)("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-primary rounded focus:ring-primary bg-secondary-bg border-gray-600",checked:y.selectedVariables.includes(e.id),onChange:()=>(e=>{const a=y.selectedVariables.includes(e);f((t=>({...t,selectedVariables:a?t.selectedVariables.filter((a=>a!==e)):[...t.selectedVariables,e]})))})(e.id)}),(0,m.jsx)("span",{className:"text-text-primary font-mono",children:`{{${e.name}}}`})]},e.id)))})]}),(0,m.jsx)("div",{className:"flex justify-end",children:(0,m.jsx)(l.A,{type:"submit",children:"Create Template"})})]})]}),(0,m.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-3",children:"Existing Templates"}),0===t.length?(0,m.jsx)("p",{className:"text-text-secondary",children:"No templates created yet."}):(0,m.jsx)("ul",{className:"space-y-2",children:t.map((a=>(0,m.jsx)("li",{className:"bg-secondary-bg p-3 rounded-md",children:(0,m.jsxs)("div",{className:"flex justify-between items-center",children:[(0,m.jsx)("span",{className:"text-text-primary font-semibold",children:a.name}),(0,m.jsx)(l.A,{size:"sm",variant:"secondary",onClick:()=>(a=>{A(a),N(a.content);const t={};a.variables.forEach((a=>{const s=e.find((e=>e.id===a));s&&(t[s.name]=s.defaultValue||"")})),V(t)})(a),children:"Preview"})]})},a.id)))})]})})]}),C&&(0,m.jsx)(n.A,{className:"mt-6",children:(0,m.jsxs)("div",{className:"p-6",children:[(0,m.jsxs)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:["Preview: ",C.name]}),(0,m.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-4",children:[(0,m.jsxs)("div",{children:[(0,m.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-3",children:"Set Variable Values for Preview"}),(0,m.jsx)("div",{className:"space-y-3",children:C.variables.map((a=>{const t=e.find((e=>e.id===a));return t?(0,m.jsx)(i.A,{id:`preview-${t.id}`,name:t.name,label:t.name,value:w[t.name]||"",onChange:e=>{return a=t.name,s=e.target.value,void V({...w,[a]:s});var a,s}},t.id):null}))}),(0,m.jsx)(l.A,{onClick:()=>{if(!C)return;let e=C.content;Object.entries(w).forEach((a=>{let[t,s]=a;const r=new RegExp(`{{${t}}}`,"g");e=e.replace(r,s||"")})),N(e)},className:"mt-4",children:"Update Preview"})]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-3",children:"Live Preview"}),(0,m.jsx)("div",{className:"prose prose-invert max-w-none bg-secondary-bg p-4 rounded-md text-text-primary min-h-[150px]",children:v?(0,m.jsx)("div",{dangerouslySetInnerHTML:{__html:v.replace(/\n/g,"<br />")}}):(0,m.jsx)("span",{className:"text-text-secondary italic",children:"Enter values and update preview..."})})]})]})]})})]})]})}}}]);
//# sourceMappingURL=368.6bc6241f.chunk.js.map