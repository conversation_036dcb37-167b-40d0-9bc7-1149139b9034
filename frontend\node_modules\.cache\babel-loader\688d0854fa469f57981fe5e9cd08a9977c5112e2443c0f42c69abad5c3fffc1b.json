{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\MobilePreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\n// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport { mobilePreviewService } from '../services'; // Corrected import path\n\n// --- Interfaces ---\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// --- Component ---\n\nconst MobilePreview = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [historyLoading, setHistoryLoading] = useState(true); // Separate loading for history\n  const [error, setError] = useState(null);\n  const [emailContent, setEmailContent] = useState('');\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [previewMode, setPreviewMode] = useState('iphone');\n  const [previewHistory, setPreviewHistory] = useState([]);\n\n  // --- Effects ---\n\n  useEffect(() => {\n    const fetchPreviewHistory = async () => {\n      setHistoryLoading(true);\n      setError(null);\n      try {\n        const response = await mobilePreviewService.getPreviewHistory();\n        if (response.success) {\n          setPreviewHistory(response.data);\n        } else {\n          setError('Failed to load preview history');\n        }\n      } catch (err) {\n        setError(err.message || 'Error fetching preview history');\n        console.error('Error fetching preview history:', err);\n      } finally {\n        setHistoryLoading(false);\n      }\n    };\n    fetchPreviewHistory();\n  }, []);\n\n  // --- Handlers ---\n\n  const handleGeneratePreview = async () => {\n    if (!emailContent.trim()) {\n      setError('Email content is required');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setPreviewUrl(null); // Clear previous preview\n    try {\n      const response = await mobilePreviewService.generatePreview(emailContent, previewMode);\n      if (response.success) {\n        setPreviewUrl(response.data.previewUrl);\n        // Add to history if new\n        if (!previewHistory.some(item => item.id === response.data.id)) {\n          setPreviewHistory(prev => [response.data, ...prev]); // Add to start\n        }\n      } else {\n        setError(response.message || 'Failed to generate preview');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while generating preview');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSendTestEmail = async () => {\n    if (!emailContent.trim()) {\n      // Check email content, not just preview URL\n      setError('Email content is required to send a test');\n      return;\n    }\n    const email = prompt('Enter email address to send test to:');\n    if (!email || !/\\S+@\\S+\\.\\S+/.test(email)) {\n      // Basic email validation\n      alert('Invalid email address provided.');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await mobilePreviewService.sendTestEmail(email, emailContent);\n      if (response.success) {\n        alert(`Test email sent successfully to ${email}`); // Use alert for confirmation\n      } else {\n        setError(response.message || 'Failed to send test email');\n      }\n    } catch (err) {\n      setError(err.message || 'An error occurred while sending test email');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleLoadFromHistory = historyItem => {\n    setEmailContent(historyItem.content);\n    setPreviewUrl(historyItem.previewUrl);\n    setPreviewMode(historyItem.deviceType); // Cast might be needed if type mismatch\n    // Optionally scroll to preview or editor\n  };\n\n  // --- Render ---\n\n  return (\n    /*#__PURE__*/\n    // <Sidebar> // Removed\n    _jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-semibold text-text-primary\",\n          children: \"Mobile Preview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-secondary mb-6\",\n        children: \"Preview how your emails will look on different mobile devices before sending.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 10\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: error,\n        onClose: () => setError(null),\n        className: \"mb-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 gap-6 lg:grid-cols-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-1 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\",\n              children: \"Content & Options\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"emailContent\",\n                  className: \"sr-only\",\n                  children: \"Email HTML Content\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"emailContent\",\n                  name: \"emailContent\",\n                  rows: 15 // Increased rows\n                  ,\n                  value: emailContent,\n                  onChange: e => setEmailContent(e.target.value),\n                  placeholder: \"Paste your email HTML content here or load a template...\",\n                  className: \"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500 font-mono text-xs\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-text-secondary mb-2\",\n                  children: \"Device Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 bg-secondary-bg p-2 rounded-md border border-gray-700\",\n                  children: ['iphone', 'android', 'tablet'].map(mode => /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: mode,\n                    className: `flex items-center justify-center flex-1 px-3 py-1 rounded-md text-sm cursor-pointer transition-colors ${previewMode === mode ? 'bg-primary text-white' : 'text-text-secondary hover:bg-gray-700'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      id: mode,\n                      name: \"deviceType\",\n                      type: \"radio\",\n                      checked: previewMode === mode,\n                      onChange: () => setPreviewMode(mode) // Cast needed\n                      ,\n                      className: \"sr-only\" // Hide actual radio button\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 28\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"capitalize\",\n                      children: mode\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 28\n                    }, this)]\n                  }, mode, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 26\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-3 pt-2\",\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleGeneratePreview,\n                  disabled: loading || !emailContent.trim(),\n                  className: \"flex-1\",\n                  children: loading ? 'Generating...' : 'Generate Preview'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleSendTestEmail,\n                  disabled: loading || !emailContent.trim() // Allow sending even without preview URL now\n                  ,\n                  variant: \"secondary\",\n                  className: \"flex-1\",\n                  children: \"Send Test Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\",\n              children: \"Preview History\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), historyLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 text-center text-text-secondary\",\n              children: \"Loading history...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 21\n            }, this) : previewHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 text-center text-text-secondary\",\n              children: \"No preview history yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"divide-y divide-gray-700 max-h-60 overflow-y-auto\",\n              children: previewHistory.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"p-3 hover:bg-gray-700 cursor-pointer flex justify-between items-center\",\n                onClick: () => handleLoadFromHistory(item),\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-text-secondary capitalize\",\n                    children: item.deviceType\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 32\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-text-secondary\",\n                    children: new Date(item.createdAt).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 28\n                }, this)\n              }, item.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 14\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          className: \"lg:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\",\n            children: [\"Preview (\", previewMode, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 flex items-center justify-center bg-gray-800 min-h-[600px]\",\n            children: [\" \", previewUrl ? /*#__PURE__*/_jsxDEV(\"iframe\", {\n              // Use device-specific dimensions based on previewMode\n              style: {\n                width: previewMode === 'tablet' ? '500px' : '375px',\n                height: previewMode === 'tablet' ? '700px' : '667px',\n                border: '10px solid #333',\n                borderRadius: '20px',\n                backgroundColor: 'white' // iframe background\n              },\n              src: previewUrl,\n              title: `Mobile Preview - ${previewMode}`,\n              sandbox: \"allow-scripts allow-same-origin\" // Security: restrict iframe capabilities\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 21\n            }, this) : loading && !previewUrl ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-text-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 26\n              }, this), \"Generating preview...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 22\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center text-text-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Generate a preview to see how your email looks on a \", previewMode, \".\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                className: \"h-24 w-24 text-gray-600 mx-auto mt-4\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                stroke: \"currentColor\",\n                strokeWidth: 1,\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 28\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 26\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)\n    // </Sidebar> // Removed\n  );\n};\n_s(MobilePreview, \"JD8lbbvdtUk2AI3wSgsLQyt7Ojg=\");\n_c = MobilePreview;\nexport default MobilePreview;\nvar _c;\n$RefreshReg$(_c, \"MobilePreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "mobilePreviewService", "jsxDEV", "_jsxDEV", "MobilePreview", "_s", "loading", "setLoading", "historyLoading", "setHistoryLoading", "error", "setError", "emailContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previewUrl", "setPreviewUrl", "previewMode", "setPreviewMode", "previewHistory", "setPreviewHistory", "fetchPreviewHistory", "response", "getPreviewHistory", "success", "data", "err", "message", "console", "handleGeneratePreview", "trim", "generatePreview", "some", "item", "id", "prev", "handleSendTestEmail", "email", "prompt", "test", "alert", "sendTestEmail", "handleLoadFromHistory", "historyItem", "content", "deviceType", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onClose", "htmlFor", "name", "rows", "value", "onChange", "e", "target", "placeholder", "map", "mode", "checked", "onClick", "disabled", "variant", "length", "Date", "createdAt", "toLocaleString", "style", "width", "height", "border", "borderRadius", "backgroundColor", "src", "title", "sandbox", "xmlns", "fill", "viewBox", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/MobilePreview.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\n// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport { mobilePreviewService } from '../services'; // Corrected import path\n\n// --- Interfaces ---\n\ninterface Template {\n    id: string;\n    name: string;\n}\n\ninterface PreviewHistoryItem {\n    id: string;\n    content: string;\n    previewUrl: string;\n    deviceType: 'iphone' | 'android' | 'tablet' | string;\n    createdAt: string;\n    // Add other properties if needed\n}\n\n// --- Component ---\n\nconst MobilePreview: React.FC = () => {\n  const [loading, setLoading] = useState<boolean>(false);\n  const [historyLoading, setHistoryLoading] = useState<boolean>(true); // Separate loading for history\n  const [error, setError] = useState<string | null>(null);\n  const [emailContent, setEmailContent] = useState<string>('');\n  const [previewUrl, setPreviewUrl] = useState<string | null>(null);\n  const [previewMode, setPreviewMode] = useState<'iphone' | 'android' | 'tablet'>('iphone');\n  const [previewHistory, setPreviewHistory] = useState<PreviewHistoryItem[]>([]);\n\n  // --- Effects ---\n\n  useEffect(() => {\n    const fetchPreviewHistory = async () => {\n      setHistoryLoading(true);\n      setError(null);\n      try {\n        const response = await mobilePreviewService.getPreviewHistory();\n        if (response.success) {\n          setPreviewHistory(response.data);\n        } else {\n            setError('Failed to load preview history');\n        }\n      } catch (err: any) {\n        setError(err.message || 'Error fetching preview history');\n        console.error('Error fetching preview history:', err);\n      } finally {\n        setHistoryLoading(false);\n      }\n    };\n    fetchPreviewHistory();\n  }, []);\n\n  // --- Handlers ---\n\n  const handleGeneratePreview = async () => {\n    if (!emailContent.trim()) {\n      setError('Email content is required');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    setPreviewUrl(null); // Clear previous preview\n    try {\n      const response = await mobilePreviewService.generatePreview(emailContent, previewMode);\n      if (response.success) {\n        setPreviewUrl(response.data.previewUrl);\n        // Add to history if new\n        if (!previewHistory.some(item => item.id === response.data.id)) {\n          setPreviewHistory(prev => [response.data, ...prev]); // Add to start\n        }\n      } else {\n        setError(response.message || 'Failed to generate preview');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while generating preview');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSendTestEmail = async () => {\n    if (!emailContent.trim()) { // Check email content, not just preview URL\n      setError('Email content is required to send a test');\n      return;\n    }\n    const email = prompt('Enter email address to send test to:');\n    if (!email || !/\\S+@\\S+\\.\\S+/.test(email)) { // Basic email validation\n        alert('Invalid email address provided.');\n        return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      const response = await mobilePreviewService.sendTestEmail(email, emailContent);\n      if (response.success) {\n        alert(`Test email sent successfully to ${email}`); // Use alert for confirmation\n      } else {\n        setError(response.message || 'Failed to send test email');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while sending test email');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleLoadFromHistory = (historyItem: PreviewHistoryItem) => {\n    setEmailContent(historyItem.content);\n    setPreviewUrl(historyItem.previewUrl);\n    setPreviewMode(historyItem.deviceType as any); // Cast might be needed if type mismatch\n    // Optionally scroll to preview or editor\n  };\n\n  // --- Render ---\n\n  return (\n    // <Sidebar> // Removed\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-2xl font-semibold text-text-primary\">Mobile Preview</h1>\n        </div>\n         <p className=\"text-text-secondary mb-6\">\n            Preview how your emails will look on different mobile devices before sending.\n         </p>\n\n        {error && (\n          <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n        )}\n\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n\n          {/* Column 1: Editor & Options */} \n          <div className=\"lg:col-span-1 space-y-6\">\n            <Card>\n              <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Content & Options</h2>\n              <div className=\"p-4 space-y-4\">\n                {/* Content Editor */} \n                <div>\n                  <label htmlFor=\"emailContent\" className=\"sr-only\">Email HTML Content</label>\n                  <textarea\n                    id=\"emailContent\"\n                    name=\"emailContent\"\n                    rows={15} // Increased rows\n                    value={emailContent}\n                    onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setEmailContent(e.target.value)}\n                    placeholder=\"Paste your email HTML content here or load a template...\"\n                    className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500 font-mono text-xs\"\n                  />\n                </div>\n\n                {/* Device Type Selector */} \n                <div>\n                  <label className=\"block text-sm font-medium text-text-secondary mb-2\">\n                    Device Type\n                  </label>\n                  <div className=\"flex items-center space-x-4 bg-secondary-bg p-2 rounded-md border border-gray-700\">\n                    {[ 'iphone', 'android', 'tablet' ].map((mode) => (\n                         <label key={mode} htmlFor={mode} className={`flex items-center justify-center flex-1 px-3 py-1 rounded-md text-sm cursor-pointer transition-colors ${previewMode === mode ? 'bg-primary text-white' : 'text-text-secondary hover:bg-gray-700'}`}>\n                           <input\n                             id={mode}\n                             name=\"deviceType\"\n                             type=\"radio\"\n                             checked={previewMode === mode}\n                             onChange={() => setPreviewMode(mode as any)} // Cast needed\n                             className=\"sr-only\" // Hide actual radio button\n                           />\n                           <span className=\"capitalize\">{mode}</span>\n                         </label>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Actions */} \n                <div className=\"flex space-x-3 pt-2\">\n                  <Button\n                    onClick={handleGeneratePreview}\n                    disabled={loading || !emailContent.trim()}\n                    className=\"flex-1\"\n                  >\n                    {loading ? 'Generating...' : 'Generate Preview'}\n                  </Button>\n                  <Button\n                    onClick={handleSendTestEmail}\n                    disabled={loading || !emailContent.trim()} // Allow sending even without preview URL now\n                    variant=\"secondary\"\n                    className=\"flex-1\"\n                  >\n                    Send Test Email\n                  </Button>\n                </div>\n              </div>\n            </Card>\n\n             {/* Preview History */}\n             <Card>\n                <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Preview History</h2>\n                 {historyLoading ? (\n                    <div className=\"p-4 text-center text-text-secondary\">Loading history...</div>\n                 ) : previewHistory.length === 0 ? (\n                    <div className=\"p-4 text-center text-text-secondary\">No preview history yet.</div>\n                 ) : (\n                    <ul className=\"divide-y divide-gray-700 max-h-60 overflow-y-auto\">\n                      {previewHistory.map((item) => (\n                        <li key={item.id} className=\"p-3 hover:bg-gray-700 cursor-pointer flex justify-between items-center\" onClick={() => handleLoadFromHistory(item)}>\n                           <div>\n                               <p className=\"text-xs text-text-secondary capitalize\">{item.deviceType}</p>\n                               <p className=\"text-xs text-text-secondary\">{new Date(item.createdAt).toLocaleString()}</p>\n                           </div>\n                           {/* Optionally add a small visual cue or icon */} \n                        </li>\n                      ))}\n                    </ul>\n                 )}\n             </Card>\n          </div>\n\n          {/* Column 2 & 3: Preview Area */} \n          <Card className=\"lg:col-span-2\">\n            <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Preview ({previewMode})</h2>\n            <div className=\"p-4 flex items-center justify-center bg-gray-800 min-h-[600px]\"> {/* Darker bg for contrast */}\n                {previewUrl ? (\n                    <iframe\n                        // Use device-specific dimensions based on previewMode\n                        style={{\n                            width: previewMode === 'tablet' ? '500px' : '375px', \n                            height: previewMode === 'tablet' ? '700px' : '667px',\n                            border: '10px solid #333', \n                            borderRadius: '20px',\n                            backgroundColor: 'white' // iframe background\n                        }}\n                        src={previewUrl}\n                        title={`Mobile Preview - ${previewMode}`}\n                        sandbox=\"allow-scripts allow-same-origin\" // Security: restrict iframe capabilities\n                    />\n                ) : loading && !previewUrl ? (\n                     <div className=\"text-center text-text-secondary\">\n                         <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4\"></div>\n                         Generating preview...\n                     </div>\n                 ) : (\n                    <div className=\"text-center text-text-secondary\">\n                        <p>Generate a preview to see how your email looks on a {previewMode}.</p>\n                        {/* Placeholder icon */} \n                         <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-24 w-24 text-gray-600 mx-auto mt-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={1}>\n                           <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                         </svg>\n                    </div>\n                 )}\n            </div>\n          </Card>\n\n        </div>\n      </div>\n    // </Sidebar> // Removed\n  );\n};\n\nexport default MobilePreview;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAEVC,SAAS,EACTC,QAAQ,QACH,OAAO;AAEd,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,IAAI,MAAM,oBAAoB;AACrC;AACA,SAASC,oBAAoB,QAAQ,aAAa,CAAC,CAAC;;AAEpD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAgBA;;AAEA,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGZ,QAAQ,CAAU,IAAI,CAAC,CAAC,CAAC;EACrE,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAS,EAAE,CAAC;EAC5D,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACjE,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAkC,QAAQ,CAAC;EACzF,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAuB,EAAE,CAAC;;EAE9E;;EAEAD,SAAS,CAAC,MAAM;IACd,MAAMwB,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtCX,iBAAiB,CAAC,IAAI,CAAC;MACvBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACF,MAAMU,QAAQ,GAAG,MAAMpB,oBAAoB,CAACqB,iBAAiB,CAAC,CAAC;QAC/D,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpBJ,iBAAiB,CAACE,QAAQ,CAACG,IAAI,CAAC;QAClC,CAAC,MAAM;UACHb,QAAQ,CAAC,gCAAgC,CAAC;QAC9C;MACF,CAAC,CAAC,OAAOc,GAAQ,EAAE;QACjBd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;QACzDC,OAAO,CAACjB,KAAK,CAAC,iCAAiC,EAAEe,GAAG,CAAC;MACvD,CAAC,SAAS;QACRhB,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IACDW,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA,MAAMQ,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAChB,YAAY,CAACiB,IAAI,CAAC,CAAC,EAAE;MACxBlB,QAAQ,CAAC,2BAA2B,CAAC;MACrC;IACF;IACAJ,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACdI,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IACrB,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMpB,oBAAoB,CAAC6B,eAAe,CAAClB,YAAY,EAAEI,WAAW,CAAC;MACtF,IAAIK,QAAQ,CAACE,OAAO,EAAE;QACpBR,aAAa,CAACM,QAAQ,CAACG,IAAI,CAACV,UAAU,CAAC;QACvC;QACA,IAAI,CAACI,cAAc,CAACa,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKZ,QAAQ,CAACG,IAAI,CAACS,EAAE,CAAC,EAAE;UAC9Dd,iBAAiB,CAACe,IAAI,IAAI,CAACb,QAAQ,CAACG,IAAI,EAAE,GAAGU,IAAI,CAAC,CAAC,CAAC,CAAC;QACvD;MACF,CAAC,MAAM;QACLvB,QAAQ,CAACU,QAAQ,CAACK,OAAO,IAAI,4BAA4B,CAAC;MAC5D;IACF,CAAC,CAAC,OAAOD,GAAQ,EAAE;MACjBd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,4CAA4C,CAAC;IACvE,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACvB,YAAY,CAACiB,IAAI,CAAC,CAAC,EAAE;MAAE;MAC1BlB,QAAQ,CAAC,0CAA0C,CAAC;MACpD;IACF;IACA,MAAMyB,KAAK,GAAGC,MAAM,CAAC,sCAAsC,CAAC;IAC5D,IAAI,CAACD,KAAK,IAAI,CAAC,cAAc,CAACE,IAAI,CAACF,KAAK,CAAC,EAAE;MAAE;MACzCG,KAAK,CAAC,iCAAiC,CAAC;MACxC;IACJ;IACAhC,UAAU,CAAC,IAAI,CAAC;IAChBI,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF,MAAMU,QAAQ,GAAG,MAAMpB,oBAAoB,CAACuC,aAAa,CAACJ,KAAK,EAAExB,YAAY,CAAC;MAC9E,IAAIS,QAAQ,CAACE,OAAO,EAAE;QACpBgB,KAAK,CAAC,mCAAmCH,KAAK,EAAE,CAAC,CAAC,CAAC;MACrD,CAAC,MAAM;QACLzB,QAAQ,CAACU,QAAQ,CAACK,OAAO,IAAI,2BAA2B,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOD,GAAQ,EAAE;MACjBd,QAAQ,CAACc,GAAG,CAACC,OAAO,IAAI,4CAA4C,CAAC;IACvE,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkC,qBAAqB,GAAIC,WAA+B,IAAK;IACjE7B,eAAe,CAAC6B,WAAW,CAACC,OAAO,CAAC;IACpC5B,aAAa,CAAC2B,WAAW,CAAC5B,UAAU,CAAC;IACrCG,cAAc,CAACyB,WAAW,CAACE,UAAiB,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC;;EAED;;EAEA;IAAA;IACE;IACEzC,OAAA;MAAK0C,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1C3C,OAAA;QAAK0C,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD3C,OAAA;UAAI0C,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eACL/C,OAAA;QAAG0C,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAC;MAExC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAEJxC,KAAK,iBACJP,OAAA,CAACL,KAAK;QAACqD,IAAI,EAAC,OAAO;QAACzB,OAAO,EAAEhB,KAAM;QAAC0C,OAAO,EAAEA,CAAA,KAAMzC,QAAQ,CAAC,IAAI,CAAE;QAACkC,SAAS,EAAC;MAAM;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CACtF,eAED/C,OAAA;QAAK0C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAGpD3C,OAAA;UAAK0C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC3C,OAAA,CAACH,IAAI;YAAA8C,QAAA,gBACH3C,OAAA;cAAI0C,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3G/C,OAAA;cAAK0C,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAE5B3C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAOkD,OAAO,EAAC,cAAc;kBAACR,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5E/C,OAAA;kBACE8B,EAAE,EAAC,cAAc;kBACjBqB,IAAI,EAAC,cAAc;kBACnBC,IAAI,EAAE,EAAG,CAAC;kBAAA;kBACVC,KAAK,EAAE5C,YAAa;kBACpB6C,QAAQ,EAAGC,CAAmC,IAAK7C,eAAe,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACnFI,WAAW,EAAC,0DAA0D;kBACtEf,SAAS,EAAC;gBAAmN;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9N,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN/C,OAAA;gBAAA2C,QAAA,gBACE3C,OAAA;kBAAO0C,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,EAAC;gBAEtE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR/C,OAAA;kBAAK0C,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,EAC/F,CAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAE,CAACe,GAAG,CAAEC,IAAI,iBACvC3D,OAAA;oBAAkBkD,OAAO,EAAES,IAAK;oBAACjB,SAAS,EAAE,yGAAyG7B,WAAW,KAAK8C,IAAI,GAAG,uBAAuB,GAAG,uCAAuC,EAAG;oBAAAhB,QAAA,gBAC9O3C,OAAA;sBACE8B,EAAE,EAAE6B,IAAK;sBACTR,IAAI,EAAC,YAAY;sBACjBH,IAAI,EAAC,OAAO;sBACZY,OAAO,EAAE/C,WAAW,KAAK8C,IAAK;sBAC9BL,QAAQ,EAAEA,CAAA,KAAMxC,cAAc,CAAC6C,IAAW,CAAE,CAAC;sBAAA;sBAC7CjB,SAAS,EAAC,SAAS,CAAC;oBAAA;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACF/C,OAAA;sBAAM0C,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEgB;oBAAI;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAThCY,IAAI;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUT,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN/C,OAAA;gBAAK0C,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClC3C,OAAA,CAACJ,MAAM;kBACLiE,OAAO,EAAEpC,qBAAsB;kBAC/BqC,QAAQ,EAAE3D,OAAO,IAAI,CAACM,YAAY,CAACiB,IAAI,CAAC,CAAE;kBAC1CgB,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EAEjBxC,OAAO,GAAG,eAAe,GAAG;gBAAkB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACT/C,OAAA,CAACJ,MAAM;kBACLiE,OAAO,EAAE7B,mBAAoB;kBAC7B8B,QAAQ,EAAE3D,OAAO,IAAI,CAACM,YAAY,CAACiB,IAAI,CAAC,CAAE,CAAC;kBAAA;kBAC3CqC,OAAO,EAAC,WAAW;kBACnBrB,SAAS,EAAC,QAAQ;kBAAAC,QAAA,EACnB;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGN/C,OAAA,CAACH,IAAI;YAAA8C,QAAA,gBACF3C,OAAA;cAAI0C,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACvG1C,cAAc,gBACZL,OAAA;cAAK0C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,GAC5EhC,cAAc,CAACiD,MAAM,KAAK,CAAC,gBAC5BhE,OAAA;cAAK0C,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAElF/C,OAAA;cAAI0C,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9D5B,cAAc,CAAC2C,GAAG,CAAE7B,IAAI,iBACvB7B,OAAA;gBAAkB0C,SAAS,EAAC,wEAAwE;gBAACmB,OAAO,EAAEA,CAAA,KAAMvB,qBAAqB,CAACT,IAAI,CAAE;gBAAAc,QAAA,eAC7I3C,OAAA;kBAAA2C,QAAA,gBACI3C,OAAA;oBAAG0C,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAEd,IAAI,CAACY;kBAAU;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3E/C,OAAA;oBAAG0C,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,EAAE,IAAIsB,IAAI,CAACpC,IAAI,CAACqC,SAAS,CAAC,CAACC,cAAc,CAAC;kBAAC;oBAAAvB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF;cAAC,GAJAlB,IAAI,CAACC,EAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMZ,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/C,OAAA,CAACH,IAAI;UAAC6C,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC7B3C,OAAA;YAAI0C,SAAS,EAAC,sEAAsE;YAAAC,QAAA,GAAC,WAAS,EAAC9B,WAAW,EAAC,GAAC;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjH/C,OAAA;YAAK0C,SAAS,EAAC,gEAAgE;YAAAC,QAAA,GAAC,GAAC,EAC5EhC,UAAU,gBACPX,OAAA;cACI;cACAoE,KAAK,EAAE;gBACHC,KAAK,EAAExD,WAAW,KAAK,QAAQ,GAAG,OAAO,GAAG,OAAO;gBACnDyD,MAAM,EAAEzD,WAAW,KAAK,QAAQ,GAAG,OAAO,GAAG,OAAO;gBACpD0D,MAAM,EAAE,iBAAiB;gBACzBC,YAAY,EAAE,MAAM;gBACpBC,eAAe,EAAE,OAAO,CAAC;cAC7B,CAAE;cACFC,GAAG,EAAE/D,UAAW;cAChBgE,KAAK,EAAE,oBAAoB9D,WAAW,EAAG;cACzC+D,OAAO,EAAC,iCAAiC,CAAC;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,GACF5C,OAAO,IAAI,CAACQ,UAAU,gBACrBX,OAAA;cAAK0C,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC5C3C,OAAA;gBAAK0C,SAAS,EAAC;cAAuF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,yBAEjH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,gBAEP/C,OAAA;cAAK0C,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC5C3C,OAAA;gBAAA2C,QAAA,GAAG,sDAAoD,EAAC9B,WAAW,EAAC,GAAC;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAExE/C,OAAA;gBAAK6E,KAAK,EAAC,4BAA4B;gBAACnC,SAAS,EAAC,sCAAsC;gBAACoC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAE,CAAE;gBAAAtC,QAAA,eAC5J3C,OAAA;kBAAMkF,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAA8E;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;IACP;EAAA;AAEJ,CAAC;AAAC7C,EAAA,CA3OID,aAAuB;AAAAoF,EAAA,GAAvBpF,aAAuB;AA6O7B,eAAeA,aAAa;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}