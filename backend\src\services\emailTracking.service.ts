import CampaignRecipient from '../models/campaign-recipient.model';
import Campaign from '../models/campaign.model';

/**
 * Service to handle email tracking events from Amazon SES
 */
export const EmailTrackingService = {
  /**
   * Process a bounce event
   * @param {Object} bounceData The bounce data from SES
   * @param {string} campaignId The campaign ID (from message tags)
   */
  processBounce: async (bounceData: any, campaignId?: string): Promise<void> => {
    try {
      if (!bounceData || !Array.isArray(bounceData.bouncedRecipients) || bounceData.bouncedRecipients.length === 0) {
        console.log('[EmailTracking] Invalid bounce data structure or no recipients');
        return;
      }

      // Extract bounced email addresses
      const bouncedEmails = bounceData.bouncedRecipients.map((recipient: any) => recipient.emailAddress);
      console.log(`[EmailTracking] Processing bounce for ${bouncedEmails.length} recipients: ${bouncedEmails.join(', ')}`);
      
      // Update campaign recipients status to 'bounced'
      if (campaignId) {
        // If we have a campaignId, update only recipients for this campaign
        const updateResult = await CampaignRecipient.updateMany(
          { 
            campaignId,
            email: { $in: bouncedEmails },
            status: { $ne: 'bounced' } // Only update if not already bounced
          },
          { 
            $set: { 
              status: 'bounced',
              bouncedAt: new Date()
            }
          }
        );
        
        console.log(`[EmailTracking] Updated ${updateResult.modifiedCount} campaign recipients to bounced status`);
        
        // Update campaign bounce count
        if (updateResult.modifiedCount > 0) {
          const campaign = await Campaign.findById(campaignId);
          if (campaign) {
            campaign.bounceCount += updateResult.modifiedCount;
            await campaign.save();
            console.log(`[EmailTracking] Updated campaign ${campaignId} bounce count to ${campaign.bounceCount}`);
          }
        }
      } else {
        // If no campaignId, update all matching recipients
        const updateResult = await CampaignRecipient.updateMany(
          { 
            email: { $in: bouncedEmails },
            status: { $ne: 'bounced' } // Only update if not already bounced
          },
          { 
            $set: { 
              status: 'bounced',
              bouncedAt: new Date()
            }
          }
        );
        
        console.log(`[EmailTracking] Updated ${updateResult.modifiedCount} campaign recipients to bounced status (no campaign ID)`);
      }
    } catch (error) {
      console.error('[EmailTracking] Error processing bounce:', error);
    }
  },

  /**
   * Process a complaint event
   * @param {Object} complaintData The complaint data from SES
   * @param {string} campaignId The campaign ID (from message tags)
   */
  processComplaint: async (complaintData: any, campaignId?: string): Promise<void> => {
    try {
      if (!complaintData || !Array.isArray(complaintData.complainedRecipients) || complaintData.complainedRecipients.length === 0) {
        console.log('[EmailTracking] Invalid complaint data structure or no recipients');
        return;
      }

      // Extract complained email addresses
      const complainedEmails = complaintData.complainedRecipients.map((recipient: any) => recipient.emailAddress);
      console.log(`[EmailTracking] Processing complaint for ${complainedEmails.length} recipients: ${complainedEmails.join(', ')}`);
      
      // Update campaign recipients status to 'complained'
      if (campaignId) {
        // If we have a campaignId, update only recipients for this campaign
        const updateResult = await CampaignRecipient.updateMany(
          { 
            campaignId,
            email: { $in: complainedEmails },
            status: { $ne: 'complained' } // Only update if not already marked as complained
          },
          { 
            $set: { 
              status: 'complained',
              complainedAt: new Date()
            }
          }
        );
        
        console.log(`[EmailTracking] Updated ${updateResult.modifiedCount} campaign recipients to complained status`);
        
        // Update campaign complaint count
        if (updateResult.modifiedCount > 0) {
          try {
            const campaign = await Campaign.findById(campaignId);
            if (campaign) {
              campaign.complaintCount = (campaign.complaintCount || 0) + updateResult.modifiedCount;
              await campaign.save();
              console.log(`[EmailTracking] Updated campaign ${campaignId} complaint count to ${campaign.complaintCount}`);
            } else {
              console.warn(`[EmailTracking] Campaign ${campaignId} not found while trying to update complaint count.`);
            }
          } catch(campaignError) {
            console.error(`[EmailTracking] Error updating campaign complaint count for ${campaignId}:`, campaignError);
          }
        }
      } else {
        // If no campaignId, update all matching recipients
        const updateResult = await CampaignRecipient.updateMany(
          { 
            email: { $in: complainedEmails },
            status: { $ne: 'complained' } // Only update if not already marked as complained
          },
          { 
            $set: { 
              status: 'complained',
              complainedAt: new Date()
            }
          }
        );
        
        console.log(`[EmailTracking] Updated ${updateResult.modifiedCount} campaign recipients to complained status (no campaign ID)`);
      }
    } catch (error) {
      console.error('[EmailTracking] Error processing complaint:', error);
    }
  },

  /**
   * Process an open event
   * @param {Object} openData The open data from SES
   * @param {string} campaignId The campaign ID (from message tags)
   * @param {string} subscriberId The subscriber ID (from message tags)
   */
  processOpen: async (openData: any, campaignId?: string, subscriberId?: string): Promise<void> => {
    try {
      if (!campaignId) {
        console.log('[EmailTracking] No campaign ID for open event, skipping');
        return;
      }

      console.log(`[EmailTracking] Processing open event for campaign ${campaignId}${subscriberId ? ` and subscriber ${subscriberId}` : ''}`);
      
      // Update campaign recipient if we have a subscriber ID
      if (subscriberId) {
        const campaignRecipient = await CampaignRecipient.findOne({
          campaignId,
          recipientId: subscriberId,
          status: { $nin: ['opened', 'clicked'] } // Only update if not already opened or clicked
        });
        
        if (campaignRecipient) {
          campaignRecipient.status = 'opened';
          campaignRecipient.openedAt = new Date();
          await campaignRecipient.save();
          console.log(`[EmailTracking] Updated campaign recipient ${campaignRecipient._id} to opened status`);
        } else {
          console.log(`[EmailTracking] No matching campaign recipient found for campaignId=${campaignId}, subscriberId=${subscriberId}`);
        }
      }
      
      // Update campaign open count
      const campaign = await Campaign.findById(campaignId);
      if (campaign) {
        campaign.openCount += 1;
        // Update open rate (opens / sent)
        if (campaign.sentCount > 0) {
          campaign.openRate = campaign.openCount / campaign.sentCount;
        }
        await campaign.save();
        console.log(`[EmailTracking] Updated campaign ${campaignId} open count to ${campaign.openCount} (rate: ${campaign.openRate.toFixed(2)})`);
      }
    } catch (error) {
      console.error('[EmailTracking] Error processing open:', error);
    }
  },

  /**
   * Process a click event
   * @param {Object} clickData The click data from SES
   * @param {string} campaignId The campaign ID (from message tags)
   * @param {string} subscriberId The subscriber ID (from message tags)
   */
  processClick: async (clickData: any, campaignId?: string, subscriberId?: string): Promise<void> => {
    try {
      if (!campaignId) {
        console.log('[EmailTracking] No campaign ID for click event, skipping');
        return;
      }

      console.log(`[EmailTracking] Processing click event for campaign ${campaignId}${subscriberId ? ` and subscriber ${subscriberId}` : ''}`);
      console.log(`[EmailTracking] Click link: ${clickData.link || 'Unknown'}`);
      
      // Update campaign recipient if we have a subscriber ID
      if (subscriberId) {
        const campaignRecipient = await CampaignRecipient.findOne({
          campaignId,
          recipientId: subscriberId,
          status: { $ne: 'clicked' } // Only update if not already clicked
        });
        
        if (campaignRecipient) {
          campaignRecipient.status = 'clicked';
          campaignRecipient.clickedAt = new Date();
          await campaignRecipient.save();
          console.log(`[EmailTracking] Updated campaign recipient ${campaignRecipient._id} to clicked status`);
        } else {
          console.log(`[EmailTracking] No matching campaign recipient found for campaignId=${campaignId}, subscriberId=${subscriberId}`);
        }
      }
      
      // Update campaign click count
      const campaign = await Campaign.findById(campaignId);
      if (campaign) {
        campaign.clickCount += 1;
        // Update click rate (clicks / sent)
        if (campaign.sentCount > 0) {
          campaign.clickRate = campaign.clickCount / campaign.sentCount;
        }
        await campaign.save();
        console.log(`[EmailTracking] Updated campaign ${campaignId} click count to ${campaign.clickCount} (rate: ${campaign.clickRate.toFixed(2)})`);
      }
    } catch (error) {
      console.error('[EmailTracking] Error processing click:', error);
    }
  },

  /**
   * Extract campaign ID from SES event
   * @param {Object} message The SES event message
   * @returns {string|undefined} The campaign ID or undefined
   */
  extractCampaignId: (message: any): string | undefined => {
    try {
      // Try to get from tags
      const { mail } = message;
      if (mail?.tags?.campaignId) {
        return mail.tags.campaignId[0];
      }
      
      // Try to get from message ID if it follows our pattern
      if (mail?.messageId) {
        const match = mail.messageId.match(/campaign-([a-zA-Z0-9]+)/);
        if (match && match[1]) {
          return match[1];
        }
      }
      
      return undefined;
    } catch (error) {
      console.error('[EmailTracking] Error extracting campaign ID:', error);
      return undefined;
    }
  },

  /**
   * Extract subscriber ID from SES event
   * @param {Object} message The SES event message
   * @returns {string|undefined} The subscriber ID or undefined
   */
  extractSubscriberId: (message: any): string | undefined => {
    try {
      // Try to get from tags
      const { mail } = message;
      if (mail?.tags?.subscriberId) {
        return mail.tags.subscriberId[0];
      }
      
      return undefined;
    } catch (error) {
      console.error('[EmailTracking] Error extracting subscriber ID:', error);
      return undefined;
    }
  }
};

export default EmailTrackingService; 