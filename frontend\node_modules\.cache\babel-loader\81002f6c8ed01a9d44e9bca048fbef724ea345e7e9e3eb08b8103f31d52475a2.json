{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd\\src\\types\\connectors.ts"], "sourcesContent": ["import type { ReactElement, RefObject } from 'react'\n\nimport type { DragPreviewOptions, DragSourceOptions } from './options'\n\nexport type ConnectableElement = RefObject<any> | ReactElement | Element | null\n\nexport type DragElementWrapper<Options> = (\n\telementOrNode: ConnectableElement,\n\toptions?: Options,\n) => ReactElement | null\n\nexport type ConnectDragSource = DragElementWrapper<DragSourceOptions>\nexport type ConnectDropTarget = DragElementWrapper<any>\nexport type ConnectDragPreview = DragElementWrapper<DragPreviewOptions>\n"], "mappings": "AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}