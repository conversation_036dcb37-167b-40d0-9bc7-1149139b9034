import mongoose from 'mongoose';

import Template from '../models/template.model';

const URI = "mongodb+srv://oblongjones1992:<EMAIL>/driftly?retryWrites=true&w=majority&appName=driftly-db";

/**
 * <PERSON>ript to delete all system templates from the database
 */
const deleteSystemTemplates = async () => {
  try {
    // Connect to MongoDB directly with URI
    console.log('Attempting to connect to MongoDB...');
    await mongoose.connect(URI);
    console.log('Connected to MongoDB');

    // Get the count of system templates before deletion
    const count = await Template.countDocuments({ isSystem: true });
    console.log(`Found ${count} system templates in the database`);

    // Delete only system templates
    const result = await Template.deleteMany({ isSystem: true });
    
    console.log(`✅ Successfully deleted ${result.deletedCount} system templates`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ Error deleting system templates:', error);
    if (mongoose.connection.readyState) {
      await mongoose.disconnect();
    }
    process.exit(1);
  }
};

// Run the script
deleteSystemTemplates(); 