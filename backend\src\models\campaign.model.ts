import mongoose, {
  Document,
  Schema,
} from 'mongoose';

export interface ICampaign extends Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  subject: string;
  fromName: string;
  fromEmail: string;
  replyTo?: string;
  htmlContent: string;
  cssContent?: string;
  status: 'draft' | 'scheduled' | 'sending' | 'completed' | 'failed' | 'sent' | 'cancelled';
  emailContents?: { html: string; css?: string }[];
  schedule?: {
    unit: 'minutes' | 'hours' | 'days';
    emailIntervals: { delay: number; unit: 'minutes' | 'hours' | 'days' }[];
  };
  scheduledFor?: Date;
  startedAt?: Date;
  completedAt?: Date;
  sentCount: number;
  errorCount: number;
  openCount: number;
  clickCount: number;
  bounceCount: number;
  complaintCount: number;
  unsubscribeCount: number;
  recipients: number;
  openRate: number;
  clickRate: number;
  errorDescription?: string;
  mosaicoJson?: any;
  createdAt: Date;
  updatedAt: Date;
}

const campaignSchema = new Schema<ICampaign>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    name: {
      type: String,
      required: true,
      trim: true
    },
    subject: {
      type: String,
      required: true,
      trim: true
    },
    fromName: {
      type: String,
      required: true,
      trim: true
    },
    fromEmail: {
      type: String,
      required: true,
      trim: true
    },
    replyTo: {
      type: String,
      trim: true
    },
    htmlContent: {
      type: String,
      required: true
    },
    cssContent: {
      type: String
    },
    status: {
      type: String,
      enum: ['draft', 'scheduled', 'sending', 'completed', 'failed', 'sent', 'cancelled'],
      default: 'draft'
    },
    emailContents: [
      {
        html: { type: String, required: true },
        css: { type: String, required: false }
      }
    ],
    schedule: {
      unit: { type: String, enum: ['minutes', 'hours', 'days'], required: false },
      emailIntervals: [
        {
          delay: { type: Number, required: true },
          unit: { type: String, enum: ['minutes', 'hours', 'days'], required: true }
        }
      ]
    },
    scheduledFor: {
      type: Date
    },
    startedAt: {
      type: Date
    },
    completedAt: {
      type: Date
    },
    sentCount: {
      type: Number,
      default: 0
    },
    errorCount: {
      type: Number,
      default: 0
    },
    openCount: {
      type: Number,
      default: 0
    },
    clickCount: {
      type: Number,
      default: 0
    },
    bounceCount: {
      type: Number,
      default: 0
    },
    complaintCount: {
      type: Number,
      default: 0
    },
    unsubscribeCount: {
      type: Number,
      default: 0
    },
    recipients: {
      type: Number,
      default: 0
    },
    openRate: {
      type: Number,
      default: 0
    },
    clickRate: {
      type: Number,
      default: 0
    },
    errorDescription: {
      type: String,
      required: false
    },
    mosaicoJson: {
      type: Schema.Types.Mixed,
      required: false
    }
  },
  {
    timestamps: true
  }
);

const Campaign = mongoose.model<ICampaign>('Campaign', campaignSchema);

export default Campaign;
