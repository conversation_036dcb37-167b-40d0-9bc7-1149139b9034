{"version": 3, "file": "static/js/630.6dad9ca5.chunk.js", "mappings": "oNAgCA,MAwQA,EAxQ0CA,KACxC,MAAOC,EAASC,IAAcC,EAAAA,EAAAA,UAAuC,OAC9DC,EAASC,IAAcF,EAAAA,EAAAA,WAAkB,IACzCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAC3CK,EAAkBC,IAAuBN,EAAAA,EAAAA,UAAS,CAAEO,QAAS,GAAIC,QAAS,MAC1EC,EAAWC,IAAgBV,EAAAA,EAAAA,UAA2B,OACtDW,EAAkBC,IAAuBZ,EAAAA,EAAAA,WAAkB,IAC3Da,EAAWC,IAAgBd,EAAAA,EAAAA,UAAkD,aAGpFe,EAAAA,EAAAA,YAAU,KACaC,WACnBd,GAAW,GACXE,EAAS,MACT,IACE,MAAMa,QAAiBC,EAAAA,GAAsBC,aACzCF,EAASG,QACXrB,EAAWkB,EAASI,MAEpBjB,EAASa,EAASK,SAAW,yCAEjC,CAAE,MAAOC,GACPnB,EAASmB,EAAID,SAAW,2CAC1B,CAAC,QACCpB,GAAW,EACb,GAGFsB,EAAc,GACb,IAGH,MA+BMC,EAAiBA,CAACC,EAA2BC,SACnCC,IAAVF,EAA4B,gBAC5BA,GAASC,EAAWE,KACf,iBACEH,GAASC,EAAWG,QACtB,kBAEA,eAKLC,EAAsBC,QACZJ,IAAVI,EAA4B,CAAEC,KAAM,MAAOC,MAAO,iBAClDF,EAAQ,EACH,CAAEC,KAAM,WAAYC,MAAO,kBACzBF,EAAQ,EACV,CAAEC,KAAM,cAAeC,MAAO,mBAE9B,CAAED,KAAM,YAAaC,MAAO,gBAKvC,OAEIC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACpDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SAAC,4BAE3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,2DAIxClC,IACEmC,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,KAAK,QAAQlB,QAASnB,EAAOsC,QAASA,IAAMrC,EAAS,MAAOgC,UAAU,UAGhFD,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,kBAAiBC,SAAA,EAE9BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACGF,UAAW,+DACK,aAAdvB,EACI,8BACA,wFAEN8B,QAASA,IAAM7B,EAAa,YAAYuB,SAC1C,cAGDC,EAAAA,EAAAA,KAAA,UACGF,UAAW,+DACK,YAAdvB,EACI,8BACA,wFAEN8B,QAASA,IAAM7B,EAAa,WAAWuB,SACzC,sBAGDC,EAAAA,EAAAA,KAAA,UACGF,UAAW,+DACK,iBAAdvB,EACI,8BACA,wFAEN8B,QAASA,IAAM7B,EAAa,gBAAgBuB,SAC7C,uBAOLC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SAClBpC,GACEqC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACtCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+EAEhBtC,GAAyB,iBAAde,GAGbsB,EAAAA,EAAAA,MAAAS,EAAAA,SAAA,CAAAP,SAAA,CAEiB,aAAdxB,GAA4Bf,IAC5BqC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,6BAC7DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EAClEC,EAAAA,EAAAA,KAACO,EAAAA,EAAQ,CAACC,MAAM,gBAAgBpB,MAAO,GAAG5B,EAAQiD,cAAgB,KAAMC,KAAK,cAC7EV,EAAAA,EAAAA,KAACO,EAAAA,EAAQ,CAACC,MAAM,YAAYpB,MAAO,GAAG5B,EAAQmD,UAAY,KAAMD,KAAK,SACrEV,EAAAA,EAAAA,KAACO,EAAAA,EAAQ,CAACC,MAAM,cAAcpB,MAAO,GAAG5B,EAAQoD,YAAc,KAAMF,KAAK,wBACzEV,EAAAA,EAAAA,KAACO,EAAAA,EAAQ,CAACC,MAAM,aAAapB,MAAO,GAAG5B,EAAQqD,WAAa,KAAMH,KAAK,uBAO9D,YAAdnC,GAA2Bf,IACzBqC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,sBAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAElDF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,qCAAoCC,SAAA,EACjDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,uBAC1DC,EAAAA,EAAAA,KAAA,KAAGF,UAAW,2BAA2BX,EAAe3B,EAAQsD,YAAa,CAAEvB,KAAM,GAAIC,QAAS,OAAQO,cAC7ET,IAAxB9B,EAAQsD,YAA4B,GAAGtD,EAAQsD,oBAAsB,SAE1Ed,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8BAA6BC,SAAC,sEAG9CF,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,qCAAoCC,SAAA,EACjDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,oBAC3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAW,2BAA2BX,OAAyCG,IAA1B9B,EAAQuD,cAA8B,IAA8B,IAAxBvD,EAAQuD,mBAAsBzB,EAAW,CAAEC,KAAM,KAAMC,QAAS,SAAUO,cAC/IT,IAA1B9B,EAAQuD,cAA8B,GAAGvD,EAAQuD,cAAcC,QAAQ,MAAQ,SAEpFhB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8BAA6BC,SAAC,8DAQ1C,iBAAdxB,IACGsB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,wBAC7DF,EAAAA,EAAAA,MAAA,QAAMoB,SAhKHvC,UAGtB,GAFAwC,EAAEC,iBAEGpD,EAAiBE,QAAQmD,QAAWrD,EAAiBG,QAAQkD,OAAlE,CAKA9C,GAAoB,GACpBR,EAAS,MACTM,EAAa,MAEb,IACE,MAAMO,QAAiBC,EAAAA,GAAsByC,eAC3CtD,EAAiBE,QACjBF,EAAiBG,SAGfS,EAASG,QACXV,EAAaO,EAASI,MAEtBjB,EAASa,EAASK,SAAW,6BAEjC,CAAE,MAAOC,GACPnB,EAASmB,EAAID,SAAW,8CAC1B,CAAC,QACCV,GAAoB,EACtB,CArBA,MAFER,EAAS,kDAuBX,EAqIoDgC,UAAU,iBAAgBC,SAAA,EACxDC,EAAAA,EAAAA,KAACsB,EAAAA,EAAK,CACHC,MAAM,eACNC,GAAG,cACHC,KAAK,cACLrC,MAAOrB,EAAiBE,QACxByD,SAAWR,GAAqClD,EAAoB,IAAID,EAAkBE,QAASiD,EAAES,OAAOvC,QAC5GwC,UAAQ,KAGV/B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,SAAO6B,QAAQ,cAAc/B,UAAU,qDAAoDC,SAAC,wCAG5FC,EAAAA,EAAAA,KAAA,YACGwB,GAAG,cACHC,KAAK,cACLK,KAAM,GACN1C,MAAOrB,EAAiBG,QACxBwD,SAAWR,GAAwClD,EAAoB,IAAID,EAAkBG,QAASgD,EAAES,OAAOvC,QAC/GwC,UAAQ,EACR9B,UAAU,wMAGhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC9BC,EAAAA,EAAAA,KAAC+B,EAAAA,EAAM,CAAC7B,KAAK,SAAS8B,SAAU3D,EAAiB0B,SAC7C1B,EAAmB,cAAgB,0BAK5CA,IACGwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BC,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4EACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2BAA0BC,SAAC,8BAIpC,OAAd5B,IAEE0B,EAAAA,EAAAA,MAACO,EAAAA,EAAI,CAACN,UAAU,qCAAoCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,wBAC3DF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,CAAC,kBAC7CC,EAAAA,EAAAA,KAAA,QAAMF,UAAW,0BAA0BL,EAAmBtB,EAAUuB,OAAOE,QAAQG,cAAsBT,IAApBnB,EAAUuB,MAAsBvB,EAAUuB,MAAMsB,QAAQ,GAAK,YAEzJnB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,CAAC,WAC5CC,EAAAA,EAAAA,KAAA,QAAMF,UAAW,sBAAsBL,EAAmBtB,EAAUuB,OAAOE,QAAQG,SAAEN,EAAmBtB,EAAUuB,OAAOC,UAE3HxB,EAAU8D,QAAU9D,EAAU8D,OAAOC,OAAS,IAC5CrC,EAAAA,EAAAA,MAAAS,EAAAA,SAAA,CAAAP,SAAA,EACIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,uBAC7DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACtE5B,EAAU8D,OAAOE,KAAI,CAACC,EAAeC,KAAkBrC,EAAAA,EAAAA,KAAA,MAAAD,SAAiBqC,GAARC,UAI5ElE,EAAU8D,QAAsC,IAA5B9D,EAAU8D,OAAOC,SAClClC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAA+BC,SAAC,0CAxGjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8BAA6BC,SAAC,6CAmHtD,C,uDCpRJ,MAAMuC,EAAOC,IAAA,IAAC,KAAEd,GAAwBc,EAAA,OAAKvC,EAAAA,EAAAA,KAAA,KAAGF,UAAW,oBAAoB2B,aAAkB,EAyDjG,EAvD0Ce,IAQnC,IARoC,MACzChC,EAAK,MACLpB,EAAK,KACLsB,EAAI,OACJ+B,EAAM,UACN3C,EAAY,GAAE,QACd4C,EAAO,QACPC,GACDH,EAKC,OACE3C,EAAAA,EAAAA,MAAA,OAAKC,UAAW,2CAAoBA,IAAYC,SAAA,EAC9CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,aAAYC,SAAES,IAC3BE,IAECV,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iCAAgCC,UAC9CC,EAAAA,EAAAA,KAACsC,EAAI,CAACb,KAAMf,UAIlBV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,SAAEX,IACjCsD,IACC1C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8CAA6CC,SAAE2C,IAE/DD,IAEC5C,EAAAA,EAAAA,MAAA,OAAKC,UAAW,+CAA8C2C,EAAOG,WAAa,oBAAsB,eAAgB7C,SAAA,EACtHC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SACnB0C,EAAOG,YACN5C,EAAAA,EAAAA,KAAA,OAAK6C,MAAM,6BAA6B/C,UAAU,UAAUgD,QAAQ,YAAYC,KAAK,eAAchD,UAACC,EAAAA,EAAAA,KAAA,QAAMgD,SAAS,UAAUC,EAAE,sHAAsHC,SAAS,eAC9PlD,EAAAA,EAAAA,KAAA,OAAK6C,MAAM,6BAA6B/C,UAAU,UAAUgD,QAAQ,YAAYC,KAAK,eAAchD,UAACC,EAAAA,EAAAA,KAAA,QAAMgD,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,gBAGhQT,EAAOrD,SAGXuD,GAAWA,EAAQT,OAAS,IAE3BlC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtD4C,EAAQR,KAAI,CAACgB,EAAQd,KACpBxC,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,oCAAmCC,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iCAAgCC,SAAEoD,EAAO5B,SACzDvB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEoD,EAAO/D,UAF9BiD,SAOZ,C", "sources": ["pages/DeliverabilityDashboard.tsx", "components/StatCard.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  FormEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button'; // Assuming components exist\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport StatCard from '../components/StatCard'; // Use StatCard for overview\n// import TextArea from '../components/TextArea'; // Remove TextArea import\n// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport { deliverabilityService } from '../services';\n\n// Define interfaces if needed\ninterface DeliverabilityMetrics {\n  deliveryRate?: number;\n  openRate?: number;\n  bounceRate?: number;\n  clickRate?: number;\n  senderScore?: number;\n  complaintRate?: number;\n  // Add other metrics if they exist\n}\n\ninterface SpamScore {\n  score: number;\n  issues: string[];\n}\n\nconst DeliverabilityDashboard: React.FC = () => {\n  const [metrics, setMetrics] = useState<DeliverabilityMetrics | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [spamCheckContent, setSpamCheckContent] = useState({ subject: '', content: '' });\n  const [spamScore, setSpamScore] = useState<SpamScore | null>(null);\n  const [spamCheckLoading, setSpamCheckLoading] = useState<boolean>(false);\n  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'spam-checker'>('overview');\n  \n  // Fetch deliverability metrics on component mount\n  useEffect(() => {\n    const fetchMetrics = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const response = await deliverabilityService.getMetrics();\n        if (response.success) {\n          setMetrics(response.data);\n        } else {\n          setError(response.message || 'Failed to fetch deliverability metrics');\n        }\n      } catch (err: any) {\n        setError(err.message || 'An error occurred while fetching metrics');\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    fetchMetrics();\n  }, []);\n  \n  // Handle spam score check\n  const handleSpamCheck = async (e: FormEvent) => {\n    e.preventDefault();\n    \n    if (!spamCheckContent.subject.trim() || !spamCheckContent.content.trim()) {\n      setError('Subject and content are required for spam check');\n      return;\n    }\n    \n    setSpamCheckLoading(true);\n    setError(null);\n    setSpamScore(null); // Clear previous score\n    \n    try {\n      const response = await deliverabilityService.checkSpamScore(\n        spamCheckContent.subject,\n        spamCheckContent.content\n      );\n      \n      if (response.success) {\n        setSpamScore(response.data);\n      } else {\n        setError(response.message || 'Failed to check spam score');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while checking spam score');\n    } finally {\n      setSpamCheckLoading(false);\n    }\n  };\n  \n  // Get status color based on value\n  const getStatusColor = (value: number | undefined, thresholds: { good: number, warning: number }): string => {\n    if (value === undefined) return 'text-gray-500'; // Handle undefined case\n    if (value >= thresholds.good) {\n      return 'text-green-500';\n    } else if (value >= thresholds.warning) {\n      return 'text-yellow-500';\n    } else {\n      return 'text-red-500';\n    }\n  };\n  \n  // Get spam score status\n  const getSpamScoreStatus = (score: number | undefined): { text: string, color: string } => {\n    if (score === undefined) return { text: 'N/A', color: 'text-gray-500' };\n    if (score < 2) {\n      return { text: 'Low Risk', color: 'text-green-500' };\n    } else if (score < 5) {\n      return { text: 'Medium Risk', color: 'text-yellow-500' };\n    } else {\n      return { text: 'High Risk', color: 'text-red-500' };\n    }\n  };\n\n  // Remove Sidebar wrapper\n  return (\n    // <Sidebar> // Removed\n      <div className=\"container mx-auto px-4 py-6\"> \n        <div className=\"flex justify-between items-center mb-6\">\n           <h1 className=\"text-2xl font-semibold text-text-primary\">Email Deliverability</h1>\n        </div>\n         <p className=\"text-text-secondary mb-6\">\n            Monitor and improve your email deliverability metrics.\n         </p>\n        \n        {error && (\n           <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n        )}\n        \n        <Card className=\"overflow-hidden\"> \n           {/* Tab Navigation */}\n           <div className=\"border-b border-gray-700\"> \n            <nav className=\"-mb-px flex space-x-4 px-6\"> \n              <button\n                 className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${\n                   activeTab === 'overview'\n                     ? 'border-primary text-primary'\n                     : 'border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500'\n                 }`}\n                 onClick={() => setActiveTab('overview')}\n              >\n                 Overview\n              </button>\n              <button\n                 className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${\n                   activeTab === 'details'\n                     ? 'border-primary text-primary'\n                     : 'border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500'\n                 }`}\n                 onClick={() => setActiveTab('details')}\n              >\n                 Detailed Metrics\n              </button>\n              <button\n                 className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${\n                   activeTab === 'spam-checker'\n                     ? 'border-primary text-primary'\n                     : 'border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500'\n                 }`}\n                 onClick={() => setActiveTab('spam-checker')}\n               >\n                Spam Checker\n              </button>\n            </nav>\n           </div>\n          \n           {/* Tab Content */} \n           <div className=\"p-6\">\n            {loading ? (\n               <div className=\"flex justify-center py-8\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n               </div>\n            ) : !metrics && activeTab !== 'spam-checker' ? (\n               <p className=\"text-sm text-text-secondary\">No deliverability data available.</p>\n            ) : (\n               <>\n                 {/* Overview Tab */} \n                 {activeTab === 'overview' && metrics && (\n                  <div>\n                    <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Deliverability Overview</h2>\n                    <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n                       <StatCard title=\"Delivery Rate\" value={`${metrics.deliveryRate || 0}%`} icon=\"inbox-in\" />\n                       <StatCard title=\"Open Rate\" value={`${metrics.openRate || 0}%`} icon=\"eye\" />\n                       <StatCard title=\"Bounce Rate\" value={`${metrics.bounceRate || 0}%`} icon=\"exclamation-circle\" />\n                       <StatCard title=\"Click Rate\" value={`${metrics.clickRate || 0}%`} icon=\"cursor-click\" />\n                    </div>\n                    {/* Add more overview charts/summaries later */}\n                   </div>\n                 )}\n                 \n                 {/* Details Tab */} \n                 {activeTab === 'details' && metrics && (\n                    <div>\n                      <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Detailed Metrics</h2>\n                       <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                           {/* Remove variant=\"outlined\" and add border styles */}\n                           <Card className=\"p-4 border border-gray-700 rounded\"> \n                              <h3 className=\"text-lg font-medium text-text-primary mb-2\">Sender Reputation</h3>\n                               <p className={`text-2xl font-bold mb-2 ${getStatusColor(metrics.senderScore, { good: 90, warning: 70 })}`}>\n                                   {metrics.senderScore !== undefined ? `${metrics.senderScore} / 100` : 'N/A'} \n                               </p>\n                               <p className=\"text-sm text-text-secondary\">Score based on engagement, bounce rates, and spam complaints.</p>\n                            </Card>\n                            {/* Remove variant=\"outlined\" and add border styles */}\n                            <Card className=\"p-4 border border-gray-700 rounded\"> \n                               <h3 className=\"text-lg font-medium text-text-primary mb-2\">Complaint Rate</h3>\n                               <p className={`text-2xl font-bold mb-2 ${getStatusColor(metrics.complaintRate !== undefined ? 100 - metrics.complaintRate * 100 : undefined, { good: 99.9, warning: 99.5 })}`}>\n                                   {metrics.complaintRate !== undefined ? `${metrics.complaintRate.toFixed(3)}%` : 'N/A'}\n                               </p>\n                               <p className=\"text-sm text-text-secondary\">Percentage of recipients marking emails as spam.</p>\n                             </Card>\n                             {/* Add more detailed metrics cards (e.g., by domain, ISP) */}\n                         </div>\n                     </div>\n                 )}\n                 \n                 {/* Spam Checker Tab */} \n                 {activeTab === 'spam-checker' && (\n                     <div>\n                       <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Email Spam Checker</h2>\n                       <form onSubmit={handleSpamCheck} className=\"space-y-4 mb-6\">\n                          <Input\n                             label=\"Subject Line\"\n                             id=\"spamSubject\"\n                             name=\"spamSubject\"\n                             value={spamCheckContent.subject}\n                             onChange={(e: ChangeEvent<HTMLInputElement>) => setSpamCheckContent({...spamCheckContent, subject: e.target.value})}\n                             required\n                           />\n                           {/* Replace custom TextArea with standard textarea */}\n                           <div>\n                              <label htmlFor=\"spamContent\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                                  Email Content (HTML or Plain Text)\n                              </label>\n                              <textarea\n                                 id=\"spamContent\"\n                                 name=\"spamContent\"\n                                 rows={10}\n                                 value={spamCheckContent.content}\n                                 onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setSpamCheckContent({...spamCheckContent, content: e.target.value})}\n                                 required\n                                 className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500\"\n                              />\n                           </div>\n                           <div className=\"flex justify-end\">\n                              <Button type=\"submit\" disabled={spamCheckLoading}>\n                                 {spamCheckLoading ? 'Checking...' : 'Check Spam Score'}\n                              </Button>\n                           </div>\n                        </form>\n\n                        {spamCheckLoading && (\n                            <div className=\"flex justify-center py-4\">\n                                <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary\"></div>\n                                <span className=\"ml-3 text-text-secondary\">Checking spam score...</span>\n                            </div>\n                        )}\n\n                        {spamScore !== null && (\n                           /* Remove variant=\"outlined\" and add border styles */\n                           <Card className=\"p-4 border border-gray-700 rounded\">\n                               <h3 className=\"text-lg font-medium text-text-primary mb-2\">Spam Check Results</h3>\n                               <p className=\"text-sm text-text-secondary mb-1\">Overall Score:\n                                  <span className={`font-bold text-xl ml-2 ${getSpamScoreStatus(spamScore.score).color}`}>{spamScore.score !== undefined ? spamScore.score.toFixed(1) : 'N/A'}</span>\n                               </p>\n                               <p className=\"text-sm text-text-secondary mb-3\">Status:\n                                   <span className={`font-semibold ml-2 ${getSpamScoreStatus(spamScore.score).color}`}>{getSpamScoreStatus(spamScore.score).text}</span>\n                               </p>\n                                {spamScore.issues && spamScore.issues.length > 0 && (\n                                   <>\n                                       <h4 className=\"text-md font-medium text-text-secondary mb-1\">Potential Issues:</h4>\n                                       <ul className=\"list-disc list-inside text-sm text-text-secondary space-y-1\">\n                                           {spamScore.issues.map((issue: string, index: number) => <li key={index}>{issue}</li>)}\n                                       </ul>\n                                   </>\n                               )}\n                               {spamScore.issues && spamScore.issues.length === 0 && (\n                                   <p className=\"text-sm text-green-500 italic\">No major spam issues found.</p>\n                               )}\n                           </Card>\n                        )}\n                     </div>\n                 )}\n               </>\n             )}\n           </div> \n        </Card>\n      </div>\n    // </Sidebar> // Removed\n  );\n};\n\nexport default DeliverabilityDashboard;\n", "import React from 'react';\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  icon?: string;\n  change?: {\n    value: string | number;\n    isPositive: boolean;\n  };\n  className?: string;\n  tooltip?: string;\n  details?: { label: string; value: string | number }[];\n}\n\n// Helper for icon rendering (replace with your actual icon logic)\nconst Icon = ({ name }: { name: string }) => <i className={`placeholder-icon-${name} w-5 h-5`} />; \n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  icon,\n  change,\n  className = '',\n  tooltip,\n  details\n}) => {\n  // Use the base .card or apply .glassmorphic for futuristic feel\n  // Use container-futuristic if defined, otherwise default card styling from index.css\n  const baseCardClass = \"card container-futuristic flex flex-col\"; \n\n  return (\n    <div className={`${baseCardClass} ${className}`}>\n      <div className=\"flex justify-between items-start mb-2\">\n        <h3 className=\"stat-label\">{title}</h3>\n        {icon && (\n          // Use text-secondary for default icon color\n          <span className=\"text-text-secondary opacity-80\">\n            <Icon name={icon} />\n          </span>\n        )}\n      </div>\n      <div className=\"stat-value mb-1\">{value}</div>\n      {tooltip && (\n        <div className=\"text-xs text-text-secondary mt-1 opacity-90\">{tooltip}</div>\n      )}\n      {change && (\n        // Apply theme colors: growth-green for positive, danger for negative\n        <div className={`text-sm mt-2 flex items-center font-medium ${change.isPositive ? 'text-growth-green' : 'text-danger'}`}>\n          <span className=\"mr-1\">\n            {change.isPositive ? \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\" clipRule=\"evenodd\" /></svg> : \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" /></svg> \n            }\n          </span>\n          {change.value}\n        </div>\n      )}\n      {details && details.length > 0 && (\n        // Use the theme border color\n        <div className=\"mt-auto pt-3 border-t border-border mt-3\">\n          {details.map((detail, index) => (\n            <div key={index} className=\"flex justify-between text-xs py-1\">\n              <span className=\"text-text-secondary opacity-90\">{detail.label}</span>\n              <span className=\"font-medium\">{detail.value}</span>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StatCard;\n"], "names": ["DeliverabilityDashboard", "metrics", "setMetrics", "useState", "loading", "setLoading", "error", "setError", "spamCheckContent", "setSpamCheckContent", "subject", "content", "spamScore", "setSpamScore", "spamCheckLoading", "setSpamCheckLoading", "activeTab", "setActiveTab", "useEffect", "async", "response", "deliverabilityService", "getMetrics", "success", "data", "message", "err", "fetchMetrics", "getStatusColor", "value", "thresholds", "undefined", "good", "warning", "getSpamScoreStatus", "score", "text", "color", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON>", "type", "onClose", "Card", "onClick", "_Fragment", "StatCard", "title", "deliveryRate", "icon", "openRate", "bounceRate", "clickRate", "senderScore", "complaintRate", "toFixed", "onSubmit", "e", "preventDefault", "trim", "checkSpamScore", "Input", "label", "id", "name", "onChange", "target", "required", "htmlFor", "rows", "<PERSON><PERSON>", "disabled", "issues", "length", "map", "issue", "index", "Icon", "_ref", "_ref2", "change", "tooltip", "details", "isPositive", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "detail"], "sourceRoot": ""}