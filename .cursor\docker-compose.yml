# docker-compose.yml (Full Stack Base + Mosaico)
version: '3.8'

services:
  # --- Redis Service ---
  redis:
    image: redis:alpine
    container_name: driftly-redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - driftly-network
    restart: unless-stopped

  # --- AI Service ---
  ai-service:
    container_name: driftly-ai
    build:
      context: ./AI-Powered Email Template Generator for Driftly
      dockerfile: Dockerfile
    ports:
      - '5000:5000'
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379 # Connect to redis service
      # Add any other AI-specific ENV vars here if needed
    # volumes: # Consider if volume mount is needed for AI dev
    #   - ./AI-Powered Email Template Generator for Driftly:/app
    networks:
      - driftly-network
    restart: unless-stopped

  # --- Backend Service (Node.js) ---
  backend:
    container_name: driftly-backend
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - '3000:3000' # Assumes backend runs on 3000 internally
    depends_on:
      - redis
      # - ai-service # Uncomment if backend directly calls AI service
    env_file:
      - ./backend/.env # Load backend .env file
    environment:
      # Override connection details for Docker network
      - REDIS_HOST=redis
      - REDIS_URL= # Clear this if REDIS_HOST/PORT are used
      - MONGODB_URI=${MONGODB_URI} # Ensure MONGODB_URI is available to compose
      - AI_SERVICE_URL=http://ai-service:5000
      - NODE_ENV=development # Or production
      # Ensure other necessary vars like JWT_SECRET, AWS keys are present
    volumes:
      - ./backend:/usr/src/app
      - /usr/src/app/node_modules
    networks:
      - driftly-network
    restart: unless-stopped

  # --- Frontend Service (React) ---
  frontend:
    container_name: driftly-frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      # Map host 3001 to the correct internal container port (e.g., 3000, 5173, etc.)
      - '3001:3000' # ADJUST INTERNAL PORT IF NEEDED
    environment:
      # These need to be handled by your frontend build/entrypoint
      - REACT_APP_API_URL=http://localhost:3000/api/v1
      - REACT_APP_SOCKET_URL=http://localhost:3000
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - driftly-network
    restart: unless-stopped

  # --- Mosaico Service ---
  mosaico:
    container_name: driftly-mosaico
    # IMPORTANT: Replace with your chosen/built Mosaico image if different
    image: joga15/mosaico:latest
    ports:
      - '8080:80' # Map host 8080 to container's web server port 80
    volumes:
      # Mount a local directory to store Mosaico config files
      # You will need to create './mosaico_config' and place Mosaico's config.ini here
      # This example assumes the image expects config in /config
      - ./mosaico_config:/config
      # You might need other volumes depending on the image (e.g., for templates, uploads)
    networks:
      - driftly-network
    restart: unless-stopped
    # depends_on: # Mosaico doesn't directly depend on backend start, but needs backend endpoint for uploads
    #   - backend

networks:
  driftly-network:
    driver: bridge

volumes:
  redis_data: # Persists Redis data

# IMPORTANT:
# 1. Verify all paths (`context`, `env_file`, `volumes`) are correct relative to this docker-compose.yml file.
# 2. Verify the internal port mapping for the `frontend` service.
# 3. Create the `./mosaico_config` directory locally.
# 4. Obtain the Mosaico `config.ini` (or equivalent) file, place it in `./mosaico_config`,
#    and configure it, especially the image upload URL to point to your backend service
#    (e.g., http://backend:3000/api/v1/images/mosaico-upload).
# 5. Choose the correct Mosaico Docker image for the `mosaico` service.
# 6. Ensure necessary environment variables (like MONGODB_URI, secrets) are available when running `docker compose up`. 