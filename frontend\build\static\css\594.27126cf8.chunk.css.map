{"version": 3, "file": "static/css/594.27126cf8.chunk.css", "mappings": "AAOE,+BAAwC,CAAxC,wBAAwC,CAAxC,qEAAwC,CAAxC,qBAAwC,CAAxC,YAAwC,CAKxC,qCAA8F,CAA9F,iBAA8F,CAA9F,iCAA8F,CAA9F,sDAA8F,CAA9F,qBAA8F,CAA9F,wDAA8F,CAA9F,uBAA8F,CAA9F,oBAA8F,CAA9F,wDAA8F,CAA9F,+CAA8F,CAA9F,gIAA8F,CAA9F,kBAA8F,CAgB9F,kFAJA,+BAImD,CAAnD,sCAAmD,CAAnD,wBAAmD,CAAnD,wDAAmD,CAAnD,mBAAmD,CAAnD,cAAmD,CAInD,iCAAmF,CAAnF,qBAAmF,CAAnF,YAAmF,CAAnF,iBAAmF,CAAnF,eAAmF,CAAnF,mBAAmF,CAAnF,qBAAmF,CAAnF,kHAAmF,CAAnF,kDAAmF,CAInF,uCAAyC,CAAzC,mBAAyC,CAAzC,iCAAyC,CAAzC,sDAAyC,CAAzC,qBAAyC,CAAzC,wDAAyC,CAAzC,+CAAyC,CAAzC,gHAAyC,CAAzC,6CAAyC,CAIzC,+CAAwC,CAAxC,aAAwC,CAAxC,+CAAwC,CAAxC,qDAAwC,CAAxC,aAAwC,CAAxC,4CAAwC,CAIxC,wCAAW,CAIX,8BAAwL,CAAxL,mBAAwL,CAAxL,wBAAwL,CAAxL,sDAAwL,CAAxL,qBAAwL,CAAxL,UAAwL,CAAxL,+CAAwL,CAAxL,iBAAwL,CAAxL,eAAwL,CAAxL,mBAAwL,CAAxL,kBAAwL,CAAxL,kHAAwL,CAAxL,kDAAwL,CAAxL,6HAAwL,CAAxL,wGAAwL,CAAxL,mBAAwL,CAAxL,wDAAwL,CAAxL,kGAAwL,CAAxL,wFAAwL,CAAxL,uBAAwL,CAAxL,kBAAwL,CAAxL,oCAAwL,CAAxL,wBAAwL,CAAxL,sDAAwL,CAIxL,uCAAuC,CAAvC,wBAAuC,CAAvC,2EAAuC,CAKvC,4BAAkC,CAAlC,QAAkC,CAAlC,eAAkC,CAKlC,0CAA2E,CAA3E,iBAA2E,CAA3E,qBAA2E,CAA3E,6EAA2E,CAA3E,wDAA2E,CAA3E,sBAA2E,CAA3E,YAA2E,CAA3E,qBAA2E,CAA3E,eAA2E,CAA3E,WAA2E,CAI3E,6CAA6D,CAA7D,uBAA6D,CAA7D,oBAA6D,CAA7D,wDAA6D,CAA7D,kBAA6D,CAA7D,mCAA6D,CAI7D,2BAA2C,CAA3C,qBAA2C,CAA3C,WAA2C,CAA3C,eAA2C,CAI3C,uCAAmC,CAAnC,uBAAmC,CAAnC,oBAAmC,CAAnC,wDAAmC,CAAnC,cAAmC,CAInC,mCAA+I,CAA/I,oBAA+I,CAA/I,wDAA+I,CAA/I,qBAA+I,CAA/I,gBAA+I,CAA/I,iBAA+I,CAA/I,wCAA+I,CAA/I,UAA+I,CAA/I,yCAA+I,CAA/I,0GAA+I,CAA/I,wGAA+I,CAA/I,mBAA+I,CAA/I,6EAA+I,CAA/I,uDAA+I,CAA/I,uEAA+I,CAA/I,wFAA+I,CAA/I,uBAA+I,CAA/I,kBAA+I,CAI/I,oCAAwD,CAAxD,uBAAwD,CAAxD,oBAAwD,CAAxD,wDAAwD,CAAxD,YAAwD,CAAxD,eAAwD,CAAxD,aAAwD,CAIxD,mCAAwF,CAAxF,iBAAwF,CAAxF,eAAwF,CAAxF,mBAAwF,CAAxF,kBAAwF,CAAxF,qBAAwF,CAAxF,kHAAwF,CAAxF,kDAAwF,CAAxF,kBAAwF,CAIxF,sCAAoC,CAApC,mBAAoC,CAApC,wBAAoC,CAApC,wDAAoC,CAApC,aAAoC,CAApC,6CAAoC,CAIpC,8CAA0D,CAA1D,aAA0D,CAA1D,+CAA0D,CAA1D,kDAA0D,CAA1D,mBAA0D,CAA1D,wBAA0D,CAA1D,wDAA0D,CAA1D,aAA0D,CAA1D,4CAA0D,CAI1D,4BAAiD,CAAjD,YAAiD,CAAjD,UAAiD,CAAjD,6CAAiD,CAAjD,eAAiD,CAAjD,cAAiD,CAIjD,oCAAkJ,CAAlJ,iBAAkJ,CAAlJ,qBAAkJ,CAAlJ,wDAAkJ,CAAlJ,oBAAkJ,CAAlJ,wDAAkJ,CAAlJ,qBAAkJ,CAAlJ,gBAAkJ,CAAlJ,WAAkJ,CAAlJ,YAAkJ,CAAlJ,qBAAkJ,CAAlJ,eAAkJ,CAAlJ,gDAAkJ,CAAlJ,kDAAkJ,CAAlJ,0CAAkJ,CAAlJ,iCAAkJ,CAAlJ,sDAAkJ,CAAlJ,oBAAkJ,CAAlJ,wDAAkJ,CAIlJ,6CAJA,+CAAkJ,CAAlJ,kGAIkC,CAAlC,6CAAkC,CAAlC,6DAAkC,CAAlC,+FAAkC,CAAlC,oBAAkC,CAAlC,uDAAkC,CAIlC,gCAAwE,CAAxE,kBAAwE,CAAxE,wBAAwE,CAAxE,qEAAwE,CAAxE,WAAwE,CAAxE,sBAAwE,CAAxE,eAAwE,CAIxE,8BAAiC,CAAjC,2BAAiC,CAIjC,kCAA8G,CAA9G,mBAA8G,CAA9G,kBAA8G,CAA9G,wBAA8G,CAA9G,wDAA8G,CAA9G,oBAA8G,CAA9G,aAA8G,CAA9G,0DAA8G,CAA9G,iBAA8G,CAA9G,eAA8G,CAA9G,WAA8G,CAA9G,sBAA8G,CAA9G,UAA8G,CAI9G,0BAAU,CAIV,+BAA6C,CAA7C,aAA6C,CAA7C,8DAA6C,CAA7C,eAA6C,CAA7C,mBAA6C,CAA7C,oBAA6C,CAI7C,mCAA4B,CAA5B,gBAA4B,CAI5B,8BAJA,mBAA4B,CAA5B,aAA4B,CAA5B,+CAIoC,CAApC,uBAAoC,CAApC,iBAAoC,CAKpC,8BAA2C,CAA3C,QAA2C,CAA3C,qBAA2C,CAA3C,eAA2C,CAI3C,mCAA6C,CAA7C,wBAA6C,CAA7C,wDAA6C,CAA7C,QAA6C,CAA7C,eAA6C,CAA7C,YAA6C,CAI7C,iCAA4E,CAA5E,kBAA4E,CAA5E,aAA4E,CAA5E,4DAA4E,CAA5E,WAA4E,CAA5E,sBAA4E,CAA5E,YAA4E,CAA5E,iBAA4E,CAI5E,sCAAyG,CAAzG,iBAAyG,CAAzG,qBAAyG,CAAzG,wDAAyG,CAAzG,oBAAyG,CAAzG,wDAAyG,CAAzG,qBAAyG,CAAzG,gBAAyG,CAAzG,WAAyG,CAAzG,kBAAyG,CAAzG,gDAAyG,CAAzG,kDAAyG,CAAzG,4CAAyG,CAAzG,oBAAyG,CAAzG,wDAAyG,CAIzG,+CAAkC,CAAlC,6DAAkC,CAAlC,+FAAkC,CAAlC,+CAAkC,CAAlC,kGAAkC,CAIlC,oDAJA,oBAAkC,CAAlC,uDAI+C,CAA/C,+CAA+C,CAA/C,0GAA+C,CAA/C,wGAA+C,CAA/C,mBAA+C,CAA/C,iIAA+C,CAA/C,wFAA+C,CAI/C,mCAAsF,CAAtF,iBAAsF,CAAtF,kBAAsF,CAAtF,wBAAsF,CAAtF,wDAAsF,CAAtF,uBAAsF,CAAtF,oBAAsF,CAAtF,wDAAsF,CAAtF,YAAsF,CAAtF,6BAAsF,CAAtF,kBAAsF,CAItF,+BAAwC,CAAxC,aAAwC,CAAxC,8DAAwC,CAAxC,eAAwC,CAAxC,mBAAwC,CAIxC,8CAAwB,CAIxB,iCAA6D,CAA7D,aAA6D,CAA7D,+CAA6D,CAA7D,cAA6D,CAA7D,kHAA6D,CAA7D,kDAA6D,CAA7D,uCAA6D,CAA7D,aAA6D,CAA7D,6CAA6D,CAI/D,eAEE,qBAAuG,CAAvG,iBAAuG,CAAvG,qBAAuG,CAAvG,wDAAuG,CAAvG,oBAAuG,CAAvG,wDAAuG,CAAvG,qBAAuG,CAAvG,YAAuG,CAAvG,qBAAuG,CAAvG,eAAuG,CAAvG,UACF,CADE,mDAAuG,EAAvG,oDAAuG,EAIvG,uCAA6D,CAA7D,uBAA6D,CAA7D,oBAA6D,CAA7D,wDAA6D,CAA7D,kBAA6D,CAA7D,mCAA6D,CAI7D,uBAAiC,CAAjC,eAAiC,CAAjC,YAAiC,CAIjC,qCAA8C,CAA9C,YAA8C,CAA9C,WAA8C,CAA9C,sBAA8C,CAGhD,0BAEE,gBAAuB,CAAvB,iBAAuB,CAAvB,eACF,CAGE,iCAAe,CAAf,qBAAe,CAAf,wDAAe,CAIf,mCAA4E,CAA5E,kBAA4E,CAA5E,aAA4E,CAA5E,4DAA4E,CAA5E,WAA4E,CAA5E,sBAA4E,CAA5E,YAA4E,CAA5E,iBAA4E,CAI9E,oBAEE,qBAAqF,CAArF,iBAAqF,CAArF,qBAAqF,CAArF,wDAAqF,CAArF,oBAAqF,CAArF,wDAAqF,CAArF,qBAAqF,CAArF,YAAqF,CAArF,qBAAqF,CAArF,eAAqF,CAArF,UACF,CADE,wDAAqF,EAIrF,4CAA6D,CAA7D,uBAA6D,CAA7D,oBAA6D,CAA7D,wDAA6D,CAA7D,kBAA6D,CAA7D,mCAA6D,CAI7D,6BAA0B,CAA1B,YAA0B,CAI1B,oCAA+C,CAA/C,aAA+C,CAA/C,2DAA+C,CAA/C,eAA+C,CAI/C,6BAJA,kBAIW,CAIX,qCAAmD,CAAnD,aAAmD,CAAnD,4DAAmD,CAAnD,oBAAmD,CAInD,gCAJA,aAAmD,CAAnD,iBAAmD,CAAnD,mBAIkJ,CAAlJ,mCAAkJ,CAAlJ,iCAAkJ,CAAlJ,sDAAkJ,CAAlJ,oBAAkJ,CAAlJ,wDAAkJ,CAAlJ,qBAAkJ,CAAlJ,gBAAkJ,CAAlJ,+CAAkJ,CAAlJ,uHAAkJ,CAAlJ,UAAkJ,CAAlJ,yCAAkJ,CAAlJ,mBAAkJ,CAAlJ,6EAAkJ,CAAlJ,uDAAkJ,CAAlJ,uBAAkJ,CAAlJ,kBAAkJ,CAIlJ,wCAAe,CAIf,wCAAoH,CAApH,mBAAoH,CAApH,aAAoH,CAApH,+CAAoH,CAApH,cAAoH,CAApH,iBAAoH,CAApH,WAAoH,CAApH,OAAoH,CAApH,6LAAoH,CAApH,kHAAoH,CAApH,kDAAoH,CAApH,4CAAoH,CAApH,aAAoH,CAApH,8CAAoH,CAKpH,0CAA6D,CAA7D,oBAA6D,CAA7D,8EAA6D,CAA7D,gBAA6D,CAA7D,eAA6D,CAA7D,eAA6D,CAI7D,wCAAwF,CAAxF,iBAAwF,CAAxF,kBAAwF,CAAxF,wBAAwF,CAAxF,wDAAwF,CAAxF,uBAAwF,CAAxF,oBAAwF,CAAxF,wDAAwF,CAAxF,YAAwF,CAAxF,6BAAwF,CAAxF,kBAAwF,CAIxF,yCAA8C,CAA9C,aAA8C,CAA9C,+DAA8C,CAA9C,eAA8C,CAA9C,mBAA8C,CAA9C,QAA8C,CAI9C,iCAA0D,CAA1D,aAA0D,CAA1D,+CAA0D,CAA1D,kHAA0D,CAA1D,kDAA0D,CAA1D,uCAA0D,CAA1D,aAA0D,CAA1D,4CAA0D,CAI1D,qCAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,cAAmB,CAInB,wCAAmE,CAAnE,kBAAmE,CAAnE,aAAmE,CAAnE,4DAAmE,CAAnE,qBAAmE,CAAnE,sBAAmE,CAAnE,mBAAmE,CAAnE,gBAAmE,CAInE,sBAA0F,CAA1F,mBAA0F,CAA1F,YAA0F,CAA1F,0CAA0F,EAA1F,8BAA0F,CAA1F,iCAA0F,CAA1F,oBAA0F,CAA1F,wDAA0F,CAA1F,oBAA0F,CAA1F,wBAA0F,CAA1F,2EAA0F,CAI1F,mCAAqC,CAArC,aAAqC,CAArC,qEAAqC,CAArC,kBAAqC,CAArC,iBAAqC,CAIrC,uEAA+B,CAA/B,qBAA+B,CAA/B,wDAA+B,CAA/B,oBAA+B,CAA/B,oHAA+B,CAI/B,mCAA6C,CAA7C,YAA6C,CAA7C,6BAA6C,CAA7C,sCAA6C,CAI7C,oCAAiC,CAAjC,aAAiC,CAAjC,8DAAiC,CAAjC,mBAAiC,CAAjC,mBAAiC,CAIjC,+BAAgH,CAAhH,mBAAgH,CAAhH,wBAAgH,CAAhH,wDAAgH,CAAhH,oBAAgH,CAAhH,aAAgH,CAAhH,6CAAgH,CAAhH,gBAAgH,CAAhH,eAAgH,CAAhH,gBAAgH,CAAhH,oBAAgH,CAAhH,kHAAgH,CAAhH,kDAAgH,CAAhH,qCAAgH,CAAhH,wBAAgH,CAAhH,wDAAgH,CAKhH,qDAAiF,CAAjF,qBAAiF,CAAjF,kDAAiF,CAAjF,YAAiF,CAAjF,OAAiF,CAAjF,sBAAiF,CAAjF,cAAiF,CAAjF,UAAiF,CAIjF,kCAAyD,CAAzD,iEAAyD,CAAzD,mGAAyD,CAAzD,qBAAyD,CAAzD,wDAAyD,CAAzD,mBAAyD,CAAzD,+CAAyD,CAAzD,mHAAyD,CAAzD,iBAAyD,CAAzD,eAAyD,CAAzD,UAAyD,CAIzD,mCAA2E,CAA3E,kBAA2E,CAA3E,uBAA2E,CAA3E,oBAA2E,CAA3E,wDAA2E,CAA3E,YAA2E,CAA3E,6BAA2E,CAA3E,mBAA2E,CAI3E,oCAAwC,CAAxC,aAAwC,CAAxC,+DAAwC,CAAxC,eAAwC,CAIxC,+BAAgB,CAIhB,gCAAyD,CAAzD,mBAAyD,CAAzD,wBAAyD,CAAzD,wDAAyD,CAAzD,qBAAyD,CAAzD,aAAyD,CAAzD,+DAAyD,CAAzD,mBAAyD,CAAzD,kBAAyD,CAAzD,cAAyD,CAIzD,oCAA4E,CAA5E,mBAA4E,CAA5E,oBAA4E,CAA5E,wDAA4E,CAA5E,oBAA4E,CAA5E,aAA4E,CAA5E,yDAA4E,CAA5E,UAA4E,CAA5E,wCAA4E,CAA5E,wDAA4E,CAI5E,oDAAwB,CAIxB,8CAAuC,CAAvC,aAAuC,CAAvC,0DAAuC,CAAvC,iBAAuC,CAAvC,mBAAuC,CAAvC,iBAAuC,CAIvC,0BAA4F,CAA5F,wBAA4F,CAA5F,kEAA4F,CAA5F,4GAA4F,CAA5F,sCAA4F,CAA5F,wDAA4F,CAA5F,+BAA4F,CAA5F,gCAA4F,CAA5F,oBAA4F,CAA5F,wDAA4F,CAA5F,oBAA4F,CAA5F,mBAA4F,CAI5F,6BAJA,qBAA4F,CAA5F,iBAI0M,CAA1M,kCAA0M,CAA1M,qBAA0M,CAA1M,wDAA0M,CAA1M,oBAA0M,CAA1M,wDAA0M,CAA1M,qBAA0M,CAA1M,gBAA0M,CAA1M,aAA0M,CAA1M,4CAA0M,CAA1M,iBAA0M,CAA1M,eAA0M,CAA1M,mBAA0M,CAA1M,kBAA0M,CAA1M,kHAA0M,CAA1M,kDAA0M,CAA1M,+HAA0M,CAA1M,wGAA0M,CAA1M,mBAA0M,CAA1M,wDAA0M,CAA1M,kGAA0M,CAA1M,wFAA0M,CAA1M,uBAA0M,CAA1M,kBAA0M,CAA1M,sCAA0M,CAA1M,wBAA0M,CAA1M,wDAA0M,CAI1M,yCAAmD,CAAnD,mBAAmD,CAAnD,wBAAmD,CAAnD,wDAAmD,CAAnD,aAAmD,CAAnD,kEAAmD,CAIrD,yBAKI,gDAAa,CAGb,uCAAiC,CAAjC,oBAAiC,CAAjC,UAAiC,CAGjC,qCAAe,CAEnB", "sources": ["styles/editor.css"], "sourcesContent": ["/* \n * Styles for the Driftly Email Generator Editor\n * Using Tailwind CSS utility classes\n */\n\n/* Base editor layout */\n.email-editor {\n  @apply flex flex-col h-screen bg-gray-50;\n}\n\n/* Toolbar */\n.editor-toolbar {\n  @apply flex items-center justify-between px-4 py-2 bg-white border-b border-gray-200 shadow-sm;\n}\n\n.toolbar-left {\n  @apply flex items-center;\n}\n\n.toolbar-center {\n  @apply flex items-center;\n}\n\n.toolbar-right {\n  @apply flex items-center;\n}\n\n.preview-mode-toggle {\n  @apply flex items-center bg-gray-100 rounded-lg p-1;\n}\n\n.toggle-button {\n  @apply flex items-center px-3 py-1 rounded-md text-sm font-medium transition-colors;\n}\n\n.toggle-button.active {\n  @apply bg-white text-indigo-600 shadow-sm;\n}\n\n.toggle-button:not(.active) {\n  @apply text-gray-500 hover:text-gray-700;\n}\n\n.toggle-button .icon {\n  @apply mr-1;\n}\n\n.save-button {\n  @apply px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors;\n}\n\n.save-button:disabled {\n  @apply bg-indigo-400 cursor-not-allowed;\n}\n\n/* Editor content layout */\n.editor-content {\n  @apply flex flex-1 overflow-hidden;\n}\n\n/* Block library panel */\n.block-library-panel {\n  @apply w-64 bg-white border-r border-gray-200 flex flex-col overflow-hidden;\n}\n\n.block-library-panel h3 {\n  @apply px-4 py-3 text-lg font-medium border-b border-gray-200;\n}\n\n.block-library {\n  @apply flex flex-col h-full overflow-hidden;\n}\n\n.search-container {\n  @apply p-3 border-b border-gray-200;\n}\n\n.search-input {\n  @apply w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500;\n}\n\n.category-tabs {\n  @apply flex overflow-x-auto p-2 border-b border-gray-200;\n}\n\n.category-tab {\n  @apply px-3 py-1 text-sm font-medium rounded-md whitespace-nowrap mr-2 transition-colors;\n}\n\n.category-tab.active {\n  @apply bg-indigo-100 text-indigo-700;\n}\n\n.category-tab:not(.active) {\n  @apply text-gray-500 hover:text-gray-700 hover:bg-gray-100;\n}\n\n.blocks-grid {\n  @apply grid grid-cols-1 gap-3 p-3 overflow-y-auto;\n}\n\n.library-block {\n  @apply flex flex-col bg-white border border-gray-200 rounded-md overflow-hidden cursor-grab hover:border-indigo-300 hover:shadow-sm transition-all;\n}\n\n.library-block.dragging {\n  @apply border-indigo-500 shadow-md;\n}\n\n.block-preview {\n  @apply h-24 bg-gray-100 flex items-center justify-center overflow-hidden;\n}\n\n.block-preview img {\n  @apply w-full h-full object-cover;\n}\n\n.block-type-icon {\n  @apply w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center text-xl font-bold;\n}\n\n.block-info {\n  @apply p-3;\n}\n\n.block-name {\n  @apply text-sm font-medium text-gray-900 mb-1;\n}\n\n.block-description {\n  @apply text-xs text-gray-500;\n}\n\n.no-blocks {\n  @apply p-4 text-center text-gray-500;\n}\n\n/* Editor workspace */\n.editor-workspace {\n  @apply flex-1 flex flex-col overflow-hidden;\n}\n\n.blocks-container {\n  @apply flex-1 p-4 overflow-y-auto bg-gray-100;\n}\n\n.empty-blocks {\n  @apply flex items-center justify-center h-full text-gray-500 text-center p-8;\n}\n\n.draggable-block {\n  @apply bg-white border border-gray-200 rounded-md mb-4 cursor-grab hover:border-indigo-300 transition-all;\n}\n\n.draggable-block.dragging {\n  @apply border-indigo-500 shadow-md;\n}\n\n.draggable-block.selected {\n  @apply border-indigo-500 ring-2 ring-indigo-200;\n}\n\n.block-header {\n  @apply flex items-center justify-between px-4 py-2 border-b border-gray-200 bg-gray-50;\n}\n\n.block-type {\n  @apply text-sm font-medium text-gray-700;\n}\n\n.block-actions {\n  @apply flex items-center;\n}\n\n.remove-block {\n  @apply p-1 text-gray-400 hover:text-red-500 transition-colors;\n}\n\n/* Preview panel */\n.preview-panel {\n  /* Adjusted width for better responsiveness, use container queries if needed */\n  @apply w-full md:w-[400px] lg:w-[600px] bg-white border-l border-gray-200 flex flex-col overflow-hidden;\n}\n\n.preview-panel h3 {\n  @apply px-4 py-3 text-lg font-medium border-b border-gray-200;\n}\n\n.email-preview {\n  @apply flex-1 overflow-hidden p-4;\n}\n\n.preview-container {\n  @apply flex items-center justify-center h-full;\n}\n\n.preview-container.mobile {\n  /* Ensure mobile preview fits well */\n  @apply max-w-sm mx-auto;\n}\n\n.preview-iframe {\n  @apply bg-white;\n}\n\n.empty-template {\n  @apply flex items-center justify-center h-full text-gray-500 text-center p-8;\n}\n\n/* Block editor panel */\n.block-editor-panel {\n    /* Adjusted width for better responsiveness */\n  @apply w-full md:w-80 bg-white border-l border-gray-200 flex flex-col overflow-hidden;\n}\n\n.block-editor-panel h3 {\n  @apply px-4 py-3 text-lg font-medium border-b border-gray-200;\n}\n\n.block-editor {\n  @apply p-4 overflow-y-auto;\n}\n\n.block-editor h4 {\n  @apply text-base font-medium text-gray-900 mb-4;\n}\n\n.form-group {\n  @apply mb-4;\n}\n\n.form-group label {\n  @apply block text-sm font-medium text-gray-700 mb-1;\n}\n\n.form-control {\n  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500;\n}\n\n.input-with-suggestion {\n  @apply relative;\n}\n\n.suggestion-button {\n  @apply absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-indigo-500 transition-colors;\n}\n\n/* AI Suggestion Panel */\n.ai-suggestion-panel {\n  @apply mt-4 border border-gray-200 rounded-md overflow-hidden;\n}\n\n.suggestion-header {\n  @apply flex items-center justify-between px-4 py-2 bg-indigo-50 border-b border-gray-200;\n}\n\n.suggestion-header h4 {\n  @apply text-sm font-medium text-indigo-700 m-0;\n}\n\n.close-button {\n  @apply text-gray-400 hover:text-gray-600 transition-colors;\n}\n\n.suggestion-content {\n  @apply p-3 bg-white;\n}\n\n.loading-suggestions {\n  @apply flex flex-col items-center justify-center py-4 text-gray-500;\n}\n\n.spinner {\n  @apply w-6 h-6 border-2 border-gray-300 border-t-indigo-600 rounded-full animate-spin mb-2;\n}\n\n.no-suggestions {\n  @apply py-3 text-center text-gray-500;\n}\n\n.suggestions-list {\n  @apply divide-y divide-gray-200;\n}\n\n.suggestion-item {\n  @apply py-2 flex items-center justify-between;\n}\n\n.suggestion-text {\n  @apply text-sm text-gray-700 pr-2;\n}\n\n.apply-button {\n  @apply px-2 py-1 bg-indigo-100 text-indigo-700 text-xs font-medium rounded hover:bg-indigo-200 transition-colors;\n}\n\n/* Modal */\n.modal-overlay {\n  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;\n}\n\n.modal-container {\n  @apply bg-white rounded-lg shadow-xl w-full max-w-md mx-4;\n}\n\n.modal-header {\n  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;\n}\n\n.modal-header h3 {\n  @apply text-lg font-medium text-gray-900;\n}\n\n.modal-body {\n  @apply px-6 py-4;\n}\n\n.error-message {\n  @apply mb-4 p-3 bg-red-50 text-red-700 text-sm rounded-md;\n}\n\n.form-checkbox {\n  @apply h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded;\n}\n\n.form-group.checkbox {\n  @apply flex items-center;\n}\n\n.form-group.checkbox label {\n  @apply ml-2 block text-sm text-gray-900;\n}\n\n.modal-footer {\n  @apply px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3 rounded-b-lg;\n}\n\n.cancel-button {\n  @apply px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors;\n}\n\n.cancel-button:disabled {\n  @apply bg-gray-100 text-gray-400 cursor-not-allowed;\n}\n\n/* Responsive adjustments for smaller screens */\n@media (max-width: 768px) {\n  .block-library-panel {\n    @apply hidden; /* Hide library on small screens */\n  }\n  .preview-panel {\n    @apply hidden; /* Hide preview panel on small screens by default */\n  }\n  .block-editor-panel {\n    @apply w-full border-l-0 border-t; /* Take full width, use top border */\n  }\n  .editor-content {\n    @apply flex-col; /* Stack vertically */\n  }\n}\n"], "names": [], "sourceRoot": ""}