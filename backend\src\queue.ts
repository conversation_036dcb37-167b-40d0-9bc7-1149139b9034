import Bull, { QueueOptions } from 'bull';
import dotenv from 'dotenv';

// Import necessary models, services, and utils
import CampaignRecipient from './models/campaign-recipient.model';
import Campaign from './models/campaign.model';
import User from './models/user.model';
// Import the Socket.IO server instance
import { io } from './server'; // Assuming server.ts exports 'io'
import {
  SESService,
  SNSService,
} from './services/aws.service';
import {
  addTrackingPixel,
  addUnsubscribeLink,
  replaceLinksWithTrackableLinks,
} from './utils/email.util';

// Load environment variables
dotenv.config();

// --- Correctly configure Bull Queue Options ---
const queueOptions: QueueOptions = {};

if (process.env.REDIS_URL) {
  // If REDIS_URL is provided, <PERSON> expects it as the second argument (optional third for opts)
  console.log('[Queue] Attempting to connect to Redis using REDIS_URL.');
} else {
  // If using host/port, they must be nested under the 'redis' key in the options object
  queueOptions.redis = {
    host: process.env.REDIS_HOST || '127.0.0.1',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    // Add any other Redis options here if needed (e.g., password, db)
  };
  console.log('[Queue] Attempting to connect to Redis using Host/Port:', queueOptions.redis);
}

// Create email sending queue instance using the correct constructor signature
const emailQueue = process.env.REDIS_URL
  ? new Bull('email-sending', process.env.REDIS_URL, queueOptions) // Signature: name, url, opts?
  : new Bull('email-sending', queueOptions); // Signature: name, opts? (where opts contains redis connection)

console.log('[Queue] Bull queue instance created for email-sending.');

// --- Add Event Listeners for Logging/Debugging ---
emailQueue.on('error', (error) => {
  console.error('[Queue] Bull queue error:', error);
});

emailQueue.on('waiting', (jobId) => {
  // console.log(`[Queue] Job ${jobId} is waiting.`); // Can be verbose
});

emailQueue.on('active', (job) => {
  console.log(`[Queue] Job ${job.id} has started.`);
});

emailQueue.on('completed', (job, result) => {
  console.log(`[Queue] Job ${job.id} completed successfully.`);
  // console.log('[Queue] Result:', result); // Optional: log result if needed
});

emailQueue.on('failed', (job, err) => {
  console.error(`[Queue] Job ${job.id} failed:`, err.message);
  console.error('[Queue] Failed job stack trace:', err.stack);
});

emailQueue.on('stalled', (job) => {
  console.warn(`[Queue] Job ${job.id} has stalled.`);
});

emailQueue.on('removed', (job) => {
  console.log(`[Queue] Job ${job.id} removed.`);
});
// --- End Event Listeners ---

// --- Add helper function for URL correction ---
const correctMosaicoImageUrls = (html: string, appBaseUrl: string): string => {
  if (!html) return '';
  try {
    // Regex to find Mosaico /img/ URLs:
    // src="http://localhost:8080/img/?src=ENCODED_ORIGINAL_URL&..."
    // It captures the ENCODED_ORIGINAL_URL part.
    // Updated regex to handle both & and &amp;
    const mosaicoImgRegex = /src=["'](http:\/\/localhost:8080\/img\/\?src=([^&;"']+)[^"']*)["']/g;

    return html.replace(mosaicoImgRegex, (match, fullMosaicoUrl, encodedOriginalUrl) => {
      try {
        const decodedOriginalUrl = decodeURIComponent(encodedOriginalUrl);
        // Extract filename from the original URL (which might be http://localhost:8080/uploads/...)
        const filename = decodedOriginalUrl.substring(decodedOriginalUrl.lastIndexOf('/') + 1);
        if (filename) {
          // Construct the correct absolute URL using the app's base URL
          const correctUrl = `${appBaseUrl}/api/v1/images/serve/${encodeURIComponent(filename)}`;
          console.log(`[URL Correction] Replacing ${fullMosaicoUrl} with ${correctUrl}`);
          return `src="${correctUrl}"`;
        }
      } catch (e) {
        console.error(`[URL Correction] Error processing URL: ${fullMosaicoUrl}`, e);
      }
      // If any error occurs or filename can't be extracted, return the original match
      return match;
    });
  } catch (e) {
    console.error("[URL Correction] Error during regex replacement:", e);
    return html; // Return original HTML on major error
  }
};
// --- End helper function ---

// --- Define the Queue Processor ---
emailQueue.process(async (job) => {
  const { campaignId, batchIndex, batchSize, emailIndex = 0 } = job.data;
  console.log(`[Queue Process] Starting job ${job.id} for campaign ${campaignId}, emailIndex: ${emailIndex}, batch ${batchIndex}`);

  let currentCampaignStatus: string | undefined;

  try {
    // 1. Find campaign & User
    const campaign = await Campaign.findById(campaignId);
    if (!campaign) throw new Error(`Campaign not found (ID: ${campaignId})`);
    
    currentCampaignStatus = campaign.status; // Store initial status for comparison
    
    if (['cancelled', 'failed', 'completed'].includes(campaign.status)) {
      console.log(`[Queue Process] Skipping job ${job.id} because campaign ${campaignId} status is already ${campaign.status}.`);
      return { success: true, message: `Campaign ${campaign.status}, job skipped.` };
    }
    const user = await User.findById(campaign.userId);
    if (!user) throw new Error(`User not found for campaign ${campaignId} (User ID: ${campaign.userId})`);
    if (!user.domain || user.domain.status !== 'active') {
       campaign.status = 'failed';
       campaign.errorDescription = 'User domain not verified at scheduled send time.';
       campaign.completedAt = new Date();
       await campaign.save();
       // Emit status update on failure
       if (currentCampaignStatus !== campaign.status) {
            console.log(`[Queue Process] Emitting status update for campaign ${campaignId}: ${campaign.status}`);
            io.emit('campaignStatusUpdate', {
                 id: campaignId.toString(),
                 status: campaign.status,
                 completedAt: campaign.completedAt?.toISOString()
             });
       }
      throw new Error(`User ${user._id} does not have a verified domain`);
    }

    // 2. Set Initial 'Sending' Status
    if (emailIndex === 0 && batchIndex === 0 && ['draft', 'scheduled'].includes(campaign.status)) {
      console.log(`[Queue Process] First job for campaign ${campaignId}. Setting status to 'sending'.`);
      campaign.status = 'sending';
      campaign.startedAt = new Date();
      campaign.sentCount = 0;
      campaign.errorCount = 0;
      await campaign.save();
      // Emit status update with startedAt
      if (currentCampaignStatus !== campaign.status) {
          console.log(`[Queue Process] Emitting status update for campaign ${campaignId}: ${campaign.status}`);
          io.emit('campaignStatusUpdate', {
               id: campaignId.toString(),
               status: campaign.status,
               startedAt: campaign.startedAt?.toISOString()
          });
          currentCampaignStatus = campaign.status; // Update current status
      }
    }

    // 3. Get Current Email Content for this emailIndex
    let currentHtmlContent: string | undefined;
    let currentCssContent: string | undefined;

    if (campaign.emailContents && campaign.emailContents.length > emailIndex) {
      currentHtmlContent = campaign.emailContents[emailIndex].html;
      currentCssContent = campaign.emailContents[emailIndex].css;
    } else if (emailIndex === 0) {
        // Fallback to top-level content if sequence array is missing/empty BUT it's the first email
        currentHtmlContent = campaign.htmlContent;
        currentCssContent = campaign.cssContent;
        console.warn(`[Queue Process] Campaign ${campaignId} missing emailContents array, using top-level content for emailIndex 0.`);
    }

    if (!currentHtmlContent) {
      // If content is missing for the current index, fail the campaign
      campaign.status = 'failed';
      campaign.errorDescription = `Missing email content for email #${emailIndex + 1} (index ${emailIndex}).`;
      campaign.completedAt = new Date();
      await campaign.save();
      // Emit status update on failure with completedAt
       if (currentCampaignStatus !== campaign.status) {
            console.log(`[Queue Process] Emitting status update for campaign ${campaignId}: ${campaign.status}`);
            io.emit('campaignStatusUpdate', {
                 id: campaignId.toString(),
                 status: campaign.status,
                 completedAt: campaign.completedAt?.toISOString()
             });
       }
      throw new Error(`Campaign ${campaignId}: Missing content for email index ${emailIndex}.`);
    }

    // 4. Fetch Recipient Batch - MODIFIED to use lastEmailIndexSent
    console.log(`[Queue Process] Getting recipients for campaign ${campaignId} needing emailIndex ${emailIndex} (batch ${batchIndex})`);
    const campaignRecipients = await CampaignRecipient.find({
      campaignId,
      lastEmailIndexSent: { $lt: emailIndex } // Find recipients who haven't received this email index yet
      // We no longer filter by status: 'pending' here, as status reflects the outcome of the *last* email sent
    })
    .sort({ createdAt: 1 })
    .limit(batchSize)
    .populate('recipientId', 'email firstName lastName');

    console.log(`[Queue Process] Found ${campaignRecipients.length} recipients needing emailIndex ${emailIndex} for campaign ${campaignId} batch ${batchIndex}`);

    // If no recipients need this email index in this batch, check if any others might need it later
    // This condition might be reached if a previous batch processed all remaining recipients for this email index.
    if (campaignRecipients.length === 0) {
       console.log(`[Queue Process] No recipients found needing emailIndex ${emailIndex} in this batch.`);
       // Proceed directly to checking if next email should be scheduled or campaign completed
       // Fall through to step 7
    }

    // 5. Process Batch (using current email's content)
    const domain = user.domain.name;
    const fromName = campaign.fromName;
    const fromEmail = `noreply@${domain}`;
    const subject = campaign.subject; // Assuming subject is same for all sequence emails? If not, need to add subject to emailContents
    const isValidReplyTo = campaign.replyTo && campaign.replyTo.includes('@');
    const replyTo = isValidReplyTo ? campaign.replyTo : fromEmail;
    const from = `${fromName} <${fromEmail}>`;

    console.log(`[Queue Process] Sending batch ${batchIndex} for campaign ${campaignId}, emailIndex: ${emailIndex}. From: ${from}`);

    let batchSentCount = 0;
    let batchErrorCount = 0;

    for (const cr of campaignRecipients) {
       if (!cr.recipientId || typeof cr.recipientId !== 'object') {
          console.error(`[Queue Process] Skipping recipient - recipientId not populated correctly for CampaignRecipient ${cr._id}. Data:`, cr);
          // Update status and index to prevent retry for this email index
          await CampaignRecipient.findByIdAndUpdate(cr._id, { status: 'failed', errorDescription: 'Internal: Missing recipient data during send.', lastEmailIndexSent: emailIndex });
          batchErrorCount++;
          continue;
      }
      const recipient = cr.recipientId as any;
      const recipientEmail = recipient.email;
      const recipientIdString = recipient._id.toString();

      if (!recipientEmail) {
          console.error(`[Queue Process] Skipping recipient - Email missing for populated recipientId ${recipientIdString} in CampaignRecipient ${cr._id}.`);
          // Update status and index to prevent retry for this email index
           await CampaignRecipient.findByIdAndUpdate(cr._id, { status: 'failed', errorDescription: 'Internal: Missing recipient email during send.', lastEmailIndexSent: emailIndex });
           batchErrorCount++;
          continue;
      }

      try {
        // Apply tracking/unsubscribe using the fetched content
        let processedHtml = currentHtmlContent;
        processedHtml = addTrackingPixel(processedHtml, campaignId.toString(), recipientIdString);
        processedHtml = replaceLinksWithTrackableLinks(processedHtml, campaignId.toString(), recipientIdString);
        processedHtml = addUnsubscribeLink(processedHtml, campaignId.toString(), recipientIdString);

        // --- Log HTML before correction for debugging ---
        // console.log("[Queue Process] HTML content BEFORE URL correction (showing img tags):");
        // const imgTags = processedHtml.match(/<img[^>]+src=[^>]+>/g) || [];
        // imgTags.forEach(tag => console.log(tag));
        // --- End Debug Log ---

        // --- Correct Mosaico Image URLs before sending ---
        const appBaseUrl = process.env.APP_BASE_URL || `http://localhost:${process.env.PORT || 3000}`; // Get app base URL
        processedHtml = correctMosaicoImageUrls(processedHtml, appBaseUrl);
        // --- End URL Correction ---

        // Send email via SES
        await SESService.sendEmail({
          from,
          to: [recipientEmail],
          subject, // Assuming same subject for all emails
          html: processedHtml, // Use the processed content
          replyTo,
          campaignId: campaignId.toString(),
          subscriberId: recipientIdString
        });

        // Update CampaignRecipient status AND lastEmailIndexSent on SUCCESS
        await CampaignRecipient.findByIdAndUpdate(cr._id, {
            status: 'sent',
            sentAt: new Date(),
            lastEmailIndexSent: emailIndex // Mark this email index as sent for this recipient
        });
        batchSentCount++;
        // console.log(`[Queue Process] Successfully sent email ${emailIndex + 1} to ${recipientEmail} for campaign ${campaignId}`);

      } catch (error: any) {
        // ... (error handling for individual email send remains the same) ...
        console.error(`[Queue Process] Error sending email ${emailIndex + 1} to ${recipientEmail} for campaign ${campaignId}:`, error.message);
         await CampaignRecipient.findByIdAndUpdate(cr._id, {
            status: 'failed',
            bouncedAt: new Date(),
            errorDescription: error.message || 'Failed to send',
            lastEmailIndexSent: emailIndex // Mark this email index as attempted (failed) for this recipient
         });
        batchErrorCount++;
      }
    }

    // 6. Update Campaign Counts
    if (batchSentCount > 0 || batchErrorCount > 0) { // Only update if something happened
      await Campaign.findByIdAndUpdate(campaignId, {
          $inc: { sentCount: batchSentCount, errorCount: batchErrorCount }
      });
    }
     console.log(`[Queue Process] Batch ${batchIndex} for campaign ${campaignId}, emailIndex ${emailIndex} finished. Sent: ${batchSentCount}, Errors: ${batchErrorCount}`);

    // 7. Determine Next Step - MODIFIED check
    // Check if any recipients still need the CURRENT email index
    const moreRecipientsNeedCurrentEmail = await CampaignRecipient.exists({ campaignId, lastEmailIndexSent: { $lt: emailIndex } });

    if (moreRecipientsNeedCurrentEmail) {
      // 7a. More recipients pending for the CURRENT email index -> Queue next batch
      console.log(`[Queue Process] More recipients need emailIndex ${emailIndex} for campaign ${campaignId}. Queueing next batch.`);
      await emailQueue.add(
        {
          campaignId,
          emailIndex, // Keep same email index
          batchIndex: batchIndex + 1,
          batchSize
        },
        {
          delay: 1000 // Add a small delay between batches
        }
      );
    } else {
      // 7b. All recipients have processed (sent or failed) the CURRENT email index -> Check for next email
      console.log(`[Queue Process] All recipients processed for emailIndex ${emailIndex} of campaign ${campaignId}. Checking for next email.`);

      const nextEmailIndex = emailIndex + 1;
      const hasNextEmail = campaign.emailContents && campaign.emailContents.length > nextEmailIndex;

      if (hasNextEmail) {
        // 7b-i. Next email exists -> Schedule its first batch
        console.log(`[Queue Process] Next email (index ${nextEmailIndex}) found for campaign ${campaignId}.`);

        // Calculate delay based on schedule
        let delayMillis = 1000; // Default small delay
        if (campaign.schedule && campaign.schedule.emailIntervals && campaign.schedule.emailIntervals.length > emailIndex) {
          const interval = campaign.schedule.emailIntervals[emailIndex];
          // Calculate delay based on unit
          if (interval.unit === 'minutes') { 
            delayMillis = interval.delay * 60 * 1000; // Convert minutes to ms
          } else if (interval.unit === 'hours') {
            delayMillis = interval.delay * 60 * 60 * 1000; // Convert hours to ms
          } else if (interval.unit === 'days') {
            delayMillis = interval.delay * 24 * 60 * 60 * 1000; // Convert days to ms
          } else {
              console.warn(`[Queue Process] Campaign ${campaignId}: Unknown interval unit '${interval.unit}' for email index ${emailIndex}. Defaulting to 1s delay.`);
          }
          console.log(`[Queue Process] Scheduling emailIndex ${nextEmailIndex} for campaign ${campaignId} with delay: ${interval.delay} ${interval.unit} (${delayMillis}ms)`);
        } else {
          console.warn(`[Queue Process] Campaign ${campaignId}: Schedule interval data missing for email index ${emailIndex}. Defaulting to 1s delay before next email.`);
        }

        // Add job for the first batch of the next email
        await emailQueue.add(
          {
            campaignId,
            emailIndex: nextEmailIndex, // Use the next index
            batchIndex: 0, // Start batch index from 0
            batchSize
          },
          {
            delay: Math.max(1000, delayMillis) // Ensure minimum 1s delay
          }
        );
        console.log(`[Queue Process] Job added for emailIndex ${nextEmailIndex}, batch 0 for campaign ${campaignId}.`);

      } else {
        // 7b-ii. No next email -> Mark campaign as completed
        console.log(`[Queue Process] Last email (index ${emailIndex}) processed for campaign ${campaignId}. Marking campaign as completed.`);
        // Refetch campaign to be sure about status before marking completed
        const finalCampaign = await Campaign.findById(campaignId);
        if (finalCampaign && finalCampaign.status === 'sending') { // Only complete if it was sending
            finalCampaign.status = 'completed';
            finalCampaign.completedAt = new Date();
            await finalCampaign.save();

            // Optional: Send SNS notification
            if (process.env.SNS_TOPIC_ARN) {
              await SNSService.publishNotification({
                topicArn: process.env.SNS_TOPIC_ARN,
                message: `Campaign "${finalCampaign.name}" (ID: ${campaignId}) has completed the entire sequence.`,
                subject: 'Campaign Sequence Completed'
              }).catch(snsError => console.error(`[Queue Process] Failed to send completion notification for campaign ${campaignId}:`, snsError));
            }
            // Emit status update with completedAt
            if (currentCampaignStatus !== finalCampaign.status) {
                console.log(`[Queue Process] Emitting status update for campaign ${campaignId}: ${finalCampaign.status}`);
                io.emit('campaignStatusUpdate', {
                     id: campaignId.toString(),
                     status: finalCampaign.status,
                     completedAt: finalCampaign.completedAt?.toISOString()
                 });
            }
            console.log(`[Queue Process] Campaign ${campaignId} successfully marked as completed.`);
        } else {
             console.log(`[Queue Process] Campaign ${campaignId} final status was ${finalCampaign?.status}. Not marking as completed.`);
        }
      }
    }

    return { success: true, message: `Job ${job.id} (Email ${emailIndex + 1}, Batch ${batchIndex}) processed. Sent: ${batchSentCount}, Errors: ${batchErrorCount}` };

  } catch (error: any) {
    console.error(`[Queue Process] CRITICAL ERROR processing job ${job.id} (Campaign ${campaignId}, Email ${emailIndex}, Batch ${batchIndex}):`, error);
     // Attempt to mark campaign as failed
     try {
       // Fetch before update to check previous status
       const campaignBeforeFail = await Campaign.findById(campaignId);
       const prevStatus = campaignBeforeFail?.status;
       
       await Campaign.findByIdAndUpdate(campaignId, {
         status: 'failed',
         errorDescription: `Worker critical error processing email #${emailIndex + 1}: ${error.message}`,
         completedAt: new Date(),
         // Don't assume all in batch failed here, let individual failures be counted
       });
       // Emit status update only if status actually changed
       if (prevStatus !== 'failed') {
            console.log(`[Queue Process] Emitting status update for campaign ${campaignId}: failed (due to critical error)`);
            // Try to send completedAt if available (set within the update)
            const failedCampaign = await Campaign.findById(campaignId).select('completedAt');
            io.emit('campaignStatusUpdate', {
                 id: campaignId.toString(),
                 status: 'failed',
                 completedAt: failedCampaign?.completedAt?.toISOString()
             });
       }
     } catch (updateError) {
       console.error(`[Queue Process] Failed to mark campaign ${campaignId} as failed after critical error:`, updateError);
     }
    throw error;
  }
});
// --- End Queue Processor ---

console.log('[Queue] Bull queue processor configured.');

// Export the configured queue instance
export default emailQueue; 