{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState } from 'react';\nimport axios from 'axios';\n\n// Create a utility file for authentication-related functions\n// const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1'; // Original with fallback\n// const API_URL = 'http://localhost:3000/api'; // Previous incorrect fix\nconst API_URL = 'http://localhost:3000/api/v1'; // Use the correct backend URL with /v1\n\n// Register a new user\nexport const registerUser = async (name, email, password) => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/register`, {\n      name,\n      email,\n      password\n    });\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Login a user\nexport const loginUser = async (email, password) => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/login`, {\n      email,\n      password\n    });\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Refresh token\nexport const refreshUserToken = async refreshToken => {\n  try {\n    // Use direct axios call to prevent interceptor loops\n    const response = await axios.post(`${API_URL}/auth/refresh-token`, {\n      refreshToken\n    });\n\n    // Return data in expected format\n    return response.data;\n  } catch (error) {\n    console.error('Error refreshing token:', error);\n    throw error;\n  }\n};\n\n// Check if token is expired\nexport const isTokenExpired = token => {\n  if (!token) return true;\n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function (c) {\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n    const {\n      exp\n    } = JSON.parse(jsonPayload);\n    const expired = Date.now() >= exp * 1000;\n    return expired;\n  } catch (e) {\n    return true;\n  }\n};\n\n// Logout a user\nexport const logoutUser = async () => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/logout`);\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Set up axios interceptors for authentication\nexport const setupAuthInterceptors = (logoutFn, axiosInstance) => {\n  // Determine which axios instance to use (custom instance or global)\n  const instance = axiosInstance || axios;\n\n  // Remove any existing interceptors to prevent duplicates\n  if (instance.interceptors && instance.__refreshInterceptorId) {\n    instance.interceptors.response.eject(instance.__refreshInterceptorId);\n  }\n\n  // Track if we're currently refreshing to prevent multiple refresh requests\n  let isRefreshing = false;\n  // Queue of requests to retry after token refresh\n  let failedQueue = [];\n\n  // Process failed queue (retry or reject)\n  const processQueue = (error, token = null) => {\n    failedQueue.forEach(prom => {\n      if (error) {\n        prom.reject(error);\n      } else {\n        prom.resolve(token);\n      }\n    });\n    failedQueue = [];\n  };\n\n  // Request interceptor to add token to all requests\n  instance.interceptors.request.use(async config => {\n    // Skip token check for refresh token request to avoid infinite loop\n    if (config.url.includes('/auth/refresh-token') || config.url.includes('/auth/login')) {\n      return config;\n    }\n    let token = localStorage.getItem('token');\n\n    // Check if token exists and is expired\n    if (token && isTokenExpired(token)) {\n      console.log('Token expired, trying to refresh...');\n\n      // If not already refreshing, attempt to refresh the token\n      if (!isRefreshing) {\n        isRefreshing = true;\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (!refreshToken) {\n          isRefreshing = false;\n          await logoutFn();\n          return Promise.reject(new Error('No refresh token available'));\n        }\n        try {\n          // Call the refresh endpoint directly\n          const response = await axios.post(`${API_URL}/auth/refresh-token`, {\n            refreshToken\n          });\n          if (response.data.data && response.data.data.token) {\n            // Update tokens\n            localStorage.setItem('token', response.data.data.token);\n            localStorage.setItem('refreshToken', response.data.data.refreshToken || refreshToken);\n            token = response.data.data.token;\n\n            // Update axios defaults\n            instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n            // If using custom instance, update global axios too\n            if (axiosInstance) {\n              axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n            }\n\n            // Process queued requests\n            processQueue(null, token);\n          } else {\n            // Invalid response format\n            await logoutFn();\n            processQueue(new Error('Failed to refresh token'));\n          }\n        } catch (refreshError) {\n          // Refresh failed\n          processQueue(refreshError);\n          await logoutFn();\n        } finally {\n          isRefreshing = false;\n        }\n      }\n\n      // If currently refreshing, queue this request\n      if (isRefreshing) {\n        return new Promise((resolve, reject) => {\n          failedQueue.push({\n            resolve,\n            reject\n          });\n        }).then(newToken => {\n          config.headers.Authorization = `Bearer ${newToken}`;\n          return config;\n        }).catch(err => {\n          return Promise.reject(err);\n        });\n      }\n    }\n\n    // Add token to headers if available\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  }, error => {\n    return Promise.reject(error);\n  });\n\n  // Response interceptor to handle 401 errors\n  const responseInterceptorId = instance.interceptors.response.use(response => {\n    return response;\n  }, async error => {\n    var _error$response;\n    const originalRequest = error.config;\n\n    // Only handle 401 errors and avoid infinite retry loops\n    if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      // If already refreshing, queue the request\n      if (isRefreshing) {\n        try {\n          const token = await new Promise((resolve, reject) => {\n            failedQueue.push({\n              resolve,\n              reject\n            });\n          });\n          originalRequest.headers['Authorization'] = `Bearer ${token}`;\n          return instance(originalRequest);\n        } catch (err) {\n          return Promise.reject(err);\n        }\n      }\n\n      // Start refreshing if not already\n      if (!isRefreshing) {\n        isRefreshing = true;\n        try {\n          const refreshToken = localStorage.getItem('refreshToken');\n          if (!refreshToken) {\n            throw new Error('No refresh token available');\n          }\n          const response = await axios.post(`${API_URL}/auth/refresh-token`, {\n            refreshToken\n          });\n          if (response.data.data && response.data.data.token) {\n            // Update tokens\n            const newToken = response.data.data.token;\n            localStorage.setItem('token', newToken);\n            localStorage.setItem('refreshToken', response.data.data.refreshToken || refreshToken);\n\n            // Update headers\n            instance.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n            originalRequest.headers['Authorization'] = `Bearer ${newToken}`;\n\n            // Process queue\n            processQueue(null, newToken);\n\n            // Retry original request\n            return instance(originalRequest);\n          } else {\n            // Invalid response\n            await logoutFn();\n            processQueue(new Error('Failed to refresh token'));\n            return Promise.reject(error);\n          }\n        } catch (refreshError) {\n          // Refresh failed\n          await logoutFn();\n          processQueue(refreshError);\n          return Promise.reject(refreshError);\n        } finally {\n          isRefreshing = false;\n        }\n      }\n    }\n    return Promise.reject(error);\n  });\n\n  // Store the interceptor ID so we can remove it later if needed\n  instance.__refreshInterceptorId = responseInterceptorId;\n  return instance;\n};\n\n// Custom hook for form handling\nexport const useForm = initialState => {\n  _s();\n  const [values, setValues] = useState(initialState);\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setValues({\n      ...values,\n      [name]: value\n    });\n  };\n  const resetForm = () => {\n    setValues(initialState);\n  };\n  return {\n    values,\n    handleChange,\n    resetForm,\n    setValues\n  };\n};\n_s(useForm, \"tR5pueiAhaduWSnglIJ8EBw8xeE=\");", "map": {"version": 3, "names": ["useState", "axios", "API_URL", "registerUser", "name", "email", "password", "response", "post", "data", "error", "loginUser", "refreshUserToken", "refreshToken", "console", "isTokenExpired", "token", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "toString", "slice", "join", "exp", "JSON", "parse", "expired", "Date", "now", "e", "logoutUser", "setupAuthInterceptors", "logoutFn", "axiosInstance", "instance", "interceptors", "__refreshInterceptorId", "eject", "isRefreshing", "failedQueue", "processQueue", "for<PERSON>ach", "prom", "reject", "resolve", "request", "use", "config", "url", "includes", "localStorage", "getItem", "log", "Promise", "Error", "setItem", "defaults", "headers", "common", "refreshError", "push", "then", "newToken", "Authorization", "catch", "err", "responseInterceptorId", "_error$response", "originalRequest", "status", "_retry", "useForm", "initialState", "_s", "values", "set<PERSON><PERSON><PERSON>", "handleChange", "value", "target", "resetForm"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/utils/auth.ts"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport axios from 'axios';\n\n// Create a utility file for authentication-related functions\n// const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1'; // Original with fallback\n// const API_URL = 'http://localhost:3000/api'; // Previous incorrect fix\nconst API_URL = 'http://localhost:3000/api/v1'; // Use the correct backend URL with /v1\n\n// Register a new user\nexport const registerUser = async (name: string, email: string, password: string) => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/register`, {\n      name,\n      email,\n      password\n    });\n    \n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Login a user\nexport const loginUser = async (email: string, password: string) => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/login`, {\n      email,\n      password\n    });\n    \n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Refresh token\nexport const refreshUserToken = async (refreshToken: string) => {\n  try {\n    // Use direct axios call to prevent interceptor loops\n    const response = await axios.post(`${API_URL}/auth/refresh-token`, {\n      refreshToken\n    });\n    \n    // Return data in expected format\n    return response.data;\n  } catch (error) {\n    console.error('Error refreshing token:', error);\n    throw error;\n  }\n};\n\n// Check if token is expired\nexport const isTokenExpired = (token: string) => {\n  if (!token) return true;\n  \n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n    \n    const { exp } = JSON.parse(jsonPayload);\n    const expired = Date.now() >= exp * 1000;\n    \n    return expired;\n  } catch (e) {\n    return true;\n  }\n};\n\n// Logout a user\nexport const logoutUser = async () => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/logout`);\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Set up axios interceptors for authentication\nexport const setupAuthInterceptors = (logoutFn: () => Promise<void>, axiosInstance?: any) => {\n  // Determine which axios instance to use (custom instance or global)\n  const instance = axiosInstance || axios;\n  \n  // Remove any existing interceptors to prevent duplicates\n  if (instance.interceptors && instance.__refreshInterceptorId) {\n    instance.interceptors.response.eject(instance.__refreshInterceptorId);\n  }\n  \n  // Track if we're currently refreshing to prevent multiple refresh requests\n  let isRefreshing = false;\n  // Queue of requests to retry after token refresh\n  let failedQueue: any[] = [];\n\n  // Process failed queue (retry or reject)\n  const processQueue = (error: any, token: string | null = null) => {\n    failedQueue.forEach(prom => {\n      if (error) {\n        prom.reject(error);\n      } else {\n        prom.resolve(token);\n      }\n    });\n    failedQueue = [];\n  };\n  \n  // Request interceptor to add token to all requests\n  instance.interceptors.request.use(\n    async (config: any) => {\n      // Skip token check for refresh token request to avoid infinite loop\n      if (config.url.includes('/auth/refresh-token') || config.url.includes('/auth/login')) {\n        return config;\n      }\n      \n      let token = localStorage.getItem('token');\n      \n      // Check if token exists and is expired\n      if (token && isTokenExpired(token)) {\n        console.log('Token expired, trying to refresh...');\n        \n        // If not already refreshing, attempt to refresh the token\n        if (!isRefreshing) {\n          isRefreshing = true;\n          \n          const refreshToken = localStorage.getItem('refreshToken');\n          if (!refreshToken) {\n            isRefreshing = false;\n            await logoutFn();\n            return Promise.reject(new Error('No refresh token available'));\n          }\n          \n          try {\n            // Call the refresh endpoint directly\n            const response = await axios.post(`${API_URL}/auth/refresh-token`, { \n              refreshToken \n            });\n            \n            if (response.data.data && response.data.data.token) {\n              // Update tokens\n              localStorage.setItem('token', response.data.data.token);\n              localStorage.setItem('refreshToken', response.data.data.refreshToken || refreshToken);\n              token = response.data.data.token;\n              \n              // Update axios defaults\n              instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n              \n              // If using custom instance, update global axios too\n              if (axiosInstance) {\n                axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n              }\n              \n              // Process queued requests\n              processQueue(null, token);\n            } else {\n              // Invalid response format\n              await logoutFn();\n              processQueue(new Error('Failed to refresh token'));\n            }\n          } catch (refreshError) {\n            // Refresh failed\n            processQueue(refreshError);\n            await logoutFn();\n          } finally {\n            isRefreshing = false;\n          }\n        }\n        \n        // If currently refreshing, queue this request\n        if (isRefreshing) {\n          return new Promise((resolve, reject) => {\n            failedQueue.push({ resolve, reject });\n          }).then((newToken) => {\n            config.headers.Authorization = `Bearer ${newToken}`;\n            return config;\n          }).catch(err => {\n            return Promise.reject(err);\n          });\n        }\n      }\n      \n      // Add token to headers if available\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      \n      return config;\n    },\n    (error: any) => {\n      return Promise.reject(error);\n    }\n  );\n\n  // Response interceptor to handle 401 errors\n  const responseInterceptorId = instance.interceptors.response.use(\n    (response: any) => {\n      return response;\n    },\n    async (error: any) => {\n      const originalRequest = error.config;\n      \n      // Only handle 401 errors and avoid infinite retry loops\n      if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        \n        // If already refreshing, queue the request\n        if (isRefreshing) {\n          try {\n            const token = await new Promise((resolve, reject) => {\n              failedQueue.push({ resolve, reject });\n            });\n            originalRequest.headers['Authorization'] = `Bearer ${token}`;\n            return instance(originalRequest);\n          } catch (err) {\n            return Promise.reject(err);\n          }\n        }\n        \n        // Start refreshing if not already\n        if (!isRefreshing) {\n          isRefreshing = true;\n          \n          try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (!refreshToken) {\n              throw new Error('No refresh token available');\n            }\n            \n            const response = await axios.post(`${API_URL}/auth/refresh-token`, { \n              refreshToken \n            });\n            \n            if (response.data.data && response.data.data.token) {\n              // Update tokens\n              const newToken = response.data.data.token;\n              localStorage.setItem('token', newToken);\n              localStorage.setItem('refreshToken', response.data.data.refreshToken || refreshToken);\n              \n              // Update headers\n              instance.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n              originalRequest.headers['Authorization'] = `Bearer ${newToken}`;\n              \n              // Process queue\n              processQueue(null, newToken);\n              \n              // Retry original request\n              return instance(originalRequest);\n            } else {\n              // Invalid response\n              await logoutFn();\n              processQueue(new Error('Failed to refresh token'));\n              return Promise.reject(error);\n            }\n          } catch (refreshError) {\n            // Refresh failed\n            await logoutFn();\n            processQueue(refreshError);\n            return Promise.reject(refreshError);\n          } finally {\n            isRefreshing = false;\n          }\n        }\n      }\n      \n      return Promise.reject(error);\n    }\n  );\n  \n  // Store the interceptor ID so we can remove it later if needed\n  instance.__refreshInterceptorId = responseInterceptorId;\n  \n  return instance;\n};\n\n// Custom hook for form handling\nexport const useForm = (initialState: any) => {\n  const [values, setValues] = useState(initialState);\n  \n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setValues({\n      ...values,\n      [name]: value\n    });\n  };\n  \n  const resetForm = () => {\n    setValues(initialState);\n  };\n  \n  return { values, handleChange, resetForm, setValues };\n};\n"], "mappings": ";AAAA,SAAgBA,QAAQ,QAAQ,OAAO;AAEvC,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA,MAAMC,OAAO,GAAG,8BAA8B,CAAC,CAAC;;AAEhD;AACA,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,IAAY,EAAEC,KAAa,EAAEC,QAAgB,KAAK;EACnF,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,gBAAgB,EAAE;MAC5DE,IAAI;MACJC,KAAK;MACLC;IACF,CAAC,CAAC;IAEF,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAG,MAAAA,CAAON,KAAa,EAAEC,QAAgB,KAAK;EAClE,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,aAAa,EAAE;MACzDG,KAAK;MACLC;IACF,CAAC,CAAC;IAEF,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAME,gBAAgB,GAAG,MAAOC,YAAoB,IAAK;EAC9D,IAAI;IACF;IACA,MAAMN,QAAQ,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,qBAAqB,EAAE;MACjEW;IACF,CAAC,CAAC;;IAEF;IACA,OAAON,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,cAAc,GAAIC,KAAa,IAAK;EAC/C,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;EAEvB,IAAI;IACF,MAAMC,SAAS,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACrC,MAAMC,MAAM,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;IAC9D,MAAMC,WAAW,GAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAAC,UAASC,CAAC,EAAE;MAC5E,OAAO,GAAG,GAAG,CAAC,IAAI,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEZ,MAAM;MAAEC;IAAI,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACX,WAAW,CAAC;IACvC,MAAMY,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIL,GAAG,GAAG,IAAI;IAExC,OAAOG,OAAO;EAChB,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,IAAI;IACF,MAAM9B,QAAQ,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,cAAc,CAAC;IAC3D,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAM4B,qBAAqB,GAAGA,CAACC,QAA6B,EAAEC,aAAmB,KAAK;EAC3F;EACA,MAAMC,QAAQ,GAAGD,aAAa,IAAIvC,KAAK;;EAEvC;EACA,IAAIwC,QAAQ,CAACC,YAAY,IAAID,QAAQ,CAACE,sBAAsB,EAAE;IAC5DF,QAAQ,CAACC,YAAY,CAACnC,QAAQ,CAACqC,KAAK,CAACH,QAAQ,CAACE,sBAAsB,CAAC;EACvE;;EAEA;EACA,IAAIE,YAAY,GAAG,KAAK;EACxB;EACA,IAAIC,WAAkB,GAAG,EAAE;;EAE3B;EACA,MAAMC,YAAY,GAAGA,CAACrC,KAAU,EAAEM,KAAoB,GAAG,IAAI,KAAK;IAChE8B,WAAW,CAACE,OAAO,CAACC,IAAI,IAAI;MAC1B,IAAIvC,KAAK,EAAE;QACTuC,IAAI,CAACC,MAAM,CAACxC,KAAK,CAAC;MACpB,CAAC,MAAM;QACLuC,IAAI,CAACE,OAAO,CAACnC,KAAK,CAAC;MACrB;IACF,CAAC,CAAC;IACF8B,WAAW,GAAG,EAAE;EAClB,CAAC;;EAED;EACAL,QAAQ,CAACC,YAAY,CAACU,OAAO,CAACC,GAAG,CAC/B,MAAOC,MAAW,IAAK;IACrB;IACA,IAAIA,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,qBAAqB,CAAC,IAAIF,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;MACpF,OAAOF,MAAM;IACf;IAEA,IAAItC,KAAK,GAAGyC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;IAEzC;IACA,IAAI1C,KAAK,IAAID,cAAc,CAACC,KAAK,CAAC,EAAE;MAClCF,OAAO,CAAC6C,GAAG,CAAC,qCAAqC,CAAC;;MAElD;MACA,IAAI,CAACd,YAAY,EAAE;QACjBA,YAAY,GAAG,IAAI;QAEnB,MAAMhC,YAAY,GAAG4C,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QACzD,IAAI,CAAC7C,YAAY,EAAE;UACjBgC,YAAY,GAAG,KAAK;UACpB,MAAMN,QAAQ,CAAC,CAAC;UAChB,OAAOqB,OAAO,CAACV,MAAM,CAAC,IAAIW,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChE;QAEA,IAAI;UACF;UACA,MAAMtD,QAAQ,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,qBAAqB,EAAE;YACjEW;UACF,CAAC,CAAC;UAEF,IAAIN,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,EAAE;YAClD;YACAyC,YAAY,CAACK,OAAO,CAAC,OAAO,EAAEvD,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,CAAC;YACvDyC,YAAY,CAACK,OAAO,CAAC,cAAc,EAAEvD,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,YAAY,IAAIA,YAAY,CAAC;YACrFG,KAAK,GAAGT,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK;;YAEhC;YACAyB,QAAQ,CAACsB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUjD,KAAK,EAAE;;YAErE;YACA,IAAIwB,aAAa,EAAE;cACjBvC,KAAK,CAAC8D,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUjD,KAAK,EAAE;YACpE;;YAEA;YACA+B,YAAY,CAAC,IAAI,EAAE/B,KAAK,CAAC;UAC3B,CAAC,MAAM;YACL;YACA,MAAMuB,QAAQ,CAAC,CAAC;YAChBQ,YAAY,CAAC,IAAIc,KAAK,CAAC,yBAAyB,CAAC,CAAC;UACpD;QACF,CAAC,CAAC,OAAOK,YAAY,EAAE;UACrB;UACAnB,YAAY,CAACmB,YAAY,CAAC;UAC1B,MAAM3B,QAAQ,CAAC,CAAC;QAClB,CAAC,SAAS;UACRM,YAAY,GAAG,KAAK;QACtB;MACF;;MAEA;MACA,IAAIA,YAAY,EAAE;QAChB,OAAO,IAAIe,OAAO,CAAC,CAACT,OAAO,EAAED,MAAM,KAAK;UACtCJ,WAAW,CAACqB,IAAI,CAAC;YAAEhB,OAAO;YAAED;UAAO,CAAC,CAAC;QACvC,CAAC,CAAC,CAACkB,IAAI,CAAEC,QAAQ,IAAK;UACpBf,MAAM,CAACU,OAAO,CAACM,aAAa,GAAG,UAAUD,QAAQ,EAAE;UACnD,OAAOf,MAAM;QACf,CAAC,CAAC,CAACiB,KAAK,CAACC,GAAG,IAAI;UACd,OAAOZ,OAAO,CAACV,MAAM,CAACsB,GAAG,CAAC;QAC5B,CAAC,CAAC;MACJ;IACF;;IAEA;IACA,IAAIxD,KAAK,EAAE;MACTsC,MAAM,CAACU,OAAO,CAACM,aAAa,GAAG,UAAUtD,KAAK,EAAE;IAClD;IAEA,OAAOsC,MAAM;EACf,CAAC,EACA5C,KAAU,IAAK;IACd,OAAOkD,OAAO,CAACV,MAAM,CAACxC,KAAK,CAAC;EAC9B,CACF,CAAC;;EAED;EACA,MAAM+D,qBAAqB,GAAGhC,QAAQ,CAACC,YAAY,CAACnC,QAAQ,CAAC8C,GAAG,CAC7D9C,QAAa,IAAK;IACjB,OAAOA,QAAQ;EACjB,CAAC,EACD,MAAOG,KAAU,IAAK;IAAA,IAAAgE,eAAA;IACpB,MAAMC,eAAe,GAAGjE,KAAK,CAAC4C,MAAM;;IAEpC;IACA,IAAI,EAAAoB,eAAA,GAAAhE,KAAK,CAACH,QAAQ,cAAAmE,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;MAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;;MAE7B;MACA,IAAIhC,YAAY,EAAE;QAChB,IAAI;UACF,MAAM7B,KAAK,GAAG,MAAM,IAAI4C,OAAO,CAAC,CAACT,OAAO,EAAED,MAAM,KAAK;YACnDJ,WAAW,CAACqB,IAAI,CAAC;cAAEhB,OAAO;cAAED;YAAO,CAAC,CAAC;UACvC,CAAC,CAAC;UACFyB,eAAe,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUhD,KAAK,EAAE;UAC5D,OAAOyB,QAAQ,CAACkC,eAAe,CAAC;QAClC,CAAC,CAAC,OAAOH,GAAG,EAAE;UACZ,OAAOZ,OAAO,CAACV,MAAM,CAACsB,GAAG,CAAC;QAC5B;MACF;;MAEA;MACA,IAAI,CAAC3B,YAAY,EAAE;QACjBA,YAAY,GAAG,IAAI;QAEnB,IAAI;UACF,MAAMhC,YAAY,GAAG4C,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;UACzD,IAAI,CAAC7C,YAAY,EAAE;YACjB,MAAM,IAAIgD,KAAK,CAAC,4BAA4B,CAAC;UAC/C;UAEA,MAAMtD,QAAQ,GAAG,MAAMN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,qBAAqB,EAAE;YACjEW;UACF,CAAC,CAAC;UAEF,IAAIN,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,EAAE;YAClD;YACA,MAAMqD,QAAQ,GAAG9D,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK;YACzCyC,YAAY,CAACK,OAAO,CAAC,OAAO,EAAEO,QAAQ,CAAC;YACvCZ,YAAY,CAACK,OAAO,CAAC,cAAc,EAAEvD,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,YAAY,IAAIA,YAAY,CAAC;;YAErF;YACA4B,QAAQ,CAACsB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUI,QAAQ,EAAE;YACxEM,eAAe,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,QAAQ,EAAE;;YAE/D;YACAtB,YAAY,CAAC,IAAI,EAAEsB,QAAQ,CAAC;;YAE5B;YACA,OAAO5B,QAAQ,CAACkC,eAAe,CAAC;UAClC,CAAC,MAAM;YACL;YACA,MAAMpC,QAAQ,CAAC,CAAC;YAChBQ,YAAY,CAAC,IAAIc,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAClD,OAAOD,OAAO,CAACV,MAAM,CAACxC,KAAK,CAAC;UAC9B;QACF,CAAC,CAAC,OAAOwD,YAAY,EAAE;UACrB;UACA,MAAM3B,QAAQ,CAAC,CAAC;UAChBQ,YAAY,CAACmB,YAAY,CAAC;UAC1B,OAAON,OAAO,CAACV,MAAM,CAACgB,YAAY,CAAC;QACrC,CAAC,SAAS;UACRrB,YAAY,GAAG,KAAK;QACtB;MACF;IACF;IAEA,OAAOe,OAAO,CAACV,MAAM,CAACxC,KAAK,CAAC;EAC9B,CACF,CAAC;;EAED;EACA+B,QAAQ,CAACE,sBAAsB,GAAG8B,qBAAqB;EAEvD,OAAOhC,QAAQ;AACjB,CAAC;;AAED;AACA,OAAO,MAAMqC,OAAO,GAAIC,YAAiB,IAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlF,QAAQ,CAAC+E,YAAY,CAAC;EAElD,MAAMI,YAAY,GAAI/C,CAAsC,IAAK;IAC/D,MAAM;MAAEhC,IAAI;MAAEgF;IAAM,CAAC,GAAGhD,CAAC,CAACiD,MAAM;IAChCH,SAAS,CAAC;MACR,GAAGD,MAAM;MACT,CAAC7E,IAAI,GAAGgF;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtBJ,SAAS,CAACH,YAAY,CAAC;EACzB,CAAC;EAED,OAAO;IAAEE,MAAM;IAAEE,YAAY;IAAEG,SAAS;IAAEJ;EAAU,CAAC;AACvD,CAAC;AAACF,EAAA,CAhBWF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}