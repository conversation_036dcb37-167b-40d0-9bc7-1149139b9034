/**
 * Utility functions for email operations
 */

/**
 * Add an unsubscribe link to HTML email content
 * @param {string} htmlContent Original HTML content
 * @param {string} campaignId Campaign ID
 * @param {string} subscriberId Subscriber ID
 * @param {string} baseUrl Base URL for unsubscribe link (e.g., https://api.driftly.com)
 * @returns {string} HTML content with unsubscribe link
 */
export const addUnsubscribeLink = (
  htmlContent: string,
  campaignId: string,
  subscriberId: string, 
  baseUrl = process.env.API_BASE_URL || 'http://localhost:3000'
): string => {
  try {
    // Create unsubscribe URL with direct query parameters
    const unsubscribeUrl = `${baseUrl}/api/v1/analytics/track/unsubscribe?campaignId=${campaignId}&recipientId=${subscriberId}`;
    
    console.log(`[Email Utils] Creating unsubscribe link: ${unsubscribeUrl}`);
    
    // Create unsubscribe link HTML
    const unsubscribeHtml = `
    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; text-align: center; font-family: Arial, sans-serif;">
      <p>If you no longer wish to receive these emails, you can <a href="${unsubscribeUrl}" style="color: #666; text-decoration: underline;">unsubscribe here</a>.</p>
    </div>
    `;
    
    // Check if HTML has a closing body tag
    if (htmlContent.includes('</body>')) {
      // Insert unsubscribe link before closing body tag
      return htmlContent.replace('</body>', `${unsubscribeHtml}</body>`);
    } else {
      // Append unsubscribe link to the end
      return `${htmlContent}${unsubscribeHtml}`;
    }
  } catch (error) {
    console.error('[Email Utils] Error adding unsubscribe link:', error);
    // Return original content if there's an error
    return htmlContent;
  }
};

/**
 * Add tracking pixel for open tracking
 * @param {string} htmlContent Original HTML content
 * @param {string} campaignId Campaign ID
 * @param {string} subscriberId Subscriber ID
 * @param {string} baseUrl Base URL for tracking pixel
 * @returns {string} HTML content with tracking pixel
 */
export const addTrackingPixel = (
  htmlContent: string,
  campaignId?: string,
  subscriberId?: string,
  baseUrl = process.env.API_BASE_URL || 'http://localhost:3000'
): string => {
  try {
    // Create tracking pixel URL with campaign and recipient IDs if available
    let trackingUrl = `${baseUrl}/api/v1/analytics/track/open`;
    
    if (campaignId && subscriberId) {
      trackingUrl += `?campaignId=${campaignId}&recipientId=${subscriberId}`;
      console.log(`[Email Utils] Creating tracking pixel: ${trackingUrl}`);
    }
    
    const trackingPixel = `<img src="${trackingUrl}" width="1" height="1" alt="" style="display:none;"/>`;
    
    // Check if HTML has a closing body tag
    if (htmlContent.includes('</body>')) {
      // Insert tracking pixel before closing body tag
      return htmlContent.replace('</body>', `${trackingPixel}</body>`);
    } else {
      // Append tracking pixel to the end
      return `${htmlContent}${trackingPixel}`;
    }
  } catch (error) {
    console.error('[Email Utils] Error adding tracking pixel:', error);
    // Return original content if there's an error
    return htmlContent;
  }
};

/**
 * Replace links in HTML content with trackable links
 * @param {string} htmlContent Original HTML content
 * @param {string} campaignId Campaign ID
 * @param {string} subscriberId Subscriber ID
 * @param {string} baseUrl Base URL for tracking
 * @returns {string} HTML content with trackable links
 */
export const replaceLinksWithTrackableLinks = (
  htmlContent: string,
  campaignId: string,
  subscriberId: string,
  baseUrl = process.env.API_BASE_URL || 'http://localhost:3000'
): string => {
  try {
    // Regular expression to find links in HTML
    const linkRegex = /<a\s+(?:[^>]*?\s+)?href=(["'])(.*?)\1/gi;
    
    // Replace each link with a trackable link
    return htmlContent.replace(linkRegex, (match, quote, url) => {
      // Skip tracking for unsubscribe links and anchor links
      if (url.includes('unsubscribe') || url.startsWith('#') || url.startsWith('tel:') || url.startsWith('mailto:')) {
        return match; // Return the original matched anchor tag
      }
      
      // Create trackable URL
      const encodedOriginalUrl = encodeURIComponent(url);
      const trackableUrl = `${baseUrl}/api/v1/analytics/track/click?campaignId=${campaignId}&recipientId=${subscriberId}&url=${encodedOriginalUrl}`;
      console.log(`[Email Utils] Creating trackable link for original URL: ${url}`);
      
      // Replace *only* the href value within the original matched tag
      const updatedMatch = match.replace(url, trackableUrl);
      return updatedMatch;
    });
  } catch (error) {
    console.error('[Email Utils] Error replacing links with trackable links:', error);
    // Return original content if there's an error
    return htmlContent;
  }
};

export default {
  addUnsubscribeLink,
  addTrackingPixel,
  replaceLinksWithTrackableLinks
}; 