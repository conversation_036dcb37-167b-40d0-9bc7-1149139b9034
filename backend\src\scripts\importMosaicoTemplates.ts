import dotenv from 'dotenv';
import fs from 'fs';
import mongoose from 'mongoose';

import Template from '../models/template.model';

// Load environment variables
dotenv.config();

/**
 * Script to import Mosaico-friendly templates
 * @param {string} templateFilePath - Path to the JSON file containing Mosaico templates
 */
const importMosaicoTemplates = async (templateFilePath: string) => {
  try {
    // Check if the template file exists
    if (!fs.existsSync(templateFilePath)) {
      console.error(`❌ Template file not found: ${templateFilePath}`);
      process.exit(1);
    }

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/driftly');
    console.log('Connected to MongoDB');

    // Read the template JSON file
    const templates = JSON.parse(fs.readFileSync(templateFilePath, 'utf8'));
    console.log(`Found ${templates.length} templates in the file`);

    // Process each template and convert it to the format expected by our model
    const templatesForDB = templates.map((template: any) => ({
      name: template.name,
      description: template.description || 'Mosaico template',
      content: template.html || '',
      category: template.category || 'Other',
      thumbnail: template.thumbnail || '',
      isSystem: true,
      mosaicoTemplate: true, // Flag to identify this as a Mosaico template
      mosaicoJson: template.mosaicoJson || {}
    }));

    // Insert templates into the database
    const result = await Template.insertMany(templatesForDB);
    
    console.log(`✅ Successfully imported ${result.length} Mosaico templates`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ Error importing Mosaico templates:', error);
    if (mongoose.connection.readyState) {
      await mongoose.disconnect();
    }
    process.exit(1);
  }
};

// Simple CLI handling
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length < 1) {
    console.error('❌ Please provide the path to the template JSON file');
    console.log('Usage: ts-node importMosaicoTemplates.ts <template-file.json>');
    process.exit(1);
  }

  const templateFilePath = args[0];
  importMosaicoTemplates(templateFilePath);
}

export default importMosaicoTemplates; 