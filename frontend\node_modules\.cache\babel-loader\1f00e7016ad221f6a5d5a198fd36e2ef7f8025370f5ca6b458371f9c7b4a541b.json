{"ast": null, "code": "// cheap lodash replacements\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */export function get(obj, path, defaultValue) {\n  return path.split('.').reduce((a, c) => a && a[c] ? a[c] : defaultValue || null, obj);\n}\n/**\n * drop-in replacement for _.without\n */\nexport function without(items, item) {\n  return items.filter(i => i !== item);\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isString(input) {\n  return typeof input === 'string';\n}\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isObject(input) {\n  return typeof input === 'object';\n}\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */\nexport function xor(itemsA, itemsB) {\n  const map = new Map();\n  const insertItem = item => {\n    map.set(item, map.has(item) ? map.get(item) + 1 : 1);\n  };\n  itemsA.forEach(insertItem);\n  itemsB.forEach(insertItem);\n  const result = [];\n  map.forEach((count, key) => {\n    if (count === 1) {\n      result.push(key);\n    }\n  });\n  return result;\n}\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */\nexport function intersection(itemsA, itemsB) {\n  return itemsA.filter(t => itemsB.indexOf(t) > -1);\n}", "map": {"version": 3, "names": ["get", "obj", "path", "defaultValue", "split", "reduce", "a", "c", "without", "items", "item", "filter", "i", "isString", "input", "isObject", "xor", "itemsA", "itemsB", "map", "Map", "insertItem", "set", "has", "for<PERSON>ach", "result", "count", "key", "push", "intersection", "t", "indexOf"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\dnd-core\\src\\utils\\js_utils.ts"], "sourcesContent": ["// cheap lodash replacements\n\n/**\n * drop-in replacement for _.get\n * @param obj\n * @param path\n * @param defaultValue\n */\nexport function get<T>(obj: any, path: string, defaultValue: T): T {\n\treturn path\n\t\t.split('.')\n\t\t.reduce((a, c) => (a && a[c] ? a[c] : defaultValue || null), obj) as T\n}\n\n/**\n * drop-in replacement for _.without\n */\nexport function without<T>(items: T[], item: T): T[] {\n\treturn items.filter((i) => i !== item)\n}\n\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isString(input: any): boolean {\n\treturn typeof input === 'string'\n}\n\n/**\n * drop-in replacement for _.isString\n * @param input\n */\nexport function isObject(input: any): boolean {\n\treturn typeof input === 'object'\n}\n\n/**\n * replacement for _.xor\n * @param itemsA\n * @param itemsB\n */\nexport function xor<T extends string | number>(itemsA: T[], itemsB: T[]): T[] {\n\tconst map = new Map<T, number>()\n\tconst insertItem = (item: T) => {\n\t\tmap.set(item, map.has(item) ? (map.get(item) as number) + 1 : 1)\n\t}\n\titemsA.forEach(insertItem)\n\titemsB.forEach(insertItem)\n\n\tconst result: T[] = []\n\tmap.forEach((count, key) => {\n\t\tif (count === 1) {\n\t\t\tresult.push(key)\n\t\t}\n\t})\n\treturn result\n}\n\n/**\n * replacement for _.intersection\n * @param itemsA\n * @param itemsB\n */\nexport function intersection<T>(itemsA: T[], itemsB: T[]): T[] {\n\treturn itemsA.filter((t) => itemsB.indexOf(t) > -1)\n}\n"], "mappings": "AAAA;AAEA;;;;;GAMA,OAAO,SAASA,GAAGA,CAAIC,GAAQ,EAAEC,IAAY,EAAEC,YAAe,EAAK;EAClE,OAAOD,IAAI,CACTE,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,IAAIA,CAAC,CAACC,CAAC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAAC,GAAGJ,YAAY,IAAI,IAAI,EAAGF,GAAG,CAAC;;AAGnE;;;AAGA,OAAO,SAASO,OAAOA,CAAIC,KAAU,EAAEC,IAAO,EAAO;EACpD,OAAOD,KAAK,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKF,IAAI,CAAC;;AAGvC;;;;AAIA,OAAO,SAASG,QAAQA,CAACC,KAAU,EAAW;EAC7C,OAAO,OAAOA,KAAK,KAAK,QAAQ;;AAGjC;;;;AAIA,OAAO,SAASC,QAAQA,CAACD,KAAU,EAAW;EAC7C,OAAO,OAAOA,KAAK,KAAK,QAAQ;;AAGjC;;;;;AAKA,OAAO,SAASE,GAAGA,CAA4BC,MAAW,EAAEC,MAAW,EAAO;EAC7E,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAa;EAChC,MAAMC,UAAU,GAAIX,IAAO,IAAK;IAC/BS,GAAG,CAACG,GAAG,CAACZ,IAAI,EAAES,GAAG,CAACI,GAAG,CAACb,IAAI,CAAC,GAAGS,GAAI,CAACnB,GAAG,CAACU,IAAI,CAAC,GAAc,CAAC,GAAG,CAAC,CAAC;GAChE;EACDO,MAAM,CAACO,OAAO,CAACH,UAAU,CAAC;EAC1BH,MAAM,CAACM,OAAO,CAACH,UAAU,CAAC;EAE1B,MAAMI,MAAM,GAAQ,EAAE;EACtBN,GAAG,CAACK,OAAO,CAAC,CAACE,KAAK,EAAEC,GAAG,KAAK;IAC3B,IAAID,KAAK,KAAK,CAAC,EAAE;MAChBD,MAAM,CAACG,IAAI,CAACD,GAAG,CAAC;;GAEjB,CAAC;EACF,OAAOF,MAAM;;AAGd;;;;;AAKA,OAAO,SAASI,YAAYA,CAAIZ,MAAW,EAAEC,MAAW,EAAO;EAC9D,OAAOD,MAAM,CAACN,MAAM,CAAEmB,CAAC,IAAKZ,MAAM,CAACa,OAAO,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}