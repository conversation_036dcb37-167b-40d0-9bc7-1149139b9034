{"version": 3, "file": "static/js/379.7ef2defc.chunk.js", "mappings": "8KA0BA,MAAMA,GAAaC,EAAAA,EAAAA,aACjB,CAAAC,EAAkEC,KAAS,IAA1E,YAAEC,EAAc,GAAE,YAAEC,EAAc,GAAE,OAAEC,EAAM,OAAEC,EAAS,QAAQL,EAC9D,MAAMM,GAAYC,EAAAA,EAAAA,QAAuB,MACnCC,GAAeD,EAAAA,EAAAA,QAAsB,MAkT3C,OA/SAE,EAAAA,EAAAA,YAAU,KACR,GAAKH,EAAUI,QAAf,CAGIF,EAAaE,UACfC,QAAQC,IAAI,qDACZJ,EAAaE,QAAQG,UACrBL,EAAaE,QAAU,MAGzBC,QAAQC,IAAI,+CAAgD,CAC1DE,UAAWZ,EACXa,YAAuB,OAAXb,QAAW,IAAXA,OAAW,EAAXA,EAAac,SAAU,EACnCC,UAAWd,EACXe,YAAuB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAaa,SAAU,IAGrC,IACE,MAAMG,EAASC,EAAAA,GAASC,KAAK,CAC3BC,UAAWhB,EAAUI,QACrBa,aAAa,EACblB,OAAQmB,OAAOnB,GACfoB,MAAO,OACPC,gBAAgB,EAChBC,QAAS,CAACC,KACVC,YAAa,CACX,gBAAiB,CAGdC,cAAc,EACdC,aAAa,MAQpB,IAAKZ,EAEH,YADAR,QAAQqB,MAAM,4CAoFhB,IAAIC,EAhFJzB,EAAaE,QAAUS,EAGlBA,EAAOe,SAASC,IAAI,mBACvBxB,QAAQC,IAAI,0DACZO,EAAOe,SAASE,IAAI,gBAAiB,CACnCC,IAAMlB,IACJ,MAAMmB,EAAOnB,EAAOoB,UAEpB,MAAO,CACLD,KAAMA,EACNE,KAAMF,EACP,KAKFnB,EAAOe,SAASC,IAAI,kBACvBxB,QAAQC,IAAI,yDACZO,EAAOe,SAASE,IAAI,eAAgB,CAClCC,IAAMlB,GACGA,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,kBAMhDC,YAAW,KACT,GAAKnC,EAAaE,QAMlB,GAAKF,EAAaE,QAAQkC,cAK1B,IAEE,GAAI1C,EAAa,CACfS,QAAQC,IAAI,qCAAsCV,EAAY2C,UAAU,EAAG,KAAO,OAClF,IACErC,EAAaE,QAAQkC,cAAc1C,GACnCS,QAAQC,IAAI,gDACd,CAAE,MAAOkC,GACPnC,QAAQqB,MAAM,2CAA4Cc,GAEtD3C,IACFQ,QAAQC,IAAI,qDACZJ,EAAaE,QAAQkC,cAAczC,GACnCQ,QAAQC,IAAI,qDAEhB,CACF,MAAWT,GACTQ,QAAQC,IAAI,yDAA0DT,EAAY0C,UAAU,EAAG,KAAO,OACtGrC,EAAaE,QAAQkC,cAAczC,GACnCQ,QAAQC,IAAI,mDAGZD,QAAQC,IAAI,8DACZJ,EAAaE,QAAQkC,cAAc,oVAYvC,CAAE,MAAOZ,GACPrB,QAAQqB,MAAM,+CAAgDA,EAChE,MAzCErB,QAAQqB,MAAM,oEANdrB,QAAQqB,MAAM,2DA+ChB,GACC,KAIH,IAAIe,GAAW,EAmEf,OAhEA5B,EAAO6B,GAAG,uBAAuB,KAC3B5C,GAAUe,IAAW4B,IAEnBd,GAAagB,aAAahB,GAG9BA,EAAcU,YAAW,KACvB,IACEI,GAAW,EAEX,IAAIG,EAAY,GACZC,EAAY,GAEhB,IAEE,MAAMC,EAAWjC,EAAOkC,WAAW,iBAC/BD,GAAgC,kBAAbA,IACrBF,EAAYE,EAASd,MAAQ,GAC7Ba,EAAYC,EAASZ,MAAQ,GAC7B7B,QAAQC,IAAI,qDAEhB,CAAE,MAAO0C,GACN3C,QAAQ4C,KAAK,0DAA2DD,EAC3E,CAGA,IAAKJ,IAAcC,EAAW,CAC1BD,EAAY/B,EAAOoB,WAAa,GAChC,IAEEY,EAAYhC,EAAOkC,WAAW,iBAAmB,EACnD,CAAE,MAAMG,GACL7C,QAAQ4C,KAAK,iCAAkCC,EAClD,CAEKL,IACHA,EAAYhC,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,IAEpE/B,QAAQC,IAAI,uDAChB,CAGA,IAAKsC,EAAUO,OAGb,OAFA9C,QAAQC,IAAI,4DACZmC,GAAW,GAIbpC,QAAQC,IAAI,6CAGZR,EAAO8C,EAAWC,GAClBxC,QAAQC,IAAI,yCAEf,CAAE,MAAOoB,GACLrB,QAAQqB,MAAM,uCAAwCA,EAC1D,CAAC,QACGe,GAAW,CACf,IACE,KACL,IAIK,KAEL,GADId,GAAagB,aAAahB,GAC1BzB,EAAaE,QAAS,CAEvB,IACEF,EAAaE,QAAQG,SACvB,CAAE,MAAO6C,GACP/C,QAAQqB,MAAM,4CAA6C0B,EAC7D,CACAlD,EAAaE,QAAU,IAC1B,EAEJ,CAAE,MAAOiD,GACPhD,QAAQqB,MAAM,4DAA6D2B,EAC7E,CA5M8B,CA4M9B,GACC,CAACzD,EAAaC,EAAaE,EAAQD,KAGtCwD,EAAAA,EAAAA,qBAAoB3D,GAAK,MACvB4D,KAAMC,UACJ,IAAIC,EAAgB,CAAEzB,KAAM,GAAIE,KAAM,IACtC,GAAIhC,EAAaE,QACd,IAEI,MAAMS,EAASX,EAAaE,QAC5B,IAAIS,EAAOe,SAASC,IAAI,iBAetB,MAAM,IAAI6B,MAAM,uCAfwB,CAExC,MAAMC,EAAS9C,EAAOkC,WAAW,iBAEjC,KAAIY,GAA4B,kBAAXA,GAAuB,SAAUA,GAAU,SAAUA,GAOvE,MAFAtD,QAAQ4C,KAAK,6EAEP,IAAIS,MAAM,yCANjBD,EAAczB,KAAO2B,EAAO3B,MAAQ,GACpCyB,EAAcvB,KAAOyB,EAAOzB,MAAQ,GACpC7B,QAAQC,IAAI,qDAAsD,CAAE0B,KAAMyB,EAAczB,KAAKO,UAAU,EAAE,IAAI,MAAOL,KAAMuB,EAAcvB,KAAKK,UAAU,EAAE,IAAI,OAMjK,CAIJ,CAAE,MAAOS,GACL3C,QAAQ4C,KAAK,+DAAgED,GAC7E,IAEE,MAAMnC,EAASX,EAAaE,QACtBwD,EAAU/C,EAAOoB,WAAa,GACpC,IAAI4B,EAAgB,GAIlBA,EADEhD,EAAOe,SAASC,IAAI,gBACNhB,EAAOkC,WAAW,iBAAmB,GAGrClC,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,GAGpEwB,GAAWC,GACXJ,EAAczB,KAAO4B,EACrBH,EAAcvB,KAAO2B,GAAiBD,EACtCvD,QAAQC,IAAI,oEAEZD,QAAQqB,MAAM,0EAEpB,CAAE,MAAOoC,GACNzD,QAAQqB,MAAM,8DAA+DoC,EAChF,CACJ,MAEDzD,QAAQqB,MAAM,oDAOhB,SAHM,IAAIqC,SAAQC,GAAW3B,WAAW2B,EAAS,OAG7C9D,EAAaE,UAAYqD,EAAcvB,KAAKiB,OAAQ,CACpD9C,QAAQC,IAAI,8DACZ,IACK,MAAMO,EAASX,EAAaE,QAE5B,IAAI6D,EAAyB,GACzBpD,EAAOe,SAASC,IAAI,kBACtBoC,EAAyBpD,EAAOkC,WAAW,iBAGxCkB,IACHA,EAAyBpD,EAAOoB,QAAQ,CAAEE,UAAWtB,EAAOuB,gBAAmB,IAG7E6B,EAAuBd,QACvB9C,QAAQC,IAAI,8DACZmD,EAAcvB,KAAO+B,IAEtB5D,QAAQC,IAAI,4DAERmD,EAAczB,OAASyB,EAAcvB,OACvCuB,EAAcvB,KAAOuB,EAAczB,MAG7C,CAAE,MAAOkC,GACL7D,QAAQqB,MAAM,iEAAkEwC,EACpF,CACJ,CAGA,OAAOT,CAAa,EAErBU,UAAWA,IAAMjE,EAAaE,aAG1BgE,EAAAA,EAAAA,KAAA,OAAKzE,IAAKK,EAAWqE,MAAO,CAAEtE,OAAQA,IAAY,IAK7DP,EAAW8E,YAAc,aAEzB,S,qJCpUA,MAoWA,EApW+BC,KAC7B,MAAMC,GAAWC,EAAAA,EAAAA,OACX,WAAEC,IAAeC,EAAAA,EAAAA,KACjBC,EAAYC,QAAQH,IACnBI,EAAMC,IAAWC,EAAAA,EAAAA,UAAiB,KAClCC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAiB,KAChDG,EAAUC,IAAeJ,EAAAA,EAAAA,UAAiB,UAC1CK,EAASC,IAAcN,EAAAA,EAAAA,UAAiB,KACxCO,EAAaC,IAAkBR,EAAAA,EAAAA,UAAiB,KAChDS,EAAWC,IAAgBV,EAAAA,EAAAA,UAAiB,KAC5CW,EAASC,IAAcZ,EAAAA,EAAAA,WAAkB,IACzCtD,EAAOmE,IAAYb,EAAAA,EAAAA,UAAwB,OAC3Cc,EAASC,IAAcf,EAAAA,EAAAA,UAAwB,OAC/CgB,EAAMC,IAAWjB,EAAAA,EAAAA,UAAiB,IAGlCkB,EAAmBC,IAAwBnB,EAAAA,EAAAA,WAAkB,IAC7DoB,EAAgBC,IAAqBrB,EAAAA,EAAAA,UAAsC,aAC3EsB,EAAaC,IAAkBvB,EAAAA,EAAAA,UAAmB,KAClDwB,EAAoBC,IAAyBzB,EAAAA,EAAAA,WAAkB,IAEtE7E,EAAAA,EAAAA,YAAU,KACJyE,GAAaF,GACfgC,EAAchC,EAChB,GACC,CAACA,IAEJ,MAAMgC,EAAgBlD,UACpBoC,GAAW,GACX,IAEE,MAAMe,QAAiBC,EAAAA,GAA8BC,gBAAgBnC,GACrE,GAAIiC,EAASb,SAAWa,EAASG,SAAU,CAEzC,MAAMA,EAAWH,EAASG,SAC1B/B,EAAQ+B,EAAShC,MACjBI,EAAe4B,EAAS7B,aACxBG,EAAY0B,EAAS3B,UACrBG,EAAWwB,EAASzB,SACpBK,EAAaoB,EAASrB,WAAa,IAG/BqB,EAASvB,aACXC,EAAesB,EAASvB,YAE5B,MACEM,EAASc,EAASI,SAAW,2BAEjC,CAAE,MAAOC,GAAW,IAADC,EAAAC,EACjBrB,GAAqB,QAAZoB,EAAAD,EAAIL,gBAAQ,IAAAM,GAAM,QAANC,EAAZD,EAAcE,YAAI,IAAAD,OAAN,EAAZA,EAAoBH,UAAWC,EAAID,SAAW,2BACzD,CAAC,QACCnB,GAAW,EACb,GASIwB,EAAe5D,UAGnB,GAFC,OAADhB,QAAC,IAADA,GAAAA,EAAG6E,iBAEU,IAATrB,EAEF,OAAKlB,GAASG,GAAgBE,GAMzBM,GACHC,EAAa,0DAA0D4B,mBAAmBxC,WAG5FmB,EAAQ,SATNJ,EAAS,sCAkBb,GAJAxF,QAAQC,IAAI,+BAIP+E,EAAL,CAKAO,GAAW,GACXC,EAAS,MACTxF,QAAQC,IAAI,yCAEZ,IACE,MAAMiH,EAAe,CACnBzC,OACAG,cACAE,WACAE,UACAI,UAAWA,GAAa,0DAA0D6B,mBAAmBxC,KACrGS,YAAaA,GAKf,IAAIoB,EAFJtG,QAAQC,IAAI,sCAAuCkH,KAAKC,UAAUF,GAAc7G,OAAQ,SAIpFkE,GAAaF,GACfrE,QAAQC,IAAI,wCAAyCoE,GACrDiC,QAAiBC,EAAAA,GAA8Bc,eAAehD,EAAY6C,KAE1ElH,QAAQC,IAAI,yCACZqG,QAAiBC,EAAAA,GAA8Be,eAAeJ,IAGhElH,QAAQC,IAAI,gCAAiCqG,GACzCA,EAASb,SACXzF,QAAQC,IAAI,4DACZyF,EAAWnB,EAAY,gCAAkC,iCAGzDvC,YAAW,KACThC,QAAQC,IAAI,gDACZsH,OAAOC,SAASC,KAAO,kBAAkB,GACxC,QAEHzH,QAAQqB,MAAM,2CAA4CiF,EAASI,SACnElB,EAASc,EAASI,UAAYnC,EAAY,4BAA8B,8BAE5E,CAAE,MAAOoC,GAIP,GAHA3G,QAAQqB,MAAM,yCAA0CsF,GAGpDA,EAAIL,SACN,GAA4B,MAAxBK,EAAIL,SAASoB,OACflC,EAAS,mEACJ,GAA4B,MAAxBmB,EAAIL,SAASoB,OACtBlC,EAAS,oEACJ,GAA4B,MAAxBmB,EAAIL,SAASoB,OAAgB,CAAC,IAADC,EACtCnC,GAA0B,QAAjBmC,EAAAhB,EAAIL,SAASQ,YAAI,IAAAa,OAAA,EAAjBA,EAAmBjB,UAAW,mDACzC,MAAO,GAAIC,EAAIL,SAASoB,QAAU,IAChClC,EAAS,gEACJ,CAAC,IAADoC,EACLpC,GAA0B,QAAjBoC,EAAAjB,EAAIL,SAASQ,YAAI,IAAAc,OAAA,EAAjBA,EAAmBlB,UAAWC,EAAID,SAAW,+CACxD,MACSC,EAAIkB,QACbrC,EAAS,0EAETA,EAASmB,EAAID,SAAW,+BAE5B,CAAC,QACC1G,QAAQC,IAAI,6CACZsF,GAAW,EACb,CAlEA,MAFEC,EAAS,gDAoEX,EAYIsC,EAAiB3E,MAAO6B,EAAiB+C,KAC7C,IACE3B,GAAsB,GACtBF,EAAe,IAEf,MAAMI,QAAiB0B,EAAAA,EAAIC,KAAK,wBAC9B,CAAEjD,UAASe,eAAgBgC,GAC3B,CACEG,QAAS,CACP,cAAiB,UAAUC,aAAaC,QAAQ,cAKlD9B,EAASQ,KAAKrB,QAChBS,EAAeI,EAASQ,KAAKA,KAAKb,aAElCjG,QAAQqB,MAAM,6BAA8BiF,EAASQ,KAAKJ,QAE9D,CAAE,MAAOrF,GACPrB,QAAQqB,MAAM,6BAA8BA,EAC9C,CAAC,QACC+E,GAAsB,EACxB,GAGF,OAAId,GAAoB,IAATK,GAEX5B,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,wCAAuCC,UACpDvE,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,iFAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EAErCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wFAAuFC,SAAA,EACpGvE,EAAAA,EAAAA,KAAA,MAAIsE,UAAU,wBAAuBC,SAClC/D,EAAY,YAAYE,IAAS,yBAEpC8D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACxBvE,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CACJT,KAAK,SACLU,QArDOC,KACJ,IAAT/C,EACFC,EAAQ,GAERzB,EAAS,mBACX,EAiDUwE,QAAQ,YACRC,KAAK,KAAIN,SAEC,IAAT3C,EAAa,oBAAsB,qBAEtC5B,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CACLT,KAAK,SACLU,QAAS1B,EACT4B,QAAQ,UACRC,KAAK,KACLC,SAAUvD,EAAQgD,SAEjBhD,EAAU,YAAwB,IAATK,EAAa,eAAkBpB,EAAY,iBAAmB,wBAMhGR,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,iCAAgCC,SACnC,IAAT3C,GAEC5B,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,4CAA2CC,UACxDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+CAA8CC,SAAA,CAC1DjH,IAAS0C,EAAAA,EAAAA,KAAC+E,EAAAA,EAAK,CAACf,KAAK,QAAQrB,QAASrF,EAAOgH,UAAU,SACvD5C,IAAW1B,EAAAA,EAAAA,KAAC+E,EAAAA,EAAK,CAACf,KAAK,UAAUrB,QAASjB,EAAS4C,UAAU,UAC9DE,EAAAA,EAAAA,MAAA,QAAMQ,SAAUhC,EAAauB,SAAA,EAC1BvE,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,OAAMC,UACnBvE,EAAAA,EAAAA,KAACiF,EAAAA,EAAK,CAACC,GAAG,eAAexE,KAAK,eAAeyE,MAAM,gBAAgBnB,KAAK,OAAOoB,MAAO1E,EAAM2E,SAAWjH,GAA2CuC,EAAQvC,EAAEkH,OAAOF,OAAQG,UAAQ,EAACC,YAAY,2BAElMhB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBvE,EAAAA,EAAAA,KAAA,SAAOsE,UAAU,wBAAuBC,SAAC,iBACzCvE,EAAAA,EAAAA,KAAA,YAAUoF,MAAOvE,EAAawE,SAAWjH,GAAM0C,EAAe1C,EAAEkH,OAAOF,OAAQG,UAAQ,EAACC,YAAY,6BAA6BlB,UAAU,gIAE7IE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBvE,EAAAA,EAAAA,KAAA,SAAOsE,UAAU,wBAAuBC,SAAC,cACzCC,EAAAA,EAAAA,MAAA,UAAQY,MAAOrE,EAAUsE,SAAWjH,GAAM4C,EAAY5C,EAAEkH,OAAOF,OAAQd,UAAU,yGAAwGC,SAAA,EAEvLvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,YAAWb,SAAC,eAC1BvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,gBAAeb,SAAC,mBAC9BvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,aAAYb,SAAC,gBAC3BvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,QAAOb,SAAC,WACtBvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,aAAYb,SAAC,gBAC3BvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,YAAWb,SAAC,eAC1BvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,cAAab,SAAC,iBAC5BvE,EAAAA,EAAAA,KAAA,UAAQoF,MAAM,QAAOb,SAAC,iBAG1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBvE,EAAAA,EAAAA,KAACiF,EAAAA,EAAK,CAACC,GAAG,eAAexE,KAAK,eAAeyE,MAAM,2BAA2BnB,KAAK,OAAOoB,MAAO/D,EAAWgE,SAAWjH,GAA2CkD,EAAalD,EAAEkH,OAAOF,OAAQI,YAAY,uCAC3MnE,IAAarB,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,OAAMC,UAACvE,EAAAA,EAAAA,KAAA,OAAKyF,IAAKpE,EAAWqE,IAAI,oBAAoBpB,UAAU,uCAE9FtE,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,mBAAkBC,UAC9BvE,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CAACT,KAAK,SAASY,QAAQ,UAAUC,KAAK,KAAIN,SAAC,qCAO3DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0EAAyEC,SAAA,EACtFvE,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,oEAAmEC,SAAC,sBACnFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEvE,EAAAA,EAAAA,KAAA,MAAIsE,UAAU,yCAAwCC,SAAC,oBACvDvE,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CACLG,QAAQ,YACRC,KAAK,KACLP,UAAU,SACVI,QAASA,KACPzC,EAAkB,YAClBF,GAAqB,GACrBgC,EAAe,gCAAiC,WAAW,EAC3DQ,SACH,wBAGDvE,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CACLG,QAAQ,YACRC,KAAK,KACLP,UAAU,cACVI,QAASA,KACPzC,EAAkB,QAClBF,GAAqB,GACrBgC,EAAe,iCAAkC,OAAO,EACxDQ,SACH,yBAGDvE,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CACLG,QAAQ,YACRC,KAAK,KACLP,UAAU,cACVI,QAASA,KACPzC,EAAkB,OAClBF,GAAqB,GACrBgC,EAAe,4BAA6B,MAAM,EAClDQ,SACH,sBAIHC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEvE,EAAAA,EAAAA,KAAA,MAAIsE,UAAU,yCAAwCC,SAAC,UACvDvE,EAAAA,EAAAA,KAAA,KAAGsE,UAAU,wBAAuBC,SAAC,gIAM3CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sCAAqCC,SAAA,EAClDvE,EAAAA,EAAAA,KAAC5E,EAAAA,EAAU,CACTK,YAAawF,EACbzF,YAAa2F,EACbzF,OA9QSiK,CAAC/H,EAAcE,KACpCoD,EAAWpD,GACXsD,EAAexD,EAAK,EA6QRjC,OAAO,SAIRmG,IACC9B,EAAAA,EAAAA,KAAA,OAAKsE,UAAU,qCAAoCC,UACjDvE,EAAAA,EAAAA,KAAC4F,EAAAA,EAAiB,CAChB5B,KAAMhC,EACNE,YAAaA,EACb2D,UAAWzD,EACX0D,QAAUC,IAGR9J,QAAQC,IAAI,uBAAwB6J,GACpCC,MAAM,iFACNjE,GAAqB,EAAM,EAE7BkE,QAASA,IAAMlE,GAAqB,iBAQ9C,C", "sources": ["components/MjmlEditor.tsx", "pages/templates/TemplateForm.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n// @ts-ignore - grapesjs-mjml lacks official types\r\nimport grapesjsMjml from 'grapesjs-mjml';\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface MjmlEditorRef {\r\n  save: () => Promise<{ mjml: string; html: string }>;\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface MjmlEditorProps {\r\n  initialMjml?: string;\r\n  initialHtml?: string; // Added to potentially load HTML if MJML is missing\r\n  onSave?: (mjml: string, html: string) => void;\r\n  height?: string | number;\r\n}\r\n\r\nconst MjmlEditor = forwardRef<MjmlEditorRef, MjmlEditorProps>(\r\n  ({ initialMjml = '', initialHtml = '', onSave, height = '70vh' }, ref) => {\r\n    const editorRef = useRef<HTMLDivElement>(null);\r\n    const grapesEditor = useRef<Editor | null>(null);\r\n\r\n    // Initialize GrapesJS Editor\r\n    useEffect(() => {\r\n      if (!editorRef.current) return; // Check for DOM element\r\n      \r\n      // Always destroy and recreate the editor to ensure consistent behavior\r\n      if (grapesEditor.current) {\r\n        console.log(\"[MjmlEditor] Cleaning up previous editor instance\");\r\n        grapesEditor.current.destroy();\r\n        grapesEditor.current = null;\r\n      }\r\n\r\n      console.log(\"[MjmlEditor] Initializing editor with props:\", {\r\n        hasMjml: !!initialMjml,\r\n        mjmlLength: initialMjml?.length || 0,\r\n        hasHtml: !!initialHtml,\r\n        htmlLength: initialHtml?.length || 0\r\n      });\r\n\r\n      try {\r\n        const editor = grapesjs.init({\r\n          container: editorRef.current,\r\n          fromElement: false, // Don't load from existing HTML/CSS in the container\r\n          height: String(height),\r\n          width: 'auto',\r\n          storageManager: false, // Disable default storage manager\r\n          plugins: [grapesjsMjml],\r\n          pluginsOpts: {\r\n            'grapesjs-mjml': {\r\n              // MJML plugin options (optional)\r\n              // columnsPadding: '0px',\r\n               useXmlParser: true, // Use the faster XML parser\r\n               resetBlocks: false, // Try keeping default GrapesJS blocks\r\n               // ... other options\r\n            }\r\n          },\r\n          // Optional: Configure panels, blocks, styles etc.\r\n        });\r\n\r\n        // Make sure the editor was initialized properly\r\n        if (!editor) {\r\n          console.error(\"[MjmlEditor] Failed to initialize editor\");\r\n          return;\r\n        }\r\n\r\n        grapesEditor.current = editor;\r\n\r\n        // Register missing commands that are expected by the editor\r\n        if (!editor.Commands.has('mjml-get-code')) {\r\n          console.log(\"[MjmlEditor] Registering missing mjml-get-code command\");\r\n          editor.Commands.add('mjml-get-code', {\r\n            run: (editor) => {\r\n              const mjml = editor.getHtml();\r\n              // Simple implementation for missing command\r\n              return { \r\n                mjml: mjml,\r\n                html: mjml // We'll process this later if needed\r\n              };\r\n            }\r\n          });\r\n        }\r\n\r\n        if (!editor.Commands.has('gjs-get-html')) {\r\n          console.log(\"[MjmlEditor] Registering missing gjs-get-html command\");\r\n          editor.Commands.add('gjs-get-html', {\r\n            run: (editor) => {\r\n              return editor.getHtml({ component: editor.getWrapper() });\r\n            }\r\n          });\r\n        }\r\n\r\n        // Use a small timeout to ensure editor is fully initialized\r\n        setTimeout(() => {\r\n          if (!grapesEditor.current) {\r\n            console.error(\"[MjmlEditor] Editor instance not available after timeout\");\r\n            return;\r\n          }\r\n          \r\n          // Verify the editor's components API is available\r\n          if (!grapesEditor.current.setComponents) {\r\n            console.error(\"[MjmlEditor] Editor's setComponents method is not available\");\r\n            return;\r\n          }\r\n          \r\n          try {\r\n            // Load initial content\r\n            if (initialMjml) {\r\n              console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\r\n              try {\r\n                grapesEditor.current.setComponents(initialMjml); // Use setComponents for MJML\r\n                console.log(\"[MjmlEditor] Successfully loaded MJML content\");\r\n              } catch (e) {\r\n                console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\r\n                // Fallback to HTML if MJML fails?\r\n                if (initialHtml) {\r\n                  console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\r\n                  grapesEditor.current.setComponents(initialHtml); // Use setComponents for HTML as well\r\n                  console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\r\n                }\r\n              }\r\n            } else if (initialHtml) {\r\n              console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\r\n              grapesEditor.current.setComponents(initialHtml);\r\n              console.log(\"[MjmlEditor] Successfully loaded HTML content\");\r\n            } else {\r\n              // Load default MJML template if nothing is provided\r\n              console.log(\"[MjmlEditor] No content provided, loading default template\");\r\n              grapesEditor.current.setComponents(`\r\n                <mjml>\r\n                  <mj-body>\r\n                    <mj-section>\r\n                      <mj-column>\r\n                        <mj-text>Start designing your email!</mj-text>\r\n                      </mj-column>\r\n                    </mj-section>\r\n                  </mj-body>\r\n                </mjml>\r\n              `);\r\n            }\r\n          } catch (error) {\r\n            console.error(\"[MjmlEditor] Error in content loading phase:\", error);\r\n          }\r\n        }, 100);\r\n\r\n        // Declare timeout variable in outer scope so it's accessible in cleanup function\r\n        let saveTimeout: NodeJS.Timeout | undefined;\r\n        let isSaving = false; // Flag to prevent multiple simultaneous save operations\r\n        \r\n        // Attach save listener with debounce\r\n        editor.on('change:changesCount', () => {\r\n          if (onSave && editor && !isSaving) { // Only proceed if not already saving\r\n            // Clear existing timeout\r\n            if (saveTimeout) clearTimeout(saveTimeout);\r\n            \r\n            // Set a new timeout\r\n            saveTimeout = setTimeout(() => { // Assign to the outer scope variable\r\n              try {\r\n                isSaving = true; // Set saving flag\r\n                // Simplify code retrieval: Prioritize commands, fallback to getHtml()\r\n                let finalMjml = '';\r\n                let finalHtml = '';\r\n\r\n                try {\r\n                  // Try the specific mjml command first\r\n                  const mjmlCode = editor.runCommand('mjml-get-code');\r\n                  if (mjmlCode && typeof mjmlCode === 'object') { // Command should return object {mjml, html}\r\n                    finalMjml = mjmlCode.mjml || '';\r\n                    finalHtml = mjmlCode.html || '';\r\n                    console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\r\n                  }\r\n                } catch (cmdErr) {\r\n                   console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\r\n                }\r\n\r\n                // If command failed or didn't return expected structure, use fallbacks\r\n                if (!finalMjml && !finalHtml) {\r\n                    finalMjml = editor.getHtml() || ''; // Often gets MJML\r\n                    try {\r\n                      // Try getting HTML via command\r\n                      finalHtml = editor.runCommand('gjs-get-html') || ''; \r\n                    } catch(htmlCmdErr) {\r\n                       console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\r\n                    }\r\n                    // As a last resort for HTML, maybe just use the component HTML (less reliable)\r\n                    if (!finalHtml) {\r\n                      finalHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                    }\r\n                    console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\r\n                }\r\n                \r\n                // Don't save if we have no content, prevents potential refresh cycles\r\n                if (!finalMjml.trim()) {\r\n                  console.log(\"[MjmlEditor] No MJML content to save, skipping save\");\r\n                  isSaving = false;\r\n                  return;\r\n                }\r\n                \r\n                console.log(\"[MjmlEditor] Attempting to call onSave...\");\r\n                // Call onSave as long as the editor instance exists and the prop was passed\r\n                // Even if mjml/html strings are empty, let the parent decide what to do\r\n                onSave(finalMjml, finalHtml);\r\n                console.log(\"[MjmlEditor] onSave callback executed.\");\r\n\r\n             } catch (error) {\r\n                 console.error(\"Error during editor change listener:\", error);\r\n             } finally {\r\n                 isSaving = false; // Reset flag whether save succeeded or failed\r\n             }\r\n            }, 500); // 500ms debounce\r\n          }\r\n        });\r\n\r\n        // Return cleanup function\r\n        return () => {\r\n          if (saveTimeout) clearTimeout(saveTimeout); // Now accessible here\r\n          if (grapesEditor.current) {\r\n             // Clean up panels, commands, etc. specific to this instance if necessary\r\n             try {\r\n               grapesEditor.current.destroy();\r\n             } catch (destroyError) {\r\n               console.error(\"[MjmlEditor] Error during editor cleanup:\", destroyError);\r\n             }\r\n             grapesEditor.current = null;\r\n          }\r\n        };\r\n      } catch (initError) {\r\n        console.error(\"[MjmlEditor] Critical error during editor initialization:\", initError);\r\n      }\r\n    }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\r\n\r\n    // Expose save method via ref\r\n    useImperativeHandle(ref, () => ({\r\n      save: async () => {\r\n        let generatedCode = { mjml: '', html: '' }; // Initialize with empty strings\r\n        if (grapesEditor.current) {\r\n           try {\r\n               // Check if command exists first to avoid warnings\r\n               const editor = grapesEditor.current;\r\n               if (editor.Commands.has('mjml-get-code')) {\r\n                 // Try the primary command\r\n                 const result = editor.runCommand('mjml-get-code');\r\n                 // Check if the command returned the expected object structure\r\n                 if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\r\n                   generatedCode.mjml = result.mjml || '';\r\n                   generatedCode.html = result.html || '';\r\n                   console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", { mjml: generatedCode.mjml.substring(0,50)+'...', html: generatedCode.html.substring(0,50)+'...' });\r\n                 } else {\r\n                    console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\r\n                    // Throw an error to trigger the catch block for fallback logic\r\n                    throw new Error(\"Command returned unexpected structure\"); \r\n                 }\r\n               } else {\r\n                 // Command doesn't exist, go straight to fallback\r\n                 throw new Error(\"mjml-get-code command not available\");\r\n               }\r\n           } catch (cmdErr) {\r\n               console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\r\n               try {\r\n                 // Fallback attempts\r\n                 const editor = grapesEditor.current;\r\n                 const rawMjml = editor.getHtml() || '';\r\n                 let generatedHtml = '';\r\n                 \r\n                 // Try gjs-get-html only if it exists\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   generatedHtml = editor.runCommand('gjs-get-html') || '';\r\n                 } else {\r\n                   // Direct fallback to component HTML\r\n                   generatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                  \r\n                 if (rawMjml || generatedHtml) { // Use if *either* fallback worked\r\n                     generatedCode.mjml = rawMjml;\r\n                     generatedCode.html = generatedHtml || rawMjml; // Use MJML as HTML if no HTML generated\r\n                     console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\r\n                 } else {\r\n                     console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\r\n                 }\r\n               } catch (fallbackErr) {\r\n                  console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\r\n               }\r\n           }\r\n        } else {\r\n          console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\r\n        }\r\n\r\n        // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\r\n        await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\r\n        \r\n        // Re-fetch the HTML specifically after the delay, as it might have updated\r\n        if (grapesEditor.current && !generatedCode.html.trim()) { // Only re-fetch if HTML was initially empty\r\n            console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\r\n            try {\r\n                 const editor = grapesEditor.current;\r\n                 \r\n                 let potentiallyUpdatedHtml = '';\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   potentiallyUpdatedHtml = editor.runCommand('gjs-get-html');\r\n                 }\r\n                 \r\n                 if (!potentiallyUpdatedHtml) {\r\n                   potentiallyUpdatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                 \r\n                 if (potentiallyUpdatedHtml.trim()) {\r\n                     console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\r\n                     generatedCode.html = potentiallyUpdatedHtml;\r\n                 } else {\r\n                    console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\r\n                    // If still no HTML but we have MJML, use that\r\n                    if (generatedCode.mjml && !generatedCode.html) {\r\n                      generatedCode.html = generatedCode.mjml;\r\n                    }\r\n                 }\r\n            } catch (refetchErr) {\r\n                console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\r\n            }\r\n        }\r\n\r\n        // ALWAYS return the potentially updated generatedCode object\r\n        return generatedCode; \r\n      },\r\n       getEditor: () => grapesEditor.current,\r\n    }));\r\n\r\n    return <div ref={editorRef} style={{ height: height }} />;\r\n  }\r\n);\r\n\r\n// Assign display name for debugging\r\nMjmlEditor.displayName = 'MjmlEditor';\r\n\r\nexport default MjmlEditor;", "import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport AISuggestionPanel from 'components/AISuggestionPanel';\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Input from 'components/Input';\nimport MjmlEditor from 'components/MjmlEditor';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\n// Import services\nimport { templateRecommendationService } from 'services';\nimport api from 'services/api';\n\nconst TemplateForm: React.FC = () => {\n  const navigate = useNavigate();\n  const { templateId } = useParams<{ templateId: string }>();\n  const isEditing = Boolean(templateId);\n  const [name, setName] = useState<string>('');\n  const [description, setDescription] = useState<string>('');\n  const [category, setCategory] = useState<string>('Other');\n  const [content, setContent] = useState<string>('');\n  const [mjmlContent, setMjmlContent] = useState<string>('');\n  const [thumbnail, setThumbnail] = useState<string>('');\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [step, setStep] = useState<number>(1);\n\n  // AI suggestion states\n  const [showAiSuggestions, setShowAiSuggestions] = useState<boolean>(false);\n  const [suggestionType, setSuggestionType] = useState<'headline' | 'body' | 'cta'>('headline');\n  const [suggestions, setSuggestions] = useState<string[]>([]);\n  const [loadingSuggestions, setLoadingSuggestions] = useState<boolean>(false);\n\n  useEffect(() => {\n    if (isEditing && templateId) {\n      fetchTemplate(templateId);\n    }\n  }, [templateId]);\n\n  const fetchTemplate = async (templateId: string) => {\n    setLoading(true);\n    try {\n      // Use the correct service method\n      const response = await templateRecommendationService.getTemplateById(templateId);\n      if (response.success && response.template) {\n        // The template data is under the 'template' key in the response\n        const template = response.template;\n        setName(template.name);\n        setDescription(template.description);\n        setCategory(template.category);\n        setContent(template.content); // Make sure 'content' is included in API response\n        setThumbnail(template.thumbnail || ''); // Handle potentially missing thumbnail\n        \n        // Set MJML content if available\n        if (template.mjmlContent) {\n          setMjmlContent(template.mjmlContent);\n        }\n      } else {\n        setError(response.message || 'Failed to fetch template');\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to fetch template');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle MjmlEditor save event\n  const handleMjmlSave = (mjml: string, html: string) => {\n    setContent(html); // Save HTML content\n    setMjmlContent(mjml); // Save MJML content\n  };\n\n  const handleSubmit = async (e?: React.FormEvent) => {\n    e?.preventDefault();\n\n    if (step === 1) {\n      // Validate first step\n      if (!name || !description || !category) {\n        setError('Please fill in all required fields');\n        return;\n      }\n\n      // Generate a thumbnail if not provided\n      if (!thumbnail) {\n        setThumbnail(`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`);\n      }\n\n      setStep(2);\n      return;\n    }\n\n    // Step 2 submission\n    console.log('[Submit Step 2] Starting...');\n    \n    // Validate that we have content (both HTML and MJML? or just HTML?)\n    // Let's assume HTML is primary for validation\n    if (!content) {\n      setError('Please create a template design before saving');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    console.log('[Submit Step 2] Preparing API call...');\n\n    try {\n      const templateData = {\n        name,\n        description,\n        category,\n        content, // HTML content\n        thumbnail: thumbnail || `https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`,\n        mjmlContent: mjmlContent, // Added MJML content\n      };\n\n      console.log('[Submit Step 2] Template data size:', JSON.stringify(templateData).length, 'bytes');\n      \n      let response;\n\n      if (isEditing && templateId) {\n        console.log('[Submit Step 2] Updating template ID:', templateId);\n        response = await templateRecommendationService.updateTemplate(templateId, templateData);\n      } else {\n        console.log('[Submit Step 2] Creating new template');\n        response = await templateRecommendationService.createTemplate(templateData);\n      }\n\n      console.log('[Submit Step 2] API Response:', response);\n      if (response.success) {\n        console.log('[Submit Step 2] Success! Setting timeout for navigation.');\n        setSuccess(isEditing ? 'Template updated successfully' : 'Template created successfully');\n        \n        // Use window.location for a full page refresh to avoid navigation issues\n        setTimeout(() => {\n          console.log('[Submit Step 2] Navigating back to templates');\n          window.location.href = '/email-templates'; // Update to match the route in App.tsx\n        }, 1500);\n      } else {\n        console.error('[Submit Step 2] API call not successful:', response.message);\n        setError(response.message || (isEditing ? 'Failed to update template' : 'Failed to create template'));\n      }\n    } catch (err: any) {\n      console.error('[Submit Step 2] Error during API call:', err);\n      \n      // Provide more detailed error message based on status code\n      if (err.response) {\n        if (err.response.status === 404) {\n          setError('API endpoint not found. Please check server configuration.');\n        } else if (err.response.status === 413) {\n          setError('Template content is too large. Try simplifying your design.');\n        } else if (err.response.status === 400) {\n          setError(err.response.data?.message || 'Invalid template data. Please check your inputs.');\n        } else if (err.response.status >= 500) {\n          setError('Server error. Please try again later or contact support.');\n        } else {\n          setError(err.response.data?.message || err.message || 'An error occurred while saving the template.');\n        }\n      } else if (err.request) {\n        setError('No response from server. Check your internet connection and try again.');\n      } else {\n        setError(err.message || 'An unexpected error occurred');\n      }\n    } finally {\n      console.log('[Submit Step 2] Setting loading to false.');\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    if (step === 2) {\n      setStep(1);\n    } else {\n      navigate('/email-templates');\n    }\n  };\n\n  // Get AI suggestions for the selected element\n  const getSuggestions = async (content: string, type: 'headline' | 'body' | 'cta') => {\n    try {\n      setLoadingSuggestions(true);\n      setSuggestions([]);\n\n      const response = await api.post('/ai-templates/suggest',\n        { content, suggestionType: type },\n        {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setSuggestions(response.data.data.suggestions);\n      } else {\n        console.error('Failed to get suggestions:', response.data.message);\n      }\n    } catch (error) {\n      console.error('Error getting suggestions:', error);\n    } finally {\n      setLoadingSuggestions(false);\n    }\n  };\n\n  if (loading && step === 1) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-800\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col h-screen\">\n      {/* Optional Header for Template Name/Actions */}\n      <div className=\"flex justify-between items-center p-4 bg-gray-800 text-white border-b border-gray-700\">\n        <h1 className=\"text-xl font-semibold\">\n          {isEditing ? `Editing: ${name}` : 'Create New Template'}\n        </h1>\n        <div className=\"flex gap-2\">\n           <Button\n              type=\"button\"\n              onClick={handleBack}\n              variant=\"secondary\"\n              size=\"sm\"\n            >\n              {step === 1 ? 'Back to Templates' : 'Back to Details'}\n            </Button>\n            <Button\n              type=\"button\"\n              onClick={handleSubmit}\n              variant=\"primary\"\n              size=\"sm\"\n              disabled={loading}\n            >\n              {loading ? 'Saving...' : (step === 1 ? 'Next: Design' : (isEditing ? 'Update & Close' : 'Save & Close'))}\n            </Button>\n        </div>\n      </div>\n\n      {/* Main Editor Area */}\n      <div className=\"flex flex-grow overflow-hidden\">\n        {step === 1 ? (\n          // Step 1 Form (centered or styled differently)\n          <div className=\"flex-grow p-6 bg-gray-900 overflow-y-auto\">\n            <div className=\"max-w-2xl mx-auto bg-gray-800 rounded-lg p-8\">\n              {error && <Alert type=\"error\" message={error} className=\"mb-6\" />}\n              {success && <Alert type=\"success\" message={success} className=\"mb-6\" />}\n              <form onSubmit={handleSubmit}>\n                 <div className=\"mb-6\">\n                   <Input id=\"templateName\" name=\"templateName\" label=\"Template Name\" type=\"text\" value={name} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setName(e.target.value)} required placeholder=\"Enter template name\" />\n                 </div>\n                 <div className=\"mb-6\">\n                   <label className=\"block text-white mb-2\">Description</label>\n                   <textarea value={description} onChange={(e) => setDescription(e.target.value)} required placeholder=\"Enter template description\" className=\"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800 resize-none h-32\" />\n                 </div>\n                 <div className=\"mb-6\">\n                   <label className=\"block text-white mb-2\">Category</label>\n                   <select value={category} onChange={(e) => setCategory(e.target.value)} className=\"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800\">\n                     {/* Categories must match the backend schema enum values */}\n                     <option value=\"Marketing\">Marketing</option>\n                     <option value=\"Transactional\">Transactional</option>\n                     <option value=\"Newsletter\">Newsletter</option>\n                     <option value=\"Event\">Event</option>\n                     <option value=\"Onboarding\">Onboarding</option>\n                     <option value=\"Follow-up\">Follow-up</option>\n                     <option value=\"Promotional\">Promotional</option>\n                     <option value=\"Other\">Other</option>\n                   </select>\n                 </div>\n                 <div className=\"mb-6\">\n                   <Input id=\"thumbnailUrl\" name=\"thumbnailUrl\" label=\"Thumbnail URL (optional)\" type=\"text\" value={thumbnail} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setThumbnail(e.target.value)} placeholder=\"Enter thumbnail URL or leave blank\" />\n                   {thumbnail && <div className=\"mt-2\"><img src={thumbnail} alt=\"Thumbnail preview\" className=\"h-32 object-cover rounded-lg\" /></div>}\n                 </div>\n                <div className=\"flex justify-end\">\n                   <Button type=\"submit\" variant=\"primary\" size=\"md\">Next: Design Template</Button>\n                 </div>\n              </form>\n            </div>\n          </div>\n        ) : (\n          // Step 2 Editor Layout\n          <div className=\"flex flex-grow h-full\">\n            {/* Left Panel for Tools */}\n            <div className=\"w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto flex flex-col\">\n              <div className=\"p-2 text-white text-center font-semibold border-b border-gray-700\">Template Options</div>\n              <div className=\"p-4 space-y-4\">\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-300 mb-2\">AI Suggestions</h3>\n                  <Button \n                    variant=\"secondary\" \n                    size=\"sm\" \n                    className=\"w-full\"\n                    onClick={() => {\n                      setSuggestionType('headline');\n                      setShowAiSuggestions(true);\n                      getSuggestions('Headline suggestion for email', 'headline');\n                    }}\n                  >\n                    Get Headline Ideas\n                  </Button>\n                  <Button \n                    variant=\"secondary\" \n                    size=\"sm\" \n                    className=\"w-full mt-2\"\n                    onClick={() => {\n                      setSuggestionType('body');\n                      setShowAiSuggestions(true);\n                      getSuggestions('Body text suggestion for email', 'body');\n                    }}\n                  >\n                    Get Body Text Ideas\n                  </Button>\n                  <Button \n                    variant=\"secondary\" \n                    size=\"sm\" \n                    className=\"w-full mt-2\"\n                    onClick={() => {\n                      setSuggestionType('cta');\n                      setShowAiSuggestions(true);\n                      getSuggestions('Call to action suggestion', 'cta');\n                    }}\n                  >\n                    Get CTA Ideas\n                  </Button>\n                </div>\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-300 mb-2\">Help</h3>\n                  <p className=\"text-xs text-gray-400\">Use the MJML editor to create responsive emails. Customize your template using the available blocks and settings.</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Center Panel (MjmlEditor) */}\n            <div className=\"flex-grow flex flex-col bg-gray-700\">\n              <MjmlEditor\n                initialHtml={content}\n                initialMjml={mjmlContent}\n                onSave={handleMjmlSave}\n                height=\"100%\"\n              />\n\n              {/* AI Suggestion Panel (conditionally rendered) */}\n              {showAiSuggestions && (\n                <div className=\"absolute right-80 top-40 w-80 z-50\">\n                  <AISuggestionPanel\n                    type={suggestionType}\n                    suggestions={suggestions}\n                    isLoading={loadingSuggestions}\n                    onApply={(suggestion) => {\n                      // Note: This needs custom handling with MjmlEditor\n                      // Likely requires interacting with the GrapesJS editor instance via a ref\n                      console.log('Selected suggestion:', suggestion);\n                      alert('Applying AI suggestions directly into the MJML editor is not yet implemented.'); // Placeholder\n                      setShowAiSuggestions(false);\n                    }}\n                    onClose={() => setShowAiSuggestions(false)}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TemplateForm;\n"], "names": ["MjmlEditor", "forwardRef", "_ref", "ref", "initialMjml", "initialHtml", "onSave", "height", "editor<PERSON><PERSON>", "useRef", "grapesEditor", "useEffect", "current", "console", "log", "destroy", "hasMjml", "mjm<PERSON><PERSON><PERSON><PERSON>", "length", "hasHtml", "htmlLength", "editor", "<PERSON><PERSON><PERSON>", "init", "container", "fromElement", "String", "width", "storageManager", "plugins", "grapesjsMjml", "pluginsOpts", "useXmlParser", "resetBlocks", "error", "saveTimeout", "Commands", "has", "add", "run", "mjml", "getHtml", "html", "component", "getWrapper", "setTimeout", "setComponents", "substring", "e", "isSaving", "on", "clearTimeout", "finalMjml", "finalHtml", "mjmlCode", "runCommand", "cmdErr", "warn", "htmlCmdErr", "trim", "destroyError", "initError", "useImperativeHandle", "save", "async", "generatedCode", "Error", "result", "rawMjml", "generatedHtml", "fallbackErr", "Promise", "resolve", "potentiallyUpdatedHtml", "refetchErr", "getEditor", "_jsx", "style", "displayName", "TemplateForm", "navigate", "useNavigate", "templateId", "useParams", "isEditing", "Boolean", "name", "setName", "useState", "description", "setDescription", "category", "setCategory", "content", "<PERSON><PERSON><PERSON><PERSON>", "mjml<PERSON><PERSON><PERSON>", "setMjmlContent", "thumbnail", "setThumbnail", "loading", "setLoading", "setError", "success", "setSuccess", "step", "setStep", "showAiSuggestions", "setShowAiSuggestions", "suggestionType", "setSuggestionType", "suggestions", "setSuggestions", "loadingSuggestions", "setLoadingSuggestions", "fetchTemplate", "response", "templateRecommendationService", "getTemplateById", "template", "message", "err", "_err$response", "_err$response$data", "data", "handleSubmit", "preventDefault", "encodeURIComponent", "templateData", "JSON", "stringify", "updateTemplate", "createTemplate", "window", "location", "href", "status", "_err$response$data2", "_err$response$data3", "request", "getSuggestions", "type", "api", "post", "headers", "localStorage", "getItem", "className", "children", "_jsxs", "<PERSON><PERSON>", "onClick", "handleBack", "variant", "size", "disabled", "<PERSON><PERSON>", "onSubmit", "Input", "id", "label", "value", "onChange", "target", "required", "placeholder", "src", "alt", "handleMjmlSave", "AISuggestionPanel", "isLoading", "onApply", "suggestion", "alert", "onClose"], "sourceRoot": ""}