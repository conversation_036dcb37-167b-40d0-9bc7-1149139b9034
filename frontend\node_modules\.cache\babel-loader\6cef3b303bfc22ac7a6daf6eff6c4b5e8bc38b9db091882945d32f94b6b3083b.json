{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\components\\\\StatCard.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Helper for icon rendering (replace with your actual icon logic)\nconst Icon = ({\n  name\n}) => /*#__PURE__*/_jsxDEV(\"i\", {\n  className: `placeholder-icon-${name} w-5 h-5`\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 17,\n  columnNumber: 46\n}, this);\n_c = Icon;\nconst StatCard = ({\n  title,\n  value,\n  icon,\n  change,\n  className = '',\n  tooltip,\n  details\n}) => {\n  // Use the base .card or apply .glassmorphic for futuristic feel\n  // Use container-futuristic if defined, otherwise default card styling from index.css\n  const baseCardClass = \"card container-futuristic flex flex-col\";\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${baseCardClass} ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-start mb-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"stat-label\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), icon &&\n      /*#__PURE__*/\n      // Use text-secondary for default icon color\n      _jsxDEV(\"span\", {\n        className: \"text-text-secondary opacity-80\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          name: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stat-value mb-1\",\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), tooltip && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xs text-text-secondary mt-1 opacity-90\",\n      children: tooltip\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 9\n    }, this), change &&\n    /*#__PURE__*/\n    // Apply theme colors: growth-green for positive, danger for negative\n    _jsxDEV(\"div\", {\n      className: `text-sm mt-2 flex items-center font-medium ${change.isPositive ? 'text-growth-green' : 'text-danger'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"mr-1\",\n        children: change.isPositive ? /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-4 w-4\",\n          viewBox: \"0 0 20 20\",\n          fill: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 115\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          className: \"h-4 w-4\",\n          viewBox: \"0 0 20 20\",\n          fill: \"currentColor\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 115\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this), change.value]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 9\n    }, this), details && details.length > 0 &&\n    /*#__PURE__*/\n    // Use the theme border color\n    _jsxDEV(\"div\", {\n      className: \"mt-auto pt-3 border-t border-border mt-3\",\n      children: details.map((detail, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between text-xs py-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-text-secondary opacity-90\",\n          children: detail.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium\",\n          children: detail.value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_c2 = StatCard;\nexport default StatCard;\nvar _c, _c2;\n$RefreshReg$(_c, \"Icon\");\n$RefreshReg$(_c2, \"StatCard\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Icon", "name", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "StatCard", "title", "value", "icon", "change", "tooltip", "details", "baseCardClass", "children", "isPositive", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "length", "map", "detail", "index", "label", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/StatCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  icon?: string;\n  change?: {\n    value: string | number;\n    isPositive: boolean;\n  };\n  className?: string;\n  tooltip?: string;\n  details?: { label: string; value: string | number }[];\n}\n\n// Helper for icon rendering (replace with your actual icon logic)\nconst Icon = ({ name }: { name: string }) => <i className={`placeholder-icon-${name} w-5 h-5`} />; \n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  icon,\n  change,\n  className = '',\n  tooltip,\n  details\n}) => {\n  // Use the base .card or apply .glassmorphic for futuristic feel\n  // Use container-futuristic if defined, otherwise default card styling from index.css\n  const baseCardClass = \"card container-futuristic flex flex-col\"; \n\n  return (\n    <div className={`${baseCardClass} ${className}`}>\n      <div className=\"flex justify-between items-start mb-2\">\n        <h3 className=\"stat-label\">{title}</h3>\n        {icon && (\n          // Use text-secondary for default icon color\n          <span className=\"text-text-secondary opacity-80\">\n            <Icon name={icon} />\n          </span>\n        )}\n      </div>\n      <div className=\"stat-value mb-1\">{value}</div>\n      {tooltip && (\n        <div className=\"text-xs text-text-secondary mt-1 opacity-90\">{tooltip}</div>\n      )}\n      {change && (\n        // Apply theme colors: growth-green for positive, danger for negative\n        <div className={`text-sm mt-2 flex items-center font-medium ${change.isPositive ? 'text-growth-green' : 'text-danger'}`}>\n          <span className=\"mr-1\">\n            {change.isPositive ? \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\" clipRule=\"evenodd\" /></svg> : \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" /></svg> \n            }\n          </span>\n          {change.value}\n        </div>\n      )}\n      {details && details.length > 0 && (\n        // Use the theme border color\n        <div className=\"mt-auto pt-3 border-t border-border mt-3\">\n          {details.map((detail, index) => (\n            <div key={index} className=\"flex justify-between text-xs py-1\">\n              <span className=\"text-text-secondary opacity-90\">{detail.label}</span>\n              <span className=\"font-medium\">{detail.value}</span>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StatCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAe1B;AACA,MAAMC,IAAI,GAAGA,CAAC;EAAEC;AAAuB,CAAC,kBAAKF,OAAA;EAAGG,SAAS,EAAE,oBAAoBD,IAAI;AAAW;EAAAE,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC;AAACC,EAAA,GAA5FP,IAAI;AAEV,MAAMQ,QAAiC,GAAGA,CAAC;EACzCC,KAAK;EACLC,KAAK;EACLC,IAAI;EACJC,MAAM;EACNV,SAAS,GAAG,EAAE;EACdW,OAAO;EACPC;AACF,CAAC,KAAK;EACJ;EACA;EACA,MAAMC,aAAa,GAAG,yCAAyC;EAE/D,oBACEhB,OAAA;IAAKG,SAAS,EAAE,GAAGa,aAAa,IAAIb,SAAS,EAAG;IAAAc,QAAA,gBAC9CjB,OAAA;MAAKG,SAAS,EAAC,uCAAuC;MAAAc,QAAA,gBACpDjB,OAAA;QAAIG,SAAS,EAAC,YAAY;QAAAc,QAAA,EAAEP;MAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACtCK,IAAI;MAAA;MACH;MACAZ,OAAA;QAAMG,SAAS,EAAC,gCAAgC;QAAAc,QAAA,eAC9CjB,OAAA,CAACC,IAAI;UAACC,IAAI,EAAEU;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CACP;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eACNP,OAAA;MAAKG,SAAS,EAAC,iBAAiB;MAAAc,QAAA,EAAEN;IAAK;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC7CO,OAAO,iBACNd,OAAA;MAAKG,SAAS,EAAC,6CAA6C;MAAAc,QAAA,EAAEH;IAAO;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAC5E,EACAM,MAAM;IAAA;IACL;IACAb,OAAA;MAAKG,SAAS,EAAE,8CAA8CU,MAAM,CAACK,UAAU,GAAG,mBAAmB,GAAG,aAAa,EAAG;MAAAD,QAAA,gBACtHjB,OAAA;QAAMG,SAAS,EAAC,MAAM;QAAAc,QAAA,EACnBJ,MAAM,CAACK,UAAU,gBAChBlB,OAAA;UAAKmB,KAAK,EAAC,4BAA4B;UAAChB,SAAS,EAAC,SAAS;UAACiB,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,cAAc;UAAAJ,QAAA,eAACjB,OAAA;YAAMsB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,qHAAqH;YAACC,QAAQ,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,gBAChRP,OAAA;UAAKmB,KAAK,EAAC,4BAA4B;UAAChB,SAAS,EAAC,SAAS;UAACiB,OAAO,EAAC,WAAW;UAACC,IAAI,EAAC,cAAc;UAAAJ,QAAA,eAACjB,OAAA;YAAMsB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,oHAAoH;YAACC,QAAQ,EAAC;UAAS;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE7Q,CAAC,EACNM,MAAM,CAACF,KAAK;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,EACAQ,OAAO,IAAIA,OAAO,CAACU,MAAM,GAAG,CAAC;IAAA;IAC5B;IACAzB,OAAA;MAAKG,SAAS,EAAC,0CAA0C;MAAAc,QAAA,EACtDF,OAAO,CAACW,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB5B,OAAA;QAAiBG,SAAS,EAAC,mCAAmC;QAAAc,QAAA,gBAC5DjB,OAAA;UAAMG,SAAS,EAAC,gCAAgC;UAAAc,QAAA,EAAEU,MAAM,CAACE;QAAK;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtEP,OAAA;UAAMG,SAAS,EAAC,aAAa;UAAAc,QAAA,EAAEU,MAAM,CAAChB;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAF3CqB,KAAK;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACuB,GAAA,GArDIrB,QAAiC;AAuDvC,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAAvB,EAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}