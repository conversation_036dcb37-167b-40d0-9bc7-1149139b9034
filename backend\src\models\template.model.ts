import {
  Document,
  model,
  Schema,
  Types,
} from 'mongoose';

// Define BlockContent structure (can be expanded)
interface BlockContent {
  [key: string]: any; // Allow flexible content
}

// Define Block structure for the 'blocks' array
interface BlockStructure {
  blockId?: string; // ID referencing a master block definition if applicable
  _id?: string | Types.ObjectId; // Or use the block's own ID if custom/generated
  type?: string;
  category?: string;
  content: BlockContent;
}

export interface ITemplate extends Document {
  name: string;
  description: string;
  content: string; // Stored HTML content
  mjmlContent?: string; // Added: MJML source code for the template
  category: string;
  thumbnail: string;
  userId?: Types.ObjectId; // Optional for system templates
  isSystem: boolean;
  blocks?: BlockStructure[]; // Added: Array of block objects with their content for this template instance
  blockIds?: string[]; // Added: Array of block IDs representing the structure
  isAiGenerated?: boolean; // Added: Flag for AI generation
  aiPrompt?: string; // Added: Store the prompt used
  mosaicoTemplate?: boolean; // Added: Flag for Mosaico templates
  mosaicoJson?: any; // Added: Mosaico template JSON data
  createdAt: Date;
  updatedAt: Date;
}

// Define schema for BlockStructure to be embedded
const blockStructureSchema = new Schema<BlockStructure>({
    blockId: { type: String, required: false },
    _id: { type: Schema.Types.ObjectId, required: false },
    type: { type: String, required: false },
    category: { type: String, required: false },
    content: { type: Schema.Types.Mixed, required: true }
}, { _id: false }); // Disable _id for subdocuments unless needed

const templateSchema = new Schema<ITemplate>(
  {
    name: {
      type: String,
      required: [true, 'Template name is required'],
      trim: true,
      maxlength: [100, 'Template name cannot exceed 100 characters']
    },
    description: {
      type: String,
      required: false, // Make description optional maybe?
      trim: true,
      maxlength: [500, 'Template description cannot exceed 500 characters']
    },
    content: {
      type: String,
      required: [true, 'Template HTML content is required'] // Keep HTML content
    },
    category: {
      type: String,
      enum: ['Marketing', 'Transactional', 'Newsletter', 'Event', 'Onboarding', 'Follow-up', 'Promotional', 'Other'],
      default: 'Other'
    },
    thumbnail: {
      type: String,
      required: false
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: function(this: any) { return !this.isSystem; } // Required only if not a system template
    },
    isSystem: {
      type: Boolean,
      default: false
    },
    blocks: { // Added schema definition for blocks
      type: [blockStructureSchema],
      required: false
    },
    blockIds: { // Added schema definition for blockIds
      type: [String],
      required: false
    },
    isAiGenerated: {
        type: Boolean,
        default: false
    },
    aiPrompt: {
        type: String,
        required: false
    },
    mosaicoTemplate: {
        type: Boolean,
        default: false
    },
    mosaicoJson: {
        type: Schema.Types.Mixed,
        required: false
    }
  },
  {
    timestamps: true
  }
);

// Create indexes for faster querying
templateSchema.index({ name: 'text', description: 'text', category: 1 });
templateSchema.index({ userId: 1 });
templateSchema.index({ isSystem: 1 });
templateSchema.index({ isAiGenerated: 1 });
templateSchema.index({ mosaicoTemplate: 1 });

const Template = model<ITemplate>('Template', templateSchema);

export default Template;
