{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { useContext } from 'react';\nimport { DndContext } from '../core/index.js';\n/**\n * A hook to retrieve the DragDropManager from Context\n */\nexport function useDragDropManager() {\n  const {\n    dragDropManager\n  } = useContext(DndContext);\n  invariant(dragDropManager != null, 'Expected drag drop context');\n  return dragDropManager;\n}", "map": {"version": 3, "names": ["invariant", "useContext", "DndContext", "useDragDropManager", "dragDropManager"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd\\src\\hooks\\useDragDropManager.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { DragDropManager } from 'dnd-core'\nimport { useContext } from 'react'\n\nimport { DndContext } from '../core/index.js'\n\n/**\n * A hook to retrieve the DragDropManager from Context\n */\nexport function useDragDropManager(): DragDropManager {\n\tconst { dragDropManager } = useContext(DndContext)\n\tinvariant(dragDropManager != null, 'Expected drag drop context')\n\treturn dragDropManager as DragDropManager\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAEhD,SAASC,UAAU,QAAQ,OAAO;AAElC,SAASC,UAAU,QAAQ,kBAAkB;AAE7C;;;AAGA,OAAO,SAASC,kBAAkBA,CAAA,EAAoB;EACrD,MAAM;IAAEC;EAAe,CAAE,GAAGH,UAAU,CAACC,UAAU,CAAC;EAClDF,SAAS,CAACI,eAAe,IAAI,IAAI,EAAE,4BAA4B,CAAC;EAChE,OAAOA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}