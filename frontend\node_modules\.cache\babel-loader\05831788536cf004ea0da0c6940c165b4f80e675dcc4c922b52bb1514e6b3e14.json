{"ast": null, "code": "import React,{useEffect,useState}from'react';import Alert from'../components/Alert';import Button from'../components/Button';import Card from'../components/Card';import Input from'../components/Input';import{abTestService}from'../services';// Define interfaces if needed\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ABTesting=()=>{var _segments$find,_selectedTest$variant,_selectedTest$variant2,_testResults$results,_testResults$results$,_testResults$results2,_testResults$results3;const[tests,setTests]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[selectedTest,setSelectedTest]=useState(null);const[testResults,setTestResults]=useState(null);const[resultsLoading,setResultsLoading]=useState(false);// New test form state\nconst[newTest,setNewTest]=useState({name:'',description:'',testType:'subject',variantA:'',variantB:'',audienceSize:30,winningCriteria:'open_rate',segmentId:''});// Mock segments data (in a real app, this would come from an API)\nconst segments=[{id:'segment1',name:'Active Users'},{id:'segment2',name:'New Subscribers'},{id:'segment3',name:'Inactive Users'},{id:'segment4',name:'High Engagement'}];// Fetch tests on component mount\nuseEffect(()=>{const fetchTests=async()=>{setLoading(true);setError(null);try{const response=await abTestService.getUserTests();if(response.success){setTests(response.data);}else{setError(response.message||'Failed to fetch A/B tests');}}catch(err){setError(err.message||'An error occurred while fetching tests');}finally{setLoading(false);}};fetchTests();},[]);// Fetch test results when a test is selected\nuseEffect(()=>{if(!selectedTest||selectedTest.status!=='running'&&selectedTest.status!=='completed'){setTestResults(null);return;}const fetchTestResults=async()=>{setResultsLoading(true);try{const response=await abTestService.getTestResults(selectedTest.id);if(response.success){setTestResults(response.data);}else{console.error('Failed to fetch test results:',response.message);setTestResults(null);}}catch(err){console.error('Error fetching test results:',err);setTestResults(null);}finally{setResultsLoading(false);}};fetchTestResults();},[selectedTest]);// Handle creating a new test\nconst handleCreateTest=async e=>{e.preventDefault();if(!newTest.name.trim()||!newTest.variantA.trim()||!newTest.variantB.trim()){setError('Test name and both variants are required');return;}if(newTest.audienceSize<1||newTest.audienceSize>100){setError('Audience size must be between 1 and 100%');return;}setLoading(true);setError(null);try{const testData={name:newTest.name,description:newTest.description||undefined,testType:newTest.testType,variants:{a:newTest.variantA,b:newTest.variantB},audienceSize:newTest.audienceSize,winningCriteria:newTest.winningCriteria,segmentId:newTest.segmentId||undefined};const response=await abTestService.createTest(testData);if(response.success){setTests([...tests,response.data]);setNewTest({name:'',description:'',testType:'subject',variantA:'',variantB:'',audienceSize:30,winningCriteria:'open_rate',segmentId:''});}else{setError(response.message||'Failed to create A/B test');}}catch(err){setError(err.message||'An error occurred while creating test');}finally{setLoading(false);}};// Handle test selection\nconst handleTestSelection=test=>{if((selectedTest===null||selectedTest===void 0?void 0:selectedTest.id)===test.id)return;setSelectedTest(test);};// Handle starting a test\nconst handleStartTest=async()=>{if(!selectedTest||selectedTest.status!=='draft')return;setLoading(true);try{const response=await abTestService.startTest(selectedTest.id);if(response.success){const updatedTest={...selectedTest,status:'running'};setTests(tests.map(test=>test.id===selectedTest.id?updatedTest:test));setSelectedTest(updatedTest);}else{setError(response.message||'Failed to start test');}}catch(err){setError(err.message||'Error starting test');console.error('Error starting test:',err);}finally{setLoading(false);}};// Handle cancelling a test\nconst handleCancelTest=async()=>{if(!selectedTest||selectedTest.status!=='running')return;if(!window.confirm('Are you sure you want to cancel this running test?'))return;setLoading(true);try{const response=await abTestService.cancelTest(selectedTest.id);if(response.success){const updatedTest={...selectedTest,status:'cancelled'};setTests(tests.map(test=>test.id===selectedTest.id?updatedTest:test));setSelectedTest(updatedTest);}else{setError(response.message||'Failed to cancel test');}}catch(err){setError(err.message||'Error cancelling test');console.error('Error cancelling test:',err);}finally{setLoading(false);}};// Get status badge color and text representation\nconst getStatusStyle=status=>{status=status||'draft';switch(status){case'draft':return{color:'gray',text:'Draft'};case'running':return{color:'blue',text:'Running'};case'completed':return{color:'green',text:'Completed'};case'cancelled':return{color:'red',text:'Cancelled'};default:return{color:'gray',text:status};}};// Helper function to get background color for badge based on status\nconst getBadgeBgColor=color=>{switch(color){case'gray':return'bg-gray-700 text-gray-200';case'blue':return'bg-blue-800 text-blue-100';case'green':return'bg-green-800 text-green-100';case'red':return'bg-red-800 text-red-100';default:return'bg-gray-700 text-gray-200';}};// Remove Sidebar wrapper\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-4 py-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-between items-center mb-6\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-semibold text-text-primary\",children:\"Enhanced A/B Testing\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-6\",children:\"Create and manage A/B tests to optimize your email campaigns.\"}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(null),className:\"mb-6\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-6 lg:grid-cols-3\",children:[/*#__PURE__*/_jsxs(Card,{className:\"lg:col-span-1\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\",children:\"Create New A/B Test\"}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleCreateTest,className:\"p-4 space-y-4\",children:[/*#__PURE__*/_jsx(Input,{label:\"Test Name\",id:\"testName\",name:\"testName\",value:newTest.name,onChange:e=>setNewTest({...newTest,name:e.target.value}),placeholder:\"e.g., Subject Line Test - May\",required:true}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"testDescription\",className:\"block text-sm font-medium text-text-secondary mb-1\",children:\"Description (Optional)\"}),/*#__PURE__*/_jsx(\"textarea\",{id:\"testDescription\",name:\"testDescription\",rows:2,value:newTest.description,onChange:e=>setNewTest({...newTest,description:e.target.value}),placeholder:\"Brief description of the test\",className:\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"testType\",className:\"block text-sm font-medium text-text-secondary mb-1\",children:\"Test Type\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"testType\",name:\"testType\",value:newTest.testType,onChange:e=>setNewTest({...newTest,testType:e.target.value}),className:\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"subject\",children:\"Subject Line\"}),/*#__PURE__*/_jsx(\"option\",{value:\"content\",children:\"Email Content\"}),/*#__PURE__*/_jsx(\"option\",{value:\"sender\",children:\"Sender Name\"}),/*#__PURE__*/_jsx(\"option\",{value:\"cta\",children:\"Call to Action\"}),/*#__PURE__*/_jsx(\"option\",{value:\"time\",children:\"Send Time\"})]})]}),/*#__PURE__*/_jsx(Input,{label:\"Variant A\",id:\"variantA\",name:\"variantA\",value:newTest.variantA,onChange:e=>setNewTest({...newTest,variantA:e.target.value}),placeholder:newTest.testType==='subject'?'e.g., Check out our new features!':newTest.testType==='content'?'Enter HTML/Text for Variant A':'Variant A value',required:true}),/*#__PURE__*/_jsx(Input,{label:\"Variant B\",id:\"variantB\",name:\"variantB\",value:newTest.variantB,onChange:e=>setNewTest({...newTest,variantB:e.target.value}),placeholder:newTest.testType==='subject'?'e.g., New Features Inside! 🔥':newTest.testType==='content'?'Enter HTML/Text for Variant B':'Variant B value',required:true}),/*#__PURE__*/_jsx(Input,{label:\"Audience Size (%)\",id:\"audienceSize\",name:\"audienceSize\",type:\"number\",value:newTest.audienceSize.toString(),onChange:e=>{const val=parseInt(e.target.value);if(!isNaN(val)&&val>=0&&val<=100){setNewTest({...newTest,audienceSize:val});}else if(e.target.value===''){setNewTest({...newTest,audienceSize:0});}}}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"winningCriteria\",className:\"block text-sm font-medium text-text-secondary mb-1\",children:\"Winning Criteria\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"winningCriteria\",name:\"winningCriteria\",value:newTest.winningCriteria,onChange:e=>setNewTest({...newTest,winningCriteria:e.target.value}),className:\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"open_rate\",children:\"Open Rate\"}),/*#__PURE__*/_jsx(\"option\",{value:\"click_rate\",children:\"Click Rate\"}),/*#__PURE__*/_jsx(\"option\",{value:\"conversion_rate\",children:\"Conversion Rate\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"segmentId\",className:\"block text-sm font-medium text-text-secondary mb-1\",children:\"Segment (Optional)\"}),/*#__PURE__*/_jsxs(\"select\",{id:\"segmentId\",name:\"segmentId\",value:newTest.segmentId,onChange:e=>setNewTest({...newTest,segmentId:e.target.value}),className:\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"All Recipients\"}),segments.map(s=>/*#__PURE__*/_jsx(\"option\",{value:s.id,children:s.name},s.id))]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-end pt-2\",children:/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading,className:\"btn-cta\",children:loading?'Creating...':'Create Test'})})]})]}),/*#__PURE__*/_jsxs(Card,{className:\"lg:col-span-1\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\",children:\"Existing Tests\"}),loading&&tests.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"p-4 text-center text-text-secondary\",children:\"Loading tests...\"}):tests.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"p-4 text-center text-text-secondary\",children:\"No A/B tests created yet.\"}):/*#__PURE__*/_jsxs(\"ul\",{className:\"divide-y divide-gray-700 max-h-[500px] overflow-y-auto\",children:[\" \",tests.map(test=>/*#__PURE__*/_jsxs(\"li\",{className:`p-4 hover:bg-gray-700 cursor-pointer ${(selectedTest===null||selectedTest===void 0?void 0:selectedTest.id)===test.id?'bg-gray-700':''}`,onClick:()=>handleTestSelection(test),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm font-medium text-text-primary truncate flex-1 mr-2\",children:test.name}),/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-0.5 rounded-full text-xs font-medium capitalize ${getBadgeBgColor(getStatusStyle(test.status||'draft').color)}`,children:getStatusStyle(test.status||'draft').text})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-text-secondary mt-1\",children:[\"Type: \",test.testType]})]},test.id))]})]}),/*#__PURE__*/_jsxs(Card,{className:\"lg:col-span-1\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\",children:\"Test Details & Results\"}),/*#__PURE__*/_jsx(\"div\",{className:\"p-4\",children:selectedTest?/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-md font-medium text-text-primary mb-1\",children:selectedTest.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-text-secondary mb-2\",children:selectedTest.description}),/*#__PURE__*/_jsxs(\"span\",{className:`inline-block px-2 py-0.5 rounded-full text-xs font-medium capitalize ${getBadgeBgColor(getStatusStyle(selectedTest.status||'draft').color)} mb-2`,children:[\"Status: \",getStatusStyle(selectedTest.status||'draft').text]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-text-secondary\",children:[\"Type: \",selectedTest.testType]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-text-secondary\",children:[\"Audience: \",selectedTest.audienceSize,\"%\"]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-text-secondary\",children:[\"Criteria: \",selectedTest.winningCriteria]}),selectedTest.segmentId&&/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-text-secondary\",children:[\"Segment: \",((_segments$find=segments.find(s=>s.id===selectedTest.segmentId))===null||_segments$find===void 0?void 0:_segments$find.name)||'Unknown']})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-1\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-text-secondary\",children:\"Variant A:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words\",children:(_selectedTest$variant=selectedTest.variants)===null||_selectedTest$variant===void 0?void 0:_selectedTest$variant.a})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-1\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-medium text-text-secondary\",children:\"Variant B:\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words\",children:(_selectedTest$variant2=selectedTest.variants)===null||_selectedTest$variant2===void 0?void 0:_selectedTest$variant2.b})]}),(selectedTest.status==='draft'||selectedTest.status==='paused')&&/*#__PURE__*/_jsx(Button,{onClick:handleStartTest,size:\"sm\",className:\"btn-cta\",children:\"Start Test\"}),selectedTest.status==='running'&&/*#__PURE__*/_jsx(Button,{onClick:handleCancelTest,variant:\"danger\",size:\"sm\",children:\"Cancel Test\"}),testResults?/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 pt-4 border-t border-gray-700\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-md font-medium text-text-primary mb-2\",children:\"Results\"}),testResults.winner?/*#__PURE__*/_jsx(Alert,{type:\"success\",message:`Winner: Variant ${testResults.winner.toUpperCase()} (${testResults.confidence} confidence)`,className:\"mb-2\"}):testResults.status==='completed'?/*#__PURE__*/_jsx(Alert,{type:\"info\",message:\"Test completed, inconclusive result.\",className:\"mb-2\"}):/*#__PURE__*/_jsx(Alert,{type:\"info\",message:\"Test is still running...\",className:\"mb-2\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-text-secondary space-y-1\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[\"Variant A (\",testResults.criteria,\"): \",((_testResults$results=testResults.results)===null||_testResults$results===void 0?void 0:(_testResults$results$=_testResults$results.a)===null||_testResults$results$===void 0?void 0:_testResults$results$.rate)||'N/A',\"%\"]}),/*#__PURE__*/_jsxs(\"p\",{children:[\"Variant B (\",testResults.criteria,\"): \",((_testResults$results2=testResults.results)===null||_testResults$results2===void 0?void 0:(_testResults$results3=_testResults$results2.b)===null||_testResults$results3===void 0?void 0:_testResults$results3.rate)||'N/A',\"%\"]})]})]}):selectedTest.status==='running'||selectedTest.status==='completed'?/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-text-secondary italic mt-4\",children:\"Loading results...\"}):null]}):/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary italic text-center\",children:\"Select a test from the list to see details and results.\"})})]})]})]});};export default ABTesting;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "Input", "abTestService", "jsx", "_jsx", "jsxs", "_jsxs", "ABTesting", "_segments$find", "_selectedTest$variant", "_selectedTest$variant2", "_testResults$results", "_testResults$results$", "_testResults$results2", "_testResults$results3", "tests", "setTests", "loading", "setLoading", "error", "setError", "selectedTest", "setSelectedTest", "testResults", "setTestResults", "resultsLoading", "setResultsLoading", "newTest", "setNewTest", "name", "description", "testType", "variantA", "variantB", "audienceSize", "winningCriteria", "segmentId", "segments", "id", "fetchTests", "response", "getUserTests", "success", "data", "message", "err", "status", "fetchTestResults", "getTestResults", "console", "handleCreateTest", "e", "preventDefault", "trim", "testData", "undefined", "variants", "a", "b", "createTest", "handleTestSelection", "test", "handleStartTest", "startTest", "updatedTest", "map", "handleCancelTest", "window", "confirm", "cancelTest", "getStatusStyle", "color", "text", "getBadgeBgColor", "className", "children", "type", "onClose", "onSubmit", "label", "value", "onChange", "target", "placeholder", "required", "htmlFor", "rows", "toString", "val", "parseInt", "isNaN", "s", "disabled", "length", "onClick", "find", "size", "variant", "winner", "toUpperCase", "confidence", "criteria", "results", "rate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/ABTesting.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  FormEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport { abTestService } from '../services';\n\n// Define interfaces if needed\ninterface TestVariantData {\n    a: string;\n    b: string;\n}\n\ninterface NewTestData {\n    name: string;\n    description?: string;\n    testType: string;\n    variants: TestVariantData;\n    audienceSize: number;\n    winningCriteria: string;\n    segmentId?: string;\n}\n\ninterface ABTest extends NewTestData {\n    id: string;\n    status?: 'draft' | 'running' | 'completed' | 'cancelled' | string; // Allow string for other statuses\n    // Add other properties from API response\n}\n\ninterface TestResultData {\n    rate?: number;\n    // Add other result metrics\n}\n\ninterface TestResults {\n    status: string;\n    winner?: 'a' | 'b';\n    confidence?: string;\n    criteria?: string;\n    results?: {\n        a?: TestResultData;\n        b?: TestResultData;\n    };\n}\n\ninterface Segment {\n  id: string;\n  name: string;\n}\n\nconst ABTesting: React.FC = () => {\n  const [tests, setTests] = useState<ABTest[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedTest, setSelectedTest] = useState<ABTest | null>(null);\n  const [testResults, setTestResults] = useState<TestResults | null>(null);\n  const [resultsLoading, setResultsLoading] = useState<boolean>(false);\n\n  // New test form state\n  const [newTest, setNewTest] = useState({\n    name: '',\n    description: '',\n    testType: 'subject',\n    variantA: '',\n    variantB: '',\n    audienceSize: 30,\n    winningCriteria: 'open_rate',\n    segmentId: ''\n  });\n\n  // Mock segments data (in a real app, this would come from an API)\n  const segments: Segment[] = [\n    { id: 'segment1', name: 'Active Users' },\n    { id: 'segment2', name: 'New Subscribers' },\n    { id: 'segment3', name: 'Inactive Users' },\n    { id: 'segment4', name: 'High Engagement' }\n  ];\n\n  // Fetch tests on component mount\n  useEffect(() => {\n    const fetchTests = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const response = await abTestService.getUserTests();\n        if (response.success) {\n          setTests(response.data);\n        } else {\n          setError(response.message || 'Failed to fetch A/B tests');\n        }\n      } catch (err: any) {\n        setError(err.message || 'An error occurred while fetching tests');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchTests();\n  }, []);\n\n  // Fetch test results when a test is selected\n  useEffect(() => {\n    if (!selectedTest || (selectedTest.status !== 'running' && selectedTest.status !== 'completed')) {\n        setTestResults(null);\n        return;\n    }\n\n    const fetchTestResults = async () => {\n      setResultsLoading(true);\n      try {\n        const response = await abTestService.getTestResults(selectedTest.id);\n        if (response.success) {\n          setTestResults(response.data);\n        } else {\n            console.error('Failed to fetch test results:', response.message);\n            setTestResults(null);\n        }\n      } catch (err) {\n        console.error('Error fetching test results:', err);\n        setTestResults(null);\n      } finally {\n          setResultsLoading(false);\n      }\n    };\n\n    fetchTestResults();\n  }, [selectedTest]);\n\n  // Handle creating a new test\n  const handleCreateTest = async (e: FormEvent) => {\n    e.preventDefault();\n\n    if (!newTest.name.trim() || !newTest.variantA.trim() || !newTest.variantB.trim()) {\n      setError('Test name and both variants are required');\n      return;\n    }\n\n    if (newTest.audienceSize < 1 || newTest.audienceSize > 100) {\n        setError('Audience size must be between 1 and 100%');\n        return;\n    }\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const testData: NewTestData = {\n        name: newTest.name,\n        description: newTest.description || undefined,\n        testType: newTest.testType,\n        variants: {\n          a: newTest.variantA,\n          b: newTest.variantB\n        },\n        audienceSize: newTest.audienceSize,\n        winningCriteria: newTest.winningCriteria,\n        segmentId: newTest.segmentId || undefined\n      };\n      const response = await abTestService.createTest(testData);\n\n      if (response.success) {\n        setTests([...tests, response.data]);\n        setNewTest({\n          name: '',\n          description: '',\n          testType: 'subject',\n          variantA: '',\n          variantB: '',\n          audienceSize: 30,\n          winningCriteria: 'open_rate',\n          segmentId: ''\n        });\n      } else {\n        setError(response.message || 'Failed to create A/B test');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating test');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle test selection\n  const handleTestSelection = (test: ABTest) => {\n    if (selectedTest?.id === test.id) return;\n    setSelectedTest(test);\n  };\n\n  // Handle starting a test\n  const handleStartTest = async () => {\n    if (!selectedTest || selectedTest.status !== 'draft') return;\n\n    setLoading(true);\n    try {\n      const response = await abTestService.startTest(selectedTest.id);\n      if (response.success) {\n        const updatedTest = { ...selectedTest, status: 'running' };\n        setTests(tests.map(test =>\n          test.id === selectedTest.id ? updatedTest : test\n        ));\n        setSelectedTest(updatedTest);\n      } else {\n          setError(response.message || 'Failed to start test');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Error starting test');\n      console.error('Error starting test:', err);\n    } finally {\n        setLoading(false);\n    }\n  };\n\n  // Handle cancelling a test\n  const handleCancelTest = async () => {\n    if (!selectedTest || selectedTest.status !== 'running') return;\n\n    if (!window.confirm('Are you sure you want to cancel this running test?')) return;\n\n    setLoading(true);\n    try {\n      const response = await abTestService.cancelTest(selectedTest.id);\n      if (response.success) {\n        const updatedTest = { ...selectedTest, status: 'cancelled' };\n        setTests(tests.map(test =>\n          test.id === selectedTest.id ? updatedTest : test\n        ));\n        setSelectedTest(updatedTest);\n      } else {\n          setError(response.message || 'Failed to cancel test');\n      }\n    } catch (err: any) {\n      setError(err.message || 'Error cancelling test');\n      console.error('Error cancelling test:', err);\n    } finally {\n        setLoading(false);\n    }\n  };\n\n  // Get status badge color and text representation\n  const getStatusStyle = (status: ABTest['status']): { color: string, text: string } => {\n     status = status || 'draft';\n     switch (status) {\n      case 'draft': return { color: 'gray', text: 'Draft' };\n      case 'running': return { color: 'blue', text: 'Running' };\n      case 'completed': return { color: 'green', text: 'Completed' };\n      case 'cancelled': return { color: 'red', text: 'Cancelled' };\n      default: return { color: 'gray', text: status };\n    }\n  };\n\n  // Helper function to get background color for badge based on status\n  const getBadgeBgColor = (color: string): string => {\n      switch(color) {\n          case 'gray': return 'bg-gray-700 text-gray-200';\n          case 'blue': return 'bg-blue-800 text-blue-100';\n          case 'green': return 'bg-green-800 text-green-100';\n          case 'red': return 'bg-red-800 text-red-100';\n          default: return 'bg-gray-700 text-gray-200';\n      }\n  }\n\n  // Remove Sidebar wrapper\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n         <h1 className=\"text-2xl font-semibold text-text-primary\">Enhanced A/B Testing</h1>\n      </div>\n       <p className=\"text-text-secondary mb-6\">\n          Create and manage A/B tests to optimize your email campaigns.\n       </p>\n      \n      {error && (\n         <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n      )}\n      \n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\n        <Card className=\"lg:col-span-1\">\n           <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Create New A/B Test</h2>\n          <form onSubmit={handleCreateTest} className=\"p-4 space-y-4\">\n            <Input\n               label=\"Test Name\"\n               id=\"testName\"\n               name=\"testName\"\n               value={newTest.name}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => setNewTest({ ...newTest, name: e.target.value })}\n               placeholder=\"e.g., Subject Line Test - May\"\n               required\n            />\n            <div>\n                <label htmlFor=\"testDescription\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                    Description (Optional)\n                </label>\n                <textarea\n                   id=\"testDescription\"\n                   name=\"testDescription\"\n                   rows={2}\n                   value={newTest.description}\n                   onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setNewTest({ ...newTest, description: e.target.value })}\n                   placeholder=\"Brief description of the test\"\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500\"\n                />\n            </div>\n            <div>\n               <label htmlFor=\"testType\" className=\"block text-sm font-medium text-text-secondary mb-1\">Test Type</label>\n               <select\n                   id=\"testType\"\n                   name=\"testType\"\n                   value={newTest.testType}\n                   onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewTest({ ...newTest, testType: e.target.value })}\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                >\n                  <option value=\"subject\">Subject Line</option>\n                  <option value=\"content\">Email Content</option>\n                  <option value=\"sender\">Sender Name</option>\n                  <option value=\"cta\">Call to Action</option>\n                  <option value=\"time\">Send Time</option>\n               </select>\n            </div>\n            <Input\n               label=\"Variant A\"\n               id=\"variantA\"\n               name=\"variantA\"\n               value={newTest.variantA}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => setNewTest({ ...newTest, variantA: e.target.value })}\n               placeholder={newTest.testType === 'subject' ? 'e.g., Check out our new features!' : newTest.testType === 'content' ? 'Enter HTML/Text for Variant A' : 'Variant A value'}\n               required\n             />\n            <Input\n               label=\"Variant B\"\n               id=\"variantB\"\n               name=\"variantB\"\n               value={newTest.variantB}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => setNewTest({ ...newTest, variantB: e.target.value })}\n               placeholder={newTest.testType === 'subject' ? 'e.g., New Features Inside! 🔥' : newTest.testType === 'content' ? 'Enter HTML/Text for Variant B' : 'Variant B value'}\n               required\n            />\n             <Input\n               label=\"Audience Size (%)\"\n               id=\"audienceSize\"\n               name=\"audienceSize\"\n               type=\"number\"\n               value={newTest.audienceSize.toString()}\n               onChange={(e: ChangeEvent<HTMLInputElement>) => {\n                   const val = parseInt(e.target.value);\n                   if (!isNaN(val) && val >= 0 && val <= 100) { \n                       setNewTest({ ...newTest, audienceSize: val })\n                   } else if (e.target.value === '') {\n                       setNewTest({ ...newTest, audienceSize: 0 })\n                   }\n               }}\n             />\n             <div>\n                 <label htmlFor=\"winningCriteria\" className=\"block text-sm font-medium text-text-secondary mb-1\">Winning Criteria</label>\n                 <select\n                   id=\"winningCriteria\"\n                   name=\"winningCriteria\"\n                   value={newTest.winningCriteria}\n                   onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewTest({ ...newTest, winningCriteria: e.target.value })}\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                >\n                     <option value=\"open_rate\">Open Rate</option>\n                     <option value=\"click_rate\">Click Rate</option>\n                     <option value=\"conversion_rate\">Conversion Rate</option> \n                 </select>\n             </div>\n             <div>\n                 <label htmlFor=\"segmentId\" className=\"block text-sm font-medium text-text-secondary mb-1\">Segment (Optional)</label>\n                 <select\n                   id=\"segmentId\"\n                   name=\"segmentId\"\n                   value={newTest.segmentId}\n                   onChange={(e: ChangeEvent<HTMLSelectElement>) => setNewTest({ ...newTest, segmentId: e.target.value })}\n                   className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                >\n                     <option value=\"\">All Recipients</option>\n                     {segments.map(s => (\n                         <option key={s.id} value={s.id}>{s.name}</option>\n                     ))}\n                 </select>\n             </div>\n            <div className=\"flex justify-end pt-2\">\n               <Button type=\"submit\" disabled={loading} className=\"btn-cta\">\n                 {loading ? 'Creating...' : 'Create Test'}\n               </Button>\n            </div>\n          </form>\n        </Card>\n        \n        {/* Existing Tests List - Column 2 */} \n        <Card className=\"lg:col-span-1\"> \n           <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Existing Tests</h2>\n           {loading && tests.length === 0 ? (\n               <div className=\"p-4 text-center text-text-secondary\">Loading tests...</div>\n           ) : tests.length === 0 ? (\n               <div className=\"p-4 text-center text-text-secondary\">No A/B tests created yet.</div>\n           ) : (\n              <ul className=\"divide-y divide-gray-700 max-h-[500px] overflow-y-auto\"> {/* Adjust max height */} \n                {tests.map((test) => (\n                  <li \n                    key={test.id} \n                    className={`p-4 hover:bg-gray-700 cursor-pointer ${selectedTest?.id === test.id ? 'bg-gray-700' : ''}`}\n                    onClick={() => handleTestSelection(test)}\n                  >\n                     <div className=\"flex justify-between items-center\">\n                       <p className=\"text-sm font-medium text-text-primary truncate flex-1 mr-2\">{test.name}</p>\n                       <span className={`px-2 py-0.5 rounded-full text-xs font-medium capitalize ${getBadgeBgColor(getStatusStyle(test.status || 'draft').color)}`}>\n                         {getStatusStyle(test.status || 'draft').text}\n                       </span>\n                     </div>\n                     <p className=\"text-xs text-text-secondary mt-1\">Type: {test.testType}</p>\n                   </li>\n                ))}\n              </ul>\n           )}\n        </Card>\n\n        {/* Test Details & Results - Column 3 */} \n        <Card className=\"lg:col-span-1\"> \n           <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Test Details & Results</h2>\n           <div className=\"p-4\">\n             {selectedTest ? (\n               <div className=\"space-y-4\">\n                 <div>\n                   <h3 className=\"text-md font-medium text-text-primary mb-1\">{selectedTest.name}</h3>\n                   <p className=\"text-sm text-text-secondary mb-2\">{selectedTest.description}</p>\n                   <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium capitalize ${getBadgeBgColor(getStatusStyle(selectedTest.status || 'draft').color)} mb-2`}>\n                     Status: {getStatusStyle(selectedTest.status || 'draft').text}\n                   </span>\n                   <p className=\"text-sm text-text-secondary\">Type: {selectedTest.testType}</p>\n                   <p className=\"text-sm text-text-secondary\">Audience: {selectedTest.audienceSize}%</p>\n                   <p className=\"text-sm text-text-secondary\">Criteria: {selectedTest.winningCriteria}</p>\n                   {selectedTest.segmentId && <p className=\"text-sm text-text-secondary\">Segment: {segments.find(s=>s.id === selectedTest.segmentId)?.name || 'Unknown'}</p>}\n                 </div>\n\n                 <div className=\"space-y-1\">\n                    <h4 className=\"text-sm font-medium text-text-secondary\">Variant A:</h4>\n                    <p className=\"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words\">{selectedTest.variants?.a}</p>\n                 </div>\n                  <div className=\"space-y-1\">\n                    <h4 className=\"text-sm font-medium text-text-secondary\">Variant B:</h4>\n                    <p className=\"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words\">{selectedTest.variants?.b}</p>\n                 </div>\n                 \n                 {/* Actions */} \n                 {(selectedTest.status === 'draft' || selectedTest.status === 'paused') && (\n                     <Button onClick={handleStartTest} size=\"sm\" className=\"btn-cta\">Start Test</Button>\n                 )}\n                 {selectedTest.status === 'running' && (\n                     <Button onClick={handleCancelTest} variant=\"danger\" size=\"sm\">Cancel Test</Button>\n                 )}\n                 \n                 {/* Results */} \n                 {testResults ? (\n                   <div className=\"mt-4 pt-4 border-t border-gray-700\">\n                      <h3 className=\"text-md font-medium text-text-primary mb-2\">Results</h3>\n                      {testResults.winner ? (\n                          <Alert type=\"success\" message={`Winner: Variant ${testResults.winner.toUpperCase()} (${testResults.confidence} confidence)`} className=\"mb-2\" />\n                      ) : testResults.status === 'completed' ? (\n                          <Alert type=\"info\" message=\"Test completed, inconclusive result.\" className=\"mb-2\" />\n                      ) : (\n                          <Alert type=\"info\" message=\"Test is still running...\" className=\"mb-2\" />\n                      )}\n                      \n                      {/* Simplified results display */} \n                      <div className=\"text-sm text-text-secondary space-y-1\">\n                         <p>Variant A ({testResults.criteria}): {testResults.results?.a?.rate || 'N/A'}%</p>\n                         <p>Variant B ({testResults.criteria}): {testResults.results?.b?.rate || 'N/A'}%</p>\n                         {/* Add more detailed stats if needed */}\n                      </div>\n                   </div>\n                 ) : selectedTest.status === 'running' || selectedTest.status === 'completed' ? (\n                   <p className=\"text-sm text-text-secondary italic mt-4\">Loading results...</p>\n                 ) : null}\n                 \n               </div>\n             ) : (\n                <p className=\"text-text-secondary italic text-center\">Select a test from the list to see details and results.</p>\n             )}\n           </div>\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default ABTesting;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAGVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,IAAI,KAAM,oBAAoB,CACrC,MAAO,CAAAC,KAAK,KAAM,qBAAqB,CACvC,OAASC,aAAa,KAAQ,aAAa,CAE3C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA2CA,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,KAAAC,cAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAChC,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAW,EAAE,CAAC,CAChD,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAU,IAAI,CAAC,CACrD,KAAM,CAACsB,KAAK,CAAEC,QAAQ,CAAC,CAAGvB,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACwB,YAAY,CAAEC,eAAe,CAAC,CAAGzB,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAqB,IAAI,CAAC,CACxE,KAAM,CAAC4B,cAAc,CAAEC,iBAAiB,CAAC,CAAG7B,QAAQ,CAAU,KAAK,CAAC,CAEpE;AACA,KAAM,CAAC8B,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,CACrCgC,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,SAAS,CACnBC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,YAAY,CAAE,EAAE,CAChBC,eAAe,CAAE,WAAW,CAC5BC,SAAS,CAAE,EACb,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,QAAmB,CAAG,CAC1B,CAAEC,EAAE,CAAE,UAAU,CAAET,IAAI,CAAE,cAAe,CAAC,CACxC,CAAES,EAAE,CAAE,UAAU,CAAET,IAAI,CAAE,iBAAkB,CAAC,CAC3C,CAAES,EAAE,CAAE,UAAU,CAAET,IAAI,CAAE,gBAAiB,CAAC,CAC1C,CAAES,EAAE,CAAE,UAAU,CAAET,IAAI,CAAE,iBAAkB,CAAC,CAC5C,CAED;AACAjC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2C,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7BrB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAoB,QAAQ,CAAG,KAAM,CAAAtC,aAAa,CAACuC,YAAY,CAAC,CAAC,CACnD,GAAID,QAAQ,CAACE,OAAO,CAAE,CACpB1B,QAAQ,CAACwB,QAAQ,CAACG,IAAI,CAAC,CACzB,CAAC,IAAM,CACLvB,QAAQ,CAACoB,QAAQ,CAACI,OAAO,EAAI,2BAA2B,CAAC,CAC3D,CACF,CAAE,MAAOC,GAAQ,CAAE,CACjBzB,QAAQ,CAACyB,GAAG,CAACD,OAAO,EAAI,wCAAwC,CAAC,CACnE,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDqB,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3C,SAAS,CAAC,IAAM,CACd,GAAI,CAACyB,YAAY,EAAKA,YAAY,CAACyB,MAAM,GAAK,SAAS,EAAIzB,YAAY,CAACyB,MAAM,GAAK,WAAY,CAAE,CAC7FtB,cAAc,CAAC,IAAI,CAAC,CACpB,OACJ,CAEA,KAAM,CAAAuB,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnCrB,iBAAiB,CAAC,IAAI,CAAC,CACvB,GAAI,CACF,KAAM,CAAAc,QAAQ,CAAG,KAAM,CAAAtC,aAAa,CAAC8C,cAAc,CAAC3B,YAAY,CAACiB,EAAE,CAAC,CACpE,GAAIE,QAAQ,CAACE,OAAO,CAAE,CACpBlB,cAAc,CAACgB,QAAQ,CAACG,IAAI,CAAC,CAC/B,CAAC,IAAM,CACHM,OAAO,CAAC9B,KAAK,CAAC,+BAA+B,CAAEqB,QAAQ,CAACI,OAAO,CAAC,CAChEpB,cAAc,CAAC,IAAI,CAAC,CACxB,CACF,CAAE,MAAOqB,GAAG,CAAE,CACZI,OAAO,CAAC9B,KAAK,CAAC,8BAA8B,CAAE0B,GAAG,CAAC,CAClDrB,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,OAAS,CACNE,iBAAiB,CAAC,KAAK,CAAC,CAC5B,CACF,CAAC,CAEDqB,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAAC1B,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAA6B,gBAAgB,CAAG,KAAO,CAAAC,CAAY,EAAK,CAC/CA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAACzB,OAAO,CAACE,IAAI,CAACwB,IAAI,CAAC,CAAC,EAAI,CAAC1B,OAAO,CAACK,QAAQ,CAACqB,IAAI,CAAC,CAAC,EAAI,CAAC1B,OAAO,CAACM,QAAQ,CAACoB,IAAI,CAAC,CAAC,CAAE,CAChFjC,QAAQ,CAAC,0CAA0C,CAAC,CACpD,OACF,CAEA,GAAIO,OAAO,CAACO,YAAY,CAAG,CAAC,EAAIP,OAAO,CAACO,YAAY,CAAG,GAAG,CAAE,CACxDd,QAAQ,CAAC,0CAA0C,CAAC,CACpD,OACJ,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CACF,KAAM,CAAAkC,QAAqB,CAAG,CAC5BzB,IAAI,CAAEF,OAAO,CAACE,IAAI,CAClBC,WAAW,CAAEH,OAAO,CAACG,WAAW,EAAIyB,SAAS,CAC7CxB,QAAQ,CAAEJ,OAAO,CAACI,QAAQ,CAC1ByB,QAAQ,CAAE,CACRC,CAAC,CAAE9B,OAAO,CAACK,QAAQ,CACnB0B,CAAC,CAAE/B,OAAO,CAACM,QACb,CAAC,CACDC,YAAY,CAAEP,OAAO,CAACO,YAAY,CAClCC,eAAe,CAAER,OAAO,CAACQ,eAAe,CACxCC,SAAS,CAAET,OAAO,CAACS,SAAS,EAAImB,SAClC,CAAC,CACD,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAtC,aAAa,CAACyD,UAAU,CAACL,QAAQ,CAAC,CAEzD,GAAId,QAAQ,CAACE,OAAO,CAAE,CACpB1B,QAAQ,CAAC,CAAC,GAAGD,KAAK,CAAEyB,QAAQ,CAACG,IAAI,CAAC,CAAC,CACnCf,UAAU,CAAC,CACTC,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,SAAS,CACnBC,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EAAE,CACZC,YAAY,CAAE,EAAE,CAChBC,eAAe,CAAE,WAAW,CAC5BC,SAAS,CAAE,EACb,CAAC,CAAC,CACJ,CAAC,IAAM,CACLhB,QAAQ,CAACoB,QAAQ,CAACI,OAAO,EAAI,2BAA2B,CAAC,CAC3D,CACF,CAAE,MAAOC,GAAQ,CAAE,CACjBzB,QAAQ,CAACyB,GAAG,CAACD,OAAO,EAAI,uCAAuC,CAAC,CAClE,CAAC,OAAS,CACR1B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA0C,mBAAmB,CAAIC,IAAY,EAAK,CAC5C,GAAI,CAAAxC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiB,EAAE,IAAKuB,IAAI,CAACvB,EAAE,CAAE,OAClChB,eAAe,CAACuC,IAAI,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAACzC,YAAY,EAAIA,YAAY,CAACyB,MAAM,GAAK,OAAO,CAAE,OAEtD5B,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAtC,aAAa,CAAC6D,SAAS,CAAC1C,YAAY,CAACiB,EAAE,CAAC,CAC/D,GAAIE,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAsB,WAAW,CAAG,CAAE,GAAG3C,YAAY,CAAEyB,MAAM,CAAE,SAAU,CAAC,CAC1D9B,QAAQ,CAACD,KAAK,CAACkD,GAAG,CAACJ,IAAI,EACrBA,IAAI,CAACvB,EAAE,GAAKjB,YAAY,CAACiB,EAAE,CAAG0B,WAAW,CAAGH,IAC9C,CAAC,CAAC,CACFvC,eAAe,CAAC0C,WAAW,CAAC,CAC9B,CAAC,IAAM,CACH5C,QAAQ,CAACoB,QAAQ,CAACI,OAAO,EAAI,sBAAsB,CAAC,CACxD,CACF,CAAE,MAAOC,GAAQ,CAAE,CACjBzB,QAAQ,CAACyB,GAAG,CAACD,OAAO,EAAI,qBAAqB,CAAC,CAC9CK,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,CAAE0B,GAAG,CAAC,CAC5C,CAAC,OAAS,CACN3B,UAAU,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAgD,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAAC7C,YAAY,EAAIA,YAAY,CAACyB,MAAM,GAAK,SAAS,CAAE,OAExD,GAAI,CAACqB,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,CAAE,OAE3ElD,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAAtC,aAAa,CAACmE,UAAU,CAAChD,YAAY,CAACiB,EAAE,CAAC,CAChE,GAAIE,QAAQ,CAACE,OAAO,CAAE,CACpB,KAAM,CAAAsB,WAAW,CAAG,CAAE,GAAG3C,YAAY,CAAEyB,MAAM,CAAE,WAAY,CAAC,CAC5D9B,QAAQ,CAACD,KAAK,CAACkD,GAAG,CAACJ,IAAI,EACrBA,IAAI,CAACvB,EAAE,GAAKjB,YAAY,CAACiB,EAAE,CAAG0B,WAAW,CAAGH,IAC9C,CAAC,CAAC,CACFvC,eAAe,CAAC0C,WAAW,CAAC,CAC9B,CAAC,IAAM,CACH5C,QAAQ,CAACoB,QAAQ,CAACI,OAAO,EAAI,uBAAuB,CAAC,CACzD,CACF,CAAE,MAAOC,GAAQ,CAAE,CACjBzB,QAAQ,CAACyB,GAAG,CAACD,OAAO,EAAI,uBAAuB,CAAC,CAChDK,OAAO,CAAC9B,KAAK,CAAC,wBAAwB,CAAE0B,GAAG,CAAC,CAC9C,CAAC,OAAS,CACN3B,UAAU,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAoD,cAAc,CAAIxB,MAAwB,EAAsC,CACnFA,MAAM,CAAGA,MAAM,EAAI,OAAO,CAC1B,OAAQA,MAAM,EACb,IAAK,OAAO,CAAE,MAAO,CAAEyB,KAAK,CAAE,MAAM,CAAEC,IAAI,CAAE,OAAQ,CAAC,CACrD,IAAK,SAAS,CAAE,MAAO,CAAED,KAAK,CAAE,MAAM,CAAEC,IAAI,CAAE,SAAU,CAAC,CACzD,IAAK,WAAW,CAAE,MAAO,CAAED,KAAK,CAAE,OAAO,CAAEC,IAAI,CAAE,WAAY,CAAC,CAC9D,IAAK,WAAW,CAAE,MAAO,CAAED,KAAK,CAAE,KAAK,CAAEC,IAAI,CAAE,WAAY,CAAC,CAC5D,QAAS,MAAO,CAAED,KAAK,CAAE,MAAM,CAAEC,IAAI,CAAE1B,MAAO,CAAC,CACjD,CACF,CAAC,CAED;AACA,KAAM,CAAA2B,eAAe,CAAIF,KAAa,EAAa,CAC/C,OAAOA,KAAK,EACR,IAAK,MAAM,CAAE,MAAO,2BAA2B,CAC/C,IAAK,MAAM,CAAE,MAAO,2BAA2B,CAC/C,IAAK,OAAO,CAAE,MAAO,6BAA6B,CAClD,IAAK,KAAK,CAAE,MAAO,yBAAyB,CAC5C,QAAS,MAAO,2BAA2B,CAC/C,CACJ,CAAC,CAED;AACA,mBACEjE,KAAA,QAAKoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CvE,IAAA,QAAKsE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACpDvE,IAAA,OAAIsE,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,CAChF,CAAC,cACLvE,IAAA,MAAGsE,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,+DAExC,CAAG,CAAC,CAEJxD,KAAK,eACHf,IAAA,CAACN,KAAK,EAAC8E,IAAI,CAAC,OAAO,CAAChC,OAAO,CAAEzB,KAAM,CAAC0D,OAAO,CAAEA,CAAA,GAAMzD,QAAQ,CAAC,IAAI,CAAE,CAACsD,SAAS,CAAC,MAAM,CAAE,CACvF,cAEDpE,KAAA,QAAKoE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDrE,KAAA,CAACN,IAAI,EAAC0E,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BvE,IAAA,OAAIsE,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC9GrE,KAAA,SAAMwE,QAAQ,CAAE5B,gBAAiB,CAACwB,SAAS,CAAC,eAAe,CAAAC,QAAA,eACzDvE,IAAA,CAACH,KAAK,EACH8E,KAAK,CAAC,WAAW,CACjBzC,EAAE,CAAC,UAAU,CACbT,IAAI,CAAC,UAAU,CACfmD,KAAK,CAAErD,OAAO,CAACE,IAAK,CACpBoD,QAAQ,CAAG9B,CAAgC,EAAKvB,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEE,IAAI,CAAEsB,CAAC,CAAC+B,MAAM,CAACF,KAAM,CAAC,CAAE,CACjGG,WAAW,CAAC,+BAA+B,CAC3CC,QAAQ,MACV,CAAC,cACF9E,KAAA,QAAAqE,QAAA,eACIvE,IAAA,UAAOiF,OAAO,CAAC,iBAAiB,CAACX,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,wBAEhG,CAAO,CAAC,cACRvE,IAAA,aACGkC,EAAE,CAAC,iBAAiB,CACpBT,IAAI,CAAC,iBAAiB,CACtByD,IAAI,CAAE,CAAE,CACRN,KAAK,CAAErD,OAAO,CAACG,WAAY,CAC3BmD,QAAQ,CAAG9B,CAAmC,EAAKvB,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEG,WAAW,CAAEqB,CAAC,CAAC+B,MAAM,CAACF,KAAM,CAAC,CAAE,CAC3GG,WAAW,CAAC,+BAA+B,CAC3CT,SAAS,CAAC,iMAAiM,CAC7M,CAAC,EACD,CAAC,cACNpE,KAAA,QAAAqE,QAAA,eACGvE,IAAA,UAAOiF,OAAO,CAAC,UAAU,CAACX,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC1GrE,KAAA,WACIgC,EAAE,CAAC,UAAU,CACbT,IAAI,CAAC,UAAU,CACfmD,KAAK,CAAErD,OAAO,CAACI,QAAS,CACxBkD,QAAQ,CAAG9B,CAAiC,EAAKvB,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEI,QAAQ,CAAEoB,CAAC,CAAC+B,MAAM,CAACF,KAAM,CAAC,CAAE,CACtGN,SAAS,CAAC,4KAA4K,CAAAC,QAAA,eAEvLvE,IAAA,WAAQ4E,KAAK,CAAC,SAAS,CAAAL,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC7CvE,IAAA,WAAQ4E,KAAK,CAAC,SAAS,CAAAL,QAAA,CAAC,eAAa,CAAQ,CAAC,cAC9CvE,IAAA,WAAQ4E,KAAK,CAAC,QAAQ,CAAAL,QAAA,CAAC,aAAW,CAAQ,CAAC,cAC3CvE,IAAA,WAAQ4E,KAAK,CAAC,KAAK,CAAAL,QAAA,CAAC,gBAAc,CAAQ,CAAC,cAC3CvE,IAAA,WAAQ4E,KAAK,CAAC,MAAM,CAAAL,QAAA,CAAC,WAAS,CAAQ,CAAC,EAClC,CAAC,EACP,CAAC,cACNvE,IAAA,CAACH,KAAK,EACH8E,KAAK,CAAC,WAAW,CACjBzC,EAAE,CAAC,UAAU,CACbT,IAAI,CAAC,UAAU,CACfmD,KAAK,CAAErD,OAAO,CAACK,QAAS,CACxBiD,QAAQ,CAAG9B,CAAgC,EAAKvB,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEK,QAAQ,CAAEmB,CAAC,CAAC+B,MAAM,CAACF,KAAM,CAAC,CAAE,CACrGG,WAAW,CAAExD,OAAO,CAACI,QAAQ,GAAK,SAAS,CAAG,mCAAmC,CAAGJ,OAAO,CAACI,QAAQ,GAAK,SAAS,CAAG,+BAA+B,CAAG,iBAAkB,CACzKqD,QAAQ,MACT,CAAC,cACHhF,IAAA,CAACH,KAAK,EACH8E,KAAK,CAAC,WAAW,CACjBzC,EAAE,CAAC,UAAU,CACbT,IAAI,CAAC,UAAU,CACfmD,KAAK,CAAErD,OAAO,CAACM,QAAS,CACxBgD,QAAQ,CAAG9B,CAAgC,EAAKvB,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEM,QAAQ,CAAEkB,CAAC,CAAC+B,MAAM,CAACF,KAAM,CAAC,CAAE,CACrGG,WAAW,CAAExD,OAAO,CAACI,QAAQ,GAAK,SAAS,CAAG,+BAA+B,CAAGJ,OAAO,CAACI,QAAQ,GAAK,SAAS,CAAG,+BAA+B,CAAG,iBAAkB,CACrKqD,QAAQ,MACV,CAAC,cACDhF,IAAA,CAACH,KAAK,EACJ8E,KAAK,CAAC,mBAAmB,CACzBzC,EAAE,CAAC,cAAc,CACjBT,IAAI,CAAC,cAAc,CACnB+C,IAAI,CAAC,QAAQ,CACbI,KAAK,CAAErD,OAAO,CAACO,YAAY,CAACqD,QAAQ,CAAC,CAAE,CACvCN,QAAQ,CAAG9B,CAAgC,EAAK,CAC5C,KAAM,CAAAqC,GAAG,CAAGC,QAAQ,CAACtC,CAAC,CAAC+B,MAAM,CAACF,KAAK,CAAC,CACpC,GAAI,CAACU,KAAK,CAACF,GAAG,CAAC,EAAIA,GAAG,EAAI,CAAC,EAAIA,GAAG,EAAI,GAAG,CAAE,CACvC5D,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEO,YAAY,CAAEsD,GAAI,CAAC,CAAC,CACjD,CAAC,IAAM,IAAIrC,CAAC,CAAC+B,MAAM,CAACF,KAAK,GAAK,EAAE,CAAE,CAC9BpD,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEO,YAAY,CAAE,CAAE,CAAC,CAAC,CAC/C,CACJ,CAAE,CACH,CAAC,cACF5B,KAAA,QAAAqE,QAAA,eACIvE,IAAA,UAAOiF,OAAO,CAAC,iBAAiB,CAACX,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,kBAAgB,CAAO,CAAC,cACxHrE,KAAA,WACEgC,EAAE,CAAC,iBAAiB,CACpBT,IAAI,CAAC,iBAAiB,CACtBmD,KAAK,CAAErD,OAAO,CAACQ,eAAgB,CAC/B8C,QAAQ,CAAG9B,CAAiC,EAAKvB,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAEQ,eAAe,CAAEgB,CAAC,CAAC+B,MAAM,CAACF,KAAM,CAAC,CAAE,CAC7GN,SAAS,CAAC,4KAA4K,CAAAC,QAAA,eAEpLvE,IAAA,WAAQ4E,KAAK,CAAC,WAAW,CAAAL,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5CvE,IAAA,WAAQ4E,KAAK,CAAC,YAAY,CAAAL,QAAA,CAAC,YAAU,CAAQ,CAAC,cAC9CvE,IAAA,WAAQ4E,KAAK,CAAC,iBAAiB,CAAAL,QAAA,CAAC,iBAAe,CAAQ,CAAC,EACpD,CAAC,EACR,CAAC,cACNrE,KAAA,QAAAqE,QAAA,eACIvE,IAAA,UAAOiF,OAAO,CAAC,WAAW,CAACX,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,oBAAkB,CAAO,CAAC,cACpHrE,KAAA,WACEgC,EAAE,CAAC,WAAW,CACdT,IAAI,CAAC,WAAW,CAChBmD,KAAK,CAAErD,OAAO,CAACS,SAAU,CACzB6C,QAAQ,CAAG9B,CAAiC,EAAKvB,UAAU,CAAC,CAAE,GAAGD,OAAO,CAAES,SAAS,CAAEe,CAAC,CAAC+B,MAAM,CAACF,KAAM,CAAC,CAAE,CACvGN,SAAS,CAAC,4KAA4K,CAAAC,QAAA,eAEpLvE,IAAA,WAAQ4E,KAAK,CAAC,EAAE,CAAAL,QAAA,CAAC,gBAAc,CAAQ,CAAC,CACvCtC,QAAQ,CAAC4B,GAAG,CAAC0B,CAAC,eACXvF,IAAA,WAAmB4E,KAAK,CAAEW,CAAC,CAACrD,EAAG,CAAAqC,QAAA,CAAEgB,CAAC,CAAC9D,IAAI,EAA1B8D,CAAC,CAACrD,EAAiC,CACnD,CAAC,EACE,CAAC,EACR,CAAC,cACPlC,IAAA,QAAKsE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACnCvE,IAAA,CAACL,MAAM,EAAC6E,IAAI,CAAC,QAAQ,CAACgB,QAAQ,CAAE3E,OAAQ,CAACyD,SAAS,CAAC,SAAS,CAAAC,QAAA,CACzD1D,OAAO,CAAG,aAAa,CAAG,aAAa,CAClC,CAAC,CACP,CAAC,EACF,CAAC,EACH,CAAC,cAGPX,KAAA,CAACN,IAAI,EAAC0E,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BvE,IAAA,OAAIsE,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,CACvG1D,OAAO,EAAIF,KAAK,CAAC8E,MAAM,GAAK,CAAC,cAC1BzF,IAAA,QAAKsE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,kBAAgB,CAAK,CAAC,CAC3E5D,KAAK,CAAC8E,MAAM,GAAK,CAAC,cAClBzF,IAAA,QAAKsE,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,2BAAyB,CAAK,CAAC,cAErFrE,KAAA,OAAIoE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,EAAC,GAAC,CACrE5D,KAAK,CAACkD,GAAG,CAAEJ,IAAI,eACdvD,KAAA,OAEEoE,SAAS,CAAE,wCAAwC,CAAArD,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEiB,EAAE,IAAKuB,IAAI,CAACvB,EAAE,CAAG,aAAa,CAAG,EAAE,EAAG,CACvGwD,OAAO,CAAEA,CAAA,GAAMlC,mBAAmB,CAACC,IAAI,CAAE,CAAAc,QAAA,eAExCrE,KAAA,QAAKoE,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDvE,IAAA,MAAGsE,SAAS,CAAC,4DAA4D,CAAAC,QAAA,CAAEd,IAAI,CAAChC,IAAI,CAAI,CAAC,cACzFzB,IAAA,SAAMsE,SAAS,CAAE,2DAA2DD,eAAe,CAACH,cAAc,CAACT,IAAI,CAACf,MAAM,EAAI,OAAO,CAAC,CAACyB,KAAK,CAAC,EAAG,CAAAI,QAAA,CACzIL,cAAc,CAACT,IAAI,CAACf,MAAM,EAAI,OAAO,CAAC,CAAC0B,IAAI,CACxC,CAAC,EACJ,CAAC,cACNlE,KAAA,MAAGoE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAAC,QAAM,CAACd,IAAI,CAAC9B,QAAQ,EAAI,CAAC,GAVrE8B,IAAI,CAACvB,EAWP,CACN,CAAC,EACA,CACN,EACE,CAAC,cAGPhC,KAAA,CAACN,IAAI,EAAC0E,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BvE,IAAA,OAAIsE,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,cAChHvE,IAAA,QAAKsE,SAAS,CAAC,KAAK,CAAAC,QAAA,CACjBtD,YAAY,cACXf,KAAA,QAAKoE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBrE,KAAA,QAAAqE,QAAA,eACEvE,IAAA,OAAIsE,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAEtD,YAAY,CAACQ,IAAI,CAAK,CAAC,cACnFzB,IAAA,MAAGsE,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEtD,YAAY,CAACS,WAAW,CAAI,CAAC,cAC9ExB,KAAA,SAAMoE,SAAS,CAAE,wEAAwED,eAAe,CAACH,cAAc,CAACjD,YAAY,CAACyB,MAAM,EAAI,OAAO,CAAC,CAACyB,KAAK,CAAC,OAAQ,CAAAI,QAAA,EAAC,UAC7J,CAACL,cAAc,CAACjD,YAAY,CAACyB,MAAM,EAAI,OAAO,CAAC,CAAC0B,IAAI,EACxD,CAAC,cACPlE,KAAA,MAAGoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,QAAM,CAACtD,YAAY,CAACU,QAAQ,EAAI,CAAC,cAC5EzB,KAAA,MAAGoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,YAAU,CAACtD,YAAY,CAACa,YAAY,CAAC,GAAC,EAAG,CAAC,cACrF5B,KAAA,MAAGoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,YAAU,CAACtD,YAAY,CAACc,eAAe,EAAI,CAAC,CACtFd,YAAY,CAACe,SAAS,eAAI9B,KAAA,MAAGoE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,WAAS,CAAC,EAAAnE,cAAA,CAAA6B,QAAQ,CAAC0D,IAAI,CAACJ,CAAC,EAAEA,CAAC,CAACrD,EAAE,GAAKjB,YAAY,CAACe,SAAS,CAAC,UAAA5B,cAAA,iBAAjDA,cAAA,CAAmDqB,IAAI,GAAI,SAAS,EAAI,CAAC,EACtJ,CAAC,cAENvB,KAAA,QAAKoE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBvE,IAAA,OAAIsE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cACvEvE,IAAA,MAAGsE,SAAS,CAAC,mEAAmE,CAAAC,QAAA,EAAAlE,qBAAA,CAAEY,YAAY,CAACmC,QAAQ,UAAA/C,qBAAA,iBAArBA,qBAAA,CAAuBgD,CAAC,CAAI,CAAC,EAC7G,CAAC,cACLnD,KAAA,QAAKoE,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBvE,IAAA,OAAIsE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cACvEvE,IAAA,MAAGsE,SAAS,CAAC,mEAAmE,CAAAC,QAAA,EAAAjE,sBAAA,CAAEW,YAAY,CAACmC,QAAQ,UAAA9C,sBAAA,iBAArBA,sBAAA,CAAuBgD,CAAC,CAAI,CAAC,EAC7G,CAAC,CAGL,CAACrC,YAAY,CAACyB,MAAM,GAAK,OAAO,EAAIzB,YAAY,CAACyB,MAAM,GAAK,QAAQ,gBACjE1C,IAAA,CAACL,MAAM,EAAC+F,OAAO,CAAEhC,eAAgB,CAACkC,IAAI,CAAC,IAAI,CAACtB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,YAAU,CAAQ,CACrF,CACAtD,YAAY,CAACyB,MAAM,GAAK,SAAS,eAC9B1C,IAAA,CAACL,MAAM,EAAC+F,OAAO,CAAE5B,gBAAiB,CAAC+B,OAAO,CAAC,QAAQ,CAACD,IAAI,CAAC,IAAI,CAAArB,QAAA,CAAC,aAAW,CAAQ,CACpF,CAGApD,WAAW,cACVjB,KAAA,QAAKoE,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAChDvE,IAAA,OAAIsE,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,CACtEpD,WAAW,CAAC2E,MAAM,cACf9F,IAAA,CAACN,KAAK,EAAC8E,IAAI,CAAC,SAAS,CAAChC,OAAO,CAAE,mBAAmBrB,WAAW,CAAC2E,MAAM,CAACC,WAAW,CAAC,CAAC,KAAK5E,WAAW,CAAC6E,UAAU,cAAe,CAAC1B,SAAS,CAAC,MAAM,CAAE,CAAC,CAChJnD,WAAW,CAACuB,MAAM,GAAK,WAAW,cAClC1C,IAAA,CAACN,KAAK,EAAC8E,IAAI,CAAC,MAAM,CAAChC,OAAO,CAAC,sCAAsC,CAAC8B,SAAS,CAAC,MAAM,CAAE,CAAC,cAErFtE,IAAA,CAACN,KAAK,EAAC8E,IAAI,CAAC,MAAM,CAAChC,OAAO,CAAC,0BAA0B,CAAC8B,SAAS,CAAC,MAAM,CAAE,CAC3E,cAGDpE,KAAA,QAAKoE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACnDrE,KAAA,MAAAqE,QAAA,EAAG,aAAW,CAACpD,WAAW,CAAC8E,QAAQ,CAAC,KAAG,CAAC,EAAA1F,oBAAA,CAAAY,WAAW,CAAC+E,OAAO,UAAA3F,oBAAA,kBAAAC,qBAAA,CAAnBD,oBAAA,CAAqB8C,CAAC,UAAA7C,qBAAA,iBAAtBA,qBAAA,CAAwB2F,IAAI,GAAI,KAAK,CAAC,GAAC,EAAG,CAAC,cACnFjG,KAAA,MAAAqE,QAAA,EAAG,aAAW,CAACpD,WAAW,CAAC8E,QAAQ,CAAC,KAAG,CAAC,EAAAxF,qBAAA,CAAAU,WAAW,CAAC+E,OAAO,UAAAzF,qBAAA,kBAAAC,qBAAA,CAAnBD,qBAAA,CAAqB6C,CAAC,UAAA5C,qBAAA,iBAAtBA,qBAAA,CAAwByF,IAAI,GAAI,KAAK,CAAC,GAAC,EAAG,CAAC,EAEjF,CAAC,EACJ,CAAC,CACJlF,YAAY,CAACyB,MAAM,GAAK,SAAS,EAAIzB,YAAY,CAACyB,MAAM,GAAK,WAAW,cAC1E1C,IAAA,MAAGsE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,CAC3E,IAAI,EAEL,CAAC,cAELvE,IAAA,MAAGsE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,yDAAuD,CAAG,CAClH,CACE,CAAC,EACH,CAAC,EACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}