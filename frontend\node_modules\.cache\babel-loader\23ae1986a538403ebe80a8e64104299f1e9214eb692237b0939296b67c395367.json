{"ast": null, "code": "import { useMemo } from 'react';\nimport { DropTargetMonitorImpl } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nexport function useDropTargetMonitor() {\n  const manager = useDragDropManager();\n  return useMemo(() => new DropTargetMonitorImpl(manager), [manager]);\n}", "map": {"version": 3, "names": ["useMemo", "DropTargetMonitorImpl", "useDragDropManager", "useDropTargetMonitor", "manager"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd\\src\\hooks\\useDrop\\useDropTargetMonitor.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { DropTargetMonitorImpl } from '../../internals/index.js'\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\n\nexport function useDropTargetMonitor<O, R>(): DropTargetMonitor<O, R> {\n\tconst manager = useDragDropManager()\n\treturn useMemo(() => new DropTargetMonitorImpl(manager), [manager])\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAE/B,SAASC,qBAAqB,QAAQ,0BAA0B;AAEhE,SAASC,kBAAkB,QAAQ,0BAA0B;AAE7D,OAAO,SAASC,oBAAoBA,CAAA,EAAkC;EACrE,MAAMC,OAAO,GAAGF,kBAAkB,EAAE;EACpC,OAAOF,OAAO,CAAC,MAAM,IAAIC,qBAAqB,CAACG,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}