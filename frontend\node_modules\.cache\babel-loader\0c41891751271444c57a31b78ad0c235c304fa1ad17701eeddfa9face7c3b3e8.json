{"ast": null, "code": "import React from'react';import{DndProvider}from'react-dnd';import{HTML5Backend}from'react-dnd-html5-backend';import{HashRouter as Router,Navigate,Route,Routes}from'react-router-dom';import{EmailEditor}from'./components/EmailEditor';// <-- Import EmailEditor\nimport Layout from'./components/Layout';// Ensure Layout is imported\nimport{AuthProvider,useAuth}from'./contexts/AuthContext';import CampaignSummary from'./pages/campaigns/CampaignSummary';// Restore direct import\nimport Login from'./pages/Login';// Import directly\nimport Register from'./pages/Register';// Import directly\n// Lazy load components for better performance\nimport{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const Dashboard=/*#__PURE__*/React.lazy(()=>import('./pages/Dashboard'));const Templates=/*#__PURE__*/React.lazy(()=>import('./pages/Templates'));const TemplateForm=/*#__PURE__*/React.lazy(()=>import('./pages/templates/TemplateForm'));// Comment out the lazy import of AITemplateGenerator\n// const AITemplateGenerator = React.lazy(() => import('./pages/templates/AITemplateGenerator'));\nconst Contacts=/*#__PURE__*/React.lazy(()=>import('./pages/Contacts'));// Added Contacts import\nconst CampaignList=/*#__PURE__*/React.lazy(()=>import('./pages/campaigns/CampaignList'));const CampaignCreate=/*#__PURE__*/React.lazy(()=>import('./pages/campaigns/CampaignCreate'));const CampaignEdit=/*#__PURE__*/React.lazy(()=>import('./pages/campaigns/CampaignEdit'));const CampaignAnalytics=/*#__PURE__*/React.lazy(()=>import('./pages/campaigns/CampaignAnalytics'));const CampaignRecipients=/*#__PURE__*/React.lazy(()=>import('./pages/campaigns/CampaignRecipients'));const CampaignSchedule=/*#__PURE__*/React.lazy(()=>import('./pages/campaigns/CampaignSchedule'));const DomainSetup=/*#__PURE__*/React.lazy(()=>import('./pages/campaigns/DomainSetup'));const Analytics=/*#__PURE__*/React.lazy(()=>import('./pages/Analytics'));const Automations=/*#__PURE__*/React.lazy(()=>import('./pages/Automations'));const Settings=/*#__PURE__*/React.lazy(()=>import('./pages/Settings'));const Billing=/*#__PURE__*/React.lazy(()=>import('./pages/Billing'));const Support=/*#__PURE__*/React.lazy(()=>import('./pages/Support'));const AIContentGenerator=/*#__PURE__*/React.lazy(()=>import('./pages/AIContentGenerator'));const PersonalizationEditor=/*#__PURE__*/React.lazy(()=>import('./pages/PersonalizationEditor'));const InteractiveElements=/*#__PURE__*/React.lazy(()=>import('./pages/InteractiveElements'));const SendTimeOptimization=/*#__PURE__*/React.lazy(()=>import('./pages/SendTimeOptimization'));const ABTesting=/*#__PURE__*/React.lazy(()=>import('./pages/ABTesting'));const SegmentBuilder=/*#__PURE__*/React.lazy(()=>import('./pages/SegmentBuilder'));const DeliverabilityDashboard=/*#__PURE__*/React.lazy(()=>import('./pages/DeliverabilityDashboard'));const JourneyBuilder=/*#__PURE__*/React.lazy(()=>import('./pages/JourneyBuilder'));const IntegrationMarketplace=/*#__PURE__*/React.lazy(()=>import('./pages/IntegrationMarketplace'));const MobilePreview=/*#__PURE__*/React.lazy(()=>import('./pages/MobilePreview'));const TemplateRecommendations=/*#__PURE__*/React.lazy(()=>import('./pages/TemplateRecommendations'));const SchedulingAutomation=/*#__PURE__*/React.lazy(()=>import('./pages/AdvancedScheduling'));const DataExportImport=/*#__PURE__*/React.lazy(()=>import('./pages/DataExportImport'));const NotFound=/*#__PURE__*/React.lazy(()=>import('./pages/NotFound'));// Simple component to directly test useAuth (Optional, can be removed if not needed)\nconst ContextTester=()=>{try{const{loading,isAuthenticated}=useAuth();console.log('ContextTester: useAuth() called successfully');return/*#__PURE__*/_jsxs(\"div\",{style:{position:'fixed',top:0,left:0,backgroundColor:'lightgreen',padding:'5px',zIndex:9999},children:[\"Context Test: \",loading?'Loading...':isAuthenticated?'Authenticated':'Not Authenticated']});}catch(err){console.error('ContextTester: Error calling useAuth()',err);return/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,backgroundColor:'red',color:'white',padding:'5px',zIndex:9999},children:\"Context Test: Error! Check console.\"});}};// Loading component\nconst Loading=()=>/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-screen\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"})});// Protected route component - Modified to include Layout\nconst ProtectedRoute=_ref=>{let{children,title}=_ref;const{isAuthenticated,loading}=useAuth();if(loading){return/*#__PURE__*/_jsx(Loading,{});}if(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}// Render Layout, and wrap children in Suspense *inside* the Layout\nreturn/*#__PURE__*/_jsx(Layout,{title:title,children:/*#__PURE__*/_jsx(React.Suspense,{fallback:/*#__PURE__*/_jsx(Loading,{}),children:children})});};const App=()=>{return/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(DndProvider,{backend:HTML5Backend,children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(Register,{})}),/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Dashboard\",children:/*#__PURE__*/_jsx(Dashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-templates\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Templates\",children:/*#__PURE__*/_jsx(Templates,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-templates/create\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Create Template\",children:/*#__PURE__*/_jsx(TemplateForm,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-templates/editor/:templateId\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Edit Template\",children:/*#__PURE__*/_jsx(TemplateForm,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/contacts\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Contacts\",children:/*#__PURE__*/_jsx(Contacts,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaigns\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Campaigns\",children:/*#__PURE__*/_jsx(CampaignList,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaigns/create\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Create Campaign\",children:/*#__PURE__*/_jsx(CampaignCreate,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaigns/domain-setup\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Domain Setup\",children:/*#__PURE__*/_jsx(DomainSetup,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaigns/edit/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Edit Campaign\",children:/*#__PURE__*/_jsx(CampaignEdit,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaigns/analytics/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Campaign Analytics\",children:/*#__PURE__*/_jsx(CampaignAnalytics,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaigns/recipients/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Campaign Recipients\",children:/*#__PURE__*/_jsx(CampaignRecipients,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaigns/:id/schedule\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Campaign Schedule\",children:/*#__PURE__*/_jsx(CampaignSchedule,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/campaign-summary/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Campaign Summary\",children:/*#__PURE__*/_jsx(CampaignSummary,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/analytics\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Analytics\",children:/*#__PURE__*/_jsx(Analytics,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/automations\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Automations\",children:/*#__PURE__*/_jsx(Automations,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/settings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Settings\",children:/*#__PURE__*/_jsx(Settings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/billing\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Billing\",children:/*#__PURE__*/_jsx(Billing,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/support\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Support\",children:/*#__PURE__*/_jsx(Support,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/ai-content-generator\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"AI Content Generator\",children:/*#__PURE__*/_jsx(AIContentGenerator,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/personalization-editor\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Personalization Editor\",children:/*#__PURE__*/_jsx(PersonalizationEditor,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/interactive-elements\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Interactive Elements\",children:/*#__PURE__*/_jsx(InteractiveElements,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/send-time-optimization\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Send Time Optimization\",children:/*#__PURE__*/_jsx(SendTimeOptimization,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/ab-testing\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"AB Testing\",children:/*#__PURE__*/_jsx(ABTesting,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/segment-builder\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Segment Builder\",children:/*#__PURE__*/_jsx(SegmentBuilder,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/deliverability-dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Deliverability Dashboard\",children:/*#__PURE__*/_jsx(DeliverabilityDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/journey-builder\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Journey Builder\",children:/*#__PURE__*/_jsx(JourneyBuilder,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/integration-marketplace\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Integration Marketplace\",children:/*#__PURE__*/_jsx(IntegrationMarketplace,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/mobile-preview\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Mobile Preview\",children:/*#__PURE__*/_jsx(MobilePreview,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/template-recommendations\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Template Recommendations\",children:/*#__PURE__*/_jsx(TemplateRecommendations,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/scheduling-automation\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Scheduling Automation\",children:/*#__PURE__*/_jsx(SchedulingAutomation,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/data-export-import\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Data Export/Import\",children:/*#__PURE__*/_jsx(DataExportImport,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-editor\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Create Email Template\",children:/*#__PURE__*/_jsx(EmailEditor,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/email-editor/:templateId\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Edit Email Template\",children:/*#__PURE__*/_jsx(EmailEditor,{})})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(ProtectedRoute,{title:\"Not Found\",children:/*#__PURE__*/_jsx(NotFound,{})})})]})})})});};export default App;", "map": {"version": 3, "names": ["React", "DndProvider", "HTML5Backend", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Navigate", "Route", "Routes", "EmailEditor", "Layout", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "CampaignSummary", "<PERSON><PERSON>", "Register", "jsxs", "_jsxs", "jsx", "_jsx", "Dashboard", "lazy", "Templates", "TemplateForm", "Contacts", "CampaignList", "CampaignCreate", "CampaignEdit", "CampaignAnalytics", "CampaignRecipients", "CampaignSchedule", "DomainSetup", "Analytics", "Automations", "Settings", "Billing", "Support", "AIContentGenerator", "PersonalizationEditor", "InteractiveElements", "SendTimeOptimization", "ABTesting", "SegmentBuilder", "DeliverabilityDashboard", "JourneyBuilder", "IntegrationMarketplace", "MobilePreview", "TemplateRecommendations", "SchedulingAutomation", "DataExportImport", "NotFound", "ContextTester", "loading", "isAuthenticated", "console", "log", "style", "position", "top", "left", "backgroundColor", "padding", "zIndex", "children", "err", "error", "color", "Loading", "className", "ProtectedRoute", "_ref", "title", "to", "replace", "Suspense", "fallback", "App", "backend", "path", "element"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport {\n  HashRouter as Router,\n  Navigate,\n  Route,\n  Routes,\n} from 'react-router-dom';\n\nimport {\n  EmailEditor,\n} from './components/EmailEditor'; // <-- Import EmailEditor\nimport Layout from './components/Layout'; // Ensure Layout is imported\nimport {\n  AuthProvider,\n  useAuth,\n} from './contexts/AuthContext';\nimport CampaignSummary\n  from './pages/campaigns/CampaignSummary'; // Restore direct import\nimport Login from './pages/Login'; // Import directly\nimport Register from './pages/Register'; // Import directly\n\n// Lazy load components for better performance\nconst Dashboard = React.lazy(() => import('./pages/Dashboard'));\nconst Templates = React.lazy(() => import('./pages/Templates'));\nconst TemplateForm = React.lazy(() => import('./pages/templates/TemplateForm'));\n// Comment out the lazy import of AITemplateGenerator\n// const AITemplateGenerator = React.lazy(() => import('./pages/templates/AITemplateGenerator'));\nconst Contacts = React.lazy(() => import('./pages/Contacts')); // Added Contacts import\nconst CampaignList = React.lazy(() => import('./pages/campaigns/CampaignList'));\nconst CampaignCreate = React.lazy(() => import('./pages/campaigns/CampaignCreate'));\nconst CampaignEdit = React.lazy(() => import('./pages/campaigns/CampaignEdit'));\nconst CampaignAnalytics = React.lazy(() => import('./pages/campaigns/CampaignAnalytics'));\nconst CampaignRecipients = React.lazy(() => import('./pages/campaigns/CampaignRecipients'));\nconst CampaignSchedule = React.lazy(() => import('./pages/campaigns/CampaignSchedule'));\nconst DomainSetup = React.lazy(() => import('./pages/campaigns/DomainSetup'));\nconst Analytics = React.lazy(() => import('./pages/Analytics'));\nconst Automations = React.lazy(() => import('./pages/Automations'));\nconst Settings = React.lazy(() => import('./pages/Settings'));\nconst Billing = React.lazy(() => import('./pages/Billing'));\nconst Support = React.lazy(() => import('./pages/Support'));\nconst AIContentGenerator = React.lazy(() => import('./pages/AIContentGenerator'));\nconst PersonalizationEditor = React.lazy(() => import('./pages/PersonalizationEditor'));\nconst InteractiveElements = React.lazy(() => import('./pages/InteractiveElements'));\nconst SendTimeOptimization = React.lazy(() => import('./pages/SendTimeOptimization'));\nconst ABTesting = React.lazy(() => import('./pages/ABTesting'));\nconst SegmentBuilder = React.lazy(() => import('./pages/SegmentBuilder'));\nconst DeliverabilityDashboard = React.lazy(() => import('./pages/DeliverabilityDashboard'));\nconst JourneyBuilder = React.lazy(() => import('./pages/JourneyBuilder'));\nconst IntegrationMarketplace = React.lazy(() => import('./pages/IntegrationMarketplace'));\nconst MobilePreview = React.lazy(() => import('./pages/MobilePreview'));\nconst TemplateRecommendations = React.lazy(() => import('./pages/TemplateRecommendations'));\nconst SchedulingAutomation = React.lazy(() => import('./pages/AdvancedScheduling'));\nconst DataExportImport = React.lazy(() => import('./pages/DataExportImport'));\nconst NotFound = React.lazy(() => import('./pages/NotFound'));\n\n// Simple component to directly test useAuth (Optional, can be removed if not needed)\nconst ContextTester: React.FC = () => {\n  try {\n    const { loading, isAuthenticated } = useAuth();\n    console.log('ContextTester: useAuth() called successfully');\n    return (\n      <div style={{ position: 'fixed', top: 0, left: 0, backgroundColor: 'lightgreen', padding: '5px', zIndex: 9999 }}>\n        Context Test: {loading ? 'Loading...' : isAuthenticated ? 'Authenticated' : 'Not Authenticated'}\n      </div>\n    );\n  } catch (err) {\n    console.error('ContextTester: Error calling useAuth()', err);\n    return (\n      <div style={{ position: 'fixed', top: 0, left: 0, backgroundColor: 'red', color: 'white', padding: '5px', zIndex: 9999 }}>\n        Context Test: Error! Check console.\n      </div>\n    );\n  }\n};\n\n// Loading component\nconst Loading = () => (\n  <div className=\"flex items-center justify-center h-screen\">\n    <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n  </div>\n);\n\n// Protected route component - Modified to include Layout\nconst ProtectedRoute = ({ children, title }: { children: React.ReactNode, title: string }) => {\n  const { isAuthenticated, loading } = useAuth();\n\n  if (loading) {\n    return <Loading />;\n  }\n\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  // Render Layout, and wrap children in Suspense *inside* the Layout\n  return (\n    <Layout title={title}>\n      <React.Suspense fallback={<Loading />}>\n        {children}\n      </React.Suspense>\n    </Layout>\n  );\n};\n\nconst App: React.FC = () => {\n  return (\n    <AuthProvider>\n      <DndProvider backend={HTML5Backend}>\n        <Router>\n          <Routes>\n            {/* Public routes rendered directly */}\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/register\" element={<Register />} />\n\n            {/* Protected routes use ProtectedRoute */}\n            <Route\n              path=\"/\"\n              element={<ProtectedRoute title=\"Dashboard\"><Dashboard /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/email-templates\"\n              element={<ProtectedRoute title=\"Templates\"><Templates /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/email-templates/create\"\n              element={<ProtectedRoute title=\"Create Template\"><TemplateForm /></ProtectedRoute>}\n            />\n            {/* Comment out the route for AITemplateGenerator */}\n            {/* <Route\n              path=\"/ai-template-generator\"\n              element={<ProtectedRoute title=\"AI Template Generator\"><AITemplateGenerator /></ProtectedRoute>}\n            /> */}\n            <Route\n              path=\"/email-templates/editor/:templateId\"\n              element={<ProtectedRoute title=\"Edit Template\"><TemplateForm /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/contacts\"\n              element={<ProtectedRoute title=\"Contacts\"><Contacts /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaigns\"\n              element={<ProtectedRoute title=\"Campaigns\"><CampaignList /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaigns/create\"\n              element={<ProtectedRoute title=\"Create Campaign\"><CampaignCreate /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaigns/domain-setup\"\n              element={<ProtectedRoute title=\"Domain Setup\"><DomainSetup /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaigns/edit/:id\"\n              element={<ProtectedRoute title=\"Edit Campaign\"><CampaignEdit /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaigns/analytics/:id\"\n              element={<ProtectedRoute title=\"Campaign Analytics\"><CampaignAnalytics /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaigns/recipients/:id\"\n              element={<ProtectedRoute title=\"Campaign Recipients\"><CampaignRecipients /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaigns/:id/schedule\"\n              element={<ProtectedRoute title=\"Campaign Schedule\"><CampaignSchedule /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/campaign-summary/:id\"\n              element={<ProtectedRoute title=\"Campaign Summary\"><CampaignSummary /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/analytics\"\n              element={<ProtectedRoute title=\"Analytics\"><Analytics /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/automations\"\n              element={<ProtectedRoute title=\"Automations\"><Automations /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/settings\"\n              element={<ProtectedRoute title=\"Settings\"><Settings /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/billing\"\n              element={<ProtectedRoute title=\"Billing\"><Billing /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/support\"\n              element={<ProtectedRoute title=\"Support\"><Support /></ProtectedRoute>}\n            />\n            {/* Advanced Features Routes */}\n            <Route\n              path=\"/ai-content-generator\"\n              element={<ProtectedRoute title=\"AI Content Generator\"><AIContentGenerator /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/personalization-editor\"\n              element={<ProtectedRoute title=\"Personalization Editor\"><PersonalizationEditor /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/interactive-elements\"\n              element={<ProtectedRoute title=\"Interactive Elements\"><InteractiveElements /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/send-time-optimization\"\n              element={<ProtectedRoute title=\"Send Time Optimization\"><SendTimeOptimization /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/ab-testing\"\n              element={<ProtectedRoute title=\"AB Testing\"><ABTesting /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/segment-builder\"\n              element={<ProtectedRoute title=\"Segment Builder\"><SegmentBuilder /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/deliverability-dashboard\"\n              element={<ProtectedRoute title=\"Deliverability Dashboard\"><DeliverabilityDashboard /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/journey-builder\"\n              element={<ProtectedRoute title=\"Journey Builder\"><JourneyBuilder /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/integration-marketplace\"\n              element={<ProtectedRoute title=\"Integration Marketplace\"><IntegrationMarketplace /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/mobile-preview\"\n              element={<ProtectedRoute title=\"Mobile Preview\"><MobilePreview /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/template-recommendations\"\n              element={<ProtectedRoute title=\"Template Recommendations\"><TemplateRecommendations /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/scheduling-automation\"\n              element={<ProtectedRoute title=\"Scheduling Automation\"><SchedulingAutomation /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/data-export-import\"\n              element={<ProtectedRoute title=\"Data Export/Import\"><DataExportImport /></ProtectedRoute>}\n            />\n\n            {/* --- New Email Editor Routes --- */}\n            <Route\n              path=\"/email-editor\"\n              element={<ProtectedRoute title=\"Create Email Template\"><EmailEditor /></ProtectedRoute>}\n            />\n            <Route\n              path=\"/email-editor/:templateId\"\n              element={<ProtectedRoute title=\"Edit Email Template\"><EmailEditor /></ProtectedRoute>}\n            />\n            {/* --- End New Email Editor Routes --- */}\n\n            {/* Restore Catch-all route for 404 */}\n            <Route\n              path=\"*\"\n              element={<ProtectedRoute title=\"Not Found\"><NotFound /></ProtectedRoute>}\n            />\n          </Routes>\n        </Router>\n      </DndProvider>\n    </AuthProvider>\n  );\n};\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,OAASC,WAAW,KAAQ,WAAW,CACvC,OAASC,YAAY,KAAQ,yBAAyB,CACtD,OACEC,UAAU,GAAI,CAAAC,MAAM,CACpBC,QAAQ,CACRC,KAAK,CACLC,MAAM,KACD,kBAAkB,CAEzB,OACEC,WAAW,KACN,0BAA0B,CAAE;AACnC,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CAAE;AAC1C,OACEC,YAAY,CACZC,OAAO,KACF,wBAAwB,CAC/B,MAAO,CAAAC,eAAe,KACf,mCAAmC,CAAE;AAC5C,MAAO,CAAAC,KAAK,KAAM,eAAe,CAAE;AACnC,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CAAE;AAEzC;AAAA,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBACA,KAAM,CAAAC,SAAS,cAAGnB,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAC,SAAS,cAAGrB,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAE,YAAY,cAAGtB,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAC/E;AACA;AACA,KAAM,CAAAG,QAAQ,cAAGvB,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAE;AAC/D,KAAM,CAAAI,YAAY,cAAGxB,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAC/E,KAAM,CAAAK,cAAc,cAAGzB,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAC,CACnF,KAAM,CAAAM,YAAY,cAAG1B,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CAC/E,KAAM,CAAAO,iBAAiB,cAAG3B,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC,CACzF,KAAM,CAAAQ,kBAAkB,cAAG5B,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC,CAC3F,KAAM,CAAAS,gBAAgB,cAAG7B,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC,CACvF,KAAM,CAAAU,WAAW,cAAG9B,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CAC7E,KAAM,CAAAW,SAAS,cAAG/B,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAY,WAAW,cAAGhC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC,CACnE,KAAM,CAAAa,QAAQ,cAAGjC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAC7D,KAAM,CAAAc,OAAO,cAAGlC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAC3D,KAAM,CAAAe,OAAO,cAAGnC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAC3D,KAAM,CAAAgB,kBAAkB,cAAGpC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CACjF,KAAM,CAAAiB,qBAAqB,cAAGrC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC,CACvF,KAAM,CAAAkB,mBAAmB,cAAGtC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC,CACnF,KAAM,CAAAmB,oBAAoB,cAAGvC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAC,CACrF,KAAM,CAAAoB,SAAS,cAAGxC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC,CAC/D,KAAM,CAAAqB,cAAc,cAAGzC,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC,CACzE,KAAM,CAAAsB,uBAAuB,cAAG1C,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAC3F,KAAM,CAAAuB,cAAc,cAAG3C,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC,CACzE,KAAM,CAAAwB,sBAAsB,cAAG5C,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC,CACzF,KAAM,CAAAyB,aAAa,cAAG7C,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,uBAAuB,CAAC,CAAC,CACvE,KAAM,CAAA0B,uBAAuB,cAAG9C,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAC3F,KAAM,CAAA2B,oBAAoB,cAAG/C,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,4BAA4B,CAAC,CAAC,CACnF,KAAM,CAAA4B,gBAAgB,cAAGhD,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAC7E,KAAM,CAAA6B,QAAQ,cAAGjD,KAAK,CAACoB,IAAI,CAAC,IAAM,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAE7D;AACA,KAAM,CAAA8B,aAAuB,CAAGA,CAAA,GAAM,CACpC,GAAI,CACF,KAAM,CAAEC,OAAO,CAAEC,eAAgB,CAAC,CAAGzC,OAAO,CAAC,CAAC,CAC9C0C,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC,CAC3D,mBACEtC,KAAA,QAAKuC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAO,CAAEC,GAAG,CAAE,CAAC,CAAEC,IAAI,CAAE,CAAC,CAAEC,eAAe,CAAE,YAAY,CAAEC,OAAO,CAAE,KAAK,CAAEC,MAAM,CAAE,IAAK,CAAE,CAAAC,QAAA,EAAC,gBACjG,CAACX,OAAO,CAAG,YAAY,CAAGC,eAAe,CAAG,eAAe,CAAG,mBAAmB,EAC5F,CAAC,CAEV,CAAE,MAAOW,GAAG,CAAE,CACZV,OAAO,CAACW,KAAK,CAAC,wCAAwC,CAAED,GAAG,CAAC,CAC5D,mBACE7C,IAAA,QAAKqC,KAAK,CAAE,CAAEC,QAAQ,CAAE,OAAO,CAAEC,GAAG,CAAE,CAAC,CAAEC,IAAI,CAAE,CAAC,CAAEC,eAAe,CAAE,KAAK,CAAEM,KAAK,CAAE,OAAO,CAAEL,OAAO,CAAE,KAAK,CAAEC,MAAM,CAAE,IAAK,CAAE,CAAAC,QAAA,CAAC,qCAE1H,CAAK,CAAC,CAEV,CACF,CAAC,CAED;AACA,KAAM,CAAAI,OAAO,CAAGA,CAAA,gBACdhD,IAAA,QAAKiD,SAAS,CAAC,2CAA2C,CAAAL,QAAA,cACxD5C,IAAA,QAAKiD,SAAS,CAAC,0EAA0E,CAAM,CAAC,CAC7F,CACN,CAED;AACA,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAuE,IAAtE,CAAEP,QAAQ,CAAEQ,KAAoD,CAAC,CAAAD,IAAA,CACvF,KAAM,CAAEjB,eAAe,CAAED,OAAQ,CAAC,CAAGxC,OAAO,CAAC,CAAC,CAE9C,GAAIwC,OAAO,CAAE,CACX,mBAAOjC,IAAA,CAACgD,OAAO,GAAE,CAAC,CACpB,CAEA,GAAI,CAACd,eAAe,CAAE,CACpB,mBAAOlC,IAAA,CAACb,QAAQ,EAACkE,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CACzC,CAEA;AACA,mBACEtD,IAAA,CAACT,MAAM,EAAC6D,KAAK,CAAEA,KAAM,CAAAR,QAAA,cACnB5C,IAAA,CAAClB,KAAK,CAACyE,QAAQ,EAACC,QAAQ,cAAExD,IAAA,CAACgD,OAAO,GAAE,CAAE,CAAAJ,QAAA,CACnCA,QAAQ,CACK,CAAC,CACX,CAAC,CAEb,CAAC,CAED,KAAM,CAAAa,GAAa,CAAGA,CAAA,GAAM,CAC1B,mBACEzD,IAAA,CAACR,YAAY,EAAAoD,QAAA,cACX5C,IAAA,CAACjB,WAAW,EAAC2E,OAAO,CAAE1E,YAAa,CAAA4D,QAAA,cACjC5C,IAAA,CAACd,MAAM,EAAA0D,QAAA,cACL9C,KAAA,CAACT,MAAM,EAAAuD,QAAA,eAEL5C,IAAA,CAACZ,KAAK,EAACuE,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAE5D,IAAA,CAACL,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CK,IAAA,CAACZ,KAAK,EAACuE,IAAI,CAAC,WAAW,CAACC,OAAO,cAAE5D,IAAA,CAACJ,QAAQ,GAAE,CAAE,CAAE,CAAC,cAGjDI,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,GAAG,CACRC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,WAAW,CAAAR,QAAA,cAAC5C,IAAA,CAACC,SAAS,GAAE,CAAC,CAAgB,CAAE,CAC3E,CAAC,cACFD,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,WAAW,CAAAR,QAAA,cAAC5C,IAAA,CAACG,SAAS,GAAE,CAAC,CAAgB,CAAE,CAC3E,CAAC,cACFH,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,yBAAyB,CAC9BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,iBAAiB,CAAAR,QAAA,cAAC5C,IAAA,CAACI,YAAY,GAAE,CAAC,CAAgB,CAAE,CACpF,CAAC,cAMFJ,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,qCAAqC,CAC1CC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,eAAe,CAAAR,QAAA,cAAC5C,IAAA,CAACI,YAAY,GAAE,CAAC,CAAgB,CAAE,CAClF,CAAC,cACFJ,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,WAAW,CAChBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,UAAU,CAAAR,QAAA,cAAC5C,IAAA,CAACK,QAAQ,GAAE,CAAC,CAAgB,CAAE,CACzE,CAAC,cACFL,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,YAAY,CACjBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,WAAW,CAAAR,QAAA,cAAC5C,IAAA,CAACM,YAAY,GAAE,CAAC,CAAgB,CAAE,CAC9E,CAAC,cACFN,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,iBAAiB,CAAAR,QAAA,cAAC5C,IAAA,CAACO,cAAc,GAAE,CAAC,CAAgB,CAAE,CACtF,CAAC,cACFP,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,yBAAyB,CAC9BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,cAAc,CAAAR,QAAA,cAAC5C,IAAA,CAACY,WAAW,GAAE,CAAC,CAAgB,CAAE,CAChF,CAAC,cACFZ,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,qBAAqB,CAC1BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,eAAe,CAAAR,QAAA,cAAC5C,IAAA,CAACQ,YAAY,GAAE,CAAC,CAAgB,CAAE,CAClF,CAAC,cACFR,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,0BAA0B,CAC/BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,oBAAoB,CAAAR,QAAA,cAAC5C,IAAA,CAACS,iBAAiB,GAAE,CAAC,CAAgB,CAAE,CAC5F,CAAC,cACFT,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,2BAA2B,CAChCC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,qBAAqB,CAAAR,QAAA,cAAC5C,IAAA,CAACU,kBAAkB,GAAE,CAAC,CAAgB,CAAE,CAC9F,CAAC,cACFV,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,yBAAyB,CAC9BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,mBAAmB,CAAAR,QAAA,cAAC5C,IAAA,CAACW,gBAAgB,GAAE,CAAC,CAAgB,CAAE,CAC1F,CAAC,cACFX,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,uBAAuB,CAC5BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,kBAAkB,CAAAR,QAAA,cAAC5C,IAAA,CAACN,eAAe,GAAE,CAAC,CAAgB,CAAE,CACxF,CAAC,cACFM,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,YAAY,CACjBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,WAAW,CAAAR,QAAA,cAAC5C,IAAA,CAACa,SAAS,GAAE,CAAC,CAAgB,CAAE,CAC3E,CAAC,cACFb,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,cAAc,CACnBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,aAAa,CAAAR,QAAA,cAAC5C,IAAA,CAACc,WAAW,GAAE,CAAC,CAAgB,CAAE,CAC/E,CAAC,cACFd,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,WAAW,CAChBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,UAAU,CAAAR,QAAA,cAAC5C,IAAA,CAACe,QAAQ,GAAE,CAAC,CAAgB,CAAE,CACzE,CAAC,cACFf,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,UAAU,CACfC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,SAAS,CAAAR,QAAA,cAAC5C,IAAA,CAACgB,OAAO,GAAE,CAAC,CAAgB,CAAE,CACvE,CAAC,cACFhB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,UAAU,CACfC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,SAAS,CAAAR,QAAA,cAAC5C,IAAA,CAACiB,OAAO,GAAE,CAAC,CAAgB,CAAE,CACvE,CAAC,cAEFjB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,uBAAuB,CAC5BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,sBAAsB,CAAAR,QAAA,cAAC5C,IAAA,CAACkB,kBAAkB,GAAE,CAAC,CAAgB,CAAE,CAC/F,CAAC,cACFlB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,yBAAyB,CAC9BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,wBAAwB,CAAAR,QAAA,cAAC5C,IAAA,CAACmB,qBAAqB,GAAE,CAAC,CAAgB,CAAE,CACpG,CAAC,cACFnB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,uBAAuB,CAC5BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,sBAAsB,CAAAR,QAAA,cAAC5C,IAAA,CAACoB,mBAAmB,GAAE,CAAC,CAAgB,CAAE,CAChG,CAAC,cACFpB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,yBAAyB,CAC9BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,wBAAwB,CAAAR,QAAA,cAAC5C,IAAA,CAACqB,oBAAoB,GAAE,CAAC,CAAgB,CAAE,CACnG,CAAC,cACFrB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,aAAa,CAClBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,YAAY,CAAAR,QAAA,cAAC5C,IAAA,CAACsB,SAAS,GAAE,CAAC,CAAgB,CAAE,CAC5E,CAAC,cACFtB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,iBAAiB,CAAAR,QAAA,cAAC5C,IAAA,CAACuB,cAAc,GAAE,CAAC,CAAgB,CAAE,CACtF,CAAC,cACFvB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,2BAA2B,CAChCC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,0BAA0B,CAAAR,QAAA,cAAC5C,IAAA,CAACwB,uBAAuB,GAAE,CAAC,CAAgB,CAAE,CACxG,CAAC,cACFxB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,iBAAiB,CAAAR,QAAA,cAAC5C,IAAA,CAACyB,cAAc,GAAE,CAAC,CAAgB,CAAE,CACtF,CAAC,cACFzB,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,0BAA0B,CAC/BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,yBAAyB,CAAAR,QAAA,cAAC5C,IAAA,CAAC0B,sBAAsB,GAAE,CAAC,CAAgB,CAAE,CACtG,CAAC,cACF1B,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,iBAAiB,CACtBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,gBAAgB,CAAAR,QAAA,cAAC5C,IAAA,CAAC2B,aAAa,GAAE,CAAC,CAAgB,CAAE,CACpF,CAAC,cACF3B,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,2BAA2B,CAChCC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,0BAA0B,CAAAR,QAAA,cAAC5C,IAAA,CAAC4B,uBAAuB,GAAE,CAAC,CAAgB,CAAE,CACxG,CAAC,cACF5B,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,wBAAwB,CAC7BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,uBAAuB,CAAAR,QAAA,cAAC5C,IAAA,CAAC6B,oBAAoB,GAAE,CAAC,CAAgB,CAAE,CAClG,CAAC,cACF7B,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,qBAAqB,CAC1BC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,oBAAoB,CAAAR,QAAA,cAAC5C,IAAA,CAAC8B,gBAAgB,GAAE,CAAC,CAAgB,CAAE,CAC3F,CAAC,cAGF9B,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,eAAe,CACpBC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,uBAAuB,CAAAR,QAAA,cAAC5C,IAAA,CAACV,WAAW,GAAE,CAAC,CAAgB,CAAE,CACzF,CAAC,cACFU,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,2BAA2B,CAChCC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,qBAAqB,CAAAR,QAAA,cAAC5C,IAAA,CAACV,WAAW,GAAE,CAAC,CAAgB,CAAE,CACvF,CAAC,cAIFU,IAAA,CAACZ,KAAK,EACJuE,IAAI,CAAC,GAAG,CACRC,OAAO,cAAE5D,IAAA,CAACkD,cAAc,EAACE,KAAK,CAAC,WAAW,CAAAR,QAAA,cAAC5C,IAAA,CAAC+B,QAAQ,GAAE,CAAC,CAAgB,CAAE,CAC1E,CAAC,EACI,CAAC,CACH,CAAC,CACE,CAAC,CACF,CAAC,CAEnB,CAAC,CAED,cAAe,CAAA0B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}