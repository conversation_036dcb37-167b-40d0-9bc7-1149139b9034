{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TextBlock=_ref=>{let{content,type}=_ref;// Basic rendering, can be expanded\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4 p-4 border border-gray-600 rounded bg-gray-800\",children:[content.headline&&/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold mb-1 text-gray-100\",children:content.headline}),content.body&&/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-300\",children:content.body})]});};export default TextBlock;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "TextBlock", "_ref", "content", "type", "className", "children", "headline", "body"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/emailBlocks/TextBlock.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface TextBlockProps {\r\n  content: {\r\n    headline?: string;\r\n    body?: string;\r\n    // Could add other simple text fields if needed for different block types\r\n  };\r\n  type: string; // Pass type to potentially style differently\r\n}\r\n\r\nconst TextBlock: React.FC<TextBlockProps> = ({ content, type }) => {\r\n  // Basic rendering, can be expanded\r\n  return (\r\n    <div className=\"mb-4 p-4 border border-gray-600 rounded bg-gray-800\">\r\n      {content.headline && <h3 className=\"text-lg font-semibold mb-1 text-gray-100\">{content.headline}</h3>}\r\n      {content.body && <p className=\"text-gray-300\">{content.body}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TextBlock; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAW1B,KAAM,CAAAC,SAAmC,CAAGC,IAAA,EAAuB,IAAtB,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAAF,IAAA,CAC5D;AACA,mBACEF,KAAA,QAAKK,SAAS,CAAC,qDAAqD,CAAAC,QAAA,EACjEH,OAAO,CAACI,QAAQ,eAAIT,IAAA,OAAIO,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAEH,OAAO,CAACI,QAAQ,CAAK,CAAC,CACpGJ,OAAO,CAACK,IAAI,eAAIV,IAAA,MAAGO,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEH,OAAO,CAACK,IAAI,CAAI,CAAC,EAC7D,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}