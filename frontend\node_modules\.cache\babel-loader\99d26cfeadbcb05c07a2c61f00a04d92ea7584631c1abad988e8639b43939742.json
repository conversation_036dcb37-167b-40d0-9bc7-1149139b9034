{"ast": null, "code": "import React,{useEffect,useState}from'react';import Alert from'components/Alert';import Button from'components/Button';import Card from'components/Card';import Input from'components/Input';import{useAuth}from'contexts/AuthContext';import{useNavigate}from'react-router-dom';import api from'services/api';// Import the configured api instance\n// Define types for DNS records for better clarity\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DomainSetup=()=>{const{user}=useAuth();const navigate=useNavigate();// Log user object immediately upon receiving it from context\nconsole.log('[DomainSetup] User object from useAuth():',JSON.stringify(user));const[domain,setDomain]=useState('');const[domainId,setDomainId]=useState('');const[loading,setLoading]=useState(false);const[verifying,setVerifying]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[dnsRecords,setDnsRecords]=useState([]);const[verificationStatus,setVerificationStatus]=useState(null);// Default to null\nconst[initialCheckDone,setInitialCheckDone]=useState(false);// Effect to check initial domain status on load\nuseEffect(()=>{console.log(`[DomainSetup useEffect] Running check. initialCheckDone: ${initialCheckDone}, user:`,JSON.stringify(user));const checkInitialStatus=async()=>{var _user$domain;// Make outer function async\nlet initialStatus=null;let existingDomainName='';if(user!==null&&user!==void 0&&(_user$domain=user.domain)!==null&&_user$domain!==void 0&&_user$domain.name){existingDomainName=user.domain.name;setDomain(existingDomainName);if(user.domain.status==='active'){console.log(`[DomainSetup useEffect] Found ACTIVE domain in user object: ${existingDomainName}`);initialStatus='active';// Removed 'Domain previously verified' message\nsetDnsRecords([]);// Clear records for active state\n}else if(user.domain.status==='pending'){console.log(`[DomainSetup useEffect] Found PENDING domain in user object: ${existingDomainName}. Setting status to pending & fetching records.`);initialStatus='pending';setSuccess('Existing domain verification is pending. Please add DNS records or click Check Status.');// --- Fetch DNS Records for Pending Domain ---\ntry{var _response$data,_response$data$data;setLoading(true);// Indicate loading while fetching records\nconst response=await api.get('/domains/records');// Call GET /api/v1/domains/records\nconst fetchedRecords=response===null||response===void 0?void 0:(_response$data=response.data)===null||_response$data===void 0?void 0:(_response$data$data=_response$data.data)===null||_response$data$data===void 0?void 0:_response$data$data.records;if(fetchedRecords&&fetchedRecords.length>0){console.log('[DomainSetup useEffect] Fetched DNS records for pending domain:',fetchedRecords);setDnsRecords(fetchedRecords);}else{console.error('[DomainSetup useEffect] API returned success but no records for pending domain.');setError('Could not retrieve DNS records for the pending domain. Please try cancelling and restarting.');setDnsRecords([]);// Keep empty\n}}catch(fetchErr){var _fetchErr$response,_fetchErr$response$da;console.error('[DomainSetup useEffect] Error fetching DNS records for pending domain:',fetchErr);setError(((_fetchErr$response=fetchErr.response)===null||_fetchErr$response===void 0?void 0:(_fetchErr$response$da=_fetchErr$response.data)===null||_fetchErr$response$da===void 0?void 0:_fetchErr$response$da.message)||fetchErr.message||'Failed to fetch DNS records for pending domain.');setDnsRecords([]);// Keep empty on error\n}finally{setLoading(false);// Stop loading indicator\n}// --- End Fetch DNS Records ---\n}else{console.log(`[DomainSetup useEffect] Found domain in user object but status is neither active/pending: ${user.domain.status}. Treating as null.`);initialStatus=null;setDnsRecords([]);// Clear records\n}}else{console.log('[DomainSetup useEffect] No domain found in user object.');initialStatus=null;setDnsRecords([]);// Clear records\n}setVerificationStatus(initialStatus);setInitialCheckDone(true);console.log(`[DomainSetup useEffect] Check complete. verificationStatus set to: ${initialStatus}`);};if(!initialCheckDone&&user){// Ensure user is loaded before check\ncheckInitialStatus();}},[user,initialCheckDone]);const handleRegisterDomain=async()=>{if(!domain){setError('Please enter a domain name');return;}const domainRegex=/^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/;if(!domainRegex.test(domain)){setError('Please enter a valid domain name');return;}try{var _response$data2,_response$data2$data,_response$data3;setLoading(true);setError('');setSuccess('');setDnsRecords([]);// Clear previous records\nconsole.log(`Registering domain via API: ${domain}`);// Use 'api' instance and relative path\nconst response=await api.post('/domains/register',{// Path relative to baseURL\ndomain});// --- Process Response ---\nconst records=response===null||response===void 0?void 0:(_response$data2=response.data)===null||_response$data2===void 0?void 0:(_response$data2$data=_response$data2.data)===null||_response$data2$data===void 0?void 0:_response$data2$data.dnsRecords;console.log('API Response Records:',records);if(response!==null&&response!==void 0&&(_response$data3=response.data)!==null&&_response$data3!==void 0&&_response$data3.success&&records&&records.length>0){// ---> SUCCESS CASE <---\nsetDnsRecords(records);// Set REAL records from API response\nsetSuccess('Domain registration initiated with SES. Please add the following DNS records to verify your domain.');setVerificationStatus('pending');}else{var _response$data4;// ---> FAILURE CASE (including domain already registered) <---\n// Handle cases where API call might succeed technically (2xx status) but indicate failure logically (success: false),\n// or doesn't return the expected records.\nconst errorMessage=(response===null||response===void 0?void 0:(_response$data4=response.data)===null||_response$data4===void 0?void 0:_response$data4.message)||'API request failed or did not return expected DNS records.';console.error('Domain registration API error:',errorMessage,response===null||response===void 0?void 0:response.data);setError(errorMessage);// Use setError to display the specific message from backend (e.g., \"Domain already registered...\")\nsetVerificationStatus(null);// Stay on the input form\n}}catch(err){var _err$response,_err$response$data;// ---> NETWORK/SERVER ERROR CASE <---\n// Handle network errors or non-2xx responses from axios\nconsole.error('Axios error during domain registration:',err);// Try to get message from backend error response, otherwise use generic error\nconst backendErrorMessage=(_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message;setError(backendErrorMessage||err.message||'Failed to register domain with SES due to a network or server error.');setVerificationStatus(null);// Allow user to try again\n}finally{setLoading(false);}};const handleCheckVerification=async()=>{// Domain is derived from state, which should be set if status is pending\nif(!domain){setError('Cannot check verification without a domain name.');return;}try{var _response$data5,_response$data5$data;setVerifying(true);setError('');setSuccess('');console.log(`Checking verification via API for domain: ${domain}`);// Use 'api' instance and relative path\nconst response=await api.get('/domains/verify');// Path relative to baseURL\n// --- Process Response ---\nconst apiStatus=response===null||response===void 0?void 0:(_response$data5=response.data)===null||_response$data5===void 0?void 0:(_response$data5$data=_response$data5.data)===null||_response$data5$data===void 0?void 0:_response$data5$data.status;// Expect 'active' or 'pending'\nconsole.log('API Response Status:',apiStatus);// Use switch statement based on the actual API status\nswitch(apiStatus){case'active':// Match backend response status\nsetDnsRecords([]);// Clear records on success\nsetVerificationStatus('active');setSuccess('Domain verified successfully with SES! You can now send emails from this domain.');break;case'pending':// Match backend response status\n// Keep existing DNS records displayed (they don't change)\nsetSuccess('Domain verification is still pending with SES. Please ensure DNS records are correct and allow time for propagation (up to 48 hours).');setVerificationStatus('pending');// Keep status as 'pending'\nbreak;default:// Handle unexpected statuses from API\nconsole.error('Unexpected status received from verification API:',apiStatus);setError('Received an unexpected status while checking verification. Please try again later.');// Decide whether to keep pending state or reset\n// setVerificationStatus('pending');\nbreak;}}catch(err){var _err$response2,_err$response2$data;console.error('Axios error during domain verification check:',err);setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||err.message||'Failed to check domain verification status due to a network or server error.');// Keep the UI in pending state so user can see records and try again\nsetVerificationStatus('pending');}finally{setVerifying(false);}};// --- NEW: Handler for Revoke/Cancel Button ---\nconst handleRevokeVerification=async()=>{if(!domain){setError('No pending domain found to revoke.');return;}// Optional: Add a confirmation dialog\n// if (!window.confirm(`Are you sure you want to revoke the verification request for ${domain}? You will need to start over.`)) {\n//   return;\n// }\nsetLoading(true);// Use general loading state for simplicity, or add a dedicated one\nsetError('');setSuccess('');try{var _response$data6;console.log(`Revoking verification request for domain: ${domain}`);// --- REAL API CALL ---\nconst response=await api.delete('/domains/revoke');// DELETE request to the new endpoint\n// --- Remove Mock ---\n// await new Promise(resolve => setTimeout(resolve, 500));\n// const response = { data: { success: true, message: 'Verification revoked' } };\n// --- End Mock ---\nif(response!==null&&response!==void 0&&(_response$data6=response.data)!==null&&_response$data6!==void 0&&_response$data6.success){setSuccess(`Verification request for ${domain} has been revoked.`);setDomain('');// Clear domain state\nsetDomainId('');// Clear ID state if used\nsetDnsRecords([]);// Clear records\nsetVerificationStatus(null);// Reset status to show input form\n}else{var _response$data7;setError((response===null||response===void 0?void 0:(_response$data7=response.data)===null||_response$data7===void 0?void 0:_response$data7.message)||'Failed to revoke verification request.');}}catch(err){var _err$response3,_err$response3$data;console.error('Axios error during domain verification revoke:',err);setError(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.message)||err.message||'Failed to revoke verification due to a network or server error.');}finally{setLoading(false);}};// Remove console logs added previously for debugging initial state\n// console.log('DomainSetup - Initial user object:', user);\n// console.log('DomainSetup - Initial verificationStatus:', verificationStatus);\n// Render logic based on verificationStatus\nconst renderContent=()=>{if(!initialCheckDone){return/*#__PURE__*/_jsx(\"p\",{children:\"Loading domain status...\"});// Show loading initially\n}switch(verificationStatus){case'active':return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",className:\"h-4 w-4 text-white\",viewBox:\"0 0 20 20\",fill:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",clipRule:\"evenodd\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-lg font-medium\",children:\"Domain Verified\"})]}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-text-secondary mb-4\",children:[\"Your domain \",/*#__PURE__*/_jsx(\"strong\",{children:domain}),\" has been verified and is ready to use for sending emails.\"]}),/*#__PURE__*/_jsx(Button,{onClick:()=>navigate('/campaigns'),children:\"Go to Campaigns\"})]});case'pending':return/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-medium mb-2\",children:[\"Verification Pending for: \",/*#__PURE__*/_jsx(\"span\",{className:\"font-bold text-primary\",children:domain})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-4\",children:\"Add the following DNS records to your domain provider (e.g., GoDaddy, Cloudflare) to verify ownership with AWS SES:\"}),dnsRecords.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-800 p-4 rounded-md mb-4 overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"min-w-full\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"text-left text-text-secondary\",children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left text-text-secondary\",children:\"Name/Host\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left text-text-secondary\",children:\"Value/Points To\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:dnsRecords.map((record,index)=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"py-2 pr-4 break-all\",children:record.type}),/*#__PURE__*/_jsx(\"td\",{className:\"py-2 pr-4 break-all\",children:record.name}),/*#__PURE__*/_jsx(\"td\",{className:\"py-2 break-all\",children:record.value})]},index))})]})}):/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-4 italic\",children:\"DNS records are not currently available. Try checking status.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-4\",children:\"After adding these records, click the button below to check verification. DNS changes may take up to 48 hours to propagate.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 mt-4\",children:[\" \",/*#__PURE__*/_jsx(Button,{onClick:handleCheckVerification,disabled:verifying||loading,children:verifying?'Checking...':'Check Verification Status'}),/*#__PURE__*/_jsx(Button,{onClick:handleRevokeVerification,disabled:loading||verifying,variant:\"danger\"// Use a danger/warning style if available in your Button component\n,children:loading?'Cancelling...':'Cancel Request'})]})]});default:// null or any other case\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsx(Input,{id:\"domain\",name:\"domain\",label:\"Domain Name\",placeholder:\"yourdomain.com\",value:domain,onChange:e=>setDomain(e.target.value),required:true,className:\"mb-4\"}),/*#__PURE__*/_jsx(Input,{id:\"domainId\",name:\"domainId\",label:\"Domain ID (Optional)\"// Label as optional unless confirmed required\n,placeholder:\"Optional reference ID\",value:domainId,onChange:e=>setDomainId(e.target.value)// Removed required attribute\n,className:\"mb-4\"}),/*#__PURE__*/_jsx(Button,{onClick:handleRegisterDomain,disabled:loading,children:loading?'Registering with SES...':'Register Domain with SES'})]});}};return/*#__PURE__*/_jsxs(Card,{title:\"Email Sending Domain Setup (AWS SES)\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-6\",children:\"Register and verify a domain with AWS SES to use it for sending emails. Add the generated DNS records to your domain provider.\"}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(''),className:\"mb-4\"}),success&&/*#__PURE__*/_jsx(Alert,{type:\"success\",message:success/* Remove onClose if message should persist */,className:\"mb-4\"}),renderContent()]});};export default DomainSetup;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "Input", "useAuth", "useNavigate", "api", "jsx", "_jsx", "jsxs", "_jsxs", "DomainSetup", "user", "navigate", "console", "log", "JSON", "stringify", "domain", "setDomain", "domainId", "setDomainId", "loading", "setLoading", "verifying", "setVerifying", "error", "setError", "success", "setSuccess", "dnsRecords", "setDnsRecords", "verificationStatus", "setVerificationStatus", "initialCheckDone", "setInitialCheckDone", "checkInitialStatus", "_user$domain", "initialStatus", "existingDomainName", "name", "status", "_response$data", "_response$data$data", "response", "get", "fetchedRecords", "data", "records", "length", "fetchErr", "_fetchErr$response", "_fetchErr$response$da", "message", "handleRegisterDomain", "domainRegex", "test", "_response$data2", "_response$data2$data", "_response$data3", "post", "_response$data4", "errorMessage", "err", "_err$response", "_err$response$data", "backendErrorMessage", "handleCheckVerification", "_response$data5", "_response$data5$data", "api<PERSON><PERSON>us", "_err$response2", "_err$response2$data", "handleRevokeVerification", "_response$data6", "delete", "_response$data7", "_err$response3", "_err$response3$data", "renderContent", "children", "className", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "onClick", "map", "record", "index", "type", "value", "disabled", "variant", "id", "label", "placeholder", "onChange", "e", "target", "required", "title", "onClose"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/campaigns/DomainSetup.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport Input from 'components/Input';\nimport { useAuth } from 'contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport api from 'services/api'; // Import the configured api instance\n\n// Define types for DNS records for better clarity\ninterface DnsRecord {\n  type: string;\n  name: string;\n  value: string;\n}\n\nconst DomainSetup: React.FC = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // Log user object immediately upon receiving it from context\n  console.log('[DomainSetup] User object from useAuth():', JSON.stringify(user));\n\n  const [domain, setDomain] = useState('');\n  const [domainId, setDomainId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [verifying, setVerifying] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [dnsRecords, setDnsRecords] = useState<DnsRecord[]>([]);\n  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'active' | null>(null); // Default to null\n  const [initialCheckDone, setInitialCheckDone] = useState(false);\n\n  // Effect to check initial domain status on load\n  useEffect(() => {\n    console.log(`[DomainSetup useEffect] Running check. initialCheckDone: ${initialCheckDone}, user:`, JSON.stringify(user));\n\n    const checkInitialStatus = async () => { // Make outer function async\n      let initialStatus: 'pending' | 'active' | null = null;\n      let existingDomainName = '';\n\n      if (user?.domain?.name) {\n        existingDomainName = user.domain.name;\n        setDomain(existingDomainName);\n\n        if (user.domain.status === 'active') {\n          console.log(`[DomainSetup useEffect] Found ACTIVE domain in user object: ${existingDomainName}`);\n          initialStatus = 'active';\n          // Removed 'Domain previously verified' message\n          setDnsRecords([]); // Clear records for active state\n        } else if (user.domain.status === 'pending') {\n          console.log(`[DomainSetup useEffect] Found PENDING domain in user object: ${existingDomainName}. Setting status to pending & fetching records.`);\n          initialStatus = 'pending';\n          setSuccess('Existing domain verification is pending. Please add DNS records or click Check Status.');\n\n          // --- Fetch DNS Records for Pending Domain ---\n          try {\n            setLoading(true); // Indicate loading while fetching records\n            const response = await api.get('/domains/records'); // Call GET /api/v1/domains/records\n            const fetchedRecords = response?.data?.data?.records;\n            if (fetchedRecords && fetchedRecords.length > 0) {\n              console.log('[DomainSetup useEffect] Fetched DNS records for pending domain:', fetchedRecords);\n              setDnsRecords(fetchedRecords);\n            } else {\n              console.error('[DomainSetup useEffect] API returned success but no records for pending domain.');\n              setError('Could not retrieve DNS records for the pending domain. Please try cancelling and restarting.');\n              setDnsRecords([]); // Keep empty\n            }\n          } catch (fetchErr: any) {\n            console.error('[DomainSetup useEffect] Error fetching DNS records for pending domain:', fetchErr);\n            setError(fetchErr.response?.data?.message || fetchErr.message || 'Failed to fetch DNS records for pending domain.');\n            setDnsRecords([]); // Keep empty on error\n          } finally {\n             setLoading(false); // Stop loading indicator\n          }\n          // --- End Fetch DNS Records ---\n\n        } else {\n           console.log(`[DomainSetup useEffect] Found domain in user object but status is neither active/pending: ${user.domain.status}. Treating as null.`);\n           initialStatus = null;\n           setDnsRecords([]); // Clear records\n        }\n      } else {\n        console.log('[DomainSetup useEffect] No domain found in user object.');\n        initialStatus = null;\n        setDnsRecords([]); // Clear records\n      }\n\n      setVerificationStatus(initialStatus);\n      setInitialCheckDone(true);\n      console.log(`[DomainSetup useEffect] Check complete. verificationStatus set to: ${initialStatus}`);\n    };\n\n    if (!initialCheckDone && user) { // Ensure user is loaded before check\n      checkInitialStatus();\n    }\n\n  }, [user, initialCheckDone]);\n\n  const handleRegisterDomain = async () => {\n    if (!domain) {\n      setError('Please enter a domain name');\n      return;\n    }\n\n    const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/;\n    if (!domainRegex.test(domain)) {\n      setError('Please enter a valid domain name');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n      setSuccess('');\n      setDnsRecords([]); // Clear previous records\n\n      console.log(`Registering domain via API: ${domain}`);\n\n      // Use 'api' instance and relative path\n      const response = await api.post('/domains/register', { // Path relative to baseURL\n        domain\n      });\n\n      // --- Process Response ---\n      const records = response?.data?.data?.dnsRecords;\n      console.log('API Response Records:', records);\n\n      if (response?.data?.success && records && records.length > 0) {\n        // ---> SUCCESS CASE <---\n        setDnsRecords(records); // Set REAL records from API response\n        setSuccess('Domain registration initiated with SES. Please add the following DNS records to verify your domain.');\n        setVerificationStatus('pending');\n      } else {\n        // ---> FAILURE CASE (including domain already registered) <---\n        // Handle cases where API call might succeed technically (2xx status) but indicate failure logically (success: false),\n        // or doesn't return the expected records.\n        const errorMessage = response?.data?.message || 'API request failed or did not return expected DNS records.';\n        console.error('Domain registration API error:', errorMessage, response?.data);\n        setError(errorMessage); // Use setError to display the specific message from backend (e.g., \"Domain already registered...\")\n        setVerificationStatus(null); // Stay on the input form\n      }\n\n    } catch (err: any) {\n      // ---> NETWORK/SERVER ERROR CASE <---\n      // Handle network errors or non-2xx responses from axios\n      console.error('Axios error during domain registration:', err);\n      // Try to get message from backend error response, otherwise use generic error\n      const backendErrorMessage = err.response?.data?.message;\n      setError(backendErrorMessage || err.message || 'Failed to register domain with SES due to a network or server error.');\n      setVerificationStatus(null); // Allow user to try again\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCheckVerification = async () => {\n    // Domain is derived from state, which should be set if status is pending\n    if (!domain) {\n        setError('Cannot check verification without a domain name.');\n        return;\n    }\n    try {\n      setVerifying(true);\n      setError('');\n      setSuccess('');\n\n      console.log(`Checking verification via API for domain: ${domain}`);\n\n      // Use 'api' instance and relative path\n      const response = await api.get('/domains/verify'); // Path relative to baseURL\n\n      // --- Process Response ---\n      const apiStatus = response?.data?.data?.status; // Expect 'active' or 'pending'\n      console.log('API Response Status:', apiStatus);\n\n      // Use switch statement based on the actual API status\n      switch (apiStatus) {\n        case 'active': // Match backend response status\n          setDnsRecords([]); // Clear records on success\n          setVerificationStatus('active');\n          setSuccess('Domain verified successfully with SES! You can now send emails from this domain.');\n          break;\n        case 'pending': // Match backend response status\n          // Keep existing DNS records displayed (they don't change)\n          setSuccess('Domain verification is still pending with SES. Please ensure DNS records are correct and allow time for propagation (up to 48 hours).');\n          setVerificationStatus('pending'); // Keep status as 'pending'\n          break;\n        default: // Handle unexpected statuses from API\n          console.error('Unexpected status received from verification API:', apiStatus);\n          setError('Received an unexpected status while checking verification. Please try again later.');\n          // Decide whether to keep pending state or reset\n          // setVerificationStatus('pending');\n          break;\n      }\n\n    } catch (err: any) {\n      console.error('Axios error during domain verification check:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to check domain verification status due to a network or server error.');\n      // Keep the UI in pending state so user can see records and try again\n      setVerificationStatus('pending');\n    } finally {\n      setVerifying(false);\n    }\n  };\n\n  // --- NEW: Handler for Revoke/Cancel Button ---\n  const handleRevokeVerification = async () => {\n    if (!domain) {\n      setError('No pending domain found to revoke.');\n      return;\n    }\n    // Optional: Add a confirmation dialog\n    // if (!window.confirm(`Are you sure you want to revoke the verification request for ${domain}? You will need to start over.`)) {\n    //   return;\n    // }\n\n    setLoading(true); // Use general loading state for simplicity, or add a dedicated one\n    setError('');\n    setSuccess('');\n\n    try {\n      console.log(`Revoking verification request for domain: ${domain}`);\n\n      // --- REAL API CALL ---\n      const response = await api.delete('/domains/revoke'); // DELETE request to the new endpoint\n\n      // --- Remove Mock ---\n      // await new Promise(resolve => setTimeout(resolve, 500));\n      // const response = { data: { success: true, message: 'Verification revoked' } };\n      // --- End Mock ---\n\n      if (response?.data?.success) {\n        setSuccess(`Verification request for ${domain} has been revoked.`);\n        setDomain(''); // Clear domain state\n        setDomainId(''); // Clear ID state if used\n        setDnsRecords([]); // Clear records\n        setVerificationStatus(null); // Reset status to show input form\n      } else {\n        setError(response?.data?.message || 'Failed to revoke verification request.');\n      }\n\n    } catch (err: any) {\n      console.error('Axios error during domain verification revoke:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to revoke verification due to a network or server error.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Remove console logs added previously for debugging initial state\n  // console.log('DomainSetup - Initial user object:', user);\n  // console.log('DomainSetup - Initial verificationStatus:', verificationStatus);\n\n  // Render logic based on verificationStatus\n  const renderContent = () => {\n    if (!initialCheckDone) {\n      return <p>Loading domain status...</p>; // Show loading initially\n    }\n\n    switch (verificationStatus) {\n      case 'active':\n        return (\n          <div className=\"mb-6\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center mr-2\">\n                {/* Checkmark SVG */}\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-white\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" /></svg>\n              </div>\n              <span className=\"text-lg font-medium\">Domain Verified</span>\n            </div>\n            <p className=\"text-text-secondary mb-4\">\n              Your domain <strong>{domain}</strong> has been verified and is ready to use for sending emails.\n            </p>\n            <Button onClick={() => navigate('/campaigns')}>Go to Campaigns</Button>\n          </div>\n        );\n      case 'pending':\n        return (\n          <div className=\"mb-6\">\n            <h3 className=\"text-lg font-medium mb-2\">Verification Pending for: <span className=\"font-bold text-primary\">{domain}</span></h3>\n            <p className=\"text-text-secondary mb-4\">\n              Add the following DNS records to your domain provider (e.g., GoDaddy, Cloudflare) to verify ownership with AWS SES:\n            </p>\n            {dnsRecords.length > 0 ? (\n              <div className=\"bg-gray-800 p-4 rounded-md mb-4 overflow-x-auto\">\n                {/* DNS Table */}\n                <table className=\"min-w-full\">\n                  <thead><tr><th className=\"text-left text-text-secondary\">Type</th><th className=\"text-left text-text-secondary\">Name/Host</th><th className=\"text-left text-text-secondary\">Value/Points To</th></tr></thead>\n                  <tbody>\n                    {dnsRecords.map((record, index) => (\n                      <tr key={index}><td className=\"py-2 pr-4 break-all\">{record.type}</td><td className=\"py-2 pr-4 break-all\">{record.name}</td><td className=\"py-2 break-all\">{record.value}</td></tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            ) : (\n              <p className=\"text-text-secondary mb-4 italic\">DNS records are not currently available. Try checking status.</p>\n            )}\n            <p className=\"text-text-secondary mb-4\">\n              After adding these records, click the button below to check verification. DNS changes may take up to 48 hours to propagate.\n            </p>\n            <div className=\"flex items-center space-x-4 mt-4\"> {/* Button container */}\n              <Button onClick={handleCheckVerification} disabled={verifying || loading}>\n                {verifying ? 'Checking...' : 'Check Verification Status'}\n              </Button>\n              <Button\n                onClick={handleRevokeVerification}\n                disabled={loading || verifying}\n                variant=\"danger\" // Use a danger/warning style if available in your Button component\n              >\n                {loading ? 'Cancelling...' : 'Cancel Request'}\n              </Button>\n            </div>\n          </div>\n        );\n      default: // null or any other case\n        return (\n          <div className=\"mb-6\">\n            <Input\n              id=\"domain\"\n              name=\"domain\"\n              label=\"Domain Name\"\n              placeholder=\"yourdomain.com\"\n              value={domain}\n              onChange={(e) => setDomain(e.target.value)}\n              required\n              className=\"mb-4\"\n            />\n            <Input\n              id=\"domainId\"\n              name=\"domainId\"\n              label=\"Domain ID (Optional)\" // Label as optional unless confirmed required\n              placeholder=\"Optional reference ID\"\n              value={domainId}\n              onChange={(e) => setDomainId(e.target.value)}\n              // Removed required attribute\n              className=\"mb-4\"\n            />\n            <Button onClick={handleRegisterDomain} disabled={loading}>\n              {loading ? 'Registering with SES...' : 'Register Domain with SES'}\n            </Button>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <Card title=\"Email Sending Domain Setup (AWS SES)\">\n      <p className=\"text-text-secondary mb-6\">\n        Register and verify a domain with AWS SES to use it for sending emails. Add the generated DNS records to your domain provider.\n      </p>\n\n      {error && (\n        <Alert type=\"error\" message={error} onClose={() => setError('')} className=\"mb-4\" />\n      )}\n      {success && (\n        <Alert type=\"success\" message={success} /* Remove onClose if message should persist */ className=\"mb-4\" />\n      )}\n\n      {renderContent()}\n\n    </Card>\n  );\n};\n\nexport default DomainSetup;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,IAAI,KAAM,iBAAiB,CAClC,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC,OAASC,OAAO,KAAQ,sBAAsB,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,GAAG,KAAM,cAAc,CAAE;AAEhC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAOA,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAEC,IAAK,CAAC,CAAGR,OAAO,CAAC,CAAC,CAC1B,KAAM,CAAAS,QAAQ,CAAGR,WAAW,CAAC,CAAC,CAE9B;AACAS,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAEC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC,CAE9E,KAAM,CAACM,MAAM,CAAEC,SAAS,CAAC,CAAGpB,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAACqB,QAAQ,CAAEC,WAAW,CAAC,CAAGtB,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACuB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyB,SAAS,CAAEC,YAAY,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC2B,KAAK,CAAEC,QAAQ,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC6B,OAAO,CAAEC,UAAU,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC+B,UAAU,CAAEC,aAAa,CAAC,CAAGhC,QAAQ,CAAc,EAAE,CAAC,CAC7D,KAAM,CAACiC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlC,QAAQ,CAA8B,IAAI,CAAC,CAAE;AACjG,KAAM,CAACmC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAE/D;AACAD,SAAS,CAAC,IAAM,CACdgB,OAAO,CAACC,GAAG,CAAC,4DAA4DmB,gBAAgB,SAAS,CAAElB,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC,CAExH,KAAM,CAAAwB,kBAAkB,CAAG,KAAAA,CAAA,GAAY,KAAAC,YAAA,CAAE;AACvC,GAAI,CAAAC,aAA0C,CAAG,IAAI,CACrD,GAAI,CAAAC,kBAAkB,CAAG,EAAE,CAE3B,GAAI3B,IAAI,SAAJA,IAAI,YAAAyB,YAAA,CAAJzB,IAAI,CAAEM,MAAM,UAAAmB,YAAA,WAAZA,YAAA,CAAcG,IAAI,CAAE,CACtBD,kBAAkB,CAAG3B,IAAI,CAACM,MAAM,CAACsB,IAAI,CACrCrB,SAAS,CAACoB,kBAAkB,CAAC,CAE7B,GAAI3B,IAAI,CAACM,MAAM,CAACuB,MAAM,GAAK,QAAQ,CAAE,CACnC3B,OAAO,CAACC,GAAG,CAAC,+DAA+DwB,kBAAkB,EAAE,CAAC,CAChGD,aAAa,CAAG,QAAQ,CACxB;AACAP,aAAa,CAAC,EAAE,CAAC,CAAE;AACrB,CAAC,IAAM,IAAInB,IAAI,CAACM,MAAM,CAACuB,MAAM,GAAK,SAAS,CAAE,CAC3C3B,OAAO,CAACC,GAAG,CAAC,gEAAgEwB,kBAAkB,iDAAiD,CAAC,CAChJD,aAAa,CAAG,SAAS,CACzBT,UAAU,CAAC,wFAAwF,CAAC,CAEpG;AACA,GAAI,KAAAa,cAAA,CAAAC,mBAAA,CACFpB,UAAU,CAAC,IAAI,CAAC,CAAE;AAClB,KAAM,CAAAqB,QAAQ,CAAG,KAAM,CAAAtC,GAAG,CAACuC,GAAG,CAAC,kBAAkB,CAAC,CAAE;AACpD,KAAM,CAAAC,cAAc,CAAGF,QAAQ,SAARA,QAAQ,kBAAAF,cAAA,CAARE,QAAQ,CAAEG,IAAI,UAAAL,cAAA,kBAAAC,mBAAA,CAAdD,cAAA,CAAgBK,IAAI,UAAAJ,mBAAA,iBAApBA,mBAAA,CAAsBK,OAAO,CACpD,GAAIF,cAAc,EAAIA,cAAc,CAACG,MAAM,CAAG,CAAC,CAAE,CAC/CnC,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAE+B,cAAc,CAAC,CAC9Ff,aAAa,CAACe,cAAc,CAAC,CAC/B,CAAC,IAAM,CACLhC,OAAO,CAACY,KAAK,CAAC,iFAAiF,CAAC,CAChGC,QAAQ,CAAC,8FAA8F,CAAC,CACxGI,aAAa,CAAC,EAAE,CAAC,CAAE;AACrB,CACF,CAAE,MAAOmB,QAAa,CAAE,KAAAC,kBAAA,CAAAC,qBAAA,CACtBtC,OAAO,CAACY,KAAK,CAAC,wEAAwE,CAAEwB,QAAQ,CAAC,CACjGvB,QAAQ,CAAC,EAAAwB,kBAAA,CAAAD,QAAQ,CAACN,QAAQ,UAAAO,kBAAA,kBAAAC,qBAAA,CAAjBD,kBAAA,CAAmBJ,IAAI,UAAAK,qBAAA,iBAAvBA,qBAAA,CAAyBC,OAAO,GAAIH,QAAQ,CAACG,OAAO,EAAI,iDAAiD,CAAC,CACnHtB,aAAa,CAAC,EAAE,CAAC,CAAE;AACrB,CAAC,OAAS,CACPR,UAAU,CAAC,KAAK,CAAC,CAAE;AACtB,CACA;AAEF,CAAC,IAAM,CACJT,OAAO,CAACC,GAAG,CAAC,6FAA6FH,IAAI,CAACM,MAAM,CAACuB,MAAM,qBAAqB,CAAC,CACjJH,aAAa,CAAG,IAAI,CACpBP,aAAa,CAAC,EAAE,CAAC,CAAE;AACtB,CACF,CAAC,IAAM,CACLjB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CACtEuB,aAAa,CAAG,IAAI,CACpBP,aAAa,CAAC,EAAE,CAAC,CAAE;AACrB,CAEAE,qBAAqB,CAACK,aAAa,CAAC,CACpCH,mBAAmB,CAAC,IAAI,CAAC,CACzBrB,OAAO,CAACC,GAAG,CAAC,sEAAsEuB,aAAa,EAAE,CAAC,CACpG,CAAC,CAED,GAAI,CAACJ,gBAAgB,EAAItB,IAAI,CAAE,CAAE;AAC/BwB,kBAAkB,CAAC,CAAC,CACtB,CAEF,CAAC,CAAE,CAACxB,IAAI,CAAEsB,gBAAgB,CAAC,CAAC,CAE5B,KAAM,CAAAoB,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CAACpC,MAAM,CAAE,CACXS,QAAQ,CAAC,4BAA4B,CAAC,CACtC,OACF,CAEA,KAAM,CAAA4B,WAAW,CAAG,8EAA8E,CAClG,GAAI,CAACA,WAAW,CAACC,IAAI,CAACtC,MAAM,CAAC,CAAE,CAC7BS,QAAQ,CAAC,kCAAkC,CAAC,CAC5C,OACF,CAEA,GAAI,KAAA8B,eAAA,CAAAC,oBAAA,CAAAC,eAAA,CACFpC,UAAU,CAAC,IAAI,CAAC,CAChBI,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CACdE,aAAa,CAAC,EAAE,CAAC,CAAE;AAEnBjB,OAAO,CAACC,GAAG,CAAC,+BAA+BG,MAAM,EAAE,CAAC,CAEpD;AACA,KAAM,CAAA0B,QAAQ,CAAG,KAAM,CAAAtC,GAAG,CAACsD,IAAI,CAAC,mBAAmB,CAAE,CAAE;AACrD1C,MACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAA8B,OAAO,CAAGJ,QAAQ,SAARA,QAAQ,kBAAAa,eAAA,CAARb,QAAQ,CAAEG,IAAI,UAAAU,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBV,IAAI,UAAAW,oBAAA,iBAApBA,oBAAA,CAAsB5B,UAAU,CAChDhB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAEiC,OAAO,CAAC,CAE7C,GAAIJ,QAAQ,SAARA,QAAQ,YAAAe,eAAA,CAARf,QAAQ,CAAEG,IAAI,UAAAY,eAAA,WAAdA,eAAA,CAAgB/B,OAAO,EAAIoB,OAAO,EAAIA,OAAO,CAACC,MAAM,CAAG,CAAC,CAAE,CAC5D;AACAlB,aAAa,CAACiB,OAAO,CAAC,CAAE;AACxBnB,UAAU,CAAC,qGAAqG,CAAC,CACjHI,qBAAqB,CAAC,SAAS,CAAC,CAClC,CAAC,IAAM,KAAA4B,eAAA,CACL;AACA;AACA;AACA,KAAM,CAAAC,YAAY,CAAG,CAAAlB,QAAQ,SAARA,QAAQ,kBAAAiB,eAAA,CAARjB,QAAQ,CAAEG,IAAI,UAAAc,eAAA,iBAAdA,eAAA,CAAgBR,OAAO,GAAI,4DAA4D,CAC5GvC,OAAO,CAACY,KAAK,CAAC,gCAAgC,CAAEoC,YAAY,CAAElB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEG,IAAI,CAAC,CAC7EpB,QAAQ,CAACmC,YAAY,CAAC,CAAE;AACxB7B,qBAAqB,CAAC,IAAI,CAAC,CAAE;AAC/B,CAEF,CAAE,MAAO8B,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjB;AACA;AACAnD,OAAO,CAACY,KAAK,CAAC,yCAAyC,CAAEqC,GAAG,CAAC,CAC7D;AACA,KAAM,CAAAG,mBAAmB,EAAAF,aAAA,CAAGD,GAAG,CAACnB,QAAQ,UAAAoB,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcjB,IAAI,UAAAkB,kBAAA,iBAAlBA,kBAAA,CAAoBZ,OAAO,CACvD1B,QAAQ,CAACuC,mBAAmB,EAAIH,GAAG,CAACV,OAAO,EAAI,sEAAsE,CAAC,CACtHpB,qBAAqB,CAAC,IAAI,CAAC,CAAE;AAC/B,CAAC,OAAS,CACRV,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4C,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C;AACA,GAAI,CAACjD,MAAM,CAAE,CACTS,QAAQ,CAAC,kDAAkD,CAAC,CAC5D,OACJ,CACA,GAAI,KAAAyC,eAAA,CAAAC,oBAAA,CACF5C,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEdf,OAAO,CAACC,GAAG,CAAC,6CAA6CG,MAAM,EAAE,CAAC,CAElE;AACA,KAAM,CAAA0B,QAAQ,CAAG,KAAM,CAAAtC,GAAG,CAACuC,GAAG,CAAC,iBAAiB,CAAC,CAAE;AAEnD;AACA,KAAM,CAAAyB,SAAS,CAAG1B,QAAQ,SAARA,QAAQ,kBAAAwB,eAAA,CAARxB,QAAQ,CAAEG,IAAI,UAAAqB,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBrB,IAAI,UAAAsB,oBAAA,iBAApBA,oBAAA,CAAsB5B,MAAM,CAAE;AAChD3B,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAEuD,SAAS,CAAC,CAE9C;AACA,OAAQA,SAAS,EACf,IAAK,QAAQ,CAAE;AACbvC,aAAa,CAAC,EAAE,CAAC,CAAE;AACnBE,qBAAqB,CAAC,QAAQ,CAAC,CAC/BJ,UAAU,CAAC,kFAAkF,CAAC,CAC9F,MACF,IAAK,SAAS,CAAE;AACd;AACAA,UAAU,CAAC,uIAAuI,CAAC,CACnJI,qBAAqB,CAAC,SAAS,CAAC,CAAE;AAClC,MACF,QAAS;AACPnB,OAAO,CAACY,KAAK,CAAC,mDAAmD,CAAE4C,SAAS,CAAC,CAC7E3C,QAAQ,CAAC,oFAAoF,CAAC,CAC9F;AACA;AACA,MACJ,CAEF,CAAE,MAAOoC,GAAQ,CAAE,KAAAQ,cAAA,CAAAC,mBAAA,CACjB1D,OAAO,CAACY,KAAK,CAAC,+CAA+C,CAAEqC,GAAG,CAAC,CACnEpC,QAAQ,CAAC,EAAA4C,cAAA,CAAAR,GAAG,CAACnB,QAAQ,UAAA2B,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcxB,IAAI,UAAAyB,mBAAA,iBAAlBA,mBAAA,CAAoBnB,OAAO,GAAIU,GAAG,CAACV,OAAO,EAAI,8EAA8E,CAAC,CACtI;AACApB,qBAAqB,CAAC,SAAS,CAAC,CAClC,CAAC,OAAS,CACRR,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED;AACA,KAAM,CAAAgD,wBAAwB,CAAG,KAAAA,CAAA,GAAY,CAC3C,GAAI,CAACvD,MAAM,CAAE,CACXS,QAAQ,CAAC,oCAAoC,CAAC,CAC9C,OACF,CACA;AACA;AACA;AACA;AAEAJ,UAAU,CAAC,IAAI,CAAC,CAAE;AAClBI,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,KAAA6C,eAAA,CACF5D,OAAO,CAACC,GAAG,CAAC,6CAA6CG,MAAM,EAAE,CAAC,CAElE;AACA,KAAM,CAAA0B,QAAQ,CAAG,KAAM,CAAAtC,GAAG,CAACqE,MAAM,CAAC,iBAAiB,CAAC,CAAE;AAEtD;AACA;AACA;AACA;AAEA,GAAI/B,QAAQ,SAARA,QAAQ,YAAA8B,eAAA,CAAR9B,QAAQ,CAAEG,IAAI,UAAA2B,eAAA,WAAdA,eAAA,CAAgB9C,OAAO,CAAE,CAC3BC,UAAU,CAAC,4BAA4BX,MAAM,oBAAoB,CAAC,CAClEC,SAAS,CAAC,EAAE,CAAC,CAAE;AACfE,WAAW,CAAC,EAAE,CAAC,CAAE;AACjBU,aAAa,CAAC,EAAE,CAAC,CAAE;AACnBE,qBAAqB,CAAC,IAAI,CAAC,CAAE;AAC/B,CAAC,IAAM,KAAA2C,eAAA,CACLjD,QAAQ,CAAC,CAAAiB,QAAQ,SAARA,QAAQ,kBAAAgC,eAAA,CAARhC,QAAQ,CAAEG,IAAI,UAAA6B,eAAA,iBAAdA,eAAA,CAAgBvB,OAAO,GAAI,wCAAwC,CAAC,CAC/E,CAEF,CAAE,MAAOU,GAAQ,CAAE,KAAAc,cAAA,CAAAC,mBAAA,CACjBhE,OAAO,CAACY,KAAK,CAAC,gDAAgD,CAAEqC,GAAG,CAAC,CACpEpC,QAAQ,CAAC,EAAAkD,cAAA,CAAAd,GAAG,CAACnB,QAAQ,UAAAiC,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc9B,IAAI,UAAA+B,mBAAA,iBAAlBA,mBAAA,CAAoBzB,OAAO,GAAIU,GAAG,CAACV,OAAO,EAAI,iEAAiE,CAAC,CAC3H,CAAC,OAAS,CACR9B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA;AACA;AAEA;AACA,KAAM,CAAAwD,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAI,CAAC7C,gBAAgB,CAAE,CACrB,mBAAO1B,IAAA,MAAAwE,QAAA,CAAG,0BAAwB,CAAG,CAAC,CAAE;AAC1C,CAEA,OAAQhD,kBAAkB,EACxB,IAAK,QAAQ,CACX,mBACEtB,KAAA,QAAKuE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBtE,KAAA,QAAKuE,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCxE,IAAA,QAAKyE,SAAS,CAAC,yEAAyE,CAAAD,QAAA,cAEtFxE,IAAA,QAAK0E,KAAK,CAAC,4BAA4B,CAACD,SAAS,CAAC,oBAAoB,CAACE,OAAO,CAAC,WAAW,CAACC,IAAI,CAAC,cAAc,CAAAJ,QAAA,cAACxE,IAAA,SAAM6E,QAAQ,CAAC,SAAS,CAACC,CAAC,CAAC,oHAAoH,CAACC,QAAQ,CAAC,SAAS,CAAE,CAAC,CAAK,CAAC,CACvR,CAAC,cACN/E,IAAA,SAAMyE,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAC,iBAAe,CAAM,CAAC,EACzD,CAAC,cACNtE,KAAA,MAAGuE,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAC,cAC1B,cAAAxE,IAAA,WAAAwE,QAAA,CAAS9D,MAAM,CAAS,CAAC,6DACvC,EAAG,CAAC,cACJV,IAAA,CAACP,MAAM,EAACuF,OAAO,CAAEA,CAAA,GAAM3E,QAAQ,CAAC,YAAY,CAAE,CAAAmE,QAAA,CAAC,iBAAe,CAAQ,CAAC,EACpE,CAAC,CAEV,IAAK,SAAS,CACZ,mBACEtE,KAAA,QAAKuE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBtE,KAAA,OAAIuE,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EAAC,4BAA0B,cAAAxE,IAAA,SAAMyE,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAE9D,MAAM,CAAO,CAAC,EAAI,CAAC,cAChIV,IAAA,MAAGyE,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,qHAExC,CAAG,CAAC,CACHlD,UAAU,CAACmB,MAAM,CAAG,CAAC,cACpBzC,IAAA,QAAKyE,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE9DtE,KAAA,UAAOuE,SAAS,CAAC,YAAY,CAAAD,QAAA,eAC3BxE,IAAA,UAAAwE,QAAA,cAAOtE,KAAA,OAAAsE,QAAA,eAAIxE,IAAA,OAAIyE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,CAAC,MAAI,CAAI,CAAC,cAAAxE,IAAA,OAAIyE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,CAAC,WAAS,CAAI,CAAC,cAAAxE,IAAA,OAAIyE,SAAS,CAAC,+BAA+B,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,EAAI,CAAC,CAAO,CAAC,cAC7MxE,IAAA,UAAAwE,QAAA,CACGlD,UAAU,CAAC2D,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBAC5BjF,KAAA,OAAAsE,QAAA,eAAgBxE,IAAA,OAAIyE,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,MAAM,CAACE,IAAI,CAAK,CAAC,cAAApF,IAAA,OAAIyE,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,MAAM,CAAClD,IAAI,CAAK,CAAC,cAAAhC,IAAA,OAAIyE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,CAAEU,MAAM,CAACG,KAAK,CAAK,CAAC,GAArKF,KAAyK,CACnL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,cAENnF,IAAA,MAAGyE,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,+DAA6D,CAAG,CAChH,cACDxE,IAAA,MAAGyE,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,6HAExC,CAAG,CAAC,cACJtE,KAAA,QAAKuE,SAAS,CAAC,kCAAkC,CAAAD,QAAA,EAAC,GAAC,cACjDxE,IAAA,CAACP,MAAM,EAACuF,OAAO,CAAErB,uBAAwB,CAAC2B,QAAQ,CAAEtE,SAAS,EAAIF,OAAQ,CAAA0D,QAAA,CACtExD,SAAS,CAAG,aAAa,CAAG,2BAA2B,CAClD,CAAC,cACThB,IAAA,CAACP,MAAM,EACLuF,OAAO,CAAEf,wBAAyB,CAClCqB,QAAQ,CAAExE,OAAO,EAAIE,SAAU,CAC/BuE,OAAO,CAAC,QAAS;AAAA,CAAAf,QAAA,CAEhB1D,OAAO,CAAG,eAAe,CAAG,gBAAgB,CACvC,CAAC,EACN,CAAC,EACH,CAAC,CAEV,QAAS;AACP,mBACEZ,KAAA,QAAKuE,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBxE,IAAA,CAACL,KAAK,EACJ6F,EAAE,CAAC,QAAQ,CACXxD,IAAI,CAAC,QAAQ,CACbyD,KAAK,CAAC,aAAa,CACnBC,WAAW,CAAC,gBAAgB,CAC5BL,KAAK,CAAE3E,MAAO,CACdiF,QAAQ,CAAGC,CAAC,EAAKjF,SAAS,CAACiF,CAAC,CAACC,MAAM,CAACR,KAAK,CAAE,CAC3CS,QAAQ,MACRrB,SAAS,CAAC,MAAM,CACjB,CAAC,cACFzE,IAAA,CAACL,KAAK,EACJ6F,EAAE,CAAC,UAAU,CACbxD,IAAI,CAAC,UAAU,CACfyD,KAAK,CAAC,sBAAuB;AAAA,CAC7BC,WAAW,CAAC,uBAAuB,CACnCL,KAAK,CAAEzE,QAAS,CAChB+E,QAAQ,CAAGC,CAAC,EAAK/E,WAAW,CAAC+E,CAAC,CAACC,MAAM,CAACR,KAAK,CAC3C;AAAA,CACAZ,SAAS,CAAC,MAAM,CACjB,CAAC,cACFzE,IAAA,CAACP,MAAM,EAACuF,OAAO,CAAElC,oBAAqB,CAACwC,QAAQ,CAAExE,OAAQ,CAAA0D,QAAA,CACtD1D,OAAO,CAAG,yBAAyB,CAAG,0BAA0B,CAC3D,CAAC,EACN,CAAC,CAEZ,CACF,CAAC,CAED,mBACEZ,KAAA,CAACR,IAAI,EAACqG,KAAK,CAAC,sCAAsC,CAAAvB,QAAA,eAChDxE,IAAA,MAAGyE,SAAS,CAAC,0BAA0B,CAAAD,QAAA,CAAC,gIAExC,CAAG,CAAC,CAEHtD,KAAK,eACJlB,IAAA,CAACR,KAAK,EAAC4F,IAAI,CAAC,OAAO,CAACvC,OAAO,CAAE3B,KAAM,CAAC8E,OAAO,CAAEA,CAAA,GAAM7E,QAAQ,CAAC,EAAE,CAAE,CAACsD,SAAS,CAAC,MAAM,CAAE,CACpF,CACArD,OAAO,eACNpB,IAAA,CAACR,KAAK,EAAC4F,IAAI,CAAC,SAAS,CAACvC,OAAO,CAAEzB,OAAS,+CAA+CqD,SAAS,CAAC,MAAM,CAAE,CAC1G,CAEAF,aAAa,CAAC,CAAC,EAEZ,CAAC,CAEX,CAAC,CAED,cAAe,CAAApE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}