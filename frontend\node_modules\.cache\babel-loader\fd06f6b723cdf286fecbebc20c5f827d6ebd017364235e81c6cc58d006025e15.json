{"ast": null, "code": "import{useState}from'react';import axios from'axios';// Create a utility file for authentication-related functions\n// const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1'; // Original with fallback\n// const API_URL = 'http://localhost:3000/api'; // Previous incorrect fix\nconst API_URL='http://localhost:3000/api/v1';// Use the correct backend URL with /v1\n// Register a new user\nexport const registerUser=async(name,email,password)=>{try{const response=await axios.post(`${API_URL}/auth/register`,{name,email,password});return response.data;}catch(error){throw error;}};// Login a user\nexport const loginUser=async(email,password)=>{try{const response=await axios.post(`${API_URL}/auth/login`,{email,password});return response.data;}catch(error){throw error;}};// Refresh token\nexport const refreshUserToken=async refreshToken=>{try{// Use direct axios call to prevent interceptor loops\nconst response=await axios.post(`${API_URL}/auth/refresh-token`,{refreshToken});// Return data in expected format\nreturn response.data;}catch(error){console.error('Error refreshing token:',error);throw error;}};// Check if token is expired\nexport const isTokenExpired=token=>{if(!token)return true;try{const base64Url=token.split('.')[1];const base64=base64Url.replace(/-/g,'+').replace(/_/g,'/');const jsonPayload=decodeURIComponent(atob(base64).split('').map(function(c){return'%'+('00'+c.charCodeAt(0).toString(16)).slice(-2);}).join(''));const{exp}=JSON.parse(jsonPayload);const expired=Date.now()>=exp*1000;return expired;}catch(e){return true;}};// Logout a user\nexport const logoutUser=async()=>{try{const response=await axios.post(`${API_URL}/auth/logout`);return response.data;}catch(error){throw error;}};// Set up axios interceptors for authentication\nexport const setupAuthInterceptors=(logoutFn,axiosInstance)=>{// Determine which axios instance to use (custom instance or global)\nconst instance=axiosInstance||axios;// Remove any existing interceptors to prevent duplicates\nif(instance.interceptors&&instance.__refreshInterceptorId){instance.interceptors.response.eject(instance.__refreshInterceptorId);}// Track if we're currently refreshing to prevent multiple refresh requests\nlet isRefreshing=false;// Queue of requests to retry after token refresh\nlet failedQueue=[];// Process failed queue (retry or reject)\nconst processQueue=function(error){let token=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;failedQueue.forEach(prom=>{if(error){prom.reject(error);}else{prom.resolve(token);}});failedQueue=[];};// Request interceptor to add token to all requests\ninstance.interceptors.request.use(async config=>{// Skip token check for refresh token request to avoid infinite loop\nif(config.url.includes('/auth/refresh-token')||config.url.includes('/auth/login')){return config;}let token=localStorage.getItem('token');// Check if token exists and is expired\nif(token&&isTokenExpired(token)){console.log('Token expired, trying to refresh...');// If not already refreshing, attempt to refresh the token\nif(!isRefreshing){isRefreshing=true;const refreshToken=localStorage.getItem('refreshToken');if(!refreshToken){isRefreshing=false;await logoutFn();return Promise.reject(new Error('No refresh token available'));}try{// Call the refresh endpoint directly\nconst response=await axios.post(`${API_URL}/auth/refresh-token`,{refreshToken});if(response.data.data&&response.data.data.token){// Update tokens\nlocalStorage.setItem('token',response.data.data.token);localStorage.setItem('refreshToken',response.data.data.refreshToken||refreshToken);token=response.data.data.token;// Update axios defaults\ninstance.defaults.headers.common['Authorization']=`Bearer ${token}`;// If using custom instance, update global axios too\nif(axiosInstance){axios.defaults.headers.common['Authorization']=`Bearer ${token}`;}// Process queued requests\nprocessQueue(null,token);}else{// Invalid response format\nawait logoutFn();processQueue(new Error('Failed to refresh token'));}}catch(refreshError){// Refresh failed\nprocessQueue(refreshError);await logoutFn();}finally{isRefreshing=false;}}// If currently refreshing, queue this request\nif(isRefreshing){return new Promise((resolve,reject)=>{failedQueue.push({resolve,reject});}).then(newToken=>{config.headers.Authorization=`Bearer ${newToken}`;return config;}).catch(err=>{return Promise.reject(err);});}}// Add token to headers if available\nif(token){config.headers.Authorization=`Bearer ${token}`;}return config;},error=>{return Promise.reject(error);});// Response interceptor to handle 401 errors\nconst responseInterceptorId=instance.interceptors.response.use(response=>{return response;},async error=>{var _error$response;const originalRequest=error.config;// Only handle 401 errors and avoid infinite retry loops\nif(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401&&!originalRequest._retry){originalRequest._retry=true;// If already refreshing, queue the request\nif(isRefreshing){try{const token=await new Promise((resolve,reject)=>{failedQueue.push({resolve,reject});});originalRequest.headers['Authorization']=`Bearer ${token}`;return instance(originalRequest);}catch(err){return Promise.reject(err);}}// Start refreshing if not already\nif(!isRefreshing){isRefreshing=true;try{const refreshToken=localStorage.getItem('refreshToken');if(!refreshToken){throw new Error('No refresh token available');}const response=await axios.post(`${API_URL}/auth/refresh-token`,{refreshToken});if(response.data.data&&response.data.data.token){// Update tokens\nconst newToken=response.data.data.token;localStorage.setItem('token',newToken);localStorage.setItem('refreshToken',response.data.data.refreshToken||refreshToken);// Update headers\ninstance.defaults.headers.common['Authorization']=`Bearer ${newToken}`;originalRequest.headers['Authorization']=`Bearer ${newToken}`;// Process queue\nprocessQueue(null,newToken);// Retry original request\nreturn instance(originalRequest);}else{// Invalid response\nawait logoutFn();processQueue(new Error('Failed to refresh token'));return Promise.reject(error);}}catch(refreshError){// Refresh failed\nawait logoutFn();processQueue(refreshError);return Promise.reject(refreshError);}finally{isRefreshing=false;}}}return Promise.reject(error);});// Store the interceptor ID so we can remove it later if needed\ninstance.__refreshInterceptorId=responseInterceptorId;return instance;};// Custom hook for form handling\nexport const useForm=initialState=>{const[values,setValues]=useState(initialState);const handleChange=e=>{const{name,value}=e.target;setValues({...values,[name]:value});};const resetForm=()=>{setValues(initialState);};return{values,handleChange,resetForm,setValues};};", "map": {"version": 3, "names": ["useState", "axios", "API_URL", "registerUser", "name", "email", "password", "response", "post", "data", "error", "loginUser", "refreshUserToken", "refreshToken", "console", "isTokenExpired", "token", "base64Url", "split", "base64", "replace", "jsonPayload", "decodeURIComponent", "atob", "map", "c", "charCodeAt", "toString", "slice", "join", "exp", "JSON", "parse", "expired", "Date", "now", "e", "logoutUser", "setupAuthInterceptors", "logoutFn", "axiosInstance", "instance", "interceptors", "__refreshInterceptorId", "eject", "isRefreshing", "failedQueue", "processQueue", "arguments", "length", "undefined", "for<PERSON>ach", "prom", "reject", "resolve", "request", "use", "config", "url", "includes", "localStorage", "getItem", "log", "Promise", "Error", "setItem", "defaults", "headers", "common", "refreshError", "push", "then", "newToken", "Authorization", "catch", "err", "responseInterceptorId", "_error$response", "originalRequest", "status", "_retry", "useForm", "initialState", "values", "set<PERSON><PERSON><PERSON>", "handleChange", "value", "target", "resetForm"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/utils/auth.ts"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport axios from 'axios';\n\n// Create a utility file for authentication-related functions\n// const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1'; // Original with fallback\n// const API_URL = 'http://localhost:3000/api'; // Previous incorrect fix\nconst API_URL = 'http://localhost:3000/api/v1'; // Use the correct backend URL with /v1\n\n// Register a new user\nexport const registerUser = async (name: string, email: string, password: string) => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/register`, {\n      name,\n      email,\n      password\n    });\n    \n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Login a user\nexport const loginUser = async (email: string, password: string) => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/login`, {\n      email,\n      password\n    });\n    \n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Refresh token\nexport const refreshUserToken = async (refreshToken: string) => {\n  try {\n    // Use direct axios call to prevent interceptor loops\n    const response = await axios.post(`${API_URL}/auth/refresh-token`, {\n      refreshToken\n    });\n    \n    // Return data in expected format\n    return response.data;\n  } catch (error) {\n    console.error('Error refreshing token:', error);\n    throw error;\n  }\n};\n\n// Check if token is expired\nexport const isTokenExpired = (token: string) => {\n  if (!token) return true;\n  \n  try {\n    const base64Url = token.split('.')[1];\n    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');\n    const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {\n      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n    }).join(''));\n    \n    const { exp } = JSON.parse(jsonPayload);\n    const expired = Date.now() >= exp * 1000;\n    \n    return expired;\n  } catch (e) {\n    return true;\n  }\n};\n\n// Logout a user\nexport const logoutUser = async () => {\n  try {\n    const response = await axios.post(`${API_URL}/auth/logout`);\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\n\n// Set up axios interceptors for authentication\nexport const setupAuthInterceptors = (logoutFn: () => Promise<void>, axiosInstance?: any) => {\n  // Determine which axios instance to use (custom instance or global)\n  const instance = axiosInstance || axios;\n  \n  // Remove any existing interceptors to prevent duplicates\n  if (instance.interceptors && instance.__refreshInterceptorId) {\n    instance.interceptors.response.eject(instance.__refreshInterceptorId);\n  }\n  \n  // Track if we're currently refreshing to prevent multiple refresh requests\n  let isRefreshing = false;\n  // Queue of requests to retry after token refresh\n  let failedQueue: any[] = [];\n\n  // Process failed queue (retry or reject)\n  const processQueue = (error: any, token: string | null = null) => {\n    failedQueue.forEach(prom => {\n      if (error) {\n        prom.reject(error);\n      } else {\n        prom.resolve(token);\n      }\n    });\n    failedQueue = [];\n  };\n  \n  // Request interceptor to add token to all requests\n  instance.interceptors.request.use(\n    async (config: any) => {\n      // Skip token check for refresh token request to avoid infinite loop\n      if (config.url.includes('/auth/refresh-token') || config.url.includes('/auth/login')) {\n        return config;\n      }\n      \n      let token = localStorage.getItem('token');\n      \n      // Check if token exists and is expired\n      if (token && isTokenExpired(token)) {\n        console.log('Token expired, trying to refresh...');\n        \n        // If not already refreshing, attempt to refresh the token\n        if (!isRefreshing) {\n          isRefreshing = true;\n          \n          const refreshToken = localStorage.getItem('refreshToken');\n          if (!refreshToken) {\n            isRefreshing = false;\n            await logoutFn();\n            return Promise.reject(new Error('No refresh token available'));\n          }\n          \n          try {\n            // Call the refresh endpoint directly\n            const response = await axios.post(`${API_URL}/auth/refresh-token`, { \n              refreshToken \n            });\n            \n            if (response.data.data && response.data.data.token) {\n              // Update tokens\n              localStorage.setItem('token', response.data.data.token);\n              localStorage.setItem('refreshToken', response.data.data.refreshToken || refreshToken);\n              token = response.data.data.token;\n              \n              // Update axios defaults\n              instance.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n              \n              // If using custom instance, update global axios too\n              if (axiosInstance) {\n                axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n              }\n              \n              // Process queued requests\n              processQueue(null, token);\n            } else {\n              // Invalid response format\n              await logoutFn();\n              processQueue(new Error('Failed to refresh token'));\n            }\n          } catch (refreshError) {\n            // Refresh failed\n            processQueue(refreshError);\n            await logoutFn();\n          } finally {\n            isRefreshing = false;\n          }\n        }\n        \n        // If currently refreshing, queue this request\n        if (isRefreshing) {\n          return new Promise((resolve, reject) => {\n            failedQueue.push({ resolve, reject });\n          }).then((newToken) => {\n            config.headers.Authorization = `Bearer ${newToken}`;\n            return config;\n          }).catch(err => {\n            return Promise.reject(err);\n          });\n        }\n      }\n      \n      // Add token to headers if available\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      \n      return config;\n    },\n    (error: any) => {\n      return Promise.reject(error);\n    }\n  );\n\n  // Response interceptor to handle 401 errors\n  const responseInterceptorId = instance.interceptors.response.use(\n    (response: any) => {\n      return response;\n    },\n    async (error: any) => {\n      const originalRequest = error.config;\n      \n      // Only handle 401 errors and avoid infinite retry loops\n      if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        \n        // If already refreshing, queue the request\n        if (isRefreshing) {\n          try {\n            const token = await new Promise((resolve, reject) => {\n              failedQueue.push({ resolve, reject });\n            });\n            originalRequest.headers['Authorization'] = `Bearer ${token}`;\n            return instance(originalRequest);\n          } catch (err) {\n            return Promise.reject(err);\n          }\n        }\n        \n        // Start refreshing if not already\n        if (!isRefreshing) {\n          isRefreshing = true;\n          \n          try {\n            const refreshToken = localStorage.getItem('refreshToken');\n            if (!refreshToken) {\n              throw new Error('No refresh token available');\n            }\n            \n            const response = await axios.post(`${API_URL}/auth/refresh-token`, { \n              refreshToken \n            });\n            \n            if (response.data.data && response.data.data.token) {\n              // Update tokens\n              const newToken = response.data.data.token;\n              localStorage.setItem('token', newToken);\n              localStorage.setItem('refreshToken', response.data.data.refreshToken || refreshToken);\n              \n              // Update headers\n              instance.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n              originalRequest.headers['Authorization'] = `Bearer ${newToken}`;\n              \n              // Process queue\n              processQueue(null, newToken);\n              \n              // Retry original request\n              return instance(originalRequest);\n            } else {\n              // Invalid response\n              await logoutFn();\n              processQueue(new Error('Failed to refresh token'));\n              return Promise.reject(error);\n            }\n          } catch (refreshError) {\n            // Refresh failed\n            await logoutFn();\n            processQueue(refreshError);\n            return Promise.reject(refreshError);\n          } finally {\n            isRefreshing = false;\n          }\n        }\n      }\n      \n      return Promise.reject(error);\n    }\n  );\n  \n  // Store the interceptor ID so we can remove it later if needed\n  instance.__refreshInterceptorId = responseInterceptorId;\n  \n  return instance;\n};\n\n// Custom hook for form handling\nexport const useForm = (initialState: any) => {\n  const [values, setValues] = useState(initialState);\n  \n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setValues({\n      ...values,\n      [name]: value\n    });\n  };\n  \n  const resetForm = () => {\n    setValues(initialState);\n  };\n  \n  return { values, handleChange, resetForm, setValues };\n};\n"], "mappings": "AAAA,OAAgBA,QAAQ,KAAQ,OAAO,CAEvC,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB;AACA;AACA;AACA,KAAM,CAAAC,OAAO,CAAG,8BAA8B,CAAE;AAEhD;AACA,MAAO,MAAM,CAAAC,YAAY,CAAG,KAAAA,CAAOC,IAAY,CAAEC,KAAa,CAAEC,QAAgB,GAAK,CACnF,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,gBAAgB,CAAE,CAC5DE,IAAI,CACJC,KAAK,CACLC,QACF,CAAC,CAAC,CAEF,MAAO,CAAAC,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,SAAS,CAAG,KAAAA,CAAON,KAAa,CAAEC,QAAgB,GAAK,CAClE,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,aAAa,CAAE,CACzDG,KAAK,CACLC,QACF,CAAC,CAAC,CAEF,MAAO,CAAAC,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,gBAAgB,CAAG,KAAO,CAAAC,YAAoB,EAAK,CAC9D,GAAI,CACF;AACA,KAAM,CAAAN,QAAQ,CAAG,KAAM,CAAAN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,qBAAqB,CAAE,CACjEW,YACF,CAAC,CAAC,CAEF;AACA,MAAO,CAAAN,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAK,cAAc,CAAIC,KAAa,EAAK,CAC/C,GAAI,CAACA,KAAK,CAAE,MAAO,KAAI,CAEvB,GAAI,CACF,KAAM,CAAAC,SAAS,CAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrC,KAAM,CAAAC,MAAM,CAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,CAAE,GAAG,CAAC,CAC9D,KAAM,CAAAC,WAAW,CAAGC,kBAAkB,CAACC,IAAI,CAACJ,MAAM,CAAC,CAACD,KAAK,CAAC,EAAE,CAAC,CAACM,GAAG,CAAC,SAASC,CAAC,CAAE,CAC5E,MAAO,GAAG,CAAG,CAAC,IAAI,CAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC9D,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAAC,CAEZ,KAAM,CAAEC,GAAI,CAAC,CAAGC,IAAI,CAACC,KAAK,CAACX,WAAW,CAAC,CACvC,KAAM,CAAAY,OAAO,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAIL,GAAG,CAAG,IAAI,CAExC,MAAO,CAAAG,OAAO,CAChB,CAAE,MAAOG,CAAC,CAAE,CACV,MAAO,KAAI,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,UAAU,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAA9B,QAAQ,CAAG,KAAM,CAAAN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,cAAc,CAAC,CAC3D,MAAO,CAAAK,QAAQ,CAACE,IAAI,CACtB,CAAE,MAAOC,KAAK,CAAE,CACd,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAA4B,qBAAqB,CAAGA,CAACC,QAA6B,CAAEC,aAAmB,GAAK,CAC3F;AACA,KAAM,CAAAC,QAAQ,CAAGD,aAAa,EAAIvC,KAAK,CAEvC;AACA,GAAIwC,QAAQ,CAACC,YAAY,EAAID,QAAQ,CAACE,sBAAsB,CAAE,CAC5DF,QAAQ,CAACC,YAAY,CAACnC,QAAQ,CAACqC,KAAK,CAACH,QAAQ,CAACE,sBAAsB,CAAC,CACvE,CAEA;AACA,GAAI,CAAAE,YAAY,CAAG,KAAK,CACxB;AACA,GAAI,CAAAC,WAAkB,CAAG,EAAE,CAE3B;AACA,KAAM,CAAAC,YAAY,CAAG,QAAAA,CAACrC,KAAU,CAAkC,IAAhC,CAAAM,KAAoB,CAAAgC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC3DF,WAAW,CAACK,OAAO,CAACC,IAAI,EAAI,CAC1B,GAAI1C,KAAK,CAAE,CACT0C,IAAI,CAACC,MAAM,CAAC3C,KAAK,CAAC,CACpB,CAAC,IAAM,CACL0C,IAAI,CAACE,OAAO,CAACtC,KAAK,CAAC,CACrB,CACF,CAAC,CAAC,CACF8B,WAAW,CAAG,EAAE,CAClB,CAAC,CAED;AACAL,QAAQ,CAACC,YAAY,CAACa,OAAO,CAACC,GAAG,CAC/B,KAAO,CAAAC,MAAW,EAAK,CACrB;AACA,GAAIA,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,qBAAqB,CAAC,EAAIF,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,CAAE,CACpF,MAAO,CAAAF,MAAM,CACf,CAEA,GAAI,CAAAzC,KAAK,CAAG4C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAEzC;AACA,GAAI7C,KAAK,EAAID,cAAc,CAACC,KAAK,CAAC,CAAE,CAClCF,OAAO,CAACgD,GAAG,CAAC,qCAAqC,CAAC,CAElD;AACA,GAAI,CAACjB,YAAY,CAAE,CACjBA,YAAY,CAAG,IAAI,CAEnB,KAAM,CAAAhC,YAAY,CAAG+C,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACzD,GAAI,CAAChD,YAAY,CAAE,CACjBgC,YAAY,CAAG,KAAK,CACpB,KAAM,CAAAN,QAAQ,CAAC,CAAC,CAChB,MAAO,CAAAwB,OAAO,CAACV,MAAM,CAAC,GAAI,CAAAW,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAChE,CAEA,GAAI,CACF;AACA,KAAM,CAAAzD,QAAQ,CAAG,KAAM,CAAAN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,qBAAqB,CAAE,CACjEW,YACF,CAAC,CAAC,CAEF,GAAIN,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,CAAE,CAClD;AACA4C,YAAY,CAACK,OAAO,CAAC,OAAO,CAAE1D,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,CAAC,CACvD4C,YAAY,CAACK,OAAO,CAAC,cAAc,CAAE1D,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,YAAY,EAAIA,YAAY,CAAC,CACrFG,KAAK,CAAGT,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,CAEhC;AACAyB,QAAQ,CAACyB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUpD,KAAK,EAAE,CAErE;AACA,GAAIwB,aAAa,CAAE,CACjBvC,KAAK,CAACiE,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUpD,KAAK,EAAE,CACpE,CAEA;AACA+B,YAAY,CAAC,IAAI,CAAE/B,KAAK,CAAC,CAC3B,CAAC,IAAM,CACL;AACA,KAAM,CAAAuB,QAAQ,CAAC,CAAC,CAChBQ,YAAY,CAAC,GAAI,CAAAiB,KAAK,CAAC,yBAAyB,CAAC,CAAC,CACpD,CACF,CAAE,MAAOK,YAAY,CAAE,CACrB;AACAtB,YAAY,CAACsB,YAAY,CAAC,CAC1B,KAAM,CAAA9B,QAAQ,CAAC,CAAC,CAClB,CAAC,OAAS,CACRM,YAAY,CAAG,KAAK,CACtB,CACF,CAEA;AACA,GAAIA,YAAY,CAAE,CAChB,MAAO,IAAI,CAAAkB,OAAO,CAAC,CAACT,OAAO,CAAED,MAAM,GAAK,CACtCP,WAAW,CAACwB,IAAI,CAAC,CAAEhB,OAAO,CAAED,MAAO,CAAC,CAAC,CACvC,CAAC,CAAC,CAACkB,IAAI,CAAEC,QAAQ,EAAK,CACpBf,MAAM,CAACU,OAAO,CAACM,aAAa,CAAG,UAAUD,QAAQ,EAAE,CACnD,MAAO,CAAAf,MAAM,CACf,CAAC,CAAC,CAACiB,KAAK,CAACC,GAAG,EAAI,CACd,MAAO,CAAAZ,OAAO,CAACV,MAAM,CAACsB,GAAG,CAAC,CAC5B,CAAC,CAAC,CACJ,CACF,CAEA;AACA,GAAI3D,KAAK,CAAE,CACTyC,MAAM,CAACU,OAAO,CAACM,aAAa,CAAG,UAAUzD,KAAK,EAAE,CAClD,CAEA,MAAO,CAAAyC,MAAM,CACf,CAAC,CACA/C,KAAU,EAAK,CACd,MAAO,CAAAqD,OAAO,CAACV,MAAM,CAAC3C,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,KAAM,CAAAkE,qBAAqB,CAAGnC,QAAQ,CAACC,YAAY,CAACnC,QAAQ,CAACiD,GAAG,CAC7DjD,QAAa,EAAK,CACjB,MAAO,CAAAA,QAAQ,CACjB,CAAC,CACD,KAAO,CAAAG,KAAU,EAAK,KAAAmE,eAAA,CACpB,KAAM,CAAAC,eAAe,CAAGpE,KAAK,CAAC+C,MAAM,CAEpC;AACA,GAAI,EAAAoB,eAAA,CAAAnE,KAAK,CAACH,QAAQ,UAAAsE,eAAA,iBAAdA,eAAA,CAAgBE,MAAM,IAAK,GAAG,EAAI,CAACD,eAAe,CAACE,MAAM,CAAE,CAC7DF,eAAe,CAACE,MAAM,CAAG,IAAI,CAE7B;AACA,GAAInC,YAAY,CAAE,CAChB,GAAI,CACF,KAAM,CAAA7B,KAAK,CAAG,KAAM,IAAI,CAAA+C,OAAO,CAAC,CAACT,OAAO,CAAED,MAAM,GAAK,CACnDP,WAAW,CAACwB,IAAI,CAAC,CAAEhB,OAAO,CAAED,MAAO,CAAC,CAAC,CACvC,CAAC,CAAC,CACFyB,eAAe,CAACX,OAAO,CAAC,eAAe,CAAC,CAAG,UAAUnD,KAAK,EAAE,CAC5D,MAAO,CAAAyB,QAAQ,CAACqC,eAAe,CAAC,CAClC,CAAE,MAAOH,GAAG,CAAE,CACZ,MAAO,CAAAZ,OAAO,CAACV,MAAM,CAACsB,GAAG,CAAC,CAC5B,CACF,CAEA;AACA,GAAI,CAAC9B,YAAY,CAAE,CACjBA,YAAY,CAAG,IAAI,CAEnB,GAAI,CACF,KAAM,CAAAhC,YAAY,CAAG+C,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACzD,GAAI,CAAChD,YAAY,CAAE,CACjB,KAAM,IAAI,CAAAmD,KAAK,CAAC,4BAA4B,CAAC,CAC/C,CAEA,KAAM,CAAAzD,QAAQ,CAAG,KAAM,CAAAN,KAAK,CAACO,IAAI,CAAC,GAAGN,OAAO,qBAAqB,CAAE,CACjEW,YACF,CAAC,CAAC,CAEF,GAAIN,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,CAAE,CAClD;AACA,KAAM,CAAAwD,QAAQ,CAAGjE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,CACzC4C,YAAY,CAACK,OAAO,CAAC,OAAO,CAAEO,QAAQ,CAAC,CACvCZ,YAAY,CAACK,OAAO,CAAC,cAAc,CAAE1D,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,YAAY,EAAIA,YAAY,CAAC,CAErF;AACA4B,QAAQ,CAACyB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAG,UAAUI,QAAQ,EAAE,CACxEM,eAAe,CAACX,OAAO,CAAC,eAAe,CAAC,CAAG,UAAUK,QAAQ,EAAE,CAE/D;AACAzB,YAAY,CAAC,IAAI,CAAEyB,QAAQ,CAAC,CAE5B;AACA,MAAO,CAAA/B,QAAQ,CAACqC,eAAe,CAAC,CAClC,CAAC,IAAM,CACL;AACA,KAAM,CAAAvC,QAAQ,CAAC,CAAC,CAChBQ,YAAY,CAAC,GAAI,CAAAiB,KAAK,CAAC,yBAAyB,CAAC,CAAC,CAClD,MAAO,CAAAD,OAAO,CAACV,MAAM,CAAC3C,KAAK,CAAC,CAC9B,CACF,CAAE,MAAO2D,YAAY,CAAE,CACrB;AACA,KAAM,CAAA9B,QAAQ,CAAC,CAAC,CAChBQ,YAAY,CAACsB,YAAY,CAAC,CAC1B,MAAO,CAAAN,OAAO,CAACV,MAAM,CAACgB,YAAY,CAAC,CACrC,CAAC,OAAS,CACRxB,YAAY,CAAG,KAAK,CACtB,CACF,CACF,CAEA,MAAO,CAAAkB,OAAO,CAACV,MAAM,CAAC3C,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA+B,QAAQ,CAACE,sBAAsB,CAAGiC,qBAAqB,CAEvD,MAAO,CAAAnC,QAAQ,CACjB,CAAC,CAED;AACA,MAAO,MAAM,CAAAwC,OAAO,CAAIC,YAAiB,EAAK,CAC5C,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGpF,QAAQ,CAACkF,YAAY,CAAC,CAElD,KAAM,CAAAG,YAAY,CAAIjD,CAAsC,EAAK,CAC/D,KAAM,CAAEhC,IAAI,CAAEkF,KAAM,CAAC,CAAGlD,CAAC,CAACmD,MAAM,CAChCH,SAAS,CAAC,CACR,GAAGD,MAAM,CACT,CAAC/E,IAAI,EAAGkF,KACV,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAE,SAAS,CAAGA,CAAA,GAAM,CACtBJ,SAAS,CAACF,YAAY,CAAC,CACzB,CAAC,CAED,MAAO,CAAEC,MAAM,CAAEE,YAAY,CAAEG,SAAS,CAAEJ,SAAU,CAAC,CACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}