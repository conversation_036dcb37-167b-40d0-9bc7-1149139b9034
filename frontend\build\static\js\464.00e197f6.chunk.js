"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[464],{4464:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(5043),n=s(9291),r=s(8417),i=s(1411),l=s(4741),c=s(9579),d=s(579);const o=()=>{var e,t,s,o,m,u,x;const[p,g]=(0,a.useState)([]),[h,y]=(0,a.useState)(!0),[b,v]=(0,a.useState)(null),[f,j]=(0,a.useState)(null),[N,w]=(0,a.useState)(null),[A,T]=(0,a.useState)(!1),[C,S]=(0,a.useState)({name:"",description:"",testType:"subject",variantA:"",variantB:"",audienceSize:30,winningCriteria:"open_rate",segmentId:""}),k=[{id:"segment1",name:"Active Users"},{id:"segment2",name:"New Subscribers"},{id:"segment3",name:"Inactive Users"},{id:"segment4",name:"High Engagement"}];(0,a.useEffect)((()=>{(async()=>{y(!0),v(null);try{const e=await c.NY.getUserTests();e.success?g(e.data):v(e.message||"Failed to fetch A/B tests")}catch(e){v(e.message||"An error occurred while fetching tests")}finally{y(!1)}})()}),[]),(0,a.useEffect)((()=>{if(!f||"running"!==f.status&&"completed"!==f.status)return void w(null);(async()=>{T(!0);try{const e=await c.NY.getTestResults(f.id);e.success?w(e.data):(console.error("Failed to fetch test results:",e.message),w(null))}catch(e){console.error("Error fetching test results:",e),w(null)}finally{T(!1)}})()}),[f]);const B=e=>{switch(e=e||"draft"){case"draft":return{color:"gray",text:"Draft"};case"running":return{color:"blue",text:"Running"};case"completed":return{color:"green",text:"Completed"};case"cancelled":return{color:"red",text:"Cancelled"};default:return{color:"gray",text:e}}},z=e=>{switch(e){case"gray":default:return"bg-gray-700 text-gray-200";case"blue":return"bg-blue-800 text-blue-100";case"green":return"bg-green-800 text-green-100";case"red":return"bg-red-800 text-red-100"}};return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,d.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,d.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"Enhanced A/B Testing"})}),(0,d.jsx)("p",{className:"text-text-secondary mb-6",children:"Create and manage A/B tests to optimize your email campaigns."}),b&&(0,d.jsx)(n.A,{type:"error",message:b,onClose:()=>v(null),className:"mb-6"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,d.jsxs)(i.A,{className:"lg:col-span-1",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Create New A/B Test"}),(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),C.name.trim()&&C.variantA.trim()&&C.variantB.trim())if(C.audienceSize<1||C.audienceSize>100)v("Audience size must be between 1 and 100%");else{y(!0),v(null);try{const e={name:C.name,description:C.description||void 0,testType:C.testType,variants:{a:C.variantA,b:C.variantB},audienceSize:C.audienceSize,winningCriteria:C.winningCriteria,segmentId:C.segmentId||void 0},t=await c.NY.createTest(e);t.success?(g([...p,t.data]),S({name:"",description:"",testType:"subject",variantA:"",variantB:"",audienceSize:30,winningCriteria:"open_rate",segmentId:""})):v(t.message||"Failed to create A/B test")}catch(t){v(t.message||"An error occurred while creating test")}finally{y(!1)}}else v("Test name and both variants are required")},className:"p-4 space-y-4",children:[(0,d.jsx)(l.A,{label:"Test Name",id:"testName",name:"testName",value:C.name,onChange:e=>S({...C,name:e.target.value}),placeholder:"e.g., Subject Line Test - May",required:!0}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"testDescription",className:"block text-sm font-medium text-text-secondary mb-1",children:"Description (Optional)"}),(0,d.jsx)("textarea",{id:"testDescription",name:"testDescription",rows:2,value:C.description,onChange:e=>S({...C,description:e.target.value}),placeholder:"Brief description of the test",className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"testType",className:"block text-sm font-medium text-text-secondary mb-1",children:"Test Type"}),(0,d.jsxs)("select",{id:"testType",name:"testType",value:C.testType,onChange:e=>S({...C,testType:e.target.value}),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:[(0,d.jsx)("option",{value:"subject",children:"Subject Line"}),(0,d.jsx)("option",{value:"content",children:"Email Content"}),(0,d.jsx)("option",{value:"sender",children:"Sender Name"}),(0,d.jsx)("option",{value:"cta",children:"Call to Action"}),(0,d.jsx)("option",{value:"time",children:"Send Time"})]})]}),(0,d.jsx)(l.A,{label:"Variant A",id:"variantA",name:"variantA",value:C.variantA,onChange:e=>S({...C,variantA:e.target.value}),placeholder:"subject"===C.testType?"e.g., Check out our new features!":"content"===C.testType?"Enter HTML/Text for Variant A":"Variant A value",required:!0}),(0,d.jsx)(l.A,{label:"Variant B",id:"variantB",name:"variantB",value:C.variantB,onChange:e=>S({...C,variantB:e.target.value}),placeholder:"subject"===C.testType?"e.g., New Features Inside! \ud83d\udd25":"content"===C.testType?"Enter HTML/Text for Variant B":"Variant B value",required:!0}),(0,d.jsx)(l.A,{label:"Audience Size (%)",id:"audienceSize",name:"audienceSize",type:"number",value:C.audienceSize.toString(),onChange:e=>{const t=parseInt(e.target.value);!isNaN(t)&&t>=0&&t<=100?S({...C,audienceSize:t}):""===e.target.value&&S({...C,audienceSize:0})}}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"winningCriteria",className:"block text-sm font-medium text-text-secondary mb-1",children:"Winning Criteria"}),(0,d.jsxs)("select",{id:"winningCriteria",name:"winningCriteria",value:C.winningCriteria,onChange:e=>S({...C,winningCriteria:e.target.value}),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:[(0,d.jsx)("option",{value:"open_rate",children:"Open Rate"}),(0,d.jsx)("option",{value:"click_rate",children:"Click Rate"}),(0,d.jsx)("option",{value:"conversion_rate",children:"Conversion Rate"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"segmentId",className:"block text-sm font-medium text-text-secondary mb-1",children:"Segment (Optional)"}),(0,d.jsxs)("select",{id:"segmentId",name:"segmentId",value:C.segmentId,onChange:e=>S({...C,segmentId:e.target.value}),className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary",children:[(0,d.jsx)("option",{value:"",children:"All Recipients"}),k.map((e=>(0,d.jsx)("option",{value:e.id,children:e.name},e.id)))]})]}),(0,d.jsx)("div",{className:"flex justify-end pt-2",children:(0,d.jsx)(r.A,{type:"submit",disabled:h,className:"btn-cta",children:h?"Creating...":"Create Test"})})]})]}),(0,d.jsxs)(i.A,{className:"lg:col-span-1",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Existing Tests"}),h&&0===p.length?(0,d.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"Loading tests..."}):0===p.length?(0,d.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"No A/B tests created yet."}):(0,d.jsxs)("ul",{className:"divide-y divide-gray-700 max-h-[500px] overflow-y-auto",children:[" ",p.map((e=>(0,d.jsxs)("li",{className:"p-4 hover:bg-gray-700 cursor-pointer "+((null===f||void 0===f?void 0:f.id)===e.id?"bg-gray-700":""),onClick:()=>(e=>{(null===f||void 0===f?void 0:f.id)!==e.id&&j(e)})(e),children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-text-primary truncate flex-1 mr-2",children:e.name}),(0,d.jsx)("span",{className:`px-2 py-0.5 rounded-full text-xs font-medium capitalize ${z(B(e.status||"draft").color)}`,children:B(e.status||"draft").text})]}),(0,d.jsxs)("p",{className:"text-xs text-text-secondary mt-1",children:["Type: ",e.testType]})]},e.id)))]})]}),(0,d.jsxs)(i.A,{className:"lg:col-span-1",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Test Details & Results"}),(0,d.jsx)("div",{className:"p-4",children:f?(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-text-primary mb-1",children:f.name}),(0,d.jsx)("p",{className:"text-sm text-text-secondary mb-2",children:f.description}),(0,d.jsxs)("span",{className:`inline-block px-2 py-0.5 rounded-full text-xs font-medium capitalize ${z(B(f.status||"draft").color)} mb-2`,children:["Status: ",B(f.status||"draft").text]}),(0,d.jsxs)("p",{className:"text-sm text-text-secondary",children:["Type: ",f.testType]}),(0,d.jsxs)("p",{className:"text-sm text-text-secondary",children:["Audience: ",f.audienceSize,"%"]}),(0,d.jsxs)("p",{className:"text-sm text-text-secondary",children:["Criteria: ",f.winningCriteria]}),f.segmentId&&(0,d.jsxs)("p",{className:"text-sm text-text-secondary",children:["Segment: ",(null===(e=k.find((e=>e.id===f.segmentId)))||void 0===e?void 0:e.name)||"Unknown"]})]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-text-secondary",children:"Variant A:"}),(0,d.jsx)("p",{className:"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words",children:null===(t=f.variants)||void 0===t?void 0:t.a})]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-text-secondary",children:"Variant B:"}),(0,d.jsx)("p",{className:"text-sm text-text-primary bg-secondary-bg p-2 rounded break-words",children:null===(s=f.variants)||void 0===s?void 0:s.b})]}),("draft"===f.status||"paused"===f.status)&&(0,d.jsx)(r.A,{onClick:async()=>{if(f&&"draft"===f.status){y(!0);try{const e=await c.NY.startTest(f.id);if(e.success){const e={...f,status:"running"};g(p.map((t=>t.id===f.id?e:t))),j(e)}else v(e.message||"Failed to start test")}catch(e){v(e.message||"Error starting test"),console.error("Error starting test:",e)}finally{y(!1)}}},size:"sm",className:"btn-cta",children:"Start Test"}),"running"===f.status&&(0,d.jsx)(r.A,{onClick:async()=>{if(f&&"running"===f.status&&window.confirm("Are you sure you want to cancel this running test?")){y(!0);try{const e=await c.NY.cancelTest(f.id);if(e.success){const e={...f,status:"cancelled"};g(p.map((t=>t.id===f.id?e:t))),j(e)}else v(e.message||"Failed to cancel test")}catch(e){v(e.message||"Error cancelling test"),console.error("Error cancelling test:",e)}finally{y(!1)}}},variant:"danger",size:"sm",children:"Cancel Test"}),N?(0,d.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-700",children:[(0,d.jsx)("h3",{className:"text-md font-medium text-text-primary mb-2",children:"Results"}),N.winner?(0,d.jsx)(n.A,{type:"success",message:`Winner: Variant ${N.winner.toUpperCase()} (${N.confidence} confidence)`,className:"mb-2"}):"completed"===N.status?(0,d.jsx)(n.A,{type:"info",message:"Test completed, inconclusive result.",className:"mb-2"}):(0,d.jsx)(n.A,{type:"info",message:"Test is still running...",className:"mb-2"}),(0,d.jsxs)("div",{className:"text-sm text-text-secondary space-y-1",children:[(0,d.jsxs)("p",{children:["Variant A (",N.criteria,"): ",(null===(o=N.results)||void 0===o||null===(m=o.a)||void 0===m?void 0:m.rate)||"N/A","%"]}),(0,d.jsxs)("p",{children:["Variant B (",N.criteria,"): ",(null===(u=N.results)||void 0===u||null===(x=u.b)||void 0===x?void 0:x.rate)||"N/A","%"]})]})]}):"running"===f.status||"completed"===f.status?(0,d.jsx)("p",{className:"text-sm text-text-secondary italic mt-4",children:"Loading results..."}):null]}):(0,d.jsx)("p",{className:"text-text-secondary italic text-center",children:"Select a test from the list to see details and results."})})]})]})]})}}}]);
//# sourceMappingURL=464.00e197f6.chunk.js.map