import dotenv from 'dotenv';

import Template from '../models/template.model'; // Remove .js extension
import dbService from '../services/db.service'; // Remove .js extension

dotenv.config(); // Load environment variables

const deleteSystemTemplates = async () => {
  console.log('Script starting...');
  try {
    console.log('Attempting to connect to MongoDB...');
    await dbService.connectMongo(); // Use the existing service to connect
    console.log('Successfully connected to MongoDB.');

    console.log('Attempting to delete system templates...');
    const deleteResult = await Template.deleteMany({ isSystem: true });
    console.log('Deletion attempt finished.');
    console.log('Full deletion result:', deleteResult);
    console.log(`Deleted ${deleteResult.deletedCount} system templates.`);

    console.log('System template deletion completed successfully.');
  } catch (error) {
    console.error('Error during system template deletion:', error);
    process.exitCode = 1; // Indicate failure
  } finally {
    console.log('Attempting to disconnect from MongoDB...');
    await dbService.disconnect();
    console.log('Database connection closed.');
  }
  console.log('Script finished.');
};

// Execute the function
deleteSystemTemplates(); 