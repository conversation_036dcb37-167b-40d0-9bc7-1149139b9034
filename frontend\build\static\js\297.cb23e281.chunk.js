"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[297],{924:(e,t,l)=>{l.d(t,{A:()=>m});l(9884);var a=l(5043),n=l(722),s=l(5897),r=l.n(s),i=l(579);const o=(0,a.forwardRef)(((e,t)=>{let{initialMjml:l="",initialHtml:s="",onSave:o,height:m="70vh"}=e;const c=(0,a.useRef)(null),d=(0,a.useRef)(null);return(0,a.useEffect)((()=>{if(c.current){d.current&&(console.log("[MjmlEditor] Cleaning up previous editor instance"),d.current.destroy(),d.current=null),console.log("[MjmlEditor] Initializing editor with props:",{hasMjml:!!l,mjmlLength:(null===l||void 0===l?void 0:l.length)||0,hasHtml:!!s,htmlLength:(null===s||void 0===s?void 0:s.length)||0});try{const e=n.Ay.init({container:c.current,fromElement:!1,height:String(m),width:"auto",storageManager:!1,plugins:[r()],pluginsOpts:{"grapesjs-mjml":{useXmlParser:!0,resetBlocks:!1}}});if(!e)return void console.error("[MjmlEditor] Failed to initialize editor");let t;d.current=e,e.Commands.has("mjml-get-code")||(console.log("[MjmlEditor] Registering missing mjml-get-code command"),e.Commands.add("mjml-get-code",{run:e=>{const t=e.getHtml();return{mjml:t,html:t}}})),e.Commands.has("gjs-get-html")||(console.log("[MjmlEditor] Registering missing gjs-get-html command"),e.Commands.add("gjs-get-html",{run:e=>e.getHtml({component:e.getWrapper()})})),setTimeout((()=>{if(d.current)if(d.current.setComponents)try{if(l){console.log("[MjmlEditor] Loading initial MJML:",l.substring(0,100)+"...");try{d.current.setComponents(l),console.log("[MjmlEditor] Successfully loaded MJML content")}catch(e){console.error("[MjmlEditor] Error loading initial MJML:",e),s&&(console.log("[MjmlEditor] Falling back to loading initial HTML"),d.current.setComponents(s),console.log("[MjmlEditor] Successfully loaded HTML as fallback"))}}else s?(console.log("[MjmlEditor] Loading initial HTML (MJML not provided):",s.substring(0,100)+"..."),d.current.setComponents(s),console.log("[MjmlEditor] Successfully loaded HTML content")):(console.log("[MjmlEditor] No content provided, loading default template"),d.current.setComponents("\n                <mjml>\n                  <mj-body>\n                    <mj-section>\n                      <mj-column>\n                        <mj-text>Start designing your email!</mj-text>\n                      </mj-column>\n                    </mj-section>\n                  </mj-body>\n                </mjml>\n              "))}catch(t){console.error("[MjmlEditor] Error in content loading phase:",t)}else console.error("[MjmlEditor] Editor's setComponents method is not available");else console.error("[MjmlEditor] Editor instance not available after timeout")}),100);let a=!1;return e.on("change:changesCount",(()=>{o&&e&&!a&&(t&&clearTimeout(t),t=setTimeout((()=>{try{a=!0;let n="",s="";try{const t=e.runCommand("mjml-get-code");t&&"object"===typeof t&&(n=t.mjml||"",s=t.html||"",console.log("[MjmlEditor] Got code via 'mjml-get-code' command"))}catch(t){console.warn("'mjml-get-code' command failed, using fallback methods:",t)}if(!n&&!s){n=e.getHtml()||"";try{s=e.runCommand("gjs-get-html")||""}catch(l){console.warn("'gjs-get-html' command failed:",l)}s||(s=e.getHtml({component:e.getWrapper()})||""),console.log("[MjmlEditor] Using fallback getHtml()/gjs-get-html()")}if(!n.trim())return console.log("[MjmlEditor] No MJML content to save, skipping save"),void(a=!1);console.log("[MjmlEditor] Attempting to call onSave..."),o(n,s),console.log("[MjmlEditor] onSave callback executed.")}catch(n){console.error("Error during editor change listener:",n)}finally{a=!1}}),500))})),()=>{if(t&&clearTimeout(t),d.current){try{d.current.destroy()}catch(e){console.error("[MjmlEditor] Error during editor cleanup:",e)}d.current=null}}}catch(e){console.error("[MjmlEditor] Critical error during editor initialization:",e)}}}),[l,s,m,o]),(0,a.useImperativeHandle)(t,(()=>({save:async()=>{let e={mjml:"",html:""};if(d.current)try{const t=d.current;if(!t.Commands.has("mjml-get-code"))throw new Error("mjml-get-code command not available");{const l=t.runCommand("mjml-get-code");if(!(l&&"object"===typeof l&&"mjml"in l&&"html"in l))throw console.warn("'mjml-get-code' command did not return expected object, trying fallbacks."),new Error("Command returned unexpected structure");e.mjml=l.mjml||"",e.html=l.html||"",console.log("[MjmlEditor] Manual Save - MJML/HTML from command:",{mjml:e.mjml.substring(0,50)+"...",html:e.html.substring(0,50)+"..."})}}catch(t){console.warn("mjml-get-code command failed on manual save, using fallback:",t);try{const t=d.current,l=t.getHtml()||"";let a="";a=t.Commands.has("gjs-get-html")?t.runCommand("gjs-get-html")||"":t.getHtml({component:t.getWrapper()})||"",l||a?(e.mjml=l,e.html=a||l,console.log("[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.")):console.error("[MjmlEditor] Manual Save - Fallback methods also failed to get content.")}catch(l){console.error("[MjmlEditor] Manual Save - Error during fallback retrieval:",l)}}else console.error("[MjmlEditor] Manual Save - Editor not available.");if(await new Promise((e=>setTimeout(e,100))),d.current&&!e.html.trim()){console.log("[MjmlEditor] Manual Save - Re-fetching HTML after delay...");try{const t=d.current;let l="";t.Commands.has("gjs-get-html")&&(l=t.runCommand("gjs-get-html")),l||(l=t.getHtml({component:t.getWrapper()})||""),l.trim()?(console.log("[MjmlEditor] Manual Save - Found updated HTML after delay."),e.html=l):(console.log("[MjmlEditor] Manual Save - HTML still empty after delay."),e.mjml&&!e.html&&(e.html=e.mjml))}catch(a){console.error("[MjmlEditor] Manual Save - Error re-fetching HTML after delay:",a)}}return e},getEditor:()=>d.current}))),(0,i.jsx)("div",{ref:c,style:{height:m}})}));o.displayName="MjmlEditor";const m=o},1297:(e,t,l)=>{l.r(t),l.d(t,{default:()=>u});l(5021);var a=l(5043),n=l(8417),s=l(1411),r=l(4741),i=l(924),o=l(9774),m=l(9066),c=l(8231),d=l(9579),g=l(6291),h=l(579);const u=()=>{var e;const{user:t}=(0,m.A)(),l=(0,c.zy)(),u=(0,c.Zp)(),[x,p]=(0,a.useState)(""),[f,j]=(0,a.useState)(""),[v,y]=(0,a.useState)((null===t||void 0===t?void 0:t.name)||""),[b,N]=(0,a.useState)("active"===(null===t||void 0===t||null===(e=t.domain)||void 0===e?void 0:e.status)?`noreply@${t.domain.name}`:""),[S,C]=(0,a.useState)(""),[w,M]=(0,a.useState)(""),[E,k]=(0,a.useState)(""),[A,T]=(0,a.useState)(!1),[L,H]=(0,a.useState)(!1),[I,$]=(0,a.useState)(1),[R,_]=(0,a.useState)(1),[F,D]=(0,a.useState)((()=>{const e=localStorage.getItem("driftly_campaign_create_email_contents");if(e)try{const t=JSON.parse(e);if(Array.isArray(t)&&10===t.length)return t.map((e=>({mjml:e.mjml||"",html:e.html||""})))}catch{}return Array.from({length:10},(()=>({mjml:"",html:""})))})),[P,J]=(0,a.useState)({intervals:[24],unit:"hours"}),[U,q]=(0,a.useState)(null),[O,z]=(0,a.useState)(!1),[B,W]=(0,a.useState)([]),[G,V]=(0,a.useState)(!1),[Z,Y]=(0,a.useState)(null),[X,K]=(0,a.useState)("later"),[Q,ee]=(0,a.useState)((()=>new Date(Date.now()+36e5).toISOString().slice(0,16))),[te,le]=(0,a.useState)([]),[ae,ne]=(0,a.useState)({email:"",name:""}),[se,re]=(0,a.useState)([]),[ie,oe]=(0,a.useState)([]),[me,ce]=(0,a.useState)(!1),de=(0,a.useRef)(null),[ge,he]=(0,a.useState)((()=>{const e=[],t=localStorage.getItem("driftly_campaign_create_email_contents");let l=Array.from({length:10},(()=>({mjml:"",html:""})));if(t)try{const e=JSON.parse(t);Array.isArray(e)&&10===e.length&&(l=e.map((e=>({mjml:e.mjml||"",html:e.html||""}))))}catch{}return l.forEach(((t,l)=>{t.html&&t.html.trim()&&e.push(l)})),e})),ue=new URLSearchParams(l.search).get("templateId");(0,a.useEffect)((()=>{ue&&xe(ue)}),[ue]);const xe=async e=>{H(!0);try{const t=await d.jg.getTemplateById(e);if(t.success&&t.template){const e=t.template;x||p(`Campaign based on ${e.name}`);const l=[...F];l[0]={mjml:e.mjmlContent||"",html:e.content||""},D(l)}else M("Failed to load template.")}catch{M("Failed to load template.")}finally{H(!1)}},pe=(e,t)=>{console.log(`--- handleMjmlSave called for email ${R} ---`),console.log("Received MJML:",e?e.substring(0,100)+"...":"(empty)"),console.log("Received HTML:",t?t.substring(0,100)+"...":"(empty)");const l=[...F],a=R-1;l[a]={mjml:e,html:t},D(l),console.log("State AFTER update attempt:",l),localStorage.setItem("driftly_campaign_create_email_contents",JSON.stringify(l)),t&&t.trim()?(he((e=>e.includes(a)?e:[...e,a].sort(((e,t)=>e-t)))),console.log(`Ensured email index ${a} is active after save.`)):(he((e=>e.filter((e=>e!==a)))),console.log(`Deactivated email index ${a} after save due to empty HTML.`))};(0,a.useEffect)((()=>{O&&(V(!0),d.jg.getAllTemplates().then((e=>W(e.data||[]))).catch(console.error).finally((()=>V(!1))))}),[O]);const fe=()=>{if(console.log("[Debug] Using selected template:",Z?{id:Z.id||Z._id,name:Z.name,hasMjml:!!Z.mjmlContent,hasContent:!!Z.content}:"No template selected"),Z){const e=[...F];e[R-1]={mjml:Z.mjmlContent||"",html:Z.content||""},console.log("[Debug] Updated email contents:",{emailIndex:R-1,hasMjml:!!e[R-1].mjml,hasHtml:!!e[R-1].html,mjmlLength:e[R-1].mjml.length,htmlLength:e[R-1].html.length}),D(e),localStorage.setItem("driftly_campaign_create_email_contents",JSON.stringify(e)),Y(null),z(!1),setTimeout((()=>{_(-1),setTimeout((()=>{_(R)}),50)}),100)}},je=async()=>{if(!x||!f||!v||!b)return void M("Please fill in all required fields");if(0!==F.filter((e=>e.html.trim())).length)if(0!==te.length){T(!0);try{var e;const l=F.filter(((e,t)=>ge.includes(t)&&e.html&&e.html.trim()));if(0===l.length)return M("Please select at least one email with valid HTML content to send."),void T(!1);if(0===te.length)return M("Please add at least one recipient"),void T(!1);const a={name:x,subject:f,fromName:v,fromEmail:b,replyTo:S||b,scheduled:"later"===X,..."later"===X&&{scheduledFor:new Date(Q).toISOString()},recipientList:te,userId:null===t||void 0===t?void 0:t.id,status:"draft",htmlContent:(null===(e=l[0])||void 0===e?void 0:e.html)||"",emailContents:l.map((e=>({mjml:e.mjml,html:e.html})))};l.length>1&&(a.schedule={unit:P.unit,emailIntervals:P.intervals.slice(0,l.length-1).map((e=>({delay:e,unit:P.unit})))}),console.log("Sending filtered & active campaign data:",a);const n=await g.J.createCampaign(a),s=(null===n||void 0===n?void 0:n._id)||(null===n||void 0===n?void 0:n.id);console.log("Create campaign response:",n,"Extracted ID:",s),k("later"===X?`Campaign scheduled for ${new Date(Q).toLocaleString()}`:"Campaign created and will start sending shortly"),localStorage.removeItem("driftly_campaign_create_email_contents"),localStorage.removeItem("driftly_campaign_draft"),sessionStorage.setItem("reloadCampaigns","true"),setTimeout((()=>{s?u(`/campaigns/${s}/summary`):(console.warn("No ID found in createCampaign response, navigating to list."),u("/campaigns"))}),1500)}catch(n){var l,a;M((null===(l=n.response)||void 0===l||null===(a=l.data)||void 0===a?void 0:a.message)||"Failed to create campaign")}finally{T(!1)}}else M("Please add at least one recipient");else M("Please add at least one email with valid HTML content")},ve=()=>{(x||f||v||b||S)&&localStorage.setItem("driftly_campaign_draft",JSON.stringify({campaignName:x,subject:f,fromName:v,fromEmail:b,replyTo:S})),u("/email-templates")};(0,a.useEffect)((()=>{const e=localStorage.getItem("driftly_campaign_draft");if(e){try{const l=JSON.parse(e);p(l.campaignName||""),j(l.subject||""),y(l.fromName||(null===t||void 0===t?void 0:t.name)||""),N(l.fromEmail||""),C(l.replyTo||"")}catch{}localStorage.removeItem("driftly_campaign_draft")}}),[t]);(0,a.useEffect)((()=>{if(3===I){const e=F.reduce(((e,t,l)=>ge.includes(l)&&t.html&&t.html.trim()?e+1:e),0);console.log("Active emails with HTML for scheduling:",e),J(e>1?t=>({...t,intervals:Array(e-1).fill(t.intervals[0]||24)}):e=>({...e,intervals:[]}))}}),[I,F,ge]);(0,a.useEffect)((()=>{(async()=>{if(4===I){ce(!0);try{var e;const t=await g.A.get("/contacts");let l=Array.isArray(t.data)?t.data:(null===(e=t.data)||void 0===e?void 0:e.data)||[];re(l.map((e=>({id:e.id||e._id||String(Math.random()),email:e.email,name:e.name||e.fullName||""}))))}catch{M("Failed to load contacts")}finally{ce(!1)}}})()}),[I]);const ye=()=>{ae.email?te.some((e=>e.email===ae.email))?M("Recipient already added."):(le([...te,{id:null,...ae}]),ne({email:"",name:""})):M("Please enter an email address."),setTimeout((()=>M("")),3e3)},be=()=>{const e=se.filter((e=>ie.includes(e.id)&&!te.some((t=>t.email===e.email))));le([...te,...e]),oe([])},Ne=e=>oe(Array.from(e.target.selectedOptions,(e=>e.value)));return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("h1",{className:"text-2xl font-bold mb-6 text-text-primary",children:"Create New Campaign"}),w&&(0,h.jsx)("div",{className:"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:(0,h.jsx)("p",{children:w})}),E&&(0,h.jsx)("div",{className:"mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded",children:(0,h.jsx)("p",{children:E})}),L&&(0,h.jsx)("div",{className:"mb-4 text-text-secondary",children:"Loading template..."}),(0,h.jsx)("div",{className:"mb-8",children:(0,h.jsx)("ol",{className:"flex items-center w-full text-sm font-medium text-center text-text-secondary dark:text-gray-400 sm:text-base",children:[1,2,3,4,5].map((e=>(0,h.jsx)("li",{className:`flex md:w-full items-center ${e===I?"text-accent-coral dark:text-blue-500":""} ${e<5?"sm:after:content-[''] after:w-full after:h-1 after:border-b after:border-border dark:after:border-gray-700 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10":""}`,children:(0,h.jsxs)("span",{className:"flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-text-disabled "+(e<I?"text-text-primary dark:text-gray-200":""),children:[e<I&&(0,h.jsx)("svg",{className:"w-3.5 h-3.5 sm:w-4 sm:h-4 mr-2.5","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 20 20",children:(0,h.jsx)("path",{d:"M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"})}),e===I&&(0,h.jsx)("span",{className:"mr-2",children:e}),e>I&&(0,h.jsx)("span",{className:"mr-2",children:e}),["Details","Content","Schedule","Recipients","Review"][e-1]]})},e)))})}),(0,h.jsx)(s.A,{className:"mb-6",children:(()=>{var e,l;switch(I){case 1:return(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsx)(r.A,{id:"campaignName",name:"campaignName",label:"Campaign Name",value:x,onChange:e=>p(e.target.value),required:!0}),(0,h.jsx)(r.A,{id:"subject",name:"subject",label:"Email Subject",value:f,onChange:e=>j(e.target.value),required:!0}),(0,h.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,h.jsx)(r.A,{id:"fromName",name:"fromName",label:"From Name",value:v,onChange:e=>y(e.target.value),required:!0}),(0,h.jsx)(r.A,{id:"fromEmail",name:"fromEmail",label:"From Email",type:"email",value:b,onChange:e=>N(e.target.value),disabled:"active"===(null===t||void 0===t||null===(e=t.domain)||void 0===e?void 0:e.status),required:!0,helpText:"active"===(null===t||void 0===t||null===(l=t.domain)||void 0===l?void 0:l.status)?`Using verified domain: ${t.domain.name}`:"Verify domain for deliverability."})]}),(0,h.jsx)(r.A,{id:"replyTo",name:"replyTo",label:"Reply-To Email (optional)",type:"email",value:S,onChange:e=>C(e.target.value)})]});case 2:const a=F[R-1];F.filter((e=>e.html.trim())).length;return(0,h.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 h-full",children:[(0,h.jsxs)("div",{className:"w-full lg:w-1/5 flex flex-col gap-2",children:[(0,h.jsx)("h3",{className:"font-semibold mb-2",children:"Email Sequence (Click to include/exclude):"}),Array.from({length:10}).map(((e,t)=>{const l=!(!F[t].html||!F[t].html.trim()),a=R===t+1,s=ge.includes(t);let r="secondary",i="",o=`Email ${t+1}`,m=null;l?s?(r="primary",m=(0,h.jsx)("span",{className:"ml-1 text-green-500 dark:text-green-400 font-bold",children:"\u2713"}),o+=" (Sending)"):(r="secondary",i="bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-500",m=(0,h.jsx)("span",{className:"ml-1 text-gray-500 dark:text-gray-400 font-bold",children:"\u23f8"}),o+=" (Excluded)"):(i="opacity-75 border border-dashed border-gray-400 dark:border-gray-600",o+=" (No Content - Click to Edit)");const c=a?"ring-2 ring-offset-2 ring-accent-coral dark:ring-offset-gray-800":"";return(0,h.jsxs)(n.A,{variant:r,onClick:()=>{_(t+1),l&&he((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]))},size:"sm",className:`w-full text-left flex items-center justify-between ${c} ${i}`,title:l?s?"Click to exclude from sending":"Click to include in sending":"Click to edit this email",children:[(0,h.jsx)("span",{className:"flex-grow truncate",children:o}),m]},t)})),(0,h.jsxs)("div",{className:"mt-4 p-2 bg-gray-100 dark:bg-gray-700 rounded-md text-sm space-y-1",children:[(0,h.jsx)("p",{className:"font-medium mb-1",children:"Email Status Legend:"}),(0,h.jsxs)("p",{className:"flex items-center",children:[(0,h.jsx)("span",{className:"ml-1 mr-2 text-green-500 dark:text-green-400 font-bold",children:"\u2713"}),(0,h.jsx)("span",{className:"text-green-700 dark:text-green-400",children:"Has Content, Will Send"})]}),(0,h.jsxs)("p",{className:"flex items-center",children:[(0,h.jsx)("span",{className:"inline-block w-3 h-3 mr-2 bg-gray-400 dark:bg-gray-500 rounded-full"}),(0,h.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:["Has Content, ",(0,h.jsx)("span",{className:"font-bold",children:"Excluded"})]})]}),(0,h.jsxs)("p",{className:"flex items-center",children:[(0,h.jsx)("span",{className:"inline-block w-3 h-3 mr-2 border border-dashed border-gray-400 dark:border-gray-600 rounded-full opacity-75"}),(0,h.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"No Content (Click to Edit)"})]}),(0,h.jsx)("p",{className:"text-xs mt-2 text-gray-600 dark:text-gray-400",children:"Click emails with content to toggle inclusion. Click any email to edit."})]}),(0,h.jsxs)("div",{className:"mt-4",children:[(0,h.jsx)(n.A,{variant:"secondary",onClick:()=>z(!0),children:"Choose Template"}),(0,h.jsx)(n.A,{variant:"secondary",onClick:ve,className:"mt-2",children:"Browse All Templates"})]})]}),(0,h.jsxs)("div",{className:"w-full lg:w-4/5 flex flex-col",children:[(0,h.jsx)(i.A,{ref:de,initialMjml:a.mjml,onSave:pe,height:"70vh"},`email-${R}-${Date.now()}`),(0,h.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-2",children:"Changes are saved automatically. Emails with a green check mark (\u2713) will be included in your campaign."})]})]});case 3:{const e=F.reduce(((e,t,l)=>ge.includes(l)&&t.html&&t.html.trim()?e+1:e),0);console.log("Active emails with HTML for rendering Step 3:",e);const t=e=>{K(e.target.value)},l=e=>{ee(e.target.value)},a=(e,t)=>{const l=parseInt(t,10);if(!isNaN(l)&&l>0){const t=[...P.intervals];t[e]=l,J({...P,intervals:t})}},n=e=>{J({...P,unit:e.target.value})};return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Campaign Start Time"}),(0,h.jsxs)("div",{className:"space-y-3",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{id:"scheduleNow",name:"scheduleOption",type:"radio",value:"now",checked:"now"===X,onChange:t,className:"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"}),(0,h.jsx)("label",{htmlFor:"scheduleNow",className:"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Send Immediately"})]}),(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{id:"scheduleLater",name:"scheduleOption",type:"radio",value:"later",checked:"later"===X,onChange:t,className:"focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"}),(0,h.jsx)("label",{htmlFor:"scheduleLater",className:"ml-3 block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Schedule for Later"})]}),"later"===X&&(0,h.jsxs)("div",{className:"pl-7 mt-2",children:[(0,h.jsx)(r.A,{id:"scheduledDateTime",name:"scheduledDateTime",type:"datetime-local",value:Q,onChange:l,label:"Scheduled Date & Time",required:!0,className:"max-w-sm"}),(0,h.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Your local timezone."})]})]})]}),e>1&&(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Email Sequence Timing"}),(0,h.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Set the time interval between each subsequent email send. The first email is sent according to the 'Campaign Start Time' setting above."}),(0,h.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,h.jsx)("label",{htmlFor:"scheduleUnit",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Interval Time Unit:"}),(0,h.jsxs)("select",{id:"scheduleUnit",name:"scheduleUnit",value:P.unit,onChange:n,className:"mt-1 block w-auto pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,h.jsx)("option",{value:"minutes",children:"Minutes"}),(0,h.jsx)("option",{value:"hours",children:"Hours"}),(0,h.jsx)("option",{value:"days",children:"Days"})]})]}),Array.from({length:e-1}).map(((e,t)=>{var l;return(0,h.jsxs)("div",{className:"flex items-center gap-4 mt-4",children:[(0,h.jsxs)("label",{htmlFor:`interval-${t}`,className:"block text-sm font-medium text-gray-700 dark:text-gray-300 w-48",children:["Wait before sending Email #",t+2,":"]}),(0,h.jsx)(r.A,{id:`interval-${t}`,name:`interval-${t}`,type:"number",value:(null===(l=P.intervals[t])||void 0===l?void 0:l.toString())||"24",onChange:e=>a(t,e.target.value),className:"w-24 mb-0",required:!0}),(0,h.jsx)("span",{className:"text-gray-600 dark:text-gray-400 capitalize",children:P.unit})]},t)}))]}),e<=1&&(0,h.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm px-4",children:"Sequence timing options are available when your campaign has more than one selected email with content."})]})}case 4:return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Add Recipients"}),(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-3 text-text-primary",children:"Add Manually"}),(0,h.jsxs)("div",{className:"md:flex md:items-end md:space-x-3",children:[(0,h.jsx)("div",{className:"flex-grow mb-3 md:mb-0",children:(0,h.jsx)(r.A,{id:"manualRecipient",name:"manualRecipient",label:"Email Address",type:"email",value:ae.email,onChange:e=>ne({...ae,email:e.target.value}),placeholder:"Enter email address"})}),(0,h.jsx)(n.A,{onClick:ye,variant:"secondary",className:"btn-cta mt-4 md:mt-0",children:"Add Recipient"})]})]}),(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-3 text-text-primary",children:"Add From Contacts"}),me?(0,h.jsx)("p",{children:"Loading contacts..."}):se.length>0?(0,h.jsxs)("div",{className:"md:flex md:items-end md:space-x-3",children:[(0,h.jsxs)("div",{className:"flex-grow mb-3 md:mb-0",children:[(0,h.jsx)("label",{htmlFor:"contactSelect",className:"block text-sm font-medium text-text-secondary mb-1",children:"Select Contacts (use Ctrl/Cmd to select multiple)"}),(0,h.jsx)("select",{id:"contactSelect",multiple:!0,value:ie,onChange:Ne,className:"form-input w-full h-32 border border-border rounded-md",children:se.map((e=>(0,h.jsx)("option",{value:e.id,children:e.name?`${e.name} (${e.email})`:e.email},e.id)))})]}),(0,h.jsx)(n.A,{onClick:be,variant:"secondary",className:"btn-cta mt-4 md:mt-0",children:"Add Selected"})]}):(0,h.jsx)("p",{className:"text-text-secondary",children:"No contacts found. You can add contacts in the 'Contacts' section."})]}),(0,h.jsxs)(s.A,{children:[(0,h.jsxs)("h3",{className:"text-lg font-medium mb-3 text-text-primary",children:["Recipients Added (",te.length,")"]}),te.length>0?(0,h.jsx)("ul",{className:"divide-y divide-border max-h-60 overflow-y-auto",children:te.map(((e,t)=>(0,h.jsxs)("li",{className:"py-2 flex justify-between items-center",children:[(0,h.jsx)("span",{children:e.name?`${e.name} (${e.email})`:e.email}),(0,h.jsx)("button",{onClick:()=>{return t=e.email,le(te.filter((e=>e.email!==t)));var t},className:"text-red-500 hover:text-red-700 text-sm",children:"\xd7 Remove"})]},t)))}):(0,h.jsx)("p",{className:"text-text-secondary",children:"No recipients added yet."})]})]});case 5:{const e="later"===X&&Q?new Date(Q).toLocaleString():"Immediately";return(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Review & Schedule"}),(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-2 text-text-primary",children:"Campaign Details"}),(0,h.jsxs)("p",{children:[(0,h.jsx)("span",{className:"font-semibold",children:"Name:"})," ",x]}),(0,h.jsxs)("p",{children:[(0,h.jsx)("span",{className:"font-semibold",children:"Subject:"})," ",f]}),(0,h.jsxs)("p",{children:[(0,h.jsx)("span",{className:"font-semibold",children:"From:"})," ",v," <",b,">"]}),S&&(0,h.jsxs)("p",{children:[(0,h.jsx)("span",{className:"font-semibold",children:"Reply To:"})," ",S]})]}),(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-2 text-text-primary",children:"Sequence (Emails to Send)"}),F.filter(((e,t)=>ge.includes(t)&&e.html.trim())).length>0?(0,h.jsx)("ul",{className:"list-decimal pl-5 space-y-1",children:F.map(((e,t)=>{if(ge.includes(t)&&e.html.trim()){const e=F.map(((e,t)=>({email:e,originalIndex:t}))).filter((e=>{let{email:t,originalIndex:l}=e;return ge.includes(l)&&t.html.trim()})),l=e.findIndex((e=>e.originalIndex===t)),a=l+1;let n="Sent at Campaign Start Time";return a>1&&void 0!==P.intervals[a-2]&&(n=`Sent ${P.intervals[a-2]} ${P.unit} after Email #${e[l-1].originalIndex+1}`),(0,h.jsxs)("li",{children:["Email #",t+1," - ",n]},t)}return null})).filter(Boolean)}):(0,h.jsx)("p",{className:"text-text-secondary",children:"No emails selected or content found for the sequence."})]}),(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-2 text-text-primary",children:"Recipients"}),(0,h.jsxs)("p",{children:[te.length," recipient(s)"]}),te.length>0&&(0,h.jsxs)("ul",{className:"list-disc pl-5 text-sm max-h-20 overflow-y-auto",children:[te.slice(0,5).map(((e,t)=>(0,h.jsx)("li",{children:e.name?`${e.name} (${e.email})`:e.email},t))),te.length>5&&(0,h.jsxs)("li",{children:["...and ",te.length-5," more"]})]})]}),(0,h.jsxs)(s.A,{children:[(0,h.jsx)("h3",{className:"text-lg font-medium mb-2 text-text-primary",children:"Schedule"}),(0,h.jsxs)("p",{children:["Start sending: ",e]})]}),(0,h.jsx)("div",{className:"mt-6 flex justify-end",children:(0,h.jsx)(n.A,{onClick:je,disabled:A,className:"btn-cta",children:A?"Creating...":"Create & Schedule Campaign"})})]})}default:return null}})()}),(0,h.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,h.jsx)(n.A,{onClick:()=>{M(""),I>1&&$(I-1)},disabled:1===I||A,variant:"secondary",children:"Previous"}),I<5?(0,h.jsx)(n.A,{onClick:async()=>{if(M(""),1!==I||x&&f&&v&&b){if(2===I){console.log("--- handleNext: Step 2 Validation ---");let e=null,t=!1,l=!1;de.current?(console.log("Forcing editor save for current email:",R),e=await de.current.save(),e?(console.log("Async save function returned content."),console.log("Returned MJML:",e.mjml?e.mjml.substring(0,50)+"...":"(empty)"),console.log("Returned HTML:",e.html?e.html.substring(0,50)+"...":"(empty)"),e.html&&e.html.trim()?(console.log("RETURNED content has non-empty HTML."),t=!0):console.log("RETURNED content has empty or no HTML."),console.log("Triggering handleMjmlSave state update..."),pe(e.mjml,e.html)):console.warn("editorRef.current.save() did not return expected content object.")):console.error("MjmlEditor ref is not available for forced save.");const a=t&&ge.includes(R-1);a&&console.log("Saved content is for an active email."),console.log("Checking emailContents state AFTER awaiting save/update attempt..."),l=F.some(((e,t)=>{const l=e.html&&e.html.trim(),a=ge.includes(t);return!(!l||!a)&&(console.log(`Email index ${t} in STATE is ACTIVE and has HTML.`),!0)})),l||console.log("No email in STATE is both active and has HTML.");const n=a||l;if(console.log("Final check result (Active & HTML):",n,`(Saved Active: ${a}, State Active: ${l})`),!n)return void M("Please ensure at least one selected email (marked as 'Sending') has valid HTML content.");console.log("Step 2 validation passed (Active HTML check).")}if(3===I){const e=F.filter((e=>e.html.trim())).length;if(e>1){if(P.intervals.length!==e-1)return void M(`Define ${e-1} intervals.`);if(P.intervals.some((e=>e<=0)))return void M("Intervals must be positive.")}}4!==I||0!==te.length?I<5&&(console.log(`Proceeding from step ${I} to step ${I+1}`),$(I+1)):M("Please add at least one recipient.")}else M("Please fill in all required fields.")},disabled:A,variant:"primary",children:"Next"}):(0,h.jsxs)(n.A,{onClick:je,disabled:A,className:"btn-cta",children:[" ",A?"Creating...":"Create & Schedule Campaign"]})]}),(0,h.jsxs)(o.a,{isOpen:O,onClose:()=>z(!1),title:"Choose Template",children:[(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto p-1",children:G?(0,h.jsx)("p",{children:"Loading..."}):B.map((e=>(0,h.jsxs)(s.A,{className:"cursor-pointer "+((null===Z||void 0===Z?void 0:Z.id)===e.id?"ring-2 ring-primary":""),onClick:()=>(async e=>{V(!0);try{console.log(`[Debug] Selecting template with ID: ${e}`);const l=await d.jg.getTemplateById(e);var t;console.log("[Debug] Template API response:",l),l.success&&l.template&&console.log("[Debug] Setting selected template:",{id:l.template.id||l.template._id,name:l.template.name,hasMjml:!!l.template.mjmlContent,hasContent:!!l.template.content,contentLength:(null===(t=l.template.content)||void 0===t?void 0:t.length)||0}),Y(l.template)}catch(l){console.error("[Debug] Error selecting template:",l),M("Failed to load selected template.")}finally{V(!1)}})(e.id),children:[(0,h.jsx)("h4",{className:"font-semibold mb-2 truncate",children:e.name}),(0,h.jsx)("div",{className:"h-24 bg-gray-200 dark:bg-gray-700 rounded flex items-center justify-center text-gray-500",children:e.thumbnailUrl?(0,h.jsx)("img",{src:e.thumbnailUrl,alt:e.name,className:"object-contain h-full w-full"}):"No Preview"})]},e.id)))}),(0,h.jsxs)("div",{className:"mt-4 flex justify-end gap-2",children:[(0,h.jsx)(n.A,{variant:"secondary",onClick:()=>z(!1),children:"Cancel"}),(0,h.jsx)(n.A,{onClick:fe,disabled:!Z||G,children:"Use Selected"})]})]})]})}},5021:()=>{},9774:(e,t,l)=>{l.d(t,{a:()=>o});var a=l(5043),n=l(6018),s=l(6443),r=l(8417),i=l(579);const o=e=>{let{isOpen:t,onClose:l,title:o,children:m,onConfirm:c,confirmText:d="Confirm",confirmVariant:g="primary",cancelText:h="Cancel"}=e;return(0,i.jsx)(n.e,{appear:!0,show:t,as:a.Fragment,children:(0,i.jsxs)(s.lG,{as:"div",className:"relative z-10",onClose:l,children:[(0,i.jsx)(n.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50"})}),(0,i.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,i.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,i.jsx)(n.e.Child,{as:a.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,i.jsxs)(s.lG.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all",children:[(0,i.jsx)(s.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-white mb-4",children:o}),(0,i.jsx)("div",{className:"mt-2 text-sm text-gray-300",children:m}),(0,i.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,i.jsx)(r.A,{variant:"secondary",onClick:l,children:h}),c&&(0,i.jsx)(r.A,{variant:g,onClick:c,children:d})]})]})})})})]})})}}}]);
//# sourceMappingURL=297.cb23e281.chunk.js.map