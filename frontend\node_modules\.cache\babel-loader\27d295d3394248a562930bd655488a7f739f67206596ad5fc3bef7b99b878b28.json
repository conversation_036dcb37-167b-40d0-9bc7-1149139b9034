{"ast": null, "code": "import React,{useEffect,useState}from'react';import Alert from'components/Alert';import Button from'components/Button';import Card from'components/Card';import ConfirmModal from'components/ConfirmModal';import Input from'components/Input';import{useAuth}from'contexts/AuthContext';import{useNavigate,useParams}from'react-router-dom';import{campaignAPI}from'services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const CampaignRecipients=()=>{const{id}=useParams();const{user}=useAuth();const navigate=useNavigate();// Redirect to campaigns list if id is undefined\nuseEffect(()=>{if(!id||id==='undefined'){console.log('Invalid campaign ID, redirecting to campaigns list');navigate('/campaigns');return;}console.log('Campaign ID in CampaignRecipients:',id);},[id,navigate]);const[campaign,setCampaign]=useState(null);const[recipients,setRecipients]=useState([]);const[loading,setLoading]=useState(true);const[saving,setSaving]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[emailInput,setEmailInput]=useState('');const[bulkEmails,setBulkEmails]=useState('');const[showBulkModal,setShowBulkModal]=useState(false);const[recipientToRemove,setRecipientToRemove]=useState(null);const[showRemoveModal,setShowRemoveModal]=useState(false);// Fetch campaign and recipients on component mount\nuseEffect(()=>{const fetchCampaignAndRecipients=async()=>{if(!id||id==='undefined')return;try{// Get campaign from API\nconst campaignResponse=await campaignAPI.getCampaign(id);setCampaign(campaignResponse.data.campaign);// Get recipients from API\nconst recipientsResponse=await campaignAPI.getCampaignRecipients(id);setRecipients(recipientsResponse.data.campaignRecipients||[]);setLoading(false);}catch(err){var _err$response,_err$response$data;console.error('Error fetching campaign or recipients:',err);setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Failed to fetch data');setLoading(false);}};fetchCampaignAndRecipients();},[id]);// Add a single recipient\nconst handleAddRecipient=async e=>{e.preventDefault();if(!emailInput.trim()){setError('Please enter an email address');return;}try{setSaving(true);setError('');// Call API to add recipient\nconst response=await campaignAPI.addRecipientToCampaign(id,emailInput);// Update recipients list\nconst updatedRecipients=await campaignAPI.getCampaignRecipients(id);setRecipients(updatedRecipients.data.campaignRecipients||[]);// Update campaign\nconst campaignResponse=await campaignAPI.getCampaign(id);setCampaign(campaignResponse.data.campaign);setSuccess(`Recipient ${emailInput} added successfully`);setEmailInput('');// Clear success message after a few seconds\nsetTimeout(()=>{setSuccess('');},3000);}catch(err){var _err$response2,_err$response2$data;console.error('Error adding recipient:',err);setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||'Failed to add recipient');}finally{setSaving(false);}};// Add bulk recipients\nconst handleAddBulkRecipients=async()=>{if(!bulkEmails.trim()){setError('Please enter email addresses');return;}try{setSaving(true);setError('');// Parse emails (split by commas, newlines, or spaces)\nconst emails=bulkEmails.split(/[,\\n\\s]+/).map(email=>email.trim()).filter(email=>email.length>0);// Call API to add recipients in bulk\nconst response=await campaignAPI.addBulkRecipientsToCampaign(id,emails);// Update recipients list\nconst updatedRecipients=await campaignAPI.getCampaignRecipients(id);setRecipients(updatedRecipients.data.campaignRecipients||[]);// Update campaign\nconst campaignResponse=await campaignAPI.getCampaign(id);setCampaign(campaignResponse.data.campaign);setSuccess(`${response.data.added.length} recipients added successfully`);setBulkEmails('');setShowBulkModal(false);// Clear success message after a few seconds\nsetTimeout(()=>{setSuccess('');},3000);}catch(err){var _err$response3,_err$response3$data;console.error('Error adding bulk recipients:',err);setError(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.message)||'Failed to add recipients');}finally{setSaving(false);}};// Remove a recipient\nconst handleRemoveRecipient=async()=>{if(!recipientToRemove)return;try{setSaving(true);setError('');// Call API to remove recipient\nawait campaignAPI.removeRecipientFromCampaign(id,recipientToRemove);// Update recipients list\nsetRecipients(recipients.filter(r=>r._id!==recipientToRemove));// Update campaign\nconst campaignResponse=await campaignAPI.getCampaign(id);setCampaign(campaignResponse.data.campaign);setSuccess('Recipient removed successfully');// Clear success message after a few seconds\nsetTimeout(()=>{setSuccess('');},3000);}catch(err){var _err$response4,_err$response4$data;console.error('Error removing recipient:',err);setError(((_err$response4=err.response)===null||_err$response4===void 0?void 0:(_err$response4$data=_err$response4.data)===null||_err$response4$data===void 0?void 0:_err$response4$data.message)||'Failed to remove recipient');}finally{setSaving(false);setShowRemoveModal(false);setRecipientToRemove(null);}};if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"})});}if(!campaign){return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Alert,{type:\"error\",message:\"Campaign not found\",className:\"mb-6\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>navigate('/campaigns'),children:\"Back to Campaigns\"})]});}// Check if campaign is already sent\nconst isCampaignSent=campaign.status==='sending'||campaign.status==='completed'||campaign.status==='sent';return/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"Campaign Recipients\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary\",children:campaign.name})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>navigate('/campaigns'),title:\"Go back to the campaigns dashboard\",children:\"Back to Dashboard\"})})]}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(''),className:\"mb-4\"}),success&&/*#__PURE__*/_jsx(Alert,{type:\"success\",message:success,className:\"mb-4\"}),isCampaignSent&&/*#__PURE__*/_jsx(Alert,{type:\"warning\",message:\"This campaign has already been sent. You cannot add or remove recipients.\",className:\"mb-4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium\",children:\"Add Recipients\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-text-secondary\",children:[\"Total Recipients: \",/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:campaign.recipients||0})]})]}),!isCampaignSent&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleAddRecipient,className:\"flex space-x-2 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-1\",children:/*#__PURE__*/_jsx(Input,{id:\"email\",name:\"email\",type:\"email\",placeholder:\"Enter email address\",value:emailInput,onChange:e=>setEmailInput(e.target.value),disabled:saving})}),/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:saving||!emailInput.trim(),children:\"Add\"}),/*#__PURE__*/_jsx(Button,{type:\"button\",variant:\"secondary\",onClick:()=>setShowBulkModal(true),disabled:saving,children:\"Bulk Add\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium mb-2\",children:\"Recipients\"}),recipients.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8 bg-gray-800 rounded-md\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary\",children:\"No recipients added yet\"})}):/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table w-full\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Added\"}),!isCampaignSent&&/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]},\"header-row\")}),/*#__PURE__*/_jsx(\"tbody\",{children:recipients.map(recipient=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:recipient.email}),/*#__PURE__*/_jsx(\"td\",{children:recipient.recipientId&&typeof recipient.recipientId==='object'?`${recipient.recipientId.firstName||''} ${recipient.recipientId.lastName||''}`.trim():'-'}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 text-xs rounded ${recipient.status==='pending'?'bg-gray-700':recipient.status==='sent'?'bg-green-800':recipient.status==='delivered'?'bg-blue-800':recipient.status==='opened'?'bg-purple-800':recipient.status==='clicked'?'bg-yellow-800':recipient.status==='bounced'?'bg-red-800':recipient.status==='complained'?'bg-red-800':'bg-gray-700'}`,children:recipient.status.charAt(0).toUpperCase()+recipient.status.slice(1)})}),/*#__PURE__*/_jsx(\"td\",{children:new Date(recipient.createdAt).toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric'})}),!isCampaignSent&&/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Button,{variant:\"danger\",size:\"sm\",onClick:()=>{setRecipientToRemove(recipient._id);setShowRemoveModal(true);},disabled:saving,children:\"Remove\"})})]},recipient._id))})]})})]})]}),/*#__PURE__*/_jsx(ConfirmModal,{isOpen:showBulkModal,title:\"Add Multiple Recipients\",message:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"mb-2\",children:\"Enter email addresses separated by commas, spaces, or new lines:\"}),/*#__PURE__*/_jsx(\"textarea\",{className:\"w-full h-40 p-2 bg-gray-800 border border-gray-700 rounded-md text-white\",value:bulkEmails,onChange:e=>setBulkEmails(e.target.value),placeholder:\"<EMAIL>, <EMAIL>\"})]}),confirmText:saving?\"Adding...\":\"Add Recipients\",onConfirm:handleAddBulkRecipients,onCancel:()=>{setShowBulkModal(false);setBulkEmails('');}}),/*#__PURE__*/_jsx(ConfirmModal,{isOpen:showRemoveModal,title:\"Remove Recipient\",message:\"Are you sure you want to remove this recipient from the campaign?\",confirmText:saving?\"Removing...\":\"Remove\",onConfirm:handleRemoveRecipient,onCancel:()=>{setShowRemoveModal(false);setRecipientToRemove(null);}})]});};export default CampaignRecipients;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "ConfirmModal", "Input", "useAuth", "useNavigate", "useParams", "campaignAPI", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "CampaignRecipients", "id", "user", "navigate", "console", "log", "campaign", "setCampaign", "recipients", "setRecipients", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "emailInput", "setEmailInput", "bulkEmails", "setBulkEmails", "showBulkModal", "setShowBulkModal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setRecipientToRemove", "showRemoveModal", "setShowRemoveModal", "fetchCampaignAndRecipients", "campaignResponse", "getCampaign", "data", "recipientsResponse", "getCampaignRecipients", "campaignRecipients", "err", "_err$response", "_err$response$data", "response", "message", "handleAddRecipient", "e", "preventDefault", "trim", "addRecipientToCampaign", "updatedRecipients", "setTimeout", "_err$response2", "_err$response2$data", "handleAddBulkRecipients", "emails", "split", "map", "email", "filter", "length", "addBulkRecipientsToCampaign", "added", "_err$response3", "_err$response3$data", "handleRemoveRecipient", "removeRecipientFromCampaign", "r", "_id", "_err$response4", "_err$response4$data", "className", "children", "type", "onClick", "isCampaignSent", "status", "name", "variant", "title", "onClose", "onSubmit", "placeholder", "value", "onChange", "target", "disabled", "recipient", "recipientId", "firstName", "lastName", "char<PERSON>t", "toUpperCase", "slice", "Date", "createdAt", "toLocaleDateString", "year", "month", "day", "size", "isOpen", "confirmText", "onConfirm", "onCancel"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/campaigns/CampaignRecipients.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport ConfirmModal from 'components/ConfirmModal';\nimport Input from 'components/Input';\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\nimport { campaignAPI } from 'services/api';\n\nconst CampaignRecipients: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // Redirect to campaigns list if id is undefined\n  useEffect(() => {\n    if (!id || id === 'undefined') {\n      console.log('Invalid campaign ID, redirecting to campaigns list');\n      navigate('/campaigns');\n      return;\n    }\n    console.log('Campaign ID in CampaignRecipients:', id);\n  }, [id, navigate]);\n\n  const [campaign, setCampaign] = useState<any>(null);\n  const [recipients, setRecipients] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [emailInput, setEmailInput] = useState('');\n  const [bulkEmails, setBulkEmails] = useState('');\n  const [showBulkModal, setShowBulkModal] = useState(false);\n  const [recipientToRemove, setRecipientToRemove] = useState<string | null>(null);\n  const [showRemoveModal, setShowRemoveModal] = useState(false);\n\n  // Fetch campaign and recipients on component mount\n  useEffect(() => {\n    const fetchCampaignAndRecipients = async () => {\n      if (!id || id === 'undefined') return;\n\n      try {\n        // Get campaign from API\n        const campaignResponse = await campaignAPI.getCampaign(id);\n        setCampaign(campaignResponse.data.campaign);\n\n        // Get recipients from API\n        const recipientsResponse = await campaignAPI.getCampaignRecipients(id);\n        setRecipients(recipientsResponse.data.campaignRecipients || []);\n        \n        setLoading(false);\n      } catch (err: any) {\n        console.error('Error fetching campaign or recipients:', err);\n        setError(err.response?.data?.message || 'Failed to fetch data');\n        setLoading(false);\n      }\n    };\n\n    fetchCampaignAndRecipients();\n  }, [id]);\n\n  // Add a single recipient\n  const handleAddRecipient = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!emailInput.trim()) {\n      setError('Please enter an email address');\n      return;\n    }\n\n    try {\n      setSaving(true);\n      setError('');\n\n      // Call API to add recipient\n      const response = await campaignAPI.addRecipientToCampaign(id as string, emailInput);\n      \n      // Update recipients list\n      const updatedRecipients = await campaignAPI.getCampaignRecipients(id as string);\n      setRecipients(updatedRecipients.data.campaignRecipients || []);\n      \n      // Update campaign\n      const campaignResponse = await campaignAPI.getCampaign(id as string);\n      setCampaign(campaignResponse.data.campaign);\n      \n      setSuccess(`Recipient ${emailInput} added successfully`);\n      setEmailInput('');\n      \n      // Clear success message after a few seconds\n      setTimeout(() => {\n        setSuccess('');\n      }, 3000);\n    } catch (err: any) {\n      console.error('Error adding recipient:', err);\n      setError(err.response?.data?.message || 'Failed to add recipient');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Add bulk recipients\n  const handleAddBulkRecipients = async () => {\n    if (!bulkEmails.trim()) {\n      setError('Please enter email addresses');\n      return;\n    }\n\n    try {\n      setSaving(true);\n      setError('');\n\n      // Parse emails (split by commas, newlines, or spaces)\n      const emails = bulkEmails\n        .split(/[,\\n\\s]+/)\n        .map(email => email.trim())\n        .filter(email => email.length > 0);\n\n      // Call API to add recipients in bulk\n      const response = await campaignAPI.addBulkRecipientsToCampaign(id as string, emails);\n      \n      // Update recipients list\n      const updatedRecipients = await campaignAPI.getCampaignRecipients(id as string);\n      setRecipients(updatedRecipients.data.campaignRecipients || []);\n      \n      // Update campaign\n      const campaignResponse = await campaignAPI.getCampaign(id as string);\n      setCampaign(campaignResponse.data.campaign);\n      \n      setSuccess(`${response.data.added.length} recipients added successfully`);\n      setBulkEmails('');\n      setShowBulkModal(false);\n      \n      // Clear success message after a few seconds\n      setTimeout(() => {\n        setSuccess('');\n      }, 3000);\n    } catch (err: any) {\n      console.error('Error adding bulk recipients:', err);\n      setError(err.response?.data?.message || 'Failed to add recipients');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Remove a recipient\n  const handleRemoveRecipient = async () => {\n    if (!recipientToRemove) return;\n\n    try {\n      setSaving(true);\n      setError('');\n\n      // Call API to remove recipient\n      await campaignAPI.removeRecipientFromCampaign(id as string, recipientToRemove);\n      \n      // Update recipients list\n      setRecipients(recipients.filter(r => r._id !== recipientToRemove));\n      \n      // Update campaign\n      const campaignResponse = await campaignAPI.getCampaign(id as string);\n      setCampaign(campaignResponse.data.campaign);\n      \n      setSuccess('Recipient removed successfully');\n      \n      // Clear success message after a few seconds\n      setTimeout(() => {\n        setSuccess('');\n      }, 3000);\n    } catch (err: any) {\n      console.error('Error removing recipient:', err);\n      setError(err.response?.data?.message || 'Failed to remove recipient');\n    } finally {\n      setSaving(false);\n      setShowRemoveModal(false);\n      setRecipientToRemove(null);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  if (!campaign) {\n    return (\n      <div>\n        <Alert\n          type=\"error\"\n          message=\"Campaign not found\"\n          className=\"mb-6\"\n        />\n        <Button onClick={() => navigate('/campaigns')}>\n          Back to Campaigns\n        </Button>\n      </div>\n    );\n  }\n\n  // Check if campaign is already sent\n  const isCampaignSent = campaign.status === 'sending' || campaign.status === 'completed' || campaign.status === 'sent';\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h2 className=\"text-xl font-semibold\">Campaign Recipients</h2>\n            <p className=\"text-text-secondary\">{campaign.name}</p>\n          </div>\n          <div> \n            <Button\n              variant=\"secondary\" \n              onClick={() => navigate('/campaigns')}\n              title=\"Go back to the campaigns dashboard\"\n            >\n              Back to Dashboard\n            </Button>\n          </div>\n        </div>\n\n        {error && (\n          <Alert\n            type=\"error\"\n            message={error}\n            onClose={() => setError('')}\n            className=\"mb-4\"\n          />\n        )}\n\n        {success && (\n          <Alert\n            type=\"success\"\n            message={success}\n            className=\"mb-4\"\n          />\n        )}\n\n        {isCampaignSent && (\n          <Alert\n            type=\"warning\"\n            message=\"This campaign has already been sent. You cannot add or remove recipients.\"\n            className=\"mb-4\"\n          />\n        )}\n\n        <div className=\"mb-6\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <h3 className=\"text-lg font-medium\">Add Recipients</h3>\n            <div className=\"text-sm text-text-secondary\">\n              Total Recipients: <span className=\"font-medium\">{campaign.recipients || 0}</span>\n            </div>\n          </div>\n\n          {!isCampaignSent && (\n            <>\n              <form onSubmit={handleAddRecipient} className=\"flex space-x-2 mb-4\">\n                <div className=\"flex-1\">\n                  <Input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    placeholder=\"Enter email address\"\n                    value={emailInput}\n                    onChange={(e) => setEmailInput(e.target.value)}\n                    disabled={saving}\n                  />\n                </div>\n                <Button\n                  type=\"submit\"\n                  disabled={saving || !emailInput.trim()}\n                >\n                  Add\n                </Button>\n                <Button\n                  type=\"button\"\n                  variant=\"secondary\"\n                  onClick={() => setShowBulkModal(true)}\n                  disabled={saving}\n                >\n                  Bulk Add\n                </Button>\n              </form>\n            </>\n          )}\n        </div>\n\n        <div>\n          <h3 className=\"text-lg font-medium mb-2\">Recipients</h3>\n          \n          {recipients.length === 0 ? (\n            <div className=\"text-center py-8 bg-gray-800 rounded-md\">\n              <p className=\"text-text-secondary\">No recipients added yet</p>\n            </div>\n          ) : (\n            <div className=\"table-container\">\n              <table className=\"table w-full\">\n                <thead>\n                  <tr key=\"header-row\">\n                    <th>Email</th>\n                    <th>Name</th>\n                    <th>Status</th>\n                    <th>Added</th>\n                    {!isCampaignSent && <th>Actions</th>}\n                  </tr>\n                </thead>\n                <tbody>\n                  {recipients.map((recipient) => (\n                    <tr key={recipient._id}>\n                      <td>{recipient.email}</td>\n                      <td>\n                        {recipient.recipientId && typeof recipient.recipientId === 'object' ? \n                          `${recipient.recipientId.firstName || ''} ${recipient.recipientId.lastName || ''}`.trim() : \n                          '-'\n                        }\n                      </td>\n                      <td>\n                        <span className={`px-2 py-1 text-xs rounded ${\n                          recipient.status === 'pending' ? 'bg-gray-700' :\n                          recipient.status === 'sent' ? 'bg-green-800' :\n                          recipient.status === 'delivered' ? 'bg-blue-800' :\n                          recipient.status === 'opened' ? 'bg-purple-800' :\n                          recipient.status === 'clicked' ? 'bg-yellow-800' :\n                          recipient.status === 'bounced' ? 'bg-red-800' :\n                          recipient.status === 'complained' ? 'bg-red-800' :\n                          'bg-gray-700'\n                        }`}>\n                          {recipient.status.charAt(0).toUpperCase() + recipient.status.slice(1)}\n                        </span>\n                      </td>\n                      <td>\n                        {new Date(recipient.createdAt).toLocaleDateString('en-US', {\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })}\n                      </td>\n                      {!isCampaignSent && (\n                        <td>\n                          <Button\n                            variant=\"danger\"\n                            size=\"sm\"\n                            onClick={() => {\n                              setRecipientToRemove(recipient._id);\n                              setShowRemoveModal(true);\n                            }}\n                            disabled={saving}\n                          >\n                            Remove\n                          </Button>\n                        </td>\n                      )}\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Bulk Add Modal */}\n      <ConfirmModal\n        isOpen={showBulkModal}\n        title=\"Add Multiple Recipients\"\n        message={\n          <div>\n            <p className=\"mb-2\">Enter email addresses separated by commas, spaces, or new lines:</p>\n            <textarea\n              className=\"w-full h-40 p-2 bg-gray-800 border border-gray-700 rounded-md text-white\"\n              value={bulkEmails}\n              onChange={(e) => setBulkEmails(e.target.value)}\n              placeholder=\"<EMAIL>, <EMAIL>\"\n            />\n          </div>\n        }\n        confirmText={saving ? \"Adding...\" : \"Add Recipients\"}\n        onConfirm={handleAddBulkRecipients}\n        onCancel={() => {\n          setShowBulkModal(false);\n          setBulkEmails('');\n        }}\n      />\n\n      {/* Remove Recipient Modal */}\n      <ConfirmModal\n        isOpen={showRemoveModal}\n        title=\"Remove Recipient\"\n        message=\"Are you sure you want to remove this recipient from the campaign?\"\n        confirmText={saving ? \"Removing...\" : \"Remove\"}\n        onConfirm={handleRemoveRecipient}\n        onCancel={() => {\n          setShowRemoveModal(false);\n          setRecipientToRemove(null);\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default CampaignRecipients;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,IAAI,KAAM,iBAAiB,CAClC,MAAO,CAAAC,YAAY,KAAM,yBAAyB,CAClD,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC,OAASC,OAAO,KAAQ,sBAAsB,CAC9C,OACEC,WAAW,CACXC,SAAS,KACJ,kBAAkB,CACzB,OAASC,WAAW,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE3C,KAAM,CAAAC,kBAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAAEC,EAAG,CAAC,CAAGT,SAAS,CAAiB,CAAC,CAC1C,KAAM,CAAEU,IAAK,CAAC,CAAGZ,OAAO,CAAC,CAAC,CAC1B,KAAM,CAAAa,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAE9B;AACAR,SAAS,CAAC,IAAM,CACd,GAAI,CAACkB,EAAE,EAAIA,EAAE,GAAK,WAAW,CAAE,CAC7BG,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CACjEF,QAAQ,CAAC,YAAY,CAAC,CACtB,OACF,CACAC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEJ,EAAE,CAAC,CACvD,CAAC,CAAE,CAACA,EAAE,CAAEE,QAAQ,CAAC,CAAC,CAElB,KAAM,CAACG,QAAQ,CAAEC,WAAW,CAAC,CAAGvB,QAAQ,CAAM,IAAI,CAAC,CACnD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAQ,EAAE,CAAC,CACvD,KAAM,CAAC0B,OAAO,CAAEC,UAAU,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4B,MAAM,CAAEC,SAAS,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAC8B,KAAK,CAAEC,QAAQ,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACgC,OAAO,CAAEC,UAAU,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkC,UAAU,CAAEC,aAAa,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACoC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACwC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzC,QAAQ,CAAgB,IAAI,CAAC,CAC/E,KAAM,CAAC0C,eAAe,CAAEC,kBAAkB,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACAD,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6C,0BAA0B,CAAG,KAAAA,CAAA,GAAY,CAC7C,GAAI,CAAC3B,EAAE,EAAIA,EAAE,GAAK,WAAW,CAAE,OAE/B,GAAI,CACF;AACA,KAAM,CAAA4B,gBAAgB,CAAG,KAAM,CAAApC,WAAW,CAACqC,WAAW,CAAC7B,EAAE,CAAC,CAC1DM,WAAW,CAACsB,gBAAgB,CAACE,IAAI,CAACzB,QAAQ,CAAC,CAE3C;AACA,KAAM,CAAA0B,kBAAkB,CAAG,KAAM,CAAAvC,WAAW,CAACwC,qBAAqB,CAAChC,EAAE,CAAC,CACtEQ,aAAa,CAACuB,kBAAkB,CAACD,IAAI,CAACG,kBAAkB,EAAI,EAAE,CAAC,CAE/DvB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAE,MAAOwB,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBjC,OAAO,CAACU,KAAK,CAAC,wCAAwC,CAAEqB,GAAG,CAAC,CAC5DpB,QAAQ,CAAC,EAAAqB,aAAA,CAAAD,GAAG,CAACG,QAAQ,UAAAF,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcL,IAAI,UAAAM,kBAAA,iBAAlBA,kBAAA,CAAoBE,OAAO,GAAI,sBAAsB,CAAC,CAC/D5B,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDiB,0BAA0B,CAAC,CAAC,CAC9B,CAAC,CAAE,CAAC3B,EAAE,CAAC,CAAC,CAER;AACA,KAAM,CAAAuC,kBAAkB,CAAG,KAAO,CAAAC,CAAkB,EAAK,CACvDA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAI,CAACxB,UAAU,CAACyB,IAAI,CAAC,CAAC,CAAE,CACtB5B,QAAQ,CAAC,+BAA+B,CAAC,CACzC,OACF,CAEA,GAAI,CACFF,SAAS,CAAC,IAAI,CAAC,CACfE,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,KAAM,CAAAuB,QAAQ,CAAG,KAAM,CAAA7C,WAAW,CAACmD,sBAAsB,CAAC3C,EAAE,CAAYiB,UAAU,CAAC,CAEnF;AACA,KAAM,CAAA2B,iBAAiB,CAAG,KAAM,CAAApD,WAAW,CAACwC,qBAAqB,CAAChC,EAAY,CAAC,CAC/EQ,aAAa,CAACoC,iBAAiB,CAACd,IAAI,CAACG,kBAAkB,EAAI,EAAE,CAAC,CAE9D;AACA,KAAM,CAAAL,gBAAgB,CAAG,KAAM,CAAApC,WAAW,CAACqC,WAAW,CAAC7B,EAAY,CAAC,CACpEM,WAAW,CAACsB,gBAAgB,CAACE,IAAI,CAACzB,QAAQ,CAAC,CAE3CW,UAAU,CAAC,aAAaC,UAAU,qBAAqB,CAAC,CACxDC,aAAa,CAAC,EAAE,CAAC,CAEjB;AACA2B,UAAU,CAAC,IAAM,CACf7B,UAAU,CAAC,EAAE,CAAC,CAChB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOkB,GAAQ,CAAE,KAAAY,cAAA,CAAAC,mBAAA,CACjB5C,OAAO,CAACU,KAAK,CAAC,yBAAyB,CAAEqB,GAAG,CAAC,CAC7CpB,QAAQ,CAAC,EAAAgC,cAAA,CAAAZ,GAAG,CAACG,QAAQ,UAAAS,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAchB,IAAI,UAAAiB,mBAAA,iBAAlBA,mBAAA,CAAoBT,OAAO,GAAI,yBAAyB,CAAC,CACpE,CAAC,OAAS,CACR1B,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED;AACA,KAAM,CAAAoC,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,CAAC7B,UAAU,CAACuB,IAAI,CAAC,CAAC,CAAE,CACtB5B,QAAQ,CAAC,8BAA8B,CAAC,CACxC,OACF,CAEA,GAAI,CACFF,SAAS,CAAC,IAAI,CAAC,CACfE,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,KAAM,CAAAmC,MAAM,CAAG9B,UAAU,CACtB+B,KAAK,CAAC,UAAU,CAAC,CACjBC,GAAG,CAACC,KAAK,EAAIA,KAAK,CAACV,IAAI,CAAC,CAAC,CAAC,CAC1BW,MAAM,CAACD,KAAK,EAAIA,KAAK,CAACE,MAAM,CAAG,CAAC,CAAC,CAEpC;AACA,KAAM,CAAAjB,QAAQ,CAAG,KAAM,CAAA7C,WAAW,CAAC+D,2BAA2B,CAACvD,EAAE,CAAYiD,MAAM,CAAC,CAEpF;AACA,KAAM,CAAAL,iBAAiB,CAAG,KAAM,CAAApD,WAAW,CAACwC,qBAAqB,CAAChC,EAAY,CAAC,CAC/EQ,aAAa,CAACoC,iBAAiB,CAACd,IAAI,CAACG,kBAAkB,EAAI,EAAE,CAAC,CAE9D;AACA,KAAM,CAAAL,gBAAgB,CAAG,KAAM,CAAApC,WAAW,CAACqC,WAAW,CAAC7B,EAAY,CAAC,CACpEM,WAAW,CAACsB,gBAAgB,CAACE,IAAI,CAACzB,QAAQ,CAAC,CAE3CW,UAAU,CAAC,GAAGqB,QAAQ,CAACP,IAAI,CAAC0B,KAAK,CAACF,MAAM,gCAAgC,CAAC,CACzElC,aAAa,CAAC,EAAE,CAAC,CACjBE,gBAAgB,CAAC,KAAK,CAAC,CAEvB;AACAuB,UAAU,CAAC,IAAM,CACf7B,UAAU,CAAC,EAAE,CAAC,CAChB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOkB,GAAQ,CAAE,KAAAuB,cAAA,CAAAC,mBAAA,CACjBvD,OAAO,CAACU,KAAK,CAAC,+BAA+B,CAAEqB,GAAG,CAAC,CACnDpB,QAAQ,CAAC,EAAA2C,cAAA,CAAAvB,GAAG,CAACG,QAAQ,UAAAoB,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc3B,IAAI,UAAA4B,mBAAA,iBAAlBA,mBAAA,CAAoBpB,OAAO,GAAI,0BAA0B,CAAC,CACrE,CAAC,OAAS,CACR1B,SAAS,CAAC,KAAK,CAAC,CAClB,CACF,CAAC,CAED;AACA,KAAM,CAAA+C,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CAACpC,iBAAiB,CAAE,OAExB,GAAI,CACFX,SAAS,CAAC,IAAI,CAAC,CACfE,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,KAAM,CAAAtB,WAAW,CAACoE,2BAA2B,CAAC5D,EAAE,CAAYuB,iBAAiB,CAAC,CAE9E;AACAf,aAAa,CAACD,UAAU,CAAC8C,MAAM,CAACQ,CAAC,EAAIA,CAAC,CAACC,GAAG,GAAKvC,iBAAiB,CAAC,CAAC,CAElE;AACA,KAAM,CAAAK,gBAAgB,CAAG,KAAM,CAAApC,WAAW,CAACqC,WAAW,CAAC7B,EAAY,CAAC,CACpEM,WAAW,CAACsB,gBAAgB,CAACE,IAAI,CAACzB,QAAQ,CAAC,CAE3CW,UAAU,CAAC,gCAAgC,CAAC,CAE5C;AACA6B,UAAU,CAAC,IAAM,CACf7B,UAAU,CAAC,EAAE,CAAC,CAChB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOkB,GAAQ,CAAE,KAAA6B,cAAA,CAAAC,mBAAA,CACjB7D,OAAO,CAACU,KAAK,CAAC,2BAA2B,CAAEqB,GAAG,CAAC,CAC/CpB,QAAQ,CAAC,EAAAiD,cAAA,CAAA7B,GAAG,CAACG,QAAQ,UAAA0B,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcjC,IAAI,UAAAkC,mBAAA,iBAAlBA,mBAAA,CAAoB1B,OAAO,GAAI,4BAA4B,CAAC,CACvE,CAAC,OAAS,CACR1B,SAAS,CAAC,KAAK,CAAC,CAChBc,kBAAkB,CAAC,KAAK,CAAC,CACzBF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CACF,CAAC,CAED,GAAIf,OAAO,CAAE,CACX,mBACEf,IAAA,QAAKuE,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDxE,IAAA,QAAKuE,SAAS,CAAC,0EAA0E,CAAM,CAAC,CAC7F,CAAC,CAEV,CAEA,GAAI,CAAC5D,QAAQ,CAAE,CACb,mBACET,KAAA,QAAAsE,QAAA,eACExE,IAAA,CAACV,KAAK,EACJmF,IAAI,CAAC,OAAO,CACZ7B,OAAO,CAAC,oBAAoB,CAC5B2B,SAAS,CAAC,MAAM,CACjB,CAAC,cACFvE,IAAA,CAACT,MAAM,EAACmF,OAAO,CAAEA,CAAA,GAAMlE,QAAQ,CAAC,YAAY,CAAE,CAAAgE,QAAA,CAAC,mBAE/C,CAAQ,CAAC,EACN,CAAC,CAEV,CAEA;AACA,KAAM,CAAAG,cAAc,CAAGhE,QAAQ,CAACiE,MAAM,GAAK,SAAS,EAAIjE,QAAQ,CAACiE,MAAM,GAAK,WAAW,EAAIjE,QAAQ,CAACiE,MAAM,GAAK,MAAM,CAErH,mBACE1E,KAAA,CAACV,IAAI,EAAAgF,QAAA,eACHtE,KAAA,QAAKqE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBtE,KAAA,QAAKqE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,OAAIuE,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAC9DxE,IAAA,MAAGuE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE7D,QAAQ,CAACkE,IAAI,CAAI,CAAC,EACnD,CAAC,cACN7E,IAAA,QAAAwE,QAAA,cACExE,IAAA,CAACT,MAAM,EACLuF,OAAO,CAAC,WAAW,CACnBJ,OAAO,CAAEA,CAAA,GAAMlE,QAAQ,CAAC,YAAY,CAAE,CACtCuE,KAAK,CAAC,oCAAoC,CAAAP,QAAA,CAC3C,mBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CAELrD,KAAK,eACJnB,IAAA,CAACV,KAAK,EACJmF,IAAI,CAAC,OAAO,CACZ7B,OAAO,CAAEzB,KAAM,CACf6D,OAAO,CAAEA,CAAA,GAAM5D,QAAQ,CAAC,EAAE,CAAE,CAC5BmD,SAAS,CAAC,MAAM,CACjB,CACF,CAEAlD,OAAO,eACNrB,IAAA,CAACV,KAAK,EACJmF,IAAI,CAAC,SAAS,CACd7B,OAAO,CAAEvB,OAAQ,CACjBkD,SAAS,CAAC,MAAM,CACjB,CACF,CAEAI,cAAc,eACb3E,IAAA,CAACV,KAAK,EACJmF,IAAI,CAAC,SAAS,CACd7B,OAAO,CAAC,2EAA2E,CACnF2B,SAAS,CAAC,MAAM,CACjB,CACF,cAEDrE,KAAA,QAAKqE,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBtE,KAAA,QAAKqE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDxE,IAAA,OAAIuE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACvDtE,KAAA,QAAKqE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,oBACzB,cAAAxE,IAAA,SAAMuE,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE7D,QAAQ,CAACE,UAAU,EAAI,CAAC,CAAO,CAAC,EAC9E,CAAC,EACH,CAAC,CAEL,CAAC8D,cAAc,eACd3E,IAAA,CAAAI,SAAA,EAAAoE,QAAA,cACEtE,KAAA,SAAM+E,QAAQ,CAAEpC,kBAAmB,CAAC0B,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjExE,IAAA,QAAKuE,SAAS,CAAC,QAAQ,CAAAC,QAAA,cACrBxE,IAAA,CAACN,KAAK,EACJY,EAAE,CAAC,OAAO,CACVuE,IAAI,CAAC,OAAO,CACZJ,IAAI,CAAC,OAAO,CACZS,WAAW,CAAC,qBAAqB,CACjCC,KAAK,CAAE5D,UAAW,CAClB6D,QAAQ,CAAGtC,CAAC,EAAKtB,aAAa,CAACsB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE,CAC/CG,QAAQ,CAAErE,MAAO,CAClB,CAAC,CACC,CAAC,cACNjB,IAAA,CAACT,MAAM,EACLkF,IAAI,CAAC,QAAQ,CACba,QAAQ,CAAErE,MAAM,EAAI,CAACM,UAAU,CAACyB,IAAI,CAAC,CAAE,CAAAwB,QAAA,CACxC,KAED,CAAQ,CAAC,cACTxE,IAAA,CAACT,MAAM,EACLkF,IAAI,CAAC,QAAQ,CACbK,OAAO,CAAC,WAAW,CACnBJ,OAAO,CAAEA,CAAA,GAAM9C,gBAAgB,CAAC,IAAI,CAAE,CACtC0D,QAAQ,CAAErE,MAAO,CAAAuD,QAAA,CAClB,UAED,CAAQ,CAAC,EACL,CAAC,CACP,CACH,EACE,CAAC,cAENtE,KAAA,QAAAsE,QAAA,eACExE,IAAA,OAAIuE,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,CAEvD3D,UAAU,CAAC+C,MAAM,GAAK,CAAC,cACtB5D,IAAA,QAAKuE,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDxE,IAAA,MAAGuE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,yBAAuB,CAAG,CAAC,CAC3D,CAAC,cAENxE,IAAA,QAAKuE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BtE,KAAA,UAAOqE,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC7BxE,IAAA,UAAAwE,QAAA,cACEtE,KAAA,OAAAsE,QAAA,eACExE,IAAA,OAAAwE,QAAA,CAAI,OAAK,CAAI,CAAC,cACdxE,IAAA,OAAAwE,QAAA,CAAI,MAAI,CAAI,CAAC,cACbxE,IAAA,OAAAwE,QAAA,CAAI,QAAM,CAAI,CAAC,cACfxE,IAAA,OAAAwE,QAAA,CAAI,OAAK,CAAI,CAAC,CACb,CAACG,cAAc,eAAI3E,IAAA,OAAAwE,QAAA,CAAI,SAAO,CAAI,CAAC,GAL9B,YAMJ,CAAC,CACA,CAAC,cACRxE,IAAA,UAAAwE,QAAA,CACG3D,UAAU,CAAC4C,GAAG,CAAE8B,SAAS,eACxBrF,KAAA,OAAAsE,QAAA,eACExE,IAAA,OAAAwE,QAAA,CAAKe,SAAS,CAAC7B,KAAK,CAAK,CAAC,cAC1B1D,IAAA,OAAAwE,QAAA,CACGe,SAAS,CAACC,WAAW,EAAI,MAAO,CAAAD,SAAS,CAACC,WAAW,GAAK,QAAQ,CACjE,GAAGD,SAAS,CAACC,WAAW,CAACC,SAAS,EAAI,EAAE,IAAIF,SAAS,CAACC,WAAW,CAACE,QAAQ,EAAI,EAAE,EAAE,CAAC1C,IAAI,CAAC,CAAC,CACzF,GAAG,CAEH,CAAC,cACLhD,IAAA,OAAAwE,QAAA,cACExE,IAAA,SAAMuE,SAAS,CAAE,6BACfgB,SAAS,CAACX,MAAM,GAAK,SAAS,CAAG,aAAa,CAC9CW,SAAS,CAACX,MAAM,GAAK,MAAM,CAAG,cAAc,CAC5CW,SAAS,CAACX,MAAM,GAAK,WAAW,CAAG,aAAa,CAChDW,SAAS,CAACX,MAAM,GAAK,QAAQ,CAAG,eAAe,CAC/CW,SAAS,CAACX,MAAM,GAAK,SAAS,CAAG,eAAe,CAChDW,SAAS,CAACX,MAAM,GAAK,SAAS,CAAG,YAAY,CAC7CW,SAAS,CAACX,MAAM,GAAK,YAAY,CAAG,YAAY,CAChD,aAAa,EACZ,CAAAJ,QAAA,CACAe,SAAS,CAACX,MAAM,CAACe,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGL,SAAS,CAACX,MAAM,CAACiB,KAAK,CAAC,CAAC,CAAC,CACjE,CAAC,CACL,CAAC,cACL7F,IAAA,OAAAwE,QAAA,CACG,GAAI,CAAAsB,IAAI,CAACP,SAAS,CAACQ,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAE,CACzDC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SACP,CAAC,CAAC,CACA,CAAC,CACJ,CAACxB,cAAc,eACd3E,IAAA,OAAAwE,QAAA,cACExE,IAAA,CAACT,MAAM,EACLuF,OAAO,CAAC,QAAQ,CAChBsB,IAAI,CAAC,IAAI,CACT1B,OAAO,CAAEA,CAAA,GAAM,CACb5C,oBAAoB,CAACyD,SAAS,CAACnB,GAAG,CAAC,CACnCpC,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACFsD,QAAQ,CAAErE,MAAO,CAAAuD,QAAA,CAClB,QAED,CAAQ,CAAC,CACP,CACL,GA3CMe,SAAS,CAACnB,GA4Cf,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,EACE,CAAC,EACH,CAAC,cAGNpE,IAAA,CAACP,YAAY,EACX4G,MAAM,CAAE1E,aAAc,CACtBoD,KAAK,CAAC,yBAAyB,CAC/BnC,OAAO,cACL1C,KAAA,QAAAsE,QAAA,eACExE,IAAA,MAAGuE,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kEAAgE,CAAG,CAAC,cACxFxE,IAAA,aACEuE,SAAS,CAAC,0EAA0E,CACpFY,KAAK,CAAE1D,UAAW,CAClB2D,QAAQ,CAAGtC,CAAC,EAAKpB,aAAa,CAACoB,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE,CAC/CD,WAAW,CAAC,4CAA4C,CACzD,CAAC,EACC,CACN,CACDoB,WAAW,CAAErF,MAAM,CAAG,WAAW,CAAG,gBAAiB,CACrDsF,SAAS,CAAEjD,uBAAwB,CACnCkD,QAAQ,CAAEA,CAAA,GAAM,CACd5E,gBAAgB,CAAC,KAAK,CAAC,CACvBF,aAAa,CAAC,EAAE,CAAC,CACnB,CAAE,CACH,CAAC,cAGF1B,IAAA,CAACP,YAAY,EACX4G,MAAM,CAAEtE,eAAgB,CACxBgD,KAAK,CAAC,kBAAkB,CACxBnC,OAAO,CAAC,mEAAmE,CAC3E0D,WAAW,CAAErF,MAAM,CAAG,aAAa,CAAG,QAAS,CAC/CsF,SAAS,CAAEtC,qBAAsB,CACjCuC,QAAQ,CAAEA,CAAA,GAAM,CACdxE,kBAAkB,CAAC,KAAK,CAAC,CACzBF,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CACH,CAAC,EACE,CAAC,CAEX,CAAC,CAED,cAAe,CAAAzB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}