{"ast": null, "code": "import { useMonitorOutput } from './useMonitorOutput.js';\nexport function useCollectedProps(collector, monitor, connector) {\n  return useMonitorOutput(monitor, collector || (() => ({})), () => connector.reconnect());\n}", "map": {"version": 3, "names": ["useMonitorOutput", "useCollectedProps", "collector", "monitor", "connector", "reconnect"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd\\src\\hooks\\useCollectedProps.ts"], "sourcesContent": ["import type { Connector } from '../internals/index.js'\nimport type { <PERSON>lerManager, MonitorEventEmitter } from '../types/index.js'\nimport { useMonitorOutput } from './useMonitorOutput.js'\n\nexport function useCollectedProps<Collected, Monitor extends HandlerManager>(\n\tcollector: ((monitor: Monitor) => Collected) | undefined,\n\tmonitor: Monitor & MonitorEventEmitter,\n\tconnector: Connector,\n) {\n\treturn useMonitorOutput(monitor, collector || (() => ({} as Collected)), () =>\n\t\tconnector.reconnect(),\n\t)\n}\n"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,uBAAuB;AAExD,OAAO,SAASC,iBAAiBA,CAChCC,SAAwD,EACxDC,OAAsC,EACtCC,SAAoB,EACnB;EACD,OAAOJ,gBAAgB,CAACG,OAAO,EAAED,SAAS,KAAK,OAAO,EAAE,CAAc,CAAC,EAAE,MACxEE,SAAS,CAACC,SAAS,EAAE,CACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}