{"ast": null, "code": "import { microTask as i } from './micro-task.js';\nfunction o() {\n  let n = [],\n    r = {\n      addEventListener(e, t, s, a) {\n        return e.addEventListener(t, s, a), r.add(() => e.removeEventListener(t, s, a));\n      },\n      requestAnimationFrame() {\n        let t = requestAnimationFrame(...arguments);\n        return r.add(() => cancelAnimationFrame(t));\n      },\n      nextFrame() {\n        for (var _len = arguments.length, e = new Array(_len), _key = 0; _key < _len; _key++) {\n          e[_key] = arguments[_key];\n        }\n        return r.requestAnimationFrame(() => r.requestAnimationFrame(...e));\n      },\n      setTimeout() {\n        let t = setTimeout(...arguments);\n        return r.add(() => clearTimeout(t));\n      },\n      microTask() {\n        for (var _len2 = arguments.length, e = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n          e[_key2] = arguments[_key2];\n        }\n        let t = {\n          current: !0\n        };\n        return i(() => {\n          t.current && e[0]();\n        }), r.add(() => {\n          t.current = !1;\n        });\n      },\n      style(e, t, s) {\n        let a = e.style.getPropertyValue(t);\n        return Object.assign(e.style, {\n          [t]: s\n        }), this.add(() => {\n          Object.assign(e.style, {\n            [t]: a\n          });\n        });\n      },\n      group(e) {\n        let t = o();\n        return e(t), this.add(() => t.dispose());\n      },\n      add(e) {\n        return n.includes(e) || n.push(e), () => {\n          let t = n.indexOf(e);\n          if (t >= 0) for (let s of n.splice(t, 1)) s();\n        };\n      },\n      dispose() {\n        for (let e of n.splice(0)) e();\n      }\n    };\n  return r;\n}\nexport { o as disposables };", "map": {"version": 3, "names": ["microTask", "i", "o", "n", "r", "addEventListener", "e", "t", "s", "a", "add", "removeEventListener", "requestAnimationFrame", "arguments", "cancelAnimationFrame", "next<PERSON><PERSON><PERSON>", "_len", "length", "Array", "_key", "setTimeout", "clearTimeout", "_len2", "_key2", "current", "style", "getPropertyValue", "Object", "assign", "group", "dispose", "includes", "push", "indexOf", "splice", "disposables"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/node_modules/@headlessui/react/dist/utils/disposables.js"], "sourcesContent": ["import{microTask as i}from'./micro-task.js';function o(){let n=[],r={addEventListener(e,t,s,a){return e.addEventListener(t,s,a),r.add(()=>e.removeEventListener(t,s,a))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e))},setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return i(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,s){let a=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:s}),this.add(()=>{Object.assign(e.style,{[t]:a})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return n.includes(e)||n.push(e),()=>{let t=n.indexOf(e);if(t>=0)for(let s of n.splice(t,1))s()}},dispose(){for(let e of n.splice(0))e()}};return r}export{o as disposables};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAAC,EAAE;IAACC,CAAC,GAAC;MAACC,gBAAgBA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;QAAC,OAAOH,CAAC,CAACD,gBAAgB,CAACE,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,EAACL,CAAC,CAACM,GAAG,CAAC,MAAIJ,CAAC,CAACK,mBAAmB,CAACJ,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,CAAC;MAAA,CAAC;MAACG,qBAAqBA,CAAA,EAAM;QAAC,IAAIL,CAAC,GAACK,qBAAqB,CAAC,GAAAC,SAAI,CAAC;QAAC,OAAOT,CAAC,CAACM,GAAG,CAAC,MAAII,oBAAoB,CAACP,CAAC,CAAC,CAAC;MAAA,CAAC;MAACQ,SAASA,CAAA,EAAM;QAAA,SAAAC,IAAA,GAAAH,SAAA,CAAAI,MAAA,EAAFX,CAAC,OAAAY,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;UAADb,CAAC,CAAAa,IAAA,IAAAN,SAAA,CAAAM,IAAA;QAAA;QAAE,OAAOf,CAAC,CAACQ,qBAAqB,CAAC,MAAIR,CAAC,CAACQ,qBAAqB,CAAC,GAAGN,CAAC,CAAC,CAAC;MAAA,CAAC;MAACc,UAAUA,CAAA,EAAM;QAAC,IAAIb,CAAC,GAACa,UAAU,CAAC,GAAAP,SAAI,CAAC;QAAC,OAAOT,CAAC,CAACM,GAAG,CAAC,MAAIW,YAAY,CAACd,CAAC,CAAC,CAAC;MAAA,CAAC;MAACP,SAASA,CAAA,EAAM;QAAA,SAAAsB,KAAA,GAAAT,SAAA,CAAAI,MAAA,EAAFX,CAAC,OAAAY,KAAA,CAAAI,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;UAADjB,CAAC,CAAAiB,KAAA,IAAAV,SAAA,CAAAU,KAAA;QAAA;QAAE,IAAIhB,CAAC,GAAC;UAACiB,OAAO,EAAC,CAAC;QAAC,CAAC;QAAC,OAAOvB,CAAC,CAAC,MAAI;UAACM,CAAC,CAACiB,OAAO,IAAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC,CAAC,EAACF,CAAC,CAACM,GAAG,CAAC,MAAI;UAACH,CAAC,CAACiB,OAAO,GAAC,CAAC,CAAC;QAAA,CAAC,CAAC;MAAA,CAAC;MAACC,KAAKA,CAACnB,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACH,CAAC,CAACmB,KAAK,CAACC,gBAAgB,CAACnB,CAAC,CAAC;QAAC,OAAOoB,MAAM,CAACC,MAAM,CAACtB,CAAC,CAACmB,KAAK,EAAC;UAAC,CAAClB,CAAC,GAAEC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACE,GAAG,CAAC,MAAI;UAACiB,MAAM,CAACC,MAAM,CAACtB,CAAC,CAACmB,KAAK,EAAC;YAAC,CAAClB,CAAC,GAAEE;UAAC,CAAC,CAAC;QAAA,CAAC,CAAC;MAAA,CAAC;MAACoB,KAAKA,CAACvB,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACL,CAAC,CAAC,CAAC;QAAC,OAAOI,CAAC,CAACC,CAAC,CAAC,EAAC,IAAI,CAACG,GAAG,CAAC,MAAIH,CAAC,CAACuB,OAAO,CAAC,CAAC,CAAC;MAAA,CAAC;MAACpB,GAAGA,CAACJ,CAAC,EAAC;QAAC,OAAOH,CAAC,CAAC4B,QAAQ,CAACzB,CAAC,CAAC,IAAEH,CAAC,CAAC6B,IAAI,CAAC1B,CAAC,CAAC,EAAC,MAAI;UAAC,IAAIC,CAAC,GAACJ,CAAC,CAAC8B,OAAO,CAAC3B,CAAC,CAAC;UAAC,IAAGC,CAAC,IAAE,CAAC,EAAC,KAAI,IAAIC,CAAC,IAAIL,CAAC,CAAC+B,MAAM,CAAC3B,CAAC,EAAC,CAAC,CAAC,EAACC,CAAC,CAAC,CAAC;QAAA,CAAC;MAAA,CAAC;MAACsB,OAAOA,CAAA,EAAE;QAAC,KAAI,IAAIxB,CAAC,IAAIH,CAAC,CAAC+B,MAAM,CAAC,CAAC,CAAC,EAAC5B,CAAC,CAAC,CAAC;MAAA;IAAC,CAAC;EAAC,OAAOF,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIiC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}