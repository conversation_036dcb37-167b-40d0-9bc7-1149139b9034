{"ast": null, "code": "import { isFirefox, isSafari } from './BrowserDetector.js';\nimport { MonotonicInterpolant } from './MonotonicInterpolant.js';\nconst ELEMENT_NODE = 1;\nexport function getNodeClientOffset(node) {\n  const el = node.nodeType === ELEMENT_NODE ? node : node.parentElement;\n  if (!el) {\n    return null;\n  }\n  const {\n    top,\n    left\n  } = el.getBoundingClientRect();\n  return {\n    x: left,\n    y: top\n  };\n}\nexport function getEventClientOffset(e) {\n  return {\n    x: e.clientX,\n    y: e.clientY\n  };\n}\nfunction isImageNode(node) {\n  var ref;\n  return node.nodeName === 'IMG' && (isFirefox() || !((ref = document.documentElement) === null || ref === void 0 ? void 0 : ref.contains(node)));\n}\nfunction getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight) {\n  let dragPreviewWidth = isImage ? dragPreview.width : sourceWidth;\n  let dragPreviewHeight = isImage ? dragPreview.height : sourceHeight;\n  // Work around @2x coordinate discrepancies in browsers\n  if (isSafari() && isImage) {\n    dragPreviewHeight /= window.devicePixelRatio;\n    dragPreviewWidth /= window.devicePixelRatio;\n  }\n  return {\n    dragPreviewWidth,\n    dragPreviewHeight\n  };\n}\nexport function getDragPreviewOffset(sourceNode, dragPreview, clientOffset, anchorPoint, offsetPoint) {\n  // The browsers will use the image intrinsic size under different conditions.\n  // Firefox only cares if it's an image, but WebKit also wants it to be detached.\n  const isImage = isImageNode(dragPreview);\n  const dragPreviewNode = isImage ? sourceNode : dragPreview;\n  const dragPreviewNodeOffsetFromClient = getNodeClientOffset(dragPreviewNode);\n  const offsetFromDragPreview = {\n    x: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n    y: clientOffset.y - dragPreviewNodeOffsetFromClient.y\n  };\n  const {\n    offsetWidth: sourceWidth,\n    offsetHeight: sourceHeight\n  } = sourceNode;\n  const {\n    anchorX,\n    anchorY\n  } = anchorPoint;\n  const {\n    dragPreviewWidth,\n    dragPreviewHeight\n  } = getDragPreviewSize(isImage, dragPreview, sourceWidth, sourceHeight);\n  const calculateYOffset = () => {\n    const interpolantY = new MonotonicInterpolant([0, 0.5, 1], [\n    // Dock to the top\n    offsetFromDragPreview.y,\n    // Align at the center\n    offsetFromDragPreview.y / sourceHeight * dragPreviewHeight,\n    // Dock to the bottom\n    offsetFromDragPreview.y + dragPreviewHeight - sourceHeight]);\n    let y = interpolantY.interpolate(anchorY);\n    // Work around Safari 8 positioning bug\n    if (isSafari() && isImage) {\n      // We'll have to wait for @3x to see if this is entirely correct\n      y += (window.devicePixelRatio - 1) * dragPreviewHeight;\n    }\n    return y;\n  };\n  const calculateXOffset = () => {\n    // Interpolate coordinates depending on anchor point\n    // If you know a simpler way to do this, let me know\n    const interpolantX = new MonotonicInterpolant([0, 0.5, 1], [\n    // Dock to the left\n    offsetFromDragPreview.x,\n    // Align at the center\n    offsetFromDragPreview.x / sourceWidth * dragPreviewWidth,\n    // Dock to the right\n    offsetFromDragPreview.x + dragPreviewWidth - sourceWidth]);\n    return interpolantX.interpolate(anchorX);\n  };\n  // Force offsets if specified in the options.\n  const {\n    offsetX,\n    offsetY\n  } = offsetPoint;\n  const isManualOffsetX = offsetX === 0 || offsetX;\n  const isManualOffsetY = offsetY === 0 || offsetY;\n  return {\n    x: isManualOffsetX ? offsetX : calculateXOffset(),\n    y: isManualOffsetY ? offsetY : calculateYOffset()\n  };\n}", "map": {"version": 3, "names": ["isFirefox", "<PERSON><PERSON><PERSON><PERSON>", "MonotonicInterpolant", "ELEMENT_NODE", "getNodeClientOffset", "node", "el", "nodeType", "parentElement", "top", "left", "getBoundingClientRect", "x", "y", "getEventClientOffset", "e", "clientX", "clientY", "isImageNode", "ref", "nodeName", "document", "documentElement", "contains", "getDragPreviewSize", "isImage", "dragPreview", "sourceWidth", "sourceHeight", "dragPreviewWidth", "width", "dragPreviewHeight", "height", "window", "devicePixelRatio", "getDragPreviewOffset", "sourceNode", "clientOffset", "anchorPoint", "offsetPoint", "dragPreviewNode", "dragPreviewNodeOffsetFromClient", "offsetFromDragPreview", "offsetWidth", "offsetHeight", "anchorX", "anchorY", "calculateYOffset", "interpolantY", "interpolate", "calculateXOffset", "interpolantX", "offsetX", "offsetY", "isManualOffsetX", "isManualOffsetY"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\react-dnd-html5-backend\\src\\OffsetUtils.ts"], "sourcesContent": ["import type { XYCoord } from 'dnd-core'\n\nimport { isFirefox, isSafari } from './BrowserDetector.js'\nimport { MonotonicInterpolant } from './MonotonicInterpolant.js'\n\nconst ELEMENT_NODE = 1\n\nexport function getNodeClientOffset(node: Node): XYCoord | null {\n\tconst el = node.nodeType === ELEMENT_NODE ? node : node.parentElement\n\n\tif (!el) {\n\t\treturn null\n\t}\n\n\tconst { top, left } = (el as HTMLElement).getBoundingClientRect()\n\treturn { x: left, y: top }\n}\n\nexport function getEventClientOffset(e: MouseEvent): XYCoord {\n\treturn {\n\t\tx: e.clientX,\n\t\ty: e.clientY,\n\t}\n}\n\nfunction isImageNode(node: any) {\n\treturn (\n\t\tnode.nodeName === 'IMG' &&\n\t\t(isFirefox() || !document.documentElement?.contains(node))\n\t)\n}\n\nfunction getDragPreviewSize(\n\tisImage: boolean,\n\tdragPreview: any,\n\tsourceWidth: number,\n\tsourceHeight: number,\n) {\n\tlet dragPreviewWidth = isImage ? dragPreview.width : sourceWidth\n\tlet dragPreviewHeight = isImage ? dragPreview.height : sourceHeight\n\n\t// Work around @2x coordinate discrepancies in browsers\n\tif (isSafari() && isImage) {\n\t\tdragPreviewHeight /= window.devicePixelRatio\n\t\tdragPreviewWidth /= window.devicePixelRatio\n\t}\n\treturn { dragPreviewWidth, dragPreviewHeight }\n}\n\nexport function getDragPreviewOffset(\n\tsourceNode: HTMLElement,\n\tdragPreview: HTMLElement,\n\tclientOffset: XYCoord,\n\tanchorPoint: { anchorX: number; anchorY: number },\n\toffsetPoint: { offsetX: number; offsetY: number },\n): XYCoord {\n\t// The browsers will use the image intrinsic size under different conditions.\n\t// Firefox only cares if it's an image, but WebKit also wants it to be detached.\n\tconst isImage = isImageNode(dragPreview)\n\tconst dragPreviewNode = isImage ? sourceNode : dragPreview\n\tconst dragPreviewNodeOffsetFromClient = getNodeClientOffset(\n\t\tdragPreviewNode,\n\t) as XYCoord\n\tconst offsetFromDragPreview = {\n\t\tx: clientOffset.x - dragPreviewNodeOffsetFromClient.x,\n\t\ty: clientOffset.y - dragPreviewNodeOffsetFromClient.y,\n\t}\n\tconst { offsetWidth: sourceWidth, offsetHeight: sourceHeight } = sourceNode\n\tconst { anchorX, anchorY } = anchorPoint\n\tconst { dragPreviewWidth, dragPreviewHeight } = getDragPreviewSize(\n\t\tisImage,\n\t\tdragPreview,\n\t\tsourceWidth,\n\t\tsourceHeight,\n\t)\n\n\tconst calculateYOffset = () => {\n\t\tconst interpolantY = new MonotonicInterpolant(\n\t\t\t[0, 0.5, 1],\n\t\t\t[\n\t\t\t\t// Dock to the top\n\t\t\t\toffsetFromDragPreview.y,\n\t\t\t\t// Align at the center\n\t\t\t\t(offsetFromDragPreview.y / sourceHeight) * dragPreviewHeight,\n\t\t\t\t// Dock to the bottom\n\t\t\t\toffsetFromDragPreview.y + dragPreviewHeight - sourceHeight,\n\t\t\t],\n\t\t)\n\t\tlet y = interpolantY.interpolate(anchorY)\n\t\t// Work around Safari 8 positioning bug\n\t\tif (isSafari() && isImage) {\n\t\t\t// We'll have to wait for @3x to see if this is entirely correct\n\t\t\ty += (window.devicePixelRatio - 1) * dragPreviewHeight\n\t\t}\n\t\treturn y\n\t}\n\n\tconst calculateXOffset = () => {\n\t\t// Interpolate coordinates depending on anchor point\n\t\t// If you know a simpler way to do this, let me know\n\t\tconst interpolantX = new MonotonicInterpolant(\n\t\t\t[0, 0.5, 1],\n\t\t\t[\n\t\t\t\t// Dock to the left\n\t\t\t\toffsetFromDragPreview.x,\n\t\t\t\t// Align at the center\n\t\t\t\t(offsetFromDragPreview.x / sourceWidth) * dragPreviewWidth,\n\t\t\t\t// Dock to the right\n\t\t\t\toffsetFromDragPreview.x + dragPreviewWidth - sourceWidth,\n\t\t\t],\n\t\t)\n\t\treturn interpolantX.interpolate(anchorX)\n\t}\n\n\t// Force offsets if specified in the options.\n\tconst { offsetX, offsetY } = offsetPoint\n\tconst isManualOffsetX = offsetX === 0 || offsetX\n\tconst isManualOffsetY = offsetY === 0 || offsetY\n\treturn {\n\t\tx: isManualOffsetX ? offsetX : calculateXOffset(),\n\t\ty: isManualOffsetY ? offsetY : calculateYOffset(),\n\t}\n}\n"], "mappings": "AAEA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,sBAAsB;AAC1D,SAASC,oBAAoB,QAAQ,2BAA2B;AAEhE,MAAMC,YAAY,GAAG,CAAC;AAEtB,OAAO,SAASC,mBAAmBA,CAACC,IAAU,EAAkB;EAC/D,MAAMC,EAAE,GAAGD,IAAI,CAACE,QAAQ,KAAKJ,YAAY,GAAGE,IAAI,GAAGA,IAAI,CAACG,aAAa;EAErE,IAAI,CAACF,EAAE,EAAE;IACR,OAAO,IAAI;;EAGZ,MAAM;IAAEG,GAAG;IAAEC;EAAI,CAAE,GAAGJ,EAAG,CAAiBK,qBAAqB,EAAE;EACjE,OAAO;IAAEC,CAAC,EAAEF,IAAI;IAAEG,CAAC,EAAEJ;GAAK;;AAG3B,OAAO,SAASK,oBAAoBA,CAACC,CAAa,EAAW;EAC5D,OAAO;IACNH,CAAC,EAAEG,CAAC,CAACC,OAAO;IACZH,CAAC,EAAEE,CAAC,CAACE;GACL;;AAGF,SAASC,WAAWA,CAACb,IAAS,EAAE;MAGbc,GAAwB;EAF1C,OACCd,IAAI,CAACe,QAAQ,KAAK,KAAK,KACtBpB,SAAS,EAAE,IAAI,EAAC,CAAAmB,GAAwB,GAAxBE,QAAQ,CAACC,eAAe,cAAxBH,GAAwB,WAAU,GAAlC,MAAkC,GAAlCA,GAAwB,CAAEI,QAAQ,CAAClB,IAAI,CAAC,EAAC;;AAI5D,SAASmB,kBAAkBA,CAC1BC,OAAgB,EAChBC,WAAgB,EAChBC,WAAmB,EACnBC,YAAoB,EACnB;EACD,IAAIC,gBAAgB,GAAGJ,OAAO,GAAGC,WAAW,CAACI,KAAK,GAAGH,WAAW;EAChE,IAAII,iBAAiB,GAAGN,OAAO,GAAGC,WAAW,CAACM,MAAM,GAAGJ,YAAY;EAEnE;EACA,IAAI3B,QAAQ,EAAE,IAAIwB,OAAO,EAAE;IAC1BM,iBAAiB,IAAIE,MAAM,CAACC,gBAAgB;IAC5CL,gBAAgB,IAAII,MAAM,CAACC,gBAAgB;;EAE5C,OAAO;IAAEL,gBAAgB;IAAEE;GAAmB;;AAG/C,OAAO,SAASI,oBAAoBA,CACnCC,UAAuB,EACvBV,WAAwB,EACxBW,YAAqB,EACrBC,WAAiD,EACjDC,WAAiD,EACvC;EACV;EACA;EACA,MAAMd,OAAO,GAAGP,WAAW,CAACQ,WAAW,CAAC;EACxC,MAAMc,eAAe,GAAGf,OAAO,GAAGW,UAAU,GAAGV,WAAW;EAC1D,MAAMe,+BAA+B,GAAGrC,mBAAmB,CAC1DoC,eAAe,CACf;EACD,MAAME,qBAAqB,GAAG;IAC7B9B,CAAC,EAAEyB,YAAY,CAACzB,CAAC,GAAG6B,+BAA+B,CAAC7B,CAAC;IACrDC,CAAC,EAAEwB,YAAY,CAACxB,CAAC,GAAG4B,+BAA+B,CAAC5B;GACpD;EACD,MAAM;IAAE8B,WAAW,EAAEhB,WAAW;IAAEiB,YAAY,EAAEhB;EAAY,CAAE,GAAGQ,UAAU;EAC3E,MAAM;IAAES,OAAO;IAAEC;EAAO,CAAE,GAAGR,WAAW;EACxC,MAAM;IAAET,gBAAgB;IAAEE;EAAiB,CAAE,GAAGP,kBAAkB,CACjEC,OAAO,EACPC,WAAW,EACXC,WAAW,EACXC,YAAY,CACZ;EAED,MAAMmB,gBAAgB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,YAAY,GAAG,IAAI9C,oBAAoB,CAC5C,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EACX;IACC;IACAwC,qBAAqB,CAAC7B,CAAC;IACvB;IACC6B,qBAAqB,CAAC7B,CAAC,GAAGe,YAAY,GAAIG,iBAAiB;IAC5D;IACAW,qBAAqB,CAAC7B,CAAC,GAAGkB,iBAAiB,GAAGH,YAAY,CAC1D,CACD;IACD,IAAIf,CAAC,GAAGmC,YAAY,CAACC,WAAW,CAACH,OAAO,CAAC;IACzC;IACA,IAAI7C,QAAQ,EAAE,IAAIwB,OAAO,EAAE;MAC1B;MACAZ,CAAC,IAAI,CAACoB,MAAM,CAACC,gBAAgB,GAAG,CAAC,IAAIH,iBAAiB;;IAEvD,OAAOlB,CAAC;GACR;EAED,MAAMqC,gBAAgB,GAAGA,CAAA,KAAM;IAC9B;IACA;IACA,MAAMC,YAAY,GAAG,IAAIjD,oBAAoB,CAC5C,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EACX;IACC;IACAwC,qBAAqB,CAAC9B,CAAC;IACvB;IACC8B,qBAAqB,CAAC9B,CAAC,GAAGe,WAAW,GAAIE,gBAAgB;IAC1D;IACAa,qBAAqB,CAAC9B,CAAC,GAAGiB,gBAAgB,GAAGF,WAAW,CACxD,CACD;IACD,OAAOwB,YAAY,CAACF,WAAW,CAACJ,OAAO,CAAC;GACxC;EAED;EACA,MAAM;IAAEO,OAAO;IAAEC;EAAO,CAAE,GAAGd,WAAW;EACxC,MAAMe,eAAe,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO;EAChD,MAAMG,eAAe,GAAGF,OAAO,KAAK,CAAC,IAAIA,OAAO;EAChD,OAAO;IACNzC,CAAC,EAAE0C,eAAe,GAAGF,OAAO,GAAGF,gBAAgB,EAAE;IACjDrC,CAAC,EAAE0C,eAAe,GAAGF,OAAO,GAAGN,gBAAgB;GAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}