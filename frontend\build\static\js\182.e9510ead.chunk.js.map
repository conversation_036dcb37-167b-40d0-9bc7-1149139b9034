{"version": 3, "file": "static/js/182.e9510ead.chunk.js", "mappings": "0MAWA,MAqTA,EArT0BA,KACxB,MAAM,KAAEC,IAASC,EAAAA,EAAAA,MACVC,EAAgBC,IAAqBC,EAAAA,EAAAA,WAAS,IAC9CC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,KAC1CG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAgB,KACrDK,EAASC,IAAcN,EAAAA,EAAAA,WAAS,IAChCO,EAAOC,IAAYR,EAAAA,EAAAA,UAAS,KAC5BS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,KAChCW,EAAmBC,IAAwBZ,EAAAA,EAAAA,WAAS,IACpDa,EAAUC,IAAed,EAAAA,EAAAA,UAAS,IAClCe,EAAiBC,IAAsBhB,EAAAA,EAAAA,WAAS,IAChDiB,EAAeC,IAAoBlB,EAAAA,EAAAA,UAAS,KAC5CmB,EAAiBC,IAAsBpB,EAAAA,EAAAA,UAAS,KAGvDqB,EAAAA,EAAAA,YAAU,KACoBC,WAC1BvB,GAAkB,GAClBG,EAAgB,IAChB,IACE,MAAMqB,QAAiBC,EAAAA,GAAeC,aACtCrB,EAAkBmB,EAASG,MAAQ,GACrC,CAAE,MAAOC,GACPC,QAAQrB,MAAM,kCAAmCoB,GACjDzB,EAAgByB,EAAIE,SAAW,sDAC/BzB,EAAkB,GACpB,CAAC,QACCL,GAAkB,EACpB,GAGF+B,EAAqB,GACpB,IAGH,MAoCMC,EAAcC,IAClB,IAEE,OADa,IAAIC,KAAKD,GACVE,mBAAmB,QAAS,CACtCC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WAEZ,CAAE,MAAOC,GAEP,OADAZ,QAAQrB,MAAM,yBAA0ByB,EAAYQ,GAC7C,cACT,GAIIC,EAAkBC,GACf,IAAIC,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,QACTC,OAAOL,GAGZ,OACEM,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yCAAwCD,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAID,UAAU,wBAAuBD,SAAC,+BACtCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,sBAAqBD,SAAC,iEAKrCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAASA,IAAM1C,GAAqB,GAAMsC,SAAC,sBAKpDjC,IACCmC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CACJC,KAAK,QACL3B,QAASZ,EACTwC,QAASA,IAAMvC,EAAiB,IAChCiC,UAAU,SAIbhC,IACCiC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CACJC,KAAK,UACL3B,QAASV,EACTgC,UAAU,UAIdH,EAAAA,EAAAA,MAAA,OAAKG,UAAU,6CAA4CD,SAAA,EACzDE,EAAAA,EAAAA,KAACM,EAAAA,EAAI,CAAAR,UACHF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBD,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,uCAAsCD,UAC9C,OAAJtD,QAAI,IAAJA,OAAI,EAAJA,EAAM+D,iBAAkB,KAE3BP,EAAAA,EAAAA,KAAA,OAAKD,UAAU,sBAAqBD,SAAC,0BAIzCE,EAAAA,EAAAA,KAACM,EAAAA,EAAI,CAAAR,UACHF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBD,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,yCAAwCD,SAAC,SAGxDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,sBAAqBD,SAAC,mBAIzCE,EAAAA,EAAAA,KAACM,EAAAA,EAAI,CAAAR,UACHF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBD,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,wCAAuCD,SAAC,WAGvDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,sBAAqBD,SAAC,iCAK3CE,EAAAA,EAAAA,KAACM,EAAAA,EAAI,CAACE,MAAM,2BAA0BV,UACpCF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,MAAKD,SAAA,EAClBE,EAAAA,EAAAA,KAAA,MAAID,UAAU,2BAA0BD,SAAC,mCACzCF,EAAAA,EAAAA,MAAA,MAAIG,UAAU,iDAAgDD,SAAA,EAC5DE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,6CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,2CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,+CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,0CACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,mCAGNE,EAAAA,EAAAA,KAAA,MAAID,UAAU,2BAA0BD,SAAC,aACzCE,EAAAA,EAAAA,KAAA,KAAGD,UAAU,2BAA0BD,SAAC,qJAKxCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CAACC,QAASA,IAAM1C,GAAqB,GAAMsC,SAAC,yBAMvDE,EAAAA,EAAAA,KAACM,EAAAA,EAAI,CAACE,MAAM,kBAAkBT,UAAU,OAAMD,UAC5CF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBD,SAAA,CAC7BpD,IACCsD,EAAAA,EAAAA,KAAA,OAAKD,UAAU,mBAAkBD,SAAC,uBAGnCjD,IAAiBH,IAChBsD,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,KAAK,QAAQ3B,QAAS5B,EAAckD,UAAU,SAGrDrD,IAAmBG,IACnB+C,EAAAA,EAAAA,MAAA,SAAOG,UAAU,eAAcD,SAAA,EAC7BE,EAAAA,EAAAA,KAAA,SAAAF,UACEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,UACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,iBACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,WACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,YACJE,EAAAA,EAAAA,KAAA,MAAAF,SAAI,iBAGRE,EAAAA,EAAAA,KAAA,SAAAF,SAC6B,IAA1B/C,EAAe0D,QACdT,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAAA,MAAIU,QAAS,EAAGX,UAAU,mBAAkBD,SAAC,mCAK/C/C,EAAe4D,KAAKC,IAAW,IAAAC,EAAA,OAC7BjB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEE,EAAAA,EAAAA,KAAA,MAAAF,SAAKnB,EAAWiC,EAAYE,SAC5Bd,EAAAA,EAAAA,KAAA,MAAAF,SACwB,aAArBc,EAAYR,KAAsB,gBACb,eAArBQ,EAAYR,KAAwB,aACpCQ,EAAYR,MAAQ,aAEvBJ,EAAAA,EAAAA,KAAA,MAAAF,SAA8B,QAA9Be,EAAKD,EAAYG,qBAAa,IAAAF,EAAAA,EAAI,SAClCb,EAAAA,EAAAA,KAAA,MAAAF,SAAKT,EAAeuB,EAAYtB,WAChCU,EAAAA,EAAAA,KAAA,MAAAF,UACEE,EAAAA,EAAAA,KAAA,QAAMD,UAAW,yCACQ,cAAvBa,EAAYI,OAAyB,eACd,YAAvBJ,EAAYI,OAAuB,gBACZ,WAAvBJ,EAAYI,OAAsB,aAClC,eACClB,SACAc,EAAYI,OAASJ,EAAYI,OAAOC,OAAO,GAAGC,cAAgBN,EAAYI,OAAOG,MAAM,GAAK,gBAhB9FP,EAAYQ,GAmBhB,cASlB7D,IACCyC,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iFAAgFD,UAC7FF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iDAAgDD,SAAA,EAC7DE,EAAAA,EAAAA,KAAA,MAAID,UAAU,6BAA4BD,SAAC,mBAE1CjC,IACCmC,EAAAA,EAAAA,KAACG,EAAAA,EAAK,CAACC,KAAK,QAAQ3B,QAASZ,EAAewC,QAASA,IAAMvC,EAAiB,IAAKiC,UAAU,UAG7FH,EAAAA,EAAAA,MAAA,OAAKG,UAAU,OAAMD,SAAA,EACnBE,EAAAA,EAAAA,KAAA,SAAOD,UAAU,iCAAgCD,SAAC,gDAIlDF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,oBAAmBD,SAAA,EAChCE,EAAAA,EAAAA,KAAA,UACED,UAAU,yDACVG,QAASA,IAAMxC,EAAY2D,KAAKC,IAAI,EAAG7D,EAAW,IAClD8D,SAAU5D,GAAmBF,GAAY,EAAEqC,SAC5C,OAGDE,EAAAA,EAAAA,KAAA,OAAKD,UAAU,iDAAgDD,SAC5DrC,KAEHuC,EAAAA,EAAAA,KAAA,UACED,UAAU,yDACVG,QAASA,IAAMxC,EAAYD,EAAW,GACtC8D,SAAU5D,EAAgBmC,SAC3B,aAMLF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,OAAMD,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKG,UAAU,qDAAoDD,SAAA,EACjEE,EAAAA,EAAAA,KAAA,QAAAF,SAAM,qBACNE,EAAAA,EAAAA,KAAA,QAAAF,SAAOT,EAAe,UAExBO,EAAAA,EAAAA,MAAA,OAAKG,UAAU,qDAAoDD,SAAA,EACjEE,EAAAA,EAAAA,KAAA,QAAAF,SAAM,eACNE,EAAAA,EAAAA,KAAA,QAAAF,SAAOrC,QAETmC,EAAAA,EAAAA,MAAA,OAAKG,UAAU,0CAAyCD,SAAA,EACtDE,EAAAA,EAAAA,KAAA,QAAAF,SAAM,YACNE,EAAAA,EAAAA,KAAA,QAAAF,SAAOT,EAA0B,GAAX5B,YAI1BmC,EAAAA,EAAAA,MAAA,OAAKG,UAAU,6BAA4BD,SAAA,EACzCE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLuB,QAAQ,YACRtB,QAASA,IAAM1C,GAAqB,GACpC+D,SAAU5D,EAAgBmC,SAC3B,YAGDE,EAAAA,EAAAA,KAACC,EAAAA,EAAM,CACLC,QArQchC,UAC1B,GAAIT,EAAW,EACbK,EAAiB,mCADnB,CAKAF,GAAmB,GACnBE,EAAiB,IACjBE,EAAmB,IAEnB,IAAK,IAADyD,EACF,MAEMC,GAA8B,QAAbD,SAFArD,EAAAA,GAAeuD,cAAclE,IAEpBa,YAAI,IAAAmD,OAAA,EAAbA,EAAeb,cAAe,CACnDQ,GAAI,OAAOvC,KAAK+C,QAChBxB,KAAM,WACNd,OAAmB,GAAX7B,EACRsD,cAAetD,EACfqD,MAAM,IAAIjC,MAAOgD,cACjBb,OAAQ,aAGVhE,EAAkB,CAAC0E,KAAmB3E,IACtCiB,EAAmB,0BAA0BP,SAA6B,IAAbA,EAAiB,IAAM,OACpFD,GAAqB,GAErBsE,YAAW,IAAM9D,EAAmB,KAAK,IAC3C,CAAE,MAAOO,GACPC,QAAQrB,MAAM,kBAAmBoB,GACjCT,EAAiBS,EAAIE,SAAW,+CAClC,CAAC,QACCb,GAAmB,EACrB,CA5BA,CA4BA,EAsOY2D,SAAU5D,EAAgBmC,SAEzBnC,EAAkB,gBAAkB,uBAM9C,C", "sources": ["pages/Billing.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\nimport { useAuth } from '../contexts/AuthContext';\nimport { billingService } from '../services';\n\nconst Billing: React.FC = () => {\n  const { user } = useAuth();\n  const [loadingHistory, setLoadingHistory] = useState(true);\n  const [historyError, setHistoryError] = useState('');\n  const [billingHistory, setBillingHistory] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showPurchaseModal, setShowPurchaseModal] = useState(false);\n  const [quantity, setQuantity] = useState(1);\n  const [loadingPurchase, setLoadingPurchase] = useState(false);\n  const [purchaseError, setPurchaseError] = useState('');\n  const [purchaseSuccess, setPurchaseSuccess] = useState('');\n  \n  // Fetch billing history\n  useEffect(() => {\n    const fetchBillingHistory = async () => {\n      setLoadingHistory(true);\n      setHistoryError('');\n      try {\n        const response = await billingService.getHistory();\n        setBillingHistory(response.data || []);\n      } catch (err: any) {\n        console.error('Error fetching billing history:', err);\n        setHistoryError(err.message || 'Failed to fetch billing history. Please try again.');\n        setBillingHistory([]);\n      } finally {\n        setLoadingHistory(false);\n      }\n    };\n\n    fetchBillingHistory();\n  }, []);\n  \n  // Handle flow purchase\n  const handlePurchaseFlows = async () => {\n    if (quantity < 1) {\n      setPurchaseError('Quantity must be at least 1');\n      return;\n    }\n    \n    setLoadingPurchase(true);\n    setPurchaseError('');\n    setPurchaseSuccess('');\n    \n    try {\n      const response = await billingService.purchaseFlows(quantity);\n\n      const newTransaction = response.data?.transaction || {\n        id: `new-${Date.now()}`,\n        type: 'purchase',\n        amount: quantity * 10.00,\n        flowsQuantity: quantity,\n        date: new Date().toISOString(),\n        status: 'completed'\n      };\n      \n      setBillingHistory([newTransaction, ...billingHistory]);\n      setPurchaseSuccess(`Successfully purchased ${quantity} flow${quantity !== 1 ? 's' : ''}.`);\n      setShowPurchaseModal(false);\n      \n      setTimeout(() => setPurchaseSuccess(''), 5000);\n    } catch (err: any) {\n      console.error('Purchase error:', err);\n      setPurchaseError(err.message || 'Failed to process payment. Please try again.');\n    } finally {\n      setLoadingPurchase(false);\n    }\n  };\n  \n  // Format date for display\n  const formatDate = (dateString: string) => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } catch (e) {\n      console.error(\"Error formatting date:\", dateString, e);\n      return \"Invalid Date\";\n    }\n  };\n  \n  // Format currency\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  \n  return (\n    <>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-xl font-semibold\">Billing & Flow Management</h2>\n          <p className=\"text-text-secondary\">\n            Manage your email flow purchases and billing information\n          </p>\n        </div>\n        \n        <Button onClick={() => setShowPurchaseModal(true)}>\n          Purchase Flows\n        </Button>\n      </div>\n      \n      {purchaseError && (\n        <Alert\n          type=\"error\"\n          message={purchaseError}\n          onClose={() => setPurchaseError('')}\n          className=\"mb-6\"\n        />\n      )}\n      \n      {purchaseSuccess && (\n        <Alert\n          type=\"success\"\n          message={purchaseSuccess}\n          className=\"mb-6\"\n        />\n      )}\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n        <Card>\n          <div className=\"text-center p-4\">\n            <div className=\"text-4xl font-bold text-primary mb-2\">\n              {user?.flowsPurchased || 0}\n            </div>\n            <div className=\"text-text-secondary\">Available Flows</div>\n          </div>\n        </Card>\n        \n        <Card>\n          <div className=\"text-center p-4\">\n            <div className=\"text-4xl font-bold text-green-500 mb-2\">\n              $10\n            </div>\n            <div className=\"text-text-secondary\">Per Flow</div>\n          </div>\n        </Card>\n        \n        <Card>\n          <div className=\"text-center p-4\">\n            <div className=\"text-4xl font-bold text-blue-500 mb-2\">\n              1,000\n            </div>\n            <div className=\"text-text-secondary\">Recipients Per Flow</div>\n          </div>\n        </Card>\n      </div>\n      \n      <Card title=\"Flow Pricing Information\">\n        <div className=\"p-4\">\n          <h3 className=\"text-lg font-medium mb-2\">What's included in each flow?</h3>\n          <ul className=\"list-disc list-inside text-text-secondary mb-4\">\n            <li>Send up to 10 emails in a campaign flow</li>\n            <li>Reach up to 1,000 recipients per flow</li>\n            <li>Full access to email templates and editor</li>\n            <li>Comprehensive analytics and tracking</li>\n            <li>Automated email scheduling</li>\n          </ul>\n          \n          <h3 className=\"text-lg font-medium mb-2\">Pricing</h3>\n          <p className=\"text-text-secondary mb-4\">\n            Each flow costs $10 and allows you to send up to 10 emails to 1,000 recipients each.\n            Flows never expire, so you can use them whenever you need.\n          </p>\n          \n          <Button onClick={() => setShowPurchaseModal(true)}>\n            Purchase Flows\n          </Button>\n        </div>\n      </Card>\n      \n      <Card title=\"Billing History\" className=\"mt-6\">\n        <div className=\"table-container\">\n          {loadingHistory && (\n            <div className=\"text-center py-4\">Loading history...</div>\n          )}\n\n          {historyError && !loadingHistory && (\n            <Alert type=\"error\" message={historyError} className=\"m-4\"/>\n          )}\n\n          {!loadingHistory && !historyError && (\n            <table className=\"table w-full\">\n              <thead>\n                <tr>\n                  <th>Date</th>\n                  <th>Transaction</th>\n                  <th>Flows</th>\n                  <th>Amount</th>\n                  <th>Status</th>\n                </tr>\n              </thead>\n              <tbody>\n                {billingHistory.length === 0 ? (\n                  <tr>\n                    <td colSpan={5} className=\"text-center py-4\">\n                      No billing history available\n                    </td>\n                  </tr>\n                ) : (\n                  billingHistory.map((transaction) => (\n                    <tr key={transaction.id}>\n                      <td>{formatDate(transaction.date)}</td>\n                      <td>\n                        {transaction.type === 'purchase' ? 'Flow Purchase' :\n                         transaction.type === 'free_trial' ? 'Free Trial' :\n                         transaction.type || 'Unknown'}\n                      </td>\n                      <td>{transaction.flowsQuantity ?? 'N/A'}</td>\n                      <td>{formatCurrency(transaction.amount)}</td>\n                      <td>\n                        <span className={`px-2 py-1 text-xs rounded capitalize ${\n                          transaction.status === 'completed' ? 'bg-green-800' :\n                          transaction.status === 'pending' ? 'bg-yellow-800' :\n                          transaction.status === 'failed' ? 'bg-red-800' :\n                          'bg-gray-700'\n                        }`}>\n                          {transaction.status ? transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1) : 'Unknown'}\n                        </span>\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </Card>\n      \n      {showPurchaseModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\">\n            <h2 className=\"text-xl font-semibold mb-4\">Purchase Flows</h2>\n            \n            {purchaseError && (\n              <Alert type=\"error\" message={purchaseError} onClose={() => setPurchaseError('')} className=\"mb-4\"/>\n            )}\n\n            <div className=\"mb-6\">\n              <label className=\"block text-text-secondary mb-2\">\n                How many flows would you like to purchase?\n              </label>\n              \n              <div className=\"flex items-center\">\n                <button\n                  className=\"px-3 py-1 bg-gray-700 rounded-l-md disabled:opacity-50\"\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                  disabled={loadingPurchase || quantity <= 1}\n                >\n                  -\n                </button>\n                <div className=\"px-4 py-1 bg-gray-800 text-center min-w-[40px]\">\n                  {quantity}\n                </div>\n                <button\n                  className=\"px-3 py-1 bg-gray-700 rounded-r-md disabled:opacity-50\"\n                  onClick={() => setQuantity(quantity + 1)}\n                  disabled={loadingPurchase}\n                >\n                  +\n                </button>\n              </div>\n            </div>\n            \n            <div className=\"mb-6\">\n              <div className=\"flex justify-between py-2 border-b border-gray-700\">\n                <span>Price per flow:</span>\n                <span>{formatCurrency(10.00)}</span>\n              </div>\n              <div className=\"flex justify-between py-2 border-b border-gray-700\">\n                <span>Quantity:</span>\n                <span>{quantity}</span>\n              </div>\n              <div className=\"flex justify-between py-2 font-semibold\">\n                <span>Total:</span>\n                <span>{formatCurrency(quantity * 10.00)}</span>\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-2\">\n              <Button\n                variant=\"secondary\"\n                onClick={() => setShowPurchaseModal(false)}\n                disabled={loadingPurchase}\n              >\n                Cancel\n              </Button>\n              <Button\n                onClick={handlePurchaseFlows}\n                disabled={loadingPurchase}\n              >\n                {loadingPurchase ? 'Processing...' : 'Purchase'}\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Billing;\n"], "names": ["Billing", "user", "useAuth", "loadingHistory", "setLoadingHistory", "useState", "historyError", "setHistoryError", "billingHistory", "setBillingHistory", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showPurchaseModal", "setShowPurchaseModal", "quantity", "setQuantity", "loadingPurchase", "setLoadingPurchase", "purchaseError", "setPurchaseError", "purchaseSuccess", "setPurchaseSuccess", "useEffect", "async", "response", "billingService", "getHistory", "data", "err", "console", "message", "fetchBillingHistory", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "e", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "_jsxs", "_Fragment", "children", "className", "_jsx", "<PERSON><PERSON>", "onClick", "<PERSON><PERSON>", "type", "onClose", "Card", "flowsPurchased", "title", "length", "colSpan", "map", "transaction", "_transaction$flowsQua", "date", "flowsQuantity", "status", "char<PERSON>t", "toUpperCase", "slice", "id", "Math", "max", "disabled", "variant", "_response$data", "newTransaction", "purchaseFlows", "now", "toISOString", "setTimeout"], "sourceRoot": ""}