import { Router } from 'express';

import * as billingController from '../controllers/billing.controller';
import { getBillingHistory } from '../controllers/billing.controller';
import { protect } from '../middleware/auth.middleware';

const router = Router();

// All routes require authentication
router.use(protect);

// Billing routes
router.get('/info', billingController.getBillingInfo);
router.get('/history', getBillingHistory);
router.post('/purchase', billingController.purchaseFlows);

// Payment method routes
router.get('/payment-methods', billingController.getPaymentMethods);
router.post('/payment-methods', billingController.addPaymentMethod);
router.delete('/payment-methods/:paymentMethodId', billingController.removePaymentMethod);
router.put('/payment-methods/:paymentMethodId/default', billingController.setDefaultPaymentMethod);

export default router;
