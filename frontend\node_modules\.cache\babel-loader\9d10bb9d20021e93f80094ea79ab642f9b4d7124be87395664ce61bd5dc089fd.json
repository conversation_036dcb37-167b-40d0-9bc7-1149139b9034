{"ast": null, "code": "import { useId as n } from \"react\";\nimport { DefaultMap as f } from '../utils/default-map.js';\nimport { createStore as u } from '../utils/store.js';\nimport { useIsoMorphicEffect as c } from './use-iso-morphic-effect.js';\nimport { useStore as l } from './use-store.js';\nlet p = new f(() => u(() => [], {\n  ADD(r) {\n    return this.includes(r) ? this : [...this, r];\n  },\n  REMOVE(r) {\n    let e = this.indexOf(r);\n    if (e === -1) return this;\n    let t = this.slice();\n    return t.splice(e, 1), t;\n  }\n}));\nfunction x(r, e) {\n  let t = p.get(e),\n    i = n(),\n    h = l(t);\n  if (c(() => {\n    if (r) return t.dispatch(\"ADD\", i), () => t.dispatch(\"REMOVE\", i);\n  }, [t, r]), !r) return !1;\n  let s = h.indexOf(i),\n    o = h.length;\n  return s === -1 && (s = o, o += 1), s === o - 1;\n}\nexport { x as useIsTopLayer };", "map": {"version": 3, "names": ["useId", "n", "DefaultMap", "f", "createStore", "u", "useIsoMorphicEffect", "c", "useStore", "l", "p", "ADD", "r", "includes", "REMOVE", "e", "indexOf", "t", "slice", "splice", "x", "get", "i", "h", "dispatch", "s", "o", "length", "useIsTopLayer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/node_modules/@headlessui/react/dist/hooks/use-is-top-layer.js"], "sourcesContent": ["import{useId as n}from\"react\";import{DefaultMap as f}from'../utils/default-map.js';import{createStore as u}from'../utils/store.js';import{useIsoMorphicEffect as c}from'./use-iso-morphic-effect.js';import{useStore as l}from'./use-store.js';let p=new f(()=>u(()=>[],{ADD(r){return this.includes(r)?this:[...this,r]},REMOVE(r){let e=this.indexOf(r);if(e===-1)return this;let t=this.slice();return t.splice(e,1),t}}));function x(r,e){let t=p.get(e),i=n(),h=l(t);if(c(()=>{if(r)return t.dispatch(\"ADD\",i),()=>t.dispatch(\"REMOVE\",i)},[t,r]),!r)return!1;let s=h.indexOf(i),o=h.length;return s===-1&&(s=o,o+=1),s===o-1}export{x as useIsTopLayer};\n"], "mappings": "AAAA,SAAOA,KAAK,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,UAAU,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,IAAIC,CAAC,GAAC,IAAIP,CAAC,CAAC,MAAIE,CAAC,CAAC,MAAI,EAAE,EAAC;EAACM,GAAGA,CAACC,CAAC,EAAC;IAAC,OAAO,IAAI,CAACC,QAAQ,CAACD,CAAC,CAAC,GAAC,IAAI,GAAC,CAAC,GAAG,IAAI,EAACA,CAAC,CAAC;EAAA,CAAC;EAACE,MAAMA,CAACF,CAAC,EAAC;IAAC,IAAIG,CAAC,GAAC,IAAI,CAACC,OAAO,CAACJ,CAAC,CAAC;IAAC,IAAGG,CAAC,KAAG,CAAC,CAAC,EAAC,OAAO,IAAI;IAAC,IAAIE,CAAC,GAAC,IAAI,CAACC,KAAK,CAAC,CAAC;IAAC,OAAOD,CAAC,CAACE,MAAM,CAACJ,CAAC,EAAC,CAAC,CAAC,EAACE,CAAC;EAAA;AAAC,CAAC,CAAC,CAAC;AAAC,SAASG,CAACA,CAACR,CAAC,EAACG,CAAC,EAAC;EAAC,IAAIE,CAAC,GAACP,CAAC,CAACW,GAAG,CAACN,CAAC,CAAC;IAACO,CAAC,GAACrB,CAAC,CAAC,CAAC;IAACsB,CAAC,GAACd,CAAC,CAACQ,CAAC,CAAC;EAAC,IAAGV,CAAC,CAAC,MAAI;IAAC,IAAGK,CAAC,EAAC,OAAOK,CAAC,CAACO,QAAQ,CAAC,KAAK,EAACF,CAAC,CAAC,EAAC,MAAIL,CAAC,CAACO,QAAQ,CAAC,QAAQ,EAACF,CAAC,CAAC;EAAA,CAAC,EAAC,CAACL,CAAC,EAACL,CAAC,CAAC,CAAC,EAAC,CAACA,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,IAAIa,CAAC,GAACF,CAAC,CAACP,OAAO,CAACM,CAAC,CAAC;IAACI,CAAC,GAACH,CAAC,CAACI,MAAM;EAAC,OAAOF,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAACC,CAAC,EAACA,CAAC,IAAE,CAAC,CAAC,EAACD,CAAC,KAAGC,CAAC,GAAC,CAAC;AAAA;AAAC,SAAON,CAAC,IAAIQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}