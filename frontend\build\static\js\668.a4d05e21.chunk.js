"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[668],{3668:(e,a,s)=>{s.r(a),s.d(a,{default:()=>o});var t=s(5043),l=s(8417),n=s(1411),d=s(4741),i=s(9774),r=s(9579),c=s(579);const o=()=>{const[e,a]=(0,t.useState)([]),[s,o]=(0,t.useState)(!1),[m,x]=(0,t.useState)(!1),[h,u]=(0,t.useState)(!1),[g,p]=(0,t.useState)(!1),[j,v]=(0,t.useState)(null),[b,y]=(0,t.useState)(null),[C,f]=(0,t.useState)(""),[N,w]=(0,t.useState)(null),[k,A]=(0,t.useState)({name:"",email:"",tags:[]}),[F,E]=(0,t.useState)(!1),[S,T]=(0,t.useState)(null),[U,_]=(0,t.useState)(null),[D,L]=(0,t.useState)(null);(0,t.useEffect)((()=>{O()}),[]);const O=async()=>{E(!0),T(null);try{const e=await r.hd.getContacts();e.success?a(e.data):T(e.message||"Failed to fetch contacts")}catch(t){var e,s;console.error("Error fetching contacts:",t),T((null===(e=t.response)||void 0===e||null===(s=e.data)||void 0===s?void 0:s.message)||t.message||"Failed to fetch contacts")}finally{E(!1)}},V=e.filter((e=>{const a=e.name.toLowerCase().includes(C.toLowerCase()),s=e.email.toLowerCase().includes(C.toLowerCase()),t=!N||e.tags.includes(N);return(a||s)&&t})),z=Array.from(new Set(e.flatMap((e=>e.tags)))),B=()=>{x(!1),u(!1),p(!1),y(null),v(null),A({name:"",email:"",tags:[]}),_(null)};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,c.jsx)("div",{className:"flex-1 mr-4",children:(0,c.jsx)(d.A,{id:"search",name:"search",placeholder:"Search contacts...",value:C,onChange:e=>f(e.target.value)})}),(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsx)(l.A,{onClick:()=>o(!0),children:"Upload CSV"}),(0,c.jsx)(l.A,{onClick:()=>x(!0),children:"Add Contact"})]})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,c.jsx)("div",{className:"lg:col-span-1",children:(0,c.jsx)(n.A,{title:"Tags",children:(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("button",{className:"block w-full text-left px-3 py-2 rounded "+(null===N?"bg-primary text-white":"hover:bg-gray-700"),onClick:()=>w(null),children:["All Contacts (",e.length,")"]}),z.map((a=>(0,c.jsxs)("button",{className:"block w-full text-left px-3 py-2 rounded "+(N===a?"bg-primary text-white":"hover:bg-gray-700"),onClick:()=>w(a),children:[a," (",e.filter((e=>e.tags.includes(a))).length,")"]},a)))]})})}),(0,c.jsx)("div",{className:"lg:col-span-3",children:(0,c.jsxs)(n.A,{children:[F&&!m&&!h&&!g&&(0,c.jsx)("p",{children:"Loading contacts..."})," ",S&&!m&&!h&&!g&&(0,c.jsxs)("p",{className:"text-red-500",children:["Error: ",S]})," ",!F&&0===V.length&&!S&&(0,c.jsx)("p",{children:"No contacts found."}),!F&&V.length>0&&(0,c.jsx)("div",{className:"table-container",children:(0,c.jsxs)("table",{className:"table w-full",children:[(0,c.jsx)("thead",{children:(0,c.jsxs)("tr",{children:[(0,c.jsx)("th",{children:"Name"}),(0,c.jsx)("th",{children:"Email"}),(0,c.jsx)("th",{children:"Tags"}),(0,c.jsx)("th",{children:"Actions"})]})}),(0,c.jsx)("tbody",{children:V.map((e=>(0,c.jsxs)("tr",{children:[" ",(0,c.jsx)("td",{children:e.name}),(0,c.jsx)("td",{children:e.email}),(0,c.jsx)("td",{children:(0,c.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e=>(0,c.jsx)("span",{className:"px-2 py-1 text-xs rounded bg-gray-700 text-white",children:e},e)))})}),(0,c.jsx)("td",{children:(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsx)(l.A,{variant:"secondary",size:"sm",onClick:()=>(e=>{y({...e}),_(null),u(!0)})(e),disabled:F,children:"Edit"}),(0,c.jsx)(l.A,{variant:"danger",size:"sm",onClick:()=>(e=>{v(e),_(null),p(!0)})(e),disabled:F,children:"Delete"})]})})]},e._id)))})]})})]})})]}),(0,c.jsxs)(i.a,{isOpen:m,onClose:B,title:"Add New Contact",children:[U&&(0,c.jsx)("div",{className:"mb-4 p-3 bg-red-900 text-red-100 border border-red-700 rounded",children:U}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"addContactName",className:"block text-text-secondary mb-2",children:"Name"}),(0,c.jsx)(d.A,{id:"addContactName",name:"addContactName",value:k.name,onChange:e=>A({...k,name:e.target.value}),placeholder:"Enter name",disabled:F})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"addContactEmail",className:"block text-text-secondary mb-2",children:"Email"}),(0,c.jsx)(d.A,{id:"addContactEmail",name:"addContactEmail",value:k.email,onChange:e=>A({...k,email:e.target.value}),placeholder:"Enter email",type:"email",disabled:F})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"addContactTags",className:"block text-text-secondary mb-2",children:"Tags (comma separated)"}),(0,c.jsx)(d.A,{id:"addContactTags",name:"addContactTags",value:k.tags.join(", "),onChange:e=>A({...k,tags:e.target.value.split(",").map((e=>e.trim())).filter(Boolean)}),placeholder:"Enter tags (e.g., customer, active)",disabled:F})]})]}),(0,c.jsxs)("div",{className:"flex justify-end space-x-2 mt-6",children:[(0,c.jsx)(l.A,{variant:"secondary",onClick:B,disabled:F,children:"Cancel"}),(0,c.jsx)(l.A,{onClick:async()=>{E(!0),_(null);try{if(!k.name||!k.email)return _("Name and email are required"),void E(!1);const s=await r.hd.createContact(k);s.success?(a([...e,s.data]),B()):_(s.message||"Failed to create contact")}catch(l){var s,t;console.error("Error creating contact:",l),_((null===(s=l.response)||void 0===s||null===(t=s.data)||void 0===t?void 0:t.message)||l.message||"Failed to create contact")}finally{E(!1)}},disabled:F,children:F?"Adding...":"Add Contact"})]})]}),(0,c.jsxs)(i.a,{isOpen:h&&!!b,onClose:B,title:"Edit Contact",children:[U&&(0,c.jsx)("div",{className:"mb-4 p-3 bg-red-900 text-red-100 border border-red-700 rounded",children:U}),b&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"editContactName",className:"block text-text-secondary mb-2",children:"Name"}),(0,c.jsx)(d.A,{id:"editContactName",name:"editContactName",value:b.name,onChange:e=>y({...b,name:e.target.value}),placeholder:"Enter name",disabled:F})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"editContactEmail",className:"block text-text-secondary mb-2",children:"Email"}),(0,c.jsx)(d.A,{id:"editContactEmail",name:"editContactEmail",value:b.email,onChange:e=>y({...b,email:e.target.value}),placeholder:"Enter email",type:"email",disabled:F})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{htmlFor:"editContactTags",className:"block text-text-secondary mb-2",children:"Tags (comma separated)"}),(0,c.jsx)(d.A,{id:"editContactTags",name:"editContactTags",value:b.tags.join(", "),onChange:e=>y({...b,tags:e.target.value.split(",").map((e=>e.trim())).filter(Boolean)}),placeholder:"Enter tags (e.g., customer, active)",disabled:F})]})]}),(0,c.jsxs)("div",{className:"flex justify-end space-x-2 mt-6",children:[(0,c.jsx)(l.A,{variant:"secondary",onClick:B,disabled:F,children:"Cancel"}),(0,c.jsx)(l.A,{onClick:async()=>{if(b){E(!0),_(null);try{const s={name:b.name,email:b.email,tags:b.tags},t=await r.hd.updateContact(b._id,s);t.success?(a(e.map((e=>e._id===b._id?t.data:e))),B()):_(t.message||"Failed to update contact")}catch(l){var s,t;console.error("Error updating contact:",l),_((null===(s=l.response)||void 0===s||null===(t=s.data)||void 0===t?void 0:t.message)||l.message||"Failed to update contact")}finally{E(!1)}}},disabled:F,children:F?"Updating...":"Update Contact"})]})]})]}),(0,c.jsxs)(i.a,{isOpen:g&&!!j,onClose:B,title:"Confirm Deletion",children:[U&&(0,c.jsx)("div",{className:"mb-4 p-3 bg-red-900 text-red-100 border border-red-700 rounded",children:U}),j&&(0,c.jsxs)("p",{className:"text-text-primary mb-6",children:['Are you sure you want to delete the contact "',j.name,'" (',j.email,")? This action cannot be undone."]}),(0,c.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,c.jsx)(l.A,{variant:"secondary",onClick:B,disabled:F,children:"Cancel"}),(0,c.jsx)(l.A,{variant:"danger",onClick:async()=>{if(j){E(!0),_(null);try{const s=await r.hd.deleteContact(j._id);s.success?(a(e.filter((e=>e._id!==j._id))),B()):_(s.message||"Failed to delete contact")}catch(l){var s,t;console.error("Error deleting contact:",l),_((null===(s=l.response)||void 0===s||null===(t=s.data)||void 0===t?void 0:t.message)||l.message||"Failed to delete contact")}finally{E(!1)}}},disabled:F,children:F?"Deleting...":"Confirm Delete"})]})]}),(0,c.jsxs)(i.a,{isOpen:s,onClose:()=>{o(!1),L(null),_(null)},title:"Upload Contacts",children:[U&&(0,c.jsx)("div",{className:"mb-4 p-3 bg-red-900 text-red-100 border border-red-700 rounded",children:U}),(0,c.jsx)("p",{className:"text-text-secondary mb-4",children:"Upload a CSV file with your contacts. The file should include columns for name and email."}),(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsx)("label",{className:"block text-text-secondary mb-2",children:"CSV File"}),(0,c.jsxs)("div",{className:"relative border-2 border-dashed border-gray-600 rounded-lg p-6 text-center",children:[(0,c.jsx)("input",{type:"file",id:"csv-upload",accept:".csv",onChange:e=>{var a;const s=null===(a=e.target.files)||void 0===a?void 0:a[0];s&&L(s)},className:"absolute inset-0 w-full h-full opacity-0 cursor-pointer"}),(0,c.jsx)("label",{htmlFor:"csv-upload",className:"cursor-pointer",children:D?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("p",{className:"text-text-primary mb-2",children:["Selected file: ",D.name]}),(0,c.jsx)("span",{className:"inline-block py-1 px-3 text-sm rounded font-medium transition-colors duration-200 ease-in-out bg-secondary-bg border border-gray-600 hover:bg-gray-800 text-white focus:ring-gray-500",children:"Change File"})]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("p",{className:"text-text-secondary mb-2",children:"Drag and drop your CSV file here, or"}),(0,c.jsx)("span",{className:"inline-block py-1 px-3 text-sm rounded font-medium transition-colors duration-200 ease-in-out bg-secondary-bg border border-gray-600 hover:bg-gray-800 text-white focus:ring-gray-500",children:"Browse Files"})]})})]})]}),(0,c.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,c.jsx)(l.A,{variant:"secondary",onClick:()=>{o(!1),L(null),_(null)},disabled:F,children:"Cancel"}),(0,c.jsx)(l.A,{onClick:async()=>{if(D){E(!0),_(null);try{const e=await r.hd.importContacts(D,"csv");e.success?(await O(),o(!1),L(null)):_(e.message||"Failed to upload contacts")}catch(s){var e,a;console.error("Error uploading contacts:",s),_((null===(e=s.response)||void 0===e||null===(a=e.data)||void 0===a?void 0:a.message)||s.message||"Failed to upload contacts")}finally{E(!1)}}else _("Please select a file to upload")},disabled:F||!D,children:F?"Uploading...":"Upload"})]})]})]})}},9774:(e,a,s)=>{s.d(a,{a:()=>r});var t=s(5043),l=s(6018),n=s(6443),d=s(8417),i=s(579);const r=e=>{let{isOpen:a,onClose:s,title:r,children:c,onConfirm:o,confirmText:m="Confirm",confirmVariant:x="primary",cancelText:h="Cancel"}=e;return(0,i.jsx)(l.e,{appear:!0,show:a,as:t.Fragment,children:(0,i.jsxs)(n.lG,{as:"div",className:"relative z-10",onClose:s,children:[(0,i.jsx)(l.e.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50"})}),(0,i.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,i.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,i.jsx)(l.e.Child,{as:t.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,i.jsxs)(n.lG.Panel,{className:"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all",children:[(0,i.jsx)(n.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-white mb-4",children:r}),(0,i.jsx)("div",{className:"mt-2 text-sm text-gray-300",children:c}),(0,i.jsxs)("div",{className:"mt-6 flex justify-end space-x-3",children:[(0,i.jsx)(d.A,{variant:"secondary",onClick:s,children:h}),o&&(0,i.jsx)(d.A,{variant:x,onClick:o,children:m})]})]})})})})]})})}}}]);
//# sourceMappingURL=668.a4d05e21.chunk.js.map