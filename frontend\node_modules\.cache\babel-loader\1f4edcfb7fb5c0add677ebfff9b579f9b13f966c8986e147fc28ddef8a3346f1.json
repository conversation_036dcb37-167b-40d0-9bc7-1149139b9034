{"ast": null, "code": "import React,{useEffect,useState}from'react';import Alert from'components/Alert';import Button from'components/Button';import Card from'components/Card';import{useNavigate,useParams}from'react-router-dom';import{campaignAPI}from'services/api';// Import the Campaign interface (assuming it might be moved to a types file eventually)\n// For now, define it locally or import from CampaignList if needed\n// NOTE: Duplicating interface definition here for clarity in this example.\n// Ideally, this would be in a shared types file (e.g., frontend/src/types/campaign.ts)\nimport{jsxs as _jsxs,jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const CampaignSummary=()=>{var _campaign$status,_campaign$status2,_campaign$recipientCo,_campaign$sentCount,_campaign$errorCount,_campaign$openRate$to,_campaign$openRate,_campaign$clickRate$t,_campaign$clickRate,_campaign$recipientCo2,_campaign$recipientCo3,_campaign$recipientCo4,_campaign$recipientCo5;console.log('[CampaignSummary] Component Rendering...');// Log component render\nconst{id}=useParams();console.log(`[CampaignSummary] Extracted ID from params: ${id}`);// Log extracted ID\nconst navigate=useNavigate();const[loading,setLoading]=useState(true);// Start loading initially\nconst[error,setError]=useState('');const[success,setSuccess]=useState('');// Use the Campaign interface for state\nconst[campaign,setCampaign]=useState(null);// REMOVED recipients and recipientsCount state\n// const [recipients, setRecipients] = useState<any[]>([]);\n// const [recipientsCount, setRecipientsCount] = useState(0);\nuseEffect(()=>{console.log('[CampaignSummary] useEffect triggered. ID:',id);// Log useEffect trigger\nconst fetchCampaignData=async()=>{if(!id){console.log('[CampaignSummary] fetchCampaignData: ID is missing, skipping fetch.');setError('Campaign ID is missing');setLoading(false);return;}try{var _response$data;setLoading(true);setError('');// Fetch ONLY campaign details (which now include the count)\nconsole.log(`[CampaignSummary] fetchCampaignData: Attempting to fetch campaign with ID: ${id}`);// Log before API call\nconst response=await campaignAPI.getCampaign(id);console.log('[CampaignSummary] fetchCampaignData: API response received:',response);// Log API response\n// API likely returns { success: true, data: { campaign: ... } }\nif((_response$data=response.data)!==null&&_response$data!==void 0&&_response$data.campaign){setCampaign(response.data.campaign);}else{throw new Error('Campaign data not found in API response');}// REMOVED call to getCampaignRecipients\n}catch(err){var _err$response,_err$response$data;console.error(\"[CampaignSummary] Fetch Campaign Error:\",err);// Log fetch error\nsetError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Failed to fetch campaign details');setCampaign(null);// Clear campaign on error\n}finally{setLoading(false);}};fetchCampaignData();},[id]);// Format date utility (can be moved to a utils file)\nconst formatDate=dateString=>{if(!dateString)return'-';try{const date=new Date(dateString);// Check if date is valid after parsing\nif(isNaN(date.getTime())){return'Invalid Date';}return date.toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric',hour:'2-digit',minute:'2-digit'});}catch(e){console.error(\"Date formatting error:\",e);return'Invalid Date';}};// Get status badge class (can be moved to utils)\nconst getStatusBadgeClass=status=>{switch(status){case'draft':return'bg-gray-700';case'scheduled':return'bg-blue-800';case'sending':return'bg-yellow-800';case'completed':return'bg-green-800';case'sent':return'bg-green-700';// Slightly different green for single sent?\ncase'failed':return'bg-red-800';case'cancelled':return'bg-pink-800';default:return'bg-gray-500';}};const handleSendNow=async()=>{if(!id||(campaign===null||campaign===void 0?void 0:campaign.status)!=='draft')return;// Only allow sending drafts\ntry{setLoading(true);setError('');setSuccess('');await campaignAPI.sendCampaign(id);setSuccess('Campaign send initiated! Status will update automatically.');// Optionally update local status optimistically\nsetCampaign(prev=>prev?{...prev,status:'sending',startedAt:new Date().toISOString()}:null);}catch(err){var _err$response2,_err$response2$data;setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||'Failed to send campaign');}finally{setLoading(false);}};// Navigate to schedule page (or implement modal)\nconst handleSchedule=()=>{if(id){navigate(`/campaigns/${id}/schedule`);}};// Function to handle cancellation\nconst handleCancelSchedule=async()=>{if(!id||(campaign===null||campaign===void 0?void 0:campaign.status)!=='scheduled')return;if(!window.confirm('Are you sure you want to cancel the schedule for this campaign?'))return;try{var _response$data2;setLoading(true);setError('');setSuccess('');// Make the API call\nconst response=await campaignAPI.cancelScheduledCampaign(id);// Update local state with the campaign returned from the API\nif((_response$data2=response.data)!==null&&_response$data2!==void 0&&_response$data2.campaign){setCampaign(response.data.campaign);setSuccess('Campaign schedule cancelled successfully.');}else{// Fallback if API doesn't return updated campaign\nsetCampaign(prev=>prev?{...prev,status:'draft',scheduledFor:undefined}:null);setSuccess('Campaign schedule cancelled (local update).');}}catch(err){var _err$response3,_err$response3$data;console.error(\"Cancel Schedule Error:\",err);setError(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.message)||'Failed to cancel schedule.');}finally{setLoading(false);}};const formatSchedule=scheduleData=>{if(!scheduleData||!scheduleData.emailIntervals||scheduleData.emailIntervals.length===0){return'No follow-up schedule set';}return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-1\",children:scheduleData.emailIntervals.map((interval,index)=>/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm\",children:[\"Email \",index+2,\" sends\",' ',/*#__PURE__*/_jsxs(\"span\",{className:\"font-semibold\",children:[interval.delay,\" \",interval.unit]}),' ',\"after Email \",index+1,\".\"]},index))});};return/*#__PURE__*/_jsxs(Card,{children:[error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(''),className:\"mb-4\"}),success&&/*#__PURE__*/_jsx(Alert,{type:\"success\",message:success,className:\"mb-4\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"Campaign Summary\"}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>navigate('/campaigns'),children:\"Back to List\"})]}),loading?/*#__PURE__*/_jsx(\"p\",{className:\"text-center py-8 text-gray-500\",children:\"Loading campaign details...\"}):!campaign?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error||'Campaign not found or failed to load.'}):/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 border border-gray-700 rounded-lg p-4 md:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-4\",children:\"Campaign Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Name\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:campaign.name})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Subject\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:campaign.subject})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Status\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-medium\",children:/*#__PURE__*/_jsx(\"span\",{className:`px-2.5 py-0.5 text-xs rounded-full ${getStatusBadgeClass(campaign.status)}`,children:((_campaign$status=campaign.status)===null||_campaign$status===void 0?void 0:_campaign$status.charAt(0).toUpperCase())+((_campaign$status2=campaign.status)===null||_campaign$status2===void 0?void 0:_campaign$status2.slice(1))})})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"From\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-white font-medium\",children:[campaign.fromName,\" <\",campaign.fromEmail,\">\"]})]}),campaign.replyTo&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Reply To\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:campaign.replyTo})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Created\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:formatDate(campaign.createdAt)})]}),campaign.scheduledFor&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Scheduled For\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:formatDate(campaign.scheduledFor)})]}),campaign.startedAt&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Started Sending\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:formatDate(campaign.startedAt)})]}),campaign.completedAt&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Completed / Failed At\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:formatDate(campaign.completedAt)})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 border border-gray-700 rounded-lg p-4 md:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-4\",children:\"Email Sequence\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-400\",children:\"Number of Emails\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-medium text-white\",children:campaign.emailContents?campaign.emailContents.filter(e=>e.html).length:1})]}),campaign.emailContents&&campaign.emailContents.length>1&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-400 mb-1\",children:\"Follow-up Schedule\"}),formatSchedule(campaign.schedule)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-800 border border-gray-700 rounded-lg p-4 md:p-6\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-white mb-4\",children:\"Recipients & Performance\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Total Recipients\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:(_campaign$recipientCo=campaign.recipientCountActual)!==null&&_campaign$recipientCo!==void 0?_campaign$recipientCo:'N/A'}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",className:\"mt-2\",onClick:()=>navigate(`/campaigns/recipients/${id}`),children:\"Manage Recipients\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Sent\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:(_campaign$sentCount=campaign.sentCount)!==null&&_campaign$sentCount!==void 0?_campaign$sentCount:0})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Errors / Failed\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white font-medium\",children:(_campaign$errorCount=campaign.errorCount)!==null&&_campaign$errorCount!==void 0?_campaign$errorCount:0})]}),(campaign.status==='sending'||campaign.status==='completed'||campaign.status==='sent')&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Open Rate\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-white font-medium\",children:[(_campaign$openRate$to=(_campaign$openRate=campaign.openRate)===null||_campaign$openRate===void 0?void 0:_campaign$openRate.toFixed(2))!==null&&_campaign$openRate$to!==void 0?_campaign$openRate$to:0,\"%\"]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400\",children:\"Click Rate\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-white font-medium\",children:[(_campaign$clickRate$t=(_campaign$clickRate=campaign.clickRate)===null||_campaign$clickRate===void 0?void 0:_campaign$clickRate.toFixed(2))!==null&&_campaign$clickRate$t!==void 0?_campaign$clickRate$t:0,\"%\"]})]}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",className:\"mt-2\",onClick:()=>navigate(`/campaigns/analytics/${id}`),children:\"View Analytics\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row justify-end items-center space-y-2 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-700\",children:[(campaign.status==='draft'||campaign.status==='scheduled')&&/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>navigate(`/campaigns/edit/${id}`),disabled:loading,children:\"Edit Campaign\"}),campaign.status==='draft'&&/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:handleSchedule,disabled:loading||((_campaign$recipientCo2=campaign.recipientCountActual)!==null&&_campaign$recipientCo2!==void 0?_campaign$recipientCo2:0)===0,title:((_campaign$recipientCo3=campaign.recipientCountActual)!==null&&_campaign$recipientCo3!==void 0?_campaign$recipientCo3:0)===0?\"Add recipients first\":\"\",children:\"Schedule Send\"}),campaign.status==='draft'&&/*#__PURE__*/_jsx(Button,{onClick:handleSendNow,disabled:loading||((_campaign$recipientCo4=campaign.recipientCountActual)!==null&&_campaign$recipientCo4!==void 0?_campaign$recipientCo4:0)===0,title:((_campaign$recipientCo5=campaign.recipientCountActual)!==null&&_campaign$recipientCo5!==void 0?_campaign$recipientCo5:0)===0?\"Add recipients first\":\"\",children:\"Send Now\"}),campaign.status==='scheduled'&&/*#__PURE__*/_jsx(Button,{variant:\"secondary\"// Using secondary for now\n,onClick:handleCancelSchedule,disabled:loading,children:\"Cancel Schedule\"})]})]})]})]});};export default CampaignSummary;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "useNavigate", "useParams", "campaignAPI", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "CampaignSummary", "_campaign$status", "_campaign$status2", "_campaign$recipientCo", "_campaign$sentCount", "_campaign$errorCount", "_campaign$openRate$to", "_campaign$openRate", "_campaign$clickRate$t", "_campaign$clickRate", "_campaign$recipientCo2", "_campaign$recipientCo3", "_campaign$recipientCo4", "_campaign$recipientCo5", "console", "log", "id", "navigate", "loading", "setLoading", "error", "setError", "success", "setSuccess", "campaign", "setCampaign", "fetchCampaignData", "_response$data", "response", "getCampaign", "data", "Error", "err", "_err$response", "_err$response$data", "message", "formatDate", "dateString", "date", "Date", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "hour", "minute", "e", "getStatusBadgeClass", "status", "handleSendNow", "sendCampaign", "prev", "startedAt", "toISOString", "_err$response2", "_err$response2$data", "handleSchedule", "handleCancelSchedule", "window", "confirm", "_response$data2", "cancelScheduledCampaign", "scheduledFor", "undefined", "_err$response3", "_err$response3$data", "formatSchedule", "scheduleData", "emailIntervals", "length", "className", "children", "map", "interval", "index", "delay", "unit", "type", "onClose", "variant", "onClick", "name", "subject", "char<PERSON>t", "toUpperCase", "slice", "fromName", "fromEmail", "replyTo", "createdAt", "completedAt", "emailContents", "filter", "html", "schedule", "recipientCountActual", "size", "sentCount", "errorCount", "openRate", "toFixed", "clickRate", "disabled", "title"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/campaigns/CampaignSummary.tsx"], "sourcesContent": ["import React, {\r\n  useEffect,\r\n  useState,\r\n} from 'react';\r\n\r\nimport Alert from 'components/Alert';\r\nimport Button from 'components/Button';\r\nimport Card from 'components/Card';\r\nimport {\r\n  useNavigate,\r\n  useParams,\r\n} from 'react-router-dom';\r\nimport { campaignAPI } from 'services/api';\r\n\r\n// Import the Campaign interface (assuming it might be moved to a types file eventually)\r\n// For now, define it locally or import from CampaignList if needed\r\n// NOTE: Duplicating interface definition here for clarity in this example.\r\n// Ideally, this would be in a shared types file (e.g., frontend/src/types/campaign.ts)\r\ninterface Campaign {\r\n  _id: string;\r\n  name: string;\r\n  subject: string;\r\n  status: string;\r\n  recipientCountActual?: number;\r\n  sentCount?: number;\r\n  errorCount?: number;\r\n  openRate?: number;\r\n  clickRate?: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  scheduledFor?: string;\r\n  startedAt?: string;\r\n  completedAt?: string;\r\n  fromName?: string;\r\n  fromEmail?: string;\r\n  replyTo?: string;\r\n  htmlContent?: string;\r\n  emailContents?: { html: string; css?: string }[];\r\n  schedule?: {\r\n    unit: 'minutes' | 'hours' | 'days';\r\n    emailIntervals: { delay: number; unit: 'minutes' | 'hours' | 'days' }[];\r\n  };\r\n}\r\n\r\nconst CampaignSummary: React.FC = () => {\r\n  console.log('[CampaignSummary] Component Rendering...'); // Log component render\r\n  const { id } = useParams<{ id: string }>();\r\n  console.log(`[CampaignSummary] Extracted ID from params: ${id}`); // Log extracted ID\r\n  const navigate = useNavigate();\r\n  const [loading, setLoading] = useState(true); // Start loading initially\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  // Use the Campaign interface for state\r\n  const [campaign, setCampaign] = useState<Campaign | null>(null);\r\n  // REMOVED recipients and recipientsCount state\r\n  // const [recipients, setRecipients] = useState<any[]>([]);\r\n  // const [recipientsCount, setRecipientsCount] = useState(0);\r\n\r\n  useEffect(() => {\r\n    console.log('[CampaignSummary] useEffect triggered. ID:', id); // Log useEffect trigger\r\n    const fetchCampaignData = async () => {\r\n      if (!id) {\r\n        console.log('[CampaignSummary] fetchCampaignData: ID is missing, skipping fetch.');\r\n        setError('Campaign ID is missing');\r\n        setLoading(false);\r\n        return;\r\n      }\r\n      try {\r\n        setLoading(true);\r\n        setError('');\r\n        // Fetch ONLY campaign details (which now include the count)\r\n        console.log(`[CampaignSummary] fetchCampaignData: Attempting to fetch campaign with ID: ${id}`); // Log before API call\r\n        const response = await campaignAPI.getCampaign(id);\r\n        console.log('[CampaignSummary] fetchCampaignData: API response received:', response); // Log API response\r\n        // API likely returns { success: true, data: { campaign: ... } }\r\n        if (response.data?.campaign) {\r\n            setCampaign(response.data.campaign);\r\n        } else {\r\n            throw new Error('Campaign data not found in API response');\r\n        }\r\n        // REMOVED call to getCampaignRecipients\r\n      } catch (err: any) {\r\n        console.error(\"[CampaignSummary] Fetch Campaign Error:\", err); // Log fetch error\r\n        setError(err.response?.data?.message || 'Failed to fetch campaign details');\r\n        setCampaign(null); // Clear campaign on error\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCampaignData();\r\n  }, [id]);\r\n\r\n  // Format date utility (can be moved to a utils file)\r\n  const formatDate = (dateString?: string) => {\r\n    if (!dateString) return '-';\r\n    try {\r\n      const date = new Date(dateString);\r\n      // Check if date is valid after parsing\r\n      if (isNaN(date.getTime())) {\r\n          return 'Invalid Date';\r\n      }\r\n      return date.toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'short',\r\n        day: 'numeric',\r\n        hour: '2-digit',\r\n        minute: '2-digit'\r\n      });\r\n    } catch (e) {\r\n        console.error(\"Date formatting error:\", e);\r\n        return 'Invalid Date';\r\n    }\r\n  };\r\n\r\n  // Get status badge class (can be moved to utils)\r\n  const getStatusBadgeClass = (status?: string) => {\r\n    switch (status) {\r\n      case 'draft': return 'bg-gray-700';\r\n      case 'scheduled': return 'bg-blue-800';\r\n      case 'sending': return 'bg-yellow-800';\r\n      case 'completed': return 'bg-green-800';\r\n      case 'sent': return 'bg-green-700'; // Slightly different green for single sent?\r\n      case 'failed': return 'bg-red-800';\r\n      case 'cancelled': return 'bg-pink-800';\r\n      default: return 'bg-gray-500';\r\n    }\r\n  };\r\n\r\n  const handleSendNow = async () => {\r\n    if (!id || campaign?.status !== 'draft') return; // Only allow sending drafts\r\n    try {\r\n      setLoading(true);\r\n      setError('');\r\n      setSuccess('');\r\n      await campaignAPI.sendCampaign(id);\r\n      setSuccess('Campaign send initiated! Status will update automatically.');\r\n      // Optionally update local status optimistically\r\n      setCampaign(prev => prev ? { ...prev, status: 'sending', startedAt: new Date().toISOString() } : null);\r\n    } catch (err: any) {\r\n      setError(err.response?.data?.message || 'Failed to send campaign');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Navigate to schedule page (or implement modal)\r\n  const handleSchedule = () => {\r\n    if (id) {\r\n      navigate(`/campaigns/${id}/schedule`);\r\n    }\r\n  };\r\n  \r\n  // Function to handle cancellation\r\n  const handleCancelSchedule = async () => {\r\n      if (!id || campaign?.status !== 'scheduled') return;\r\n      if (!window.confirm('Are you sure you want to cancel the schedule for this campaign?')) return;\r\n      try {\r\n          setLoading(true);\r\n          setError('');\r\n          setSuccess('');\r\n          // Make the API call\r\n          const response = await campaignAPI.cancelScheduledCampaign(id);\r\n          // Update local state with the campaign returned from the API\r\n          if (response.data?.campaign) {\r\n              setCampaign(response.data.campaign); \r\n              setSuccess('Campaign schedule cancelled successfully.');\r\n          } else {\r\n              // Fallback if API doesn't return updated campaign\r\n              setCampaign(prev => prev ? { ...prev, status: 'draft', scheduledFor: undefined } : null);\r\n              setSuccess('Campaign schedule cancelled (local update).');\r\n          }\r\n      } catch(err: any) {\r\n          console.error(\"Cancel Schedule Error:\", err);\r\n          setError(err.response?.data?.message || 'Failed to cancel schedule.');\r\n      } finally {\r\n          setLoading(false);\r\n      }\r\n  };\r\n\r\n\r\n  const formatSchedule = (scheduleData?: Campaign['schedule']) => {\r\n    if (!scheduleData || !scheduleData.emailIntervals || scheduleData.emailIntervals.length === 0) {\r\n      return 'No follow-up schedule set';\r\n    }\r\n    return (\r\n      <div className=\"space-y-1\">\r\n        {scheduleData.emailIntervals.map((interval, index) => (\r\n          <p key={index} className=\"text-sm\">\r\n            Email {index + 2} sends{' '}\r\n            <span className=\"font-semibold\">{interval.delay} {interval.unit}</span>{' '}\r\n            after Email {index + 1}.\r\n          </p>\r\n        ))}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      {error && (\r\n        <Alert\r\n          type=\"error\"\r\n          message={error}\r\n          onClose={() => setError('')}\r\n          className=\"mb-4\"\r\n        />\r\n      )}\r\n\r\n      {success && (\r\n        <Alert\r\n          type=\"success\"\r\n          message={success}\r\n          className=\"mb-4\"\r\n        />\r\n      )}\r\n\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex justify-between items-center mb-6\">\r\n             <h2 className=\"text-xl font-semibold\">Campaign Summary</h2>\r\n             <Button variant=\"secondary\" onClick={() => navigate('/campaigns')}>Back to List</Button>\r\n        </div>\r\n        \r\n        {loading ? (\r\n          <p className=\"text-center py-8 text-gray-500\">Loading campaign details...</p>\r\n        ) : !campaign ? (\r\n           <Alert type=\"error\" message={error || 'Campaign not found or failed to load.'} />\r\n        ) : (\r\n          <div className=\"space-y-6\">\r\n            {/* --- Campaign Details --- */}\r\n            <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-4 md:p-6\">\r\n              <h3 className=\"text-lg font-semibold text-white mb-4\">Campaign Details</h3>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\r\n                <div>\r\n                  <p className=\"text-gray-400\">Name</p>\r\n                  <p className=\"text-white font-medium\">{campaign.name}</p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-gray-400\">Subject</p>\r\n                  <p className=\"text-white font-medium\">{campaign.subject}</p>\r\n                </div>\r\n                 <div>\r\n                  <p className=\"text-gray-400\">Status</p>\r\n                  <p className=\"font-medium\">\r\n                     <span className={`px-2.5 py-0.5 text-xs rounded-full ${getStatusBadgeClass(campaign.status)}`}>\r\n                         {campaign.status?.charAt(0).toUpperCase() + campaign.status?.slice(1)}\r\n                     </span>\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-gray-400\">From</p>\r\n                  <p className=\"text-white font-medium\">{campaign.fromName} &lt;{campaign.fromEmail}&gt;</p>\r\n                </div>\r\n                {campaign.replyTo && (\r\n                  <div>\r\n                    <p className=\"text-gray-400\">Reply To</p>\r\n                    <p className=\"text-white font-medium\">{campaign.replyTo}</p>\r\n                  </div>\r\n                )}\r\n                 <div>\r\n                  <p className=\"text-gray-400\">Created</p>\r\n                  <p className=\"text-white font-medium\">{formatDate(campaign.createdAt)}</p>\r\n                </div>\r\n                 {campaign.scheduledFor && (\r\n                     <div>\r\n                         <p className=\"text-gray-400\">Scheduled For</p>\r\n                         <p className=\"text-white font-medium\">{formatDate(campaign.scheduledFor)}</p>\r\n                     </div>\r\n                 )}\r\n                 {campaign.startedAt && (\r\n                     <div>\r\n                         <p className=\"text-gray-400\">Started Sending</p>\r\n                         <p className=\"text-white font-medium\">{formatDate(campaign.startedAt)}</p>\r\n                     </div>\r\n                 )}\r\n                 {campaign.completedAt && (\r\n                     <div>\r\n                         <p className=\"text-gray-400\">Completed / Failed At</p>\r\n                         <p className=\"text-white font-medium\">{formatDate(campaign.completedAt)}</p>\r\n                     </div>\r\n                 )}\r\n              </div>\r\n            </div>\r\n            \r\n            {/* --- Email Sequence & Content --- */}\r\n             <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-4 md:p-6\">\r\n               <h3 className=\"text-lg font-semibold text-white mb-4\">Email Sequence</h3>\r\n               <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                   <div>\r\n                     <p className=\"text-sm text-gray-400\">Number of Emails</p>\r\n                     <p className=\"font-medium text-white\">\r\n                       {campaign.emailContents ? \r\n                         campaign.emailContents.filter((e: any) => e.html).length :\r\n                         1}\r\n                     </p>\r\n                   </div>\r\n                   {campaign.emailContents && campaign.emailContents.length > 1 && (\r\n                     <div>\r\n                       <p className=\"text-sm text-gray-400 mb-1\">Follow-up Schedule</p>\r\n                       {formatSchedule(campaign.schedule)}\r\n                     </div>\r\n                   )}\r\n               </div>\r\n                {/* Add preview button maybe? */}\r\n             </div>\r\n            \r\n            {/* --- Recipients & Performance --- */}\r\n            <div className=\"bg-gray-800 border border-gray-700 rounded-lg p-4 md:p-6\">\r\n              <h3 className=\"text-lg font-semibold text-white mb-4\">Recipients & Performance</h3>\r\n               <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\r\n                    <div>\r\n                      <p className=\"text-gray-400\">Total Recipients</p>\r\n                      <p className=\"text-white font-medium\">{campaign.recipientCountActual ?? 'N/A'}</p>\r\n                       <Button \r\n                          size=\"sm\"\r\n                          variant=\"secondary\"\r\n                          className=\"mt-2\"\r\n                          onClick={() => navigate(`/campaigns/recipients/${id}`)}\r\n                       >\r\n                         Manage Recipients\r\n                       </Button>\r\n                    </div>\r\n                    <div>\r\n                      <p className=\"text-gray-400\">Sent</p>\r\n                      <p className=\"text-white font-medium\">{campaign.sentCount ?? 0}</p>\r\n                    </div>\r\n                     <div>\r\n                      <p className=\"text-gray-400\">Errors / Failed</p>\r\n                      <p className=\"text-white font-medium\">{campaign.errorCount ?? 0}</p>\r\n                    </div>\r\n                     {/* Only show rates if sending/completed */}\r\n                    {(campaign.status === 'sending' || campaign.status === 'completed' || campaign.status === 'sent') && (\r\n                        <>\r\n                            <div>\r\n                                <p className=\"text-gray-400\">Open Rate</p>\r\n                                <p className=\"text-white font-medium\">{campaign.openRate?.toFixed(2) ?? 0}%</p>\r\n                            </div>\r\n                            <div>\r\n                                <p className=\"text-gray-400\">Click Rate</p>\r\n                                <p className=\"text-white font-medium\">{campaign.clickRate?.toFixed(2) ?? 0}%</p>\r\n                            </div>\r\n                             <Button\r\n                                size=\"sm\"\r\n                                variant=\"secondary\"\r\n                                className=\"mt-2\"\r\n                                onClick={() => navigate(`/campaigns/analytics/${id}`)}\r\n                             >\r\n                               View Analytics\r\n                             </Button>\r\n                        </>\r\n                    )}\r\n               </div>\r\n            </div>\r\n            \r\n            {/* --- Actions --- */}\r\n            <div className=\"flex flex-col sm:flex-row justify-end items-center space-y-2 sm:space-y-0 sm:space-x-3 pt-6 border-t border-gray-700\">\r\n               {/* Show Edit only if draft/scheduled */} \r\n               {(campaign.status === 'draft' || campaign.status === 'scheduled') && (\r\n                   <Button \r\n                     variant=\"secondary\" \r\n                     onClick={() => navigate(`/campaigns/edit/${id}`)}\r\n                     disabled={loading}\r\n                   >\r\n                     Edit Campaign\r\n                   </Button>\r\n               )}\r\n              {/* Show Schedule only if draft */} \r\n              {campaign.status === 'draft' && (\r\n                  <Button\r\n                    variant=\"secondary\"\r\n                    onClick={handleSchedule}\r\n                    disabled={loading || (campaign.recipientCountActual ?? 0) === 0}\r\n                    title={(campaign.recipientCountActual ?? 0) === 0 ? \"Add recipients first\" : \"\"}\r\n                  >\r\n                    Schedule Send\r\n                  </Button>\r\n              )}\r\n              {/* Show Send Now only if draft */} \r\n              {campaign.status === 'draft' && (\r\n                  <Button\r\n                    onClick={handleSendNow}\r\n                    disabled={loading || (campaign.recipientCountActual ?? 0) === 0}\r\n                    title={(campaign.recipientCountActual ?? 0) === 0 ? \"Add recipients first\" : \"\"}\r\n                  >\r\n                    Send Now\r\n                  </Button>\r\n              )}\r\n               {/* Show Cancel Schedule only if scheduled */} \r\n               {campaign.status === 'scheduled' && (\r\n                   <Button\r\n                     variant=\"secondary\" // Using secondary for now\r\n                     onClick={handleCancelSchedule} \r\n                     disabled={loading}\r\n                   >\r\n                     Cancel Schedule\r\n                   </Button>\r\n               )}\r\n               {/* Add Duplicate/Delete? */} \r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default CampaignSummary; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,IAAI,KAAM,iBAAiB,CAClC,OACEC,WAAW,CACXC,SAAS,KACJ,kBAAkB,CACzB,OAASC,WAAW,KAAQ,cAAc,CAE1C;AACA;AACA;AACA;AAAA,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBA2BA,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,KAAAC,gBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACtCC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CAAE;AACzD,KAAM,CAAEC,EAAG,CAAC,CAAGxB,SAAS,CAAiB,CAAC,CAC1CsB,OAAO,CAACC,GAAG,CAAC,+CAA+CC,EAAE,EAAE,CAAC,CAAE;AAClE,KAAM,CAAAC,QAAQ,CAAG1B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC2B,OAAO,CAAEC,UAAU,CAAC,CAAGhC,QAAQ,CAAC,IAAI,CAAC,CAAE;AAC9C,KAAM,CAACiC,KAAK,CAAEC,QAAQ,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACmC,OAAO,CAAEC,UAAU,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAC1C;AACA,KAAM,CAACqC,QAAQ,CAAEC,WAAW,CAAC,CAAGtC,QAAQ,CAAkB,IAAI,CAAC,CAC/D;AACA;AACA;AAEAD,SAAS,CAAC,IAAM,CACd4B,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAEC,EAAE,CAAC,CAAE;AAC/D,KAAM,CAAAU,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACV,EAAE,CAAE,CACPF,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC,CAClFM,QAAQ,CAAC,wBAAwB,CAAC,CAClCF,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACA,GAAI,KAAAQ,cAAA,CACFR,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZ;AACAP,OAAO,CAACC,GAAG,CAAC,8EAA8EC,EAAE,EAAE,CAAC,CAAE;AACjG,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAnC,WAAW,CAACoC,WAAW,CAACb,EAAE,CAAC,CAClDF,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAEa,QAAQ,CAAC,CAAE;AACtF;AACA,IAAAD,cAAA,CAAIC,QAAQ,CAACE,IAAI,UAAAH,cAAA,WAAbA,cAAA,CAAeH,QAAQ,CAAE,CACzBC,WAAW,CAACG,QAAQ,CAACE,IAAI,CAACN,QAAQ,CAAC,CACvC,CAAC,IAAM,CACH,KAAM,IAAI,CAAAO,KAAK,CAAC,yCAAyC,CAAC,CAC9D,CACA;AACF,CAAE,MAAOC,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBpB,OAAO,CAACM,KAAK,CAAC,yCAAyC,CAAEY,GAAG,CAAC,CAAE;AAC/DX,QAAQ,CAAC,EAAAY,aAAA,CAAAD,GAAG,CAACJ,QAAQ,UAAAK,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcH,IAAI,UAAAI,kBAAA,iBAAlBA,kBAAA,CAAoBC,OAAO,GAAI,kCAAkC,CAAC,CAC3EV,WAAW,CAAC,IAAI,CAAC,CAAE;AACrB,CAAC,OAAS,CACRN,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDO,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAACV,EAAE,CAAC,CAAC,CAER;AACA,KAAM,CAAAoB,UAAU,CAAIC,UAAmB,EAAK,CAC1C,GAAI,CAACA,UAAU,CAAE,MAAO,GAAG,CAC3B,GAAI,CACF,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC;AACA,GAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE,CACvB,MAAO,cAAc,CACzB,CACA,MAAO,CAAAH,IAAI,CAACI,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAE,MAAOC,CAAC,CAAE,CACRlC,OAAO,CAACM,KAAK,CAAC,wBAAwB,CAAE4B,CAAC,CAAC,CAC1C,MAAO,cAAc,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAIC,MAAe,EAAK,CAC/C,OAAQA,MAAM,EACZ,IAAK,OAAO,CAAE,MAAO,aAAa,CAClC,IAAK,WAAW,CAAE,MAAO,aAAa,CACtC,IAAK,SAAS,CAAE,MAAO,eAAe,CACtC,IAAK,WAAW,CAAE,MAAO,cAAc,CACvC,IAAK,MAAM,CAAE,MAAO,cAAc,CAAE;AACpC,IAAK,QAAQ,CAAE,MAAO,YAAY,CAClC,IAAK,WAAW,CAAE,MAAO,aAAa,CACtC,QAAS,MAAO,aAAa,CAC/B,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CAACnC,EAAE,EAAI,CAAAQ,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE0B,MAAM,IAAK,OAAO,CAAE,OAAQ;AACjD,GAAI,CACF/B,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CACd,KAAM,CAAA9B,WAAW,CAAC2D,YAAY,CAACpC,EAAE,CAAC,CAClCO,UAAU,CAAC,4DAA4D,CAAC,CACxE;AACAE,WAAW,CAAC4B,IAAI,EAAIA,IAAI,CAAG,CAAE,GAAGA,IAAI,CAAEH,MAAM,CAAE,SAAS,CAAEI,SAAS,CAAE,GAAI,CAAAf,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC,CAAE,CAAC,CAAG,IAAI,CAAC,CACxG,CAAE,MAAOvB,GAAQ,CAAE,KAAAwB,cAAA,CAAAC,mBAAA,CACjBpC,QAAQ,CAAC,EAAAmC,cAAA,CAAAxB,GAAG,CAACJ,QAAQ,UAAA4B,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc1B,IAAI,UAAA2B,mBAAA,iBAAlBA,mBAAA,CAAoBtB,OAAO,GAAI,yBAAyB,CAAC,CACpE,CAAC,OAAS,CACRhB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAI1C,EAAE,CAAE,CACNC,QAAQ,CAAC,cAAcD,EAAE,WAAW,CAAC,CACvC,CACF,CAAC,CAED;AACA,KAAM,CAAA2C,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CAAC3C,EAAE,EAAI,CAAAQ,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE0B,MAAM,IAAK,WAAW,CAAE,OAC7C,GAAI,CAACU,MAAM,CAACC,OAAO,CAAC,iEAAiE,CAAC,CAAE,OACxF,GAAI,KAAAC,eAAA,CACA3C,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CACd;AACA,KAAM,CAAAK,QAAQ,CAAG,KAAM,CAAAnC,WAAW,CAACsE,uBAAuB,CAAC/C,EAAE,CAAC,CAC9D;AACA,IAAA8C,eAAA,CAAIlC,QAAQ,CAACE,IAAI,UAAAgC,eAAA,WAAbA,eAAA,CAAetC,QAAQ,CAAE,CACzBC,WAAW,CAACG,QAAQ,CAACE,IAAI,CAACN,QAAQ,CAAC,CACnCD,UAAU,CAAC,2CAA2C,CAAC,CAC3D,CAAC,IAAM,CACH;AACAE,WAAW,CAAC4B,IAAI,EAAIA,IAAI,CAAG,CAAE,GAAGA,IAAI,CAAEH,MAAM,CAAE,OAAO,CAAEc,YAAY,CAAEC,SAAU,CAAC,CAAG,IAAI,CAAC,CACxF1C,UAAU,CAAC,6CAA6C,CAAC,CAC7D,CACJ,CAAE,MAAMS,GAAQ,CAAE,KAAAkC,cAAA,CAAAC,mBAAA,CACdrD,OAAO,CAACM,KAAK,CAAC,wBAAwB,CAAEY,GAAG,CAAC,CAC5CX,QAAQ,CAAC,EAAA6C,cAAA,CAAAlC,GAAG,CAACJ,QAAQ,UAAAsC,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcpC,IAAI,UAAAqC,mBAAA,iBAAlBA,mBAAA,CAAoBhC,OAAO,GAAI,4BAA4B,CAAC,CACzE,CAAC,OAAS,CACNhB,UAAU,CAAC,KAAK,CAAC,CACrB,CACJ,CAAC,CAGD,KAAM,CAAAiD,cAAc,CAAIC,YAAmC,EAAK,CAC9D,GAAI,CAACA,YAAY,EAAI,CAACA,YAAY,CAACC,cAAc,EAAID,YAAY,CAACC,cAAc,CAACC,MAAM,GAAK,CAAC,CAAE,CAC7F,MAAO,2BAA2B,CACpC,CACA,mBACE1E,IAAA,QAAK2E,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBJ,YAAY,CAACC,cAAc,CAACI,GAAG,CAAC,CAACC,QAAQ,CAAEC,KAAK,gBAC/CjF,KAAA,MAAe6E,SAAS,CAAC,SAAS,CAAAC,QAAA,EAAC,QAC3B,CAACG,KAAK,CAAG,CAAC,CAAC,QAAM,CAAC,GAAG,cAC3BjF,KAAA,SAAM6E,SAAS,CAAC,eAAe,CAAAC,QAAA,EAAEE,QAAQ,CAACE,KAAK,CAAC,GAAC,CAACF,QAAQ,CAACG,IAAI,EAAO,CAAC,CAAC,GAAG,CAAC,cAChE,CAACF,KAAK,CAAG,CAAC,CAAC,GACzB,GAJQA,KAIL,CACJ,CAAC,CACC,CAAC,CAEV,CAAC,CAED,mBACEjF,KAAA,CAACL,IAAI,EAAAmF,QAAA,EACFrD,KAAK,eACJvB,IAAA,CAACT,KAAK,EACJ2F,IAAI,CAAC,OAAO,CACZ5C,OAAO,CAAEf,KAAM,CACf4D,OAAO,CAAEA,CAAA,GAAM3D,QAAQ,CAAC,EAAE,CAAE,CAC5BmD,SAAS,CAAC,MAAM,CACjB,CACF,CAEAlD,OAAO,eACNzB,IAAA,CAACT,KAAK,EACJ2F,IAAI,CAAC,SAAS,CACd5C,OAAO,CAAEb,OAAQ,CACjBkD,SAAS,CAAC,MAAM,CACjB,CACF,cAED7E,KAAA,QAAK6E,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9E,KAAA,QAAK6E,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eAClD5E,IAAA,OAAI2E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC3D5E,IAAA,CAACR,MAAM,EAAC4F,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAMjE,QAAQ,CAAC,YAAY,CAAE,CAAAwD,QAAA,CAAC,cAAY,CAAQ,CAAC,EACxF,CAAC,CAELvD,OAAO,cACNrB,IAAA,MAAG2E,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,6BAA2B,CAAG,CAAC,CAC3E,CAACjD,QAAQ,cACV3B,IAAA,CAACT,KAAK,EAAC2F,IAAI,CAAC,OAAO,CAAC5C,OAAO,CAAEf,KAAK,EAAI,uCAAwC,CAAE,CAAC,cAElFzB,KAAA,QAAK6E,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB9E,KAAA,QAAK6E,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvE5E,IAAA,OAAI2E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC3E9E,KAAA,QAAK6E,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5D9E,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,MAAI,CAAG,CAAC,cACrC5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEjD,QAAQ,CAAC2D,IAAI,CAAI,CAAC,EACtD,CAAC,cACNxF,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,SAAO,CAAG,CAAC,cACxC5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEjD,QAAQ,CAAC4D,OAAO,CAAI,CAAC,EACzD,CAAC,cACLzF,KAAA,QAAA8E,QAAA,eACC5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,QAAM,CAAG,CAAC,cACvC5E,IAAA,MAAG2E,SAAS,CAAC,aAAa,CAAAC,QAAA,cACvB5E,IAAA,SAAM2E,SAAS,CAAE,sCAAsCvB,mBAAmB,CAACzB,QAAQ,CAAC0B,MAAM,CAAC,EAAG,CAAAuB,QAAA,CACzF,EAAAxE,gBAAA,CAAAuB,QAAQ,CAAC0B,MAAM,UAAAjD,gBAAA,iBAAfA,gBAAA,CAAiBoF,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,IAAApF,iBAAA,CAAGsB,QAAQ,CAAC0B,MAAM,UAAAhD,iBAAA,iBAAfA,iBAAA,CAAiBqF,KAAK,CAAC,CAAC,CAAC,EACnE,CAAC,CACP,CAAC,EACD,CAAC,cACN5F,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,MAAI,CAAG,CAAC,cACrC9E,KAAA,MAAG6E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAEjD,QAAQ,CAACgE,QAAQ,CAAC,IAAK,CAAChE,QAAQ,CAACiE,SAAS,CAAC,GAAI,EAAG,CAAC,EACvF,CAAC,CACLjE,QAAQ,CAACkE,OAAO,eACf/F,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,cACzC5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAEjD,QAAQ,CAACkE,OAAO,CAAI,CAAC,EACzD,CACN,cACA/F,KAAA,QAAA8E,QAAA,eACC5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,SAAO,CAAG,CAAC,cACxC5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAErC,UAAU,CAACZ,QAAQ,CAACmE,SAAS,CAAC,CAAI,CAAC,EACvE,CAAC,CACJnE,QAAQ,CAACwC,YAAY,eAClBrE,KAAA,QAAA8E,QAAA,eACI5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,eAAa,CAAG,CAAC,cAC9C5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAErC,UAAU,CAACZ,QAAQ,CAACwC,YAAY,CAAC,CAAI,CAAC,EAC5E,CACR,CACAxC,QAAQ,CAAC8B,SAAS,eACf3D,KAAA,QAAA8E,QAAA,eACI5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,cAChD5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAErC,UAAU,CAACZ,QAAQ,CAAC8B,SAAS,CAAC,CAAI,CAAC,EACzE,CACR,CACA9B,QAAQ,CAACoE,WAAW,eACjBjG,KAAA,QAAA8E,QAAA,eACI5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uBAAqB,CAAG,CAAC,cACtD5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAErC,UAAU,CAACZ,QAAQ,CAACoE,WAAW,CAAC,CAAI,CAAC,EAC3E,CACR,EACC,CAAC,EACH,CAAC,cAGLjG,KAAA,QAAK6E,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvE5E,IAAA,OAAI2E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACzE9E,KAAA,QAAK6E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAClD9E,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,cACzD5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAClCjD,QAAQ,CAACqE,aAAa,CACrBrE,QAAQ,CAACqE,aAAa,CAACC,MAAM,CAAE9C,CAAM,EAAKA,CAAC,CAAC+C,IAAI,CAAC,CAACxB,MAAM,CACxD,CAAC,CACF,CAAC,EACD,CAAC,CACL/C,QAAQ,CAACqE,aAAa,EAAIrE,QAAQ,CAACqE,aAAa,CAACtB,MAAM,CAAG,CAAC,eAC1D5E,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,oBAAkB,CAAG,CAAC,CAC/DL,cAAc,CAAC5C,QAAQ,CAACwE,QAAQ,CAAC,EAC/B,CACN,EACA,CAAC,EAEH,CAAC,cAGPrG,KAAA,QAAK6E,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvE5E,IAAA,OAAI2E,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAClF9E,KAAA,QAAK6E,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eACzD9E,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAG,CAAC,cACjD5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAAtE,qBAAA,CAAEqB,QAAQ,CAACyE,oBAAoB,UAAA9F,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAI,CAAC,cACjFN,IAAA,CAACR,MAAM,EACJ6G,IAAI,CAAC,IAAI,CACTjB,OAAO,CAAC,WAAW,CACnBT,SAAS,CAAC,MAAM,CAChBU,OAAO,CAAEA,CAAA,GAAMjE,QAAQ,CAAC,yBAAyBD,EAAE,EAAE,CAAE,CAAAyD,QAAA,CACzD,mBAED,CAAQ,CAAC,EACP,CAAC,cACN9E,KAAA,QAAA8E,QAAA,eACE5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,MAAI,CAAG,CAAC,cACrC5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAArE,mBAAA,CAAEoB,QAAQ,CAAC2E,SAAS,UAAA/F,mBAAA,UAAAA,mBAAA,CAAI,CAAC,CAAI,CAAC,EAChE,CAAC,cACLT,KAAA,QAAA8E,QAAA,eACC5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,cAChD5E,IAAA,MAAG2E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAApE,oBAAA,CAAEmB,QAAQ,CAAC4E,UAAU,UAAA/F,oBAAA,UAAAA,oBAAA,CAAI,CAAC,CAAI,CAAC,EACjE,CAAC,CAEL,CAACmB,QAAQ,CAAC0B,MAAM,GAAK,SAAS,EAAI1B,QAAQ,CAAC0B,MAAM,GAAK,WAAW,EAAI1B,QAAQ,CAAC0B,MAAM,GAAK,MAAM,gBAC5FvD,KAAA,CAAAI,SAAA,EAAA0E,QAAA,eACI9E,KAAA,QAAA8E,QAAA,eACI5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,WAAS,CAAG,CAAC,cAC1C9E,KAAA,MAAG6E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,GAAAnE,qBAAA,EAAAC,kBAAA,CAAEiB,QAAQ,CAAC6E,QAAQ,UAAA9F,kBAAA,iBAAjBA,kBAAA,CAAmB+F,OAAO,CAAC,CAAC,CAAC,UAAAhG,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,GAAC,EAAG,CAAC,EAC9E,CAAC,cACNX,KAAA,QAAA8E,QAAA,eACI5E,IAAA,MAAG2E,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,YAAU,CAAG,CAAC,cAC3C9E,KAAA,MAAG6E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,GAAAjE,qBAAA,EAAAC,mBAAA,CAAEe,QAAQ,CAAC+E,SAAS,UAAA9F,mBAAA,iBAAlBA,mBAAA,CAAoB6F,OAAO,CAAC,CAAC,CAAC,UAAA9F,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,GAAC,EAAG,CAAC,EAC/E,CAAC,cACLX,IAAA,CAACR,MAAM,EACJ6G,IAAI,CAAC,IAAI,CACTjB,OAAO,CAAC,WAAW,CACnBT,SAAS,CAAC,MAAM,CAChBU,OAAO,CAAEA,CAAA,GAAMjE,QAAQ,CAAC,wBAAwBD,EAAE,EAAE,CAAE,CAAAyD,QAAA,CACxD,gBAED,CAAQ,CAAC,EACZ,CACL,EACD,CAAC,EACJ,CAAC,cAGN9E,KAAA,QAAK6E,SAAS,CAAC,sHAAsH,CAAAC,QAAA,EAEjI,CAACjD,QAAQ,CAAC0B,MAAM,GAAK,OAAO,EAAI1B,QAAQ,CAAC0B,MAAM,GAAK,WAAW,gBAC5DrD,IAAA,CAACR,MAAM,EACL4F,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAEA,CAAA,GAAMjE,QAAQ,CAAC,mBAAmBD,EAAE,EAAE,CAAE,CACjDwF,QAAQ,CAAEtF,OAAQ,CAAAuD,QAAA,CACnB,eAED,CAAQ,CACX,CAEDjD,QAAQ,CAAC0B,MAAM,GAAK,OAAO,eACxBrD,IAAA,CAACR,MAAM,EACL4F,OAAO,CAAC,WAAW,CACnBC,OAAO,CAAExB,cAAe,CACxB8C,QAAQ,CAAEtF,OAAO,EAAI,EAAAR,sBAAA,CAACc,QAAQ,CAACyE,oBAAoB,UAAAvF,sBAAA,UAAAA,sBAAA,CAAI,CAAC,IAAM,CAAE,CAChE+F,KAAK,CAAE,EAAA9F,sBAAA,CAACa,QAAQ,CAACyE,oBAAoB,UAAAtF,sBAAA,UAAAA,sBAAA,CAAI,CAAC,IAAM,CAAC,CAAG,sBAAsB,CAAG,EAAG,CAAA8D,QAAA,CACjF,eAED,CAAQ,CACX,CAEAjD,QAAQ,CAAC0B,MAAM,GAAK,OAAO,eACxBrD,IAAA,CAACR,MAAM,EACL6F,OAAO,CAAE/B,aAAc,CACvBqD,QAAQ,CAAEtF,OAAO,EAAI,EAAAN,sBAAA,CAACY,QAAQ,CAACyE,oBAAoB,UAAArF,sBAAA,UAAAA,sBAAA,CAAI,CAAC,IAAM,CAAE,CAChE6F,KAAK,CAAE,EAAA5F,sBAAA,CAACW,QAAQ,CAACyE,oBAAoB,UAAApF,sBAAA,UAAAA,sBAAA,CAAI,CAAC,IAAM,CAAC,CAAG,sBAAsB,CAAG,EAAG,CAAA4D,QAAA,CACjF,UAED,CAAQ,CACX,CAECjD,QAAQ,CAAC0B,MAAM,GAAK,WAAW,eAC5BrD,IAAA,CAACR,MAAM,EACL4F,OAAO,CAAC,WAAY;AAAA,CACpBC,OAAO,CAAEvB,oBAAqB,CAC9B6C,QAAQ,CAAEtF,OAAQ,CAAAuD,QAAA,CACnB,iBAED,CAAQ,CACX,EAEC,CAAC,EACH,CACN,EACE,CAAC,EACF,CAAC,CAEX,CAAC,CAED,cAAe,CAAAzE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}