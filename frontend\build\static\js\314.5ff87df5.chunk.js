"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[314],{3314:(e,s,a)=>{a.r(s),a.d(s,{default:()=>c});var t=a(5043),n=a(9291),r=a(8417),i=a(1411),l=a(4741),o=a(579);const c=()=>{const[e,s]=(0,t.useState)(""),[a,c]=(0,t.useState)(""),[d,m]=(0,t.useState)(""),[u,h]=(0,t.useState)(""),[x,p]=(0,t.useState)(!1);return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,o.jsx)("div",{className:"lg:col-span-2",children:(0,o.jsx)(i.A,{title:"Frequently Asked Questions",children:(0,o.jsx)("div",{className:"space-y-6",children:[{question:"What is a flow?",answer:"A flow is a series of emails sent to your contacts. Each flow costs $10 and includes 10 emails that can be sent to up to 1000 recipients each."},{question:"How do I verify my domain?",answer:'Go to the Campaigns section and click "Register Domain". Enter your domain name and follow the instructions to add the required DNS records. Once verified, your domain status will update to "Active".'},{question:"How do I create an email campaign?",answer:"Go to the Campaigns section, create a new campaign, and use the drag-and-drop editor to design your emails. You can then schedule them to be sent to your contacts."},{question:"How do I import contacts?",answer:'Go to the Contacts section and click "Upload Contacts". You can upload a CSV file with your contacts\' information. Make sure it includes at least the email and name columns.'},{question:"How do I track the performance of my campaigns?",answer:"Go to the Analytics section to view detailed reports on your campaigns, including open rates, click rates, and more. You can also export this data as CSV."}].map(((e,s)=>(0,o.jsxs)("div",{className:"border-b border-gray-700 pb-4 last:border-b-0 last:pb-0",children:[(0,o.jsx)("h3",{className:"text-lg font-medium mb-2",children:e.question}),(0,o.jsx)("p",{className:"text-text-secondary",children:e.answer})]},s)))})})}),(0,o.jsxs)("div",{children:[(0,o.jsxs)(i.A,{title:"Contact Support",children:[d&&(0,o.jsx)(n.A,{type:"error",message:d,onClose:()=>m(""),className:"mb-4"}),u&&(0,o.jsx)(n.A,{type:"success",message:u,onClose:()=>h(""),className:"mb-4"}),(0,o.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),e&&a)try{p(!0),m(""),await new Promise((e=>setTimeout(e,1e3))),h("Your support request has been submitted. We will get back to you soon."),s(""),c("")}catch(i){var n,r;m((null===(n=i.response)||void 0===n||null===(r=n.data)||void 0===r?void 0:r.message)||"Failed to submit support request")}finally{p(!1)}else m("Please fill in all fields")},children:[(0,o.jsx)(l.A,{id:"subject",name:"subject",label:"Subject",value:e,onChange:e=>s(e.target.value),required:!0,className:"mb-4"}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsxs)("label",{htmlFor:"message",className:"form-label",children:["Message ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("textarea",{id:"message",name:"message",value:a,onChange:e=>c(e.target.value),required:!0,rows:6,className:"form-input w-full"})]}),(0,o.jsx)(r.A,{type:"submit",disabled:x,children:x?"Submitting...":"Submit Request"})]})]}),(0,o.jsx)(i.A,{title:"Other Resources",className:"mt-6",children:(0,o.jsxs)("ul",{className:"space-y-3",children:[(0,o.jsx)("li",{children:(0,o.jsxs)("a",{href:"#",className:"text-primary hover:text-blue-400 flex items-center",children:[(0,o.jsx)("span",{className:"mr-2",children:"\ud83d\udcc4"}),"Documentation"]})}),(0,o.jsx)("li",{children:(0,o.jsxs)("a",{href:"#",className:"text-primary hover:text-blue-400 flex items-center",children:[(0,o.jsx)("span",{className:"mr-2",children:"\ud83d\udcda"}),"Knowledge Base"]})}),(0,o.jsx)("li",{children:(0,o.jsxs)("a",{href:"#",className:"text-primary hover:text-blue-400 flex items-center",children:[(0,o.jsx)("span",{className:"mr-2",children:"\ud83d\udcf9"}),"Video Tutorials"]})}),(0,o.jsx)("li",{children:(0,o.jsxs)("a",{href:"#",className:"text-primary hover:text-blue-400 flex items-center",children:[(0,o.jsx)("span",{className:"mr-2",children:"\ud83d\udd04"}),"System Status"]})})]})})]})]})})}}}]);
//# sourceMappingURL=314.5ff87df5.chunk.js.map