{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport axios from 'axios';\nimport Card from '../components/Card';\nimport Chart from '../components/Chart';\nimport StatCard from '../components/StatCard';\nimport { useAuth } from '../contexts/AuthContext';\nimport api from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  var _dashboardData$user$d;\n  let authContextValue;\n  try {\n    authContextValue = useAuth();\n    console.log(\"Dashboard: Successfully called useAuth. Value:\", authContextValue);\n  } catch (err) {\n    console.error(\"Dashboard: Error calling useAuth!\", err);\n    // Re-throw or handle error appropriately, maybe render an error message\n    throw err; // Re-throw to see the original error behavior\n  }\n  const {\n    user\n  } = authContextValue;\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [campaignStats, setCampaignStats] = useState(null);\n\n  // Fetch campaign stats\n  useEffect(() => {\n    const fetchCampaignStats = async () => {\n      try {\n        // Use the API URL from the api service instead of hardcoded path\n        const response = await api.get('/analytics/campaign-stats', {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n        if (response.data.success) {\n          setCampaignStats(response.data.data);\n        } else {\n          throw new Error(response.data.message || 'Failed to fetch campaign stats');\n        }\n      } catch (err) {\n        console.error('Error fetching campaign stats:', err);\n        setError('Failed to fetch campaign statistics');\n      }\n    };\n    fetchCampaignStats();\n  }, []);\n\n  // Fetch dashboard data\n  useEffect(() => {\n    // In a real app, this would be an API call\n    const fetchDashboardData = async () => {\n      try {\n        // Retrieve token safely\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Authentication token not found.');\n        }\n\n        // Use the API URL from the api service instead of hardcoded path\n        const response = await api.get('/analytics/dashboard', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        // Check if response data has the expected structure\n        if (response.data && response.data.success && response.data.data) {\n          setDashboardData(response.data.data);\n        } else {\n          // Handle cases where the response might be successful (status 200)\n          // but not contain the expected data structure or success flag\n          console.error('Dashboard data fetch successful but response format is unexpected:', response.data);\n          setError('Failed to retrieve valid dashboard data.');\n        }\n      } catch (err) {\n        // Catch axios errors and other errors\n        console.error('Error fetching dashboard data:', err);\n        // More specific error handling based on error type if needed\n        if (axios.isAxiosError(err)) {\n          var _err$response, _err$response$data;\n          setError(`Failed to fetch dashboard data: ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message}`);\n        } else if (err instanceof Error) {\n          setError(`Failed to fetch dashboard data: ${err.message}`);\n        } else {\n          setError('An unknown error occurred while fetching dashboard data.');\n        }\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchDashboardData(); // Call the function\n  }, [user]); // Dependency array remains [user] for now. If campaignStats is needed immediately, might need adjustment.\n\n  // Format date for display\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  // Prepare chart data\n  const prepareChartData = () => {\n    if (!dashboardData) return null;\n\n    // Campaign performance chart\n    const campaignPerformanceData = {\n      labels: dashboardData.timeAnalytics.map(item => formatDate(item._id)),\n      datasets: [{\n        label: 'Sent',\n        data: dashboardData.timeAnalytics.map(item => item.sent),\n        backgroundColor: 'rgba(59, 130, 246, 0.5)',\n        borderColor: 'rgb(59, 130, 246)',\n        borderWidth: 1\n      }, {\n        label: 'Opens',\n        data: dashboardData.timeAnalytics.map(item => item.opens),\n        backgroundColor: 'rgba(75, 192, 192, 0.5)',\n        borderColor: 'rgb(75, 192, 192)',\n        borderWidth: 1\n      }, {\n        label: 'Clicks',\n        data: dashboardData.timeAnalytics.map(item => item.clicks),\n        backgroundColor: 'rgba(255, 94, 98, 0.5)',\n        borderColor: 'rgb(255, 94, 98)',\n        borderWidth: 1\n      }]\n    };\n\n    // Engagement rate chart\n    const engagementRateData = {\n      labels: dashboardData.timeAnalytics.map(item => formatDate(item._id)),\n      datasets: [{\n        label: 'Open Rate (%)',\n        data: dashboardData.timeAnalytics.map(item => item.sent > 0 ? (item.opens / item.sent * 100).toFixed(1) : 0),\n        backgroundColor: 'rgba(75, 192, 192, 0.5)',\n        borderColor: 'rgb(75, 192, 192)',\n        borderWidth: 1\n      }, {\n        label: 'Click Rate (%)',\n        data: dashboardData.timeAnalytics.map(item => item.sent > 0 ? (item.clicks / item.sent * 100).toFixed(1) : 0),\n        backgroundColor: 'rgba(255, 94, 98, 0.5)',\n        borderColor: 'rgb(255, 94, 98)',\n        borderWidth: 1\n      }]\n    };\n    return {\n      campaignPerformance: campaignPerformanceData,\n      engagementRate: engagementRateData\n    };\n  };\n  const chartData = dashboardData ? prepareChartData() : null;\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-accent-coral\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-danger/80 text-white p-4 rounded-md mb-6\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this);\n  }\n  if (!dashboardData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-secondary\",\n        children: \"No dashboard data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-semibold mb-2\",\n        children: [\"Welcome, \", dashboardData.user.name, \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-text-secondary\",\n        children: [\"You have \", dashboardData.user.flowsPurchased, \" flow\", dashboardData.user.flowsPurchased !== 1 ? 's' : '', \" available.\", ((_dashboardData$user$d = dashboardData.user.domain) === null || _dashboardData$user$d === void 0 ? void 0 : _dashboardData$user$d.status) === 'active' && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\" Your domain \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            className: \"text-text-primary\",\n            children: dashboardData.user.domain.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 32\n          }, this), \" is active and ready for sending.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 p-4 glassmorphic rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(StatCard, {\n        title: \"Total Campaigns\",\n        value: dashboardData.metrics.totalCampaigns,\n        icon: \"chart-bar\",\n        details: campaignStats ? [{\n          label: 'Draft',\n          value: campaignStats.byStatus.draft\n        }, {\n          label: 'Scheduled',\n          value: campaignStats.byStatus.scheduled\n        }, {\n          label: 'Sending',\n          value: campaignStats.byStatus.sending\n        }, {\n          label: 'Completed',\n          value: campaignStats.byStatus.completed\n        }] : undefined\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        title: \"Active Campaigns\",\n        value: dashboardData.metrics.activeCampaigns,\n        icon: \"rocket-launch\",\n        tooltip: \"Campaigns currently sending or scheduled\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        title: \"Total Recipients\",\n        value: (campaignStats === null || campaignStats === void 0 ? void 0 : campaignStats.totalRecipients) || 0,\n        icon: \"users\",\n        details: campaignStats ? [{\n          label: 'Sent',\n          value: campaignStats.totalSent\n        }, {\n          label: 'Errors',\n          value: campaignStats.totalErrors\n        }] : undefined\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n        title: \"Avg. Open Rate\",\n        value: `${dashboardData.metrics.averageOpenRate.toFixed(1)}%`,\n        icon: \"envelope-open\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this), (campaignStats === null || campaignStats === void 0 ? void 0 : campaignStats.recipients) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8 glassmorphic rounded-lg p-6 transition-all duration-300 ease-in-out hover:shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-semibold mb-4 text-text-primary\",\n        children: \"Recipient Statistics\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Unique Recipients\",\n          value: campaignStats.recipients.totalUnique,\n          icon: \"identification\",\n          tooltip: \"Total unique recipients in your database\",\n          className: \"bg-neutral-base/80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          title: \"Delivery Status\",\n          value: campaignStats.recipients.byStatus.sent,\n          icon: \"inbox-arrow-down\",\n          tooltip: \"Successfully sent emails\",\n          details: [{\n            label: 'Pending',\n            value: campaignStats.recipients.byStatus.pending\n          }, {\n            label: 'Sent',\n            value: campaignStats.recipients.byStatus.sent\n          }, {\n            label: 'Bounced',\n            value: campaignStats.recipients.byStatus.bounced\n          }, {\n            label: 'Unsubscribed',\n            value: campaignStats.recipients.byStatus.unsubscribed\n          }],\n          className: \"bg-neutral-base/80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        title: \"Campaign Performance\",\n        className: \"transition-all duration-300 ease-in-out hover:shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-semibold mb-4 text-text-primary\",\n          children: \"Campaign Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this), chartData && /*#__PURE__*/_jsxDEV(Chart, {\n          type: \"bar\",\n          data: chartData.campaignPerformance,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                labels: {\n                  color: 'var(--color-text-secondary)'\n                }\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: true,\n                grid: {\n                  color: 'var(--color-border)'\n                },\n                ticks: {\n                  color: 'var(--color-text-secondary)'\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                },\n                ticks: {\n                  color: 'var(--color-text-secondary)'\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"Engagement Rates\",\n        className: \"transition-all duration-300 ease-in-out hover:shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-semibold mb-4 text-text-primary\",\n          children: \"Engagement Rates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), chartData && /*#__PURE__*/_jsxDEV(Chart, {\n          type: \"line\",\n          data: chartData.engagementRate,\n          options: {\n            responsive: true,\n            maintainAspectRatio: false,\n            plugins: {\n              legend: {\n                labels: {\n                  color: 'var(--color-text-secondary)'\n                }\n              }\n            },\n            scales: {\n              y: {\n                beginAtZero: true,\n                max: 100,\n                grid: {\n                  color: 'var(--color-border)'\n                },\n                ticks: {\n                  color: 'var(--color-text-secondary)'\n                }\n              },\n              x: {\n                grid: {\n                  display: false\n                },\n                ticks: {\n                  color: 'var(--color-text-secondary)'\n                }\n              }\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"Recent Campaigns\",\n      className: \"transition-all duration-300 ease-in-out hover:shadow-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-semibold mb-4 text-text-primary\",\n        children: \"Recent Campaigns\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                children: \"Sent\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                children: \"Opens\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                children: \"Clicks\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                children: \"Created\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: dashboardData.recentCampaigns.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: 6,\n                className: \"text-center py-4 text-text-secondary\",\n                children: \"No campaigns yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this) : dashboardData.recentCampaigns.map(campaign => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-neutral-light\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: `/campaigns/edit/${campaign._id}`,\n                  className: \"hover:underline\",\n                  children: campaign.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 text-xs rounded font-medium ${campaign.status === 'draft' ? 'bg-neutral-light text-text-secondary' : campaign.status === 'scheduled' ? 'bg-primary-blue/80 text-white' : campaign.status === 'sending' ? 'bg-accent-coral/80 text-white' : campaign.status === 'completed' ? 'bg-growth-green/80 text-white' : 'bg-danger/80 text-white'}`,\n                  children: campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 363,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: campaign.sentCount || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: campaign.sentCount > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [campaign.openCount || 0, \" (\", ((campaign.openCount || 0) / campaign.sentCount * 100).toFixed(1), \"%)\"]\n                }, void 0, true) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: campaign.sentCount > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [campaign.clickCount || 0, \" (\", ((campaign.clickCount || 0) / campaign.sentCount * 100).toFixed(1), \"%)\"]\n                }, void 0, true) : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: formatDate(campaign.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 392,\n                columnNumber: 21\n              }, this)]\n            }, campaign._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Dashboard, \"YK+1cQ6JOCr6L+bRbSV6XsVFeN4=\", false, function () {\n  return [useAuth];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "axios", "Card", "Chart", "StatCard", "useAuth", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Dashboard", "_s", "_dashboardData$user$d", "authContextValue", "console", "log", "err", "error", "user", "loading", "setLoading", "setError", "dashboardData", "setDashboardData", "campaignStats", "setCampaignStats", "fetchCampaignStats", "response", "get", "headers", "localStorage", "getItem", "data", "success", "Error", "message", "fetchDashboardData", "token", "isAxiosError", "_err$response", "_err$response$data", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "prepareChartData", "campaignPerformanceData", "labels", "timeAnalytics", "map", "item", "_id", "datasets", "label", "sent", "backgroundColor", "borderColor", "borderWidth", "opens", "clicks", "engagementRateData", "toFixed", "campaignPerformance", "engagementRate", "chartData", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "flowsPurchased", "domain", "status", "title", "value", "metrics", "totalCampaigns", "icon", "details", "byStatus", "draft", "scheduled", "sending", "completed", "undefined", "activeCampaigns", "tooltip", "totalRecipients", "totalSent", "totalErrors", "averageOpenRate", "recipients", "totalUnique", "pending", "bounced", "unsubscribed", "type", "options", "responsive", "maintainAspectRatio", "plugins", "legend", "color", "scales", "y", "beginAtZero", "grid", "ticks", "x", "display", "max", "scope", "recentCampaigns", "length", "colSpan", "campaign", "href", "char<PERSON>t", "toUpperCase", "slice", "sentCount", "openCount", "clickCount", "createdAt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport axios from 'axios';\n\nimport Card from '../components/Card';\nimport Chart from '../components/Chart';\nimport StatCard from '../components/StatCard';\nimport { useAuth } from '../contexts/AuthContext';\nimport api from '../services/api';\n\nconst Dashboard: React.FC = () => {\n  let authContextValue;\n  try {\n    authContextValue = useAuth();\n    console.log(\"Dashboard: Successfully called useAuth. Value:\", authContextValue);\n  } catch (err) {\n    console.error(\"Dashboard: Error calling useAuth!\", err);\n    // Re-throw or handle error appropriately, maybe render an error message\n    throw err; // Re-throw to see the original error behavior\n  }\n\n  const { user } = authContextValue;\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [dashboardData, setDashboardData] = useState<any>(null);\n  const [campaignStats, setCampaignStats] = useState<any>(null);\n\n  // Fetch campaign stats\n  useEffect(() => {\n    const fetchCampaignStats = async () => {\n      try {\n        // Use the API URL from the api service instead of hardcoded path\n        const response = await api.get('/analytics/campaign-stats', {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n\n        if (response.data.success) {\n          setCampaignStats(response.data.data);\n        } else {\n          throw new Error(response.data.message || 'Failed to fetch campaign stats');\n        }\n      } catch (err) {\n        console.error('Error fetching campaign stats:', err);\n        setError('Failed to fetch campaign statistics');\n      }\n    };\n\n    fetchCampaignStats();\n  }, []);\n\n  // Fetch dashboard data\n  useEffect(() => {\n    // In a real app, this would be an API call\n    const fetchDashboardData = async () => {\n      try {\n        // Retrieve token safely\n        const token = localStorage.getItem('token');\n        if (!token) {\n          throw new Error('Authentication token not found.');\n        }\n\n        // Use the API URL from the api service instead of hardcoded path\n        const response = await api.get('/analytics/dashboard', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        // Check if response data has the expected structure\n        if (response.data && response.data.success && response.data.data) {\n           setDashboardData(response.data.data);\n        } else {\n           // Handle cases where the response might be successful (status 200)\n           // but not contain the expected data structure or success flag\n           console.error('Dashboard data fetch successful but response format is unexpected:', response.data);\n           setError('Failed to retrieve valid dashboard data.');\n        }\n\n      } catch (err: any) { // Catch axios errors and other errors\n         console.error('Error fetching dashboard data:', err);\n         // More specific error handling based on error type if needed\n         if (axios.isAxiosError(err)) {\n           setError(`Failed to fetch dashboard data: ${err.response?.data?.message || err.message}`);\n         } else if (err instanceof Error) {\n           setError(`Failed to fetch dashboard data: ${err.message}`);\n         } else {\n           setError('An unknown error occurred while fetching dashboard data.');\n         }\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDashboardData(); // Call the function\n  }, [user]); // Dependency array remains [user] for now. If campaignStats is needed immediately, might need adjustment.\n\n  // Format date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  // Prepare chart data\n  const prepareChartData = () => {\n    if (!dashboardData) return null;\n\n    // Campaign performance chart\n    const campaignPerformanceData = {\n      labels: dashboardData.timeAnalytics.map((item: any) => formatDate(item._id)),\n      datasets: [\n        {\n          label: 'Sent',\n          data: dashboardData.timeAnalytics.map((item: any) => item.sent),\n          backgroundColor: 'rgba(59, 130, 246, 0.5)',\n          borderColor: 'rgb(59, 130, 246)',\n          borderWidth: 1\n        },\n        {\n          label: 'Opens',\n          data: dashboardData.timeAnalytics.map((item: any) => item.opens),\n          backgroundColor: 'rgba(75, 192, 192, 0.5)',\n          borderColor: 'rgb(75, 192, 192)',\n          borderWidth: 1\n        },\n        {\n          label: 'Clicks',\n          data: dashboardData.timeAnalytics.map((item: any) => item.clicks),\n          backgroundColor: 'rgba(255, 94, 98, 0.5)',\n          borderColor: 'rgb(255, 94, 98)',\n          borderWidth: 1\n        }\n      ]\n    };\n\n    // Engagement rate chart\n    const engagementRateData = {\n      labels: dashboardData.timeAnalytics.map((item: any) => formatDate(item._id)),\n      datasets: [\n        {\n          label: 'Open Rate (%)',\n          data: dashboardData.timeAnalytics.map((item: any) =>\n            item.sent > 0 ? (item.opens / item.sent * 100).toFixed(1) : 0\n          ),\n          backgroundColor: 'rgba(75, 192, 192, 0.5)',\n          borderColor: 'rgb(75, 192, 192)',\n          borderWidth: 1\n        },\n        {\n          label: 'Click Rate (%)',\n          data: dashboardData.timeAnalytics.map((item: any) =>\n            item.sent > 0 ? (item.clicks / item.sent * 100).toFixed(1) : 0\n          ),\n          backgroundColor: 'rgba(255, 94, 98, 0.5)',\n          borderColor: 'rgb(255, 94, 98)',\n          borderWidth: 1\n        }\n      ]\n    };\n\n    return {\n      campaignPerformance: campaignPerformanceData,\n      engagementRate: engagementRateData\n    };\n  };\n\n  const chartData = dashboardData ? prepareChartData() : null;\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-accent-coral\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-danger/80 text-white p-4 rounded-md mb-6\">\n        {error}\n      </div>\n    );\n  }\n\n  if (!dashboardData) {\n    return (\n      <div className=\"text-center py-8\">\n        <p className=\"text-text-secondary\">No dashboard data available</p>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <div className=\"mb-8\">\n        <h2 className=\"text-3xl font-semibold mb-2\">Welcome, {dashboardData.user.name}!</h2>\n        <p className=\"text-text-secondary\">\n          You have {dashboardData.user.flowsPurchased} flow{dashboardData.user.flowsPurchased !== 1 ? 's' : ''} available.\n          {dashboardData.user.domain?.status === 'active' && (\n            <span> Your domain <strong className=\"text-text-primary\">{dashboardData.user.domain.name}</strong> is active and ready for sending.</span>\n          )}\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 p-4 glassmorphic rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\">\n        <StatCard\n          title=\"Total Campaigns\"\n          value={dashboardData.metrics.totalCampaigns}\n          icon=\"chart-bar\"\n          details={campaignStats ? [\n            { label: 'Draft', value: campaignStats.byStatus.draft },\n            { label: 'Scheduled', value: campaignStats.byStatus.scheduled },\n            { label: 'Sending', value: campaignStats.byStatus.sending },\n            { label: 'Completed', value: campaignStats.byStatus.completed }\n          ] : undefined}\n        />\n        <StatCard\n          title=\"Active Campaigns\"\n          value={dashboardData.metrics.activeCampaigns}\n          icon=\"rocket-launch\"\n          tooltip=\"Campaigns currently sending or scheduled\"\n        />\n        <StatCard\n          title=\"Total Recipients\"\n          value={campaignStats?.totalRecipients || 0}\n          icon=\"users\"\n          details={campaignStats ? [\n            { label: 'Sent', value: campaignStats.totalSent },\n            { label: 'Errors', value: campaignStats.totalErrors }\n          ] : undefined}\n        />\n        <StatCard\n          title=\"Avg. Open Rate\"\n          value={`${dashboardData.metrics.averageOpenRate.toFixed(1)}%`}\n          icon=\"envelope-open\"\n        />\n      </div>\n\n      {campaignStats?.recipients && (\n        <div className=\"mb-8 glassmorphic rounded-lg p-6 transition-all duration-300 ease-in-out hover:shadow-lg\">\n          <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Recipient Statistics</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <StatCard\n              title=\"Unique Recipients\"\n              value={campaignStats.recipients.totalUnique}\n              icon=\"identification\"\n              tooltip=\"Total unique recipients in your database\"\n              className=\"bg-neutral-base/80\"\n            />\n            <StatCard\n              title=\"Delivery Status\"\n              value={campaignStats.recipients.byStatus.sent}\n              icon=\"inbox-arrow-down\"\n              tooltip=\"Successfully sent emails\"\n              details={[\n                { label: 'Pending', value: campaignStats.recipients.byStatus.pending },\n                { label: 'Sent', value: campaignStats.recipients.byStatus.sent },\n                { label: 'Bounced', value: campaignStats.recipients.byStatus.bounced },\n                { label: 'Unsubscribed', value: campaignStats.recipients.byStatus.unsubscribed }\n              ]}\n              className=\"bg-neutral-base/80\"\n            />\n          </div>\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n        <Card title=\"Campaign Performance\" className=\"transition-all duration-300 ease-in-out hover:shadow-lg\">\n          <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Campaign Performance</h3>\n          {chartData && (\n            <Chart\n              type=\"bar\"\n              data={chartData.campaignPerformance}\n              options={{\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: { legend: { labels: { color: 'var(--color-text-secondary)' } } },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    grid: { color: 'var(--color-border)' },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  },\n                  x: {\n                    grid: { display: false },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  }\n                }\n              }}\n            />\n          )}\n        </Card>\n\n        <Card title=\"Engagement Rates\" className=\"transition-all duration-300 ease-in-out hover:shadow-lg\">\n          <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Engagement Rates</h3>\n          {chartData && (\n            <Chart\n              type=\"line\"\n              data={chartData.engagementRate}\n              options={{\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: { legend: { labels: { color: 'var(--color-text-secondary)' } } },\n                scales: {\n                  y: {\n                    beginAtZero: true,\n                    max: 100,\n                    grid: { color: 'var(--color-border)' },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  },\n                  x: {\n                    grid: { display: false },\n                    ticks: { color: 'var(--color-text-secondary)' }\n                  }\n                }\n              }}\n            />\n          )}\n        </Card>\n      </div>\n\n      <Card title=\"Recent Campaigns\" className=\"transition-all duration-300 ease-in-out hover:shadow-lg\">\n        <h3 className=\"text-2xl font-semibold mb-4 text-text-primary\">Recent Campaigns</h3>\n        <div className=\"table-container\">\n          <table className=\"table w-full\">\n            <thead>\n              <tr>\n                <th scope=\"col\">Name</th>\n                <th scope=\"col\">Status</th>\n                <th scope=\"col\">Sent</th>\n                <th scope=\"col\">Opens</th>\n                <th scope=\"col\">Clicks</th>\n                <th scope=\"col\">Created</th>\n              </tr>\n            </thead>\n            <tbody>\n              {dashboardData.recentCampaigns.length === 0 ? (\n                <tr>\n                  <td colSpan={6} className=\"text-center py-4 text-text-secondary\">\n                    No campaigns yet\n                  </td>\n                </tr>\n              ) : (\n                dashboardData.recentCampaigns.map((campaign: any) => (\n                  <tr key={campaign._id} className=\"hover:bg-neutral-light\">\n                    <td>\n                      <a\n                        href={`/campaigns/edit/${campaign._id}`}\n                        className=\"hover:underline\"\n                      >\n                        {campaign.name}\n                      </a>\n                    </td>\n                    <td>\n                      <span className={`px-2 py-1 text-xs rounded font-medium ${ \n                        campaign.status === 'draft' ? 'bg-neutral-light text-text-secondary' :\n                        campaign.status === 'scheduled' ? 'bg-primary-blue/80 text-white' :\n                        campaign.status === 'sending' ? 'bg-accent-coral/80 text-white' :\n                        campaign.status === 'completed' ? 'bg-growth-green/80 text-white' :\n                        'bg-danger/80 text-white'\n                      }`}>\n                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}\n                      </span>\n                    </td>\n                    <td>{campaign.sentCount || 0}</td>\n                    <td>\n                      {campaign.sentCount > 0 ? (\n                        <>\n                          {campaign.openCount || 0} ({((campaign.openCount || 0) / campaign.sentCount * 100).toFixed(1)}%)\n                        </>\n                      ) : (\n                        '-'\n                      )}\n                    </td>\n                    <td>\n                      {campaign.sentCount > 0 ? (\n                        <>\n                          {campaign.clickCount || 0} ({((campaign.clickCount || 0) / campaign.sentCount * 100).toFixed(1)}%)\n                        </>\n                      ) : (\n                        '-'\n                      )}\n                    </td>\n                    <td>{formatDate(campaign.createdAt)}</td>\n                  </tr>\n                ))\n              )}\n            </tbody>\n          </table>\n        </div>\n      </Card>\n    </>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,SAAS,EACTC,QAAQ,QACH,OAAO;AAEd,OAAOC,KAAK,MAAM,OAAO;AAEzB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,GAAG,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElC,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EAChC,IAAIC,gBAAgB;EACpB,IAAI;IACFA,gBAAgB,GAAGT,OAAO,CAAC,CAAC;IAC5BU,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEF,gBAAgB,CAAC;EACjF,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZF,OAAO,CAACG,KAAK,CAAC,mCAAmC,EAAED,GAAG,CAAC;IACvD;IACA,MAAMA,GAAG,CAAC,CAAC;EACb;EAEA,MAAM;IAAEE;EAAK,CAAC,GAAGL,gBAAgB;EACjC,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEI,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAM,IAAI,CAAC;EAC7D,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAM,IAAI,CAAC;;EAE7D;EACAD,SAAS,CAAC,MAAM;IACd,MAAM4B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF;QACA,MAAMC,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,2BAA2B,EAAE;UAC1DC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D;QACF,CAAC,CAAC;QAEF,IAAIJ,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;UACzBR,gBAAgB,CAACE,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC;QACtC,CAAC,MAAM;UACL,MAAM,IAAIE,KAAK,CAACP,QAAQ,CAACK,IAAI,CAACG,OAAO,IAAI,gCAAgC,CAAC;QAC5E;MACF,CAAC,CAAC,OAAOnB,GAAG,EAAE;QACZF,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAED,GAAG,CAAC;QACpDK,QAAQ,CAAC,qCAAqC,CAAC;MACjD;IACF,CAAC;IAEDK,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA5B,SAAS,CAAC,MAAM;IACd;IACA,MAAMsC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF;QACA,MAAMC,KAAK,GAAGP,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAACM,KAAK,EAAE;UACV,MAAM,IAAIH,KAAK,CAAC,iCAAiC,CAAC;QACpD;;QAEA;QACA,MAAMP,QAAQ,GAAG,MAAMtB,GAAG,CAACuB,GAAG,CAAC,sBAAsB,EAAE;UACrDC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUQ,KAAK;UAClC;QACF,CAAC,CAAC;;QAEF;QACA,IAAIV,QAAQ,CAACK,IAAI,IAAIL,QAAQ,CAACK,IAAI,CAACC,OAAO,IAAIN,QAAQ,CAACK,IAAI,CAACA,IAAI,EAAE;UAC/DT,gBAAgB,CAACI,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC;QACvC,CAAC,MAAM;UACJ;UACA;UACAlB,OAAO,CAACG,KAAK,CAAC,oEAAoE,EAAEU,QAAQ,CAACK,IAAI,CAAC;UAClGX,QAAQ,CAAC,0CAA0C,CAAC;QACvD;MAEF,CAAC,CAAC,OAAOL,GAAQ,EAAE;QAAE;QAClBF,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAED,GAAG,CAAC;QACpD;QACA,IAAIhB,KAAK,CAACsC,YAAY,CAACtB,GAAG,CAAC,EAAE;UAAA,IAAAuB,aAAA,EAAAC,kBAAA;UAC3BnB,QAAQ,CAAC,mCAAmC,EAAAkB,aAAA,GAAAvB,GAAG,CAACW,QAAQ,cAAAY,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcP,IAAI,cAAAQ,kBAAA,uBAAlBA,kBAAA,CAAoBL,OAAO,KAAInB,GAAG,CAACmB,OAAO,EAAE,CAAC;QAC3F,CAAC,MAAM,IAAInB,GAAG,YAAYkB,KAAK,EAAE;UAC/Bb,QAAQ,CAAC,mCAAmCL,GAAG,CAACmB,OAAO,EAAE,CAAC;QAC5D,CAAC,MAAM;UACLd,QAAQ,CAAC,0DAA0D,CAAC;QACtE;MACH,CAAC,SAAS;QACRD,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDgB,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ;EACA,MAAMuB,UAAU,GAAIC,UAAkB,IAAK;IACzC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAAC3B,aAAa,EAAE,OAAO,IAAI;;IAE/B;IACA,MAAM4B,uBAAuB,GAAG;MAC9BC,MAAM,EAAE7B,aAAa,CAAC8B,aAAa,CAACC,GAAG,CAAEC,IAAS,IAAKb,UAAU,CAACa,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5EC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,MAAM;QACbzB,IAAI,EAAEV,aAAa,CAAC8B,aAAa,CAACC,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACI,IAAI,CAAC;QAC/DC,eAAe,EAAE,yBAAyB;QAC1CC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE;MACf,CAAC,EACD;QACEJ,KAAK,EAAE,OAAO;QACdzB,IAAI,EAAEV,aAAa,CAAC8B,aAAa,CAACC,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACQ,KAAK,CAAC;QAChEH,eAAe,EAAE,yBAAyB;QAC1CC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE;MACf,CAAC,EACD;QACEJ,KAAK,EAAE,QAAQ;QACfzB,IAAI,EAAEV,aAAa,CAAC8B,aAAa,CAACC,GAAG,CAAEC,IAAS,IAAKA,IAAI,CAACS,MAAM,CAAC;QACjEJ,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE;MACf,CAAC;IAEL,CAAC;;IAED;IACA,MAAMG,kBAAkB,GAAG;MACzBb,MAAM,EAAE7B,aAAa,CAAC8B,aAAa,CAACC,GAAG,CAAEC,IAAS,IAAKb,UAAU,CAACa,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5EC,QAAQ,EAAE,CACR;QACEC,KAAK,EAAE,eAAe;QACtBzB,IAAI,EAAEV,aAAa,CAAC8B,aAAa,CAACC,GAAG,CAAEC,IAAS,IAC9CA,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,CAACJ,IAAI,CAACQ,KAAK,GAAGR,IAAI,CAACI,IAAI,GAAG,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC,GAAG,CAC9D,CAAC;QACDN,eAAe,EAAE,yBAAyB;QAC1CC,WAAW,EAAE,mBAAmB;QAChCC,WAAW,EAAE;MACf,CAAC,EACD;QACEJ,KAAK,EAAE,gBAAgB;QACvBzB,IAAI,EAAEV,aAAa,CAAC8B,aAAa,CAACC,GAAG,CAAEC,IAAS,IAC9CA,IAAI,CAACI,IAAI,GAAG,CAAC,GAAG,CAACJ,IAAI,CAACS,MAAM,GAAGT,IAAI,CAACI,IAAI,GAAG,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC,GAAG,CAC/D,CAAC;QACDN,eAAe,EAAE,wBAAwB;QACzCC,WAAW,EAAE,kBAAkB;QAC/BC,WAAW,EAAE;MACf,CAAC;IAEL,CAAC;IAED,OAAO;MACLK,mBAAmB,EAAEhB,uBAAuB;MAC5CiB,cAAc,EAAEH;IAClB,CAAC;EACH,CAAC;EAED,MAAMI,SAAS,GAAG9C,aAAa,GAAG2B,gBAAgB,CAAC,CAAC,GAAG,IAAI;EAE3D,IAAI9B,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAK8D,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD/D,OAAA;QAAK8D,SAAS,EAAC;MAA+E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClG,CAAC;EAEV;EAEA,IAAIzD,KAAK,EAAE;IACT,oBACEV,OAAA;MAAK8D,SAAS,EAAC,6CAA6C;MAAAC,QAAA,EACzDrD;IAAK;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACpD,aAAa,EAAE;IAClB,oBACEf,OAAA;MAAK8D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B/D,OAAA;QAAG8D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC;EAEV;EAEA,oBACEnE,OAAA,CAAAE,SAAA;IAAA6D,QAAA,gBACE/D,OAAA;MAAK8D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnB/D,OAAA;QAAI8D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GAAC,WAAS,EAAChD,aAAa,CAACJ,IAAI,CAACyD,IAAI,EAAC,GAAC;MAAA;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpFnE,OAAA;QAAG8D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,GAAC,WACxB,EAAChD,aAAa,CAACJ,IAAI,CAAC0D,cAAc,EAAC,OAAK,EAACtD,aAAa,CAACJ,IAAI,CAAC0D,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,aACrG,EAAC,EAAAhE,qBAAA,GAAAU,aAAa,CAACJ,IAAI,CAAC2D,MAAM,cAAAjE,qBAAA,uBAAzBA,qBAAA,CAA2BkE,MAAM,MAAK,QAAQ,iBAC7CvE,OAAA;UAAA+D,QAAA,GAAM,eAAa,eAAA/D,OAAA;YAAQ8D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAEhD,aAAa,CAACJ,IAAI,CAAC2D,MAAM,CAACF;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,qCAAiC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAC1I;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENnE,OAAA;MAAK8D,SAAS,EAAC,+IAA+I;MAAAC,QAAA,gBAC5J/D,OAAA,CAACJ,QAAQ;QACP4E,KAAK,EAAC,iBAAiB;QACvBC,KAAK,EAAE1D,aAAa,CAAC2D,OAAO,CAACC,cAAe;QAC5CC,IAAI,EAAC,WAAW;QAChBC,OAAO,EAAE5D,aAAa,GAAG,CACvB;UAAEiC,KAAK,EAAE,OAAO;UAAEuB,KAAK,EAAExD,aAAa,CAAC6D,QAAQ,CAACC;QAAM,CAAC,EACvD;UAAE7B,KAAK,EAAE,WAAW;UAAEuB,KAAK,EAAExD,aAAa,CAAC6D,QAAQ,CAACE;QAAU,CAAC,EAC/D;UAAE9B,KAAK,EAAE,SAAS;UAAEuB,KAAK,EAAExD,aAAa,CAAC6D,QAAQ,CAACG;QAAQ,CAAC,EAC3D;UAAE/B,KAAK,EAAE,WAAW;UAAEuB,KAAK,EAAExD,aAAa,CAAC6D,QAAQ,CAACI;QAAU,CAAC,CAChE,GAAGC;MAAU;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACFnE,OAAA,CAACJ,QAAQ;QACP4E,KAAK,EAAC,kBAAkB;QACxBC,KAAK,EAAE1D,aAAa,CAAC2D,OAAO,CAACU,eAAgB;QAC7CR,IAAI,EAAC,eAAe;QACpBS,OAAO,EAAC;MAA0C;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACFnE,OAAA,CAACJ,QAAQ;QACP4E,KAAK,EAAC,kBAAkB;QACxBC,KAAK,EAAE,CAAAxD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqE,eAAe,KAAI,CAAE;QAC3CV,IAAI,EAAC,OAAO;QACZC,OAAO,EAAE5D,aAAa,GAAG,CACvB;UAAEiC,KAAK,EAAE,MAAM;UAAEuB,KAAK,EAAExD,aAAa,CAACsE;QAAU,CAAC,EACjD;UAAErC,KAAK,EAAE,QAAQ;UAAEuB,KAAK,EAAExD,aAAa,CAACuE;QAAY,CAAC,CACtD,GAAGL;MAAU;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC,eACFnE,OAAA,CAACJ,QAAQ;QACP4E,KAAK,EAAC,gBAAgB;QACtBC,KAAK,EAAE,GAAG1D,aAAa,CAAC2D,OAAO,CAACe,eAAe,CAAC/B,OAAO,CAAC,CAAC,CAAC,GAAI;QAC9DkB,IAAI,EAAC;MAAe;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAEL,CAAAlD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyE,UAAU,kBACxB1F,OAAA;MAAK8D,SAAS,EAAC,0FAA0F;MAAAC,QAAA,gBACvG/D,OAAA;QAAI8D,SAAS,EAAC,+CAA+C;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvFnE,OAAA;QAAK8D,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBACnE/D,OAAA,CAACJ,QAAQ;UACP4E,KAAK,EAAC,mBAAmB;UACzBC,KAAK,EAAExD,aAAa,CAACyE,UAAU,CAACC,WAAY;UAC5Cf,IAAI,EAAC,gBAAgB;UACrBS,OAAO,EAAC,0CAA0C;UAClDvB,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACFnE,OAAA,CAACJ,QAAQ;UACP4E,KAAK,EAAC,iBAAiB;UACvBC,KAAK,EAAExD,aAAa,CAACyE,UAAU,CAACZ,QAAQ,CAAC3B,IAAK;UAC9CyB,IAAI,EAAC,kBAAkB;UACvBS,OAAO,EAAC,0BAA0B;UAClCR,OAAO,EAAE,CACP;YAAE3B,KAAK,EAAE,SAAS;YAAEuB,KAAK,EAAExD,aAAa,CAACyE,UAAU,CAACZ,QAAQ,CAACc;UAAQ,CAAC,EACtE;YAAE1C,KAAK,EAAE,MAAM;YAAEuB,KAAK,EAAExD,aAAa,CAACyE,UAAU,CAACZ,QAAQ,CAAC3B;UAAK,CAAC,EAChE;YAAED,KAAK,EAAE,SAAS;YAAEuB,KAAK,EAAExD,aAAa,CAACyE,UAAU,CAACZ,QAAQ,CAACe;UAAQ,CAAC,EACtE;YAAE3C,KAAK,EAAE,cAAc;YAAEuB,KAAK,EAAExD,aAAa,CAACyE,UAAU,CAACZ,QAAQ,CAACgB;UAAa,CAAC,CAChF;UACFhC,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDnE,OAAA;MAAK8D,SAAS,EAAC,4CAA4C;MAAAC,QAAA,gBACzD/D,OAAA,CAACN,IAAI;QAAC8E,KAAK,EAAC,sBAAsB;QAACV,SAAS,EAAC,yDAAyD;QAAAC,QAAA,gBACpG/D,OAAA;UAAI8D,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACtFN,SAAS,iBACR7D,OAAA,CAACL,KAAK;UACJoG,IAAI,EAAC,KAAK;UACVtE,IAAI,EAAEoC,SAAS,CAACF,mBAAoB;UACpCqC,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cAAEC,MAAM,EAAE;gBAAExD,MAAM,EAAE;kBAAEyD,KAAK,EAAE;gBAA8B;cAAE;YAAE,CAAC;YACzEC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,IAAI;gBACjBC,IAAI,EAAE;kBAAEJ,KAAK,EAAE;gBAAsB,CAAC;gBACtCK,KAAK,EAAE;kBAAEL,KAAK,EAAE;gBAA8B;cAChD,CAAC;cACDM,CAAC,EAAE;gBACDF,IAAI,EAAE;kBAAEG,OAAO,EAAE;gBAAM,CAAC;gBACxBF,KAAK,EAAE;kBAAEL,KAAK,EAAE;gBAA8B;cAChD;YACF;UACF;QAAE;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEPnE,OAAA,CAACN,IAAI;QAAC8E,KAAK,EAAC,kBAAkB;QAACV,SAAS,EAAC,yDAAyD;QAAAC,QAAA,gBAChG/D,OAAA;UAAI8D,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAClFN,SAAS,iBACR7D,OAAA,CAACL,KAAK;UACJoG,IAAI,EAAC,MAAM;UACXtE,IAAI,EAAEoC,SAAS,CAACD,cAAe;UAC/BoC,OAAO,EAAE;YACPC,UAAU,EAAE,IAAI;YAChBC,mBAAmB,EAAE,KAAK;YAC1BC,OAAO,EAAE;cAAEC,MAAM,EAAE;gBAAExD,MAAM,EAAE;kBAAEyD,KAAK,EAAE;gBAA8B;cAAE;YAAE,CAAC;YACzEC,MAAM,EAAE;cACNC,CAAC,EAAE;gBACDC,WAAW,EAAE,IAAI;gBACjBK,GAAG,EAAE,GAAG;gBACRJ,IAAI,EAAE;kBAAEJ,KAAK,EAAE;gBAAsB,CAAC;gBACtCK,KAAK,EAAE;kBAAEL,KAAK,EAAE;gBAA8B;cAChD,CAAC;cACDM,CAAC,EAAE;gBACDF,IAAI,EAAE;kBAAEG,OAAO,EAAE;gBAAM,CAAC;gBACxBF,KAAK,EAAE;kBAAEL,KAAK,EAAE;gBAA8B;cAChD;YACF;UACF;QAAE;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENnE,OAAA,CAACN,IAAI;MAAC8E,KAAK,EAAC,kBAAkB;MAACV,SAAS,EAAC,yDAAyD;MAAAC,QAAA,gBAChG/D,OAAA;QAAI8D,SAAS,EAAC,+CAA+C;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnFnE,OAAA;QAAK8D,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B/D,OAAA;UAAO8D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC7B/D,OAAA;YAAA+D,QAAA,eACE/D,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAI8G,KAAK,EAAC,KAAK;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBnE,OAAA;gBAAI8G,KAAK,EAAC,KAAK;gBAAA/C,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BnE,OAAA;gBAAI8G,KAAK,EAAC,KAAK;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBnE,OAAA;gBAAI8G,KAAK,EAAC,KAAK;gBAAA/C,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1BnE,OAAA;gBAAI8G,KAAK,EAAC,KAAK;gBAAA/C,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BnE,OAAA;gBAAI8G,KAAK,EAAC,KAAK;gBAAA/C,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRnE,OAAA;YAAA+D,QAAA,EACGhD,aAAa,CAACgG,eAAe,CAACC,MAAM,KAAK,CAAC,gBACzChH,OAAA;cAAA+D,QAAA,eACE/D,OAAA;gBAAIiH,OAAO,EAAE,CAAE;gBAACnD,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEjE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAELpD,aAAa,CAACgG,eAAe,CAACjE,GAAG,CAAEoE,QAAa,iBAC9ClH,OAAA;cAAuB8D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACvD/D,OAAA;gBAAA+D,QAAA,eACE/D,OAAA;kBACEmH,IAAI,EAAE,mBAAmBD,QAAQ,CAAClE,GAAG,EAAG;kBACxCc,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAE1BmD,QAAQ,CAAC9C;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACLnE,OAAA;gBAAA+D,QAAA,eACE/D,OAAA;kBAAM8D,SAAS,EAAE,yCACfoD,QAAQ,CAAC3C,MAAM,KAAK,OAAO,GAAG,sCAAsC,GACpE2C,QAAQ,CAAC3C,MAAM,KAAK,WAAW,GAAG,+BAA+B,GACjE2C,QAAQ,CAAC3C,MAAM,KAAK,SAAS,GAAG,+BAA+B,GAC/D2C,QAAQ,CAAC3C,MAAM,KAAK,WAAW,GAAG,+BAA+B,GACjE,yBAAyB,EACxB;kBAAAR,QAAA,EACAmD,QAAQ,CAAC3C,MAAM,CAAC6C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGH,QAAQ,CAAC3C,MAAM,CAAC+C,KAAK,CAAC,CAAC;gBAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACLnE,OAAA;gBAAA+D,QAAA,EAAKmD,QAAQ,CAACK,SAAS,IAAI;cAAC;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClCnE,OAAA;gBAAA+D,QAAA,EACGmD,QAAQ,CAACK,SAAS,GAAG,CAAC,gBACrBvH,OAAA,CAAAE,SAAA;kBAAA6D,QAAA,GACGmD,QAAQ,CAACM,SAAS,IAAI,CAAC,EAAC,IAAE,EAAC,CAAC,CAACN,QAAQ,CAACM,SAAS,IAAI,CAAC,IAAIN,QAAQ,CAACK,SAAS,GAAG,GAAG,EAAE7D,OAAO,CAAC,CAAC,CAAC,EAAC,IAChG;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnE,OAAA;gBAAA+D,QAAA,EACGmD,QAAQ,CAACK,SAAS,GAAG,CAAC,gBACrBvH,OAAA,CAAAE,SAAA;kBAAA6D,QAAA,GACGmD,QAAQ,CAACO,UAAU,IAAI,CAAC,EAAC,IAAE,EAAC,CAAC,CAACP,QAAQ,CAACO,UAAU,IAAI,CAAC,IAAIP,QAAQ,CAACK,SAAS,GAAG,GAAG,EAAE7D,OAAO,CAAC,CAAC,CAAC,EAAC,IAClG;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACLnE,OAAA;gBAAA+D,QAAA,EAAK7B,UAAU,CAACgF,QAAQ,CAACQ,SAAS;cAAC;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA,GAvClC+C,QAAQ,CAAClE,GAAG;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwCjB,CACL;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA,eACP,CAAC;AAEP,CAAC;AAAC/D,EAAA,CApYID,SAAmB;EAAA,QAGFN,OAAO;AAAA;AAAA8H,EAAA,GAHxBxH,SAAmB;AAsYzB,eAAeA,SAAS;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}