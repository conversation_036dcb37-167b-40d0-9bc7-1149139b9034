{"version": 3, "file": "static/js/552.7576a392.chunk.js", "mappings": "8TAiBA,MA2EA,EA3EkDA,IAQ3C,IAR4C,OACjDC,EAAM,MACNC,EAAK,QACLC,EAAO,YACPC,EAAc,UAAS,WACvBC,EAAa,SAAQ,UACrBC,EAAS,SACTC,GACDP,EACC,MAAMQ,GAAWC,EAAAA,EAAAA,QAAuB,MAoCxC,OAjCAC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAASK,UAAYL,EAASK,QAAQC,SAASF,EAAMG,SACvDR,GACF,EAOF,OAJIN,GACFe,SAASC,iBAAiB,YAAaN,GAGlC,KACLK,SAASE,oBAAoB,YAAaP,EAAmB,CAC9D,GACA,CAACV,EAAQM,KAGZG,EAAAA,EAAAA,YAAU,KACR,MAAMS,EAAgBP,IACF,WAAdA,EAAMQ,KACRb,GACF,EAOF,OAJIN,GACFe,SAASC,iBAAiB,UAAWE,GAGhC,KACLH,SAASE,oBAAoB,UAAWC,EAAa,CACtD,GACA,CAAClB,EAAQM,IAEPN,GAGHoB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6EAA4EC,UACzFC,EAAAA,EAAAA,MAAA,OACEC,IAAKjB,EACLc,UAAU,mFAAkFC,SAAA,EAE5FF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAErB,KACvDmB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAoBC,SAAEpB,KAErCqB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,QAAQ,YACRC,QAASrB,EAASgB,SAEjBlB,KAEHgB,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,QAAQ,SACRC,QAAStB,EAAUiB,SAElBnB,YAtBS,IA0BZ,C,2ECnEVyB,EAAAA,GAAQC,SACNC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,GACAC,EAAAA,IAWF,MAuBA,EAvBoCvC,IAA6C,IAA5C,KAAEwC,EAAI,KAAEC,EAAI,QAAEC,EAAO,OAAEC,EAAM,MAAEC,GAAO5C,EACzE,MAAM6C,EAAe,CACnBC,YAAY,EACZC,qBAAqB,KAClBL,GAgBL,OAAOrB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,SAbnByB,MAClB,OAAQR,GACN,IAAK,OACH,OAAOnB,EAAAA,EAAAA,KAAC4B,EAAAA,GAAI,CAACR,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACzE,IAAK,MACH,OAAOvB,EAAAA,EAAAA,KAAC6B,EAAAA,GAAG,CAACT,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACxE,IAAK,MACH,OAAOvB,EAAAA,EAAAA,KAAC8B,EAAAA,GAAG,CAACV,KAAMA,EAAMC,QAASG,EAAcF,OAAQA,EAAQC,MAAOA,IACxE,QACE,OAAOvB,EAAAA,EAAAA,KAAA,KAAAE,SAAG,2BACd,EAGuCyB,IAAoB,C,uDC7C/D,MAAMI,EAAOpD,IAAA,IAAC,KAAEqD,GAAwBrD,EAAA,OAAKqB,EAAAA,EAAAA,KAAA,KAAGC,UAAW,oBAAoB+B,aAAkB,EAyDjG,EAvD0CC,IAQnC,IARoC,MACzCpD,EAAK,MACLqD,EAAK,KACLC,EAAI,OACJC,EAAM,UACNnC,EAAY,GAAE,QACdoC,EAAO,QACPC,GACDL,EAKC,OACE9B,EAAAA,EAAAA,MAAA,OAAKF,UAAW,2CAAoBA,IAAYC,SAAA,EAC9CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAErB,IAC3BsD,IAECnC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,UAC9CF,EAAAA,EAAAA,KAAC+B,EAAI,CAACC,KAAMG,UAIlBnC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,SAAEgC,IACjCG,IACCrC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,SAAEmC,IAE/DD,IAECjC,EAAAA,EAAAA,MAAA,OAAKF,UAAW,+CAA8CmC,EAAOG,WAAa,oBAAsB,eAAgBrC,SAAA,EACtHF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,OAAMC,SACnBkC,EAAOG,YACNvC,EAAAA,EAAAA,KAAA,OAAKwC,MAAM,6BAA6BvC,UAAU,UAAUwC,QAAQ,YAAYC,KAAK,eAAcxC,UAACF,EAAAA,EAAAA,KAAA,QAAM2C,SAAS,UAAUC,EAAE,sHAAsHC,SAAS,eAC9P7C,EAAAA,EAAAA,KAAA,OAAKwC,MAAM,6BAA6BvC,UAAU,UAAUwC,QAAQ,YAAYC,KAAK,eAAcxC,UAACF,EAAAA,EAAAA,KAAA,QAAM2C,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,gBAGhQT,EAAOF,SAGXI,GAAWA,EAAQQ,OAAS,IAE3B9C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDoC,EAAQS,KAAI,CAACC,EAAQC,KACpB9C,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAE8C,EAAOE,SACzDlD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAE8C,EAAOd,UAF9Be,SAOZ,C", "sources": ["components/ConfirmModal.tsx", "components/Chart.tsx", "components/StatCard.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useRef,\n} from 'react';\n\nimport Button from './Button';\n\ninterface ConfirmModalProps {\n  isOpen: boolean;\n  title: string;\n  message: React.ReactNode;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n}\n\nconst ConfirmModal: React.FC<ConfirmModalProps> = ({\n  isOpen,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  onConfirm,\n  onCancel,\n}) => {\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  // Close modal when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {\n        onCancel();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isOpen, onCancel]);\n\n  // Handle escape key press\n  useEffect(() => {\n    const handleEscKey = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onCancel();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscKey);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscKey);\n    };\n  }, [isOpen, onCancel]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\n      <div\n        ref={modalRef}\n        className=\"bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6 w-full max-w-md mx-4\"\n      >\n        <h3 className=\"text-xl font-semibold mb-4 text-white\">{title}</h3>\n        <div className=\"mb-6 text-gray-300\">{message}</div>\n\n        <div className=\"flex justify-end space-x-3\">\n          <Button\n            variant=\"secondary\"\n            onClick={onCancel}\n          >\n            {cancelText}\n          </Button>\n          <Button\n            variant=\"danger\"\n            onClick={onConfirm}\n          >\n            {confirmText}\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfirmModal;\n", "import React from 'react';\n\nimport {\n  ArcElement,\n  BarElement,\n  CategoryScale,\n  Chart as ChartJS,\n  Legend,\n  LinearScale,\n  LineElement,\n  PointElement,\n  Title,\n  Tooltip,\n} from 'chart.js';\nimport {\n  Bar,\n  Line,\n  Pie,\n} from 'react-chartjs-2';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  ArcElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\ninterface ChartProps {\n  type: 'line' | 'bar' | 'pie';\n  data: any;\n  options?: any;\n  height?: number;\n  width?: number;\n}\n\nconst Chart: React.FC<ChartProps> = ({ type, data, options, height, width }) => {\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: true,\n    ...options,\n  };\n\n  const renderChart = () => {\n    switch (type) {\n      case 'line':\n        return <Line data={data} options={chartOptions} height={height} width={width} />;\n      case 'bar':\n        return <Bar data={data} options={chartOptions} height={height} width={width} />;\n      case 'pie':\n        return <Pie data={data} options={chartOptions} height={height} width={width} />;\n      default:\n        return <p>Unsupported chart type</p>;\n    }\n  };\n\n  return <div className=\"chart-container\">{renderChart()}</div>;\n};\n\nexport default Chart;\n", "import React from 'react';\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  icon?: string;\n  change?: {\n    value: string | number;\n    isPositive: boolean;\n  };\n  className?: string;\n  tooltip?: string;\n  details?: { label: string; value: string | number }[];\n}\n\n// Helper for icon rendering (replace with your actual icon logic)\nconst Icon = ({ name }: { name: string }) => <i className={`placeholder-icon-${name} w-5 h-5`} />; \n\nconst StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  icon,\n  change,\n  className = '',\n  tooltip,\n  details\n}) => {\n  // Use the base .card or apply .glassmorphic for futuristic feel\n  // Use container-futuristic if defined, otherwise default card styling from index.css\n  const baseCardClass = \"card container-futuristic flex flex-col\"; \n\n  return (\n    <div className={`${baseCardClass} ${className}`}>\n      <div className=\"flex justify-between items-start mb-2\">\n        <h3 className=\"stat-label\">{title}</h3>\n        {icon && (\n          // Use text-secondary for default icon color\n          <span className=\"text-text-secondary opacity-80\">\n            <Icon name={icon} />\n          </span>\n        )}\n      </div>\n      <div className=\"stat-value mb-1\">{value}</div>\n      {tooltip && (\n        <div className=\"text-xs text-text-secondary mt-1 opacity-90\">{tooltip}</div>\n      )}\n      {change && (\n        // Apply theme colors: growth-green for positive, danger for negative\n        <div className={`text-sm mt-2 flex items-center font-medium ${change.isPositive ? 'text-growth-green' : 'text-danger'}`}>\n          <span className=\"mr-1\">\n            {change.isPositive ? \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z\" clipRule=\"evenodd\" /></svg> : \n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" /></svg> \n            }\n          </span>\n          {change.value}\n        </div>\n      )}\n      {details && details.length > 0 && (\n        // Use the theme border color\n        <div className=\"mt-auto pt-3 border-t border-border mt-3\">\n          {details.map((detail, index) => (\n            <div key={index} className=\"flex justify-between text-xs py-1\">\n              <span className=\"text-text-secondary opacity-90\">{detail.label}</span>\n              <span className=\"font-medium\">{detail.value}</span>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default StatCard;\n"], "names": ["_ref", "isOpen", "title", "message", "confirmText", "cancelText", "onConfirm", "onCancel", "modalRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleEscKey", "key", "_jsx", "className", "children", "_jsxs", "ref", "<PERSON><PERSON>", "variant", "onClick", "ChartJS", "register", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "ArcElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "type", "data", "options", "height", "width", "chartOptions", "responsive", "maintainAspectRatio", "<PERSON><PERSON><PERSON>", "Line", "Bar", "Pie", "Icon", "name", "_ref2", "value", "icon", "change", "tooltip", "details", "isPositive", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "length", "map", "detail", "index", "label"], "sourceRoot": ""}