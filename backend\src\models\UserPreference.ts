// backend/models/UserPreference.ts

import mongoose, {
  Document,
  Schema,
} from 'mongoose';

// Interface for UserPreference document
export interface IUserPreference extends Document {
  userId: string;
  companyName?: string;
  industry?: string;
  targetAudience?: string;
  preferredTone?: string;
  brandColors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
    background?: string;
    text?: string;
  };
  brandLogo?: string;
  defaultFonts?: {
    heading?: string;
    body?: string;
  };
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    website?: string;
  };
  footerText?: string;
  unsubscribeText?: string;
  favoriteBlocks?: string[];
  recentTemplates?: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Define default values separately for clarity
const defaultBrandColors = {
    primary: '#4F46E5',
    secondary: '#10B981',
    accent: '#F59E0B',
    background: '#FFFFFF',
    text: '#111827'
};

const defaultFonts = {
    heading: 'Arial, sans-serif',
    body: 'Arial, sans-serif'
};

const defaultFooterText = 'Copyright © 2025. All rights reserved.';
const defaultUnsubscribeText = 'If you prefer not to receive emails from us, you can unsubscribe here.';

// Schema definition
const UserPreferenceSchema: Schema = new Schema(
  {
    userId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    companyName: { type: String, default: '' },
    industry: { type: String, default: '' },
    targetAudience: { type: String, default: 'General Audience' },
    preferredTone: { type: String, default: 'Neutral' },
    brandColors: {
      primary: { type: String, default: defaultBrandColors.primary },
      secondary: { type: String, default: defaultBrandColors.secondary },
      accent: { type: String, default: defaultBrandColors.accent },
      background: { type: String, default: defaultBrandColors.background },
      text: { type: String, default: defaultBrandColors.text }
    },
    brandLogo: {
      type: String,
      default: ''
    },
    defaultFonts: {
      heading: { type: String, default: defaultFonts.heading },
      body: { type: String, default: defaultFonts.body }
    },
    socialLinks: {
      facebook: { type: String, default: '' },
      twitter: { type: String, default: '' },
      instagram: { type: String, default: '' },
      linkedin: { type: String, default: '' },
      website: { type: String, default: '' }
    },
    footerText: {
      type: String,
      default: defaultFooterText
    },
    unsubscribeText: {
      type: String,
      default: defaultUnsubscribeText
    },
    favoriteBlocks: {
      type: [String],
      default: []
    },
    recentTemplates: {
      type: [String], // Store Template ObjectIds as strings
      default: []
    }
  },
  {
    timestamps: true // Automatically adds createdAt and updatedAt
  }
);

// Ensure recentTemplates doesn't grow indefinitely (e.g., keep last 20)
UserPreferenceSchema.pre<IUserPreference>('save', function(next) {
  if (this.isModified('recentTemplates') && this.recentTemplates && this.recentTemplates.length > 20) {
    this.recentTemplates = this.recentTemplates.slice(-20);
  }
  next();
});

// Create the model
// Check if the model already exists before defining it
const UserPreference = mongoose.models.UserPreference || mongoose.model<IUserPreference>('UserPreference', UserPreferenceSchema);

export default UserPreference; 