{"version": 3, "file": "static/js/33.8f2e4e66.chunk.js", "mappings": "oJAiBA,MA2EA,EA3EkDA,IAQ3C,IAR4C,OACjDC,EAAM,MACNC,EAAK,QACLC,EAAO,YACPC,EAAc,UAAS,WACvBC,EAAa,SAAQ,UACrBC,EAAS,SACTC,GACDP,EACC,MAAMQ,GAAWC,EAAAA,EAAAA,QAAuB,MAoCxC,OAjCAC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAASK,UAAYL,EAASK,QAAQC,SAASF,EAAMG,SACvDR,GACF,EAOF,OAJIN,GACFe,SAASC,iBAAiB,YAAaN,GAGlC,KACLK,SAASE,oBAAoB,YAAaP,EAAmB,CAC9D,GACA,CAACV,EAAQM,KAGZG,EAAAA,EAAAA,YAAU,KACR,MAAMS,EAAgBP,IACF,WAAdA,EAAMQ,KACRb,GACF,EAOF,OAJIN,GACFe,SAASC,iBAAiB,UAAWE,GAGhC,KACLH,SAASE,oBAAoB,UAAWC,EAAa,CACtD,GACA,CAAClB,EAAQM,IAEPN,GAGHoB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6EAA4EC,UACzFC,EAAAA,EAAAA,MAAA,OACEC,IAAKjB,EACLc,UAAU,mFAAkFC,SAAA,EAE5FF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wCAAuCC,SAAErB,KACvDmB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qBAAoBC,SAAEpB,KAErCqB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,QAAQ,YACRC,QAASrB,EAASgB,SAEjBlB,KAEHgB,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,QAAQ,SACRC,QAAStB,EAAUiB,SAElBnB,YAtBS,IA0BZ,C,sJCvEV,MAyYA,EAzYqCyB,KACnC,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,MACT,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,OAGjBzB,EAAAA,EAAAA,YAAU,KACR,IAAKoB,GAAa,cAAPA,EAGT,OAFAM,QAAQC,IAAI,2DACZH,EAAS,cAGXE,QAAQC,IAAI,qCAAsCP,EAAG,GACpD,CAACA,EAAII,IAER,MAAOI,EAAUC,IAAeC,EAAAA,EAAAA,UAAc,OACvCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAgB,KAC7CG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAQC,IAAaN,EAAAA,EAAAA,WAAS,IAC9BO,EAAOC,IAAYR,EAAAA,EAAAA,UAAS,KAC5BS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,KAChCW,EAAYC,IAAiBZ,EAAAA,EAAAA,UAAS,KACtCa,EAAYC,IAAiBd,EAAAA,EAAAA,UAAS,KACtCe,EAAeC,IAAoBhB,EAAAA,EAAAA,WAAS,IAC5CiB,EAAmBC,IAAwBlB,EAAAA,EAAAA,UAAwB,OACnEmB,EAAiBC,IAAsBpB,EAAAA,EAAAA,WAAS,IAGvD9B,EAAAA,EAAAA,YAAU,KAC2BmD,WACjC,GAAK/B,GAAa,cAAPA,EAEX,IAEE,MAAMgC,QAAyBC,EAAAA,EAAYC,YAAYlC,GACvDS,EAAYuB,EAAiBG,KAAK3B,UAGlC,MAAM4B,QAA2BH,EAAAA,EAAYI,sBAAsBrC,GACnEY,EAAcwB,EAAmBD,KAAKG,oBAAsB,IAE5DxB,GAAW,EACb,CAAE,MAAOyB,GAAW,IAADC,EAAAC,EACjBnC,QAAQW,MAAM,yCAA0CsB,GACxDrB,GAAqB,QAAZsB,EAAAD,EAAIG,gBAAQ,IAAAF,GAAM,QAANC,EAAZD,EAAcL,YAAI,IAAAM,OAAN,EAAZA,EAAoBpE,UAAW,wBACxCyC,GAAW,EACb,GAGF6B,EAA4B,GAC3B,CAAC3C,IAuHJ,GAAIa,EACF,OACEtB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+EAKrB,IAAKgB,EACH,OACEd,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,KAAK,QACLxE,QAAQ,qBACRmB,UAAU,UAEZD,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CAACE,QAASA,IAAMM,EAAS,cAAcX,SAAC,yBAQrD,MAAMqD,EAAqC,YAApBtC,EAASuC,QAA4C,cAApBvC,EAASuC,QAA8C,SAApBvC,EAASuC,OAEpG,OACErD,EAAAA,EAAAA,MAACsD,EAAAA,EAAI,CAAAvD,SAAA,EACHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,SAAC,yBACtCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAqBC,SAAEe,EAASyC,WAE/C1D,EAAAA,EAAAA,KAAA,OAAAE,UACEF,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,QAAQ,YACRC,QAASA,IAAMM,EAAS,cACxBhC,MAAM,qCAAoCqB,SAC3C,2BAMJwB,IACC1B,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,KAAK,QACLxE,QAAS4C,EACTiC,QAASA,IAAMhC,EAAS,IACxB1B,UAAU,SAIb2B,IACC5B,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,KAAK,UACLxE,QAAS8C,EACT3B,UAAU,SAIbsD,IACCvD,EAAAA,EAAAA,KAACqD,EAAAA,EAAK,CACJC,KAAK,UACLxE,QAAQ,4EACRmB,UAAU,UAIdE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sBAAqBC,SAAC,oBACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,CAAC,sBACzBF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAEe,EAASG,YAAc,WAI1EmC,IACAvD,EAAAA,EAAAA,KAAA4D,EAAAA,SAAA,CAAA1D,UACEC,EAAAA,EAAAA,MAAA,QAAM0D,SApMSrB,UAGzB,GAFAsB,EAAEC,iBAEGjC,EAAWkC,OAKhB,IACEvC,GAAU,GACVE,EAAS,UAGce,EAAAA,EAAYuB,uBAAuBxD,EAAcqB,GAAxE,MAGMoC,QAA0BxB,EAAAA,EAAYI,sBAAsBrC,GAClEY,EAAc6C,EAAkBtB,KAAKG,oBAAsB,IAG3D,MAAMN,QAAyBC,EAAAA,EAAYC,YAAYlC,GACvDS,EAAYuB,EAAiBG,KAAK3B,UAElCY,EAAW,aAAaC,wBACxBC,EAAc,IAGdoC,YAAW,KACTtC,EAAW,GAAG,GACb,IACL,CAAE,MAAOmB,GAAW,IAADoB,EAAAC,EACjBtD,QAAQW,MAAM,0BAA2BsB,GACzCrB,GAAqB,QAAZyC,EAAApB,EAAIG,gBAAQ,IAAAiB,GAAM,QAANC,EAAZD,EAAcxB,YAAI,IAAAyB,OAAN,EAAZA,EAAoBvF,UAAW,0BAC1C,CAAC,QACC2C,GAAU,EACZ,MA/BEE,EAAS,gCA+BX,EAiK8C1B,UAAU,sBAAqBC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACrBF,EAAAA,EAAAA,KAACsE,EAAAA,EAAK,CACJ7D,GAAG,QACHiD,KAAK,QACLJ,KAAK,QACLiB,YAAY,sBACZC,MAAO1C,EACP2C,SAAWX,GAAM/B,EAAc+B,EAAEpE,OAAO8E,OACxCE,SAAUlD,OAGdxB,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLiD,KAAK,SACLoB,SAAUlD,IAAWM,EAAWkC,OAAO9D,SACxC,SAGDF,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLiD,KAAK,SACLhD,QAAQ,YACRC,QAASA,IAAM4B,GAAiB,GAChCuC,SAAUlD,EAAOtB,SAClB,sBAQTC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2BAA0BC,SAAC,eAElB,IAAtBkB,EAAWuD,QACV3E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0CAAyCC,UACtDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAqBC,SAAC,+BAGrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,eAAcC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,SAAAE,UACEC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,UACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,YACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,WACFqD,IAAkBvD,EAAAA,EAAAA,KAAA,MAAAE,SAAI,cALlB,iBAQVF,EAAAA,EAAAA,KAAA,SAAAE,SACGkB,EAAWwD,KAAKC,IACf1E,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAAE,SAAK2E,EAAUC,SACf9E,EAAAA,EAAAA,KAAA,MAAAE,SACG2E,EAAUE,aAAgD,kBAA1BF,EAAUE,YACzC,GAAGF,EAAUE,YAAYC,WAAa,MAAMH,EAAUE,YAAYE,UAAY,KAAKjB,OACnF,OAGJhE,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAAA,QAAMC,UAAW,8BACM,YAArB4E,EAAUrB,OAAuB,cACZ,SAArBqB,EAAUrB,OAAoB,eACT,cAArBqB,EAAUrB,OAAyB,cACd,WAArBqB,EAAUrB,OAAsB,gBACX,YAArBqB,EAAUrB,OAAuB,gBACZ,YAArBqB,EAAUrB,QACW,eAArBqB,EAAUrB,OADuB,aAEjC,eACCtD,SACA2E,EAAUrB,OAAO0B,OAAO,GAAGC,cAAgBN,EAAUrB,OAAO4B,MAAM,QAGvEpF,EAAAA,EAAAA,KAAA,MAAAE,SACG,IAAImF,KAAKR,EAAUS,WAAWC,mBAAmB,QAAS,CACzDC,KAAM,UACNC,MAAO,QACPC,IAAK,eAGPnC,IACAvD,EAAAA,EAAAA,KAAA,MAAAE,UACEF,EAAAA,EAAAA,KAACK,EAAAA,EAAM,CACLC,QAAQ,SACRqF,KAAK,KACLpF,QAASA,KACP8B,EAAqBwC,EAAUe,KAC/BrD,GAAmB,EAAK,EAE1BmC,SAAUlD,EAAOtB,SAClB,eAvCE2E,EAAUe,qBAsDjC5F,EAAAA,EAAAA,KAAC6F,EAAAA,EAAY,CACXjH,OAAQsD,EACRrD,MAAM,0BACNC,SACEqB,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,OAAMC,SAAC,sEACpBF,EAAAA,EAAAA,KAAA,YACEC,UAAU,2EACVuE,MAAOxC,EACPyC,SAAWX,GAAM7B,EAAc6B,EAAEpE,OAAO8E,OACxCD,YAAY,kDAIlBxF,YAAayC,EAAS,YAAc,iBACpCvC,UAtR0BuD,UAC9B,GAAKR,EAAWgC,OAKhB,IACEvC,GAAU,GACVE,EAAS,IAGT,MAAMmE,EAAS9D,EACZ+D,MAAM,YACNnB,KAAIE,GAASA,EAAMd,SACnBgC,QAAOlB,GAASA,EAAMH,OAAS,IAG5BxB,QAAiBT,EAAAA,EAAYuD,4BAA4BxF,EAAcqF,GAGvE5B,QAA0BxB,EAAAA,EAAYI,sBAAsBrC,GAClEY,EAAc6C,EAAkBtB,KAAKG,oBAAsB,IAG3D,MAAMN,QAAyBC,EAAAA,EAAYC,YAAYlC,GACvDS,EAAYuB,EAAiBG,KAAK3B,UAElCY,EAAW,GAAGsB,EAASP,KAAKsD,MAAMvB,wCAClC1C,EAAc,IACdE,GAAiB,GAGjBgC,YAAW,KACTtC,EAAW,GAAG,GACb,IACL,CAAE,MAAOmB,GAAW,IAADmD,EAAAC,EACjBrF,QAAQW,MAAM,gCAAiCsB,GAC/CrB,GAAqB,QAAZwE,EAAAnD,EAAIG,gBAAQ,IAAAgD,GAAM,QAANC,EAAZD,EAAcvD,YAAI,IAAAwD,OAAN,EAAZA,EAAoBtH,UAAW,2BAC1C,CAAC,QACC2C,GAAU,EACZ,MAtCEE,EAAS,+BAsCX,EA+OIzC,SAAUA,KACRiD,GAAiB,GACjBF,EAAc,GAAG,KAKrBjC,EAAAA,EAAAA,KAAC6F,EAAAA,EAAY,CACXjH,OAAQ0D,EACRzD,MAAM,mBACNC,QAAQ,oEACRC,YAAayC,EAAS,cAAgB,SACtCvC,UAvPwBuD,UAC5B,GAAKJ,EAEL,IACEX,GAAU,GACVE,EAAS,UAGHe,EAAAA,EAAY2D,4BAA4B5F,EAAc2B,GAG5Df,EAAcD,EAAW4E,QAAOM,GAAKA,EAAEV,MAAQxD,KAG/C,MAAMK,QAAyBC,EAAAA,EAAYC,YAAYlC,GACvDS,EAAYuB,EAAiBG,KAAK3B,UAElCY,EAAW,kCAGXsC,YAAW,KACTtC,EAAW,GAAG,GACb,IACL,CAAE,MAAOmB,GAAW,IAADuD,EAAAC,EACjBzF,QAAQW,MAAM,4BAA6BsB,GAC3CrB,GAAqB,QAAZ4E,EAAAvD,EAAIG,gBAAQ,IAAAoD,GAAM,QAANC,EAAZD,EAAc3D,YAAI,IAAA4D,OAAN,EAAZA,EAAoB1H,UAAW,6BAC1C,CAAC,QACC2C,GAAU,GACVc,GAAmB,GACnBF,EAAqB,KACvB,GA0NInD,SAAUA,KACRqD,GAAmB,GACnBF,EAAqB,KAAK,MAGzB,C", "sources": ["components/ConfirmModal.tsx", "pages/campaigns/CampaignRecipients.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useRef,\n} from 'react';\n\nimport Button from './Button';\n\ninterface ConfirmModalProps {\n  isOpen: boolean;\n  title: string;\n  message: React.ReactNode;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm: () => void;\n  onCancel: () => void;\n}\n\nconst ConfirmModal: React.FC<ConfirmModalProps> = ({\n  isOpen,\n  title,\n  message,\n  confirmText = 'Confirm',\n  cancelText = 'Cancel',\n  onConfirm,\n  onCancel,\n}) => {\n  const modalRef = useRef<HTMLDivElement>(null);\n\n  // Close modal when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {\n        onCancel();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isOpen, onCancel]);\n\n  // Handle escape key press\n  useEffect(() => {\n    const handleEscKey = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onCancel();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscKey);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscKey);\n    };\n  }, [isOpen, onCancel]);\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\">\n      <div\n        ref={modalRef}\n        className=\"bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6 w-full max-w-md mx-4\"\n      >\n        <h3 className=\"text-xl font-semibold mb-4 text-white\">{title}</h3>\n        <div className=\"mb-6 text-gray-300\">{message}</div>\n\n        <div className=\"flex justify-end space-x-3\">\n          <Button\n            variant=\"secondary\"\n            onClick={onCancel}\n          >\n            {cancelText}\n          </Button>\n          <Button\n            variant=\"danger\"\n            onClick={onConfirm}\n          >\n            {confirmText}\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ConfirmModal;\n", "import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport ConfirmModal from 'components/ConfirmModal';\nimport Input from 'components/Input';\nimport { useAuth } from 'contexts/AuthContext';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\nimport { campaignAPI } from 'services/api';\n\nconst CampaignRecipients: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // Redirect to campaigns list if id is undefined\n  useEffect(() => {\n    if (!id || id === 'undefined') {\n      console.log('Invalid campaign ID, redirecting to campaigns list');\n      navigate('/campaigns');\n      return;\n    }\n    console.log('Campaign ID in CampaignRecipients:', id);\n  }, [id, navigate]);\n\n  const [campaign, setCampaign] = useState<any>(null);\n  const [recipients, setRecipients] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [emailInput, setEmailInput] = useState('');\n  const [bulkEmails, setBulkEmails] = useState('');\n  const [showBulkModal, setShowBulkModal] = useState(false);\n  const [recipientToRemove, setRecipientToRemove] = useState<string | null>(null);\n  const [showRemoveModal, setShowRemoveModal] = useState(false);\n\n  // Fetch campaign and recipients on component mount\n  useEffect(() => {\n    const fetchCampaignAndRecipients = async () => {\n      if (!id || id === 'undefined') return;\n\n      try {\n        // Get campaign from API\n        const campaignResponse = await campaignAPI.getCampaign(id);\n        setCampaign(campaignResponse.data.campaign);\n\n        // Get recipients from API\n        const recipientsResponse = await campaignAPI.getCampaignRecipients(id);\n        setRecipients(recipientsResponse.data.campaignRecipients || []);\n        \n        setLoading(false);\n      } catch (err: any) {\n        console.error('Error fetching campaign or recipients:', err);\n        setError(err.response?.data?.message || 'Failed to fetch data');\n        setLoading(false);\n      }\n    };\n\n    fetchCampaignAndRecipients();\n  }, [id]);\n\n  // Add a single recipient\n  const handleAddRecipient = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!emailInput.trim()) {\n      setError('Please enter an email address');\n      return;\n    }\n\n    try {\n      setSaving(true);\n      setError('');\n\n      // Call API to add recipient\n      const response = await campaignAPI.addRecipientToCampaign(id as string, emailInput);\n      \n      // Update recipients list\n      const updatedRecipients = await campaignAPI.getCampaignRecipients(id as string);\n      setRecipients(updatedRecipients.data.campaignRecipients || []);\n      \n      // Update campaign\n      const campaignResponse = await campaignAPI.getCampaign(id as string);\n      setCampaign(campaignResponse.data.campaign);\n      \n      setSuccess(`Recipient ${emailInput} added successfully`);\n      setEmailInput('');\n      \n      // Clear success message after a few seconds\n      setTimeout(() => {\n        setSuccess('');\n      }, 3000);\n    } catch (err: any) {\n      console.error('Error adding recipient:', err);\n      setError(err.response?.data?.message || 'Failed to add recipient');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Add bulk recipients\n  const handleAddBulkRecipients = async () => {\n    if (!bulkEmails.trim()) {\n      setError('Please enter email addresses');\n      return;\n    }\n\n    try {\n      setSaving(true);\n      setError('');\n\n      // Parse emails (split by commas, newlines, or spaces)\n      const emails = bulkEmails\n        .split(/[,\\n\\s]+/)\n        .map(email => email.trim())\n        .filter(email => email.length > 0);\n\n      // Call API to add recipients in bulk\n      const response = await campaignAPI.addBulkRecipientsToCampaign(id as string, emails);\n      \n      // Update recipients list\n      const updatedRecipients = await campaignAPI.getCampaignRecipients(id as string);\n      setRecipients(updatedRecipients.data.campaignRecipients || []);\n      \n      // Update campaign\n      const campaignResponse = await campaignAPI.getCampaign(id as string);\n      setCampaign(campaignResponse.data.campaign);\n      \n      setSuccess(`${response.data.added.length} recipients added successfully`);\n      setBulkEmails('');\n      setShowBulkModal(false);\n      \n      // Clear success message after a few seconds\n      setTimeout(() => {\n        setSuccess('');\n      }, 3000);\n    } catch (err: any) {\n      console.error('Error adding bulk recipients:', err);\n      setError(err.response?.data?.message || 'Failed to add recipients');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Remove a recipient\n  const handleRemoveRecipient = async () => {\n    if (!recipientToRemove) return;\n\n    try {\n      setSaving(true);\n      setError('');\n\n      // Call API to remove recipient\n      await campaignAPI.removeRecipientFromCampaign(id as string, recipientToRemove);\n      \n      // Update recipients list\n      setRecipients(recipients.filter(r => r._id !== recipientToRemove));\n      \n      // Update campaign\n      const campaignResponse = await campaignAPI.getCampaign(id as string);\n      setCampaign(campaignResponse.data.campaign);\n      \n      setSuccess('Recipient removed successfully');\n      \n      // Clear success message after a few seconds\n      setTimeout(() => {\n        setSuccess('');\n      }, 3000);\n    } catch (err: any) {\n      console.error('Error removing recipient:', err);\n      setError(err.response?.data?.message || 'Failed to remove recipient');\n    } finally {\n      setSaving(false);\n      setShowRemoveModal(false);\n      setRecipientToRemove(null);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center py-8\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n      </div>\n    );\n  }\n\n  if (!campaign) {\n    return (\n      <div>\n        <Alert\n          type=\"error\"\n          message=\"Campaign not found\"\n          className=\"mb-6\"\n        />\n        <Button onClick={() => navigate('/campaigns')}>\n          Back to Campaigns\n        </Button>\n      </div>\n    );\n  }\n\n  // Check if campaign is already sent\n  const isCampaignSent = campaign.status === 'sending' || campaign.status === 'completed' || campaign.status === 'sent';\n\n  return (\n    <Card>\n      <div className=\"mb-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h2 className=\"text-xl font-semibold\">Campaign Recipients</h2>\n            <p className=\"text-text-secondary\">{campaign.name}</p>\n          </div>\n          <div> \n            <Button\n              variant=\"secondary\" \n              onClick={() => navigate('/campaigns')}\n              title=\"Go back to the campaigns dashboard\"\n            >\n              Back to Dashboard\n            </Button>\n          </div>\n        </div>\n\n        {error && (\n          <Alert\n            type=\"error\"\n            message={error}\n            onClose={() => setError('')}\n            className=\"mb-4\"\n          />\n        )}\n\n        {success && (\n          <Alert\n            type=\"success\"\n            message={success}\n            className=\"mb-4\"\n          />\n        )}\n\n        {isCampaignSent && (\n          <Alert\n            type=\"warning\"\n            message=\"This campaign has already been sent. You cannot add or remove recipients.\"\n            className=\"mb-4\"\n          />\n        )}\n\n        <div className=\"mb-6\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <h3 className=\"text-lg font-medium\">Add Recipients</h3>\n            <div className=\"text-sm text-text-secondary\">\n              Total Recipients: <span className=\"font-medium\">{campaign.recipients || 0}</span>\n            </div>\n          </div>\n\n          {!isCampaignSent && (\n            <>\n              <form onSubmit={handleAddRecipient} className=\"flex space-x-2 mb-4\">\n                <div className=\"flex-1\">\n                  <Input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    placeholder=\"Enter email address\"\n                    value={emailInput}\n                    onChange={(e) => setEmailInput(e.target.value)}\n                    disabled={saving}\n                  />\n                </div>\n                <Button\n                  type=\"submit\"\n                  disabled={saving || !emailInput.trim()}\n                >\n                  Add\n                </Button>\n                <Button\n                  type=\"button\"\n                  variant=\"secondary\"\n                  onClick={() => setShowBulkModal(true)}\n                  disabled={saving}\n                >\n                  Bulk Add\n                </Button>\n              </form>\n            </>\n          )}\n        </div>\n\n        <div>\n          <h3 className=\"text-lg font-medium mb-2\">Recipients</h3>\n          \n          {recipients.length === 0 ? (\n            <div className=\"text-center py-8 bg-gray-800 rounded-md\">\n              <p className=\"text-text-secondary\">No recipients added yet</p>\n            </div>\n          ) : (\n            <div className=\"table-container\">\n              <table className=\"table w-full\">\n                <thead>\n                  <tr key=\"header-row\">\n                    <th>Email</th>\n                    <th>Name</th>\n                    <th>Status</th>\n                    <th>Added</th>\n                    {!isCampaignSent && <th>Actions</th>}\n                  </tr>\n                </thead>\n                <tbody>\n                  {recipients.map((recipient) => (\n                    <tr key={recipient._id}>\n                      <td>{recipient.email}</td>\n                      <td>\n                        {recipient.recipientId && typeof recipient.recipientId === 'object' ? \n                          `${recipient.recipientId.firstName || ''} ${recipient.recipientId.lastName || ''}`.trim() : \n                          '-'\n                        }\n                      </td>\n                      <td>\n                        <span className={`px-2 py-1 text-xs rounded ${\n                          recipient.status === 'pending' ? 'bg-gray-700' :\n                          recipient.status === 'sent' ? 'bg-green-800' :\n                          recipient.status === 'delivered' ? 'bg-blue-800' :\n                          recipient.status === 'opened' ? 'bg-purple-800' :\n                          recipient.status === 'clicked' ? 'bg-yellow-800' :\n                          recipient.status === 'bounced' ? 'bg-red-800' :\n                          recipient.status === 'complained' ? 'bg-red-800' :\n                          'bg-gray-700'\n                        }`}>\n                          {recipient.status.charAt(0).toUpperCase() + recipient.status.slice(1)}\n                        </span>\n                      </td>\n                      <td>\n                        {new Date(recipient.createdAt).toLocaleDateString('en-US', {\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })}\n                      </td>\n                      {!isCampaignSent && (\n                        <td>\n                          <Button\n                            variant=\"danger\"\n                            size=\"sm\"\n                            onClick={() => {\n                              setRecipientToRemove(recipient._id);\n                              setShowRemoveModal(true);\n                            }}\n                            disabled={saving}\n                          >\n                            Remove\n                          </Button>\n                        </td>\n                      )}\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Bulk Add Modal */}\n      <ConfirmModal\n        isOpen={showBulkModal}\n        title=\"Add Multiple Recipients\"\n        message={\n          <div>\n            <p className=\"mb-2\">Enter email addresses separated by commas, spaces, or new lines:</p>\n            <textarea\n              className=\"w-full h-40 p-2 bg-gray-800 border border-gray-700 rounded-md text-white\"\n              value={bulkEmails}\n              onChange={(e) => setBulkEmails(e.target.value)}\n              placeholder=\"<EMAIL>, <EMAIL>\"\n            />\n          </div>\n        }\n        confirmText={saving ? \"Adding...\" : \"Add Recipients\"}\n        onConfirm={handleAddBulkRecipients}\n        onCancel={() => {\n          setShowBulkModal(false);\n          setBulkEmails('');\n        }}\n      />\n\n      {/* Remove Recipient Modal */}\n      <ConfirmModal\n        isOpen={showRemoveModal}\n        title=\"Remove Recipient\"\n        message=\"Are you sure you want to remove this recipient from the campaign?\"\n        confirmText={saving ? \"Removing...\" : \"Remove\"}\n        onConfirm={handleRemoveRecipient}\n        onCancel={() => {\n          setShowRemoveModal(false);\n          setRecipientToRemove(null);\n        }}\n      />\n    </Card>\n  );\n};\n\nexport default CampaignRecipients;\n"], "names": ["_ref", "isOpen", "title", "message", "confirmText", "cancelText", "onConfirm", "onCancel", "modalRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleEscKey", "key", "_jsx", "className", "children", "_jsxs", "ref", "<PERSON><PERSON>", "variant", "onClick", "CampaignRecipients", "id", "useParams", "user", "useAuth", "navigate", "useNavigate", "console", "log", "campaign", "setCampaign", "useState", "recipients", "setRecipients", "loading", "setLoading", "saving", "setSaving", "error", "setError", "success", "setSuccess", "emailInput", "setEmailInput", "bulkEmails", "setBulkEmails", "showBulkModal", "setShowBulkModal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setRecipientToRemove", "showRemoveModal", "setShowRemoveModal", "async", "campaignResponse", "campaignAPI", "getCampaign", "data", "recipientsResponse", "getCampaignRecipients", "campaignRecipients", "err", "_err$response", "_err$response$data", "response", "fetchCampaignAndRecipients", "<PERSON><PERSON>", "type", "isCampaignSent", "status", "Card", "name", "onClose", "_Fragment", "onSubmit", "e", "preventDefault", "trim", "addRecipientToCampaign", "updatedRecipients", "setTimeout", "_err$response2", "_err$response2$data", "Input", "placeholder", "value", "onChange", "disabled", "length", "map", "recipient", "email", "recipientId", "firstName", "lastName", "char<PERSON>t", "toUpperCase", "slice", "Date", "createdAt", "toLocaleDateString", "year", "month", "day", "size", "_id", "ConfirmModal", "emails", "split", "filter", "addBulkRecipientsToCampaign", "added", "_err$response3", "_err$response3$data", "removeRecipientFromCampaign", "r", "_err$response4", "_err$response4$data"], "sourceRoot": ""}