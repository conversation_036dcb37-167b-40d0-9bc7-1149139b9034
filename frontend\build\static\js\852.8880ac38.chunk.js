"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[852],{4852:(e,s,a)=>{a.r(s),a.d(s,{default:()=>m});var n=a(5043),l=a(9291),i=a(8417),r=a(1411),t=a(4741),d=a(9066),o=a(579);const c=e=>{if(!e)return"N/A";try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(s){return console.error("Error formatting date:",s),"Invalid Date"}},m=()=>{var e;const{user:s}=(0,d.A)(),[a,m]=(0,n.useState)({name:(null===s||void 0===s?void 0:s.name)||"<PERSON>",email:(null===s||void 0===s?void 0:s.email)||"<EMAIL>",companyName:(null===s||void 0===s?void 0:s.companyName)||"Acme Inc.",timezone:(null===s||void 0===s?void 0:s.timezone)||"UTC-5"}),[u,x]=(0,n.useState)({physicalAddress:"123 Main St, Suite 100, New York, NY 10001",unsubscribeText:"If you no longer wish to receive these emails, you can unsubscribe here."}),[h,p]=(0,n.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[v,j]=(0,n.useState)(!1),[C,T]=(0,n.useState)(!1),[N,w]=(0,n.useState)(!1),[b,g]=(0,n.useState)("");return(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{className:"lg:col-span-2",children:[(0,o.jsxs)(r.A,{title:"General Settings",children:[v&&(0,o.jsx)(l.A,{type:"success",message:"General settings updated successfully!",className:"mb-4"}),(0,o.jsxs)("form",{onSubmit:e=>{e.preventDefault(),setTimeout((()=>{j(!0),setTimeout((()=>j(!1)),3e3)}),500)},children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,o.jsx)(t.A,{id:"name",name:"name",label:"Full Name",value:a.name,onChange:e=>m({...a,name:e.target.value}),required:!0}),(0,o.jsx)(t.A,{id:"email",name:"email",type:"email",label:"Email Address",value:a.email,onChange:e=>m({...a,email:e.target.value}),required:!0})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,o.jsx)(t.A,{id:"companyName",name:"companyName",label:"Company Name",value:a.companyName,onChange:e=>m({...a,companyName:e.target.value})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"timezone",className:"form-label",children:"Timezone"}),(0,o.jsxs)("select",{id:"timezone",name:"timezone",value:a.timezone,onChange:e=>m({...a,timezone:e.target.value}),className:"form-input w-full",children:[(0,o.jsx)("option",{value:"UTC-12",children:"UTC-12"}),(0,o.jsx)("option",{value:"UTC-11",children:"UTC-11"}),(0,o.jsx)("option",{value:"UTC-10",children:"UTC-10"}),(0,o.jsx)("option",{value:"UTC-9",children:"UTC-9"}),(0,o.jsx)("option",{value:"UTC-8",children:"UTC-8 (Pacific)"}),(0,o.jsx)("option",{value:"UTC-7",children:"UTC-7 (Mountain)"}),(0,o.jsx)("option",{value:"UTC-6",children:"UTC-6 (Central)"}),(0,o.jsx)("option",{value:"UTC-5",children:"UTC-5 (Eastern)"}),(0,o.jsx)("option",{value:"UTC-4",children:"UTC-4"}),(0,o.jsx)("option",{value:"UTC-3",children:"UTC-3"}),(0,o.jsx)("option",{value:"UTC-2",children:"UTC-2"}),(0,o.jsx)("option",{value:"UTC-1",children:"UTC-1"}),(0,o.jsx)("option",{value:"UTC+0",children:"UTC+0"}),(0,o.jsx)("option",{value:"UTC+1",children:"UTC+1"}),(0,o.jsx)("option",{value:"UTC+2",children:"UTC+2"}),(0,o.jsx)("option",{value:"UTC+3",children:"UTC+3"}),(0,o.jsx)("option",{value:"UTC+4",children:"UTC+4"}),(0,o.jsx)("option",{value:"UTC+5",children:"UTC+5"}),(0,o.jsx)("option",{value:"UTC+6",children:"UTC+6"}),(0,o.jsx)("option",{value:"UTC+7",children:"UTC+7"}),(0,o.jsx)("option",{value:"UTC+8",children:"UTC+8"}),(0,o.jsx)("option",{value:"UTC+9",children:"UTC+9"}),(0,o.jsx)("option",{value:"UTC+10",children:"UTC+10"}),(0,o.jsx)("option",{value:"UTC+11",children:"UTC+11"}),(0,o.jsx)("option",{value:"UTC+12",children:"UTC+12"})]})]})]}),(0,o.jsx)(i.A,{type:"submit",children:"Save General Settings"})]})]}),(0,o.jsxs)(r.A,{title:"CAN-SPAM Compliance",className:"mt-6",children:[C&&(0,o.jsx)(l.A,{type:"success",message:"CAN-SPAM settings updated successfully!",className:"mb-4"}),(0,o.jsx)("p",{className:"text-text-secondary mb-4",children:"The CAN-SPAM Act requires that all commercial emails include your physical address and an unsubscribe option."}),(0,o.jsxs)("form",{onSubmit:e=>{e.preventDefault(),setTimeout((()=>{T(!0),setTimeout((()=>T(!1)),3e3)}),500)},children:[(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsxs)("label",{htmlFor:"physicalAddress",className:"form-label",children:["Physical Address ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("textarea",{id:"physicalAddress",name:"physicalAddress",value:u.physicalAddress,onChange:e=>x({...u,physicalAddress:e.target.value}),required:!0,rows:3,className:"form-input w-full"})]}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsxs)("label",{htmlFor:"unsubscribeText",className:"form-label",children:["Unsubscribe Text ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("textarea",{id:"unsubscribeText",name:"unsubscribeText",value:u.unsubscribeText,onChange:e=>x({...u,unsubscribeText:e.target.value}),required:!0,rows:2,className:"form-input w-full"})]}),(0,o.jsx)(i.A,{type:"submit",children:"Save CAN-SPAM Settings"})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)(r.A,{title:"Change Password",children:[N&&(0,o.jsx)(l.A,{type:"success",message:"Password updated successfully!",className:"mb-4"}),b&&(0,o.jsx)(l.A,{type:"error",message:b,onClose:()=>g(""),className:"mb-4"}),(0,o.jsxs)("form",{onSubmit:e=>{e.preventDefault(),h.currentPassword&&h.newPassword&&h.confirmPassword?h.newPassword===h.confirmPassword?h.newPassword.length<8?g("New password must be at least 8 characters long"):setTimeout((()=>{w(!0),p({currentPassword:"",newPassword:"",confirmPassword:""}),g(""),setTimeout((()=>w(!1)),3e3)}),500):g("New passwords do not match"):g("All password fields are required")},children:[(0,o.jsx)(t.A,{id:"currentPassword",name:"currentPassword",type:"password",label:"Current Password",value:h.currentPassword,onChange:e=>p({...h,currentPassword:e.target.value}),required:!0,className:"mb-4"}),(0,o.jsx)(t.A,{id:"newPassword",name:"newPassword",type:"password",label:"New Password",value:h.newPassword,onChange:e=>p({...h,newPassword:e.target.value}),required:!0,className:"mb-4"}),(0,o.jsx)(t.A,{id:"confirmPassword",name:"confirmPassword",type:"password",label:"Confirm New Password",value:h.confirmPassword,onChange:e=>p({...h,confirmPassword:e.target.value}),required:!0,className:"mb-6"}),(0,o.jsx)(i.A,{type:"submit",children:"Update Password"})]})]}),(0,o.jsxs)(r.A,{title:"Account Information",className:"mt-6",children:[(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("div",{className:"text-text-secondary",children:"Account Type"}),(0,o.jsx)("div",{className:"font-medium capitalize",children:(null===s||void 0===s?void 0:s.accountType)||"N/A"})]}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("div",{className:"text-text-secondary",children:"Member Since"}),(0,o.jsx)("div",{className:"font-medium",children:c(null===s||void 0===s?void 0:s.createdAt)})]}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("div",{className:"text-text-secondary",children:"Domain Status"}),(0,o.jsx)("div",{className:"font-medium",children:null!==s&&void 0!==s&&null!==(e=s.domain)&&void 0!==e&&e.status?(0,o.jsx)("span",{className:"px-2 py-1 text-xs rounded capitalize "+("active"===s.domain.status?"bg-green-800":"pending"===s.domain.status?"bg-yellow-800":"bg-red-800"),children:s.domain.status}):(0,o.jsx)("span",{className:"px-2 py-1 text-xs rounded bg-gray-700",children:"Not Setup"})})]}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("div",{className:"text-text-secondary",children:"Flows Purchased"}),(0,o.jsx)("div",{className:"font-medium",children:"N/A"})]}),(0,o.jsxs)("div",{className:"mb-4",children:[(0,o.jsx)("div",{className:"text-text-secondary",children:"Flows Remaining"}),(0,o.jsx)("div",{className:"font-medium",children:"N/A"})]})]})]})]})})}}}]);
//# sourceMappingURL=852.8880ac38.chunk.js.map