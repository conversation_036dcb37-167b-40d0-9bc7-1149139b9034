"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[379],{924:(e,t,l)=>{l.d(t,{A:()=>m});l(9884);var o=l(5043),a=l(722),n=l(5897),s=l.n(n),r=l(579);const i=(0,o.forwardRef)(((e,t)=>{let{initialMjml:l="",initialHtml:n="",onSave:i,height:m="70vh"}=e;const c=(0,o.useRef)(null),d=(0,o.useRef)(null);return(0,o.useEffect)((()=>{if(c.current){d.current&&(console.log("[MjmlEditor] Cleaning up previous editor instance"),d.current.destroy(),d.current=null),console.log("[MjmlEditor] Initializing editor with props:",{hasMjml:!!l,mjmlLength:(null===l||void 0===l?void 0:l.length)||0,hasHtml:!!n,htmlLength:(null===n||void 0===n?void 0:n.length)||0});try{const e=a.Ay.init({container:c.current,fromElement:!1,height:String(m),width:"auto",storageManager:!1,plugins:[s()],pluginsOpts:{"grapesjs-mjml":{useXmlParser:!0,resetBlocks:!1}}});if(!e)return void console.error("[MjmlEditor] Failed to initialize editor");let t;d.current=e,e.Commands.has("mjml-get-code")||(console.log("[MjmlEditor] Registering missing mjml-get-code command"),e.Commands.add("mjml-get-code",{run:e=>{const t=e.getHtml();return{mjml:t,html:t}}})),e.Commands.has("gjs-get-html")||(console.log("[MjmlEditor] Registering missing gjs-get-html command"),e.Commands.add("gjs-get-html",{run:e=>e.getHtml({component:e.getWrapper()})})),setTimeout((()=>{if(d.current)if(d.current.setComponents)try{if(l){console.log("[MjmlEditor] Loading initial MJML:",l.substring(0,100)+"...");try{d.current.setComponents(l),console.log("[MjmlEditor] Successfully loaded MJML content")}catch(e){console.error("[MjmlEditor] Error loading initial MJML:",e),n&&(console.log("[MjmlEditor] Falling back to loading initial HTML"),d.current.setComponents(n),console.log("[MjmlEditor] Successfully loaded HTML as fallback"))}}else n?(console.log("[MjmlEditor] Loading initial HTML (MJML not provided):",n.substring(0,100)+"..."),d.current.setComponents(n),console.log("[MjmlEditor] Successfully loaded HTML content")):(console.log("[MjmlEditor] No content provided, loading default template"),d.current.setComponents("\n                <mjml>\n                  <mj-body>\n                    <mj-section>\n                      <mj-column>\n                        <mj-text>Start designing your email!</mj-text>\n                      </mj-column>\n                    </mj-section>\n                  </mj-body>\n                </mjml>\n              "))}catch(t){console.error("[MjmlEditor] Error in content loading phase:",t)}else console.error("[MjmlEditor] Editor's setComponents method is not available");else console.error("[MjmlEditor] Editor instance not available after timeout")}),100);let o=!1;return e.on("change:changesCount",(()=>{i&&e&&!o&&(t&&clearTimeout(t),t=setTimeout((()=>{try{o=!0;let a="",n="";try{const t=e.runCommand("mjml-get-code");t&&"object"===typeof t&&(a=t.mjml||"",n=t.html||"",console.log("[MjmlEditor] Got code via 'mjml-get-code' command"))}catch(t){console.warn("'mjml-get-code' command failed, using fallback methods:",t)}if(!a&&!n){a=e.getHtml()||"";try{n=e.runCommand("gjs-get-html")||""}catch(l){console.warn("'gjs-get-html' command failed:",l)}n||(n=e.getHtml({component:e.getWrapper()})||""),console.log("[MjmlEditor] Using fallback getHtml()/gjs-get-html()")}if(!a.trim())return console.log("[MjmlEditor] No MJML content to save, skipping save"),void(o=!1);console.log("[MjmlEditor] Attempting to call onSave..."),i(a,n),console.log("[MjmlEditor] onSave callback executed.")}catch(a){console.error("Error during editor change listener:",a)}finally{o=!1}}),500))})),()=>{if(t&&clearTimeout(t),d.current){try{d.current.destroy()}catch(e){console.error("[MjmlEditor] Error during editor cleanup:",e)}d.current=null}}}catch(e){console.error("[MjmlEditor] Critical error during editor initialization:",e)}}}),[l,n,m,i]),(0,o.useImperativeHandle)(t,(()=>({save:async()=>{let e={mjml:"",html:""};if(d.current)try{const t=d.current;if(!t.Commands.has("mjml-get-code"))throw new Error("mjml-get-code command not available");{const l=t.runCommand("mjml-get-code");if(!(l&&"object"===typeof l&&"mjml"in l&&"html"in l))throw console.warn("'mjml-get-code' command did not return expected object, trying fallbacks."),new Error("Command returned unexpected structure");e.mjml=l.mjml||"",e.html=l.html||"",console.log("[MjmlEditor] Manual Save - MJML/HTML from command:",{mjml:e.mjml.substring(0,50)+"...",html:e.html.substring(0,50)+"..."})}}catch(t){console.warn("mjml-get-code command failed on manual save, using fallback:",t);try{const t=d.current,l=t.getHtml()||"";let o="";o=t.Commands.has("gjs-get-html")?t.runCommand("gjs-get-html")||"":t.getHtml({component:t.getWrapper()})||"",l||o?(e.mjml=l,e.html=o||l,console.log("[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.")):console.error("[MjmlEditor] Manual Save - Fallback methods also failed to get content.")}catch(l){console.error("[MjmlEditor] Manual Save - Error during fallback retrieval:",l)}}else console.error("[MjmlEditor] Manual Save - Editor not available.");if(await new Promise((e=>setTimeout(e,100))),d.current&&!e.html.trim()){console.log("[MjmlEditor] Manual Save - Re-fetching HTML after delay...");try{const t=d.current;let l="";t.Commands.has("gjs-get-html")&&(l=t.runCommand("gjs-get-html")),l||(l=t.getHtml({component:t.getWrapper()})||""),l.trim()?(console.log("[MjmlEditor] Manual Save - Found updated HTML after delay."),e.html=l):(console.log("[MjmlEditor] Manual Save - HTML still empty after delay."),e.mjml&&!e.html&&(e.html=e.mjml))}catch(o){console.error("[MjmlEditor] Manual Save - Error re-fetching HTML after delay:",o)}}return e},getEditor:()=>d.current}))),(0,r.jsx)("div",{ref:c,style:{height:m}})}));i.displayName="MjmlEditor";const m=i},8379:(e,t,l)=>{l.r(t),l.d(t,{default:()=>u});var o=l(5043),a=l(4665),n=l(9291),s=l(8417),r=l(4741),i=l(924),m=l(8231),c=l(9579),d=l(6291),g=l(579);const u=()=>{const e=(0,m.Zp)(),{templateId:t}=(0,m.g)(),l=Boolean(t),[u,h]=(0,o.useState)(""),[p,j]=(0,o.useState)(""),[f,v]=(0,o.useState)("Other"),[x,b]=(0,o.useState)(""),[y,M]=(0,o.useState)(""),[S,E]=(0,o.useState)(""),[w,C]=(0,o.useState)(!1),[N,T]=(0,o.useState)(null),[k,A]=(0,o.useState)(null),[H,L]=(0,o.useState)(1),[F,I]=(0,o.useState)(!1),[P,z]=(0,o.useState)("headline"),[R,U]=(0,o.useState)([]),[J,B]=(0,o.useState)(!1);(0,o.useEffect)((()=>{l&&t&&O(t)}),[t]);const O=async e=>{C(!0);try{const t=await c.jg.getTemplateById(e);if(t.success&&t.template){const e=t.template;h(e.name),j(e.description),v(e.category),b(e.content),E(e.thumbnail||""),e.mjmlContent&&M(e.mjmlContent)}else T(t.message||"Failed to fetch template")}catch(o){var t,l;T((null===(t=o.response)||void 0===t||null===(l=t.data)||void 0===l?void 0:l.message)||o.message||"Failed to fetch template")}finally{C(!1)}},D=async e=>{if(null===e||void 0===e||e.preventDefault(),1===H)return u&&p&&f?(S||E(`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(u)}`),void L(2)):void T("Please fill in all required fields");if(console.log("[Submit Step 2] Starting..."),x){C(!0),T(null),console.log("[Submit Step 2] Preparing API call...");try{const e={name:u,description:p,category:f,content:x,thumbnail:S||`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(u)}`,mjmlContent:y};let o;console.log("[Submit Step 2] Template data size:",JSON.stringify(e).length,"bytes"),l&&t?(console.log("[Submit Step 2] Updating template ID:",t),o=await c.jg.updateTemplate(t,e)):(console.log("[Submit Step 2] Creating new template"),o=await c.jg.createTemplate(e)),console.log("[Submit Step 2] API Response:",o),o.success?(console.log("[Submit Step 2] Success! Setting timeout for navigation."),A(l?"Template updated successfully":"Template created successfully"),setTimeout((()=>{console.log("[Submit Step 2] Navigating back to templates"),window.location.href="/email-templates"}),1500)):(console.error("[Submit Step 2] API call not successful:",o.message),T(o.message||(l?"Failed to update template":"Failed to create template")))}catch(n){if(console.error("[Submit Step 2] Error during API call:",n),n.response)if(404===n.response.status)T("API endpoint not found. Please check server configuration.");else if(413===n.response.status)T("Template content is too large. Try simplifying your design.");else if(400===n.response.status){var o;T((null===(o=n.response.data)||void 0===o?void 0:o.message)||"Invalid template data. Please check your inputs.")}else if(n.response.status>=500)T("Server error. Please try again later or contact support.");else{var a;T((null===(a=n.response.data)||void 0===a?void 0:a.message)||n.message||"An error occurred while saving the template.")}else n.request?T("No response from server. Check your internet connection and try again."):T(n.message||"An unexpected error occurred")}finally{console.log("[Submit Step 2] Setting loading to false."),C(!1)}}else T("Please create a template design before saving")},q=async(e,t)=>{try{B(!0),U([]);const l=await d.A.post("/ai-templates/suggest",{content:e,suggestionType:t},{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}});l.data.success?U(l.data.data.suggestions):console.error("Failed to get suggestions:",l.data.message)}catch(N){console.error("Error getting suggestions:",N)}finally{B(!1)}};return w&&1===H?(0,g.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,g.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-800"})}):(0,g.jsxs)("div",{className:"flex flex-col h-screen",children:[(0,g.jsxs)("div",{className:"flex justify-between items-center p-4 bg-gray-800 text-white border-b border-gray-700",children:[(0,g.jsx)("h1",{className:"text-xl font-semibold",children:l?`Editing: ${u}`:"Create New Template"}),(0,g.jsxs)("div",{className:"flex gap-2",children:[(0,g.jsx)(s.A,{type:"button",onClick:()=>{2===H?L(1):e("/email-templates")},variant:"secondary",size:"sm",children:1===H?"Back to Templates":"Back to Details"}),(0,g.jsx)(s.A,{type:"button",onClick:D,variant:"primary",size:"sm",disabled:w,children:w?"Saving...":1===H?"Next: Design":l?"Update & Close":"Save & Close"})]})]}),(0,g.jsx)("div",{className:"flex flex-grow overflow-hidden",children:1===H?(0,g.jsx)("div",{className:"flex-grow p-6 bg-gray-900 overflow-y-auto",children:(0,g.jsxs)("div",{className:"max-w-2xl mx-auto bg-gray-800 rounded-lg p-8",children:[N&&(0,g.jsx)(n.A,{type:"error",message:N,className:"mb-6"}),k&&(0,g.jsx)(n.A,{type:"success",message:k,className:"mb-6"}),(0,g.jsxs)("form",{onSubmit:D,children:[(0,g.jsx)("div",{className:"mb-6",children:(0,g.jsx)(r.A,{id:"templateName",name:"templateName",label:"Template Name",type:"text",value:u,onChange:e=>h(e.target.value),required:!0,placeholder:"Enter template name"})}),(0,g.jsxs)("div",{className:"mb-6",children:[(0,g.jsx)("label",{className:"block text-white mb-2",children:"Description"}),(0,g.jsx)("textarea",{value:p,onChange:e=>j(e.target.value),required:!0,placeholder:"Enter template description",className:"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800 resize-none h-32"})]}),(0,g.jsxs)("div",{className:"mb-6",children:[(0,g.jsx)("label",{className:"block text-white mb-2",children:"Category"}),(0,g.jsxs)("select",{value:f,onChange:e=>v(e.target.value),className:"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800",children:[(0,g.jsx)("option",{value:"Marketing",children:"Marketing"}),(0,g.jsx)("option",{value:"Transactional",children:"Transactional"}),(0,g.jsx)("option",{value:"Newsletter",children:"Newsletter"}),(0,g.jsx)("option",{value:"Event",children:"Event"}),(0,g.jsx)("option",{value:"Onboarding",children:"Onboarding"}),(0,g.jsx)("option",{value:"Follow-up",children:"Follow-up"}),(0,g.jsx)("option",{value:"Promotional",children:"Promotional"}),(0,g.jsx)("option",{value:"Other",children:"Other"})]})]}),(0,g.jsxs)("div",{className:"mb-6",children:[(0,g.jsx)(r.A,{id:"thumbnailUrl",name:"thumbnailUrl",label:"Thumbnail URL (optional)",type:"text",value:S,onChange:e=>E(e.target.value),placeholder:"Enter thumbnail URL or leave blank"}),S&&(0,g.jsx)("div",{className:"mt-2",children:(0,g.jsx)("img",{src:S,alt:"Thumbnail preview",className:"h-32 object-cover rounded-lg"})})]}),(0,g.jsx)("div",{className:"flex justify-end",children:(0,g.jsx)(s.A,{type:"submit",variant:"primary",size:"md",children:"Next: Design Template"})})]})]})}):(0,g.jsxs)("div",{className:"flex flex-grow h-full",children:[(0,g.jsxs)("div",{className:"w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto flex flex-col",children:[(0,g.jsx)("div",{className:"p-2 text-white text-center font-semibold border-b border-gray-700",children:"Template Options"}),(0,g.jsxs)("div",{className:"p-4 space-y-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:"AI Suggestions"}),(0,g.jsx)(s.A,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>{z("headline"),I(!0),q("Headline suggestion for email","headline")},children:"Get Headline Ideas"}),(0,g.jsx)(s.A,{variant:"secondary",size:"sm",className:"w-full mt-2",onClick:()=>{z("body"),I(!0),q("Body text suggestion for email","body")},children:"Get Body Text Ideas"}),(0,g.jsx)(s.A,{variant:"secondary",size:"sm",className:"w-full mt-2",onClick:()=>{z("cta"),I(!0),q("Call to action suggestion","cta")},children:"Get CTA Ideas"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("h3",{className:"text-sm font-medium text-gray-300 mb-2",children:"Help"}),(0,g.jsx)("p",{className:"text-xs text-gray-400",children:"Use the MJML editor to create responsive emails. Customize your template using the available blocks and settings."})]})]})]}),(0,g.jsxs)("div",{className:"flex-grow flex flex-col bg-gray-700",children:[(0,g.jsx)(i.A,{initialHtml:x,initialMjml:y,onSave:(e,t)=>{b(t),M(e)},height:"100%"}),F&&(0,g.jsx)("div",{className:"absolute right-80 top-40 w-80 z-50",children:(0,g.jsx)(a.A,{type:P,suggestions:R,isLoading:J,onApply:e=>{console.log("Selected suggestion:",e),alert("Applying AI suggestions directly into the MJML editor is not yet implemented."),I(!1)},onClose:()=>I(!1)})})]})]})})]})}}}]);
//# sourceMappingURL=379.7ef2defc.chunk.js.map