"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[552],{552:(e,t,s)=>{s.d(t,{Fc:()=>a.A,$n:()=>n.A,Zp:()=>l.A,pd:()=>r.A});var a=s(9291),n=s(8417),l=s(1411),r=(s(1830),s(2677),s(4741));s(3425),s(5043),s(9066),s(579);s(6517)},1830:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(5043),n=s(8417),l=s(579);const r=e=>{let{isOpen:t,title:s,message:r,confirmText:i="Confirm",cancelText:c="Cancel",onConfirm:d,onCancel:o}=e;const x=(0,a.useRef)(null);return(0,a.useEffect)((()=>{const e=e=>{x.current&&!x.current.contains(e.target)&&o()};return t&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[t,o]),(0,a.useEffect)((()=>{const e=e=>{"Escape"===e.key&&o()};return t&&document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[t,o]),t?(0,l.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:(0,l.jsxs)("div",{ref:x,className:"bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-6 w-full max-w-md mx-4",children:[(0,l.jsx)("h3",{className:"text-xl font-semibold mb-4 text-white",children:s}),(0,l.jsx)("div",{className:"mb-6 text-gray-300",children:r}),(0,l.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,l.jsx)(n.A,{variant:"secondary",onClick:o,children:c}),(0,l.jsx)(n.A,{variant:"danger",onClick:d,children:i})]})]})}):null}},2677:(e,t,s)=>{s.d(t,{A:()=>r});s(5043);var a=s(7304),n=s(6058),l=s(579);a.t1.register(a.PP,a.kc,a.FN,a.No,a.E8,a.Bs,a.hE,a.m_,a.s$);const r=e=>{let{type:t,data:s,options:a,height:r,width:i}=e;const c={responsive:!0,maintainAspectRatio:!0,...a};return(0,l.jsx)("div",{className:"chart-container",children:(()=>{switch(t){case"line":return(0,l.jsx)(n.N1,{data:s,options:c,height:r,width:i});case"bar":return(0,l.jsx)(n.yP,{data:s,options:c,height:r,width:i});case"pie":return(0,l.jsx)(n.Fq,{data:s,options:c,height:r,width:i});default:return(0,l.jsx)("p",{children:"Unsupported chart type"})}})()})}},6517:(e,t,s)=>{s.d(t,{A:()=>l});s(5043);var a=s(579);const n=e=>{let{name:t}=e;return(0,a.jsx)("i",{className:`placeholder-icon-${t} w-5 h-5`})},l=e=>{let{title:t,value:s,icon:l,change:r,className:i="",tooltip:c,details:d}=e;return(0,a.jsxs)("div",{className:`card container-futuristic flex flex-col ${i}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"stat-label",children:t}),l&&(0,a.jsx)("span",{className:"text-text-secondary opacity-80",children:(0,a.jsx)(n,{name:l})})]}),(0,a.jsx)("div",{className:"stat-value mb-1",children:s}),c&&(0,a.jsx)("div",{className:"text-xs text-text-secondary mt-1 opacity-90",children:c}),r&&(0,a.jsxs)("div",{className:"text-sm mt-2 flex items-center font-medium "+(r.isPositive?"text-growth-green":"text-danger"),children:[(0,a.jsx)("span",{className:"mr-1",children:r.isPositive?(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})}),r.value]}),d&&d.length>0&&(0,a.jsx)("div",{className:"mt-auto pt-3 border-t border-border mt-3",children:d.map(((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between text-xs py-1",children:[(0,a.jsx)("span",{className:"text-text-secondary opacity-90",children:e.label}),(0,a.jsx)("span",{className:"font-medium",children:e.value})]},t)))})]})}}}]);
//# sourceMappingURL=552.7576a392.chunk.js.map