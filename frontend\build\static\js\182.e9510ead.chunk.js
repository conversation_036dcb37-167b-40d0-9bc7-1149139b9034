"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[182],{6182:(e,s,t)=>{t.r(s),t.d(s,{default:()=>o});var a=t(5043),l=t(9291),i=t(8417),n=t(1411),r=t(9066),c=t(9579),d=t(579);const o=()=>{const{user:e}=(0,r.A)(),[s,t]=(0,a.useState)(!0),[o,x]=(0,a.useState)(""),[h,m]=(0,a.useState)([]),[u,j]=(0,a.useState)(!1),[y,p]=(0,a.useState)(""),[b,g]=(0,a.useState)(""),[f,w]=(0,a.useState)(!1),[N,v]=(0,a.useState)(1),[S,A]=(0,a.useState)(!1),[F,k]=(0,a.useState)(""),[P,C]=(0,a.useState)("");(0,a.useEffect)((()=>{(async()=>{t(!0),x("");try{const e=await c.p9.getHistory();m(e.data||[])}catch(e){console.error("Error fetching billing history:",e),x(e.message||"Failed to fetch billing history. Please try again."),m([])}finally{t(!1)}})()}),[]);const D=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(s){return console.error("Error formatting date:",e,s),"Invalid Date"}},U=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e);return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold",children:"Billing & Flow Management"}),(0,d.jsx)("p",{className:"text-text-secondary",children:"Manage your email flow purchases and billing information"})]}),(0,d.jsx)(i.A,{onClick:()=>w(!0),children:"Purchase Flows"})]}),F&&(0,d.jsx)(l.A,{type:"error",message:F,onClose:()=>k(""),className:"mb-6"}),P&&(0,d.jsx)(l.A,{type:"success",message:P,className:"mb-6"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6",children:[(0,d.jsx)(n.A,{children:(0,d.jsxs)("div",{className:"text-center p-4",children:[(0,d.jsx)("div",{className:"text-4xl font-bold text-primary mb-2",children:(null===e||void 0===e?void 0:e.flowsPurchased)||0}),(0,d.jsx)("div",{className:"text-text-secondary",children:"Available Flows"})]})}),(0,d.jsx)(n.A,{children:(0,d.jsxs)("div",{className:"text-center p-4",children:[(0,d.jsx)("div",{className:"text-4xl font-bold text-green-500 mb-2",children:"$10"}),(0,d.jsx)("div",{className:"text-text-secondary",children:"Per Flow"})]})}),(0,d.jsx)(n.A,{children:(0,d.jsxs)("div",{className:"text-center p-4",children:[(0,d.jsx)("div",{className:"text-4xl font-bold text-blue-500 mb-2",children:"1,000"}),(0,d.jsx)("div",{className:"text-text-secondary",children:"Recipients Per Flow"})]})})]}),(0,d.jsx)(n.A,{title:"Flow Pricing Information",children:(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"What's included in each flow?"}),(0,d.jsxs)("ul",{className:"list-disc list-inside text-text-secondary mb-4",children:[(0,d.jsx)("li",{children:"Send up to 10 emails in a campaign flow"}),(0,d.jsx)("li",{children:"Reach up to 1,000 recipients per flow"}),(0,d.jsx)("li",{children:"Full access to email templates and editor"}),(0,d.jsx)("li",{children:"Comprehensive analytics and tracking"}),(0,d.jsx)("li",{children:"Automated email scheduling"})]}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Pricing"}),(0,d.jsx)("p",{className:"text-text-secondary mb-4",children:"Each flow costs $10 and allows you to send up to 10 emails to 1,000 recipients each. Flows never expire, so you can use them whenever you need."}),(0,d.jsx)(i.A,{onClick:()=>w(!0),children:"Purchase Flows"})]})}),(0,d.jsx)(n.A,{title:"Billing History",className:"mt-6",children:(0,d.jsxs)("div",{className:"table-container",children:[s&&(0,d.jsx)("div",{className:"text-center py-4",children:"Loading history..."}),o&&!s&&(0,d.jsx)(l.A,{type:"error",message:o,className:"m-4"}),!s&&!o&&(0,d.jsxs)("table",{className:"table w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{children:"Date"}),(0,d.jsx)("th",{children:"Transaction"}),(0,d.jsx)("th",{children:"Flows"}),(0,d.jsx)("th",{children:"Amount"}),(0,d.jsx)("th",{children:"Status"})]})}),(0,d.jsx)("tbody",{children:0===h.length?(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:5,className:"text-center py-4",children:"No billing history available"})}):h.map((e=>{var s;return(0,d.jsxs)("tr",{children:[(0,d.jsx)("td",{children:D(e.date)}),(0,d.jsx)("td",{children:"purchase"===e.type?"Flow Purchase":"free_trial"===e.type?"Free Trial":e.type||"Unknown"}),(0,d.jsx)("td",{children:null!==(s=e.flowsQuantity)&&void 0!==s?s:"N/A"}),(0,d.jsx)("td",{children:U(e.amount)}),(0,d.jsx)("td",{children:(0,d.jsx)("span",{className:"px-2 py-1 text-xs rounded capitalize "+("completed"===e.status?"bg-green-800":"pending"===e.status?"bg-yellow-800":"failed"===e.status?"bg-red-800":"bg-gray-700"),children:e.status?e.status.charAt(0).toUpperCase()+e.status.slice(1):"Unknown"})})]},e.id)}))})]})]})}),f&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,d.jsxs)("div",{className:"bg-secondary-bg rounded-lg p-6 max-w-md w-full",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Purchase Flows"}),F&&(0,d.jsx)(l.A,{type:"error",message:F,onClose:()=>k(""),className:"mb-4"}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("label",{className:"block text-text-secondary mb-2",children:"How many flows would you like to purchase?"}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("button",{className:"px-3 py-1 bg-gray-700 rounded-l-md disabled:opacity-50",onClick:()=>v(Math.max(1,N-1)),disabled:S||N<=1,children:"-"}),(0,d.jsx)("div",{className:"px-4 py-1 bg-gray-800 text-center min-w-[40px]",children:N}),(0,d.jsx)("button",{className:"px-3 py-1 bg-gray-700 rounded-r-md disabled:opacity-50",onClick:()=>v(N+1),disabled:S,children:"+"})]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-700",children:[(0,d.jsx)("span",{children:"Price per flow:"}),(0,d.jsx)("span",{children:U(10)})]}),(0,d.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-700",children:[(0,d.jsx)("span",{children:"Quantity:"}),(0,d.jsx)("span",{children:N})]}),(0,d.jsxs)("div",{className:"flex justify-between py-2 font-semibold",children:[(0,d.jsx)("span",{children:"Total:"}),(0,d.jsx)("span",{children:U(10*N)})]})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,d.jsx)(i.A,{variant:"secondary",onClick:()=>w(!1),disabled:S,children:"Cancel"}),(0,d.jsx)(i.A,{onClick:async()=>{if(N<1)k("Quantity must be at least 1");else{A(!0),k(""),C("");try{var e;const s=(null===(e=(await c.p9.purchaseFlows(N)).data)||void 0===e?void 0:e.transaction)||{id:`new-${Date.now()}`,type:"purchase",amount:10*N,flowsQuantity:N,date:(new Date).toISOString(),status:"completed"};m([s,...h]),C(`Successfully purchased ${N} flow${1!==N?"s":""}.`),w(!1),setTimeout((()=>C("")),5e3)}catch(s){console.error("Purchase error:",s),k(s.message||"Failed to process payment. Please try again.")}finally{A(!1)}}},disabled:S,children:S?"Processing...":"Purchase"})]})]})})]})}}}]);
//# sourceMappingURL=182.e9510ead.chunk.js.map