{"ast": null, "code": "/**\n * AISuggestionPanel component for Driftly Email Generator\n * Displays AI-generated content suggestions\n */import React from'react';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const AISuggestionPanel=_ref=>{let{type,suggestions,isLoading,onApply,onClose}=_ref;const typeLabel={headline:'Headline',body:'Body Text',cta:'Call to Action'}[type];return/*#__PURE__*/_jsxs(\"div\",{className:\"ai-suggestion-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"suggestion-header\",children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"AI Suggestions for \",typeLabel]}),/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:onClose,children:\"\\xD7\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"suggestion-content\",children:isLoading?/*#__PURE__*/_jsxs(\"div\",{className:\"loading-suggestions\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Generating suggestions...\"})]}):suggestions.length===0?/*#__PURE__*/_jsx(\"div\",{className:\"no-suggestions\",children:/*#__PURE__*/_jsx(\"p\",{children:\"No suggestions available. Try with different content.\"})}):/*#__PURE__*/_jsx(\"ul\",{className:\"suggestions-list\",children:suggestions.map((suggestion,index)=>/*#__PURE__*/_jsxs(\"li\",{className:\"suggestion-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"suggestion-text\",children:suggestion}),/*#__PURE__*/_jsx(\"button\",{className:\"apply-button\",onClick:()=>onApply(suggestion),children:\"Apply\"})]},index))})})]});};export default AISuggestionPanel;", "map": {"version": 3, "names": ["React", "jsxs", "_jsxs", "jsx", "_jsx", "AISuggestionPanel", "_ref", "type", "suggestions", "isLoading", "onApply", "onClose", "typeLabel", "headline", "body", "cta", "className", "children", "onClick", "length", "map", "suggestion", "index"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/AISuggestionPanel.tsx"], "sourcesContent": ["/**\n * AISuggestionPanel component for Driftly Email Generator\n * Displays AI-generated content suggestions\n */\n\nimport React from 'react';\n\ninterface AISuggestionPanelProps {\n  type: 'headline' | 'body' | 'cta';\n  suggestions: string[];\n  isLoading: boolean;\n  onApply: (suggestion: string) => void;\n  onClose: () => void;\n}\n\nconst AISuggestionPanel: React.FC<AISuggestionPanelProps> = ({\n  type,\n  suggestions,\n  isLoading,\n  onApply,\n  onClose\n}) => {\n  const typeLabel = {\n    headline: 'Headline',\n    body: 'Body Text',\n    cta: 'Call to Action'\n  }[type];\n\n  return (\n    <div className=\"ai-suggestion-panel\">\n      <div className=\"suggestion-header\">\n        <h4>AI Suggestions for {typeLabel}</h4>\n        <button className=\"close-button\" onClick={onClose}>×</button>\n      </div>\n      \n      <div className=\"suggestion-content\">\n        {isLoading ? (\n          <div className=\"loading-suggestions\">\n            <div className=\"spinner\"></div>\n            <p>Generating suggestions...</p>\n          </div>\n        ) : suggestions.length === 0 ? (\n          <div className=\"no-suggestions\">\n            <p>No suggestions available. Try with different content.</p>\n          </div>\n        ) : (\n          <ul className=\"suggestions-list\">\n            {suggestions.map((suggestion, index) => (\n              <li key={index} className=\"suggestion-item\">\n                <div className=\"suggestion-text\">{suggestion}</div>\n                <button \n                  className=\"apply-button\"\n                  onClick={() => onApply(suggestion)}\n                >\n                  Apply\n                </button>\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default AISuggestionPanel;\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAU1B,KAAM,CAAAC,iBAAmD,CAAGC,IAAA,EAMtD,IANuD,CAC3DC,IAAI,CACJC,WAAW,CACXC,SAAS,CACTC,OAAO,CACPC,OACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAAAM,SAAS,CAAG,CAChBC,QAAQ,CAAE,UAAU,CACpBC,IAAI,CAAE,WAAW,CACjBC,GAAG,CAAE,gBACP,CAAC,CAACR,IAAI,CAAC,CAEP,mBACEL,KAAA,QAAKc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCf,KAAA,QAAKc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCf,KAAA,OAAAe,QAAA,EAAI,qBAAmB,CAACL,SAAS,EAAK,CAAC,cACvCR,IAAA,WAAQY,SAAS,CAAC,cAAc,CAACE,OAAO,CAAEP,OAAQ,CAAAM,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC1D,CAAC,cAENb,IAAA,QAAKY,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChCR,SAAS,cACRP,KAAA,QAAKc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCb,IAAA,QAAKY,SAAS,CAAC,SAAS,CAAM,CAAC,cAC/BZ,IAAA,MAAAa,QAAA,CAAG,2BAAyB,CAAG,CAAC,EAC7B,CAAC,CACJT,WAAW,CAACW,MAAM,GAAK,CAAC,cAC1Bf,IAAA,QAAKY,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7Bb,IAAA,MAAAa,QAAA,CAAG,uDAAqD,CAAG,CAAC,CACzD,CAAC,cAENb,IAAA,OAAIY,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC7BT,WAAW,CAACY,GAAG,CAAC,CAACC,UAAU,CAAEC,KAAK,gBACjCpB,KAAA,OAAgBc,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eACzCb,IAAA,QAAKY,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAEI,UAAU,CAAM,CAAC,cACnDjB,IAAA,WACEY,SAAS,CAAC,cAAc,CACxBE,OAAO,CAAEA,CAAA,GAAMR,OAAO,CAACW,UAAU,CAAE,CAAAJ,QAAA,CACpC,OAED,CAAQ,CAAC,GAPFK,KAQL,CACL,CAAC,CACA,CACL,CACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAjB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}