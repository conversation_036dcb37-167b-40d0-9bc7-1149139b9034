"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[630],{3630:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var a=s(5043),r=s(9291),l=s(8417),i=s(1411),c=s(4741),n=s(6517),o=s(9579),d=s(579);const m=()=>{const[e,t]=(0,a.useState)(null),[s,m]=(0,a.useState)(!0),[x,p]=(0,a.useState)(null),[h,u]=(0,a.useState)({subject:"",content:""}),[b,j]=(0,a.useState)(null),[y,v]=(0,a.useState)(!1),[g,N]=(0,a.useState)("overview");(0,a.useEffect)((()=>{(async()=>{m(!0),p(null);try{const e=await o.pT.getMetrics();e.success?t(e.data):p(e.message||"Failed to fetch deliverability metrics")}catch(e){p(e.message||"An error occurred while fetching metrics")}finally{m(!1)}})()}),[]);const f=(e,t)=>void 0===e?"text-gray-500":e>=t.good?"text-green-500":e>=t.warning?"text-yellow-500":"text-red-500",w=e=>void 0===e?{text:"N/A",color:"text-gray-500"}:e<2?{text:"Low Risk",color:"text-green-500"}:e<5?{text:"Medium Risk",color:"text-yellow-500"}:{text:"High Risk",color:"text-red-500"};return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,d.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,d.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"Email Deliverability"})}),(0,d.jsx)("p",{className:"text-text-secondary mb-6",children:"Monitor and improve your email deliverability metrics."}),x&&(0,d.jsx)(r.A,{type:"error",message:x,onClose:()=>p(null),className:"mb-6"}),(0,d.jsxs)(i.A,{className:"overflow-hidden",children:[(0,d.jsx)("div",{className:"border-b border-gray-700",children:(0,d.jsxs)("nav",{className:"-mb-px flex space-x-4 px-6",children:[(0,d.jsx)("button",{className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm "+("overview"===g?"border-primary text-primary":"border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500"),onClick:()=>N("overview"),children:"Overview"}),(0,d.jsx)("button",{className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm "+("details"===g?"border-primary text-primary":"border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500"),onClick:()=>N("details"),children:"Detailed Metrics"}),(0,d.jsx)("button",{className:"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm "+("spam-checker"===g?"border-primary text-primary":"border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500"),onClick:()=>N("spam-checker"),children:"Spam Checker"})]})}),(0,d.jsx)("div",{className:"p-6",children:s?(0,d.jsx)("div",{className:"flex justify-center py-8",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):e||"spam-checker"===g?(0,d.jsxs)(d.Fragment,{children:["overview"===g&&e&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Deliverability Overview"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,d.jsx)(n.A,{title:"Delivery Rate",value:`${e.deliveryRate||0}%`,icon:"inbox-in"}),(0,d.jsx)(n.A,{title:"Open Rate",value:`${e.openRate||0}%`,icon:"eye"}),(0,d.jsx)(n.A,{title:"Bounce Rate",value:`${e.bounceRate||0}%`,icon:"exclamation-circle"}),(0,d.jsx)(n.A,{title:"Click Rate",value:`${e.clickRate||0}%`,icon:"cursor-click"})]})]}),"details"===g&&e&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Detailed Metrics"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)(i.A,{className:"p-4 border border-gray-700 rounded",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-2",children:"Sender Reputation"}),(0,d.jsx)("p",{className:`text-2xl font-bold mb-2 ${f(e.senderScore,{good:90,warning:70})}`,children:void 0!==e.senderScore?`${e.senderScore} / 100`:"N/A"}),(0,d.jsx)("p",{className:"text-sm text-text-secondary",children:"Score based on engagement, bounce rates, and spam complaints."})]}),(0,d.jsxs)(i.A,{className:"p-4 border border-gray-700 rounded",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-2",children:"Complaint Rate"}),(0,d.jsx)("p",{className:`text-2xl font-bold mb-2 ${f(void 0!==e.complaintRate?100-100*e.complaintRate:void 0,{good:99.9,warning:99.5})}`,children:void 0!==e.complaintRate?`${e.complaintRate.toFixed(3)}%`:"N/A"}),(0,d.jsx)("p",{className:"text-sm text-text-secondary",children:"Percentage of recipients marking emails as spam."})]})]})]}),"spam-checker"===g&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Email Spam Checker"}),(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),h.subject.trim()&&h.content.trim()){v(!0),p(null),j(null);try{const e=await o.pT.checkSpamScore(h.subject,h.content);e.success?j(e.data):p(e.message||"Failed to check spam score")}catch(t){p(t.message||"An error occurred while checking spam score")}finally{v(!1)}}else p("Subject and content are required for spam check")},className:"space-y-4 mb-6",children:[(0,d.jsx)(c.A,{label:"Subject Line",id:"spamSubject",name:"spamSubject",value:h.subject,onChange:e=>u({...h,subject:e.target.value}),required:!0}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"spamContent",className:"block text-sm font-medium text-text-secondary mb-1",children:"Email Content (HTML or Plain Text)"}),(0,d.jsx)("textarea",{id:"spamContent",name:"spamContent",rows:10,value:h.content,onChange:e=>u({...h,content:e.target.value}),required:!0,className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500"})]}),(0,d.jsx)("div",{className:"flex justify-end",children:(0,d.jsx)(l.A,{type:"submit",disabled:y,children:y?"Checking...":"Check Spam Score"})})]}),y&&(0,d.jsxs)("div",{className:"flex justify-center py-4",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"}),(0,d.jsx)("span",{className:"ml-3 text-text-secondary",children:"Checking spam score..."})]}),null!==b&&(0,d.jsxs)(i.A,{className:"p-4 border border-gray-700 rounded",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-text-primary mb-2",children:"Spam Check Results"}),(0,d.jsxs)("p",{className:"text-sm text-text-secondary mb-1",children:["Overall Score:",(0,d.jsx)("span",{className:`font-bold text-xl ml-2 ${w(b.score).color}`,children:void 0!==b.score?b.score.toFixed(1):"N/A"})]}),(0,d.jsxs)("p",{className:"text-sm text-text-secondary mb-3",children:["Status:",(0,d.jsx)("span",{className:`font-semibold ml-2 ${w(b.score).color}`,children:w(b.score).text})]}),b.issues&&b.issues.length>0&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h4",{className:"text-md font-medium text-text-secondary mb-1",children:"Potential Issues:"}),(0,d.jsx)("ul",{className:"list-disc list-inside text-sm text-text-secondary space-y-1",children:b.issues.map(((e,t)=>(0,d.jsx)("li",{children:e},t)))})]}),b.issues&&0===b.issues.length&&(0,d.jsx)("p",{className:"text-sm text-green-500 italic",children:"No major spam issues found."})]})]})]}):(0,d.jsx)("p",{className:"text-sm text-text-secondary",children:"No deliverability data available."})})]})]})}},6517:(e,t,s)=>{s.d(t,{A:()=>l});s(5043);var a=s(579);const r=e=>{let{name:t}=e;return(0,a.jsx)("i",{className:`placeholder-icon-${t} w-5 h-5`})},l=e=>{let{title:t,value:s,icon:l,change:i,className:c="",tooltip:n,details:o}=e;return(0,a.jsxs)("div",{className:`card container-futuristic flex flex-col ${c}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"stat-label",children:t}),l&&(0,a.jsx)("span",{className:"text-text-secondary opacity-80",children:(0,a.jsx)(r,{name:l})})]}),(0,a.jsx)("div",{className:"stat-value mb-1",children:s}),n&&(0,a.jsx)("div",{className:"text-xs text-text-secondary mt-1 opacity-90",children:n}),i&&(0,a.jsxs)("div",{className:"text-sm mt-2 flex items-center font-medium "+(i.isPositive?"text-growth-green":"text-danger"),children:[(0,a.jsx)("span",{className:"mr-1",children:i.isPositive?(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})}),i.value]}),o&&o.length>0&&(0,a.jsx)("div",{className:"mt-auto pt-3 border-t border-border mt-3",children:o.map(((e,t)=>(0,a.jsxs)("div",{className:"flex justify-between text-xs py-1",children:[(0,a.jsx)("span",{className:"text-text-secondary opacity-90",children:e.label}),(0,a.jsx)("span",{className:"font-medium",children:e.value})]},t)))})]})}}}]);
//# sourceMappingURL=630.6dad9ca5.chunk.js.map