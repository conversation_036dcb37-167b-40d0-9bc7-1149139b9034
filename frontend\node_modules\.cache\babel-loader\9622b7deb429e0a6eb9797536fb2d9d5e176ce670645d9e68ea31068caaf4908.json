{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\Billing.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\nimport { useAuth } from '../contexts/AuthContext';\nimport { billingService } from '../services';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Billing = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [loadingHistory, setLoadingHistory] = useState(true);\n  const [historyError, setHistoryError] = useState('');\n  const [billingHistory, setBillingHistory] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showPurchaseModal, setShowPurchaseModal] = useState(false);\n  const [quantity, setQuantity] = useState(1);\n  const [loadingPurchase, setLoadingPurchase] = useState(false);\n  const [purchaseError, setPurchaseError] = useState('');\n  const [purchaseSuccess, setPurchaseSuccess] = useState('');\n\n  // Fetch billing history\n  useEffect(() => {\n    const fetchBillingHistory = async () => {\n      setLoadingHistory(true);\n      setHistoryError('');\n      try {\n        const response = await billingService.getHistory();\n        setBillingHistory(response.data || []);\n      } catch (err) {\n        console.error('Error fetching billing history:', err);\n        setHistoryError(err.message || 'Failed to fetch billing history. Please try again.');\n        setBillingHistory([]);\n      } finally {\n        setLoadingHistory(false);\n      }\n    };\n    fetchBillingHistory();\n  }, []);\n\n  // Handle flow purchase\n  const handlePurchaseFlows = async () => {\n    if (quantity < 1) {\n      setPurchaseError('Quantity must be at least 1');\n      return;\n    }\n    setLoadingPurchase(true);\n    setPurchaseError('');\n    setPurchaseSuccess('');\n    try {\n      var _response$data;\n      const response = await billingService.purchaseFlows(quantity);\n      const newTransaction = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.transaction) || {\n        id: `new-${Date.now()}`,\n        type: 'purchase',\n        amount: quantity * 10.00,\n        flowsQuantity: quantity,\n        date: new Date().toISOString(),\n        status: 'completed'\n      };\n      setBillingHistory([newTransaction, ...billingHistory]);\n      setPurchaseSuccess(`Successfully purchased ${quantity} flow${quantity !== 1 ? 's' : ''}.`);\n      setShowPurchaseModal(false);\n      setTimeout(() => setPurchaseSuccess(''), 5000);\n    } catch (err) {\n      console.error('Purchase error:', err);\n      setPurchaseError(err.message || 'Failed to process payment. Please try again.');\n    } finally {\n      setLoadingPurchase(false);\n    }\n  };\n\n  // Format date for display\n  const formatDate = dateString => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } catch (e) {\n      console.error(\"Error formatting date:\", dateString, e);\n      return \"Invalid Date\";\n    }\n  };\n\n  // Format currency\n  const formatCurrency = amount => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold\",\n          children: \"Billing & Flow Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary\",\n          children: \"Manage your email flow purchases and billing information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setShowPurchaseModal(true),\n        children: \"Purchase Flows\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), purchaseError && /*#__PURE__*/_jsxDEV(Alert, {\n      type: \"error\",\n      message: purchaseError,\n      onClose: () => setPurchaseError(''),\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this), purchaseSuccess && /*#__PURE__*/_jsxDEV(Alert, {\n      type: \"success\",\n      message: purchaseSuccess,\n      className: \"mb-6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-primary mb-2\",\n            children: (user === null || user === void 0 ? void 0 : user.flowsPurchased) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary\",\n            children: \"Available Flows\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-green-500 mb-2\",\n            children: \"$10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary\",\n            children: \"Per Flow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-4xl font-bold text-blue-500 mb-2\",\n            children: \"1,000\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-text-secondary\",\n            children: \"Recipients Per Flow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"Flow Pricing Information\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium mb-2\",\n          children: \"What's included in each flow?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"list-disc list-inside text-text-secondary mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Send up to 10 emails in a campaign flow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Reach up to 1,000 recipients per flow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Full access to email templates and editor\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Comprehensive analytics and tracking\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Automated email scheduling\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium mb-2\",\n          children: \"Pricing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary mb-4\",\n          children: \"Each flow costs $10 and allows you to send up to 10 emails to 1,000 recipients each. Flows never expire, so you can use them whenever you need.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowPurchaseModal(true),\n          children: \"Purchase Flows\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"Billing History\",\n      className: \"mt-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-container\",\n        children: [loadingHistory && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: \"Loading history...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), historyError && !loadingHistory && /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: historyError,\n          className: \"m-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), !loadingHistory && !historyError && /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"table w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Transaction\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Flows\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Amount\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: billingHistory.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: 5,\n                className: \"text-center py-4\",\n                children: \"No billing history available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 19\n            }, this) : billingHistory.map(transaction => {\n              var _transaction$flowsQua;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  children: formatDate(transaction.date)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: transaction.type === 'purchase' ? 'Flow Purchase' : transaction.type === 'free_trial' ? 'Free Trial' : transaction.type || 'Unknown'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: (_transaction$flowsQua = transaction.flowsQuantity) !== null && _transaction$flowsQua !== void 0 ? _transaction$flowsQua : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: formatCurrency(transaction.amount)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `px-2 py-1 text-xs rounded capitalize ${transaction.status === 'completed' ? 'bg-green-800' : transaction.status === 'pending' ? 'bg-yellow-800' : transaction.status === 'failed' ? 'bg-red-800' : 'bg-gray-700'}`,\n                    children: transaction.status ? transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1) : 'Unknown'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)]\n              }, transaction.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), showPurchaseModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-secondary-bg rounded-lg p-6 max-w-md w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold mb-4\",\n          children: \"Purchase Flows\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this), purchaseError && /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: purchaseError,\n          onClose: () => setPurchaseError(''),\n          className: \"mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"block text-text-secondary mb-2\",\n            children: \"How many flows would you like to purchase?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-3 py-1 bg-gray-700 rounded-l-md disabled:opacity-50\",\n              onClick: () => setQuantity(Math.max(1, quantity - 1)),\n              disabled: loadingPurchase || quantity <= 1,\n              children: \"-\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 py-1 bg-gray-800 text-center min-w-[40px]\",\n              children: quantity\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"px-3 py-1 bg-gray-700 rounded-r-md disabled:opacity-50\",\n              onClick: () => setQuantity(quantity + 1),\n              disabled: loadingPurchase,\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between py-2 border-b border-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Price per flow:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatCurrency(10.00)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between py-2 border-b border-gray-700\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Quantity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: quantity\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between py-2 font-semibold\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Total:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatCurrency(quantity * 10.00)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"secondary\",\n            onClick: () => setShowPurchaseModal(false),\n            disabled: loadingPurchase,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handlePurchaseFlows,\n            disabled: loadingPurchase,\n            children: loadingPurchase ? 'Processing...' : 'Purchase'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(Billing, \"Hzm0sCzyJmjjEteJ1+aIoBoCzEM=\", false, function () {\n  return [useAuth];\n});\n_c = Billing;\nexport default Billing;\nvar _c;\n$RefreshReg$(_c, \"Billing\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "useAuth", "billingService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Billing", "_s", "user", "loadingHistory", "setLoadingHistory", "historyError", "setHistoryError", "billingHistory", "setBillingHistory", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showPurchaseModal", "setShowPurchaseModal", "quantity", "setQuantity", "loadingPurchase", "setLoadingPurchase", "purchaseError", "setPurchaseError", "purchaseSuccess", "setPurchaseSuccess", "fetchBillingHistory", "response", "getHistory", "data", "err", "console", "message", "handlePurchaseFlows", "_response$data", "purchaseFlows", "newTransaction", "transaction", "id", "Date", "now", "type", "amount", "flowsQuantity", "date", "toISOString", "status", "setTimeout", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "hour", "minute", "e", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onClose", "flowsPurchased", "title", "length", "colSpan", "map", "_transaction$flowsQua", "char<PERSON>t", "toUpperCase", "slice", "Math", "max", "disabled", "variant", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/Billing.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\nimport { useAuth } from '../contexts/AuthContext';\nimport { billingService } from '../services';\n\nconst Billing: React.FC = () => {\n  const { user } = useAuth();\n  const [loadingHistory, setLoadingHistory] = useState(true);\n  const [historyError, setHistoryError] = useState('');\n  const [billingHistory, setBillingHistory] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showPurchaseModal, setShowPurchaseModal] = useState(false);\n  const [quantity, setQuantity] = useState(1);\n  const [loadingPurchase, setLoadingPurchase] = useState(false);\n  const [purchaseError, setPurchaseError] = useState('');\n  const [purchaseSuccess, setPurchaseSuccess] = useState('');\n  \n  // Fetch billing history\n  useEffect(() => {\n    const fetchBillingHistory = async () => {\n      setLoadingHistory(true);\n      setHistoryError('');\n      try {\n        const response = await billingService.getHistory();\n        setBillingHistory(response.data || []);\n      } catch (err: any) {\n        console.error('Error fetching billing history:', err);\n        setHistoryError(err.message || 'Failed to fetch billing history. Please try again.');\n        setBillingHistory([]);\n      } finally {\n        setLoadingHistory(false);\n      }\n    };\n\n    fetchBillingHistory();\n  }, []);\n  \n  // Handle flow purchase\n  const handlePurchaseFlows = async () => {\n    if (quantity < 1) {\n      setPurchaseError('Quantity must be at least 1');\n      return;\n    }\n    \n    setLoadingPurchase(true);\n    setPurchaseError('');\n    setPurchaseSuccess('');\n    \n    try {\n      const response = await billingService.purchaseFlows(quantity);\n\n      const newTransaction = response.data?.transaction || {\n        id: `new-${Date.now()}`,\n        type: 'purchase',\n        amount: quantity * 10.00,\n        flowsQuantity: quantity,\n        date: new Date().toISOString(),\n        status: 'completed'\n      };\n      \n      setBillingHistory([newTransaction, ...billingHistory]);\n      setPurchaseSuccess(`Successfully purchased ${quantity} flow${quantity !== 1 ? 's' : ''}.`);\n      setShowPurchaseModal(false);\n      \n      setTimeout(() => setPurchaseSuccess(''), 5000);\n    } catch (err: any) {\n      console.error('Purchase error:', err);\n      setPurchaseError(err.message || 'Failed to process payment. Please try again.');\n    } finally {\n      setLoadingPurchase(false);\n    }\n  };\n  \n  // Format date for display\n  const formatDate = (dateString: string) => {\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } catch (e) {\n      console.error(\"Error formatting date:\", dateString, e);\n      return \"Invalid Date\";\n    }\n  };\n  \n  // Format currency\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(amount);\n  };\n  \n  return (\n    <>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-xl font-semibold\">Billing & Flow Management</h2>\n          <p className=\"text-text-secondary\">\n            Manage your email flow purchases and billing information\n          </p>\n        </div>\n        \n        <Button onClick={() => setShowPurchaseModal(true)}>\n          Purchase Flows\n        </Button>\n      </div>\n      \n      {purchaseError && (\n        <Alert\n          type=\"error\"\n          message={purchaseError}\n          onClose={() => setPurchaseError('')}\n          className=\"mb-6\"\n        />\n      )}\n      \n      {purchaseSuccess && (\n        <Alert\n          type=\"success\"\n          message={purchaseSuccess}\n          className=\"mb-6\"\n        />\n      )}\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n        <Card>\n          <div className=\"text-center p-4\">\n            <div className=\"text-4xl font-bold text-primary mb-2\">\n              {user?.flowsPurchased || 0}\n            </div>\n            <div className=\"text-text-secondary\">Available Flows</div>\n          </div>\n        </Card>\n        \n        <Card>\n          <div className=\"text-center p-4\">\n            <div className=\"text-4xl font-bold text-green-500 mb-2\">\n              $10\n            </div>\n            <div className=\"text-text-secondary\">Per Flow</div>\n          </div>\n        </Card>\n        \n        <Card>\n          <div className=\"text-center p-4\">\n            <div className=\"text-4xl font-bold text-blue-500 mb-2\">\n              1,000\n            </div>\n            <div className=\"text-text-secondary\">Recipients Per Flow</div>\n          </div>\n        </Card>\n      </div>\n      \n      <Card title=\"Flow Pricing Information\">\n        <div className=\"p-4\">\n          <h3 className=\"text-lg font-medium mb-2\">What's included in each flow?</h3>\n          <ul className=\"list-disc list-inside text-text-secondary mb-4\">\n            <li>Send up to 10 emails in a campaign flow</li>\n            <li>Reach up to 1,000 recipients per flow</li>\n            <li>Full access to email templates and editor</li>\n            <li>Comprehensive analytics and tracking</li>\n            <li>Automated email scheduling</li>\n          </ul>\n          \n          <h3 className=\"text-lg font-medium mb-2\">Pricing</h3>\n          <p className=\"text-text-secondary mb-4\">\n            Each flow costs $10 and allows you to send up to 10 emails to 1,000 recipients each.\n            Flows never expire, so you can use them whenever you need.\n          </p>\n          \n          <Button onClick={() => setShowPurchaseModal(true)}>\n            Purchase Flows\n          </Button>\n        </div>\n      </Card>\n      \n      <Card title=\"Billing History\" className=\"mt-6\">\n        <div className=\"table-container\">\n          {loadingHistory && (\n            <div className=\"text-center py-4\">Loading history...</div>\n          )}\n\n          {historyError && !loadingHistory && (\n            <Alert type=\"error\" message={historyError} className=\"m-4\"/>\n          )}\n\n          {!loadingHistory && !historyError && (\n            <table className=\"table w-full\">\n              <thead>\n                <tr>\n                  <th>Date</th>\n                  <th>Transaction</th>\n                  <th>Flows</th>\n                  <th>Amount</th>\n                  <th>Status</th>\n                </tr>\n              </thead>\n              <tbody>\n                {billingHistory.length === 0 ? (\n                  <tr>\n                    <td colSpan={5} className=\"text-center py-4\">\n                      No billing history available\n                    </td>\n                  </tr>\n                ) : (\n                  billingHistory.map((transaction) => (\n                    <tr key={transaction.id}>\n                      <td>{formatDate(transaction.date)}</td>\n                      <td>\n                        {transaction.type === 'purchase' ? 'Flow Purchase' :\n                         transaction.type === 'free_trial' ? 'Free Trial' :\n                         transaction.type || 'Unknown'}\n                      </td>\n                      <td>{transaction.flowsQuantity ?? 'N/A'}</td>\n                      <td>{formatCurrency(transaction.amount)}</td>\n                      <td>\n                        <span className={`px-2 py-1 text-xs rounded capitalize ${\n                          transaction.status === 'completed' ? 'bg-green-800' :\n                          transaction.status === 'pending' ? 'bg-yellow-800' :\n                          transaction.status === 'failed' ? 'bg-red-800' :\n                          'bg-gray-700'\n                        }`}>\n                          {transaction.status ? transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1) : 'Unknown'}\n                        </span>\n                      </td>\n                    </tr>\n                  ))\n                )}\n              </tbody>\n            </table>\n          )}\n        </div>\n      </Card>\n      \n      {showPurchaseModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-secondary-bg rounded-lg p-6 max-w-md w-full\">\n            <h2 className=\"text-xl font-semibold mb-4\">Purchase Flows</h2>\n            \n            {purchaseError && (\n              <Alert type=\"error\" message={purchaseError} onClose={() => setPurchaseError('')} className=\"mb-4\"/>\n            )}\n\n            <div className=\"mb-6\">\n              <label className=\"block text-text-secondary mb-2\">\n                How many flows would you like to purchase?\n              </label>\n              \n              <div className=\"flex items-center\">\n                <button\n                  className=\"px-3 py-1 bg-gray-700 rounded-l-md disabled:opacity-50\"\n                  onClick={() => setQuantity(Math.max(1, quantity - 1))}\n                  disabled={loadingPurchase || quantity <= 1}\n                >\n                  -\n                </button>\n                <div className=\"px-4 py-1 bg-gray-800 text-center min-w-[40px]\">\n                  {quantity}\n                </div>\n                <button\n                  className=\"px-3 py-1 bg-gray-700 rounded-r-md disabled:opacity-50\"\n                  onClick={() => setQuantity(quantity + 1)}\n                  disabled={loadingPurchase}\n                >\n                  +\n                </button>\n              </div>\n            </div>\n            \n            <div className=\"mb-6\">\n              <div className=\"flex justify-between py-2 border-b border-gray-700\">\n                <span>Price per flow:</span>\n                <span>{formatCurrency(10.00)}</span>\n              </div>\n              <div className=\"flex justify-between py-2 border-b border-gray-700\">\n                <span>Quantity:</span>\n                <span>{quantity}</span>\n              </div>\n              <div className=\"flex justify-between py-2 font-semibold\">\n                <span>Total:</span>\n                <span>{formatCurrency(quantity * 10.00)}</span>\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-2\">\n              <Button\n                variant=\"secondary\"\n                onClick={() => setShowPurchaseModal(false)}\n                disabled={loadingPurchase}\n              >\n                Cancel\n              </Button>\n              <Button\n                onClick={handlePurchaseFlows}\n                disabled={loadingPurchase}\n              >\n                {loadingPurchase ? 'Processing...' : 'Purchase'}\n              </Button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default Billing;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,SAAS,EACTC,QAAQ,QACH,OAAO;AAEd,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,cAAc,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACe,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2B,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACAD,SAAS,CAAC,MAAM;IACd,MAAMoC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtCrB,iBAAiB,CAAC,IAAI,CAAC;MACvBE,eAAe,CAAC,EAAE,CAAC;MACnB,IAAI;QACF,MAAMoB,QAAQ,GAAG,MAAM/B,cAAc,CAACgC,UAAU,CAAC,CAAC;QAClDnB,iBAAiB,CAACkB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;MACxC,CAAC,CAAC,OAAOC,GAAQ,EAAE;QACjBC,OAAO,CAACnB,KAAK,CAAC,iCAAiC,EAAEkB,GAAG,CAAC;QACrDvB,eAAe,CAACuB,GAAG,CAACE,OAAO,IAAI,oDAAoD,CAAC;QACpFvB,iBAAiB,CAAC,EAAE,CAAC;MACvB,CAAC,SAAS;QACRJ,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAEDqB,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAIf,QAAQ,GAAG,CAAC,EAAE;MAChBK,gBAAgB,CAAC,6BAA6B,CAAC;MAC/C;IACF;IAEAF,kBAAkB,CAAC,IAAI,CAAC;IACxBE,gBAAgB,CAAC,EAAE,CAAC;IACpBE,kBAAkB,CAAC,EAAE,CAAC;IAEtB,IAAI;MAAA,IAAAS,cAAA;MACF,MAAMP,QAAQ,GAAG,MAAM/B,cAAc,CAACuC,aAAa,CAACjB,QAAQ,CAAC;MAE7D,MAAMkB,cAAc,GAAG,EAAAF,cAAA,GAAAP,QAAQ,CAACE,IAAI,cAAAK,cAAA,uBAAbA,cAAA,CAAeG,WAAW,KAAI;QACnDC,EAAE,EAAE,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACvBC,IAAI,EAAE,UAAU;QAChBC,MAAM,EAAExB,QAAQ,GAAG,KAAK;QACxByB,aAAa,EAAEzB,QAAQ;QACvB0B,IAAI,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;QAC9BC,MAAM,EAAE;MACV,CAAC;MAEDrC,iBAAiB,CAAC,CAAC2B,cAAc,EAAE,GAAG5B,cAAc,CAAC,CAAC;MACtDiB,kBAAkB,CAAC,0BAA0BP,QAAQ,QAAQA,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,CAAC;MAC1FD,oBAAoB,CAAC,KAAK,CAAC;MAE3B8B,UAAU,CAAC,MAAMtB,kBAAkB,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IAChD,CAAC,CAAC,OAAOK,GAAQ,EAAE;MACjBC,OAAO,CAACnB,KAAK,CAAC,iBAAiB,EAAEkB,GAAG,CAAC;MACrCP,gBAAgB,CAACO,GAAG,CAACE,OAAO,IAAI,8CAA8C,CAAC;IACjF,CAAC,SAAS;MACRX,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM2B,UAAU,GAAIC,UAAkB,IAAK;IACzC,IAAI;MACF,MAAML,IAAI,GAAG,IAAIL,IAAI,CAACU,UAAU,CAAC;MACjC,OAAOL,IAAI,CAACM,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,CAAC,EAAE;MACVzB,OAAO,CAACnB,KAAK,CAAC,wBAAwB,EAAEqC,UAAU,EAAEO,CAAC,CAAC;MACtD,OAAO,cAAc;IACvB;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIf,MAAc,IAAK;IACzC,OAAO,IAAIgB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACpB,MAAM,CAAC;EACnB,CAAC;EAED,oBACE5C,OAAA,CAAAE,SAAA;IAAA+D,QAAA,gBACEjE,OAAA;MAAKkE,SAAS,EAAC,wCAAwC;MAAAD,QAAA,gBACrDjE,OAAA;QAAAiE,QAAA,gBACEjE,OAAA;UAAIkE,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EAAC;QAAyB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEtE,OAAA;UAAGkE,SAAS,EAAC,qBAAqB;UAAAD,QAAA,EAAC;QAEnC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENtE,OAAA,CAACL,MAAM;QAAC4E,OAAO,EAAEA,CAAA,KAAMpD,oBAAoB,CAAC,IAAI,CAAE;QAAA8C,QAAA,EAAC;MAEnD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL9C,aAAa,iBACZxB,OAAA,CAACN,KAAK;MACJiD,IAAI,EAAC,OAAO;MACZT,OAAO,EAAEV,aAAc;MACvBgD,OAAO,EAAEA,CAAA,KAAM/C,gBAAgB,CAAC,EAAE,CAAE;MACpCyC,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACF,EAEA5C,eAAe,iBACd1B,OAAA,CAACN,KAAK;MACJiD,IAAI,EAAC,SAAS;MACdT,OAAO,EAAER,eAAgB;MACzBwC,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACF,eAEDtE,OAAA;MAAKkE,SAAS,EAAC,4CAA4C;MAAAD,QAAA,gBACzDjE,OAAA,CAACJ,IAAI;QAAAqE,QAAA,eACHjE,OAAA;UAAKkE,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9BjE,OAAA;YAAKkE,SAAS,EAAC,sCAAsC;YAAAD,QAAA,EAClD,CAAA5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,cAAc,KAAI;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACNtE,OAAA;YAAKkE,SAAS,EAAC,qBAAqB;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPtE,OAAA,CAACJ,IAAI;QAAAqE,QAAA,eACHjE,OAAA;UAAKkE,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9BjE,OAAA;YAAKkE,SAAS,EAAC,wCAAwC;YAAAD,QAAA,EAAC;UAExD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtE,OAAA;YAAKkE,SAAS,EAAC,qBAAqB;YAAAD,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEPtE,OAAA,CAACJ,IAAI;QAAAqE,QAAA,eACHjE,OAAA;UAAKkE,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC9BjE,OAAA;YAAKkE,SAAS,EAAC,uCAAuC;YAAAD,QAAA,EAAC;UAEvD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtE,OAAA;YAAKkE,SAAS,EAAC,qBAAqB;YAAAD,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENtE,OAAA,CAACJ,IAAI;MAAC8E,KAAK,EAAC,0BAA0B;MAAAT,QAAA,eACpCjE,OAAA;QAAKkE,SAAS,EAAC,KAAK;QAAAD,QAAA,gBAClBjE,OAAA;UAAIkE,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EAAC;QAA6B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EtE,OAAA;UAAIkE,SAAS,EAAC,gDAAgD;UAAAD,QAAA,gBAC5DjE,OAAA;YAAAiE,QAAA,EAAI;UAAuC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDtE,OAAA;YAAAiE,QAAA,EAAI;UAAqC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CtE,OAAA;YAAAiE,QAAA,EAAI;UAAyC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClDtE,OAAA;YAAAiE,QAAA,EAAI;UAAoC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CtE,OAAA;YAAAiE,QAAA,EAAI;UAA0B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAELtE,OAAA;UAAIkE,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrDtE,OAAA;UAAGkE,SAAS,EAAC,0BAA0B;UAAAD,QAAA,EAAC;QAGxC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJtE,OAAA,CAACL,MAAM;UAAC4E,OAAO,EAAEA,CAAA,KAAMpD,oBAAoB,CAAC,IAAI,CAAE;UAAA8C,QAAA,EAAC;QAEnD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPtE,OAAA,CAACJ,IAAI;MAAC8E,KAAK,EAAC,iBAAiB;MAACR,SAAS,EAAC,MAAM;MAAAD,QAAA,eAC5CjE,OAAA;QAAKkE,SAAS,EAAC,iBAAiB;QAAAD,QAAA,GAC7B3D,cAAc,iBACbN,OAAA;UAAKkE,SAAS,EAAC,kBAAkB;UAAAD,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAC1D,EAEA9D,YAAY,IAAI,CAACF,cAAc,iBAC9BN,OAAA,CAACN,KAAK;UAACiD,IAAI,EAAC,OAAO;UAACT,OAAO,EAAE1B,YAAa;UAAC0D,SAAS,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAC5D,EAEA,CAAChE,cAAc,IAAI,CAACE,YAAY,iBAC/BR,OAAA;UAAOkE,SAAS,EAAC,cAAc;UAAAD,QAAA,gBAC7BjE,OAAA;YAAAiE,QAAA,eACEjE,OAAA;cAAAiE,QAAA,gBACEjE,OAAA;gBAAAiE,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbtE,OAAA;gBAAAiE,QAAA,EAAI;cAAW;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpBtE,OAAA;gBAAAiE,QAAA,EAAI;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdtE,OAAA;gBAAAiE,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACftE,OAAA;gBAAAiE,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRtE,OAAA;YAAAiE,QAAA,EACGvD,cAAc,CAACiE,MAAM,KAAK,CAAC,gBAC1B3E,OAAA;cAAAiE,QAAA,eACEjE,OAAA;gBAAI4E,OAAO,EAAE,CAAE;gBAACV,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAEL5D,cAAc,CAACmE,GAAG,CAAEtC,WAAW;cAAA,IAAAuC,qBAAA;cAAA,oBAC7B9E,OAAA;gBAAAiE,QAAA,gBACEjE,OAAA;kBAAAiE,QAAA,EAAKf,UAAU,CAACX,WAAW,CAACO,IAAI;gBAAC;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvCtE,OAAA;kBAAAiE,QAAA,EACG1B,WAAW,CAACI,IAAI,KAAK,UAAU,GAAG,eAAe,GACjDJ,WAAW,CAACI,IAAI,KAAK,YAAY,GAAG,YAAY,GAChDJ,WAAW,CAACI,IAAI,IAAI;gBAAS;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACLtE,OAAA;kBAAAiE,QAAA,GAAAa,qBAAA,GAAKvC,WAAW,CAACM,aAAa,cAAAiC,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7CtE,OAAA;kBAAAiE,QAAA,EAAKN,cAAc,CAACpB,WAAW,CAACK,MAAM;gBAAC;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7CtE,OAAA;kBAAAiE,QAAA,eACEjE,OAAA;oBAAMkE,SAAS,EAAE,wCACf3B,WAAW,CAACS,MAAM,KAAK,WAAW,GAAG,cAAc,GACnDT,WAAW,CAACS,MAAM,KAAK,SAAS,GAAG,eAAe,GAClDT,WAAW,CAACS,MAAM,KAAK,QAAQ,GAAG,YAAY,GAC9C,aAAa,EACZ;oBAAAiB,QAAA,EACA1B,WAAW,CAACS,MAAM,GAAGT,WAAW,CAACS,MAAM,CAAC+B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGzC,WAAW,CAACS,MAAM,CAACiC,KAAK,CAAC,CAAC,CAAC,GAAG;kBAAS;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAlBE/B,WAAW,CAACC,EAAE;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBnB,CAAC;YAAA,CACN;UACF;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAENpD,iBAAiB,iBAChBlB,OAAA;MAAKkE,SAAS,EAAC,gFAAgF;MAAAD,QAAA,eAC7FjE,OAAA;QAAKkE,SAAS,EAAC,gDAAgD;QAAAD,QAAA,gBAC7DjE,OAAA;UAAIkE,SAAS,EAAC,4BAA4B;UAAAD,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAE7D9C,aAAa,iBACZxB,OAAA,CAACN,KAAK;UAACiD,IAAI,EAAC,OAAO;UAACT,OAAO,EAAEV,aAAc;UAACgD,OAAO,EAAEA,CAAA,KAAM/C,gBAAgB,CAAC,EAAE,CAAE;UAACyC,SAAS,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CACnG,eAEDtE,OAAA;UAAKkE,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACnBjE,OAAA;YAAOkE,SAAS,EAAC,gCAAgC;YAAAD,QAAA,EAAC;UAElD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAERtE,OAAA;YAAKkE,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChCjE,OAAA;cACEkE,SAAS,EAAC,wDAAwD;cAClEK,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAAC6D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/D,QAAQ,GAAG,CAAC,CAAC,CAAE;cACtDgE,QAAQ,EAAE9D,eAAe,IAAIF,QAAQ,IAAI,CAAE;cAAA6C,QAAA,EAC5C;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtE,OAAA;cAAKkE,SAAS,EAAC,gDAAgD;cAAAD,QAAA,EAC5D7C;YAAQ;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNtE,OAAA;cACEkE,SAAS,EAAC,wDAAwD;cAClEK,OAAO,EAAEA,CAAA,KAAMlD,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAE;cACzCgE,QAAQ,EAAE9D,eAAgB;cAAA2C,QAAA,EAC3B;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKkE,SAAS,EAAC,MAAM;UAAAD,QAAA,gBACnBjE,OAAA;YAAKkE,SAAS,EAAC,oDAAoD;YAAAD,QAAA,gBACjEjE,OAAA;cAAAiE,QAAA,EAAM;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5BtE,OAAA;cAAAiE,QAAA,EAAON,cAAc,CAAC,KAAK;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACNtE,OAAA;YAAKkE,SAAS,EAAC,oDAAoD;YAAAD,QAAA,gBACjEjE,OAAA;cAAAiE,QAAA,EAAM;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACtBtE,OAAA;cAAAiE,QAAA,EAAO7C;YAAQ;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACNtE,OAAA;YAAKkE,SAAS,EAAC,yCAAyC;YAAAD,QAAA,gBACtDjE,OAAA;cAAAiE,QAAA,EAAM;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBtE,OAAA;cAAAiE,QAAA,EAAON,cAAc,CAACvC,QAAQ,GAAG,KAAK;YAAC;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtE,OAAA;UAAKkE,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzCjE,OAAA,CAACL,MAAM;YACL0F,OAAO,EAAC,WAAW;YACnBd,OAAO,EAAEA,CAAA,KAAMpD,oBAAoB,CAAC,KAAK,CAAE;YAC3CiE,QAAQ,EAAE9D,eAAgB;YAAA2C,QAAA,EAC3B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtE,OAAA,CAACL,MAAM;YACL4E,OAAO,EAAEpC,mBAAoB;YAC7BiD,QAAQ,EAAE9D,eAAgB;YAAA2C,QAAA,EAEzB3C,eAAe,GAAG,eAAe,GAAG;UAAU;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAClE,EAAA,CAnTID,OAAiB;EAAA,QACJN,OAAO;AAAA;AAAAyF,EAAA,GADpBnF,OAAiB;AAqTvB,eAAeA,OAAO;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}