{"version": 3, "file": "static/js/967.4589c43d.chunk.js", "mappings": "0MAqCA,MAsfA,EAtfiCA,KAC/B,MAAOC,EAAUC,IAAeC,EAAAA,EAAAA,UAAoB,KAC7CC,EAASC,IAAcF,EAAAA,EAAAA,WAAkB,IACzCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAC3CK,EAAiBC,IAAsBN,EAAAA,EAAAA,UAAyB,OAChEO,EAAiBC,IAAsBR,EAAAA,EAAAA,UAA2B,KAClES,EAAiBC,IAAsBV,EAAAA,EAAAA,WAAkB,IACzDW,EAAcC,IAAmBZ,EAAAA,EAAAA,UAAiB,IAClDa,EAAeC,IAAoBd,EAAAA,EAAAA,UAAiB,IAIpDe,EAAYC,IAAiBhB,EAAAA,EAAAA,UAAyB,CAC3DiB,KAAM,GACNC,YAAa,GACbC,MAAO,CACL,CACEC,MAAO,QACPC,SAAU,WACVC,MAAO,OAMPC,EAA+F,CACnG,CAAEC,GAAI,QAASP,KAAM,QAASQ,KAAM,UACpC,CAAED,GAAI,YAAaP,KAAM,aAAcQ,KAAM,UAC7C,CAAED,GAAI,WAAYP,KAAM,YAAaQ,KAAM,UAC3C,CAAED,GAAI,UAAWP,KAAM,UAAWQ,KAAM,UACxC,CAAED,GAAI,aAAcP,KAAM,mBAAoBQ,KAAM,QACpD,CAAED,GAAI,cAAeP,KAAM,oBAAqBQ,KAAM,QACtD,CAAED,GAAI,OAAQP,KAAM,OAAQQ,KAAM,OAClC,CAAED,GAAI,SAAUP,KAAM,SAAUQ,KAAM,UACtC,CAAED,GAAI,SAAUP,KAAM,SAAUQ,KAAM,UACtC,CAAED,GAAI,UAAWP,KAAM,UAAWQ,KAAM,WAIpCC,EAAyBC,IAC7B,MAAMP,EAAQG,EAAgBK,MAAKC,GAAKA,EAAEL,KAAOG,IAGjD,QAFuB,OAALP,QAAK,IAALA,OAAK,EAALA,EAAOK,OAAQ,UAG/B,IAAK,SACL,IAAK,MACL,IAAK,SACH,MAAO,CACL,CAAED,GAAI,SAAUP,KAAM,UACtB,CAAEO,GAAI,WAAYP,KAAM,YACxB,CAAEO,GAAI,aAAcP,KAAM,eAC1B,CAAEO,GAAI,WAAYP,KAAM,aACxB,CAAEO,GAAI,YAAaP,KAAM,kBACzB,CAAEO,GAAI,iBAAkBP,KAAM,qBAElC,IAAK,OACH,MAAO,CACL,CAAEO,GAAI,SAAUP,KAAM,uBACtB,CAAEO,GAAI,QAASP,KAAM,sBAErB,CAAEO,GAAI,WAAYP,KAAM,wBACxB,CAAEO,GAAI,WAAYP,KAAM,yBAE5B,QACE,MAAO,CACL,CAAEO,GAAI,SAAUP,KAAM,UACtB,CAAEO,GAAI,WAAYP,KAAM,aAE9B,GAIFa,EAAAA,EAAAA,YAAU,KACcC,WACpB7B,GAAW,GACXE,EAAS,MACT,IACE,MAAM4B,QAAiBC,EAAAA,GAAeC,kBAClCF,EAASG,QACXpC,EAAYiC,EAASI,MAErBhC,EAAS4B,EAASK,SAAW,2BAEjC,CAAE,MAAOC,GACPlC,EAASkC,EAAID,SAAW,4CAC1B,CAAC,QACCnC,GAAW,EACb,GAGFqC,EAAe,GACd,KAGHT,EAAAA,EAAAA,YAAU,KACR,IAAKzB,EAGD,OAFAG,EAAmB,SACnBM,EAAiB,GAIQiB,WAC3BrB,GAAmB,GACnB,IACE,MAAMsB,QAAiBC,EAAAA,GAAeO,mBACpCnC,EAAgBmB,GAChBb,EAjGgB,IAqGdqB,EAASG,SACX3B,EAAmBwB,EAASI,KAAKK,UACjC3B,EAAiBkB,EAASI,KAAKM,SAE5BC,QAAQxC,MAAM,oCAAqC6B,EAASK,SAC5DjC,EAAS,sDACTI,EAAmB,IACnBM,EAAiB,GAExB,CAAE,MAAOwB,GACPK,QAAQxC,MAAM,mCAAoCmC,GAClDlC,EAASkC,EAAID,SAAW,8CACxB7B,EAAmB,IACnBM,EAAiB,EACnB,CAAC,QACCJ,GAAmB,EACrB,GAGFkC,EAAsB,GAErB,CAACvC,EAAiBM,IAGrB,MA+EMkC,EAAmBA,CAACC,EAAeC,EAA6BzB,KACpE,MAAM0B,EAAejC,EAAWI,MAAM8B,KAAI,CAACC,EAAMC,KAC7C,GAAIA,IAAML,EAAO,CACb,MAAMM,EAAc,IAAKF,EAAM,CAACH,GAAWzB,GAK3C,MAHiB,UAAbyB,IACAK,EAAY/B,SAAWK,EAAsBJ,GAAO,GAAGE,IAEpD4B,CACX,CACA,OAAOF,CAAI,IAGflC,EAAc,IAAKD,EAAYI,MAAO6B,GAAe,EAoCjDK,EAAoBC,KAAKC,KAAK1C,EA7PZ,IAgQlB2C,EAAoBC,IAClBA,GAAW,GAAKA,GAAWJ,GAC3BzC,EAAgB6C,EACpB,EAIJ,OAEIC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACpDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SAAC,uBAE3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,+DAIxCzD,IACE0D,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACrC,KAAK,QAAQY,QAASlC,EAAO4D,QAASA,IAAM3D,EAAS,MAAOuD,UAAU,UAGhFD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACL,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,wBACtFF,EAAAA,EAAAA,MAAA,QAAMO,SA5JYlC,UAG1B,GAFAmC,EAAEC,iBAEGpD,EAAWE,KAAKmD,OAKrB,GAAIrD,EAAWI,MAAMkD,MAAKnB,IAASA,EAAK5B,MAAM8C,SAC5ChE,EAAS,sCADX,CAKAF,GAAW,GACXE,EAAS,MAET,IACE,MAAM4B,QAAiBC,EAAAA,GAAeqC,cAAcvD,GAEhDiB,EAASG,SAEXpC,EAAY,IAAID,EAAUkC,EAASI,OAEnCpB,EAAc,CACZC,KAAM,GACNC,YAAa,GACbC,MAAO,CACL,CACEC,MAAO,QACPC,SAAU,WACVC,MAAO,QAKblB,EAAS4B,EAASK,SAAW,2BAEjC,CAAE,MAAOC,GACPlC,EAASkC,EAAID,SAAW,2CAC1B,CAAC,QACCnC,GAAW,EACb,CA9BA,MAPEE,EAAS,2BAqCX,EAmH6CuD,UAAU,gBAAeC,SAAA,EAC5DC,EAAAA,EAAAA,KAACU,EAAAA,EAAK,CACHC,MAAM,eACNhD,GAAG,cACHP,KAAK,cACLK,MAAOP,EAAWE,KAClBwD,SAAWP,GAAqClD,EAAc,IAAKD,EAAYE,KAAMiD,EAAEQ,OAAOpD,QAC9FqD,YAAY,uCACZC,UAAQ,KAEXlB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,SAAOgB,QAAQ,qBAAqBlB,UAAU,qDAAoDC,SAAC,4BAGnGC,EAAAA,EAAAA,KAAA,YACIrC,GAAG,qBACHP,KAAK,qBACL6D,KAAM,EACNxD,MAAOP,EAAWG,YAClBuD,SAAWP,GAAwClD,EAAc,IAAKD,EAAYG,YAAagD,EAAEQ,OAAOpD,QACxGqD,YAAY,mCACZhB,UAAU,wMAKjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0CAAyCC,SAAC,yCACvD7C,EAAWI,MAAM8B,KAAI,CAACC,EAAMJ,KAC1BY,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,yEAAwEC,SAAA,CAE/F7C,EAAWI,MAAM4D,OAAS,IACvBlB,EAAAA,EAAAA,KAAA,UACIpC,KAAK,SACLuD,QAASA,IA1HZlC,KAExB,GAAI/B,EAAWI,MAAM4D,QAAU,EAE3B,YADA3E,EAAS,0CAGb,MAAM4C,EAAejC,EAAWI,MAAM8D,QAAO,CAACC,EAAG/B,IAAMA,IAAML,IAC7D9B,EAAc,IAAKD,EAAYI,MAAO6B,GAAe,EAmHXmC,CAAiBrC,GAChCa,UAAU,+GACVyB,MAAM,cAAaxB,UAEpBC,EAAAA,EAAAA,KAAA,OAAKwB,MAAM,6BAA6B1B,UAAU,UAAU2B,KAAK,OAAOC,QAAQ,YAAYC,OAAO,eAAeC,YAAa,EAAE7B,UAC/HC,EAAAA,EAAAA,KAAA,QAAM6B,cAAc,QAAQC,eAAe,QAAQC,EAAE,8BAK9DlC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOgB,QAAS,cAAc/B,IAASa,UAAU,qDAAoDC,SAAC,WACtGC,EAAAA,EAAAA,KAAA,UACGrC,GAAI,cAAcsB,IAClB7B,KAAM,cAAc6B,IACpBxB,MAAO4B,EAAK9B,MACZqD,SAAWP,GAAsCrB,EAAiBC,EAAO,QAASoB,EAAEQ,OAAOpD,OAC3FqC,UAAU,6KAA4KC,SAErLrC,EAAgB0B,KAAIpB,IACjBgC,EAAAA,EAAAA,KAAA,UAAmBvC,MAAOO,EAAEL,GAAGoC,SAAE/B,EAAEZ,MAAtBY,EAAEL,YAI1BkC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOgB,QAAS,iBAAiB/B,IAASa,UAAU,qDAAoDC,SAAC,cACzGC,EAAAA,EAAAA,KAAA,UACGrC,GAAI,iBAAiBsB,IACrB7B,KAAM,iBAAiB6B,IACvBxB,MAAO4B,EAAK7B,SACZoD,SAAWP,GAAsCrB,EAAiBC,EAAO,WAAYoB,EAAEQ,OAAOpD,OAC9FqC,UAAU,6KAA4KC,SAErLlC,EAAsBwB,EAAK9B,OAAO6B,KAAI4C,IACnChC,EAAAA,EAAAA,KAAA,UAAoBvC,MAAOuE,EAAGrE,GAAGoC,SAAEiC,EAAG5E,MAAzB4E,EAAGrE,YAI3BqC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACtBC,EAAAA,EAAAA,KAACU,EAAAA,EAAK,CACJC,MAAM,QACNhD,GAAI,cAAcsB,IAClB7B,KAAM,cAAc6B,IACpBxB,MAAO4B,EAAK5B,MACZmD,SAAWP,GAAqCrB,EAAiBC,EAAO,QAASoB,EAAEQ,OAAOpD,OAC1FsD,UAAQ,EACRD,YAAY,0BApDhB7B,MA0Dbe,EAAAA,EAAAA,KAACiC,EAAAA,EAAM,CAACrE,KAAK,SAASsE,QAAQ,YAAYC,KAAK,KAAKhB,QA9L7CiB,KACpBjF,EAAc,IACTD,EACHI,MAAO,IACFJ,EAAWI,MACd,CACEC,MAAO,QACPC,SAAUK,EAAsB,SAAS,GAAGF,GAC5CF,MAAO,MAGX,EAmLsFsC,SAAC,mBAK/EC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,KAACiC,EAAAA,EAAM,CAACrE,KAAK,SAASyE,SAAUjG,GAAuC,IAA5Bc,EAAWI,MAAM4D,OAAanB,SACtE3D,EAAU,cAAgB,4BAOpCyD,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACL,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,sBACpF3D,GAA+B,IAApBH,EAASiF,QACjBlB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,wBACjC,IAApB9D,EAASiF,QACTlB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SAAC,8BAEtDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnE9D,EAASmD,KAAKkD,IACbzC,EAAAA,EAAAA,MAAA,MAEEC,UAAW,0CAAuD,OAAftD,QAAe,IAAfA,OAAe,EAAfA,EAAiBmB,MAAO2E,EAAQ3E,GAAK,cAAgB,IACxGwD,QAASA,IAhOGmB,MACX,OAAf9F,QAAe,IAAfA,OAAe,EAAfA,EAAiBmB,MAAO2E,EAAQ3E,KACpClB,EAAmB6F,GACnBvF,EAAgB,GAAE,EA6NewF,CAAuBD,GAASvC,SAAA,EAE9CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DAA4DC,SAAEuC,EAAQlF,YAExDoF,IAAzBF,EAAQG,eACN5C,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gEAA+DC,SAAA,CAC1EuC,EAAQG,aAAa,mBAIhCzC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAEuC,EAAQjF,gBAb9DiF,EAAQ3E,YAqBzBkC,EAAAA,EAAAA,MAACM,EAAAA,EAAI,CAACL,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uEAAsEC,SAAC,gCACrFC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACjBvD,GACCqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAEvD,EAAgBY,QAC5E4C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEvD,EAAgBa,aAAe,uBAEpF2C,EAAAA,EAAAA,KAACiC,EAAAA,EAAM,CAACC,QAAQ,SAASC,KAAK,KAAKhB,QA5M9BjD,UAC1B,GAAK1B,GAGAkG,OAAOC,QAAQ,gDAAgDnG,EAAgBY,wCAApF,CAIAf,GAAW,GACXE,EAAS,MACT,IACE,MAAM4B,QAAiBC,EAAAA,GAAewE,cAAcpG,EAAgBmB,IAEhEQ,EAASG,SAEXpC,GAAY2G,GAAgBA,EAAazB,QAAOkB,GAAWA,EAAQ3E,KAAOnB,EAAgBmB,OAE1FlB,EAAmB,MACnBE,EAAmB,IACnBM,EAAiB,IAEfV,EAAS4B,EAASK,SAAW,2BAEnC,CAAE,MAAOC,GACPlC,EAASkC,EAAID,SAAW,gDACxBM,QAAQxC,MAAM,0BAA2BmC,EAC3C,CAAC,QACGpC,GAAW,EACf,CAtBA,CAsBA,EAgLsFgG,SAAUjG,EAAQ2D,SAC9E3D,EAAU,cAAgB,eAGnC4D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oDAAmDC,SAAC,YACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8FAA6FC,SACvGvD,EAAgBc,MAAM8B,KAAI,CAACC,EAAMJ,KAC9Be,EAAAA,EAAAA,KAAA,MAAgBF,UAAU,YAAWC,SAAE,GAAGV,EAAK9B,SAAS8B,EAAK7B,aAAa6B,EAAK5B,UAAtEwB,WAMnBY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qCAAoCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6CAA4CC,SAAA,CAAC,wBAAsB/C,EAAc,OAC7FJ,GACCoD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uCAAsCC,SAAC,wBACzB,IAA3BrD,EAAgBwE,QAClBlB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDAAqDC,SAAC,qCAEjEF,EAAAA,EAAAA,MAAAiD,EAAAA,SAAA,CAAA/C,SAAA,CAAE,KACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gGAA+FC,SAC1GrD,EAAgB0C,KAAI2D,IAClB/C,EAAAA,EAAAA,KAAA,MAAqBF,UAAU,YAAWC,UACvCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAqCyB,MAAOwB,EAAQC,MAAMjD,SAAEgD,EAAQC,SAD3ED,EAAQpF,QAOrB6B,EAAoB,IACjBK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qEAAoEC,SAAA,EAC/EF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,QAAMjD,EAAa,OAAK0C,MAC9BK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACtBC,EAAAA,EAAAA,KAACiC,EAAAA,EAAM,CACHE,KAAK,KACLD,QAAQ,YACRf,QAASA,IAAMxB,EAAiB7C,EAAe,GAC/CuF,SAAUvF,GAAgB,GAAKF,EAAgBmD,SAClD,cAGDC,EAAAA,EAAAA,KAACiC,EAAAA,EAAM,CACHE,KAAK,KACLD,QAAQ,YACRf,QAASA,IAAMxB,EAAiB7C,EAAe,GAC/CuF,SAAUvF,GAAgB0C,GAAqB5C,EAAgBmD,SAClE,yBAW1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCAAwCC,SAAC,0EAMpE,C", "sources": ["pages/SegmentBuilder.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  FormEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button'; // Assuming components exist\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport { segmentService } from '../services';\n\n// Define interfaces if needed\ninterface SegmentRule {\n    field: string;\n    operator: string;\n    value: string;\n}\n\ninterface NewSegmentData {\n    name: string;\n    description: string;\n    rules: SegmentRule[];\n}\n\ninterface Segment extends NewSegmentData {\n    id: string;\n    contactCount?: number; // Optional contact count\n}\n\ninterface SegmentContact {\n    id: string;\n    email: string;\n    // Add other contact properties if available\n}\n\nconst SegmentBuilder: React.FC = () => {\n  const [segments, setSegments] = useState<Segment[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedSegment, setSelectedSegment] = useState<Segment | null>(null);\n  const [segmentContacts, setSegmentContacts] = useState<SegmentContact[]>([]);\n  const [contactsLoading, setContactsLoading] = useState<boolean>(false);\n  const [contactsPage, setContactsPage] = useState<number>(1);\n  const [totalContacts, setTotalContacts] = useState<number>(0);\n  const contactsPerPage = 20;\n  \n  // New segment form state\n  const [newSegment, setNewSegment] = useState<NewSegmentData>({\n    name: '',\n    description: '',\n    rules: [\n      {\n        field: 'email',\n        operator: 'contains',\n        value: ''\n      }\n    ]\n  });\n  \n  // Available fields for segmentation\n  const availableFields: { id: string, name: string, type?: 'string' | 'date' | 'tag' | 'status' }[] = [\n    { id: 'email', name: 'Email', type: 'string' },\n    { id: 'firstName', name: 'First Name', type: 'string' },\n    { id: 'lastName', name: 'Last Name', type: 'string' },\n    { id: 'company', name: 'Company', type: 'string' },\n    { id: 'lastOpened', name: 'Last Opened Date', type: 'date' },\n    { id: 'lastClicked', name: 'Last Clicked Date', type: 'date' },\n    { id: 'tags', name: 'Tags', type: 'tag' }, // Example type for tags\n    { id: 'status', name: 'Status', type: 'status' }, // Example type for status\n    { id: 'source', name: 'Source', type: 'string' },\n    { id: 'country', name: 'Country', type: 'string' }\n  ];\n  \n  // Available operators based on field type\n  const getAvailableOperators = (fieldId: string): { id: string, name: string }[] => {\n    const field = availableFields.find(f => f.id === fieldId);\n    const fieldType = field?.type || 'string';\n\n    switch (fieldType) {\n      case 'string':\n      case 'tag':\n      case 'status': // Status might just use equals\n        return [\n          { id: 'equals', name: 'Equals' },\n          { id: 'contains', name: 'Contains' },\n          { id: 'startsWith', name: 'Starts With' },\n          { id: 'endsWith', name: 'Ends With' },\n          { id: 'notEquals', name: 'Does Not Equal' },\n          { id: 'doesNotContain', name: 'Does Not Contain' }\n        ];\n      case 'date':\n        return [\n          { id: 'before', name: 'Before (YYYY-MM-DD)' },\n          { id: 'after', name: 'After (YYYY-MM-DD)' },\n          // { id: 'between', name: 'Between' }, // Requires two inputs\n          { id: 'moreThan', name: 'More Than X Days Ago' },\n          { id: 'lessThan', name: 'Less Than X Days Ago' }\n        ];\n      default:\n        return [\n          { id: 'equals', name: 'Equals' },\n          { id: 'contains', name: 'Contains' }\n        ];\n    }\n  };\n\n  // Fetch segments on component mount\n  useEffect(() => {\n    const fetchSegments = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const response = await segmentService.getUserSegments();\n        if (response.success) {\n          setSegments(response.data);\n        } else {\n          setError(response.message || 'Failed to fetch segments');\n        }\n      } catch (err: any) {\n        setError(err.message || 'An error occurred while fetching segments');\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    fetchSegments();\n  }, []);\n  \n  // Fetch segment contacts when a segment is selected or page changes\n  useEffect(() => {\n    if (!selectedSegment) {\n        setSegmentContacts([]);\n        setTotalContacts(0);\n        return;\n    }\n    \n    const fetchSegmentContacts = async () => {\n      setContactsLoading(true);\n      try {\n        const response = await segmentService.getSegmentContacts(\n          selectedSegment.id,\n          contactsPage,\n          contactsPerPage // Use constant\n        );\n        \n        if (response.success) {\n          setSegmentContacts(response.data.contacts);\n          setTotalContacts(response.data.total);\n        } else {\n             console.error('Failed to fetch segment contacts:', response.message);\n             setError('Failed to fetch contacts for the selected segment.');\n             setSegmentContacts([]);\n             setTotalContacts(0);\n        }\n      } catch (err: any) { // Type err\n        console.error('Error fetching segment contacts:', err);\n        setError(err.message || 'An error occurred while fetching contacts.');\n        setSegmentContacts([]);\n        setTotalContacts(0);\n      } finally {\n        setContactsLoading(false);\n      }\n    };\n    \n    fetchSegmentContacts();\n    // Dependency array includes selectedSegment and contactsPage\n  }, [selectedSegment, contactsPage]);\n  \n  // Handle creating a new segment\n  const handleCreateSegment = async (e: FormEvent) => {\n    e.preventDefault();\n    \n    if (!newSegment.name.trim()) {\n      setError('Segment name is required');\n      return;\n    }\n    \n    if (newSegment.rules.some(rule => !rule.value.trim())) {\n      setError('All rule values must be filled');\n      return;\n    }\n    \n    setLoading(true);\n    setError(null);\n    \n    try {\n      const response = await segmentService.createSegment(newSegment);\n      \n      if (response.success) {\n        // Add new segment to state\n        setSegments([...segments, response.data]);\n        // Reset form\n        setNewSegment({\n          name: '',\n          description: '',\n          rules: [\n            {\n              field: 'email',\n              operator: 'contains',\n              value: ''\n            }\n          ]\n        });\n      } else {\n        setError(response.message || 'Failed to create segment');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating segment');\n    } finally {\n      setLoading(false);\n    }\n  };\n  \n  // Handle segment selection\n  const handleSegmentSelection = (segment: Segment) => {\n    if (selectedSegment?.id === segment.id) return; // Avoid re-selecting same segment\n    setSelectedSegment(segment);\n    setContactsPage(1); // Reset page when changing segment\n    // Contacts will be fetched by the useEffect hook\n  };\n  \n  // Handle adding a new rule\n  const handleAddRule = () => {\n    setNewSegment({\n      ...newSegment,\n      rules: [\n        ...newSegment.rules,\n        {\n          field: 'email', // Default new rule field\n          operator: getAvailableOperators('email')[0].id, // Default operator for email\n          value: ''\n        }\n      ]\n    });\n  };\n  \n  // Handle removing a rule\n  const handleRemoveRule = (index: number) => {\n    // Prevent removing the last rule\n    if (newSegment.rules.length <= 1) {\n        setError(\"A segment must have at least one rule.\");\n        return;\n    }\n    const updatedRules = newSegment.rules.filter((_, i) => i !== index);\n    setNewSegment({ ...newSegment, rules: updatedRules });\n  };\n  \n  // Handle updating a rule\n  const handleRuleChange = (index: number, property: keyof SegmentRule, value: string) => {\n    const updatedRules = newSegment.rules.map((rule, i) => {\n        if (i === index) {\n            const updatedRule = { ...rule, [property]: value };\n            // If the field changed, update the operator to the default for the new field type\n            if (property === 'field') {\n                updatedRule.operator = getAvailableOperators(value)[0].id;\n            }\n            return updatedRule;\n        }\n        return rule;\n    });\n\n    setNewSegment({ ...newSegment, rules: updatedRules });\n  };\n  \n  // Handle deleting a segment\n  const handleDeleteSegment = async () => {\n    if (!selectedSegment) return;\n    \n    // Consider using a modal for confirmation instead of window.confirm\n    if (!window.confirm(`Are you sure you want to delete the segment \"${selectedSegment.name}\"? This action cannot be undone.`)) {\n      return;\n    }\n    \n    setLoading(true); // Indicate loading state during delete\n    setError(null);\n    try {\n      const response = await segmentService.deleteSegment(selectedSegment.id);\n      \n      if (response.success) {\n        // Remove segment from state\n        setSegments(prevSegments => prevSegments.filter(segment => segment.id !== selectedSegment.id));\n        // Clear selected segment\n        setSelectedSegment(null);\n        setSegmentContacts([]); // Clear contacts\n        setTotalContacts(0);\n      } else {\n          setError(response.message || 'Failed to delete segment');\n      }\n    } catch (err: any) { // Type error\n      setError(err.message || 'An error occurred while deleting the segment');\n      console.error('Error deleting segment:', err);\n    } finally {\n        setLoading(false);\n    }\n  };\n\n  // Calculate total pages for contacts\n  const totalContactPages = Math.ceil(totalContacts / contactsPerPage);\n\n  // Function to handle pagination change\n  const handlePageChange = (newPage: number) => {\n      if (newPage >= 1 && newPage <= totalContactPages) {\n          setContactsPage(newPage);\n      }\n  }\n\n  // Remove Sidebar wrapper\n  return (\n    // <Sidebar> // Removed\n      <div className=\"container mx-auto px-4 py-6\"> \n        <div className=\"flex justify-between items-center mb-6\">\n           <h1 className=\"text-2xl font-semibold text-text-primary\">Segment Builder</h1>\n        </div>\n         <p className=\"text-text-secondary mb-6\">\n            Create dynamic contact segments based on various criteria.\n         </p>\n        \n        {error && (\n           <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n        )}\n        \n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\"> \n          {/* Create new segment form - Column 1 */}\n          <Card className=\"lg:col-span-1\"> \n             <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Create New Segment</h2>\n            <form onSubmit={handleCreateSegment} className=\"p-4 space-y-4\">\n              <Input\n                 label=\"Segment Name\"\n                 id=\"segmentName\"\n                 name=\"segmentName\"\n                 value={newSegment.name}\n                 onChange={(e: ChangeEvent<HTMLInputElement>) => setNewSegment({ ...newSegment, name: e.target.value })}\n                 placeholder=\"e.g., Engaged Newsletter Subscribers\"\n                 required\n              />\n              <div>\n                 <label htmlFor=\"segmentDescription\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                     Description (Optional)\n                 </label>\n                 <textarea\n                     id=\"segmentDescription\"\n                     name=\"segmentDescription\"\n                     rows={2}\n                     value={newSegment.description}\n                     onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setNewSegment({ ...newSegment, description: e.target.value })}\n                     placeholder=\"Brief description of the segment\"\n                     className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500\"\n                  />\n              </div>\n              \n              {/* Rules Builder */}\n              <div className=\"space-y-3 pt-2\">\n                 <h3 className=\"text-md font-medium text-text-secondary\">Rules (All must be true - AND logic)</h3>\n                 {newSegment.rules.map((rule, index) => (\n                    <div key={index} className=\"p-3 border border-gray-700 rounded-md space-y-2 relative bg-background\">\n                       {/* Remove button */} \n                       {newSegment.rules.length > 1 && (\n                           <button \n                               type=\"button\"\n                               onClick={() => handleRemoveRule(index)}\n                               className=\"absolute top-1 right-1 text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-700 transition-colors\"\n                               title=\"Remove rule\"\n                            >\n                              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" strokeWidth={2}>\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n                              </svg>\n                           </button>\n                       )}\n                       \n                       <div className=\"grid grid-cols-3 gap-2\">\n                           <div className=\"col-span-1\">\n                               <label htmlFor={`rule-field-${index}`} className=\"block text-xs font-medium text-text-secondary mb-1\">Field</label>\n                               <select\n                                  id={`rule-field-${index}`}\n                                  name={`rule-field-${index}`}\n                                  value={rule.field}\n                                  onChange={(e: ChangeEvent<HTMLSelectElement>) => handleRuleChange(index, 'field', e.target.value)}\n                                  className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                               >\n                                  {availableFields.map(f => (\n                                      <option key={f.id} value={f.id}>{f.name}</option>\n                                  ))}\n                               </select>\n                           </div>\n                           <div className=\"col-span-1\">\n                               <label htmlFor={`rule-operator-${index}`} className=\"block text-xs font-medium text-text-secondary mb-1\">Operator</label>\n                               <select\n                                  id={`rule-operator-${index}`}\n                                  name={`rule-operator-${index}`}\n                                  value={rule.operator}\n                                  onChange={(e: ChangeEvent<HTMLSelectElement>) => handleRuleChange(index, 'operator', e.target.value)}\n                                  className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                               >\n                                  {getAvailableOperators(rule.field).map(op => (\n                                      <option key={op.id} value={op.id}>{op.name}</option>\n                                  ))}\n                               </select>\n                           </div>\n                           <div className=\"col-span-1\">\n                                <Input\n                                  label=\"Value\"\n                                  id={`rule-value-${index}`}\n                                  name={`rule-value-${index}`}\n                                  value={rule.value}\n                                  onChange={(e: ChangeEvent<HTMLInputElement>) => handleRuleChange(index, 'value', e.target.value)}\n                                  required\n                                  placeholder=\"Enter value...\"\n                               />\n                           </div>\n                       </div>\n                    </div>\n                 ))}\n                 <Button type=\"button\" variant=\"secondary\" size=\"sm\" onClick={handleAddRule}>\n                   + Add Rule\n                 </Button>\n              </div>\n               \n              <div className=\"flex justify-end pt-2\">\n                 <Button type=\"submit\" disabled={loading || newSegment.rules.length === 0}>\n                   {loading ? 'Creating...' : 'Create Segment'}\n                 </Button>\n              </div>\n            </form>\n          </Card>\n          \n          {/* Existing Segments List - Column 2 */} \n          <Card className=\"lg:col-span-1\"> \n             <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Existing Segments</h2>\n             {loading && segments.length === 0 ? (\n                 <div className=\"p-4 text-center text-text-secondary\">Loading segments...</div>\n             ) : segments.length === 0 ? (\n                 <div className=\"p-4 text-center text-text-secondary\">No segments created yet.</div>\n             ) : (\n                <ul className=\"divide-y divide-gray-700 max-h-[500px] overflow-y-auto\"> \n                  {segments.map((segment) => (\n                    <li \n                      key={segment.id} \n                      className={`p-4 hover:bg-gray-700 cursor-pointer ${selectedSegment?.id === segment.id ? 'bg-gray-700' : ''}`}\n                      onClick={() => handleSegmentSelection(segment)}\n                    >\n                       <div className=\"flex justify-between items-center\">\n                         <p className=\"text-sm font-medium text-text-primary truncate flex-1 mr-2\">{segment.name}</p>\n                          {/* Display contact count if available */} \n                          {segment.contactCount !== undefined && (\n                             <span className=\"text-xs text-blue-400 bg-blue-900/50 px-2 py-0.5 rounded-full\">\n                                 {segment.contactCount} Contacts\n                             </span>\n                          )} \n                       </div>\n                       <p className=\"text-xs text-text-secondary mt-1 truncate\">{segment.description}</p>\n                     </li>\n                  ))}\n                </ul>\n             )}\n          </Card>\n\n          {/* Segment Details & Contacts - Column 3 */} \n          <Card className=\"lg:col-span-1\"> \n             <h2 className=\"text-lg font-semibold text-text-primary p-4 border-b border-gray-700\">Segment Details & Contacts</h2>\n             <div className=\"p-4\">\n               {selectedSegment ? (\n                 <div className=\"space-y-4\">\n                   {/* Segment Info */} \n                   <div>\n                     <div className=\"flex justify-between items-start\">\n                         <div>\n                             <h3 className=\"text-md font-medium text-text-primary mb-1\">{selectedSegment.name}</h3>\n                             <p className=\"text-sm text-text-secondary mb-3\">{selectedSegment.description || 'No description'}</p>\n                         </div>\n                         <Button variant=\"danger\" size=\"sm\" onClick={handleDeleteSegment} disabled={loading}>\n                             {loading ? 'Deleting...' : 'Delete'}\n                         </Button>\n                     </div>\n                     <h4 className=\"text-sm font-medium text-text-secondary mb-1 mt-2\">Rules:</h4>\n                      <ul className=\"text-xs text-text-secondary space-y-1 bg-secondary-bg p-3 rounded-md border border-gray-700\">\n                         {selectedSegment.rules.map((rule, index) => (\n                             <li key={index} className=\"font-mono\">{`${rule.field} ${rule.operator} \"${rule.value}\"`}</li>\n                         ))}\n                     </ul>\n                   </div>\n                   \n                   {/* Contacts List */}\n                   <div className=\"mt-4 pt-4 border-t border-gray-700\">\n                      <h3 className=\"text-md font-medium text-text-primary mb-2\">Contacts in Segment ({totalContacts})</h3>\n                       {contactsLoading ? (\n                         <div className=\"text-center text-text-secondary py-4\">Loading contacts...</div>\n                       ) : segmentContacts.length === 0 ? (\n                         <p className=\"text-sm text-text-secondary italic text-center py-4\">No contacts match this segment.</p>\n                       ) : (\n                           <> {/* Fragment needed */} \n                               <ul className=\"divide-y divide-gray-700 max-h-60 overflow-y-auto pr-2 mb-4 border border-gray-700 rounded-md\">\n                                 {segmentContacts.map(contact => (\n                                    <li key={contact.id} className=\"py-2 px-3\">\n                                       <p className=\"text-sm text-text-primary truncate\" title={contact.email}>{contact.email}</p>\n                                       {/* Optionally display name or other info */}\n                                    </li> \n                                 ))}\n                               </ul>\n                               {/* Pagination for contacts - Basic implementation */} \n                               {totalContactPages > 1 && (\n                                   <div className=\"flex justify-between items-center mt-2 text-sm text-text-secondary\">\n                                       <span>Page {contactsPage} of {totalContactPages}</span>\n                                       <div className=\"space-x-1\">\n                                           <Button\n                                               size=\"sm\"\n                                               variant=\"secondary\"\n                                               onClick={() => handlePageChange(contactsPage - 1)}\n                                               disabled={contactsPage <= 1 || contactsLoading}\n                                           >\n                                               Previous\n                                           </Button>\n                                           <Button\n                                               size=\"sm\"\n                                               variant=\"secondary\"\n                                               onClick={() => handlePageChange(contactsPage + 1)}\n                                               disabled={contactsPage >= totalContactPages || contactsLoading}\n                                           >\n                                               Next\n                                           </Button>\n                                       </div>\n                                   </div>\n                               )}\n                           </> \n                       )}\n                   </div>\n                 </div>\n               ) : (\n                  <p className=\"text-text-secondary italic text-center\">Select a segment from the list to see details and contacts.</p>\n               )}\n             </div>\n          </Card>\n        </div>\n      </div>\n    // </Sidebar> // Removed\n  );\n};\n\nexport default SegmentBuilder;\n"], "names": ["SegmentBuilder", "segments", "setSegments", "useState", "loading", "setLoading", "error", "setError", "selectedSegment", "setSelectedSegment", "segmentContacts", "setSegmentContacts", "contactsLoading", "setContactsLoading", "contactsPage", "setContactsPage", "totalContacts", "setTotalContacts", "newSegment", "setNewSegment", "name", "description", "rules", "field", "operator", "value", "availableFields", "id", "type", "getAvailableOperators", "fieldId", "find", "f", "useEffect", "async", "response", "segmentService", "getUserSegments", "success", "data", "message", "err", "fetchSegments", "getSegmentContacts", "contacts", "total", "console", "fetchSegmentContacts", "handleRuleChange", "index", "property", "updatedRules", "map", "rule", "i", "updatedRule", "totalContactPages", "Math", "ceil", "handlePageChange", "newPage", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON>", "onClose", "Card", "onSubmit", "e", "preventDefault", "trim", "some", "createSegment", "Input", "label", "onChange", "target", "placeholder", "required", "htmlFor", "rows", "length", "onClick", "filter", "_", "handleRemoveRule", "title", "xmlns", "fill", "viewBox", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "d", "op", "<PERSON><PERSON>", "variant", "size", "handleAddRule", "disabled", "segment", "handleSegmentSelection", "undefined", "contactCount", "window", "confirm", "deleteSegment", "prevSegments", "_Fragment", "contact", "email"], "sourceRoot": ""}