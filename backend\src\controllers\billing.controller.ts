import {
  NextFunction,
  Request,
  Response,
} from 'express';
import <PERSON><PERSON> from 'stripe';

import Transaction from '../models/integrations/integrationConnection.model';
import User from '../models/user.model';
import { SNSService } from '../services/aws.service';
import { createError } from '../utils/error.util';

// Initialize Stripe (Ensure STRIPE_SECRET_KEY is in your .env)
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY || '', { apiVersion: '2023-10-16' as any }); // Use a specific API version, added 'as any'

// Get user billing information
export const getBillingInfo = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) return next(createError(401, 'User not authenticated'));

    const user = await User.findById(userId);
    if (!user) return next(createError(404, 'User not found'));
    
    // Combine info from user model (e.g., plan) and Stripe (if needed)
    res.status(200).json({
      success: true,
      data: {
        plan: user.subscription?.plan || 'Free',
        status: user.subscription?.status || 'inactive',
        currentPeriodEnd: user.subscription?.currentPeriodEnd,
        stripeCustomerId: user.stripeCustomerId,
        // Add more fields as needed
      }
    });
  } catch (error: any) {
    console.error("Error getting billing info:", error);
    next(createError(500, (error as Error).message || 'Failed to get billing info'));
  }
};

// Mock billing history data
const mockBillingHistory = [
  {
    id: 'txn_1',
    type: 'purchase',
    amount: 10.00,
    flowsQuantity: 1,
    date: '2024-04-01T10:00:00Z',
    status: 'completed'
  },
  {
    id: 'txn_2',
    type: 'purchase',
    amount: 20.00,
    flowsQuantity: 2,
    date: '2024-03-15T14:30:00Z',
    status: 'completed'
  },
  {
    id: 'txn_3',
    type: 'free_trial',
    amount: 0.00,
    flowsQuantity: 1,
    date: '2024-03-01T09:15:00Z',
    status: 'completed'
  },
  {
    id: 'txn_4',
    type: 'purchase',
    amount: 50.00,
    flowsQuantity: 5,
    date: '2024-05-10T11:20:00Z',
    status: 'pending'
  }
];

export const getBillingHistory = async (req: Request, res: Response): Promise<Response> => {
  try {
    // In a real implementation, fetch this from a database based on the logged-in user (req.user.id)
    console.log('Fetching billing history for user:', (req as any).user?.id || 'Unknown User'); 
    
    // Simulate async operation
    await new Promise(resolve => setTimeout(resolve, 100)); 

    return res.status(200).json({ 
      success: true,
      message: 'Billing history retrieved successfully.',
      data: mockBillingHistory // Return the mock data array
    });
  } catch (error: any) {
    console.error('Error fetching billing history:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to retrieve billing history.',
      error: error.message 
    });
  }
};

// Purchase flows
export const purchaseFlows = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { quantity, paymentMethodId } = req.body;
    const userId = req.user?.id;
    
    if (!quantity || quantity < 1) {
      return next(createError(400, 'Invalid quantity'));
    }
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    // Calculate amount
    const pricePerFlow = 10.00;
    const amount = quantity * pricePerFlow;
    
    // In a real app, you would process the payment here
    // For now, we'll just create a transaction and update the user's flow count
    
    // Create transaction
    const transaction = new Transaction({
      userId,
      type: 'purchase',
      amount,
      flowsQuantity: quantity,
      status: 'completed',
      paymentMethodId
    });
    
    await transaction.save();
    
    // Update user's flow count
    user.flowsPurchased += quantity;
    await user.save();
    
    // Send notification via SNS
    if (process.env.SNS_TOPIC_ARN) {
      await SNSService.publishNotification({
        topicArn: process.env.SNS_TOPIC_ARN,
        message: `User ${user.email} purchased ${quantity} flow(s) for $${amount}`,
        subject: 'Flow Purchase'
      });
    }
    
    res.status(200).json({
      success: true,
      message: `Successfully purchased ${quantity} flow(s)`,
      data: {
        transaction,
        flowsPurchased: user.flowsPurchased,
        flowsUsed: user.flowsUsed || 0,
        flowsAvailable: user.flowsPurchased - (user.flowsUsed || 0)
      }
    });
  } catch (error) {
    next(error);
  }
};

// Add payment method
export const addPaymentMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { paymentMethodId, isDefault } = req.body;
    const userId = req.user?.id;
    
    if (!paymentMethodId) {
      return next(createError(400, 'Payment method ID is required'));
    }
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    // In a real app, you would validate the payment method with your payment processor
    // For now, we'll just add it to the user's payment methods
    
    // Check if payment method already exists
    const existingPaymentMethod = user.paymentMethods?.find(pm => pm.id === paymentMethodId);
    
    if (existingPaymentMethod) {
      return next(createError(400, 'Payment method already exists'));
    }
    
    // Add payment method
    if (!user.paymentMethods) {
      user.paymentMethods = [];
    }
    
    user.paymentMethods.push({
      id: paymentMethodId,
      isDefault: isDefault || user.paymentMethods.length === 0
    });
    
    // If this is the default payment method, update other payment methods
    if (isDefault) {
      user.paymentMethods.forEach(pm => {
        if (pm.id !== paymentMethodId) {
          pm.isDefault = false;
        }
      });
    }
    
    await user.save();
    
    res.status(200).json({
      success: true,
      message: 'Payment method added successfully',
      data: {
        paymentMethods: user.paymentMethods
      }
    });
  } catch (error) {
    next(error);
  }
};

// Remove payment method
export const removePaymentMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { paymentMethodId } = req.params;
    const userId = req.user?.id;
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    // Check if payment method exists
    if (!user.paymentMethods || !user.paymentMethods.find(pm => pm.id === paymentMethodId)) {
      return next(createError(404, 'Payment method not found'));
    }
    
    // Check if this is the default payment method
    const isDefault = user.paymentMethods.find(pm => pm.id === paymentMethodId)?.isDefault;
    
    // Remove payment method
    user.paymentMethods = user.paymentMethods.filter(pm => pm.id !== paymentMethodId);
    
    // If this was the default payment method, set a new default
    if (isDefault && user.paymentMethods.length > 0) {
      user.paymentMethods[0].isDefault = true;
    }
    
    await user.save();
    
    res.status(200).json({
      success: true,
      message: 'Payment method removed successfully',
      data: {
        paymentMethods: user.paymentMethods
      }
    });
  } catch (error) {
    next(error);
  }
};

// Set default payment method
export const setDefaultPaymentMethod = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { paymentMethodId } = req.params;
    const userId = req.user?.id;
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    // Check if payment method exists
    if (!user.paymentMethods || !user.paymentMethods.find(pm => pm.id === paymentMethodId)) {
      return next(createError(404, 'Payment method not found'));
    }
    
    // Update payment methods
    user.paymentMethods.forEach(pm => {
      pm.isDefault = pm.id === paymentMethodId;
    });
    
    await user.save();
    
    res.status(200).json({
      success: true,
      message: 'Default payment method updated successfully',
      data: {
        paymentMethods: user.paymentMethods
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get payment methods
export const getPaymentMethods = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const userId = req.user?.id;
    
    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }
    
    res.status(200).json({
      success: true,
      data: {
        paymentMethods: user.paymentMethods || []
      }
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to get Stripe Price ID based on plan name
const getPriceIdForPlan = (plan: string): string | null => {
  const planMap: { [key: string]: string | undefined } = {
    'basic': process.env.STRIPE_BASIC_PRICE_ID, 
    'pro': process.env.STRIPE_PRO_PRICE_ID,
    'enterprise': process.env.STRIPE_ENTERPRISE_PRICE_ID,
  };
  const priceId = planMap[plan.toLowerCase()];
  return priceId || null; // Return null if plan or env var is not found
};

// Create a new subscription
export const createSubscription = async (req: Request, res: Response, next: NextFunction) => {
  const { plan, paymentMethodId } = req.body;
  const userId = req.user?.id; 

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }
  if (!plan || !paymentMethodId) {
      return next(createError(400, 'Plan and Payment Method ID are required'));
  }

  try {
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    let customerId = user.stripeCustomerId;
    
    // Create or update Stripe customer
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        payment_method: paymentMethodId,
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
      customerId = customer.id;
      user.stripeCustomerId = customerId;
    } else {
        // Attach payment method and set as default for existing customer
        try {
            await stripe.paymentMethods.attach(paymentMethodId, { customer: customerId });
            await stripe.customers.update(customerId, {
                invoice_settings: { default_payment_method: paymentMethodId },
            });
        } catch (attachError: any) {
             console.error('Error attaching/updating payment method:', attachError);
             return next(createError(400, `Failed to set payment method: ${attachError.message}`));
        }
    }

    const priceId = getPriceIdForPlan(plan);
    if (!priceId) {
      return next(createError(400, `Invalid plan or price ID not configured for plan: ${plan}`));
    }

    // Check for existing active/trialing subscription to prevent duplicates
    if (user.subscription?.stripeSubscriptionId) {
        const existingSub = await stripe.subscriptions.retrieve(user.subscription.stripeSubscriptionId);
        if (['active', 'trialing', 'past_due'].includes(existingSub.status)) {
            return next(createError(400, 'User already has an active subscription.'));
        }
    }

    // Create Stripe subscription
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      expand: ['latest_invoice.payment_intent'],
      payment_behavior: 'default_incomplete', 
      proration_behavior: 'create_prorations',
      // trial_period_days: 14, // Optional: Add trial period if applicable
    });

    // Update user's subscription status in local DB
    user.subscription = {
      stripeSubscriptionId: subscription.id,
      plan: plan,
      status: subscription.status,
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    };
    await user.save();
    
    // Record transaction (optional, depends on needs)
    // ... (transaction creation logic) ...

    // Send notification (optional)
    // ... (SNS notification logic) ...

    res.status(201).json({
      success: true,
      message: 'Subscription created successfully',
      data: {
        subscriptionId: subscription.id,
        status: subscription.status,
        // Send client secret if payment requires action
        clientSecret: (subscription.latest_invoice as Stripe.Invoice)?.payment_intent ? 
                      ((subscription.latest_invoice as Stripe.Invoice).payment_intent as Stripe.PaymentIntent).client_secret : null
      }
    });

  } catch (error: any) {
    console.error('Stripe subscription creation error:', error);
    // Provide more specific Stripe error messages if available
    const stripeError = error as Stripe.errors.StripeError;
    next(createError(stripeError.statusCode || 500, stripeError.message || 'Failed to create subscription'));
  }
};

// Get user's current subscription details from Stripe
export const getSubscription = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user?.id;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }

  try {
    const user = await User.findById(userId);
    if (!user || !user.subscription?.stripeSubscriptionId) {
      return res.status(200).json({ success: true, message: 'No active subscription found', data: null });
    }

    const subscription = await stripe.subscriptions.retrieve(user.subscription.stripeSubscriptionId);

    // Optionally sync local DB status
    if (user.subscription.status !== subscription.status) {
        user.subscription.status = subscription.status;
        user.subscription.currentPeriodEnd = new Date(subscription.current_period_end * 1000);
        await user.save();
    }

    res.status(200).json({
      success: true,
      data: {
        id: subscription.id,
        plan: user.subscription.plan, // Get plan name from local DB
        status: subscription.status,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        // Include other useful details like price, quantity if needed
        price: (subscription.items.data[0]?.price.unit_amount || 0) / 100,
        currency: subscription.items.data[0]?.price.currency,
      }
    });

  } catch (error: any) {
    console.error('Get subscription error:', error);
    const stripeError = error as Stripe.errors.StripeError;
    next(createError(stripeError.statusCode || 500, stripeError.message || 'Failed to retrieve subscription'));
  }
};

// Cancel user's subscription (at period end)
export const cancelSubscription = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user?.id;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }

  try {
    const user = await User.findById(userId);
    if (!user || !user.subscription?.stripeSubscriptionId) {
      return next(createError(404, 'No active subscription found to cancel'));
    }

    // Cancel subscription at period end in Stripe
    const canceledSubscription = await stripe.subscriptions.update(
        user.subscription.stripeSubscriptionId,
        { cancel_at_period_end: true }
    );

    // Update user's subscription status locally
    user.subscription.status = 'canceled'; // Or use stripe status if preferred
    await user.save();
    
    // Record transaction (optional)
    // ... 
    
    // Send notification (optional)
    // ...

    res.status(200).json({
      success: true,
      message: 'Subscription scheduled for cancellation at period end',
      data: {
        status: canceledSubscription.status,
        cancelAtPeriodEnd: canceledSubscription.cancel_at_period_end,
        currentPeriodEnd: new Date(canceledSubscription.current_period_end * 1000),
      }
    });

  } catch (error: any) {
    console.error('Cancel subscription error:', error);
    const stripeError = error as Stripe.errors.StripeError;
    next(createError(stripeError.statusCode || 500, stripeError.message || 'Failed to cancel subscription'));
  }
};

// Update user's default payment method
export const updatePaymentMethod = async (req: Request, res: Response, next: NextFunction) => {
  const { paymentMethodId } = req.body;
  const userId = req.user?.id;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }
  if (!paymentMethodId) {
    return next(createError(400, 'Payment Method ID is required'));
  }

  try {
    const user = await User.findById(userId);
    if (!user || !user.stripeCustomerId) {
      return next(createError(404, 'Stripe customer not found for this user. Cannot update payment method.'));
    }

    // Attach the new payment method to the customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: user.stripeCustomerId,
    });

    // Update the customer's default payment method
    await stripe.customers.update(user.stripeCustomerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    res.status(200).json({
      success: true,
      message: 'Default payment method updated successfully'
    });

  } catch (error: any) {
    console.error('Update payment method error:', error);
    const stripeError = error as Stripe.errors.StripeError;
    next(createError(stripeError.statusCode || 500, stripeError.message || 'Failed to update payment method'));
  }
};

// Get user's invoices from Stripe
export const getInvoices = async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user?.id;
  const { limit = 10, starting_after, ending_before } = req.query;

  if (!userId) {
    return next(createError(401, 'User not authenticated'));
  }

  try {
    const user = await User.findById(userId);
    if (!user || !user.stripeCustomerId) {
      return res.status(200).json({ success: true, data: [], has_more: false }); // No customer, no invoices
    }

    const invoices = await stripe.invoices.list({
      customer: user.stripeCustomerId,
      limit: Number(limit),
      starting_after: starting_after as string | undefined,
      ending_before: ending_before as string | undefined,
    });

    res.status(200).json({
      success: true,
      data: invoices.data, // The list of invoices
      has_more: invoices.has_more // For pagination
    });

  } catch (error: any) {
    console.error('Get invoices error:', error);
    const stripeError = error as Stripe.errors.StripeError;
    next(createError(stripeError.statusCode || 500, stripeError.message || 'Failed to retrieve invoices'));
  }
};
