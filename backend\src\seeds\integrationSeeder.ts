import dotenv from 'dotenv';
import mongoose from 'mongoose';

import Integration from '../models/integrations/integration.model';

// Load environment variables
dotenv.config();

// Function to seed integrations
const seedIntegrations = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/driftly');
    console.log('Connected to MongoDB for integration seeding');

    // Check if integrations already exist
    const count = await Integration.countDocuments();
    if (count > 0) {
      console.log(`Skipping integration seeds - ${count} integrations already exist`);
      await mongoose.disconnect();
      return;
    }

    // Define the integrations to seed
    const integrations = [
      {
        id: 'mosaico',
        name: 'Mosaico',
        description: 'Integrate with Mosaico email editor for advanced email template design',
        category: 'email_marketing',
        icon: 'mosaico-icon.svg', // Update with actual icon path
        authType: 'api_key',
      },
      // Add other integrations here as needed
    ];

    // Insert the integrations
    await Integration.insertMany(integrations);
    console.log(`✅ ${integrations.length} integrations seeded successfully`);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('❌ Error seeding integrations:', error);
    await mongoose.disconnect();
    process.exit(1);
  }
};

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedIntegrations();
}

export default seedIntegrations; 