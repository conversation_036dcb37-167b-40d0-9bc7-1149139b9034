{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useNavigate}from'react-router-dom';import Button from'../components/Button';// import axios from 'axios'; // Remove axios if only using mock data\nimport Card from'../components/Card';import{Modal}from'../components/Modal';// Use named import\n// Import the service\nimport{templateRecommendationService}from'../services';// Type for categorized templates\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";// Helper function to get gradient class based on category\nconst getGradientForCategory=category=>{switch(category.toLowerCase()){case'newsletter':return'from-blue-500 to-indigo-600';case'promotion':return'from-purple-500 to-pink-600';case'welcome':return'from-green-400 to-teal-500';case'announcement':return'from-yellow-400 to-orange-500';case'e-commerce':return'from-red-500 to-rose-600';default:return'from-gray-600 to-gray-700';// Default fallback\n}};// --- SVG Icons (can be moved to a separate file later) ---\nconst NewsletterIcon=()=>/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"currentColor\",className:\"w-12 h-12 text-white opacity-50\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z\"})});const PromotionIcon=()=>/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"currentColor\",className:\"w-12 h-12 text-white opacity-50\",children:[/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z\"}),/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6 6h.008v.008H6V6Z\"})]});const WelcomeIcon=()=>/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"currentColor\",className:\"w-12 h-12 text-white opacity-50\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})});const AnnouncementIcon=()=>/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"currentColor\",className:\"w-12 h-12 text-white opacity-50\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535L19.5 17.11c.463-.304.646-.961.463-1.511a20.845 20.845 0 0 0-1.44-4.282m-3.102-.069a18.03 18.03 0 0 0-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18C9.36 16.33 8.5 17.644 8.5 19.5a2.25 2.25 0 0 0 4.5 0c0-1.09-.394-2.068-.998-2.818\"})});const EcommerceIcon=()=>/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"currentColor\",className:\"w-12 h-12 text-white opacity-50\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.106M15.75 5.106l-1.483 7.411M9.75 9.75l4.5 1.5m-4.5-1.5L12 9.75m-2.25 0L9.75 7.5M15 12l-2.25-1.5M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})});// Default/Fallback Icon\nconst DefaultTemplateIcon=()=>/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"currentColor\",className:\"w-12 h-12 text-white opacity-50\",children:[/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M11.35 3.836A1.5 1.5 0 0 1 12.183 3h9.634A1.5 1.5 0 0 1 23.317 4.183L19.817 7.683A1.5 1.5 0 0 1 19.183 8H2.817a1.5 1.5 0 0 1-1.317-.817L.683 4.183A1.5 1.5 0 0 1 1.817 3h9.534Z\"}),/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2 8v11a1.5 1.5 0 0 0 1.5 1.5h17A1.5 1.5 0 0 0 22 19V8M8 12h8M8 16h4\"})]});// --- Helper function to get icon based on category ---\nconst getIconForCategory=category=>{switch(category.toLowerCase()){case'newsletter':return/*#__PURE__*/_jsx(NewsletterIcon,{});case'promotion':case'promotional':// Added alias\nreturn/*#__PURE__*/_jsx(PromotionIcon,{});case'welcome':case'onboarding':// Added alias\nreturn/*#__PURE__*/_jsx(WelcomeIcon,{});case'announcement':return/*#__PURE__*/_jsx(AnnouncementIcon,{});case'e-commerce':return/*#__PURE__*/_jsx(EcommerceIcon,{});case'event':case'follow-up':case'transactional':case'marketing':// Catch-all for other generated types\ndefault:return/*#__PURE__*/_jsx(DefaultTemplateIcon,{});}};const TemplatesPage=()=>{const navigate=useNavigate();// --- Updated State ---\nconst[allTemplates,setAllTemplates]=useState([]);// Store all fetched templates\nconst[categorizedTemplates,setCategorizedTemplates]=useState({});const[userTemplates,setUserTemplates]=useState([]);const[categories,setCategories]=useState([]);const[selectedCategory,setSelectedCategory]=useState('');const[viewMode,setViewMode]=useState('system');// 'system' or 'user'\nconst[loading,setLoading]=useState(true);const[error,setError]=useState(null);// State for delete confirmation modal\nconst[isDeleteModalOpen,setIsDeleteModalOpen]=useState(false);const[templateToDeleteId,setTemplateToDeleteId]=useState(null);// State for bulk delete\nconst[selectedTemplates,setSelectedTemplates]=useState([]);const[isMultiDeleteModalOpen,setIsMultiDeleteModalOpen]=useState(false);useEffect(()=>{fetchAllTemplates();},[]);// Process templates whenever allTemplates or viewMode changes\nuseEffect(()=>{processTemplates();},[allTemplates,viewMode]);const fetchAllTemplates=async()=>{setLoading(true);setError(null);try{console.log(\"Fetching all user and system templates...\");// Use getAllTemplates which fetches user's + system\nconst response=await templateRecommendationService.getAllTemplates();if(response.success&&response.data){setAllTemplates(response.data);// Store the combined list\n}else{setError(response.message||'Failed to fetch templates');setAllTemplates([]);// Clear on error\n}}catch(err){var _err$response,_err$response$data;console.error('Error fetching all templates:',err);const message=((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||err.message||'An unexpected error occurred';setError(message);setAllTemplates([]);}finally{setLoading(false);}};// Separate function to process templates based on viewMode\nconst processTemplates=()=>{if(viewMode==='system'){const systemTemplates=allTemplates.filter(t=>t.isSystem);const categorized=systemTemplates.reduce((acc,template)=>{const category=template.category||'other';if(!acc[category]){acc[category]=[];}acc[category].push(template);return acc;},{});setCategorizedTemplates(categorized);const fetchedCategories=Object.keys(categorized);setCategories(fetchedCategories);// Set selected category only if it's not already set or valid\nif(!selectedCategory||!fetchedCategories.includes(selectedCategory)){setSelectedCategory(fetchedCategories[0]||'');}setUserTemplates([]);// Clear user templates\n}else{// viewMode === 'user'\n// Filter for user-owned templates (isSystem is false)\n// Note: Backend's getUserTemplates already filters by userId, but double-checking isSystem is safe.\nconst userOwnedTemplates=allTemplates.filter(t=>!t.isSystem);setUserTemplates(userOwnedTemplates);setCategorizedTemplates({});// Clear system templates\nsetCategories([]);setSelectedCategory('');}};// --- Keep Navigation Handlers ---\nconst handleUseTemplate=templateId=>{navigate(`/email-templates/editor/${templateId}`);};const handleCreateTemplate=()=>{navigate('/email-templates/create');};const handleEditTemplate=templateId=>{navigate(`/email-templates/editor/${templateId}`);};// Handler for deleting a template - Opens the modal\nconst handleDeleteTemplate=templateId=>{setTemplateToDeleteId(templateId);setIsDeleteModalOpen(true);};// Function to actually perform the deletion (called from modal confirm)\nconst confirmDeleteTemplate=async()=>{if(!templateToDeleteId)return;// Should not happen, but safeguard\ntry{await templateRecommendationService.deleteTemplate(templateToDeleteId);// Refresh templates after deletion\nfetchAllTemplates();closeDeleteModal();// Close modal on success\n}catch(err){var _err$response2,_err$response2$data;console.error('Error deleting template:',err);setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||err.message||'Failed to delete template');// Optionally keep modal open on error or display error within modal/toast\ncloseDeleteModal();// Close modal even on error for now\n}};// Function to close the modal\nconst closeDeleteModal=()=>{setIsDeleteModalOpen(false);setTemplateToDeleteId(null);};// Function to handle checkbox selection\nconst handleTemplateSelect=(templateId,isSelected)=>{setSelectedTemplates(prev=>isSelected?[...prev,templateId]:prev.filter(id=>id!==templateId));};// Function to handle bulk delete\nconst handleBulkDelete=()=>{if(selectedTemplates.length===0)return;setIsMultiDeleteModalOpen(true);};// Function to confirm bulk delete\nconst confirmBulkDelete=async()=>{if(selectedTemplates.length===0)return;try{setLoading(true);// Process delete operations in sequence\nfor(const id of selectedTemplates){await templateRecommendationService.deleteTemplate(id);}// Clear selection and refresh templates\nsetSelectedTemplates([]);fetchAllTemplates();closeMultiDeleteModal();}catch(err){var _err$response3,_err$response3$data;console.error('Error deleting templates in bulk:',err);setError(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.message)||err.message||'Failed to delete selected templates');closeMultiDeleteModal();}finally{setLoading(false);}};// Function to close multi-delete modal\nconst closeMultiDeleteModal=()=>{setIsMultiDeleteModalOpen(false);};// Function to select all user templates\nconst selectAllTemplates=()=>{const ids=userTemplates.map(template=>template.id);setSelectedTemplates(ids);};// Function to deselect all templates\nconst deselectAllTemplates=()=>{setSelectedTemplates([]);};// --- Render Logic ---\nlet contentToRender;if(loading){contentToRender=/*#__PURE__*/_jsx(\"p\",{className:\"text-white col-span-full text-center py-10\",children:\"Loading templates...\"});}else if(error){contentToRender=/*#__PURE__*/_jsxs(\"p\",{className:\"text-red-500 col-span-full text-center py-10\",children:[\"Error: \",error]});}else if(viewMode==='system'){var _categorizedTemplates;if(categories.length===0){contentToRender=/*#__PURE__*/_jsx(\"p\",{className:\"text-white col-span-full text-center py-10\",children:\"No system template categories found.\"});}else if(selectedCategory&&((_categorizedTemplates=categorizedTemplates[selectedCategory])===null||_categorizedTemplates===void 0?void 0:_categorizedTemplates.length)>0){const templatesToDisplay=categorizedTemplates[selectedCategory];contentToRender=templatesToDisplay.map(template=>{// ... Same Card rendering logic as before ...\nconst gradientClass=getGradientForCategory(template.category);const CategoryIcon=getIconForCategory(template.category);// System templates are never directly editable (copy-on-edit happens)\nconst canEdit=false;return/*#__PURE__*/_jsxs(Card,{className:\"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:`relative w-full h-48 bg-gradient-to-br ${gradientClass} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-2\",children:CategoryIcon}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md\",children:template.name})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 flex flex-col flex-grow\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm mb-4 flex-grow\",children:template.description||'No description available.'}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-auto flex gap-2 justify-end pt-2\",children:/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleUseTemplate(template.id),children:\"Use Template\"})})]})]},template.id);});}else{contentToRender=/*#__PURE__*/_jsxs(\"p\",{className:\"text-white col-span-full text-center py-10\",children:[\"No templates found in the '\",selectedCategory,\"' category.\"]});}}else{// viewMode === 'user'\nif(userTemplates.length===0){contentToRender=/*#__PURE__*/_jsx(\"p\",{className:\"text-white col-span-full text-center py-10\",children:\"You haven't created or saved any templates yet.\"});}else{contentToRender=userTemplates.map(template=>{// User templates ARE editable\nconst canEdit=true;const gradientClass=getGradientForCategory(template.category);const CategoryIcon=getIconForCategory(template.category);const isSelected=selectedTemplates.includes(template.id);return/*#__PURE__*/_jsxs(Card,{className:\"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:`relative w-full h-48 bg-gradient-to-br ${gradientClass} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`,children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute top-2 left-2 z-20\",children:/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isSelected,onChange:e=>handleTemplateSelect(template.id,e.target.checked),className:\"h-5 w-5 rounded border-gray-500 text-indigo-600 focus:ring-indigo-500\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mb-2\",children:CategoryIcon}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md\",children:template.name})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 flex flex-col flex-grow\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-400 text-sm mb-4 flex-grow\",children:template.description||'No description available.'}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-auto flex gap-2 justify-end pt-2 w-full\",children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleUseTemplate(template.id),children:\"Use Template\"}),canEdit&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"secondary\",onClick:()=>handleEditTemplate(template.id),children:\"Edit\"}),/*#__PURE__*/_jsx(Button,{size:\"sm\",variant:\"danger\",onClick:()=>handleDeleteTemplate(template.id),children:\"Delete\"})]})]})]})]},template.id);});}}return/*#__PURE__*/_jsxs(\"div\",{className:\"p-6 bg-gray-900 min-h-screen text-white\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-semibold\",children:\"Email Templates\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2\",children:[viewMode==='user'&&/*#__PURE__*/_jsx(_Fragment,{children:selectedTemplates.length>0?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:deselectAllTemplates,children:\"Deselect All\"}),/*#__PURE__*/_jsxs(Button,{variant:\"danger\",onClick:handleBulkDelete,children:[\"Delete Selected (\",selectedTemplates.length,\")\"]})]}):/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:selectAllTemplates,children:\"Select All\"})}),/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>navigate('/ai-template-generator'),children:\"AI Generator\"}),/*#__PURE__*/_jsx(Button,{variant:\"primary\",onClick:handleCreateTemplate,children:\"Create New Template\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-4 mb-6 border-b border-gray-700 pb-4\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('system'),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${viewMode==='system'?'bg-indigo-600 text-white':'bg-gray-700 text-gray-300 hover:bg-gray-600'}\n           `,children:\"System Templates\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('user'),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${viewMode==='user'?'bg-indigo-600 text-white':'bg-gray-700 text-gray-300 hover:bg-gray-600'}\n           `,children:\"My Templates\"})]}),viewMode==='system'&&!loading&&!error&&categories.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-2 mb-6\",children:categories.map(category=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectedCategory(category),className:`px-3 py-1 rounded-md text-xs font-medium transition-colors duration-150\n                ${selectedCategory===category?'bg-indigo-500 text-white':'bg-gray-600 text-gray-200 hover:bg-gray-500'}\n              `,children:category.charAt(0).toUpperCase()+category.slice(1)},category))}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",children:contentToRender}),/*#__PURE__*/_jsx(Modal,{isOpen:isDeleteModalOpen,onClose:closeDeleteModal,title:\"Confirm Deletion\",onConfirm:confirmDeleteTemplate,confirmText:\"Delete\",confirmVariant:\"danger\",children:/*#__PURE__*/_jsx(\"p\",{children:\"Are you sure you want to delete this template? This action cannot be undone.\"})}),/*#__PURE__*/_jsx(Modal,{isOpen:isMultiDeleteModalOpen,onClose:closeMultiDeleteModal,title:\"Confirm Bulk Deletion\",onConfirm:confirmBulkDelete,confirmText:\"Delete Selected\",confirmVariant:\"danger\",children:/*#__PURE__*/_jsxs(\"p\",{children:[\"Are you sure you want to delete \",selectedTemplates.length,\" selected template(s)? This action cannot be undone.\"]})})]});};export default TemplatesPage;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "<PERSON><PERSON>", "Card", "Modal", "templateRecommendationService", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "getGradientForCategory", "category", "toLowerCase", "NewsletterIcon", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "className", "children", "strokeLinecap", "strokeLinejoin", "d", "PromotionIcon", "WelcomeIcon", "AnnouncementIcon", "EcommerceIcon", "DefaultTemplateIcon", "getIconForCategory", "TemplatesPage", "navigate", "allTemplates", "setAllTemplates", "categorizedTemplates", "setCategorizedTemplates", "userTemplates", "setUserTemplates", "categories", "setCategories", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "viewMode", "setViewMode", "loading", "setLoading", "error", "setError", "isDeleteModalOpen", "setIsDeleteModalOpen", "templateToDeleteId", "setTemplateToDeleteId", "selectedTemplates", "setSelectedTemplates", "isMultiDeleteModalOpen", "setIsMultiDeleteModalOpen", "fetchAllTemplates", "processTemplates", "console", "log", "response", "getAllTemplates", "success", "data", "message", "err", "_err$response", "_err$response$data", "systemTemplates", "filter", "t", "isSystem", "categorized", "reduce", "acc", "template", "push", "fetchedCategories", "Object", "keys", "includes", "userOwnedTemplates", "handleUseTemplate", "templateId", "handleCreateTemplate", "handleEditTemplate", "handleDeleteTemplate", "confirmDeleteTemplate", "deleteTemplate", "closeDeleteModal", "_err$response2", "_err$response2$data", "handleTemplateSelect", "isSelected", "prev", "id", "handleBulkDelete", "length", "confirmBulkDelete", "closeMultiDeleteModal", "_err$response3", "_err$response3$data", "selectAllTemplates", "ids", "map", "deselectAllTemplates", "contentToRender", "_categorizedTemplates", "templatesToDisplay", "gradientClass", "CategoryIcon", "canEdit", "name", "description", "size", "variant", "onClick", "type", "checked", "onChange", "e", "target", "char<PERSON>t", "toUpperCase", "slice", "isOpen", "onClose", "title", "onConfirm", "confirmText", "confirmVariant"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/Templates.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport { useNavigate } from 'react-router-dom';\n\nimport Button from '../components/Button';\n// import axios from 'axios'; // Remove axios if only using mock data\nimport Card from '../components/Card';\nimport { Modal } from '../components/Modal'; // Use named import\n// Import the service\nimport { templateRecommendationService } from '../services';\n\ninterface Template {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  thumbnail: string;\n  userId?: string;\n  isSystem: boolean;\n  createdAt: string;\n  performance?: {\n    opens: number;\n    clicks: number;\n    conversions: number;\n    usageCount: number;\n  };\n}\n\n// Type for categorized templates\ninterface CategorizedTemplates {\n  [key: string]: Template[];\n}\n\n// Helper function to get gradient class based on category\nconst getGradientForCategory = (category: string): string => {\n  switch (category.toLowerCase()) {\n    case 'newsletter':\n      return 'from-blue-500 to-indigo-600';\n    case 'promotion':\n      return 'from-purple-500 to-pink-600';\n    case 'welcome':\n      return 'from-green-400 to-teal-500';\n    case 'announcement':\n      return 'from-yellow-400 to-orange-500';\n    case 'e-commerce':\n      return 'from-red-500 to-rose-600';\n    default:\n      return 'from-gray-600 to-gray-700'; // Default fallback\n  }\n};\n\n// --- SVG Icons (can be moved to a separate file later) ---\nconst NewsletterIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 7.5h1.5m-1.5 3h1.5m-7.5 3h7.5m-7.5 3h7.5m3-9h3.375c.621 0 1.125.504 1.125 1.125V18a2.25 2.25 0 0 1-2.25 2.25M16.5 7.5V18a2.25 2.25 0 0 0 2.25 2.25M16.5 7.5V4.875c0-.621-.504-1.125-1.125-1.125H4.125C3.504 3.75 3 4.254 3 4.875V18a2.25 2.25 0 0 0 2.25 2.25h13.5M6 7.5h3v3H6v-3Z\" />\n  </svg>\n);\n\nconst PromotionIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z\" />\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 6h.008v.008H6V6Z\" />\n  </svg>\n);\n\nconst WelcomeIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n  </svg>\n);\n\nconst AnnouncementIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M10.34 15.84c-.688-.06-1.386-.09-2.09-.09H7.5a4.5 4.5 0 1 1 0-9h.75c.704 0 1.402-.03 2.09-.09m0 9.18c.253.962.584 1.892.985 2.783.247.55.06 1.21-.463 1.511l-.657.38c-.551.318-1.26.117-1.527-.461a20.845 20.845 0 0 1-1.44-4.282m3.102.069a18.03 18.03 0 0 1-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18a23.848 23.848 0 0 1 8.835 2.535L19.5 17.11c.463-.304.646-.961.463-1.511a20.845 20.845 0 0 0-1.44-4.282m-3.102-.069a18.03 18.03 0 0 0-.59-4.59c0-1.586.205-3.124.59-4.59m0 9.18C9.36 16.33 8.5 17.644 8.5 19.5a2.25 2.25 0 0 0 4.5 0c0-1.09-.394-2.068-.998-2.818\" />\n  </svg>\n);\n\nconst EcommerceIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 0 0-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 0 0-16.536-1.84M7.5 14.25 5.106 5.106M15.75 5.106l-1.483 7.411M9.75 9.75l4.5 1.5m-4.5-1.5L12 9.75m-2.25 0L9.75 7.5M15 12l-2.25-1.5M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n  </svg>\n);\n\n// Default/Fallback Icon\nconst DefaultTemplateIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth={1.5} stroke=\"currentColor\" className=\"w-12 h-12 text-white opacity-50\">\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M11.35 3.836A1.5 1.5 0 0 1 12.183 3h9.634A1.5 1.5 0 0 1 23.317 4.183L19.817 7.683A1.5 1.5 0 0 1 19.183 8H2.817a1.5 1.5 0 0 1-1.317-.817L.683 4.183A1.5 1.5 0 0 1 1.817 3h9.534Z\" />\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2 8v11a1.5 1.5 0 0 0 1.5 1.5h17A1.5 1.5 0 0 0 22 19V8M8 12h8M8 16h4\" />\n  </svg>\n);\n\n// --- Helper function to get icon based on category ---\nconst getIconForCategory = (category: string): JSX.Element => {\n  switch (category.toLowerCase()) {\n    case 'newsletter':\n      return <NewsletterIcon />;\n    case 'promotion':\n    case 'promotional': // Added alias\n      return <PromotionIcon />;\n    case 'welcome':\n    case 'onboarding': // Added alias\n      return <WelcomeIcon />;\n    case 'announcement':\n      return <AnnouncementIcon />;\n    case 'e-commerce':\n      return <EcommerceIcon />;\n    case 'event':\n    case 'follow-up':\n    case 'transactional':\n    case 'marketing': // Catch-all for other generated types\n    default:\n      return <DefaultTemplateIcon />;\n  }\n};\n\ninterface TemplatesPageProps {}\n\nconst TemplatesPage: React.FC<TemplatesPageProps> = () => {\n  const navigate = useNavigate();\n\n  // --- Updated State ---\n  const [allTemplates, setAllTemplates] = useState<Template[]>([]); // Store all fetched templates\n  const [categorizedTemplates, setCategorizedTemplates] = useState<CategorizedTemplates>({});\n  const [userTemplates, setUserTemplates] = useState<Template[]>([]);\n  const [categories, setCategories] = useState<string[]>([]);\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\n  const [viewMode, setViewMode] = useState<'system' | 'user'>('system'); // 'system' or 'user'\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  // State for delete confirmation modal\n  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);\n  const [templateToDeleteId, setTemplateToDeleteId] = useState<string | null>(null);\n  // State for bulk delete\n  const [selectedTemplates, setSelectedTemplates] = useState<string[]>([]);\n  const [isMultiDeleteModalOpen, setIsMultiDeleteModalOpen] = useState(false);\n\n  useEffect(() => {\n    fetchAllTemplates();\n  }, []);\n\n  // Process templates whenever allTemplates or viewMode changes\n  useEffect(() => {\n    processTemplates();\n  }, [allTemplates, viewMode]);\n\n  const fetchAllTemplates = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Fetching all user and system templates...\");\n      // Use getAllTemplates which fetches user's + system\n      const response = await templateRecommendationService.getAllTemplates();\n\n      if (response.success && response.data) {\n        setAllTemplates(response.data); // Store the combined list\n      } else {\n        setError(response.message || 'Failed to fetch templates');\n        setAllTemplates([]); // Clear on error\n      }\n    } catch (err: any) {\n      console.error('Error fetching all templates:', err);\n      const message = err.response?.data?.message || err.message || 'An unexpected error occurred';\n      setError(message);\n      setAllTemplates([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Separate function to process templates based on viewMode\n  const processTemplates = () => {\n    if (viewMode === 'system') {\n      const systemTemplates = allTemplates.filter(t => t.isSystem);\n      const categorized = systemTemplates.reduce((acc: CategorizedTemplates, template) => {\n        const category = template.category || 'other';\n        if (!acc[category]) {\n          acc[category] = [];\n        }\n        acc[category].push(template);\n        return acc;\n      }, {});\n      setCategorizedTemplates(categorized);\n      const fetchedCategories = Object.keys(categorized);\n      setCategories(fetchedCategories);\n      // Set selected category only if it's not already set or valid\n      if (!selectedCategory || !fetchedCategories.includes(selectedCategory)) {\n         setSelectedCategory(fetchedCategories[0] || '');\n      }\n      setUserTemplates([]); // Clear user templates\n    } else { // viewMode === 'user'\n      // Filter for user-owned templates (isSystem is false)\n      // Note: Backend's getUserTemplates already filters by userId, but double-checking isSystem is safe.\n      const userOwnedTemplates = allTemplates.filter(t => !t.isSystem);\n      setUserTemplates(userOwnedTemplates);\n      setCategorizedTemplates({}); // Clear system templates\n      setCategories([]);\n      setSelectedCategory('');\n    }\n  };\n\n  // --- Keep Navigation Handlers ---\n  const handleUseTemplate = (templateId: string) => {\n    navigate(`/email-templates/editor/${templateId}`);\n  };\n  const handleCreateTemplate = () => {\n    navigate('/email-templates/create');\n  };\n  const handleEditTemplate = (templateId: string) => {\n    navigate(`/email-templates/editor/${templateId}`);\n  };\n\n  // Handler for deleting a template - Opens the modal\n  const handleDeleteTemplate = (templateId: string) => {\n    setTemplateToDeleteId(templateId);\n    setIsDeleteModalOpen(true);\n  };\n\n  // Function to actually perform the deletion (called from modal confirm)\n  const confirmDeleteTemplate = async () => {\n    if (!templateToDeleteId) return; // Should not happen, but safeguard\n\n    try {\n      await templateRecommendationService.deleteTemplate(templateToDeleteId);\n      // Refresh templates after deletion\n      fetchAllTemplates();\n      closeDeleteModal(); // Close modal on success\n    } catch (err: any) {\n      console.error('Error deleting template:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to delete template');\n      // Optionally keep modal open on error or display error within modal/toast\n      closeDeleteModal(); // Close modal even on error for now\n    }\n  };\n\n  // Function to close the modal\n  const closeDeleteModal = () => {\n    setIsDeleteModalOpen(false);\n    setTemplateToDeleteId(null);\n  };\n\n  // Function to handle checkbox selection\n  const handleTemplateSelect = (templateId: string, isSelected: boolean) => {\n    setSelectedTemplates(prev => \n      isSelected \n        ? [...prev, templateId] \n        : prev.filter(id => id !== templateId)\n    );\n  };\n\n  // Function to handle bulk delete\n  const handleBulkDelete = () => {\n    if (selectedTemplates.length === 0) return;\n    setIsMultiDeleteModalOpen(true);\n  };\n\n  // Function to confirm bulk delete\n  const confirmBulkDelete = async () => {\n    if (selectedTemplates.length === 0) return;\n    \n    try {\n      setLoading(true);\n      // Process delete operations in sequence\n      for (const id of selectedTemplates) {\n        await templateRecommendationService.deleteTemplate(id);\n      }\n      // Clear selection and refresh templates\n      setSelectedTemplates([]);\n      fetchAllTemplates();\n      closeMultiDeleteModal();\n    } catch (err: any) {\n      console.error('Error deleting templates in bulk:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to delete selected templates');\n      closeMultiDeleteModal();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to close multi-delete modal\n  const closeMultiDeleteModal = () => {\n    setIsMultiDeleteModalOpen(false);\n  };\n\n  // Function to select all user templates\n  const selectAllTemplates = () => {\n    const ids = userTemplates.map(template => template.id);\n    setSelectedTemplates(ids);\n  };\n\n  // Function to deselect all templates\n  const deselectAllTemplates = () => {\n    setSelectedTemplates([]);\n  };\n\n  // --- Render Logic ---\n  let contentToRender;\n  if (loading) {\n    contentToRender = <p className=\"text-white col-span-full text-center py-10\">Loading templates...</p>;\n  } else if (error) {\n    contentToRender = <p className=\"text-red-500 col-span-full text-center py-10\">Error: {error}</p>;\n  } else if (viewMode === 'system') {\n      if (categories.length === 0) {\n          contentToRender = <p className=\"text-white col-span-full text-center py-10\">No system template categories found.</p>;\n      } else if (selectedCategory && categorizedTemplates[selectedCategory]?.length > 0) {\n          const templatesToDisplay = categorizedTemplates[selectedCategory];\n          contentToRender = templatesToDisplay.map((template) => {\n             // ... Same Card rendering logic as before ...\n            const gradientClass = getGradientForCategory(template.category);\n            const CategoryIcon = getIconForCategory(template.category);\n            // System templates are never directly editable (copy-on-edit happens)\n            const canEdit = false;\n            return (\n              <Card key={template.id} className=\"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col\">\n                <div className={`relative w-full h-48 bg-gradient-to-br ${gradientClass} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`}>\n                  <div className=\"mb-2\">{CategoryIcon}</div>\n                  <span className=\"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md\">{template.name}</span>\n                </div>\n                <div className=\"p-4 flex flex-col flex-grow\">\n                  <p className=\"text-gray-400 text-sm mb-4 flex-grow\">{template.description || 'No description available.'}</p>\n                  <div className=\"mt-auto flex gap-2 justify-end pt-2\">\n                    <Button size=\"sm\" variant=\"secondary\" onClick={() => handleUseTemplate(template.id)}>Use Template</Button>\n                    {/* Edit button hidden for system templates in this view */}\n                  </div>\n                </div>\n              </Card>\n            );\n          });\n      } else {\n          contentToRender = <p className=\"text-white col-span-full text-center py-10\">No templates found in the '{selectedCategory}' category.</p>;\n      }\n  } else { // viewMode === 'user'\n      if (userTemplates.length === 0) {\n          contentToRender = <p className=\"text-white col-span-full text-center py-10\">You haven't created or saved any templates yet.</p>;\n      } else {\n          contentToRender = userTemplates.map((template) => {\n            // User templates ARE editable\n            const canEdit = true;\n            const gradientClass = getGradientForCategory(template.category);\n            const CategoryIcon = getIconForCategory(template.category);\n            const isSelected = selectedTemplates.includes(template.id);\n            \n            return (\n              <Card key={template.id} className=\"bg-gray-800 hover:bg-gray-750 transition-colors duration-200 flex flex-col\">\n                <div className={`relative w-full h-48 bg-gradient-to-br ${gradientClass} flex flex-col items-center justify-center text-center p-4 rounded-t-lg overflow-hidden shadow-inner`}>\n                  {/* Add checkbox for selection in user templates view */}\n                  <div className=\"absolute top-2 left-2 z-20\">\n                    <input\n                      type=\"checkbox\"\n                      checked={isSelected}\n                      onChange={(e) => handleTemplateSelect(template.id, e.target.checked)}\n                      className=\"h-5 w-5 rounded border-gray-500 text-indigo-600 focus:ring-indigo-500\"\n                    />\n                  </div>\n                  <div className=\"mb-2\">{CategoryIcon}</div>\n                  <span className=\"text-sm font-semibold text-white text-opacity-90 z-10 drop-shadow-md\">{template.name}</span>\n                </div>\n                <div className=\"p-4 flex flex-col flex-grow\">\n                  <p className=\"text-gray-400 text-sm mb-4 flex-grow\">{template.description || 'No description available.'}</p>\n                  <div className=\"mt-auto flex gap-2 justify-end pt-2 w-full\">\n                    <Button size=\"sm\" variant=\"secondary\" onClick={() => handleUseTemplate(template.id)}>Use Template</Button>\n                    {canEdit && (\n                      <>\n                        <Button size=\"sm\" variant=\"secondary\" onClick={() => handleEditTemplate(template.id)}>Edit</Button>\n                        <Button size=\"sm\" variant=\"danger\" onClick={() => handleDeleteTemplate(template.id)}>Delete</Button>\n                      </>\n                    )}\n                  </div>\n                </div>\n              </Card>\n            );\n          });\n      }\n  }\n\n  return (\n    <div className=\"p-6 bg-gray-900 min-h-screen text-white\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-semibold\">Email Templates</h1>\n        <div className=\"flex gap-2\">\n          {viewMode === 'user' && (\n            <>\n              {selectedTemplates.length > 0 ? (\n                <>\n                  <Button variant=\"secondary\" onClick={deselectAllTemplates}>\n                    Deselect All\n                  </Button>\n                  <Button variant=\"danger\" onClick={handleBulkDelete}>\n                    Delete Selected ({selectedTemplates.length})\n                  </Button>\n                </>\n              ) : (\n                <Button variant=\"secondary\" onClick={selectAllTemplates}>\n                  Select All\n                </Button>\n              )}\n            </>\n          )}\n          <Button variant=\"secondary\" onClick={() => navigate('/ai-template-generator')}>\n            AI Generator\n          </Button>\n          <Button variant=\"primary\" onClick={handleCreateTemplate}>\n            Create New Template\n          </Button>\n        </div>\n      </div>\n\n      {/* View Mode Selector */}\n      <div className=\"flex gap-4 mb-6 border-b border-gray-700 pb-4\">\n         <button\n           onClick={() => setViewMode('system')}\n           className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${viewMode === 'system'\n               ? 'bg-indigo-600 text-white'\n               : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}\n           `}\n         >\n           System Templates\n         </button>\n                   <button\n           onClick={() => setViewMode('user')}\n           className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-150\n             ${viewMode === 'user'\n               ? 'bg-indigo-600 text-white'\n               : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}\n           `}\n         >\n           My Templates\n                    </button>\n                </div>\n\n      {/* Category Selector (Only show for System view) */}\n      {viewMode === 'system' && !loading && !error && categories.length > 0 && (\n        <div className=\"flex flex-wrap gap-2 mb-6\">\n          {categories.map(category => (\n            <button\n              key={category}\n              onClick={() => setSelectedCategory(category)}\n              className={`px-3 py-1 rounded-md text-xs font-medium transition-colors duration-150\n                ${selectedCategory === category\n                  ? 'bg-indigo-500 text-white'\n                  : 'bg-gray-600 text-gray-200 hover:bg-gray-500'}\n              `}\n            >\n              {category.charAt(0).toUpperCase() + category.slice(1)}\n            </button>\n          ))}\n        </div>\n      )}\n\n      {/* Template Grid */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n        {contentToRender}\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      <Modal\n        isOpen={isDeleteModalOpen}\n        onClose={closeDeleteModal}\n        title=\"Confirm Deletion\"\n        onConfirm={confirmDeleteTemplate}\n        confirmText=\"Delete\"\n        confirmVariant=\"danger\"\n      >\n        <p>Are you sure you want to delete this template? This action cannot be undone.</p>\n      </Modal>\n\n      {/* Bulk Delete Confirmation Modal */}\n      <Modal\n        isOpen={isMultiDeleteModalOpen}\n        onClose={closeMultiDeleteModal}\n        title=\"Confirm Bulk Deletion\"\n        onConfirm={confirmBulkDelete}\n        confirmText=\"Delete Selected\"\n        confirmVariant=\"danger\"\n      >\n        <p>Are you sure you want to delete {selectedTemplates.length} selected template(s)? This action cannot be undone.</p>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TemplatesPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,OAASC,WAAW,KAAQ,kBAAkB,CAE9C,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC;AACA,MAAO,CAAAC,IAAI,KAAM,oBAAoB,CACrC,OAASC,KAAK,KAAQ,qBAAqB,CAAE;AAC7C;AACA,OAASC,6BAA6B,KAAQ,aAAa,CAmB3D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAKA;AACA,KAAM,CAAAC,sBAAsB,CAAIC,QAAgB,EAAa,CAC3D,OAAQA,QAAQ,CAACC,WAAW,CAAC,CAAC,EAC5B,IAAK,YAAY,CACf,MAAO,6BAA6B,CACtC,IAAK,WAAW,CACd,MAAO,6BAA6B,CACtC,IAAK,SAAS,CACZ,MAAO,4BAA4B,CACrC,IAAK,cAAc,CACjB,MAAO,+BAA+B,CACxC,IAAK,YAAY,CACf,MAAO,0BAA0B,CACnC,QACE,MAAO,2BAA2B,CAAE;AACxC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,gBACrBR,IAAA,QAAKS,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAE,GAAI,CAACC,MAAM,CAAC,cAAc,CAACC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cACzJf,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,wRAAwR,CAAE,CAAC,CAC7U,CACN,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,gBACpBjB,KAAA,QAAKO,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAE,GAAI,CAACC,MAAM,CAAC,cAAc,CAACC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eACzJf,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,+MAA+M,CAAE,CAAC,cACvQlB,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,qBAAqB,CAAE,CAAC,EAC1E,CACN,CAED,KAAM,CAAAE,WAAW,CAAGA,CAAA,gBAClBpB,IAAA,QAAKS,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAE,GAAI,CAACC,MAAM,CAAC,cAAc,CAACC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cACzJf,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,4RAA4R,CAAE,CAAC,CACjV,CACN,CAED,KAAM,CAAAG,gBAAgB,CAAGA,CAAA,gBACvBrB,IAAA,QAAKS,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAE,GAAI,CAACC,MAAM,CAAC,cAAc,CAACC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cACzJf,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,0iBAA0iB,CAAE,CAAC,CAC/lB,CACN,CAED,KAAM,CAAAI,aAAa,CAAGA,CAAA,gBACpBtB,IAAA,QAAKS,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAE,GAAI,CAACC,MAAM,CAAC,cAAc,CAACC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,cACzJf,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uTAAuT,CAAE,CAAC,CAC5W,CACN,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAGA,CAAA,gBAC1BrB,KAAA,QAAKO,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,WAAW,CAAE,GAAI,CAACC,MAAM,CAAC,cAAc,CAACC,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eACzJf,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,iLAAiL,CAAE,CAAC,cACzOlB,IAAA,SAAMgB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,sEAAsE,CAAE,CAAC,EAC3H,CACN,CAED;AACA,KAAM,CAAAM,kBAAkB,CAAIlB,QAAgB,EAAkB,CAC5D,OAAQA,QAAQ,CAACC,WAAW,CAAC,CAAC,EAC5B,IAAK,YAAY,CACf,mBAAOP,IAAA,CAACQ,cAAc,GAAE,CAAC,CAC3B,IAAK,WAAW,CAChB,IAAK,aAAa,CAAE;AAClB,mBAAOR,IAAA,CAACmB,aAAa,GAAE,CAAC,CAC1B,IAAK,SAAS,CACd,IAAK,YAAY,CAAE;AACjB,mBAAOnB,IAAA,CAACoB,WAAW,GAAE,CAAC,CACxB,IAAK,cAAc,CACjB,mBAAOpB,IAAA,CAACqB,gBAAgB,GAAE,CAAC,CAC7B,IAAK,YAAY,CACf,mBAAOrB,IAAA,CAACsB,aAAa,GAAE,CAAC,CAC1B,IAAK,OAAO,CACZ,IAAK,WAAW,CAChB,IAAK,eAAe,CACpB,IAAK,WAAW,CAAE;AAClB,QACE,mBAAOtB,IAAA,CAACuB,mBAAmB,GAAE,CAAC,CAClC,CACF,CAAC,CAID,KAAM,CAAAE,aAA2C,CAAGA,CAAA,GAAM,CACxD,KAAM,CAAAC,QAAQ,CAAGhC,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAACiC,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAa,EAAE,CAAC,CAAE;AAClE,KAAM,CAACoC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrC,QAAQ,CAAuB,CAAC,CAAC,CAAC,CAC1F,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAa,EAAE,CAAC,CAClE,KAAM,CAACwC,UAAU,CAAEC,aAAa,CAAC,CAAGzC,QAAQ,CAAW,EAAE,CAAC,CAC1D,KAAM,CAAC0C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3C,QAAQ,CAAS,EAAE,CAAC,CACpE,KAAM,CAAC4C,QAAQ,CAAEC,WAAW,CAAC,CAAG7C,QAAQ,CAAoB,QAAQ,CAAC,CAAE;AACvE,KAAM,CAAC8C,OAAO,CAAEC,UAAU,CAAC,CAAG/C,QAAQ,CAAU,IAAI,CAAC,CACrD,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAgB,IAAI,CAAC,CACvD;AACA,KAAM,CAACkD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnD,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACoD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGrD,QAAQ,CAAgB,IAAI,CAAC,CACjF;AACA,KAAM,CAACsD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvD,QAAQ,CAAW,EAAE,CAAC,CACxE,KAAM,CAACwD,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CAE3ED,SAAS,CAAC,IAAM,CACd2D,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3D,SAAS,CAAC,IAAM,CACd4D,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAACzB,YAAY,CAAEU,QAAQ,CAAC,CAAC,CAE5B,KAAM,CAAAc,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpCX,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACFW,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC,CACxD;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAzD,6BAA6B,CAAC0D,eAAe,CAAC,CAAC,CAEtE,GAAID,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,IAAI,CAAE,CACrC9B,eAAe,CAAC2B,QAAQ,CAACG,IAAI,CAAC,CAAE;AAClC,CAAC,IAAM,CACLhB,QAAQ,CAACa,QAAQ,CAACI,OAAO,EAAI,2BAA2B,CAAC,CACzD/B,eAAe,CAAC,EAAE,CAAC,CAAE;AACvB,CACF,CAAE,MAAOgC,GAAQ,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACjBT,OAAO,CAACZ,KAAK,CAAC,+BAA+B,CAAEmB,GAAG,CAAC,CACnD,KAAM,CAAAD,OAAO,CAAG,EAAAE,aAAA,CAAAD,GAAG,CAACL,QAAQ,UAAAM,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcH,IAAI,UAAAI,kBAAA,iBAAlBA,kBAAA,CAAoBH,OAAO,GAAIC,GAAG,CAACD,OAAO,EAAI,8BAA8B,CAC5FjB,QAAQ,CAACiB,OAAO,CAAC,CACjB/B,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,OAAS,CACRY,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAY,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAIf,QAAQ,GAAK,QAAQ,CAAE,CACzB,KAAM,CAAA0B,eAAe,CAAGpC,YAAY,CAACqC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,QAAQ,CAAC,CAC5D,KAAM,CAAAC,WAAW,CAAGJ,eAAe,CAACK,MAAM,CAAC,CAACC,GAAyB,CAAEC,QAAQ,GAAK,CAClF,KAAM,CAAAhE,QAAQ,CAAGgE,QAAQ,CAAChE,QAAQ,EAAI,OAAO,CAC7C,GAAI,CAAC+D,GAAG,CAAC/D,QAAQ,CAAC,CAAE,CAClB+D,GAAG,CAAC/D,QAAQ,CAAC,CAAG,EAAE,CACpB,CACA+D,GAAG,CAAC/D,QAAQ,CAAC,CAACiE,IAAI,CAACD,QAAQ,CAAC,CAC5B,MAAO,CAAAD,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CACNvC,uBAAuB,CAACqC,WAAW,CAAC,CACpC,KAAM,CAAAK,iBAAiB,CAAGC,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAClDjC,aAAa,CAACsC,iBAAiB,CAAC,CAChC;AACA,GAAI,CAACrC,gBAAgB,EAAI,CAACqC,iBAAiB,CAACG,QAAQ,CAACxC,gBAAgB,CAAC,CAAE,CACrEC,mBAAmB,CAACoC,iBAAiB,CAAC,CAAC,CAAC,EAAI,EAAE,CAAC,CAClD,CACAxC,gBAAgB,CAAC,EAAE,CAAC,CAAE;AACxB,CAAC,IAAM,CAAE;AACP;AACA;AACA,KAAM,CAAA4C,kBAAkB,CAAGjD,YAAY,CAACqC,MAAM,CAACC,CAAC,EAAI,CAACA,CAAC,CAACC,QAAQ,CAAC,CAChElC,gBAAgB,CAAC4C,kBAAkB,CAAC,CACpC9C,uBAAuB,CAAC,CAAC,CAAC,CAAC,CAAE;AAC7BI,aAAa,CAAC,EAAE,CAAC,CACjBE,mBAAmB,CAAC,EAAE,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAyC,iBAAiB,CAAIC,UAAkB,EAAK,CAChDpD,QAAQ,CAAC,2BAA2BoD,UAAU,EAAE,CAAC,CACnD,CAAC,CACD,KAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACjCrD,QAAQ,CAAC,yBAAyB,CAAC,CACrC,CAAC,CACD,KAAM,CAAAsD,kBAAkB,CAAIF,UAAkB,EAAK,CACjDpD,QAAQ,CAAC,2BAA2BoD,UAAU,EAAE,CAAC,CACnD,CAAC,CAED;AACA,KAAM,CAAAG,oBAAoB,CAAIH,UAAkB,EAAK,CACnDhC,qBAAqB,CAACgC,UAAU,CAAC,CACjClC,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAsC,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CAACrC,kBAAkB,CAAE,OAAQ;AAEjC,GAAI,CACF,KAAM,CAAA/C,6BAA6B,CAACqF,cAAc,CAACtC,kBAAkB,CAAC,CACtE;AACAM,iBAAiB,CAAC,CAAC,CACnBiC,gBAAgB,CAAC,CAAC,CAAE;AACtB,CAAE,MAAOxB,GAAQ,CAAE,KAAAyB,cAAA,CAAAC,mBAAA,CACjBjC,OAAO,CAACZ,KAAK,CAAC,0BAA0B,CAAEmB,GAAG,CAAC,CAC9ClB,QAAQ,CAAC,EAAA2C,cAAA,CAAAzB,GAAG,CAACL,QAAQ,UAAA8B,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc3B,IAAI,UAAA4B,mBAAA,iBAAlBA,mBAAA,CAAoB3B,OAAO,GAAIC,GAAG,CAACD,OAAO,EAAI,2BAA2B,CAAC,CACnF;AACAyB,gBAAgB,CAAC,CAAC,CAAE;AACtB,CACF,CAAC,CAED;AACA,KAAM,CAAAA,gBAAgB,CAAGA,CAAA,GAAM,CAC7BxC,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAAyC,oBAAoB,CAAGA,CAACT,UAAkB,CAAEU,UAAmB,GAAK,CACxExC,oBAAoB,CAACyC,IAAI,EACvBD,UAAU,CACN,CAAC,GAAGC,IAAI,CAAEX,UAAU,CAAC,CACrBW,IAAI,CAACzB,MAAM,CAAC0B,EAAE,EAAIA,EAAE,GAAKZ,UAAU,CACzC,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAa,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI5C,iBAAiB,CAAC6C,MAAM,GAAK,CAAC,CAAE,OACpC1C,yBAAyB,CAAC,IAAI,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAA2C,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI9C,iBAAiB,CAAC6C,MAAM,GAAK,CAAC,CAAE,OAEpC,GAAI,CACFpD,UAAU,CAAC,IAAI,CAAC,CAChB;AACA,IAAK,KAAM,CAAAkD,EAAE,GAAI,CAAA3C,iBAAiB,CAAE,CAClC,KAAM,CAAAjD,6BAA6B,CAACqF,cAAc,CAACO,EAAE,CAAC,CACxD,CACA;AACA1C,oBAAoB,CAAC,EAAE,CAAC,CACxBG,iBAAiB,CAAC,CAAC,CACnB2C,qBAAqB,CAAC,CAAC,CACzB,CAAE,MAAOlC,GAAQ,CAAE,KAAAmC,cAAA,CAAAC,mBAAA,CACjB3C,OAAO,CAACZ,KAAK,CAAC,mCAAmC,CAAEmB,GAAG,CAAC,CACvDlB,QAAQ,CAAC,EAAAqD,cAAA,CAAAnC,GAAG,CAACL,QAAQ,UAAAwC,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcrC,IAAI,UAAAsC,mBAAA,iBAAlBA,mBAAA,CAAoBrC,OAAO,GAAIC,GAAG,CAACD,OAAO,EAAI,qCAAqC,CAAC,CAC7FmC,qBAAqB,CAAC,CAAC,CACzB,CAAC,OAAS,CACRtD,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAsD,qBAAqB,CAAGA,CAAA,GAAM,CAClC5C,yBAAyB,CAAC,KAAK,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAA+C,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,GAAG,CAAGnE,aAAa,CAACoE,GAAG,CAAC7B,QAAQ,EAAIA,QAAQ,CAACoB,EAAE,CAAC,CACtD1C,oBAAoB,CAACkD,GAAG,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAE,oBAAoB,CAAGA,CAAA,GAAM,CACjCpD,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CAAC,CAED;AACA,GAAI,CAAAqD,eAAe,CACnB,GAAI9D,OAAO,CAAE,CACX8D,eAAe,cAAGrG,IAAA,MAAGc,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,sBAAoB,CAAG,CAAC,CACtG,CAAC,IAAM,IAAI0B,KAAK,CAAE,CAChB4D,eAAe,cAAGnG,KAAA,MAAGY,SAAS,CAAC,8CAA8C,CAAAC,QAAA,EAAC,SAAO,CAAC0B,KAAK,EAAI,CAAC,CAClG,CAAC,IAAM,IAAIJ,QAAQ,GAAK,QAAQ,CAAE,KAAAiE,qBAAA,CAC9B,GAAIrE,UAAU,CAAC2D,MAAM,GAAK,CAAC,CAAE,CACzBS,eAAe,cAAGrG,IAAA,MAAGc,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,sCAAoC,CAAG,CAAC,CACxH,CAAC,IAAM,IAAIoB,gBAAgB,EAAI,EAAAmE,qBAAA,CAAAzE,oBAAoB,CAACM,gBAAgB,CAAC,UAAAmE,qBAAA,iBAAtCA,qBAAA,CAAwCV,MAAM,EAAG,CAAC,CAAE,CAC/E,KAAM,CAAAW,kBAAkB,CAAG1E,oBAAoB,CAACM,gBAAgB,CAAC,CACjEkE,eAAe,CAAGE,kBAAkB,CAACJ,GAAG,CAAE7B,QAAQ,EAAK,CACpD;AACD,KAAM,CAAAkC,aAAa,CAAGnG,sBAAsB,CAACiE,QAAQ,CAAChE,QAAQ,CAAC,CAC/D,KAAM,CAAAmG,YAAY,CAAGjF,kBAAkB,CAAC8C,QAAQ,CAAChE,QAAQ,CAAC,CAC1D;AACA,KAAM,CAAAoG,OAAO,CAAG,KAAK,CACrB,mBACExG,KAAA,CAACN,IAAI,EAAmBkB,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eAC5Gb,KAAA,QAAKY,SAAS,CAAE,0CAA0C0F,aAAa,sGAAuG,CAAAzF,QAAA,eAC5Kf,IAAA,QAAKc,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE0F,YAAY,CAAM,CAAC,cAC1CzG,IAAA,SAAMc,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAEuD,QAAQ,CAACqC,IAAI,CAAO,CAAC,EAC1G,CAAC,cACNzG,KAAA,QAAKY,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1Cf,IAAA,MAAGc,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAEuD,QAAQ,CAACsC,WAAW,EAAI,2BAA2B,CAAI,CAAC,cAC7G5G,IAAA,QAAKc,SAAS,CAAC,qCAAqC,CAAAC,QAAA,cAClDf,IAAA,CAACL,MAAM,EAACkH,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAMlC,iBAAiB,CAACP,QAAQ,CAACoB,EAAE,CAAE,CAAA3E,QAAA,CAAC,cAAY,CAAQ,CAAC,CAEvG,CAAC,EACH,CAAC,GAXGuD,QAAQ,CAACoB,EAYd,CAAC,CAEX,CAAC,CAAC,CACN,CAAC,IAAM,CACHW,eAAe,cAAGnG,KAAA,MAAGY,SAAS,CAAC,4CAA4C,CAAAC,QAAA,EAAC,6BAA2B,CAACoB,gBAAgB,CAAC,aAAW,EAAG,CAAC,CAC5I,CACJ,CAAC,IAAM,CAAE;AACL,GAAIJ,aAAa,CAAC6D,MAAM,GAAK,CAAC,CAAE,CAC5BS,eAAe,cAAGrG,IAAA,MAAGc,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,CACnI,CAAC,IAAM,CACHsF,eAAe,CAAGtE,aAAa,CAACoE,GAAG,CAAE7B,QAAQ,EAAK,CAChD;AACA,KAAM,CAAAoC,OAAO,CAAG,IAAI,CACpB,KAAM,CAAAF,aAAa,CAAGnG,sBAAsB,CAACiE,QAAQ,CAAChE,QAAQ,CAAC,CAC/D,KAAM,CAAAmG,YAAY,CAAGjF,kBAAkB,CAAC8C,QAAQ,CAAChE,QAAQ,CAAC,CAC1D,KAAM,CAAAkF,UAAU,CAAGzC,iBAAiB,CAAC4B,QAAQ,CAACL,QAAQ,CAACoB,EAAE,CAAC,CAE1D,mBACExF,KAAA,CAACN,IAAI,EAAmBkB,SAAS,CAAC,4EAA4E,CAAAC,QAAA,eAC5Gb,KAAA,QAAKY,SAAS,CAAE,0CAA0C0F,aAAa,sGAAuG,CAAAzF,QAAA,eAE5Kf,IAAA,QAAKc,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCf,IAAA,UACEgH,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEzB,UAAW,CACpB0B,QAAQ,CAAGC,CAAC,EAAK5B,oBAAoB,CAACjB,QAAQ,CAACoB,EAAE,CAAEyB,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE,CACrEnG,SAAS,CAAC,uEAAuE,CAClF,CAAC,CACC,CAAC,cACNd,IAAA,QAAKc,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE0F,YAAY,CAAM,CAAC,cAC1CzG,IAAA,SAAMc,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAEuD,QAAQ,CAACqC,IAAI,CAAO,CAAC,EAC1G,CAAC,cACNzG,KAAA,QAAKY,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1Cf,IAAA,MAAGc,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAEuD,QAAQ,CAACsC,WAAW,EAAI,2BAA2B,CAAI,CAAC,cAC7G1G,KAAA,QAAKY,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDf,IAAA,CAACL,MAAM,EAACkH,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAMlC,iBAAiB,CAACP,QAAQ,CAACoB,EAAE,CAAE,CAAA3E,QAAA,CAAC,cAAY,CAAQ,CAAC,CACzG2F,OAAO,eACNxG,KAAA,CAAAE,SAAA,EAAAW,QAAA,eACEf,IAAA,CAACL,MAAM,EAACkH,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAM/B,kBAAkB,CAACV,QAAQ,CAACoB,EAAE,CAAE,CAAA3E,QAAA,CAAC,MAAI,CAAQ,CAAC,cACnGf,IAAA,CAACL,MAAM,EAACkH,IAAI,CAAC,IAAI,CAACC,OAAO,CAAC,QAAQ,CAACC,OAAO,CAAEA,CAAA,GAAM9B,oBAAoB,CAACX,QAAQ,CAACoB,EAAE,CAAE,CAAA3E,QAAA,CAAC,QAAM,CAAQ,CAAC,EACpG,CACH,EACE,CAAC,EACH,CAAC,GAzBGuD,QAAQ,CAACoB,EA0Bd,CAAC,CAEX,CAAC,CAAC,CACN,CACJ,CAEA,mBACExF,KAAA,QAAKY,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtDb,KAAA,QAAKY,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDf,IAAA,OAAIc,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC3Db,KAAA,QAAKY,SAAS,CAAC,YAAY,CAAAC,QAAA,EACxBsB,QAAQ,GAAK,MAAM,eAClBrC,IAAA,CAAAI,SAAA,EAAAW,QAAA,CACGgC,iBAAiB,CAAC6C,MAAM,CAAG,CAAC,cAC3B1F,KAAA,CAAAE,SAAA,EAAAW,QAAA,eACEf,IAAA,CAACL,MAAM,EAACmH,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEX,oBAAqB,CAAArF,QAAA,CAAC,cAE3D,CAAQ,CAAC,cACTb,KAAA,CAACP,MAAM,EAACmH,OAAO,CAAC,QAAQ,CAACC,OAAO,CAAEpB,gBAAiB,CAAA5E,QAAA,EAAC,mBACjC,CAACgC,iBAAiB,CAAC6C,MAAM,CAAC,GAC7C,EAAQ,CAAC,EACT,CAAC,cAEH5F,IAAA,CAACL,MAAM,EAACmH,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEd,kBAAmB,CAAAlF,QAAA,CAAC,YAEzD,CAAQ,CACT,CACD,CACH,cACDf,IAAA,CAACL,MAAM,EAACmH,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAMrF,QAAQ,CAAC,wBAAwB,CAAE,CAAAX,QAAA,CAAC,cAE/E,CAAQ,CAAC,cACTf,IAAA,CAACL,MAAM,EAACmH,OAAO,CAAC,SAAS,CAACC,OAAO,CAAEhC,oBAAqB,CAAAhE,QAAA,CAAC,qBAEzD,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNb,KAAA,QAAKY,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC3Df,IAAA,WACE+G,OAAO,CAAEA,CAAA,GAAMzE,WAAW,CAAC,QAAQ,CAAE,CACrCxB,SAAS,CAAE;AACtB,eAAeuB,QAAQ,GAAK,QAAQ,CACnB,0BAA0B,CAC1B,6CAA6C;AAC9D,YAAa,CAAAtB,QAAA,CACH,kBAED,CAAQ,CAAC,cACCf,IAAA,WACR+G,OAAO,CAAEA,CAAA,GAAMzE,WAAW,CAAC,MAAM,CAAE,CACnCxB,SAAS,CAAE;AACtB,eAAeuB,QAAQ,GAAK,MAAM,CACjB,0BAA0B,CAC1B,6CAA6C;AAC9D,YAAa,CAAAtB,QAAA,CACH,cAEU,CAAQ,CAAC,EACR,CAAC,CAGfsB,QAAQ,GAAK,QAAQ,EAAI,CAACE,OAAO,EAAI,CAACE,KAAK,EAAIR,UAAU,CAAC2D,MAAM,CAAG,CAAC,eACnE5F,IAAA,QAAKc,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACvCkB,UAAU,CAACkE,GAAG,CAAC7F,QAAQ,eACtBN,IAAA,WAEE+G,OAAO,CAAEA,CAAA,GAAM3E,mBAAmB,CAAC9B,QAAQ,CAAE,CAC7CQ,SAAS,CAAE;AACzB,kBAAkBqB,gBAAgB,GAAK7B,QAAQ,CAC3B,0BAA0B,CAC1B,6CAA6C;AACjE,eAAgB,CAAAS,QAAA,CAEDT,QAAQ,CAAC+G,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGhH,QAAQ,CAACiH,KAAK,CAAC,CAAC,CAAC,EARhDjH,QASC,CACT,CAAC,CACC,CACN,cAGDN,IAAA,QAAKc,SAAS,CAAC,qEAAqE,CAAAC,QAAA,CACjFsF,eAAe,CACb,CAAC,cAGNrG,IAAA,CAACH,KAAK,EACJ2H,MAAM,CAAE7E,iBAAkB,CAC1B8E,OAAO,CAAErC,gBAAiB,CAC1BsC,KAAK,CAAC,kBAAkB,CACxBC,SAAS,CAAEzC,qBAAsB,CACjC0C,WAAW,CAAC,QAAQ,CACpBC,cAAc,CAAC,QAAQ,CAAA9G,QAAA,cAEvBf,IAAA,MAAAe,QAAA,CAAG,8EAA4E,CAAG,CAAC,CAC9E,CAAC,cAGRf,IAAA,CAACH,KAAK,EACJ2H,MAAM,CAAEvE,sBAAuB,CAC/BwE,OAAO,CAAE3B,qBAAsB,CAC/B4B,KAAK,CAAC,uBAAuB,CAC7BC,SAAS,CAAE9B,iBAAkB,CAC7B+B,WAAW,CAAC,iBAAiB,CAC7BC,cAAc,CAAC,QAAQ,CAAA9G,QAAA,cAEvBb,KAAA,MAAAa,QAAA,EAAG,kCAAgC,CAACgC,iBAAiB,CAAC6C,MAAM,CAAC,sDAAoD,EAAG,CAAC,CAChH,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}