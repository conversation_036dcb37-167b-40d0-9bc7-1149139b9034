{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\n// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport { useAuth } from '../contexts/AuthContext'; // Import useAuth\n\n// Helper function to format date\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst formatDate = dateString => {\n  if (!dateString) return 'N/A';\n  try {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    return 'Invalid Date';\n  }\n};\nconst Settings = () => {\n  _s();\n  var _user$domain;\n  const {\n    user\n  } = useAuth(); // Get user from context\n\n  const [generalSettings, setGeneralSettings] = useState({\n    name: (user === null || user === void 0 ? void 0 : user.name) || 'John Doe',\n    // Initialize with user data or fallback\n    email: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>',\n    // Initialize with user data or fallback\n    companyName: (user === null || user === void 0 ? void 0 : user.companyName) || 'Acme Inc.',\n    // Optional: Add companyName to user type if needed\n    timezone: (user === null || user === void 0 ? void 0 : user.timezone) || 'UTC-5' // Optional: Add timezone to user type if needed\n  });\n  const [canSpamSettings, setCanSpamSettings] = useState({\n    physicalAddress: '123 Main St, Suite 100, New York, NY 10001',\n    unsubscribeText: 'If you no longer wish to receive these emails, you can unsubscribe here.'\n  });\n  const [passwordSettings, setPasswordSettings] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: ''\n  });\n  const [generalSuccess, setGeneralSuccess] = useState(false);\n  const [canSpamSuccess, setCanSpamSuccess] = useState(false);\n  const [passwordSuccess, setPasswordSuccess] = useState(false);\n  const [passwordError, setPasswordError] = useState('');\n  const handleGeneralSubmit = e => {\n    e.preventDefault();\n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setGeneralSuccess(true);\n      setTimeout(() => setGeneralSuccess(false), 3000);\n    }, 500);\n  };\n  const handleCanSpamSubmit = e => {\n    e.preventDefault();\n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setCanSpamSuccess(true);\n      setTimeout(() => setCanSpamSuccess(false), 3000);\n    }, 500);\n  };\n  const handlePasswordSubmit = e => {\n    e.preventDefault();\n    if (!passwordSettings.currentPassword || !passwordSettings.newPassword || !passwordSettings.confirmPassword) {\n      setPasswordError('All password fields are required');\n      return;\n    }\n    if (passwordSettings.newPassword !== passwordSettings.confirmPassword) {\n      setPasswordError('New passwords do not match');\n      return;\n    }\n    if (passwordSettings.newPassword.length < 8) {\n      setPasswordError('New password must be at least 8 characters long');\n      return;\n    }\n\n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setPasswordSuccess(true);\n      setPasswordSettings({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: ''\n      });\n      setPasswordError('');\n      setTimeout(() => setPasswordSuccess(false), 3000);\n    }, 500);\n  };\n  return (\n    /*#__PURE__*/\n    // <Layout title=\"Settings\">\n    _jsxDEV(_Fragment, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2\",\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            title: \"General Settings\",\n            children: [generalSuccess && /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"success\",\n              message: \"General settings updated successfully!\",\n              className: \"mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleGeneralSubmit,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Input, {\n                  id: \"name\",\n                  name: \"name\",\n                  label: \"Full Name\",\n                  value: generalSettings.name,\n                  onChange: e => setGeneralSettings({\n                    ...generalSettings,\n                    name: e.target.value\n                  }),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Input, {\n                  id: \"email\",\n                  name: \"email\",\n                  type: \"email\",\n                  label: \"Email Address\",\n                  value: generalSettings.email,\n                  onChange: e => setGeneralSettings({\n                    ...generalSettings,\n                    email: e.target.value\n                  }),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(Input, {\n                  id: \"companyName\",\n                  name: \"companyName\",\n                  label: \"Company Name\",\n                  value: generalSettings.companyName,\n                  onChange: e => setGeneralSettings({\n                    ...generalSettings,\n                    companyName: e.target.value\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"timezone\",\n                    className: \"form-label\",\n                    children: \"Timezone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    id: \"timezone\",\n                    name: \"timezone\",\n                    value: generalSettings.timezone,\n                    onChange: e => setGeneralSettings({\n                      ...generalSettings,\n                      timezone: e.target.value\n                    }),\n                    className: \"form-input w-full\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-12\",\n                      children: \"UTC-12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-11\",\n                      children: \"UTC-11\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-10\",\n                      children: \"UTC-10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 156,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-9\",\n                      children: \"UTC-9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-8\",\n                      children: \"UTC-8 (Pacific)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-7\",\n                      children: \"UTC-7 (Mountain)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-6\",\n                      children: \"UTC-6 (Central)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-5\",\n                      children: \"UTC-5 (Eastern)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-4\",\n                      children: \"UTC-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-3\",\n                      children: \"UTC-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 163,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-2\",\n                      children: \"UTC-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 164,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC-1\",\n                      children: \"UTC-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+0\",\n                      children: \"UTC+0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+1\",\n                      children: \"UTC+1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 167,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+2\",\n                      children: \"UTC+2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+3\",\n                      children: \"UTC+3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+4\",\n                      children: \"UTC+4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+5\",\n                      children: \"UTC+5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+6\",\n                      children: \"UTC+6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 172,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+7\",\n                      children: \"UTC+7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+8\",\n                      children: \"UTC+8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+9\",\n                      children: \"UTC+9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+10\",\n                      children: \"UTC+10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+11\",\n                      children: \"UTC+11\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"UTC+12\",\n                      children: \"UTC+12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                children: \"Save General Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"CAN-SPAM Compliance\",\n            className: \"mt-6\",\n            children: [canSpamSuccess && /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"success\",\n              message: \"CAN-SPAM settings updated successfully!\",\n              className: \"mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary mb-4\",\n              children: \"The CAN-SPAM Act requires that all commercial emails include your physical address and an unsubscribe option.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleCanSpamSubmit,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"physicalAddress\",\n                  className: \"form-label\",\n                  children: [\"Physical Address \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-500\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"physicalAddress\",\n                  name: \"physicalAddress\",\n                  value: canSpamSettings.physicalAddress,\n                  onChange: e => setCanSpamSettings({\n                    ...canSpamSettings,\n                    physicalAddress: e.target.value\n                  }),\n                  required: true,\n                  rows: 3,\n                  className: \"form-input w-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"unsubscribeText\",\n                  className: \"form-label\",\n                  children: [\"Unsubscribe Text \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-500\",\n                    children: \"*\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 36\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"unsubscribeText\",\n                  name: \"unsubscribeText\",\n                  value: canSpamSettings.unsubscribeText,\n                  onChange: e => setCanSpamSettings({\n                    ...canSpamSettings,\n                    unsubscribeText: e.target.value\n                  }),\n                  required: true,\n                  rows: 2,\n                  className: \"form-input w-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                children: \"Save CAN-SPAM Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Card, {\n            title: \"Change Password\",\n            children: [passwordSuccess && /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"success\",\n              message: \"Password updated successfully!\",\n              className: \"mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), passwordError && /*#__PURE__*/_jsxDEV(Alert, {\n              type: \"error\",\n              message: passwordError,\n              onClose: () => setPasswordError(''),\n              className: \"mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handlePasswordSubmit,\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                id: \"currentPassword\",\n                name: \"currentPassword\",\n                type: \"password\",\n                label: \"Current Password\",\n                value: passwordSettings.currentPassword,\n                onChange: e => setPasswordSettings({\n                  ...passwordSettings,\n                  currentPassword: e.target.value\n                }),\n                required: true,\n                className: \"mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"newPassword\",\n                name: \"newPassword\",\n                type: \"password\",\n                label: \"New Password\",\n                value: passwordSettings.newPassword,\n                onChange: e => setPasswordSettings({\n                  ...passwordSettings,\n                  newPassword: e.target.value\n                }),\n                required: true,\n                className: \"mb-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Input, {\n                id: \"confirmPassword\",\n                name: \"confirmPassword\",\n                type: \"password\",\n                label: \"Confirm New Password\",\n                value: passwordSettings.confirmPassword,\n                onChange: e => setPasswordSettings({\n                  ...passwordSettings,\n                  confirmPassword: e.target.value\n                }),\n                required: true,\n                className: \"mb-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                children: \"Update Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            title: \"Account Information\",\n            className: \"mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-text-secondary\",\n                children: \"Account Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium capitalize\",\n                children: (user === null || user === void 0 ? void 0 : user.accountType) || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-text-secondary\",\n                children: \"Member Since\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium\",\n                children: formatDate(user === null || user === void 0 ? void 0 : user.createdAt)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-text-secondary\",\n                children: \"Domain Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium\",\n                children: user !== null && user !== void 0 && (_user$domain = user.domain) !== null && _user$domain !== void 0 && _user$domain.status ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `px-2 py-1 text-xs rounded capitalize ${user.domain.status === 'active' ? 'bg-green-800' : user.domain.status === 'pending' ? 'bg-yellow-800' : 'bg-red-800'}`,\n                  children: user.domain.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 20\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"px-2 py-1 text-xs rounded bg-gray-700\",\n                  children: \"Not Setup\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 20\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-text-secondary\",\n                children: \"Flows Purchased\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium\",\n                children: \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-text-secondary\",\n                children: \"Flows Remaining\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-medium\",\n                children: \"N/A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 7\n      }, this)\n    }, void 0, false)\n    // </Layout>\n  );\n};\n_s(Settings, \"p+iQKP/g1kSd7fHCKCZ+fJ0bBY8=\", false, function () {\n  return [useAuth];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "Input", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "error", "console", "Settings", "_s", "_user$domain", "user", "generalSettings", "setGeneralSettings", "name", "email", "companyName", "timezone", "canSpamSettings", "setCanSpamSettings", "<PERSON><PERSON><PERSON>ress", "unsubscribeText", "passwordSettings", "setPasswordSettings", "currentPassword", "newPassword", "confirmPassword", "generalSuccess", "setGeneralSuccess", "canSpamSuccess", "setCanSpamSuccess", "passwordSuccess", "setPasswordSuccess", "passwordError", "setPasswordError", "handleGeneralSubmit", "e", "preventDefault", "setTimeout", "handleCanSpamSubmit", "handlePasswordSubmit", "length", "children", "className", "title", "type", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "id", "label", "value", "onChange", "target", "required", "htmlFor", "rows", "onClose", "accountType", "createdAt", "domain", "status", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/Settings.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\n// import Layout from '../components/Layout'; // Removed Layout import\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport { useAuth } from '../contexts/AuthContext'; // Import useAuth\n\n// Helper function to format date\nconst formatDate = (dateString: string | undefined): string => {\n  if (!dateString) return 'N/A';\n  try {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n    });\n  } catch (error) {\n    console.error('Error formatting date:', error);\n    return 'Invalid Date';\n  }\n};\n\nconst Settings: React.FC = () => {\n  const { user } = useAuth(); // Get user from context\n\n  const [generalSettings, setGeneralSettings] = useState({\n    name: user?.name || '<PERSON>', // Initialize with user data or fallback\n    email: user?.email || '<EMAIL>', // Initialize with user data or fallback\n    companyName: user?.companyName || 'Acme Inc.', // Optional: Add companyName to user type if needed\n    timezone: user?.timezone || 'UTC-5', // Optional: Add timezone to user type if needed\n  });\n  \n  const [canSpamSettings, setCanSpamSettings] = useState({\n    physicalAddress: '123 Main St, Suite 100, New York, NY 10001',\n    unsubscribeText: 'If you no longer wish to receive these emails, you can unsubscribe here.',\n  });\n  \n  const [passwordSettings, setPasswordSettings] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n  \n  const [generalSuccess, setGeneralSuccess] = useState(false);\n  const [canSpamSuccess, setCanSpamSuccess] = useState(false);\n  const [passwordSuccess, setPasswordSuccess] = useState(false);\n  const [passwordError, setPasswordError] = useState('');\n  \n  const handleGeneralSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setGeneralSuccess(true);\n      setTimeout(() => setGeneralSuccess(false), 3000);\n    }, 500);\n  };\n  \n  const handleCanSpamSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setCanSpamSuccess(true);\n      setTimeout(() => setCanSpamSuccess(false), 3000);\n    }, 500);\n  };\n  \n  const handlePasswordSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!passwordSettings.currentPassword || !passwordSettings.newPassword || !passwordSettings.confirmPassword) {\n      setPasswordError('All password fields are required');\n      return;\n    }\n    \n    if (passwordSettings.newPassword !== passwordSettings.confirmPassword) {\n      setPasswordError('New passwords do not match');\n      return;\n    }\n    \n    if (passwordSettings.newPassword.length < 8) {\n      setPasswordError('New password must be at least 8 characters long');\n      return;\n    }\n    \n    // This would be an actual API call in the real app\n    setTimeout(() => {\n      setPasswordSuccess(true);\n      setPasswordSettings({\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n      });\n      setPasswordError('');\n      setTimeout(() => setPasswordSuccess(false), 3000);\n    }, 500);\n  };\n  \n  return (\n    // <Layout title=\"Settings\">\n    <>\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2\">\n          <Card title=\"General Settings\">\n            {generalSuccess && (\n              <Alert\n                type=\"success\"\n                message=\"General settings updated successfully!\"\n                className=\"mb-4\"\n              />\n            )}\n            \n            <form onSubmit={handleGeneralSubmit}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                <Input\n                  id=\"name\"\n                  name=\"name\"\n                  label=\"Full Name\"\n                  value={generalSettings.name}\n                  onChange={(e) => setGeneralSettings({...generalSettings, name: e.target.value})}\n                  required\n                />\n                \n                <Input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  label=\"Email Address\"\n                  value={generalSettings.email}\n                  onChange={(e) => setGeneralSettings({...generalSettings, email: e.target.value})}\n                  required\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n                <Input\n                  id=\"companyName\"\n                  name=\"companyName\"\n                  label=\"Company Name\"\n                  value={generalSettings.companyName}\n                  onChange={(e) => setGeneralSettings({...generalSettings, companyName: e.target.value})}\n                />\n                \n                <div>\n                  <label htmlFor=\"timezone\" className=\"form-label\">Timezone</label>\n                  <select\n                    id=\"timezone\"\n                    name=\"timezone\"\n                    value={generalSettings.timezone}\n                    onChange={(e) => setGeneralSettings({...generalSettings, timezone: e.target.value})}\n                    className=\"form-input w-full\"\n                  >\n                    <option value=\"UTC-12\">UTC-12</option>\n                    <option value=\"UTC-11\">UTC-11</option>\n                    <option value=\"UTC-10\">UTC-10</option>\n                    <option value=\"UTC-9\">UTC-9</option>\n                    <option value=\"UTC-8\">UTC-8 (Pacific)</option>\n                    <option value=\"UTC-7\">UTC-7 (Mountain)</option>\n                    <option value=\"UTC-6\">UTC-6 (Central)</option>\n                    <option value=\"UTC-5\">UTC-5 (Eastern)</option>\n                    <option value=\"UTC-4\">UTC-4</option>\n                    <option value=\"UTC-3\">UTC-3</option>\n                    <option value=\"UTC-2\">UTC-2</option>\n                    <option value=\"UTC-1\">UTC-1</option>\n                    <option value=\"UTC+0\">UTC+0</option>\n                    <option value=\"UTC+1\">UTC+1</option>\n                    <option value=\"UTC+2\">UTC+2</option>\n                    <option value=\"UTC+3\">UTC+3</option>\n                    <option value=\"UTC+4\">UTC+4</option>\n                    <option value=\"UTC+5\">UTC+5</option>\n                    <option value=\"UTC+6\">UTC+6</option>\n                    <option value=\"UTC+7\">UTC+7</option>\n                    <option value=\"UTC+8\">UTC+8</option>\n                    <option value=\"UTC+9\">UTC+9</option>\n                    <option value=\"UTC+10\">UTC+10</option>\n                    <option value=\"UTC+11\">UTC+11</option>\n                    <option value=\"UTC+12\">UTC+12</option>\n                  </select>\n                </div>\n              </div>\n              \n              <Button type=\"submit\">\n                Save General Settings\n              </Button>\n            </form>\n          </Card>\n          \n          <Card title=\"CAN-SPAM Compliance\" className=\"mt-6\">\n            {canSpamSuccess && (\n              <Alert\n                type=\"success\"\n                message=\"CAN-SPAM settings updated successfully!\"\n                className=\"mb-4\"\n              />\n            )}\n            \n            <p className=\"text-text-secondary mb-4\">\n              The CAN-SPAM Act requires that all commercial emails include your physical address and an unsubscribe option.\n            </p>\n            \n            <form onSubmit={handleCanSpamSubmit}>\n              <div className=\"mb-4\">\n                <label htmlFor=\"physicalAddress\" className=\"form-label\">\n                  Physical Address <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"physicalAddress\"\n                  name=\"physicalAddress\"\n                  value={canSpamSettings.physicalAddress}\n                  onChange={(e) => setCanSpamSettings({...canSpamSettings, physicalAddress: e.target.value})}\n                  required\n                  rows={3}\n                  className=\"form-input w-full\"\n                ></textarea>\n              </div>\n              \n              <div className=\"mb-6\">\n                <label htmlFor=\"unsubscribeText\" className=\"form-label\">\n                  Unsubscribe Text <span className=\"text-red-500\">*</span>\n                </label>\n                <textarea\n                  id=\"unsubscribeText\"\n                  name=\"unsubscribeText\"\n                  value={canSpamSettings.unsubscribeText}\n                  onChange={(e) => setCanSpamSettings({...canSpamSettings, unsubscribeText: e.target.value})}\n                  required\n                  rows={2}\n                  className=\"form-input w-full\"\n                ></textarea>\n              </div>\n              \n              <Button type=\"submit\">\n                Save CAN-SPAM Settings\n              </Button>\n            </form>\n          </Card>\n        </div>\n        \n        <div>\n          <Card title=\"Change Password\">\n            {passwordSuccess && (\n              <Alert\n                type=\"success\"\n                message=\"Password updated successfully!\"\n                className=\"mb-4\"\n              />\n            )}\n            \n            {passwordError && (\n              <Alert\n                type=\"error\"\n                message={passwordError}\n                onClose={() => setPasswordError('')}\n                className=\"mb-4\"\n              />\n            )}\n            \n            <form onSubmit={handlePasswordSubmit}>\n              <Input\n                id=\"currentPassword\"\n                name=\"currentPassword\"\n                type=\"password\"\n                label=\"Current Password\"\n                value={passwordSettings.currentPassword}\n                onChange={(e) => setPasswordSettings({...passwordSettings, currentPassword: e.target.value})}\n                required\n                className=\"mb-4\"\n              />\n              \n              <Input\n                id=\"newPassword\"\n                name=\"newPassword\"\n                type=\"password\"\n                label=\"New Password\"\n                value={passwordSettings.newPassword}\n                onChange={(e) => setPasswordSettings({...passwordSettings, newPassword: e.target.value})}\n                required\n                className=\"mb-4\"\n              />\n              \n              <Input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                label=\"Confirm New Password\"\n                value={passwordSettings.confirmPassword}\n                onChange={(e) => setPasswordSettings({...passwordSettings, confirmPassword: e.target.value})}\n                required\n                className=\"mb-6\"\n              />\n              \n              <Button type=\"submit\">\n                Update Password\n              </Button>\n            </form>\n          </Card>\n          \n          <Card title=\"Account Information\" className=\"mt-6\">\n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Account Type</div>\n              <div className=\"font-medium capitalize\">{user?.accountType || 'N/A'}</div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Member Since</div>\n              <div className=\"font-medium\">{formatDate(user?.createdAt)}</div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Domain Status</div>\n              <div className=\"font-medium\">\n                {user?.domain?.status ? (\n                   <span className={`px-2 py-1 text-xs rounded capitalize ${ \n                     user.domain.status === 'active' ? 'bg-green-800' : \n                     user.domain.status === 'pending' ? 'bg-yellow-800' : \n                     'bg-red-800'\n                   }`}>\n                     {user.domain.status}\n                   </span>\n                ) : (\n                   <span className=\"px-2 py-1 text-xs rounded bg-gray-700\">Not Setup</span>\n                )}\n              </div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Flows Purchased</div>\n              <div className=\"font-medium\">N/A</div>\n            </div>\n            \n            <div className=\"mb-4\">\n              <div className=\"text-text-secondary\">Flows Remaining</div>\n              <div className=\"font-medium\">N/A</div>\n            </div>\n          </Card>\n        </div>\n      </div>\n    </>\n    // </Layout>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAEvC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,OAAOC,MAAM,MAAM,sBAAsB;AACzC;AACA,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,qBAAqB;AACvC,SAASC,OAAO,QAAQ,yBAAyB,CAAC,CAAC;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,UAAU,GAAIC,UAA8B,IAAa;EAC7D,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;EAC7B,IAAI;IACF,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,cAAc;EACvB;AACF,CAAC;AAED,MAAME,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,YAAA;EAC/B,MAAM;IAAEC;EAAK,CAAC,GAAGjB,OAAO,CAAC,CAAC,CAAC,CAAC;;EAE5B,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC;IACrDyB,IAAI,EAAE,CAAAH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,IAAI,KAAI,UAAU;IAAE;IAChCC,KAAK,EAAE,CAAAJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,KAAK,KAAI,kBAAkB;IAAE;IAC1CC,WAAW,EAAE,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,WAAW,KAAI,WAAW;IAAE;IAC/CC,QAAQ,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,QAAQ,KAAI,OAAO,CAAE;EACvC,CAAC,CAAC;EAEF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC;IACrD+B,eAAe,EAAE,4CAA4C;IAC7DC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlC,QAAQ,CAAC;IACvDmC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE;EACnB,CAAC,CAAC;EAEF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC0C,eAAe,EAAEC,kBAAkB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM8C,mBAAmB,GAAIC,CAAkB,IAAK;IAClDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,UAAU,CAAC,MAAM;MACfV,iBAAiB,CAAC,IAAI,CAAC;MACvBU,UAAU,CAAC,MAAMV,iBAAiB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMW,mBAAmB,GAAIH,CAAkB,IAAK;IAClDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB;IACAC,UAAU,CAAC,MAAM;MACfR,iBAAiB,CAAC,IAAI,CAAC;MACvBQ,UAAU,CAAC,MAAMR,iBAAiB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IAClD,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAMU,oBAAoB,GAAIJ,CAAkB,IAAK;IACnDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACf,gBAAgB,CAACE,eAAe,IAAI,CAACF,gBAAgB,CAACG,WAAW,IAAI,CAACH,gBAAgB,CAACI,eAAe,EAAE;MAC3GQ,gBAAgB,CAAC,kCAAkC,CAAC;MACpD;IACF;IAEA,IAAIZ,gBAAgB,CAACG,WAAW,KAAKH,gBAAgB,CAACI,eAAe,EAAE;MACrEQ,gBAAgB,CAAC,4BAA4B,CAAC;MAC9C;IACF;IAEA,IAAIZ,gBAAgB,CAACG,WAAW,CAACgB,MAAM,GAAG,CAAC,EAAE;MAC3CP,gBAAgB,CAAC,iDAAiD,CAAC;MACnE;IACF;;IAEA;IACAI,UAAU,CAAC,MAAM;MACfN,kBAAkB,CAAC,IAAI,CAAC;MACxBT,mBAAmB,CAAC;QAClBC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFQ,gBAAgB,CAAC,EAAE,CAAC;MACpBI,UAAU,CAAC,MAAMN,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACnD,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED;IAAA;IACE;IACApC,OAAA,CAAAE,SAAA;MAAA4C,QAAA,eACE9C,OAAA;QAAK+C,SAAS,EAAC,uCAAuC;QAAAD,QAAA,gBACpD9C,OAAA;UAAK+C,SAAS,EAAC,eAAe;UAAAD,QAAA,gBAC5B9C,OAAA,CAACJ,IAAI;YAACoD,KAAK,EAAC,kBAAkB;YAAAF,QAAA,GAC3Bf,cAAc,iBACb/B,OAAA,CAACN,KAAK;cACJuD,IAAI,EAAC,SAAS;cACdC,OAAO,EAAC,wCAAwC;cAChDH,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACF,eAEDtD,OAAA;cAAMuD,QAAQ,EAAEhB,mBAAoB;cAAAO,QAAA,gBAClC9C,OAAA;gBAAK+C,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,gBACzD9C,OAAA,CAACH,KAAK;kBACJ2D,EAAE,EAAC,MAAM;kBACTtC,IAAI,EAAC,MAAM;kBACXuC,KAAK,EAAC,WAAW;kBACjBC,KAAK,EAAE1C,eAAe,CAACE,IAAK;kBAC5ByC,QAAQ,EAAGnB,CAAC,IAAKvB,kBAAkB,CAAC;oBAAC,GAAGD,eAAe;oBAAEE,IAAI,EAAEsB,CAAC,CAACoB,MAAM,CAACF;kBAAK,CAAC,CAAE;kBAChFG,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eAEFtD,OAAA,CAACH,KAAK;kBACJ2D,EAAE,EAAC,OAAO;kBACVtC,IAAI,EAAC,OAAO;kBACZ+B,IAAI,EAAC,OAAO;kBACZQ,KAAK,EAAC,eAAe;kBACrBC,KAAK,EAAE1C,eAAe,CAACG,KAAM;kBAC7BwC,QAAQ,EAAGnB,CAAC,IAAKvB,kBAAkB,CAAC;oBAAC,GAAGD,eAAe;oBAAEG,KAAK,EAAEqB,CAAC,CAACoB,MAAM,CAACF;kBAAK,CAAC,CAAE;kBACjFG,QAAQ;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtD,OAAA;gBAAK+C,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,gBACzD9C,OAAA,CAACH,KAAK;kBACJ2D,EAAE,EAAC,aAAa;kBAChBtC,IAAI,EAAC,aAAa;kBAClBuC,KAAK,EAAC,cAAc;kBACpBC,KAAK,EAAE1C,eAAe,CAACI,WAAY;kBACnCuC,QAAQ,EAAGnB,CAAC,IAAKvB,kBAAkB,CAAC;oBAAC,GAAGD,eAAe;oBAAEI,WAAW,EAAEoB,CAAC,CAACoB,MAAM,CAACF;kBAAK,CAAC;gBAAE;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CAAC,eAEFtD,OAAA;kBAAA8C,QAAA,gBACE9C,OAAA;oBAAO8D,OAAO,EAAC,UAAU;oBAACf,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAC;kBAAQ;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjEtD,OAAA;oBACEwD,EAAE,EAAC,UAAU;oBACbtC,IAAI,EAAC,UAAU;oBACfwC,KAAK,EAAE1C,eAAe,CAACK,QAAS;oBAChCsC,QAAQ,EAAGnB,CAAC,IAAKvB,kBAAkB,CAAC;sBAAC,GAAGD,eAAe;sBAAEK,QAAQ,EAAEmB,CAAC,CAACoB,MAAM,CAACF;oBAAK,CAAC,CAAE;oBACpFX,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,gBAE7B9C,OAAA;sBAAQ0D,KAAK,EAAC,QAAQ;sBAAAZ,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtD,OAAA;sBAAQ0D,KAAK,EAAC,QAAQ;sBAAAZ,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtD,OAAA;sBAAQ0D,KAAK,EAAC,QAAQ;sBAAAZ,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAe;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAgB;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC/CtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAe;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAe;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,OAAO;sBAAAZ,QAAA,EAAC;oBAAK;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtD,OAAA;sBAAQ0D,KAAK,EAAC,QAAQ;sBAAAZ,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtD,OAAA;sBAAQ0D,KAAK,EAAC,QAAQ;sBAAAZ,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtD,OAAA;sBAAQ0D,KAAK,EAAC,QAAQ;sBAAAZ,QAAA,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtD,OAAA,CAACL,MAAM;gBAACsD,IAAI,EAAC,QAAQ;gBAAAH,QAAA,EAAC;cAEtB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPtD,OAAA,CAACJ,IAAI;YAACoD,KAAK,EAAC,qBAAqB;YAACD,SAAS,EAAC,MAAM;YAAAD,QAAA,GAC/Cb,cAAc,iBACbjC,OAAA,CAACN,KAAK;cACJuD,IAAI,EAAC,SAAS;cACdC,OAAO,EAAC,yCAAyC;cACjDH,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACF,eAEDtD,OAAA;cAAG+C,SAAS,EAAC,0BAA0B;cAAAD,QAAA,EAAC;YAExC;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJtD,OAAA;cAAMuD,QAAQ,EAAEZ,mBAAoB;cAAAG,QAAA,gBAClC9C,OAAA;gBAAK+C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnB9C,OAAA;kBAAO8D,OAAO,EAAC,iBAAiB;kBAACf,SAAS,EAAC,YAAY;kBAAAD,QAAA,GAAC,mBACrC,eAAA9C,OAAA;oBAAM+C,SAAS,EAAC,cAAc;oBAAAD,QAAA,EAAC;kBAAC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACRtD,OAAA;kBACEwD,EAAE,EAAC,iBAAiB;kBACpBtC,IAAI,EAAC,iBAAiB;kBACtBwC,KAAK,EAAEpC,eAAe,CAACE,eAAgB;kBACvCmC,QAAQ,EAAGnB,CAAC,IAAKjB,kBAAkB,CAAC;oBAAC,GAAGD,eAAe;oBAAEE,eAAe,EAAEgB,CAAC,CAACoB,MAAM,CAACF;kBAAK,CAAC,CAAE;kBAC3FG,QAAQ;kBACRE,IAAI,EAAE,CAAE;kBACRhB,SAAS,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAENtD,OAAA;gBAAK+C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnB9C,OAAA;kBAAO8D,OAAO,EAAC,iBAAiB;kBAACf,SAAS,EAAC,YAAY;kBAAAD,QAAA,GAAC,mBACrC,eAAA9C,OAAA;oBAAM+C,SAAS,EAAC,cAAc;oBAAAD,QAAA,EAAC;kBAAC;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACRtD,OAAA;kBACEwD,EAAE,EAAC,iBAAiB;kBACpBtC,IAAI,EAAC,iBAAiB;kBACtBwC,KAAK,EAAEpC,eAAe,CAACG,eAAgB;kBACvCkC,QAAQ,EAAGnB,CAAC,IAAKjB,kBAAkB,CAAC;oBAAC,GAAGD,eAAe;oBAAEG,eAAe,EAAEe,CAAC,CAACoB,MAAM,CAACF;kBAAK,CAAC,CAAE;kBAC3FG,QAAQ;kBACRE,IAAI,EAAE,CAAE;kBACRhB,SAAS,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAENtD,OAAA,CAACL,MAAM;gBAACsD,IAAI,EAAC,QAAQ;gBAAAH,QAAA,EAAC;cAEtB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENtD,OAAA;UAAA8C,QAAA,gBACE9C,OAAA,CAACJ,IAAI;YAACoD,KAAK,EAAC,iBAAiB;YAAAF,QAAA,GAC1BX,eAAe,iBACdnC,OAAA,CAACN,KAAK;cACJuD,IAAI,EAAC,SAAS;cACdC,OAAO,EAAC,gCAAgC;cACxCH,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACF,EAEAjB,aAAa,iBACZrC,OAAA,CAACN,KAAK;cACJuD,IAAI,EAAC,OAAO;cACZC,OAAO,EAAEb,aAAc;cACvB2B,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC,EAAE,CAAE;cACpCS,SAAS,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CACF,eAEDtD,OAAA;cAAMuD,QAAQ,EAAEX,oBAAqB;cAAAE,QAAA,gBACnC9C,OAAA,CAACH,KAAK;gBACJ2D,EAAE,EAAC,iBAAiB;gBACpBtC,IAAI,EAAC,iBAAiB;gBACtB+B,IAAI,EAAC,UAAU;gBACfQ,KAAK,EAAC,kBAAkB;gBACxBC,KAAK,EAAEhC,gBAAgB,CAACE,eAAgB;gBACxC+B,QAAQ,EAAGnB,CAAC,IAAKb,mBAAmB,CAAC;kBAAC,GAAGD,gBAAgB;kBAAEE,eAAe,EAAEY,CAAC,CAACoB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBAC7FG,QAAQ;gBACRd,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eAEFtD,OAAA,CAACH,KAAK;gBACJ2D,EAAE,EAAC,aAAa;gBAChBtC,IAAI,EAAC,aAAa;gBAClB+B,IAAI,EAAC,UAAU;gBACfQ,KAAK,EAAC,cAAc;gBACpBC,KAAK,EAAEhC,gBAAgB,CAACG,WAAY;gBACpC8B,QAAQ,EAAGnB,CAAC,IAAKb,mBAAmB,CAAC;kBAAC,GAAGD,gBAAgB;kBAAEG,WAAW,EAAEW,CAAC,CAACoB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBACzFG,QAAQ;gBACRd,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eAEFtD,OAAA,CAACH,KAAK;gBACJ2D,EAAE,EAAC,iBAAiB;gBACpBtC,IAAI,EAAC,iBAAiB;gBACtB+B,IAAI,EAAC,UAAU;gBACfQ,KAAK,EAAC,sBAAsB;gBAC5BC,KAAK,EAAEhC,gBAAgB,CAACI,eAAgB;gBACxC6B,QAAQ,EAAGnB,CAAC,IAAKb,mBAAmB,CAAC;kBAAC,GAAGD,gBAAgB;kBAAEI,eAAe,EAAEU,CAAC,CAACoB,MAAM,CAACF;gBAAK,CAAC,CAAE;gBAC7FG,QAAQ;gBACRd,SAAS,EAAC;cAAM;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,eAEFtD,OAAA,CAACL,MAAM;gBAACsD,IAAI,EAAC,QAAQ;gBAAAH,QAAA,EAAC;cAEtB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPtD,OAAA,CAACJ,IAAI;YAACoD,KAAK,EAAC,qBAAqB;YAACD,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAChD9C,OAAA;cAAK+C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9C,OAAA;gBAAK+C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAAC;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDtD,OAAA;gBAAK+C,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,EAAE,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,WAAW,KAAI;cAAK;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eAENtD,OAAA;cAAK+C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9C,OAAA;gBAAK+C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAAC;cAAY;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvDtD,OAAA;gBAAK+C,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAE3C,UAAU,CAACY,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,SAAS;cAAC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eAENtD,OAAA;cAAK+C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9C,OAAA;gBAAK+C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxDtD,OAAA;gBAAK+C,SAAS,EAAC,aAAa;gBAAAD,QAAA,EACzB/B,IAAI,aAAJA,IAAI,gBAAAD,YAAA,GAAJC,IAAI,CAAEoD,MAAM,cAAArD,YAAA,eAAZA,YAAA,CAAcsD,MAAM,gBAClBpE,OAAA;kBAAM+C,SAAS,EAAE,wCACfhC,IAAI,CAACoD,MAAM,CAACC,MAAM,KAAK,QAAQ,GAAG,cAAc,GAChDrD,IAAI,CAACoD,MAAM,CAACC,MAAM,KAAK,SAAS,GAAG,eAAe,GAClD,YAAY,EACX;kBAAAtB,QAAA,EACA/B,IAAI,CAACoD,MAAM,CAACC;gBAAM;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,gBAEPtD,OAAA;kBAAM+C,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cACzE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cAAK+C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9C,OAAA;gBAAK+C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DtD,OAAA;gBAAK+C,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAENtD,OAAA;cAAK+C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnB9C,OAAA;gBAAK+C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DtD,OAAA;gBAAK+C,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAG;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC,gBACN;IACF;EAAA;AAEJ,CAAC;AAACzC,EAAA,CA7TID,QAAkB;EAAA,QACLd,OAAO;AAAA;AAAAuE,EAAA,GADpBzD,QAAkB;AA+TxB,eAAeA,QAAQ;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}