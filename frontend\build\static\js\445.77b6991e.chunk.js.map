{"version": 3, "file": "static/js/445.77b6991e.chunk.js", "mappings": "gMAoCA,MAgRA,EAhRuCA,KACrC,MAAOC,EAAcC,IAAmBC,EAAAA,EAAAA,UAAwB,KACzDC,EAASC,IAAcF,EAAAA,EAAAA,WAAkB,IACzCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAwB,OAC3CK,EAAiBC,IAAsBN,EAAAA,EAAAA,UAAiC,OACxEO,EAAWC,IAAgBR,EAAAA,EAAAA,UAAwB,OACnDS,EAAmBC,IAAwBV,EAAAA,EAAAA,UAAiB,KAC5DW,EAAUC,IAAeZ,EAAAA,EAAAA,UAAoB,KAC7Ca,EAAYC,IAAiBd,EAAAA,EAAAA,UAAgC,OAGpEe,EAAAA,EAAAA,YAAU,KACiBC,WACvBd,GAAW,GACXE,EAAS,MACT,IACE,MAAOa,EAAkBC,SAAuBC,QAAQC,IAAI,CAC1DC,EAAAA,GAAeC,kBACfC,EAAAA,GAAgBC,oBAGdP,EAAiBQ,QACnBb,EAAYc,MAAMC,QAAQV,EAAiBW,MAAQX,EAAiBW,KAAO,IAE3ExB,GAASyB,GAAQA,EAAO,GAAGA,MAASZ,EAAiBa,UAAYb,EAAiBa,SAAW,6BAG3FZ,EAAcO,QAEhB1B,EAAgB2B,MAAMC,QAAQT,EAAcU,MAAQV,EAAcU,KAAO,KAEzExB,GAASyB,GAAQA,EAAO,GAAGA,MAASX,EAAcY,UAAYZ,EAAcY,SAAW,kCACvF/B,EAAgB,IAGpB,CAAE,MAAOgC,GACP3B,EAAS2B,EAAID,SAAW,gDAC1B,CAAC,QACC5B,GAAW,EACb,GAGF8B,EAAkB,GACjB,KAGHjB,EAAAA,EAAAA,YAAU,KACR,IAAKV,GAAiC,YAAdE,EAGpB,OAFIM,GAAYoB,cAAcpB,QAC9BC,EAAc,MAIlB,MAAMoB,EAAgBlB,UACpB,IACEmB,QAAQC,IAAI,0BAA0B/B,EAAgBgC,SACtD,MAAMC,QAAiBf,EAAAA,GAAgBgB,aAAalC,EAAgBgC,IACpE,GAAIC,EAASb,QAAS,CACpB,MAAMe,EAAgBF,EAASV,KAAKa,OAKpC,GAJAjC,EAAagC,GACbL,QAAQC,IAAI,eAAeI,KAGL,cAAlBA,GAAmD,WAAlBA,EAKnC,GAJI3B,GAAYoB,cAAcpB,GAC9BC,EAAc,MACdR,EAAmB,MAEG,cAAlBkC,EAA+B,CAC9BL,QAAQC,IAAI,4CACZ,MAAMlB,QAAsBK,EAAAA,GAAgBC,kBACxCN,EAAcO,QAChB1B,EAAgB2B,MAAMC,QAAQT,EAAcU,MAAQV,EAAcU,KAAO,IAEvExB,EAAS,8DAElB,MACKA,EAAS,2DAGlB,MACI+B,QAAQhC,MAAM,6BAA8BmC,EAASR,QAM3D,CAAE,MAAOC,GACPI,QAAQhC,MAAM,4BAA6B4B,EAK7C,GAIF,IAAKlB,EAAY,CACbsB,QAAQC,IAAI,2BACd,MAAMM,EAAaC,YAAYT,EAAe,KAC9CpB,EAAc4B,EAChB,CAGA,MAAO,KACD7B,IACFsB,QAAQC,IAAI,kCACZH,cAAcpB,GAChB,CACD,GACA,CAACR,EAAiBE,IAGrB,MA0BMqC,EAAsB9C,EAAa+C,QAAO,CAACC,EAAKC,KACpD,MAAMC,EAAMD,EAAKE,WAAa,UAK9B,OAJKH,EAAIE,KACPF,EAAIE,GAAO,IAEbF,EAAIE,GAAKE,KAAKH,GACPD,CAAG,GACT,CAAC,GAMJ,OAEIK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SAAC,yCAE3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,0EAIvClD,IACCmD,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CAACC,KAAK,QAAQ1B,QAAS3B,EAAOsD,QAASA,IAAMrD,EAAS,MAAOgD,UAAU,UAG/EE,EAAAA,EAAAA,KAACI,EAAAA,EAAI,CAACN,UAAU,OAAMC,UACpBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EACjBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,sBAC7DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEnDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIC,EAAAA,EAAAA,KAAA,SAAOK,QAAQ,YAAYP,UAAU,qDAAoDC,SAAC,wBAG1FF,EAAAA,EAAAA,MAAA,UACEd,GAAG,YACHuB,KAAK,YACLC,MAAOpD,EACPqD,SAAWC,GAAsCrD,EAAqBqD,EAAEC,OAAOH,OAC/ET,UAAU,6KAA4KC,SAAA,EAEpLC,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAER,SAAC,mBAChB1C,EAASsD,KAAKC,IACXZ,EAAAA,EAAAA,KAAA,UAAyBO,MAAOK,EAAQ7B,GAAGgB,SAAEa,EAAQN,MAAxCM,EAAQ7B,UAG7BiB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAC,kEAGpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC5BC,EAAAA,EAAAA,KAACa,EAAAA,EAAM,CACNC,QA/EYpD,UAC5BZ,EAAS,MACTI,EAAa,MACbF,EAAmB,MACnBJ,GAAW,GAEX,IACE,MAAMoC,QAAiBf,EAAAA,GAAgB8C,oBACrC5D,QAAqB6D,GAGnBhC,EAASb,SACXnB,EAAmBgC,EAASV,MAC5BpB,EAAa,WACb2B,QAAQC,IAAI,4BAA6BE,EAASV,OAElDxB,EAASkC,EAASR,SAAW,mCAEjC,CAAE,MAAOC,GACP3B,EAAS2B,EAAID,SAAW,gDAC1B,CAAC,QACC5B,GAAW,EACb,GA0DgBqE,SAAUtE,GAAyB,YAAdM,EACrB6C,UAAU,mBAAmBC,SAEd,YAAd9C,EAA0B,0BAA4BN,EAAU,cAAgB,0BAMzE,YAAdM,IACE+C,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CACJC,KAAK,OACL1B,QAAS,qCAAqCvB,oCAC9C6C,UAAU,eAMpBE,EAAAA,EAAAA,KAACI,EAAAA,EAAI,CAAAL,UACFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EACjBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,uBAE7DpD,GAAmC,IAAxBH,EAAa0E,QACtBlB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACtCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+EAEO,IAAxBtD,EAAa0E,QACdlB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SAAC,uFAE5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SA1ErB,CAAC,SAAU,UAAW,YAAa,WAAY,SAAU,WAAY,UA2E5DY,KAAKjB,IACf,MAAMyB,EAAc7B,EAAoBI,IAAQ,GAChD,OAA2B,IAAvByB,EAAYD,OAAqB,MAGnCrB,EAAAA,EAAAA,MAAA,OAAeC,UAAU,gCAA+BC,SAAA,EACtDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAEL,KAC5DM,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAAqEC,SACjFoB,EAAYR,KAAI,CAAClB,EAAM2B,KACtBvB,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,2CAA0CC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,SAChDN,EAAKA,MAAQ,cAEhBO,EAAAA,EAAAA,KAAA,OAAKF,UAAW,4DACM,SAApBL,EAAK4B,WAAwB,8BACT,WAApB5B,EAAK4B,WAA0B,gCACX,QAApB5B,EAAK4B,WAAuB,0BAC5B,6BACCtB,SACAN,EAAK4B,YAAc,gBAGxBrB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCAAkCC,SAC9CN,EAAKmB,QAAU,YAAYnB,EAAKmB,UAAY,oBAE/Cf,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,CAAC,YACtCN,EAAK6B,YAAc,IAAI,mBAEnCzB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,UAASC,SAAA,EACtBC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAC,sBACtCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BC,SAAEN,EAAK8B,kBAAoB,aAtBpEH,SAJN1B,EA+BJ,aAQxB,C", "sources": ["pages/SendTimeOptimization.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button'; // Assuming components exist\nimport Card from '../components/Card';\n// import Sidebar from '../components/layout/Sidebar'; // Remove Sidebar import\nimport {\n  segmentService,\n  sendTimeService,\n} from '../services';\n\n// Define interfaces if needed\ninterface OptimalTime {\n  dayOfWeek: string;\n  time: string;\n  confidence: 'high' | 'medium' | 'low' | string; // Allow string for fallback\n  segment?: string;\n  dataPoints?: number;\n  expectedOpenRate?: string;\n  // Add other properties if they exist\n}\n\ninterface Segment {\n  id: string;\n  name: string;\n}\n\ninterface OptimizationJob {\n  id: string;\n  // Add other job properties if needed\n}\n\nconst SendTimeOptimization: React.FC = () => {\n  const [optimalTimes, setOptimalTimes] = useState<OptimalTime[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [optimizationJob, setOptimizationJob] = useState<OptimizationJob | null>(null);\n  const [jobStatus, setJobStatus] = useState<string | null>(null); // e.g., 'running', 'completed', 'failed'\n  const [selectedSegmentId, setSelectedSegmentId] = useState<string>('');\n  const [segments, setSegments] = useState<Segment[]>([]);\n  const [jobPolling, setJobPolling] = useState<NodeJS.Timeout | null>(null); // Type for interval ID\n\n  // Fetch optimal send times on component mount\n  useEffect(() => {\n    const fetchInitialData = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const [segmentsResponse, timesResponse] = await Promise.all([\n          segmentService.getUserSegments(),\n          sendTimeService.getOptimalTimes()\n        ]);\n\n        if (segmentsResponse.success) {\n          setSegments(Array.isArray(segmentsResponse.data) ? segmentsResponse.data : []);\n        } else {\n          setError(prev => prev ? `${prev}; ${segmentsResponse.message}` : segmentsResponse.message || 'Failed to fetch segments');\n        }\n\n        if (timesResponse.success) {\n          // Ensure data is an array before setting state\n          setOptimalTimes(Array.isArray(timesResponse.data) ? timesResponse.data : []);\n        } else {\n          setError(prev => prev ? `${prev}; ${timesResponse.message}` : timesResponse.message || 'Failed to fetch optimal times');\n          setOptimalTimes([]); // Explicitly set empty on fetch error\n        }\n\n      } catch (err: any) {\n        setError(err.message || 'An error occurred while fetching initial data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchInitialData();\n  }, []);\n\n  // Poll job status when a job is running\n  useEffect(() => {\n    if (!optimizationJob || jobStatus !== 'running') {\n        if (jobPolling) clearInterval(jobPolling);\n        setJobPolling(null);\n        return; // Stop polling if no job or job not running\n    }\n\n    const pollJobStatus = async () => {\n      try {\n        console.log(`Polling job status for ${optimizationJob.id}...`);\n        const response = await sendTimeService.getJobStatus(optimizationJob.id);\n        if (response.success) {\n          const currentStatus = response.data.status;\n          setJobStatus(currentStatus);\n          console.log(`Job status: ${currentStatus}`);\n\n          // If job is complete or failed, fetch updated optimal times and clear polling\n          if (currentStatus === 'completed' || currentStatus === 'failed') {\n            if (jobPolling) clearInterval(jobPolling);\n            setJobPolling(null);\n            setOptimizationJob(null); // Clear the job\n\n            if (currentStatus === 'completed') {\n                 console.log('Job completed, fetching updated times...');\n                 const timesResponse = await sendTimeService.getOptimalTimes();\n                 if (timesResponse.success) {\n                   setOptimalTimes(Array.isArray(timesResponse.data) ? timesResponse.data : []);\n                 } else {\n                     setError('Failed to fetch updated optimal times after job completion.');\n                 }\n            } else {\n                 setError('Optimization job failed. Please check logs or try again.');\n            }\n          }\n        } else {\n            console.error('Failed to poll job status:', response.message);\n            // Decide if polling should stop on error\n            // clearInterval(jobPolling); \n            // setJobPolling(null);\n            // setError('Error checking job status.');\n        }\n      } catch (err) {\n        console.error('Error polling job status:', err);\n        // Consider stopping polling on error\n        // if (jobPolling) clearInterval(jobPolling);\n        // setJobPolling(null);\n        // setError('An error occurred while checking job status.');\n      }\n    };\n\n    // Set up polling interval only if not already polling\n    if (!jobPolling) {\n        console.log('Starting job polling...');\n      const intervalId = setInterval(pollJobStatus, 5000); // Poll every 5 seconds\n      setJobPolling(intervalId);\n    }\n\n    // Clean up interval on unmount or when job changes/completes\n    return () => {\n      if (jobPolling) {\n        console.log('Clearing job polling interval.');\n        clearInterval(jobPolling);\n      }\n    };\n  }, [optimizationJob, jobStatus]); // Rerun effect if job or status changes\n\n  // Handle running bulk optimization\n  const handleRunOptimization = async () => {\n    setError(null);\n    setJobStatus(null);\n    setOptimizationJob(null);\n    setLoading(true);\n\n    try {\n      const response = await sendTimeService.runBulkOptimization(\n        selectedSegmentId || undefined\n      );\n\n      if (response.success) {\n        setOptimizationJob(response.data); // Set the job object\n        setJobStatus('running'); // Start polling\n        console.log('Optimization job started:', response.data);\n      } else {\n        setError(response.message || 'Failed to start optimization job');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while starting optimization');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Group optimal times by day of week\n  const groupedOptimalTimes = optimalTimes.reduce((acc, time) => {\n    const day = time.dayOfWeek || 'Unknown';\n    if (!acc[day]) {\n      acc[day] = [];\n    }\n    acc[day].push(time);\n    return acc;\n  }, {} as Record<string, OptimalTime[]>); // Add type to accumulator\n\n  // Days of week for sorting\n  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];\n\n  // Remove Sidebar wrapper\n  return (\n    // <Sidebar> // Removed\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"flex justify-between items-center mb-6\">\n          <h1 className=\"text-2xl font-semibold text-text-primary\">Predictive Send-Time Optimization</h1>\n        </div>\n        <p className=\"text-text-secondary mb-6\">\n            Optimize email delivery times based on recipient engagement patterns.\n        </p>\n\n        {error && (\n          <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-6\" />\n        )}\n\n        <Card className=\"mb-6\">\n          <div className=\"p-6\">\n             <h2 className=\"text-lg font-semibold text-text-primary mb-4\">Run Optimization</h2>\n             <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2\">\n               {/* Use standard select element */}\n                <div>\n                    <label htmlFor=\"segmentId\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                        Segment (Optional)\n                    </label>\n                    <select\n                      id=\"segmentId\"\n                      name=\"segmentId\"\n                      value={selectedSegmentId}\n                      onChange={(e: ChangeEvent<HTMLSelectElement>) => setSelectedSegmentId(e.target.value)}\n                      className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary\"\n                    >\n                        <option value=\"\">All Recipients</option>\n                        {segments.map((segment) => (\n                            <option key={segment.id} value={segment.id}>{segment.name}</option>\n                        ))}\n                    </select>\n                    <p className=\"mt-1 text-xs text-text-secondary\">Select a segment to optimize for, or leave blank for all.</p>\n                </div>\n\n                <div className=\"flex items-end\">\n                   <Button\n                    onClick={handleRunOptimization}\n                    disabled={loading || jobStatus === 'running'}\n                    className=\"w-full sm:w-auto\" // Adjust button width\n                  >\n                    {jobStatus === 'running' ? 'Optimization Running...' : loading ? 'Starting...' : 'Run Optimization'}\n                  </Button>\n                </div>\n              </div>\n\n             {/* Fix: Correct JSX conditional rendering */} \n             {jobStatus === 'running' && (\n                <Alert \n                  type=\"info\" \n                  message={`Optimization in progress. Status: ${jobStatus}. This may take a few minutes...`} \n                  className=\"mt-4\" \n                />\n             )}\n          </div>\n        </Card>\n\n        <Card>\n           <div className=\"p-6\">\n              <h2 className=\"text-lg font-semibold text-text-primary mb-4\">Optimal Send Times</h2>\n\n             {loading && optimalTimes.length === 0 ? (\n                <div className=\"flex justify-center py-8\">\n                   <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n                </div>\n             ) : optimalTimes.length === 0 ? (\n                <p className=\"text-sm text-text-secondary text-center py-4\">No optimal send times available. Run an optimization to generate recommendations.</p>\n             ) : (\n                <div className=\"space-y-6\">\n                  {daysOfWeek.map((day) => {\n                    const timesForDay = groupedOptimalTimes[day] || [];\n                    if (timesForDay.length === 0) return null;\n\n                    return (\n                      <div key={day} className=\"border-t border-gray-700 pt-4\">\n                        <h3 className=\"text-md font-medium text-text-primary mb-3\">{day}</h3>\n                        <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\n                          {timesForDay.map((time, index) => (\n                            <div key={index} className=\"bg-secondary-bg p-4 rounded-lg shadow-md\">\n                              <div className=\"flex justify-between items-start mb-2\">\n                                <div className=\"text-lg font-semibold text-primary\">\n                                  {time.time || 'Time N/A'}\n                                </div>\n                                <div className={`px-2 py-0.5 rounded-full text-xs font-medium capitalize ${ \n                                  time.confidence === 'high' ? 'bg-green-800 text-green-100' : \n                                  time.confidence === 'medium' ? 'bg-yellow-800 text-yellow-100' : \n                                  time.confidence === 'low' ? 'bg-red-800 text-red-100' :\n                                  'bg-gray-700 text-gray-300' \n                                }`}>\n                                  {time.confidence || 'unknown'}\n                                </div>\n                              </div>\n                              <div className=\"text-sm text-text-secondary mb-1\">\n                                {time.segment ? `Segment: ${time.segment}` : 'All Recipients'}\n                              </div>\n                              <div className=\"text-sm text-text-secondary mb-2\">\n                                Based on {time.dataPoints || '?'} data points\n                              </div>\n                              <div className=\"text-sm\">\n                                <span className=\"text-text-secondary\">Est. Open Rate: </span>\n                                <span className=\"font-medium text-text-primary\">{time.expectedOpenRate || 'N/A'}</span>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              )}\n           </div>\n        </Card>\n      </div>\n    // </Sidebar> // Removed\n  );\n};\n\nexport default SendTimeOptimization;\n"], "names": ["SendTimeOptimization", "optimalTimes", "setOptimalTimes", "useState", "loading", "setLoading", "error", "setError", "optimizationJob", "setOptimizationJob", "jobStatus", "setJobStatus", "selectedSegmentId", "setSelectedSegmentId", "segments", "setSegments", "jobPolling", "setJob<PERSON><PERSON>ing", "useEffect", "async", "segmentsResponse", "timesResponse", "Promise", "all", "segmentService", "getUserSegments", "sendTimeService", "getOptimalTimes", "success", "Array", "isArray", "data", "prev", "message", "err", "fetchInitialData", "clearInterval", "pollJobStatus", "console", "log", "id", "response", "getJobStatus", "currentStatus", "status", "intervalId", "setInterval", "groupedOptimalTimes", "reduce", "acc", "time", "day", "dayOfWeek", "push", "_jsxs", "className", "children", "_jsx", "<PERSON><PERSON>", "type", "onClose", "Card", "htmlFor", "name", "value", "onChange", "e", "target", "map", "segment", "<PERSON><PERSON>", "onClick", "runBulkOptimization", "undefined", "disabled", "length", "timesForDay", "index", "confidence", "dataPoints", "expectedOpenRate"], "sourceRoot": ""}