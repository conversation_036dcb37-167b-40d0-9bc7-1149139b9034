import dotenv from 'dotenv';
import mongoose from 'mongoose'; // Import mongoose itself

import dbService
  from '../config/database'; // Use default import for the service instance
import Template from '../models/template.model';

dotenv.config();

// Delete the model from Mongoose's cache if it exists
if (mongoose.models.Template) {
  delete mongoose.models.Template;
}

// Clear require cache for the model to ensure the latest version is loaded
const modelPath = require.resolve('../models/template.model');
delete require.cache[modelPath];

// Basic structure for many templates
const basicBusinessTemplate = (title: string, bodyText: string, ctaText: string, ctaUrl: string = '#') => `
<table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width: 600px; margin: 0 auto; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
  <tr>
    <td style="padding: 20px 0; text-align: center; background-color: #1E40AF;">
      <h1 style="color: #FFFFFF; font-size: 28px; margin: 0; padding: 0 20px;">${title}</h1>
    </td>
  </tr>
  <tr>
    <td style="background-color: #FFFFFF; padding: 40px 20px;">
      <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
          Hi {{name}},
      </p>
      <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
          ${bodyText}
      </p>
      <p style="text-align: center; margin: 30px 0;">
        <a href="${ctaUrl}" style="display: inline-block; padding: 14px 30px; background-color: #1E40AF; color: #FFFFFF; text-decoration: none; font-weight: bold; border-radius: 4px; font-size: 16px;">${ctaText}</a>
      </p>
      <p style="color: #666666; font-size: 14px; line-height: 1.4; margin: 30px 0 0 0;">
        Thanks,<br>
          The Team at {{YourCompanyName}}
      </p>
    </td>
  </tr>
  <tr>
    <td style="background-color: #F3F4F6; padding: 20px; text-align: center;">
      <p style="color: #6B7280; font-size: 12px; margin: 0 0 10px 0;">
        © ${new Date().getFullYear()} {{YourCompanyName}}. All rights reserved.
      </p>
      <p style="color: #6B7280; font-size: 12px; margin: 0;">
        <a href="{{unsubscribeUrl}}" style="color: #6B7280; text-decoration: underline;">Unsubscribe</a> | {{YourCompanyAddress}}
      </p>
    </td>
  </tr>
</table>
`;

// Modern confirmation template
const confirmationTemplate = (title: string, orderDetails: string) => `
<table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width: 600px; margin: 0 auto; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
  <tr>
    <td style="padding: 25px 0; text-align: center; background-color: #10B981;">
      <h1 style="color: #FFFFFF; font-size: 28px; margin: 0; padding: 0 20px;">${title}</h1>
    </td>
  </tr>
  <tr>
    <td style="background-color: #FFFFFF; padding: 40px 20px;">
      <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
          Hi {{name}},
      </p>
      <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
        Thank you for your order! We've received it and are processing it right away.
      </p>
      
      <table width="100%" cellpadding="0" cellspacing="0" border="0" style="background-color: #F9FAFB; border-radius: 6px; margin-bottom: 30px;">
        <tr>
          <td style="padding: 20px;">
            <h2 style="color: #111827; font-size: 18px; margin: 0 0 15px 0;">Order Summary</h2>
            <div style="color: #4B5563; font-size: 16px; line-height: 1.6;">
          ${orderDetails}
            </div>
          </td>
        </tr>
      </table>
      
      <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
        We'll let you know when your order ships. If you have any questions, please feel free to contact our support team.
      </p>
      
      <p style="text-align: center; margin: 30px 0;">
        <a href="{{trackOrderUrl}}" style="display: inline-block; padding: 14px 30px; background-color: #10B981; color: #FFFFFF; text-decoration: none; font-weight: bold; border-radius: 4px; font-size: 16px;">Track Your Order</a>
      </p>
      
      <p style="color: #666666; font-size: 14px; line-height: 1.4; margin: 30px 0 0 0;">
        Thanks for your business,<br>
        The Team at {{YourCompanyName}}
      </p>
    </td>
  </tr>
  <tr>
    <td style="background-color: #F3F4F6; padding: 20px; text-align: center;">
      <p style="color: #6B7280; font-size: 12px; margin: 0 0 10px 0;">
        © ${new Date().getFullYear()} {{YourCompanyName}}. All rights reserved.
      </p>
      <p style="color: #6B7280; font-size: 12px; margin: 0;">
        <a href="{{unsubscribeUrl}}" style="color: #6B7280; text-decoration: underline;">Unsubscribe</a> | {{YourCompanyAddress}}
      </p>
    </td>
  </tr>
</table>
`;

// Welcome email template
const welcomeTemplate = (title: string, welcomeText: string, ctaText: string, ctaUrl: string = '#') => `
<table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width: 600px; margin: 0 auto; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
  <tr>
    <td style="padding: 0;">
      <table width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td style="padding: 30px 0; text-align: center; background-color: #4F46E5;">
            <h1 style="color: #FFFFFF; font-size: 30px; margin: 0; padding: 0 20px;">${title}</h1>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td style="background-color: #FFFFFF; padding: 0;">
      <table width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td style="padding: 40px 30px;">
            <p style="color: #333333; font-size: 18px; line-height: 1.6; margin: 0 0 20px 0; font-weight: bold;">
              Hi {{name}},
            </p>
            <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
              ${welcomeText}
            </p>
            
            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin: 30px 0;">
              <tr>
                <td align="center">
                  <table cellpadding="0" cellspacing="0" border="0">
                    <tr>
                      <td style="background-color: #4F46E5; border-radius: 4px; padding: 0;">
                        <a href="${ctaUrl}" style="display: inline-block; padding: 16px 36px; color: #FFFFFF; text-decoration: none; font-weight: bold; font-size: 16px;">${ctaText}</a>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
            
            <h2 style="color: #111827; font-size: 20px; margin: 30px 0 20px 0;">What you can expect:</h2>
            
            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-bottom: 20px;">
              <tr valign="top">
                <td width="80" style="padding: 5px 0;">
                  <div style="background-color: #EEF2FF; border-radius: 50%; width: 60px; height: 60px; text-align: center; line-height: 60px; font-size: 24px; color: #4F46E5; font-weight: bold;">1</div>
                </td>
                <td style="padding: 5px 0;">
                  <p style="color: #4B5563; font-size: 16px; margin: 0; line-height: 1.6;">
                    <strong style="color: #111827;">Expert insights</strong><br>
                    Weekly updates with the latest trends and best practices.
                  </p>
                </td>
              </tr>
            </table>
            
            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-bottom: 20px;">
              <tr valign="top">
                <td width="80" style="padding: 5px 0;">
                  <div style="background-color: #EEF2FF; border-radius: 50%; width: 60px; height: 60px; text-align: center; line-height: 60px; font-size: 24px; color: #4F46E5; font-weight: bold;">2</div>
                </td>
                <td style="padding: 5px 0;">
                  <p style="color: #4B5563; font-size: 16px; margin: 0; line-height: 1.6;">
                    <strong style="color: #111827;">Special offers</strong><br>
                    Exclusive promotions and discounts for our subscribers.
                  </p>
                </td>
              </tr>
            </table>
            
            <table width="100%" cellpadding="0" cellspacing="0" border="0">
              <tr valign="top">
                <td width="80" style="padding: 5px 0;">
                  <div style="background-color: #EEF2FF; border-radius: 50%; width: 60px; height: 60px; text-align: center; line-height: 60px; font-size: 24px; color: #4F46E5; font-weight: bold;">3</div>
                </td>
                <td style="padding: 5px 0;">
                  <p style="color: #4B5563; font-size: 16px; margin: 0; line-height: 1.6;">
                    <strong style="color: #111827;">Community access</strong><br>
                    Join discussions with like-minded professionals.
                  </p>
                </td>
              </tr>
            </table>
            
            <p style="color: #666666; font-size: 16px; line-height: 1.4; margin: 30px 0 0 0;">
              We're excited to have you join us!<br><br>
              Warm regards,<br>
              The Team at {{YourCompanyName}}
            </p>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td style="background-color: #F3F4F6; padding: 20px; text-align: center;">
      <p style="color: #6B7280; font-size: 12px; margin: 0 0 10px 0;">
        © ${new Date().getFullYear()} {{YourCompanyName}}. All rights reserved.
      </p>
      <p style="color: #6B7280; font-size: 12px; margin: 0 0 10px 0;">
        <a href="{{privacyUrl}}" style="color: #6B7280; text-decoration: underline;">Privacy Policy</a> | 
        <a href="{{termsUrl}}" style="color: #6B7280; text-decoration: underline;">Terms of Service</a>
      </p>
      <p style="color: #6B7280; font-size: 12px; margin: 0;">
        <a href="{{unsubscribeUrl}}" style="color: #6B7280; text-decoration: underline;">Unsubscribe</a> | {{YourCompanyAddress}}
      </p>
    </td>
  </tr>
</table>
`;

// Newsletter template
const newsletterTemplate = (title: string, intro: string, articles: any[]) => `
<table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width: 600px; margin: 0 auto; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
  <tr>
    <td style="padding: 25px 0; text-align: center; background-color: #2563EB;">
      <h1 style="color: #FFFFFF; font-size: 28px; margin: 0; padding: 0 20px;">${title}</h1>
    </td>
  </tr>
  <tr>
    <td style="background-color: #FFFFFF; padding: 30px 20px;">
      <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
        Hi {{name}},
      </p>
      <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 30px 0;">
        ${intro}
      </p>
      
      ${articles.map(article => `
      <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin-bottom: 30px; border-bottom: 1px solid #E5E7EB; padding-bottom: 30px;">
        <tr>
          <td>
            <h2 style="color: #1F2937; font-size: 22px; margin: 0 0 15px 0;">${article.title}</h2>
            <p style="color: #4B5563; font-size: 16px; line-height: 1.6; margin: 0 0 15px 0;">${article.excerpt}</p>
            <p style="margin: 0;">
              <a href="${article.link}" style="display: inline-block; color: #2563EB; text-decoration: none; font-weight: 500;">Read more →</a>
            </p>
          </td>
        </tr>
      </table>
      `).join('')}
      
      <p style="color: #666666; font-size: 14px; line-height: 1.4; margin: 30px 0 0 0;">
        Thanks for reading,<br>
        The Team at {{YourCompanyName}}
      </p>
    </td>
  </tr>
  <tr>
    <td style="background-color: #F3F4F6; padding: 20px; text-align: center;">
      <p style="color: #6B7280; font-size: 12px; margin: 0 0 10px 0;">
        © ${new Date().getFullYear()} {{YourCompanyName}}. All rights reserved.
      </p>
      <p style="color: #6B7280; font-size: 12px; margin: 0;">
        <a href="{{unsubscribeUrl}}" style="color: #6B7280; text-decoration: underline;">Unsubscribe</a> | {{YourCompanyAddress}}
      </p>
    </td>
  </tr>
</table>
`;

// Promotional template
const promotionalTemplate = (title: string, promotionText: string, discountCode: string, ctaText: string, ctaUrl: string = '#', productImageUrl: string = '') => `
<table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width: 600px; margin: 0 auto; font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;">
  <tr>
    <td style="padding: 0;">
      <table width="100%" cellpadding="0" cellspacing="0" border="0">
        <tr>
          <td style="padding: 30px 0; text-align: center; background-color: #C026D3;">
            <h1 style="color: #FFFFFF; font-size: 32px; margin: 0; padding: 0 20px;">${title}</h1>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td style="background-color: #FFFFFF; padding: 0;">
      <table width="100%" cellpadding="0" cellspacing="0" border="0">
        ${productImageUrl ? `
        <tr>
          <td style="padding: 0;">
            <img src="${productImageUrl}" alt="Promotion" style="display: block; width: 100%; max-width: 600px; height: auto;" />
          </td>
        </tr>
        ` : ''}
        <tr>
          <td style="padding: 40px 30px;">
            <p style="color: #333333; font-size: 18px; line-height: 1.6; margin: 0 0 20px 0;">
              Hi {{name}},
            </p>
            <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
              ${promotionText}
            </p>
            
            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin: 30px 0; background-color: #FDF2F8; padding: 20px; border-radius: 6px;">
              <tr>
                <td align="center">
                  <p style="font-size: 14px; color: #9D174D; margin: 0 0 10px 0;">USE PROMO CODE</p>
                  <p style="font-size: 24px; font-weight: bold; color: #DB2777; margin: 0 0 15px 0; letter-spacing: 2px;">${discountCode}</p>
                  <p style="font-size: 14px; color: #9D174D; margin: 0;">Valid until {{expiryDate}}</p>
                </td>
              </tr>
            </table>
            
            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="margin: 30px 0;">
              <tr>
                <td align="center">
                  <table cellpadding="0" cellspacing="0" border="0">
                    <tr>
                      <td style="background-color: #C026D3; border-radius: 4px; padding: 0;">
                        <a href="${ctaUrl}" style="display: inline-block; padding: 16px 36px; color: #FFFFFF; text-decoration: none; font-weight: bold; font-size: 16px;">${ctaText}</a>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
            
            <p style="color: #666666; font-size: 14px; line-height: 1.4; margin: 30px 0 0 0;">
              Hurry, this offer won't last long!<br><br>
              Cheers,<br>
          The Team at {{YourCompanyName}}
            </p>
          </td>
        </tr>
      </table>
    </td>
  </tr>
  <tr>
    <td style="background-color: #F3F4F6; padding: 20px; text-align: center;">
      <p style="color: #6B7280; font-size: 12px; margin: 0 0 10px 0;">
        © ${new Date().getFullYear()} {{YourCompanyName}}. All rights reserved.
      </p>
      <p style="color: #6B7280; font-size: 12px; margin: 0 0 10px 0;">
        <a href="{{privacyUrl}}" style="color: #6B7280; text-decoration: underline;">Privacy Policy</a> | 
        <a href="{{termsUrl}}" style="color: #6B7280; text-decoration: underline;">Terms of Service</a>
      </p>
      <p style="color: #6B7280; font-size: 12px; margin: 0;">
        <a href="{{unsubscribeUrl}}" style="color: #6B7280; text-decoration: underline;">Unsubscribe</a> | {{YourCompanyAddress}}
      </p>
    </td>
  </tr>
</table>
`;

// --- START: Example Descriptions ---
const exampleDescriptions: { [key: string]: string[] } = {
  welcome: [
    "Welcome new subscribers and introduce your brand with a special offer.",
    "A warm welcome email to guide new users through getting started.",
    "Say hello and share your most popular resources with new members."
  ],
  promotion: [
    "Announce a limited-time sale with a clear call-to-action.",
    "Showcase a featured product with compelling visuals and a discount code.",
    "Holiday promotion template designed to drive seasonal sales.",
    "Flash sale announcement - create urgency and boost conversions.",
    "Promote your loyalty program and reward repeat customers.",
    "Bundle offer promotion template.",
    "Exclusive subscriber-only discount announcement.",
    "End-of-season clearance sale template."
  ],
  product: [
    "Introduce a new product launch with feature highlights.",
    "Product update announcement with details on new improvements.",
    "Showcase a specific product line or collection.",
    "Highlight the benefits of a best-selling product.",
    "Product comparison template."
  ],
  engagement: [
    "Share valuable blog content or news relevant to your audience.",
    "Re-engage inactive subscribers with a special offer or update.",
    "Company update or milestone announcement.",
    "Behind-the-scenes look at your brand or team.",
    "Share user-generated content or customer testimonials.",
    "Run a poll or quiz to increase interaction.",
    "Tips & tricks related to your product/service.",
    "Invite users to follow you on social media."
  ],
  transactional: [
    "Order confirmation with summary and shipping details.",
    "Shipping confirmation with tracking information.",
    "Password reset request confirmation.",
    "Account verification email.",
    "Subscription confirmation/renewal notice.",
    "Receipt or invoice template.",
    "Appointment confirmation/reminder.",
    "Download link delivery."
  ],
  feedback: [
    "Request feedback on a recent purchase or interaction.",
    "Net Promoter Score (NPS) survey template.",
    "General customer satisfaction survey."
  ],
  event: [
    "Invitation to an upcoming webinar or online event.",
    "Save the date announcement for a future event.",
    "Event reminder email with agenda and joining details.",
    "Post-event thank you and resource sharing.",
    "In-person event invitation."
  ],
  'local business': [
    "Promote a local event or in-store sale.",
    "Share updates specific to a local branch or location.",
    "Highlight community involvement or local partnerships.",
    "New location opening announcement.",
    "Local customer appreciation offer."
  ],
  other: [
    "General announcement template.",
    "Simple text-based email template.",
    "Basic branded email structure.",
    "Internal communication template.",
    "Miscellaneous update template."
  ]
};
// --- END: Example Descriptions ---

// Generate 50 Small Business Templates
const generateTemplates = async () => {
  const templates = [];
  const categories = [
    { name: 'Marketing', count: 3, titlePrefix: 'Welcome' },
    { name: 'Promotional', count: 8, titlePrefix: 'Sale' },
    { name: 'Newsletter', count: 4, titlePrefix: 'Newsletter' },
    { name: 'Other', count: 5, titlePrefix: 'Product' },
    { name: 'Marketing', count: 6, titlePrefix: 'Update' },
    { name: 'Transactional', count: 8, titlePrefix: 'Order' },
    { name: 'Other', count: 3, titlePrefix: 'Feedback' },
    { name: 'Event', count: 5, titlePrefix: 'Event' },
    { name: 'Marketing', count: 5, titlePrefix: 'Local' },
    { name: 'Other', count: 3, titlePrefix: 'Misc' },
  ];

  let totalGenerated = 0;

  // Sample newsletter articles
  const sampleArticles = [
    { title: "Industry Insights for Q3 2023", excerpt: "Learn about the latest trends shaping our industry in the third quarter of 2023.", link: "#article1" },
    { title: "Customer Success Story: How Company X Increased Revenue by 45%", excerpt: "Discover how our solution helped Company X transform their operations and boost revenue significantly.", link: "#article2" },
    { title: "New Feature Spotlight: AI-Powered Analytics", excerpt: "Our latest update brings powerful AI capabilities to your analytics dashboard. Here's what you need to know.", link: "#article3" }
  ];

  // Sample promotional content
  const promotions = [
    { 
      title: "Summer Sale: 30% Off Everything", 
      text: "Summer is here, and we're celebrating with our biggest sale of the season! For a limited time, enjoy 30% off our entire collection.",
      code: "SUMMER30",
      cta: "Shop Now"
    },
    { 
      title: "Flash Sale: 24 Hours Only", 
      text: "Quick! This deal won't last long. For the next 24 hours only, enjoy exclusive savings on our premium products.",
      code: "FLASH24",
      cta: "Claim Offer"
    },
    { 
      title: "Holiday Special Offers", 
      text: "Celebrate the holidays with special deals on our most popular items. The perfect time to treat yourself or find gifts for loved ones.",
      code: "HOLIDAY2023",
      cta: "Explore Deals"
    },
    { 
      title: "New Customer Discount", 
      text: "Welcome to our family! As a new customer, you're eligible for a special discount on your first purchase.",
      code: "WELCOME15",
      cta: "Start Shopping"
    }
  ];

  // Sample welcome messages
  const welcomeMessages = [
    { 
      title: "Welcome to Our Community", 
      text: "We're thrilled to have you join our community! Your membership gives you access to exclusive content, special offers, and more.",
      cta: "Explore Your Account"
    },
    { 
      title: "Thanks for Signing Up", 
      text: "Thank you for creating an account! We're excited to help you achieve your goals with our platform.",
      cta: "Get Started"
    },
    { 
      title: "Welcome Aboard", 
      text: "Welcome to the team! We're delighted to have you as a new subscriber. Your journey with us begins now.",
      cta: "Discover Features"
    }
  ];

  for (const cat of categories) {
    const descriptionsForCat = exampleDescriptions[cat.name] || exampleDescriptions['other'];
    for (let i = 1; i <= cat.count; i++) {
      if (totalGenerated >= 50) break;

      let content = '';
      let name = `${cat.titlePrefix} Example ${i}`;
      // Use the specific description list for the category, looping if needed
      let description = descriptionsForCat[(i - 1) % descriptionsForCat.length];

      // Assign content based on category
      if (cat.name === 'transactional') {
          name = `${cat.titlePrefix} Confirmation ${i}`;
        content = confirmationTemplate(
          name, 
          'Order #: {{orderNumber}}<br/>Item: {{itemName}}<br/>Total: {{orderTotal}}<br/>Shipping Address: {{shippingAddress}}'
        );
        
      } else if (cat.name === 'welcome') {
        const welcomeData = welcomeMessages[(i - 1) % welcomeMessages.length];
        name = welcomeData.title;
        content = welcomeTemplate(
          name,
          welcomeData.text,
          welcomeData.cta
        );
        
      } else if (cat.name === 'newsletter') {
        name = `${cat.titlePrefix} ${i}: Latest Updates`;
        content = newsletterTemplate(
          name,
          "Here's your curated roundup of our latest articles, news, and updates. We've selected content we think you'll find valuable and interesting.",
          sampleArticles
        );
        
      } else if (cat.name === 'promotion') {
        const promo = promotions[(i - 1) % promotions.length];
        name = promo.title;
        content = promotionalTemplate(
          name,
          promo.text,
          promo.code,
          promo.cta
        );
        
      } else if (cat.name === 'feedback') {
          name = `${cat.titlePrefix} Request ${i}`;
        content = basicBusinessTemplate(
          name, 
          'We value your opinion! Please take a moment to share your feedback on your recent experience with us. Your insights help us improve our services.',
          'Give Feedback'
        );
        
      } else if (cat.name === 'event') {
        name = `${cat.titlePrefix} Invitation: ${['Annual Conference', 'Webinar Series', 'Product Launch', 'Customer Appreciation Day', 'Industry Summit'][(i - 1) % 5]}`;
        content = basicBusinessTemplate(
          name,
          `We're excited to invite you to our upcoming ${name.split(':')[1].trim()}. Join us for this special occasion with expert speakers, networking opportunities, and valuable insights.`,
          'Register Now'
        );
        
      } else {
        // Default template for other categories
        content = basicBusinessTemplate(
          name,
          `This is the main content area for the ${name.toLowerCase()} template. Here you can include important information relevant to your audience.`,
          'Learn More'
        );
      } 

      templates.push({
        name: name,
        description: description,
        content: content,
        category: cat.name, // Now using the enum-valid category values
        thumbnail: `placeholder_${cat.name.replace(/\s+/g, '')}_${i}`,
      isSystem: true
      });
      totalGenerated++;
    }
    if (totalGenerated >= 50) break;
  }

  // Add more if needed to reach exactly 50
  const remaining = 50 - totalGenerated;
  if (remaining > 0) {
      const lastCat = categories[categories.length - 1];
    const descriptionsForLastCat = exampleDescriptions[lastCat.name.toLowerCase()] || exampleDescriptions['other'];
      for (let i = 1; i <= remaining; i++) {
          const name = `${lastCat.titlePrefix} Example ${lastCat.count + i}`;
          // Use the specific description list for the category, looping if needed
          const description = descriptionsForLastCat[(lastCat.count + i - 1) % descriptionsForLastCat.length];
    templates.push({
            name: name,
        description: description,
            content: basicBusinessTemplate(name, `This is an extra ${lastCat.name} template.`, 'Click Here'),
            category: lastCat.name,
            thumbnail: `placeholder_${lastCat.name.replace(/\s+/g, '')}_${lastCat.count + i}`,
      isSystem: true
    });
      }
  }

  return templates;
};

// Function to seed templates
export const seedTemplates = async () => {
  try {
    await dbService.connectMongo(); // Use connectMongo instead of connect
    console.log('Connected to MongoDB for template seeding');

    console.log('Clearing existing system templates...');
    // Ensure ONLY system templates are deleted before seeding
    await Template.deleteMany({ isSystem: true }); 

    console.log('Generating new small business template data...');
    const templatesToSeed = await generateTemplates();
    
    console.log(`Inserting ${templatesToSeed.length} new templates...`);
    if (templatesToSeed.length > 0) {
        await Template.insertMany(templatesToSeed);
    }
    
    console.log('Template seeding completed successfully.');
  } catch (error) {
    console.error('Error during template seeding:', error);
  } finally {
    await dbService.disconnect();
    console.log('Database connection closed after seeding.');
  }
};

// Run the seed function
seedTemplates();
