{"ast": null, "code": "\"use client\";\n\nimport { useFocusRing as N } from \"@react-aria/focus\";\nimport { useHover as V } from \"@react-aria/interactions\";\nimport i, { useCallback as w, useMemo as J, useState as $ } from \"react\";\nimport { useActivePress as j } from '../../hooks/use-active-press.js';\nimport { useControllable as q } from '../../hooks/use-controllable.js';\nimport { useDefaultValue as z } from '../../hooks/use-default-value.js';\nimport { useDisposables as Q } from '../../hooks/use-disposables.js';\nimport { useEvent as l } from '../../hooks/use-event.js';\nimport { useId as Y } from '../../hooks/use-id.js';\nimport { useDisabled as Z } from '../../internal/disabled.js';\nimport { FormFields as ee } from '../../internal/form-fields.js';\nimport { useProvidedId as oe } from '../../internal/id.js';\nimport { isDisabledReactIssue7711 as te } from '../../utils/bugs.js';\nimport { attemptSubmit as re } from '../../utils/form.js';\nimport { forwardRefWithAs as ae, mergeProps as ne, useRender as le } from '../../utils/render.js';\nimport { useDescribedBy as se } from '../description/description.js';\nimport { Keys as y } from '../keyboard.js';\nimport { useLabelledBy as ie } from '../label/label.js';\nlet de = \"span\";\nfunction pe(T, h) {\n  let C = Y(),\n    k = oe(),\n    x = Z(),\n    {\n      id: g = k || `headlessui-checkbox-${C}`,\n      disabled: e = x || !1,\n      autoFocus: s = !1,\n      checked: E,\n      defaultChecked: v,\n      onChange: P,\n      name: d,\n      value: D,\n      form: R,\n      indeterminate: n = !1,\n      tabIndex: A = 0,\n      ...F\n    } = T,\n    r = z(v),\n    [a, t] = q(E, P, r != null ? r : !1),\n    K = ie(),\n    _ = se(),\n    H = Q(),\n    [p, c] = $(!1),\n    u = l(() => {\n      c(!0), t == null || t(!a), H.nextFrame(() => {\n        c(!1);\n      });\n    }),\n    B = l(o => {\n      if (te(o.currentTarget)) return o.preventDefault();\n      o.preventDefault(), u();\n    }),\n    I = l(o => {\n      o.key === y.Space ? (o.preventDefault(), u()) : o.key === y.Enter && re(o.currentTarget);\n    }),\n    L = l(o => o.preventDefault()),\n    {\n      isFocusVisible: m,\n      focusProps: M\n    } = N({\n      autoFocus: s\n    }),\n    {\n      isHovered: b,\n      hoverProps: U\n    } = V({\n      isDisabled: e\n    }),\n    {\n      pressed: f,\n      pressProps: O\n    } = j({\n      disabled: e\n    }),\n    X = ne({\n      ref: h,\n      id: g,\n      role: \"checkbox\",\n      \"aria-checked\": n ? \"mixed\" : a ? \"true\" : \"false\",\n      \"aria-labelledby\": K,\n      \"aria-describedby\": _,\n      \"aria-disabled\": e ? !0 : void 0,\n      indeterminate: n ? \"true\" : void 0,\n      tabIndex: e ? void 0 : A,\n      onKeyUp: e ? void 0 : I,\n      onKeyPress: e ? void 0 : L,\n      onClick: e ? void 0 : B\n    }, M, U, O),\n    G = J(() => ({\n      checked: a,\n      disabled: e,\n      hover: b,\n      focus: m,\n      active: f,\n      indeterminate: n,\n      changing: p,\n      autofocus: s\n    }), [a, n, e, b, m, f, p, s]),\n    S = w(() => {\n      if (r !== void 0) return t == null ? void 0 : t(r);\n    }, [t, r]),\n    W = le();\n  return i.createElement(i.Fragment, null, d != null && i.createElement(ee, {\n    disabled: e,\n    data: {\n      [d]: D || \"on\"\n    },\n    overrides: {\n      type: \"checkbox\",\n      checked: a\n    },\n    form: R,\n    onReset: S\n  }), W({\n    ourProps: X,\n    theirProps: F,\n    slot: G,\n    defaultTag: de,\n    name: \"Checkbox\"\n  }));\n}\nlet Fe = ae(pe);\nexport { Fe as Checkbox };", "map": {"version": 3, "names": ["useFocusRing", "N", "useHover", "V", "i", "useCallback", "w", "useMemo", "J", "useState", "$", "useActivePress", "j", "useControllable", "q", "useDefaultValue", "z", "useDisposables", "Q", "useEvent", "l", "useId", "Y", "useDisabled", "Z", "<PERSON><PERSON><PERSON>s", "ee", "useProvidedId", "oe", "isDisabledReactIssue7711", "te", "attemptSubmit", "re", "forwardRefWithAs", "ae", "mergeProps", "ne", "useRender", "le", "useDescribedBy", "se", "Keys", "y", "useLabelledBy", "ie", "de", "pe", "T", "h", "C", "k", "x", "id", "g", "disabled", "e", "autoFocus", "s", "checked", "E", "defaultChecked", "v", "onChange", "P", "name", "d", "value", "D", "form", "R", "indeterminate", "n", "tabIndex", "A", "F", "r", "a", "t", "K", "_", "H", "p", "c", "u", "next<PERSON><PERSON><PERSON>", "B", "o", "currentTarget", "preventDefault", "I", "key", "Space", "Enter", "L", "isFocusVisible", "m", "focusProps", "M", "isHovered", "b", "hoverProps", "U", "isDisabled", "pressed", "f", "pressProps", "O", "X", "ref", "role", "onKeyUp", "onKeyPress", "onClick", "G", "hover", "focus", "active", "changing", "autofocus", "S", "W", "createElement", "Fragment", "data", "overrides", "type", "onReset", "ourProps", "theirProps", "slot", "defaultTag", "Fe", "Checkbox"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/node_modules/@headlessui/react/dist/components/checkbox/checkbox.js"], "sourcesContent": ["\"use client\";import{useFocusRing as N}from\"@react-aria/focus\";import{useHover as V}from\"@react-aria/interactions\";import i,{useCallback as w,useMemo as J,useState as $}from\"react\";import{useActivePress as j}from'../../hooks/use-active-press.js';import{useControllable as q}from'../../hooks/use-controllable.js';import{useDefaultValue as z}from'../../hooks/use-default-value.js';import{useDisposables as Q}from'../../hooks/use-disposables.js';import{useEvent as l}from'../../hooks/use-event.js';import{useId as Y}from'../../hooks/use-id.js';import{useDisabled as Z}from'../../internal/disabled.js';import{FormFields as ee}from'../../internal/form-fields.js';import{useProvidedId as oe}from'../../internal/id.js';import{isDisabledReactIssue7711 as te}from'../../utils/bugs.js';import{attemptSubmit as re}from'../../utils/form.js';import{forwardRefWithAs as ae,mergeProps as ne,useRender as le}from'../../utils/render.js';import{useDescribedBy as se}from'../description/description.js';import{Keys as y}from'../keyboard.js';import{useLabelledBy as ie}from'../label/label.js';let de=\"span\";function pe(T,h){let C=Y(),k=oe(),x=Z(),{id:g=k||`headlessui-checkbox-${C}`,disabled:e=x||!1,autoFocus:s=!1,checked:E,defaultChecked:v,onChange:P,name:d,value:D,form:R,indeterminate:n=!1,tabIndex:A=0,...F}=T,r=z(v),[a,t]=q(E,P,r!=null?r:!1),K=ie(),_=se(),H=Q(),[p,c]=$(!1),u=l(()=>{c(!0),t==null||t(!a),H.nextFrame(()=>{c(!1)})}),B=l(o=>{if(te(o.currentTarget))return o.preventDefault();o.preventDefault(),u()}),I=l(o=>{o.key===y.Space?(o.preventDefault(),u()):o.key===y.Enter&&re(o.currentTarget)}),L=l(o=>o.preventDefault()),{isFocusVisible:m,focusProps:M}=N({autoFocus:s}),{isHovered:b,hoverProps:U}=V({isDisabled:e}),{pressed:f,pressProps:O}=j({disabled:e}),X=ne({ref:h,id:g,role:\"checkbox\",\"aria-checked\":n?\"mixed\":a?\"true\":\"false\",\"aria-labelledby\":K,\"aria-describedby\":_,\"aria-disabled\":e?!0:void 0,indeterminate:n?\"true\":void 0,tabIndex:e?void 0:A,onKeyUp:e?void 0:I,onKeyPress:e?void 0:L,onClick:e?void 0:B},M,U,O),G=J(()=>({checked:a,disabled:e,hover:b,focus:m,active:f,indeterminate:n,changing:p,autofocus:s}),[a,n,e,b,m,f,p,s]),S=w(()=>{if(r!==void 0)return t==null?void 0:t(r)},[t,r]),W=le();return i.createElement(i.Fragment,null,d!=null&&i.createElement(ee,{disabled:e,data:{[d]:D||\"on\"},overrides:{type:\"checkbox\",checked:a},form:R,onReset:S}),W({ourProps:X,theirProps:F,slot:G,defaultTag:de,name:\"Checkbox\"}))}let Fe=ae(pe);export{Fe as Checkbox};\n"], "mappings": "AAAA,YAAY;;AAAC,SAAOA,YAAY,IAAIC,CAAC,QAAK,mBAAmB;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,OAAOC,CAAC,IAAEC,WAAW,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,kCAAkC;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,UAAU,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAOC,wBAAwB,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,EAACC,UAAU,IAAIC,EAAE,EAACC,SAAS,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,mBAAmB;AAAC,IAAIC,EAAE,GAAC,MAAM;AAAC,SAASC,EAAEA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC3B,CAAC,CAAC,CAAC;IAAC4B,CAAC,GAACtB,EAAE,CAAC,CAAC;IAACuB,CAAC,GAAC3B,CAAC,CAAC,CAAC;IAAC;MAAC4B,EAAE,EAACC,CAAC,GAACH,CAAC,IAAE,uBAAuBD,CAAC,EAAE;MAACK,QAAQ,EAACC,CAAC,GAACJ,CAAC,IAAE,CAAC,CAAC;MAACK,SAAS,EAACC,CAAC,GAAC,CAAC,CAAC;MAACC,OAAO,EAACC,CAAC;MAACC,cAAc,EAACC,CAAC;MAACC,QAAQ,EAACC,CAAC;MAACC,IAAI,EAACC,CAAC;MAACC,KAAK,EAACC,CAAC;MAACC,IAAI,EAACC,CAAC;MAACC,aAAa,EAACC,CAAC,GAAC,CAAC,CAAC;MAACC,QAAQ,EAACC,CAAC,GAAC,CAAC;MAAC,GAAGC;IAAC,CAAC,GAAC3B,CAAC;IAAC4B,CAAC,GAAC3D,CAAC,CAAC6C,CAAC,CAAC;IAAC,CAACe,CAAC,EAACC,CAAC,CAAC,GAAC/D,CAAC,CAAC6C,CAAC,EAACI,CAAC,EAACY,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC,CAAC,CAAC;IAACG,CAAC,GAAClC,EAAE,CAAC,CAAC;IAACmC,CAAC,GAACvC,EAAE,CAAC,CAAC;IAACwC,CAAC,GAAC9D,CAAC,CAAC,CAAC;IAAC,CAAC+D,CAAC,EAACC,CAAC,CAAC,GAACxE,CAAC,CAAC,CAAC,CAAC,CAAC;IAACyE,CAAC,GAAC/D,CAAC,CAAC,MAAI;MAAC8D,CAAC,CAAC,CAAC,CAAC,CAAC,EAACL,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC,CAACD,CAAC,CAAC,EAACI,CAAC,CAACI,SAAS,CAAC,MAAI;QAACF,CAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAACG,CAAC,GAACjE,CAAC,CAACkE,CAAC,IAAE;MAAC,IAAGxD,EAAE,CAACwD,CAAC,CAACC,aAAa,CAAC,EAAC,OAAOD,CAAC,CAACE,cAAc,CAAC,CAAC;MAACF,CAAC,CAACE,cAAc,CAAC,CAAC,EAACL,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACM,CAAC,GAACrE,CAAC,CAACkE,CAAC,IAAE;MAACA,CAAC,CAACI,GAAG,KAAGhD,CAAC,CAACiD,KAAK,IAAEL,CAAC,CAACE,cAAc,CAAC,CAAC,EAACL,CAAC,CAAC,CAAC,IAAEG,CAAC,CAACI,GAAG,KAAGhD,CAAC,CAACkD,KAAK,IAAE5D,EAAE,CAACsD,CAAC,CAACC,aAAa,CAAC;IAAA,CAAC,CAAC;IAACM,CAAC,GAACzE,CAAC,CAACkE,CAAC,IAAEA,CAAC,CAACE,cAAc,CAAC,CAAC,CAAC;IAAC;MAACM,cAAc,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAChG,CAAC,CAAC;MAACuD,SAAS,EAACC;IAAC,CAAC,CAAC;IAAC;MAACyC,SAAS,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAClG,CAAC,CAAC;MAACmG,UAAU,EAAC/C;IAAC,CAAC,CAAC;IAAC;MAACgD,OAAO,EAACC,CAAC;MAACC,UAAU,EAACC;IAAC,CAAC,GAAC9F,CAAC,CAAC;MAAC0C,QAAQ,EAACC;IAAC,CAAC,CAAC;IAACoD,CAAC,GAACvE,EAAE,CAAC;MAACwE,GAAG,EAAC5D,CAAC;MAACI,EAAE,EAACC,CAAC;MAACwD,IAAI,EAAC,UAAU;MAAC,cAAc,EAACtC,CAAC,GAAC,OAAO,GAACK,CAAC,GAAC,MAAM,GAAC,OAAO;MAAC,iBAAiB,EAACE,CAAC;MAAC,kBAAkB,EAACC,CAAC;MAAC,eAAe,EAACxB,CAAC,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;MAACe,aAAa,EAACC,CAAC,GAAC,MAAM,GAAC,KAAK,CAAC;MAACC,QAAQ,EAACjB,CAAC,GAAC,KAAK,CAAC,GAACkB,CAAC;MAACqC,OAAO,EAACvD,CAAC,GAAC,KAAK,CAAC,GAACkC,CAAC;MAACsB,UAAU,EAACxD,CAAC,GAAC,KAAK,CAAC,GAACsC,CAAC;MAACmB,OAAO,EAACzD,CAAC,GAAC,KAAK,CAAC,GAAC8B;IAAC,CAAC,EAACY,CAAC,EAACI,CAAC,EAACK,CAAC,CAAC;IAACO,CAAC,GAACzG,CAAC,CAAC,OAAK;MAACkD,OAAO,EAACkB,CAAC;MAACtB,QAAQ,EAACC,CAAC;MAAC2D,KAAK,EAACf,CAAC;MAACgB,KAAK,EAACpB,CAAC;MAACqB,MAAM,EAACZ,CAAC;MAAClC,aAAa,EAACC,CAAC;MAAC8C,QAAQ,EAACpC,CAAC;MAACqC,SAAS,EAAC7D;IAAC,CAAC,CAAC,EAAC,CAACmB,CAAC,EAACL,CAAC,EAAChB,CAAC,EAAC4C,CAAC,EAACJ,CAAC,EAACS,CAAC,EAACvB,CAAC,EAACxB,CAAC,CAAC,CAAC;IAAC8D,CAAC,GAACjH,CAAC,CAAC,MAAI;MAAC,IAAGqE,CAAC,KAAG,KAAK,CAAC,EAAC,OAAOE,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACE,CAAC,EAACF,CAAC,CAAC,CAAC;IAAC6C,CAAC,GAAClF,EAAE,CAAC,CAAC;EAAC,OAAOlC,CAAC,CAACqH,aAAa,CAACrH,CAAC,CAACsH,QAAQ,EAAC,IAAI,EAACzD,CAAC,IAAE,IAAI,IAAE7D,CAAC,CAACqH,aAAa,CAAC/F,EAAE,EAAC;IAAC4B,QAAQ,EAACC,CAAC;IAACoE,IAAI,EAAC;MAAC,CAAC1D,CAAC,GAAEE,CAAC,IAAE;IAAI,CAAC;IAACyD,SAAS,EAAC;MAACC,IAAI,EAAC,UAAU;MAACnE,OAAO,EAACkB;IAAC,CAAC;IAACR,IAAI,EAACC,CAAC;IAACyD,OAAO,EAACP;EAAC,CAAC,CAAC,EAACC,CAAC,CAAC;IAACO,QAAQ,EAACpB,CAAC;IAACqB,UAAU,EAACtD,CAAC;IAACuD,IAAI,EAAChB,CAAC;IAACiB,UAAU,EAACrF,EAAE;IAACmB,IAAI,EAAC;EAAU,CAAC,CAAC,CAAC;AAAA;AAAC,IAAImE,EAAE,GAACjG,EAAE,CAACY,EAAE,CAAC;AAAC,SAAOqF,EAAE,IAAIC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}