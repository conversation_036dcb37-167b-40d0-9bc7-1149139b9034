{"version": 3, "file": "static/js/368.6bc6241f.chunk.js", "mappings": "0MA0BA,MAkWA,EAlWwCA,KACtC,MAAOC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAqB,KAChDC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAqB,KAChDG,EAASC,IAAcJ,EAAAA,EAAAA,WAAkB,IACzCK,EAAOC,IAAYN,EAAAA,EAAAA,UAAwB,OAE3CO,EAAaC,IAAkBR,EAAAA,EAAAA,UAAS,CAAES,KAAM,GAAIC,aAAc,MAClEC,EAAaC,IAAkBZ,EAAAA,EAAAA,UAAS,CAAES,KAAM,GAAII,QAAS,GAAIC,kBAAmB,MACpFC,EAAWC,IAAgBhB,EAAAA,EAAAA,UAAoC,cAC/DiB,EAAgBC,IAAqBlB,EAAAA,EAAAA,UAAiB,KACtDmB,EAAeC,IAAoBpB,EAAAA,EAAAA,UAAiC,CAAC,IACrEqB,EAAkBC,IAAuBtB,EAAAA,EAAAA,UAA0B,OAE1EuB,EAAAA,EAAAA,YAAU,KACUC,WAChBpB,GAAW,GACX,IACE,MAAOqB,EAAmBC,SAA2BC,QAAQC,IAAI,CAC/DC,EAAAA,GAAuBC,eACvBD,EAAAA,GAAuBE,iBAGrBN,EAAkBO,QACpBjC,EAAakC,MAAMC,QAAQT,EAAkBU,MAAQV,EAAkBU,KAAO,IAE9E7B,EAASmB,EAAkBW,SAAW,6BAGpCV,EAAkBM,QACpB9B,EAAawB,EAAkBS,MAE/B7B,GAAS+B,GAAaA,EAAY,GAAGA,MAAcX,EAAkBU,UAAYV,EAAkBU,SAAW,6BAElH,CAAE,MAAOE,GACPhC,EAASgC,EAAIF,SAAW,uCAC1B,CAAC,QACChC,GAAW,EACb,GAGFmC,EAAW,GACV,IAqGH,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SAAC,8BAE3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BAA0BC,SAAC,+DAIvCvC,GACCwC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACvCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gFAGjBD,EAAAA,EAAAA,MAAAI,EAAAA,SAAA,CAAAF,SAAA,CACGrC,IACCsC,EAAAA,EAAAA,KAACE,EAAAA,EAAK,CAACC,KAAK,QAAQV,QAAS/B,EAAO0C,QAASA,IAAMzC,EAAS,MAAOmC,UAAU,UAG/ED,EAAAA,EAAAA,MAACQ,EAAAA,EAAI,CAACP,UAAU,kBAAiBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEF,UAAW,+DACK,cAAd1B,EACI,8BACA,wFAENkC,QAASA,IAAMjC,EAAa,aAAa0B,SAC1C,eAGDC,EAAAA,EAAAA,KAAA,UACEF,UAAW,+DACK,cAAd1B,EACI,8BACA,wFAENkC,QAASA,IAAMjC,EAAa,aAAa0B,SAC1C,oBAMLC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACH,cAAd3B,GACCyB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,sBAE7DF,EAAAA,EAAAA,MAACQ,EAAAA,EAAI,CAACP,UAAU,0CAAyCC,SAAA,EACvDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,yBAC3DF,EAAAA,EAAAA,MAAA,QAAMU,SAvJK1B,UAI3B,GAHA2B,EAAEC,iBACF9C,EAAS,MAEJC,EAAYE,KAAK4C,OAKtB,IACE,MAAMC,QAAiBzB,EAAAA,GAAuB0B,eAC5ChD,EAAYE,KACZF,EAAYG,cAGV4C,EAAStB,SACXjC,EAAa,IAAID,EAAWwD,EAASnB,OACrC3B,EAAe,CAAEC,KAAM,GAAIC,aAAc,MAEzCJ,EAASgD,EAASlB,SAAW,4BAEjC,CAAE,MAAOE,GACPhC,EAASgC,EAAIF,SAAW,4CAC1B,MAlBE9B,EAAS,4BAkBX,EAgIsDmC,UAAU,YAAWC,SAAA,EACzDC,EAAAA,EAAAA,KAACa,EAAAA,EAAK,CACJC,MAAM,gBACNC,GAAG,eACHjD,KAAK,eACLkD,MAAOpD,EAAYE,KACnBmD,SAAWT,GAAqC3C,EAAe,IAAKD,EAAaE,KAAM0C,EAAEU,OAAOF,QAChGG,YAAY,+BACZC,UAAQ,KAEVpB,EAAAA,EAAAA,KAACa,EAAAA,EAAK,CACJC,MAAM,2BACNC,GAAG,eACHjD,KAAK,eACLkD,MAAOpD,EAAYG,aACnBkD,SAAWT,GAAqC3C,EAAe,IAAKD,EAAaG,aAAcyC,EAAEU,OAAOF,QACxGG,YAAY,+BAEdnB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC9BC,EAAAA,EAAAA,KAACqB,EAAAA,EAAM,CAAClB,KAAK,SAAQJ,SAAC,6BAK7BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,uBACrC,IAArB5C,EAAUmE,QACTtB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBAAqBC,SAAC,+BAEnCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,YAAWC,SACtB5C,EAAUoE,KAAKC,IACd3B,EAAAA,EAAAA,MAAA,MAAsBC,UAAU,mEAAkEC,SAAA,EAChGC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,8BAA6BC,SAAE,KAAKyB,EAAS1D,YAC7D+B,EAAAA,EAAAA,MAAA,QAAMC,UAAU,8BAA6BC,SAAA,CAAC,aAAWyB,EAASzD,cAAgB,GAAG,SAF9EyD,EAAST,YAU1BlB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAC,sBAE7DF,EAAAA,EAAAA,MAACQ,EAAAA,EAAI,CAACP,UAAU,0CAAyCC,SAAA,EACtDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,yBAC5DF,EAAAA,EAAAA,MAAA,QAAMU,SA1KK1B,UAI3B,GAHA2B,EAAEC,iBACF9C,EAAS,MAEJK,EAAYF,KAAK4C,QAAW1C,EAAYE,QAAQwC,OAKrD,IACE,MAAMC,QAAiBzB,EAAAA,GAAuBuC,eAC5CzD,EAAYF,KACZE,EAAYE,QACZF,EAAYG,mBAGVwC,EAAStB,SACX9B,EAAa,IAAID,EAAWqD,EAASnB,OACrCvB,EAAe,CAAEH,KAAM,GAAII,QAAS,GAAIC,kBAAmB,MAE3DR,EAASgD,EAASlB,SAAW,4BAEjC,CAAE,MAAOE,GACPhC,EAASgC,EAAIF,SAAW,4CAC1B,MAnBE9B,EAAS,yCAmBX,EAkJsDmC,UAAU,YAAWC,SAAA,EACxDC,EAAAA,EAAAA,KAACa,EAAAA,EAAK,CACHC,MAAM,gBACNC,GAAG,eACHjD,KAAK,eACLkD,MAAOhD,EAAYF,KACnBmD,SAAWT,GAAqCvC,EAAe,IAAKD,EAAaF,KAAM0C,EAAEU,OAAOF,QAChGI,UAAQ,KAEVvB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACIF,EAAAA,EAAAA,MAAA,SAAO6B,QAAQ,kBAAkB5B,UAAU,qDAAoDC,SAAA,CAAC,yBACrE,mBAAmB,sBAE9CC,EAAAA,EAAAA,KAAA,YACIe,GAAG,kBACHjD,KAAK,kBACL6D,KAAM,EACNX,MAAOhD,EAAYE,QACnB+C,SAAWT,GAAwCvC,EAAe,IAAKD,EAAaE,QAASsC,EAAEU,OAAOF,QACtGI,UAAQ,EACRtB,UAAU,wMAIlBD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,qDAAoDC,SAAC,8BACtEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SACjC5C,EAAUoE,KAAIC,IACb3B,EAAAA,EAAAA,MAAA,SAAyBC,UAAU,4FAA2FC,SAAA,EAC3HC,EAAAA,EAAAA,KAAA,SACGG,KAAK,WACLL,UAAU,gGACV8B,QAAS5D,EAAYG,kBAAkB0D,SAASL,EAAST,IACzDE,SAAUA,IAhLba,KAC/B,MAAMC,EAAa/D,EAAYG,kBAAkB0D,SAASC,GAE1D7D,GAAe+D,IAAI,IACdA,EACH7D,kBAAmB4D,EACfC,EAAK7D,kBAAkB8D,QAAOlB,GAAMA,IAAOe,IAC3C,IAAIE,EAAK7D,kBAAmB2D,MAC/B,EAwK+CI,CAAwBV,EAAST,OAEnDf,EAAAA,EAAAA,KAAA,QAAMF,UAAU,8BAA6BC,SAAE,KAAKyB,EAAS1D,aAPrD0D,EAAST,YAa7Bf,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC7BC,EAAAA,EAAAA,KAACqB,EAAAA,EAAM,CAAClB,KAAK,SAAQJ,SAAC,6BAK9BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,uBACpC,IAArBzC,EAAUgE,QACTtB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBAAqBC,SAAC,+BAEnCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,YAAWC,SACtBzC,EAAUiE,KAAKY,IACdnC,EAAAA,EAAAA,KAAA,MAAsBF,UAAU,iCAAgCC,UAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,kCAAiCC,SAAEoC,EAASrE,QAC5DkC,EAAAA,EAAAA,KAACqB,EAAAA,EAAM,CAACe,KAAK,KAAKC,QAAQ,YAAY/B,QAASA,IA5L3C6B,KAC/BxD,EAAoBwD,GACpB5D,EAAkB4D,EAASjE,SAE3B,MAAMoE,EAAwC,CAAC,EAC/CH,EAAShF,UAAUoF,SAASC,IAC1B,MAAMhB,EAAWrE,EAAUsF,MAAKC,GAAKA,EAAE3B,KAAOyB,IAC1ChB,IACFc,EAAcd,EAAS1D,MAAQ0D,EAASzD,cAAgB,GAC1D,IAGFU,EAAiB6D,EAAc,EAgLiDK,CAAwBR,GAAUpC,SAAC,gBAHnFoC,EAASpB,gBAelCrC,IACCsB,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAACP,UAAU,OAAMC,UACpBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,+CAA8CC,SAAA,CAAC,YAAUrB,EAAiBZ,SACvF+B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EAEvDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,qCAC3DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACzBrB,EAAiBvB,UAAUoE,KAAKiB,IAC7B,MAAMhB,EAAWrE,EAAUsF,MAAKC,GAAKA,EAAE3B,KAAOyB,IAC9C,OAAKhB,GAEDxB,EAAAA,EAAAA,KAACa,EAAAA,EAAK,CAEFE,GAAI,WAAWS,EAAST,KACxBjD,KAAM0D,EAAS1D,KACfgD,MAAOU,EAAS1D,KAChBkD,MAAOxC,EAAcgD,EAAS1D,OAAS,GACvCmD,SAAWT,IAAqCoC,OA5MhDC,EA4MyErB,EAAS1D,KA5M5DkD,EA4MkER,EAAEU,OAAOF,WA3MjIvC,EAAiB,IACZD,EACH,CAACqE,GAAe7B,IAHa4B,IAACC,EAAsB7B,CA4MkF,GALnGQ,EAAST,IAHA,IAShB,OAIVf,EAAAA,EAAAA,KAACqB,EAAAA,EAAM,CAACf,QA1MNwC,KACtB,IAAKpE,EAAkB,OAEvB,IAAIqE,EAAUrE,EAAiBR,QAE/B8E,OAAOC,QAAQzE,GAAe+D,SAAQW,IAAoB,IAAlBpF,EAAMkD,GAAMkC,EAClD,MAAMC,EAAQ,IAAIC,OAAO,KAAOtF,MAAY,KAC5CiF,EAAUA,EAAQM,QAAQF,EAAOnC,GAAS,GAAG,IAG/CzC,EAAkBwE,EAAQ,EAgM4BjD,UAAU,OAAMC,SAAC,uBAGtDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6CAA4CC,SAAC,kBAC1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,SACxGzB,GACG0B,EAAAA,EAAAA,KAAA,OAAKsD,wBAAyB,CAAEC,OAAQjF,EAAe+E,QAAQ,MAAO,cAEtErD,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6BAA4BC,SAAC,yDAUpE,C", "sources": ["pages/PersonalizationEditor.tsx"], "sourcesContent": ["import React, {\n  ChangeEvent,\n  FormEvent,\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from '../components/Alert';\nimport Button from '../components/Button';\nimport Card from '../components/Card';\nimport Input from '../components/Input';\nimport { personalizationService } from '../services';\n\ninterface Variable {\n  id: string;\n  name: string;\n  defaultValue: string;\n}\n\ninterface Template {\n  id: string;\n  name: string;\n  content: string;\n  variables: string[];\n}\n\nconst PersonalizationEditor: React.FC = () => {\n  const [variables, setVariables] = useState<Variable[]>([]);\n  const [templates, setTemplates] = useState<Template[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  \n  const [newVariable, setNewVariable] = useState({ name: '', defaultValue: '' });\n  const [newTemplate, setNewTemplate] = useState({ name: '', content: '', selectedVariables: [] as string[] });\n  const [activeTab, setActiveTab] = useState<'variables' | 'templates'>('variables');\n  const [previewContent, setPreviewContent] = useState<string>('');\n  const [previewValues, setPreviewValues] = useState<Record<string, string>>({});\n  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      setLoading(true);\n      try {\n        const [variablesResponse, templatesResponse] = await Promise.all([\n          personalizationService.getVariables(),\n          personalizationService.getTemplates()\n        ]);\n\n        if (variablesResponse.success) {\n          setVariables(Array.isArray(variablesResponse.data) ? variablesResponse.data : []);\n        } else {\n          setError(variablesResponse.message || 'Failed to fetch variables');\n        }\n\n        if (templatesResponse.success) {\n          setTemplates(templatesResponse.data);\n        } else {\n          setError(prevError => prevError ? `${prevError}; ${templatesResponse.message}` : templatesResponse.message || 'Failed to fetch templates');\n        }\n      } catch (err: any) {\n        setError(err.message || 'Failed to fetch personalization data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    fetchData();\n  }, []);\n  \n  const handleCreateVariable = async (e: FormEvent) => {\n    e.preventDefault();\n    setError(null);\n\n    if (!newVariable.name.trim()) {\n      setError('Variable name is required');\n      return;\n    }\n    \n    try {\n      const response = await personalizationService.createVariable(\n        newVariable.name,\n        newVariable.defaultValue\n      );\n      \n      if (response.success) {\n        setVariables([...variables, response.data]);\n        setNewVariable({ name: '', defaultValue: '' });\n      } else {\n        setError(response.message || 'Failed to create variable');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating variable');\n    }\n  };\n  \n  const handleCreateTemplate = async (e: FormEvent) => {\n    e.preventDefault();\n    setError(null);\n\n    if (!newTemplate.name.trim() || !newTemplate.content.trim()) {\n      setError('Template name and content are required');\n      return;\n    }\n    \n    try {\n      const response = await personalizationService.createTemplate(\n        newTemplate.name,\n        newTemplate.content,\n        newTemplate.selectedVariables\n      );\n      \n      if (response.success) {\n        setTemplates([...templates, response.data]);\n        setNewTemplate({ name: '', content: '', selectedVariables: [] });\n      } else {\n        setError(response.message || 'Failed to create template');\n      }\n    } catch (err: any) {\n      setError(err.message || 'An error occurred while creating template');\n    }\n  };\n  \n  const handleVariableSelection = (variableId: string) => {\n    const isSelected = newTemplate.selectedVariables.includes(variableId);\n    \n    setNewTemplate(prev => ({\n      ...prev,\n      selectedVariables: isSelected\n        ? prev.selectedVariables.filter(id => id !== variableId)\n        : [...prev.selectedVariables, variableId]\n    }));\n  };\n  \n  const handleTemplateSelection = (template: Template) => {\n    setSelectedTemplate(template);\n    setPreviewContent(template.content);\n    \n    const initialValues: Record<string, string> = {};\n    template.variables.forEach((varId: string) => {\n      const variable = variables.find(v => v.id === varId);\n      if (variable) {\n        initialValues[variable.name] = variable.defaultValue || '';\n      }\n    });\n    \n    setPreviewValues(initialValues);\n  };\n  \n  const handlePreviewValueChange = (variableName: string, value: string) => {\n    setPreviewValues({\n      ...previewValues,\n      [variableName]: value\n    });\n  };\n  \n  const generatePreview = () => {\n    if (!selectedTemplate) return;\n    \n    let preview = selectedTemplate.content;\n    \n    Object.entries(previewValues).forEach(([name, value]) => {\n      const regex = new RegExp(`\\{\\{${name}\\}\\}`, 'g');\n      preview = preview.replace(regex, value || '');\n    });\n    \n    setPreviewContent(preview);\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-6\">\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-semibold text-text-primary\">Personalization Editor</h1>\n      </div>\n      <p className=\"text-text-secondary mb-6\">\n        Create and manage personalization variables and templates.\n      </p>\n      \n      {loading ? (\n        <div className=\"flex justify-center py-8\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n        </div>\n      ) : (\n        <>\n          {error && (\n            <Alert type=\"error\" message={error} onClose={() => setError(null)} className=\"mb-4\" />\n          )}\n          \n          <Card className=\"overflow-hidden\">\n            <div className=\"border-b border-gray-700\">\n              <nav className=\"-mb-px flex space-x-4 px-6\">\n                <button\n                  className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === 'variables'\n                      ? 'border-primary text-primary'\n                      : 'border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500'\n                  }`}\n                  onClick={() => setActiveTab('variables')}\n                >\n                  Variables\n                </button>\n                <button\n                  className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === 'templates'\n                      ? 'border-primary text-primary'\n                      : 'border-transparent text-text-secondary hover:text-text-primary hover:border-gray-500'\n                  }`}\n                  onClick={() => setActiveTab('templates')}\n                >\n                  Templates\n                </button>\n              </nav>\n            </div>\n            \n            <div className=\"p-6\">\n              {activeTab === 'variables' ? (\n                <div>\n                  <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Manage Variables</h2>\n                  \n                  <Card className=\"mb-6 p-4 border border-gray-700 rounded\">\n                    <h3 className=\"text-lg font-medium text-text-primary mb-3\">Create New Variable</h3>\n                    <form onSubmit={handleCreateVariable} className=\"space-y-4\">\n                      <Input\n                        label=\"Variable Name\"\n                        id=\"variableName\"\n                        name=\"variableName\"\n                        value={newVariable.name}\n                        onChange={(e: ChangeEvent<HTMLInputElement>) => setNewVariable({ ...newVariable, name: e.target.value })}\n                        placeholder=\"e.g., firstName, companyName\"\n                        required\n                      />\n                      <Input\n                        label=\"Default Value (Optional)\"\n                        id=\"defaultValue\"\n                        name=\"defaultValue\"\n                        value={newVariable.defaultValue}\n                        onChange={(e: ChangeEvent<HTMLInputElement>) => setNewVariable({ ...newVariable, defaultValue: e.target.value })}\n                        placeholder=\"e.g., there, your company\"\n                      />\n                      <div className=\"flex justify-end\">\n                         <Button type=\"submit\">Create Variable</Button>\n                      </div>\n                    </form>\n                   </Card>\n                   \n                  <h3 className=\"text-lg font-medium text-text-primary mb-3\">Existing Variables</h3>\n                  {variables.length === 0 ? (\n                    <p className=\"text-text-secondary\">No variables created yet.</p>\n                  ) : (\n                    <ul className=\"space-y-2\">\n                      {variables.map((variable) => (\n                        <li key={variable.id} className=\"bg-secondary-bg p-3 rounded-md flex justify-between items-center\">\n                          <span className=\"text-text-primary font-mono\">{`{{${variable.name}}}`}</span>\n                          <span className=\"text-sm text-text-secondary\">Default: \"{variable.defaultValue || ''}\"</span>\n                           {/* Add delete button later */}\n                        </li>\n                      ))}\n                    </ul>\n                  )}\n                </div>\n              ) : (\n                <div>\n                  <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Manage Templates</h2>\n                  \n                  <Card className=\"mb-6 p-4 border border-gray-700 rounded\">\n                     <h3 className=\"text-lg font-medium text-text-primary mb-3\">Create New Template</h3>\n                    <form onSubmit={handleCreateTemplate} className=\"space-y-4\">\n                       <Input\n                          label=\"Template Name\"\n                          id=\"templateName\"\n                          name=\"templateName\"\n                          value={newTemplate.name}\n                          onChange={(e: ChangeEvent<HTMLInputElement>) => setNewTemplate({ ...newTemplate, name: e.target.value })}\n                          required\n                        />\n                        <div>\n                            <label htmlFor=\"templateContent\" className=\"block text-sm font-medium text-text-secondary mb-1\">\n                                Template Content (use {'{{variableName}}'} for variables)\n                            </label>\n                            <textarea\n                                id=\"templateContent\"\n                                name=\"templateContent\"\n                                rows={6}\n                                value={newTemplate.content}\n                                onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setNewTemplate({ ...newTemplate, content: e.target.value })}\n                                required\n                                className=\"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500\"\n                            />\n                         </div>\n                         \n                        <div>\n                           <label className=\"block text-sm font-medium text-text-secondary mb-2\">Select Variables to Use:</label>\n                           <div className=\"flex flex-wrap gap-2\">\n                              {variables.map(variable => (\n                                <label key={variable.id} className=\"flex items-center space-x-2 bg-secondary-bg px-3 py-1 rounded-full text-sm cursor-pointer\">\n                                   <input \n                                      type=\"checkbox\" \n                                      className=\"form-checkbox h-4 w-4 text-primary rounded focus:ring-primary bg-secondary-bg border-gray-600\"\n                                      checked={newTemplate.selectedVariables.includes(variable.id)}\n                                      onChange={() => handleVariableSelection(variable.id)}\n                                    />\n                                    <span className=\"text-text-primary font-mono\">{`{{${variable.name}}}`}</span>\n                                </label>\n                              ))}\n                           </div>\n                         </div>\n                         \n                        <div className=\"flex justify-end\">\n                            <Button type=\"submit\">Create Template</Button>\n                         </div>\n                      </form>\n                    </Card>\n                    \n                    <h3 className=\"text-lg font-medium text-text-primary mb-3\">Existing Templates</h3>\n                     {templates.length === 0 ? (\n                       <p className=\"text-text-secondary\">No templates created yet.</p>\n                     ) : (\n                       <ul className=\"space-y-2\">\n                         {templates.map((template) => (\n                           <li key={template.id} className=\"bg-secondary-bg p-3 rounded-md\">\n                             <div className=\"flex justify-between items-center\">\n                               <span className=\"text-text-primary font-semibold\">{template.name}</span>\n                               <Button size=\"sm\" variant=\"secondary\" onClick={() => handleTemplateSelection(template)}>Preview</Button>\n                             </div>\n                              {/* Add edit/delete buttons later */}\n                           </li>\n                         ))}\n                       </ul>\n                     )}\n                </div>\n              )}\n            </div>\n          </Card>\n\n          {selectedTemplate && (\n            <Card className=\"mt-6\">\n              <div className=\"p-6\">\n                <h2 className=\"text-xl font-semibold text-text-primary mb-4\">Preview: {selectedTemplate.name}</h2>\n                 <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-4\">\n                     {/* Input fields for variable values */} \n                     <div>\n                        <h3 className=\"text-lg font-medium text-text-primary mb-3\">Set Variable Values for Preview</h3>\n                        <div className=\"space-y-3\">\n                        {selectedTemplate.variables.map((varId: string) => {\n                            const variable = variables.find(v => v.id === varId);\n                            if (!variable) return null;\n                            return (\n                                <Input\n                                    key={variable.id}\n                                    id={`preview-${variable.id}`}\n                                    name={variable.name}\n                                    label={variable.name}\n                                    value={previewValues[variable.name] || ''}\n                                    onChange={(e: ChangeEvent<HTMLInputElement>) => handlePreviewValueChange(variable.name, e.target.value)}\n                                />\n                            );\n                        })}\n                        </div>\n                        <Button onClick={generatePreview} className=\"mt-4\">Update Preview</Button>\n                     </div>\n                     {/* Preview Output */} \n                     <div>\n                        <h3 className=\"text-lg font-medium text-text-primary mb-3\">Live Preview</h3>\n                         <div className=\"prose prose-invert max-w-none bg-secondary-bg p-4 rounded-md text-text-primary min-h-[150px]\">\n                             {previewContent ? (\n                                 <div dangerouslySetInnerHTML={{ __html: previewContent.replace(/\\n/g, '<br />') }} />\n                             ) : (\n                                 <span className=\"text-text-secondary italic\">Enter values and update preview...</span>\n                             )}\n                         </div>\n                     </div>\n                 </div>\n              </div>\n            </Card>\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default PersonalizationEditor;\n"], "names": ["PersonalizationEditor", "variables", "setVariables", "useState", "templates", "setTemplates", "loading", "setLoading", "error", "setError", "newVariable", "setNewVariable", "name", "defaultValue", "newTemplate", "setNewTemplate", "content", "selectedVariables", "activeTab", "setActiveTab", "previewContent", "setPreviewContent", "previewValues", "setPreviewValues", "selectedTemplate", "setSelectedTemplate", "useEffect", "async", "variablesResponse", "templatesResponse", "Promise", "all", "personalizationService", "getVariables", "getTemplates", "success", "Array", "isArray", "data", "message", "prevError", "err", "fetchData", "_jsxs", "className", "children", "_jsx", "_Fragment", "<PERSON><PERSON>", "type", "onClose", "Card", "onClick", "onSubmit", "e", "preventDefault", "trim", "response", "createVariable", "Input", "label", "id", "value", "onChange", "target", "placeholder", "required", "<PERSON><PERSON>", "length", "map", "variable", "createTemplate", "htmlFor", "rows", "checked", "includes", "variableId", "isSelected", "prev", "filter", "handleVariableSelection", "template", "size", "variant", "initialValues", "for<PERSON>ach", "varId", "find", "v", "handleTemplateSelection", "handlePreviewValueChange", "variableName", "generatePreview", "preview", "Object", "entries", "_ref", "regex", "RegExp", "replace", "dangerouslySetInnerHTML", "__html"], "sourceRoot": ""}