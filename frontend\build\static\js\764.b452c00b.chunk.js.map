{"version": 3, "file": "static/js/764.b452c00b.chunk.js", "mappings": "8NAoBA,MA8VA,EA9V8BA,KAC5B,MAAM,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,MAGjBC,QAAQC,IAAI,4CAA6CC,KAAKC,UAAUP,IAExE,MAAOQ,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,KAC9BC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,KAClCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAWC,IAAgBN,EAAAA,EAAAA,WAAS,IACpCO,EAAOC,IAAYR,EAAAA,EAAAA,UAAS,KAC5BS,EAASC,IAAcV,EAAAA,EAAAA,UAAS,KAChCW,EAAYC,IAAiBZ,EAAAA,EAAAA,UAAsB,KACnDa,EAAoBC,IAAyBd,EAAAA,EAAAA,UAAsC,OACnFe,EAAkBC,IAAuBhB,EAAAA,EAAAA,WAAS,IAGzDiB,EAAAA,EAAAA,YAAU,KACRvB,QAAQC,IAAI,4DAA4DoB,WAA2BnB,KAAKC,UAAUP,KA0D7GyB,GAAoBzB,GAxDE4B,WAAa,IAADC,EACrC,IAAIC,EAA6C,KAC7CC,EAAqB,GAEzB,GAAQ,OAAJ/B,QAAI,IAAJA,GAAY,QAAR6B,EAAJ7B,EAAMQ,cAAM,IAAAqB,GAAZA,EAAcG,KAIhB,GAHAD,EAAqB/B,EAAKQ,OAAOwB,KACjCvB,EAAUsB,GAEiB,WAAvB/B,EAAKQ,OAAOyB,OACd7B,QAAQC,IAAI,+DAA+D0B,KAC3ED,EAAgB,SAEhBR,EAAc,SACT,GAA2B,YAAvBtB,EAAKQ,OAAOyB,OAAsB,CAC3C7B,QAAQC,IAAI,gEAAgE0B,oDAC5ED,EAAgB,UAChBV,EAAW,0FAGX,IAAK,IAADc,EAAAC,EACFrB,GAAW,GACX,MAAMsB,QAAiBC,EAAAA,EAAIC,IAAI,oBACzBC,EAAyB,OAARH,QAAQ,IAARA,GAAc,QAANF,EAARE,EAAUI,YAAI,IAAAN,GAAM,QAANC,EAAdD,EAAgBM,YAAI,IAAAL,OAAZ,EAARA,EAAsBM,QACzCF,GAAkBA,EAAeG,OAAS,GAC5CtC,QAAQC,IAAI,kEAAmEkC,GAC/EjB,EAAciB,KAEdnC,QAAQa,MAAM,mFACdC,EAAS,gGACTI,EAAc,IAElB,CAAE,MAAOqB,GAAgB,IAADC,EAAAC,EACtBzC,QAAQa,MAAM,yEAA0E0B,GACxFzB,GAA0B,QAAjB0B,EAAAD,EAASP,gBAAQ,IAAAQ,GAAM,QAANC,EAAjBD,EAAmBJ,YAAI,IAAAK,OAAN,EAAjBA,EAAyBC,UAAWH,EAASG,SAAW,mDACjExB,EAAc,GAChB,CAAC,QACER,GAAW,EACd,CAGF,MACGV,QAAQC,IAAI,6FAA6FL,EAAKQ,OAAOyB,6BACrHH,EAAgB,KAChBR,EAAc,SAGjBlB,QAAQC,IAAI,2DACZyB,EAAgB,KAChBR,EAAc,IAGhBE,EAAsBM,GACtBJ,GAAoB,GACpBtB,QAAQC,IAAI,sEAAsEyB,IAAgB,EAIlGiB,EACF,GAEC,CAAC/C,EAAMyB,IAEV,MAAMuB,EAAuBpB,UAC3B,IAAKpB,EAEH,YADAU,EAAS,8BAKX,GADoB,+EACH+B,KAAKzC,GAKtB,IAAK,IAAD0C,EAAAC,EAAAC,EACFtC,GAAW,GACXI,EAAS,IACTE,EAAW,IACXE,EAAc,IAEdlB,QAAQC,IAAI,+BAA+BG,KAG3C,MAAM4B,QAAiBC,EAAAA,EAAIgB,KAAK,oBAAqB,CACnD7C,WAIIiC,EAAkB,OAARL,QAAQ,IAARA,GAAc,QAANc,EAARd,EAAUI,YAAI,IAAAU,GAAM,QAANC,EAAdD,EAAgBV,YAAI,IAAAW,OAAZ,EAARA,EAAsB9B,WAGtC,GAFAjB,QAAQC,IAAI,wBAAyBoC,GAEzB,OAARL,QAAQ,IAARA,GAAc,QAANgB,EAARhB,EAAUI,YAAI,IAAAY,GAAdA,EAAgBjC,SAAWsB,GAAWA,EAAQC,OAAS,EAEzDpB,EAAcmB,GACdrB,EAAW,uGACXI,EAAsB,eACjB,CAAC,IAAD8B,EAIL,MAAMC,GAAuB,OAARnB,QAAQ,IAARA,GAAc,QAANkB,EAARlB,EAAUI,YAAI,IAAAc,OAAN,EAARA,EAAgBR,UAAW,6DAChD1C,QAAQa,MAAM,iCAAkCsC,EAAsB,OAARnB,QAAQ,IAARA,OAAQ,EAARA,EAAUI,MACxEtB,EAASqC,GACT/B,EAAsB,KACxB,CAEF,CAAE,MAAOgC,GAAW,IAADC,EAAAC,EAGjBtD,QAAQa,MAAM,0CAA2CuC,GAEzD,MAAMG,EAAkC,QAAfF,EAAGD,EAAIpB,gBAAQ,IAAAqB,GAAM,QAANC,EAAZD,EAAcjB,YAAI,IAAAkB,OAAN,EAAZA,EAAoBZ,QAChD5B,EAASyC,GAAuBH,EAAIV,SAAW,wEAC/CtB,EAAsB,KACxB,CAAC,QACCV,GAAW,EACb,MA9CEI,EAAS,mCA8CX,EAGI0C,EAA0BhC,UAE9B,GAAKpB,EAIL,IAAK,IAADqD,EAAAC,EACF9C,GAAa,GACbE,EAAS,IACTE,EAAW,IAEXhB,QAAQC,IAAI,6CAA6CG,KAGzD,MAAM4B,QAAiBC,EAAAA,EAAIC,IAAI,mBAGzByB,EAAoB,OAAR3B,QAAQ,IAARA,GAAc,QAANyB,EAARzB,EAAUI,YAAI,IAAAqB,GAAM,QAANC,EAAdD,EAAgBrB,YAAI,IAAAsB,OAAZ,EAARA,EAAsB7B,OAIxC,OAHA7B,QAAQC,IAAI,uBAAwB0D,GAG5BA,GACN,IAAK,SACHzC,EAAc,IACdE,EAAsB,UACtBJ,EAAW,oFACX,MACF,IAAK,UAEHA,EAAW,yIACXI,EAAsB,WACtB,MACF,QACEpB,QAAQa,MAAM,oDAAqD8C,GACnE7C,EAAS,sFAMf,CAAE,MAAOsC,GAAW,IAADQ,EAAAC,EACjB7D,QAAQa,MAAM,gDAAiDuC,GAC/DtC,GAAqB,QAAZ8C,EAAAR,EAAIpB,gBAAQ,IAAA4B,GAAM,QAANC,EAAZD,EAAcxB,YAAI,IAAAyB,OAAN,EAAZA,EAAoBnB,UAAWU,EAAIV,SAAW,gFAEvDtB,EAAsB,UACxB,CAAC,QACCR,GAAa,EACf,MA5CIE,EAAS,mDA4Cb,EAIIgD,EAA2BtC,UAC/B,GAAKpB,EAAL,CASAM,GAAW,GACXI,EAAS,IACTE,EAAW,IAEX,IAAK,IAAD+C,EACF/D,QAAQC,IAAI,6CAA6CG,KAGzD,MAAM4B,QAAiBC,EAAAA,EAAI+B,OAAO,mBAa1B,IAADC,EANP,GAAY,OAARjC,QAAQ,IAARA,GAAc,QAAN+B,EAAR/B,EAAUI,YAAI,IAAA2B,GAAdA,EAAgBhD,QAClBC,EAAW,4BAA4BZ,uBACvCC,EAAU,IACVG,EAAY,IACZU,EAAc,IACdE,EAAsB,WAEtBN,GAAiB,OAARkB,QAAQ,IAARA,GAAc,QAANiC,EAARjC,EAAUI,YAAI,IAAA6B,OAAN,EAARA,EAAgBvB,UAAW,yCAGxC,CAAE,MAAOU,GAAW,IAADc,EAAAC,EACjBnE,QAAQa,MAAM,iDAAkDuC,GAChEtC,GAAqB,QAAZoD,EAAAd,EAAIpB,gBAAQ,IAAAkC,GAAM,QAANC,EAAZD,EAAc9B,YAAI,IAAA+B,OAAN,EAAZA,EAAoBzB,UAAWU,EAAIV,SAAW,kEACzD,CAAC,QACChC,GAAW,EACb,CApCA,MAFEI,EAAS,qCAsCX,EAoGF,OACEsD,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACC,MAAM,uCAAsCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BAA0BF,SAAC,mIAIvC1D,IACC2D,EAAAA,EAAAA,KAACE,EAAAA,EAAK,CAACC,KAAK,QAAQjC,QAAS7B,EAAO+D,QAASA,IAAM9D,EAAS,IAAK2D,UAAU,SAE5E1D,IACCyD,EAAAA,EAAAA,KAACE,EAAAA,EAAK,CAACC,KAAK,UAAUjC,QAAS3B,EAAwD0D,UAAU,SAtGjFI,MACpB,IAAKxD,EACH,OAAOmD,EAAAA,EAAAA,KAAA,KAAAD,SAAG,6BAGZ,OAAQpD,GACN,IAAK,SACH,OACEiD,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMF,SAAA,EACnBH,EAAAA,EAAAA,MAAA,OAAKK,UAAU,yBAAwBF,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEF,UAEtFC,EAAAA,EAAAA,KAAA,OAAKM,MAAM,6BAA6BL,UAAU,qBAAqBM,QAAQ,YAAYC,KAAK,eAAcT,UAACC,EAAAA,EAAAA,KAAA,QAAMS,SAAS,UAAUC,EAAE,qHAAqHC,SAAS,iBAE1QX,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBF,SAAC,wBAExCH,EAAAA,EAAAA,MAAA,KAAGK,UAAU,2BAA0BF,SAAA,CAAC,gBAC1BC,EAAAA,EAAAA,KAAA,UAAAD,SAASnE,IAAgB,iEAEvCoE,EAAAA,EAAAA,KAACY,EAAAA,EAAM,CAACC,QAASA,IAAMvF,EAAS,cAAcyE,SAAC,uBAGrD,IAAK,UACH,OACEH,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMF,SAAA,EACnBH,EAAAA,EAAAA,MAAA,MAAIK,UAAU,2BAA0BF,SAAA,CAAC,8BAA0BC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yBAAwBF,SAAEnE,QAC7GoE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BAA0BF,SAAC,wHAGvCtD,EAAWqB,OAAS,GACnBkC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDF,UAE9DH,EAAAA,EAAAA,MAAA,SAAOK,UAAU,aAAYF,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,SAAAD,UAAOH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EAAIC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gCAA+BF,SAAC,UAASC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gCAA+BF,SAAC,eAAcC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gCAA+BF,SAAC,0BAC5KC,EAAAA,EAAAA,KAAA,SAAAD,SACGtD,EAAWqE,KAAI,CAACC,EAAQC,KACvBpB,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EAAgBC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sBAAqBF,SAAEgB,EAAOZ,QAAUH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sBAAqBF,SAAEgB,EAAO3D,QAAU4C,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iBAAgBF,SAAEgB,EAAOE,UAA1JD,aAMjBhB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCAAiCF,SAAC,mEAEjDC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BAA0BF,SAAC,iIAGxCH,EAAAA,EAAAA,MAAA,OAAKK,UAAU,mCAAkCF,SAAA,CAAC,KAChDC,EAAAA,EAAAA,KAACY,EAAAA,EAAM,CAACC,QAAS7B,EAAyBkC,SAAU/E,GAAaF,EAAQ8D,SACtE5D,EAAY,cAAgB,+BAE/B6D,EAAAA,EAAAA,KAACY,EAAAA,EAAM,CACLC,QAASvB,EACT4B,SAAUjF,GAAWE,EACrBgF,QAAQ,SAASpB,SAEhB9D,EAAU,gBAAkB,yBAKvC,QACE,OACE2D,EAAAA,EAAAA,MAAA,OAAKK,UAAU,OAAMF,SAAA,EACnBC,EAAAA,EAAAA,KAACoB,EAAAA,EAAK,CACJC,GAAG,SACHjE,KAAK,SACLkE,MAAM,cACNC,YAAY,iBACZN,MAAOrF,EACP4F,SAAWC,GAAM5F,EAAU4F,EAAEC,OAAOT,OACpCU,UAAQ,EACR1B,UAAU,UAEZD,EAAAA,EAAAA,KAACoB,EAAAA,EAAK,CACJC,GAAG,WACHjE,KAAK,WACLkE,MAAM,uBACNC,YAAY,wBACZN,MAAOlF,EACPyF,SAAWC,GAAMzF,EAAYyF,EAAEC,OAAOT,OAEtChB,UAAU,UAEZD,EAAAA,EAAAA,KAACY,EAAAA,EAAM,CAACC,QAASzC,EAAsB8C,SAAUjF,EAAQ8D,SACtD9D,EAAU,0BAA4B,gCAIjD,EAgBGoE,KAEI,C", "sources": ["pages/campaigns/DomainSetup.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport Input from 'components/Input';\nimport { useAuth } from 'contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport api from 'services/api'; // Import the configured api instance\n\n// Define types for DNS records for better clarity\ninterface DnsRecord {\n  type: string;\n  name: string;\n  value: string;\n}\n\nconst DomainSetup: React.FC = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  // Log user object immediately upon receiving it from context\n  console.log('[DomainSetup] User object from useAuth():', JSON.stringify(user));\n\n  const [domain, setDomain] = useState('');\n  const [domainId, setDomainId] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [verifying, setVerifying] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [dnsRecords, setDnsRecords] = useState<DnsRecord[]>([]);\n  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'active' | null>(null); // Default to null\n  const [initialCheckDone, setInitialCheckDone] = useState(false);\n\n  // Effect to check initial domain status on load\n  useEffect(() => {\n    console.log(`[DomainSetup useEffect] Running check. initialCheckDone: ${initialCheckDone}, user:`, JSON.stringify(user));\n\n    const checkInitialStatus = async () => { // Make outer function async\n      let initialStatus: 'pending' | 'active' | null = null;\n      let existingDomainName = '';\n\n      if (user?.domain?.name) {\n        existingDomainName = user.domain.name;\n        setDomain(existingDomainName);\n\n        if (user.domain.status === 'active') {\n          console.log(`[DomainSetup useEffect] Found ACTIVE domain in user object: ${existingDomainName}`);\n          initialStatus = 'active';\n          // Removed 'Domain previously verified' message\n          setDnsRecords([]); // Clear records for active state\n        } else if (user.domain.status === 'pending') {\n          console.log(`[DomainSetup useEffect] Found PENDING domain in user object: ${existingDomainName}. Setting status to pending & fetching records.`);\n          initialStatus = 'pending';\n          setSuccess('Existing domain verification is pending. Please add DNS records or click Check Status.');\n\n          // --- Fetch DNS Records for Pending Domain ---\n          try {\n            setLoading(true); // Indicate loading while fetching records\n            const response = await api.get('/domains/records'); // Call GET /api/v1/domains/records\n            const fetchedRecords = response?.data?.data?.records;\n            if (fetchedRecords && fetchedRecords.length > 0) {\n              console.log('[DomainSetup useEffect] Fetched DNS records for pending domain:', fetchedRecords);\n              setDnsRecords(fetchedRecords);\n            } else {\n              console.error('[DomainSetup useEffect] API returned success but no records for pending domain.');\n              setError('Could not retrieve DNS records for the pending domain. Please try cancelling and restarting.');\n              setDnsRecords([]); // Keep empty\n            }\n          } catch (fetchErr: any) {\n            console.error('[DomainSetup useEffect] Error fetching DNS records for pending domain:', fetchErr);\n            setError(fetchErr.response?.data?.message || fetchErr.message || 'Failed to fetch DNS records for pending domain.');\n            setDnsRecords([]); // Keep empty on error\n          } finally {\n             setLoading(false); // Stop loading indicator\n          }\n          // --- End Fetch DNS Records ---\n\n        } else {\n           console.log(`[DomainSetup useEffect] Found domain in user object but status is neither active/pending: ${user.domain.status}. Treating as null.`);\n           initialStatus = null;\n           setDnsRecords([]); // Clear records\n        }\n      } else {\n        console.log('[DomainSetup useEffect] No domain found in user object.');\n        initialStatus = null;\n        setDnsRecords([]); // Clear records\n      }\n\n      setVerificationStatus(initialStatus);\n      setInitialCheckDone(true);\n      console.log(`[DomainSetup useEffect] Check complete. verificationStatus set to: ${initialStatus}`);\n    };\n\n    if (!initialCheckDone && user) { // Ensure user is loaded before check\n      checkInitialStatus();\n    }\n\n  }, [user, initialCheckDone]);\n\n  const handleRegisterDomain = async () => {\n    if (!domain) {\n      setError('Please enter a domain name');\n      return;\n    }\n\n    const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/;\n    if (!domainRegex.test(domain)) {\n      setError('Please enter a valid domain name');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n      setSuccess('');\n      setDnsRecords([]); // Clear previous records\n\n      console.log(`Registering domain via API: ${domain}`);\n\n      // Use 'api' instance and relative path\n      const response = await api.post('/domains/register', { // Path relative to baseURL\n        domain\n      });\n\n      // --- Process Response ---\n      const records = response?.data?.data?.dnsRecords;\n      console.log('API Response Records:', records);\n\n      if (response?.data?.success && records && records.length > 0) {\n        // ---> SUCCESS CASE <---\n        setDnsRecords(records); // Set REAL records from API response\n        setSuccess('Domain registration initiated with SES. Please add the following DNS records to verify your domain.');\n        setVerificationStatus('pending');\n      } else {\n        // ---> FAILURE CASE (including domain already registered) <---\n        // Handle cases where API call might succeed technically (2xx status) but indicate failure logically (success: false),\n        // or doesn't return the expected records.\n        const errorMessage = response?.data?.message || 'API request failed or did not return expected DNS records.';\n        console.error('Domain registration API error:', errorMessage, response?.data);\n        setError(errorMessage); // Use setError to display the specific message from backend (e.g., \"Domain already registered...\")\n        setVerificationStatus(null); // Stay on the input form\n      }\n\n    } catch (err: any) {\n      // ---> NETWORK/SERVER ERROR CASE <---\n      // Handle network errors or non-2xx responses from axios\n      console.error('Axios error during domain registration:', err);\n      // Try to get message from backend error response, otherwise use generic error\n      const backendErrorMessage = err.response?.data?.message;\n      setError(backendErrorMessage || err.message || 'Failed to register domain with SES due to a network or server error.');\n      setVerificationStatus(null); // Allow user to try again\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCheckVerification = async () => {\n    // Domain is derived from state, which should be set if status is pending\n    if (!domain) {\n        setError('Cannot check verification without a domain name.');\n        return;\n    }\n    try {\n      setVerifying(true);\n      setError('');\n      setSuccess('');\n\n      console.log(`Checking verification via API for domain: ${domain}`);\n\n      // Use 'api' instance and relative path\n      const response = await api.get('/domains/verify'); // Path relative to baseURL\n\n      // --- Process Response ---\n      const apiStatus = response?.data?.data?.status; // Expect 'active' or 'pending'\n      console.log('API Response Status:', apiStatus);\n\n      // Use switch statement based on the actual API status\n      switch (apiStatus) {\n        case 'active': // Match backend response status\n          setDnsRecords([]); // Clear records on success\n          setVerificationStatus('active');\n          setSuccess('Domain verified successfully with SES! You can now send emails from this domain.');\n          break;\n        case 'pending': // Match backend response status\n          // Keep existing DNS records displayed (they don't change)\n          setSuccess('Domain verification is still pending with SES. Please ensure DNS records are correct and allow time for propagation (up to 48 hours).');\n          setVerificationStatus('pending'); // Keep status as 'pending'\n          break;\n        default: // Handle unexpected statuses from API\n          console.error('Unexpected status received from verification API:', apiStatus);\n          setError('Received an unexpected status while checking verification. Please try again later.');\n          // Decide whether to keep pending state or reset\n          // setVerificationStatus('pending');\n          break;\n      }\n\n    } catch (err: any) {\n      console.error('Axios error during domain verification check:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to check domain verification status due to a network or server error.');\n      // Keep the UI in pending state so user can see records and try again\n      setVerificationStatus('pending');\n    } finally {\n      setVerifying(false);\n    }\n  };\n\n  // --- NEW: Handler for Revoke/Cancel Button ---\n  const handleRevokeVerification = async () => {\n    if (!domain) {\n      setError('No pending domain found to revoke.');\n      return;\n    }\n    // Optional: Add a confirmation dialog\n    // if (!window.confirm(`Are you sure you want to revoke the verification request for ${domain}? You will need to start over.`)) {\n    //   return;\n    // }\n\n    setLoading(true); // Use general loading state for simplicity, or add a dedicated one\n    setError('');\n    setSuccess('');\n\n    try {\n      console.log(`Revoking verification request for domain: ${domain}`);\n\n      // --- REAL API CALL ---\n      const response = await api.delete('/domains/revoke'); // DELETE request to the new endpoint\n\n      // --- Remove Mock ---\n      // await new Promise(resolve => setTimeout(resolve, 500));\n      // const response = { data: { success: true, message: 'Verification revoked' } };\n      // --- End Mock ---\n\n      if (response?.data?.success) {\n        setSuccess(`Verification request for ${domain} has been revoked.`);\n        setDomain(''); // Clear domain state\n        setDomainId(''); // Clear ID state if used\n        setDnsRecords([]); // Clear records\n        setVerificationStatus(null); // Reset status to show input form\n      } else {\n        setError(response?.data?.message || 'Failed to revoke verification request.');\n      }\n\n    } catch (err: any) {\n      console.error('Axios error during domain verification revoke:', err);\n      setError(err.response?.data?.message || err.message || 'Failed to revoke verification due to a network or server error.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Remove console logs added previously for debugging initial state\n  // console.log('DomainSetup - Initial user object:', user);\n  // console.log('DomainSetup - Initial verificationStatus:', verificationStatus);\n\n  // Render logic based on verificationStatus\n  const renderContent = () => {\n    if (!initialCheckDone) {\n      return <p>Loading domain status...</p>; // Show loading initially\n    }\n\n    switch (verificationStatus) {\n      case 'active':\n        return (\n          <div className=\"mb-6\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-6 h-6 rounded-full bg-green-500 flex items-center justify-center mr-2\">\n                {/* Checkmark SVG */}\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-white\" viewBox=\"0 0 20 20\" fill=\"currentColor\"><path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" /></svg>\n              </div>\n              <span className=\"text-lg font-medium\">Domain Verified</span>\n            </div>\n            <p className=\"text-text-secondary mb-4\">\n              Your domain <strong>{domain}</strong> has been verified and is ready to use for sending emails.\n            </p>\n            <Button onClick={() => navigate('/campaigns')}>Go to Campaigns</Button>\n          </div>\n        );\n      case 'pending':\n        return (\n          <div className=\"mb-6\">\n            <h3 className=\"text-lg font-medium mb-2\">Verification Pending for: <span className=\"font-bold text-primary\">{domain}</span></h3>\n            <p className=\"text-text-secondary mb-4\">\n              Add the following DNS records to your domain provider (e.g., GoDaddy, Cloudflare) to verify ownership with AWS SES:\n            </p>\n            {dnsRecords.length > 0 ? (\n              <div className=\"bg-gray-800 p-4 rounded-md mb-4 overflow-x-auto\">\n                {/* DNS Table */}\n                <table className=\"min-w-full\">\n                  <thead><tr><th className=\"text-left text-text-secondary\">Type</th><th className=\"text-left text-text-secondary\">Name/Host</th><th className=\"text-left text-text-secondary\">Value/Points To</th></tr></thead>\n                  <tbody>\n                    {dnsRecords.map((record, index) => (\n                      <tr key={index}><td className=\"py-2 pr-4 break-all\">{record.type}</td><td className=\"py-2 pr-4 break-all\">{record.name}</td><td className=\"py-2 break-all\">{record.value}</td></tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            ) : (\n              <p className=\"text-text-secondary mb-4 italic\">DNS records are not currently available. Try checking status.</p>\n            )}\n            <p className=\"text-text-secondary mb-4\">\n              After adding these records, click the button below to check verification. DNS changes may take up to 48 hours to propagate.\n            </p>\n            <div className=\"flex items-center space-x-4 mt-4\"> {/* Button container */}\n              <Button onClick={handleCheckVerification} disabled={verifying || loading}>\n                {verifying ? 'Checking...' : 'Check Verification Status'}\n              </Button>\n              <Button\n                onClick={handleRevokeVerification}\n                disabled={loading || verifying}\n                variant=\"danger\" // Use a danger/warning style if available in your Button component\n              >\n                {loading ? 'Cancelling...' : 'Cancel Request'}\n              </Button>\n            </div>\n          </div>\n        );\n      default: // null or any other case\n        return (\n          <div className=\"mb-6\">\n            <Input\n              id=\"domain\"\n              name=\"domain\"\n              label=\"Domain Name\"\n              placeholder=\"yourdomain.com\"\n              value={domain}\n              onChange={(e) => setDomain(e.target.value)}\n              required\n              className=\"mb-4\"\n            />\n            <Input\n              id=\"domainId\"\n              name=\"domainId\"\n              label=\"Domain ID (Optional)\" // Label as optional unless confirmed required\n              placeholder=\"Optional reference ID\"\n              value={domainId}\n              onChange={(e) => setDomainId(e.target.value)}\n              // Removed required attribute\n              className=\"mb-4\"\n            />\n            <Button onClick={handleRegisterDomain} disabled={loading}>\n              {loading ? 'Registering with SES...' : 'Register Domain with SES'}\n            </Button>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <Card title=\"Email Sending Domain Setup (AWS SES)\">\n      <p className=\"text-text-secondary mb-6\">\n        Register and verify a domain with AWS SES to use it for sending emails. Add the generated DNS records to your domain provider.\n      </p>\n\n      {error && (\n        <Alert type=\"error\" message={error} onClose={() => setError('')} className=\"mb-4\" />\n      )}\n      {success && (\n        <Alert type=\"success\" message={success} /* Remove onClose if message should persist */ className=\"mb-4\" />\n      )}\n\n      {renderContent()}\n\n    </Card>\n  );\n};\n\nexport default DomainSetup;\n"], "names": ["DomainSetup", "user", "useAuth", "navigate", "useNavigate", "console", "log", "JSON", "stringify", "domain", "setDomain", "useState", "domainId", "setDomainId", "loading", "setLoading", "verifying", "setVerifying", "error", "setError", "success", "setSuccess", "dnsRecords", "setDnsRecords", "verificationStatus", "setVerificationStatus", "initialCheckDone", "setInitialCheckDone", "useEffect", "async", "_user$domain", "initialStatus", "existingDomainName", "name", "status", "_response$data", "_response$data$data", "response", "api", "get", "fetchedRecords", "data", "records", "length", "fetchErr", "_fetchErr$response", "_fetchErr$response$da", "message", "checkInitialStatus", "handleRegisterDomain", "test", "_response$data2", "_response$data2$data", "_response$data3", "post", "_response$data4", "errorMessage", "err", "_err$response", "_err$response$data", "backendErrorMessage", "handleCheckVerification", "_response$data5", "_response$data5$data", "api<PERSON><PERSON>us", "_err$response2", "_err$response2$data", "handleRevokeVerification", "_response$data6", "delete", "_response$data7", "_err$response3", "_err$response3$data", "_jsxs", "Card", "title", "children", "_jsx", "className", "<PERSON><PERSON>", "type", "onClose", "renderContent", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "<PERSON><PERSON>", "onClick", "map", "record", "index", "value", "disabled", "variant", "Input", "id", "label", "placeholder", "onChange", "e", "target", "required"], "sourceRoot": ""}