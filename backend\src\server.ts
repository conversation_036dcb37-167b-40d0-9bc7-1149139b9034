import dotenv from 'dotenv';
import http from 'http';
import { Server as SocketIOServer } from 'socket.io'; // Import Socket.IO Server

import app from './app'; // Import the Express app

// Load env vars FIRST
dotenv.config();

// Create HTTP server using the Express app
const httpServer = http.createServer(app);

// --- Initialize Socket.IO Server ---
const allowedOrigins = [
  'http://localhost:3001', // Current frontend origin
  'http://localhost:5173', // Previous origin
  // Add production/staging origins from environment variables if needed
  ...(process.env.FRONTEND_URL ? [process.env.FRONTEND_URL] : [])
];

export const io = new SocketIOServer(httpServer, {
  cors: {
    origin: allowedOrigins,
    methods: ["GET", "POST"], // Adjust methods if needed
    credentials: true
  }
});

console.log('[Server] Socket.IO initialized, allowing origins:', allowedOrigins);

io.on('connection', (socket) => {
  console.log(`[Server] Socket connected: ${socket.id}`);
  // Add any specific connection logic here if needed (e.g., joining rooms)
  socket.on('disconnect', () => {
    console.log(`[Server] Socket disconnected: ${socket.id}`);
  });
});
// --- End Socket.IO Init ---


// Start the HTTP server (instead of app.listen)
const PORT = process.env.PORT || 3000;
httpServer.listen(PORT, () => {
  console.log(`[Server] HTTP server running on port ${PORT}`);
});

// Optional: Export the http server if needed elsewhere, though io is usually enough
// export default httpServer; 