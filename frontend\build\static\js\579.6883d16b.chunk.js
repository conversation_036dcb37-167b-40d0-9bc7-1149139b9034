"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[579],{9579:(t,r,e)=>{e.d(r,{DZ:()=>s,NY:()=>i,Zg:()=>l,gx:()=>g,hD:()=>n,hd:()=>y,jg:()=>p,mv:()=>c,p9:()=>m,pT:()=>h,rG:()=>o});var a=e(6291);const o={generateContent:async(t,r)=>{try{return(await a.A.post("/ai/generate",{prompt:t,type:r})).data}catch(e){throw console.error("Error generating content:",e),e}},getHistory:async()=>{try{return(await a.A.get("/ai/history")).data}catch(t){throw console.error("Error fetching content history:",t),t}},saveContent:async(t,r)=>{try{return(await a.A.post("/ai/save",{contentId:t,name:r})).data}catch(e){throw console.error("Error saving content:",e),e}}},n={getVariables:async()=>{try{return(await a.A.get("/personalization/variables")).data}catch(t){throw console.error("Error fetching personalization variables:",t),t}},createVariable:async(t,r)=>{try{return(await a.A.post("/personalization/variables/create",{name:t,defaultValue:r})).data}catch(e){throw console.error("Error creating personalization variable:",e),e}},getTemplates:async()=>{try{return(await a.A.get("/personalization/templates")).data}catch(t){throw console.error("Error fetching personalization templates:",t),t}},createTemplate:async(t,r,e)=>{try{return(await a.A.post("/personalization/templates/create",{name:t,content:r,variables:e})).data}catch(o){throw console.error("Error creating personalization template:",o),o}}},c={getElements:async()=>{try{return(await a.A.get("/interactive/elements")).data}catch(t){throw console.error("Error fetching interactive elements:",t),t}},createElement:async(t,r,e)=>{try{return(await a.A.post("/interactive/elements/create",{type:t,content:r,settings:e})).data}catch(o){throw console.error("Error creating interactive element:",o),o}},getElementAnalytics:async t=>{try{return(await a.A.get(`/interactive/elements/${t}/analytics`)).data}catch(r){throw console.error("Error fetching element analytics:",r),r}}},s={getOptimalTimes:async()=>{try{return(await a.A.get("/send-time/optimal")).data}catch(t){throw console.error("Error fetching optimal send times:",t),t}},optimizeSendTime:async t=>{try{return(await a.A.post("/send-time/optimize",{contactId:t})).data}catch(r){throw console.error("Error optimizing send time:",r),r}},runBulkOptimization:async t=>{try{return(await a.A.post("/send-time/run-optimization",{segmentId:t})).data}catch(r){throw console.error("Error running bulk optimization:",r),r}},getJobStatus:async t=>{try{return(await a.A.get(`/send-time/job/${t}`)).data}catch(r){throw console.error("Error fetching optimization job status:",r),r}}},i={createTest:async t=>{try{return(await a.A.post("/ab-test/create",t)).data}catch(r){throw console.error("Error creating A/B test:",r),r}},getTestResults:async t=>{try{return(await a.A.get(`/ab-test/${t}/results`)).data}catch(r){throw console.error("Error fetching test results:",r),r}},getUserTests:async()=>{try{return(await a.A.get("/ab-test")).data}catch(t){throw console.error("Error fetching user tests:",t),t}},startTest:async t=>{try{return(await a.A.put(`/ab-test/${t}/start`)).data}catch(r){throw console.error("Error starting test:",r),r}},cancelTest:async t=>{try{return(await a.A.put(`/ab-test/${t}/cancel`)).data}catch(r){throw console.error("Error cancelling test:",r),r}}},l={createSegment:async t=>{try{return(await a.A.post("/segments/create",t)).data}catch(r){throw console.error("Error creating segment:",r),r}},getSegmentContacts:async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20;try{return(await a.A.get(`/segments/${t}/contacts?page=${r}&limit=${e}`)).data}catch(o){throw console.error("Error fetching segment contacts:",o),o}},getUserSegments:async()=>{try{return(await a.A.get("/segments")).data}catch(t){throw console.error("Error fetching user segments:",t),t}},updateSegment:async(t,r)=>{try{return(await a.A.put(`/segments/${t}`,r)).data}catch(e){throw console.error("Error updating segment:",e),e}},deleteSegment:async t=>{try{return(await a.A.delete(`/segments/${t}`)).data}catch(r){throw console.error("Error deleting segment:",r),r}}},h={getMetrics:async()=>{try{return(await a.A.get("/deliverability/metrics")).data}catch(t){throw console.error("Error fetching deliverability metrics:",t),t}},checkSpamScore:async(t,r)=>{try{return(await a.A.post("/deliverability/spam-score",{subject:t,content:r})).data}catch(e){throw console.error("Error checking spam score:",e),e}},updateMetrics:async t=>{try{return(await a.A.put("/deliverability/metrics",{metrics:t})).data}catch(r){throw console.error("Error updating deliverability metrics:",r),r}}},g={generatePreview:async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"iphone_13",e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"portrait";try{return(await a.A.post("/mobile-preview/generate",{content:t,device:r,orientation:e})).data}catch(o){throw console.error("Error generating mobile preview:",o),o}},testResponsiveness:async t=>{try{return(await a.A.post("/mobile-preview/test-responsiveness",{content:t})).data}catch(r){throw console.error("Error testing responsiveness:",r),r}},getPreviewHistory:async()=>{try{return(await a.A.get("/mobile-preview/history")).data}catch(t){throw console.error("Error getting preview history:",t),t}},getTemplateContent:async t=>{try{return(await a.A.get(`/templates/${t}`)).data}catch(r){throw console.error("Error getting template content:",r),r}},sendTestEmail:async(t,r)=>{try{return(await a.A.post("/mobile-preview/send-test",{email:t,content:r})).data}catch(e){throw console.error("Error sending test email:",e),e}}},p={getRecommendations:async(t,r)=>{try{return(await a.A.post("/templates/recommendations",{context:t,preferences:r})).data}catch(e){throw console.error("Error getting template recommendations:",e),e}},getAllTemplates:async()=>{try{return(await a.A.get("/templates")).data}catch(t){throw console.error("Error fetching all templates:",t),t}},getSystemTemplatesCategorized:async()=>{try{return(await a.A.get("/templates/system/categorized")).data}catch(t){throw console.error("Error fetching categorized system templates:",t),t}},getTemplateById:async t=>{try{return(await a.A.get(`/templates/${t}`)).data}catch(r){throw console.error(`Error fetching template ${t}:`,r),r}},createTemplate:async t=>{try{return(await a.A.post("/templates/create",t)).data}catch(r){throw console.error("Error creating template:",r),r}},updateTemplate:async(t,r)=>{try{return(await a.A.put(`/templates/${t}`,r)).data}catch(e){throw console.error(`Error updating template ${t}:`,e),e}},updatePerformance:async(t,r)=>{try{return(await a.A.patch(`/templates/${t}/performance`,r)).data}catch(e){throw console.error(`Error updating template performance ${t}:`,e),e}},deleteTemplate:async t=>{try{return(await a.A.delete(`/templates/${t}`)).data}catch(r){throw console.error(`Error deleting template ${t}:`,r),r}}},y={getContacts:async t=>{try{return(await a.A.get("/contacts",{params:{tag:t}})).data}catch(r){throw console.error("Error fetching contacts:",r),r}},getContact:async t=>{try{return(await a.A.get(`/contacts/${t}`)).data}catch(r){throw console.error("Error fetching contact:",r),r}},createContact:async t=>{try{return(await a.A.post("/contacts",t)).data}catch(r){throw console.error("Error creating contact:",r),r}},updateContact:async(t,r)=>{try{return(await a.A.put(`/contacts/${t}`,r)).data}catch(e){throw console.error("Error updating contact:",e),e}},deleteContact:async t=>{try{return(await a.A.delete(`/contacts/${t}`)).data}catch(r){throw console.error("Error deleting contact:",r),r}},importContacts:async(t,r,e)=>{try{const o=new FormData;o.append("file",t),o.append("format",r),e&&o.append("segmentId",e);return(await a.A.post("/contacts/import",o,{headers:{"Content-Type":"multipart/form-data"}})).data}catch(o){throw console.error("Error importing contacts:",o),o}},exportContacts:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"csv",r=arguments.length>1?arguments[1]:void 0;try{return(await a.A.post("/contacts/export",{format:t,segmentId:r})).data}catch(e){throw console.error("Error exporting contacts:",e),e}},addTag:async(t,r)=>{try{return(await a.A.post(`/contacts/${t}/tags`,{tag:r})).data}catch(e){throw console.error("Error adding tag to contact:",e),e}},removeTag:async(t,r)=>{try{return(await a.A.delete(`/contacts/${t}/tags/${r}`)).data}catch(e){throw console.error("Error removing tag from contact:",e),e}}},m={getHistory:async()=>{try{return(await a.A.get("/billing/history")).data}catch(t){throw console.error("Error fetching billing history:",t),t}},purchaseFlows:async t=>{try{return(await a.A.post("/billing/purchase",{quantity:t})).data}catch(r){throw console.error("Error purchasing flows:",r),r}}}}}]);
//# sourceMappingURL=579.6883d16b.chunk.js.map