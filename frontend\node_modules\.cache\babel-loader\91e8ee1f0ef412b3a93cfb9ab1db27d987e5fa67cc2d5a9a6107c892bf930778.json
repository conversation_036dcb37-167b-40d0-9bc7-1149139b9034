{"ast": null, "code": "import React,{Fragment}from'react';import{Dialog,Transition}from'@headlessui/react';import Button from'./Button';// Assuming Button component exists\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const Modal=_ref=>{let{isOpen,onClose,title,children,onConfirm,confirmText='Confirm',confirmVariant='primary',cancelText='Cancel'}=_ref;return/*#__PURE__*/_jsx(Transition,{appear:true,show:isOpen,as:Fragment,children:/*#__PURE__*/_jsxs(Dialog,{as:\"div\",className:\"relative z-10\",onClose:onClose,children:[/*#__PURE__*/_jsx(Transition.Child,{as:Fragment,enter:\"ease-out duration-300\",enterFrom:\"opacity-0\",enterTo:\"opacity-100\",leave:\"ease-in duration-200\",leaveFrom:\"opacity-100\",leaveTo:\"opacity-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black bg-opacity-50\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 overflow-y-auto\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex min-h-full items-center justify-center p-4 text-center\",children:/*#__PURE__*/_jsx(Transition.Child,{as:Fragment,enter:\"ease-out duration-300\",enterFrom:\"opacity-0 scale-95\",enterTo:\"opacity-100 scale-100\",leave:\"ease-in duration-200\",leaveFrom:\"opacity-100 scale-100\",leaveTo:\"opacity-0 scale-95\",children:/*#__PURE__*/_jsxs(Dialog.Panel,{className:\"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all\",children:[/*#__PURE__*/_jsx(Dialog.Title,{as:\"h3\",className:\"text-lg font-medium leading-6 text-white mb-4\",children:title}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 text-sm text-gray-300\",children:children}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 flex justify-end space-x-3\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:onClose,children:cancelText}),onConfirm&&/*#__PURE__*/_jsx(Button,{variant:confirmVariant,onClick:onConfirm,children:confirmText})]})]})})})})]})});};", "map": {"version": 3, "names": ["React", "Fragment", "Dialog", "Transition", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "Modal", "_ref", "isOpen", "onClose", "title", "children", "onConfirm", "confirmText", "confirmVariant", "cancelText", "appear", "show", "as", "className", "Child", "enter", "enterFrom", "enterTo", "leave", "leaveFrom", "leaveTo", "Panel", "Title", "variant", "onClick"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/Modal.tsx"], "sourcesContent": ["import React, { Fragment } from 'react';\r\n\r\nimport {\r\n  Dialog,\r\n  Transition,\r\n} from '@headlessui/react';\r\n\r\nimport Button from './Button'; // Assuming Button component exists\r\n\r\ninterface ModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  title: string;\r\n  children: React.ReactNode;\r\n  onConfirm?: () => void;\r\n  confirmText?: string;\r\n  confirmVariant?: 'primary' | 'secondary' | 'danger';\r\n  cancelText?: string;\r\n}\r\n\r\nexport const Modal: React.FC<ModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  title,\r\n  children,\r\n  onConfirm,\r\n  confirmText = 'Confirm',\r\n  confirmVariant = 'primary',\r\n  cancelText = 'Cancel',\r\n}) => {\r\n  return (\r\n    <Transition appear show={isOpen} as={Fragment}>\r\n      <Dialog as=\"div\" className=\"relative z-10\" onClose={onClose}>\r\n        <Transition.Child\r\n          as={Fragment}\r\n          enter=\"ease-out duration-300\"\r\n          enterFrom=\"opacity-0\"\r\n          enterTo=\"opacity-100\"\r\n          leave=\"ease-in duration-200\"\r\n          leaveFrom=\"opacity-100\"\r\n          leaveTo=\"opacity-0\"\r\n        >\r\n          <div className=\"fixed inset-0 bg-black bg-opacity-50\" />\r\n        </Transition.Child>\r\n\r\n        <div className=\"fixed inset-0 overflow-y-auto\">\r\n          <div className=\"flex min-h-full items-center justify-center p-4 text-center\">\r\n            <Transition.Child\r\n              as={Fragment}\r\n              enter=\"ease-out duration-300\"\r\n              enterFrom=\"opacity-0 scale-95\"\r\n              enterTo=\"opacity-100 scale-100\"\r\n              leave=\"ease-in duration-200\"\r\n              leaveFrom=\"opacity-100 scale-100\"\r\n              leaveTo=\"opacity-0 scale-95\"\r\n            >\r\n              <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-lg bg-gray-800 p-6 text-left align-middle shadow-xl transition-all\">\r\n                <Dialog.Title\r\n                  as=\"h3\"\r\n                  className=\"text-lg font-medium leading-6 text-white mb-4\"\r\n                >\r\n                  {title}\r\n                </Dialog.Title>\r\n                <div className=\"mt-2 text-sm text-gray-300\">\r\n                  {children}\r\n                </div>\r\n\r\n                <div className=\"mt-6 flex justify-end space-x-3\">\r\n                  <Button variant=\"secondary\" onClick={onClose}>\r\n                    {cancelText}\r\n                  </Button>\r\n                  {onConfirm && (\r\n                    <Button variant={confirmVariant} onClick={onConfirm}>\r\n                      {confirmText}\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </Dialog.Panel>\r\n            </Transition.Child>\r\n          </div>\r\n        </div>\r\n      </Dialog>\r\n    </Transition>\r\n  );\r\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CAEvC,OACEC,MAAM,CACNC,UAAU,KACL,mBAAmB,CAE1B,MAAO,CAAAC,MAAM,KAAM,UAAU,CAAE;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAa/B,MAAO,MAAM,CAAAC,KAA2B,CAAGC,IAAA,EASrC,IATsC,CAC1CC,MAAM,CACNC,OAAO,CACPC,KAAK,CACLC,QAAQ,CACRC,SAAS,CACTC,WAAW,CAAG,SAAS,CACvBC,cAAc,CAAG,SAAS,CAC1BC,UAAU,CAAG,QACf,CAAC,CAAAR,IAAA,CACC,mBACEJ,IAAA,CAACH,UAAU,EAACgB,MAAM,MAACC,IAAI,CAAET,MAAO,CAACU,EAAE,CAAEpB,QAAS,CAAAa,QAAA,cAC5CN,KAAA,CAACN,MAAM,EAACmB,EAAE,CAAC,KAAK,CAACC,SAAS,CAAC,eAAe,CAACV,OAAO,CAAEA,OAAQ,CAAAE,QAAA,eAC1DR,IAAA,CAACH,UAAU,CAACoB,KAAK,EACfF,EAAE,CAAEpB,QAAS,CACbuB,KAAK,CAAC,uBAAuB,CAC7BC,SAAS,CAAC,WAAW,CACrBC,OAAO,CAAC,aAAa,CACrBC,KAAK,CAAC,sBAAsB,CAC5BC,SAAS,CAAC,aAAa,CACvBC,OAAO,CAAC,WAAW,CAAAf,QAAA,cAEnBR,IAAA,QAAKgB,SAAS,CAAC,sCAAsC,CAAE,CAAC,CACxC,CAAC,cAEnBhB,IAAA,QAAKgB,SAAS,CAAC,+BAA+B,CAAAR,QAAA,cAC5CR,IAAA,QAAKgB,SAAS,CAAC,6DAA6D,CAAAR,QAAA,cAC1ER,IAAA,CAACH,UAAU,CAACoB,KAAK,EACfF,EAAE,CAAEpB,QAAS,CACbuB,KAAK,CAAC,uBAAuB,CAC7BC,SAAS,CAAC,oBAAoB,CAC9BC,OAAO,CAAC,uBAAuB,CAC/BC,KAAK,CAAC,sBAAsB,CAC5BC,SAAS,CAAC,uBAAuB,CACjCC,OAAO,CAAC,oBAAoB,CAAAf,QAAA,cAE5BN,KAAA,CAACN,MAAM,CAAC4B,KAAK,EAACR,SAAS,CAAC,sHAAsH,CAAAR,QAAA,eAC5IR,IAAA,CAACJ,MAAM,CAAC6B,KAAK,EACXV,EAAE,CAAC,IAAI,CACPC,SAAS,CAAC,+CAA+C,CAAAR,QAAA,CAExDD,KAAK,CACM,CAAC,cACfP,IAAA,QAAKgB,SAAS,CAAC,4BAA4B,CAAAR,QAAA,CACxCA,QAAQ,CACN,CAAC,cAENN,KAAA,QAAKc,SAAS,CAAC,iCAAiC,CAAAR,QAAA,eAC9CR,IAAA,CAACF,MAAM,EAAC4B,OAAO,CAAC,WAAW,CAACC,OAAO,CAAErB,OAAQ,CAAAE,QAAA,CAC1CI,UAAU,CACL,CAAC,CACRH,SAAS,eACRT,IAAA,CAACF,MAAM,EAAC4B,OAAO,CAAEf,cAAe,CAACgB,OAAO,CAAElB,SAAU,CAAAD,QAAA,CACjDE,WAAW,CACN,CACT,EACE,CAAC,EACM,CAAC,CACC,CAAC,CAChB,CAAC,CACH,CAAC,EACA,CAAC,CACC,CAAC,CAEjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}