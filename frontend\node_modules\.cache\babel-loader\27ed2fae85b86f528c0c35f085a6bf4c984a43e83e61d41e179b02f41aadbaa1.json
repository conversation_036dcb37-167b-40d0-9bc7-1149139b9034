{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\DONT DELETE\\\\driftly-enhanced-current-2.0.0\\\\frontend\\\\src\\\\pages\\\\templates\\\\TemplateForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport AISuggestionPanel from 'components/AISuggestionPanel';\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport HtmlEmailEditor from 'components/HtmlEmailEditor';\nimport Input from 'components/Input';\nimport { useNavigate, useParams } from 'react-router-dom';\n// Import services\nimport { templateRecommendationService } from 'services';\nimport api from 'services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TemplateForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    templateId\n  } = useParams();\n  const isEditing = Boolean(templateId);\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [category, setCategory] = useState('Other');\n  const [content, setContent] = useState('');\n  const [mjmlContent, setMjmlContent] = useState('');\n  const [thumbnail, setThumbnail] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [success, setSuccess] = useState(null);\n  const [step, setStep] = useState(1);\n\n  // AI suggestion states\n  const [showAiSuggestions, setShowAiSuggestions] = useState(false);\n  const [suggestionType, setSuggestionType] = useState('headline');\n  const [suggestions, setSuggestions] = useState([]);\n  const [loadingSuggestions, setLoadingSuggestions] = useState(false);\n  useEffect(() => {\n    if (isEditing && templateId) {\n      fetchTemplate(templateId);\n    }\n  }, [templateId]);\n  const fetchTemplate = async templateId => {\n    setLoading(true);\n    try {\n      // Use the correct service method\n      const response = await templateRecommendationService.getTemplateById(templateId);\n      if (response.success && response.template) {\n        // The template data is under the 'template' key in the response\n        const template = response.template;\n        setName(template.name);\n        setDescription(template.description);\n        setCategory(template.category);\n        setContent(template.content); // Make sure 'content' is included in API response\n        setThumbnail(template.thumbnail || ''); // Handle potentially missing thumbnail\n\n        // Set MJML content if available\n        if (template.mjmlContent) {\n          setMjmlContent(template.mjmlContent);\n        }\n      } else {\n        setError(response.message || 'Failed to fetch template');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || err.message || 'Failed to fetch template');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle MjmlEditor save event\n  const handleHtmlSave = html => {\n    console.log('Template HTML saved:', html ? `${html.substring(0, 50)}...` : '(empty)');\n    // Update template content\n    setContent(html);\n    setMjmlContent(html);\n    setHasUnsavedChanges(true);\n  };\n  const handleSubmit = async e => {\n    e === null || e === void 0 ? void 0 : e.preventDefault();\n    if (step === 1) {\n      // Validate first step\n      if (!name || !description || !category) {\n        setError('Please fill in all required fields');\n        return;\n      }\n\n      // Generate a thumbnail if not provided\n      if (!thumbnail) {\n        setThumbnail(`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`);\n      }\n      setStep(2);\n      return;\n    }\n\n    // Step 2 submission\n    console.log('[Submit Step 2] Starting...');\n\n    // Validate that we have content (both HTML and MJML? or just HTML?)\n    // Let's assume HTML is primary for validation\n    if (!content) {\n      setError('Please create a template design before saving');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    console.log('[Submit Step 2] Preparing API call...');\n    try {\n      const templateData = {\n        name,\n        description,\n        category,\n        content,\n        // HTML content\n        thumbnail: thumbnail || `https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`,\n        mjmlContent: mjmlContent // Added MJML content\n      };\n      console.log('[Submit Step 2] Template data size:', JSON.stringify(templateData).length, 'bytes');\n      let response;\n      if (isEditing && templateId) {\n        console.log('[Submit Step 2] Updating template ID:', templateId);\n        response = await templateRecommendationService.updateTemplate(templateId, templateData);\n      } else {\n        console.log('[Submit Step 2] Creating new template');\n        response = await templateRecommendationService.createTemplate(templateData);\n      }\n      console.log('[Submit Step 2] API Response:', response);\n      if (response.success) {\n        console.log('[Submit Step 2] Success! Setting timeout for navigation.');\n        setSuccess(isEditing ? 'Template updated successfully' : 'Template created successfully');\n\n        // Use window.location for a full page refresh to avoid navigation issues\n        setTimeout(() => {\n          console.log('[Submit Step 2] Navigating back to templates');\n          window.location.href = '/email-templates'; // Update to match the route in App.tsx\n        }, 1500);\n      } else {\n        console.error('[Submit Step 2] API call not successful:', response.message);\n        setError(response.message || (isEditing ? 'Failed to update template' : 'Failed to create template'));\n      }\n    } catch (err) {\n      console.error('[Submit Step 2] Error during API call:', err);\n\n      // Provide more detailed error message based on status code\n      if (err.response) {\n        if (err.response.status === 404) {\n          setError('API endpoint not found. Please check server configuration.');\n        } else if (err.response.status === 413) {\n          setError('Template content is too large. Try simplifying your design.');\n        } else if (err.response.status === 400) {\n          var _err$response$data2;\n          setError(((_err$response$data2 = err.response.data) === null || _err$response$data2 === void 0 ? void 0 : _err$response$data2.message) || 'Invalid template data. Please check your inputs.');\n        } else if (err.response.status >= 500) {\n          setError('Server error. Please try again later or contact support.');\n        } else {\n          var _err$response$data3;\n          setError(((_err$response$data3 = err.response.data) === null || _err$response$data3 === void 0 ? void 0 : _err$response$data3.message) || err.message || 'An error occurred while saving the template.');\n        }\n      } else if (err.request) {\n        setError('No response from server. Check your internet connection and try again.');\n      } else {\n        setError(err.message || 'An unexpected error occurred');\n      }\n    } finally {\n      console.log('[Submit Step 2] Setting loading to false.');\n      setLoading(false);\n    }\n  };\n  const handleBack = () => {\n    if (step === 2) {\n      setStep(1);\n    } else {\n      navigate('/email-templates');\n    }\n  };\n\n  // Get AI suggestions for the selected element\n  const getSuggestions = async (content, type) => {\n    try {\n      setLoadingSuggestions(true);\n      setSuggestions([]);\n      const response = await api.post('/ai-templates/suggest', {\n        content,\n        suggestionType: type\n      }, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.data.success) {\n        setSuggestions(response.data.data.suggestions);\n      } else {\n        console.error('Failed to get suggestions:', response.data.message);\n      }\n    } catch (error) {\n      console.error('Error getting suggestions:', error);\n    } finally {\n      setLoadingSuggestions(false);\n    }\n  };\n  if (loading && step === 1) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-800\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center p-4 bg-gray-800 text-white border-b border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-xl font-semibold\",\n        children: isEditing ? `Editing: ${name}` : 'Create New Template'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          onClick: handleBack,\n          variant: \"secondary\",\n          size: \"sm\",\n          children: step === 1 ? 'Back to Templates' : 'Back to Details'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"button\",\n          onClick: handleSubmit,\n          variant: \"primary\",\n          size: \"sm\",\n          disabled: loading,\n          children: loading ? 'Saving...' : step === 1 ? 'Next: Design' : isEditing ? 'Update & Close' : 'Save & Close'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-grow overflow-hidden\",\n      children: step === 1 ?\n      /*#__PURE__*/\n      // Step 1 Form (centered or styled differently)\n      _jsxDEV(\"div\", {\n        className: \"flex-grow p-6 bg-gray-900 overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-2xl mx-auto bg-gray-800 rounded-lg p-8\",\n          children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n            type: \"error\",\n            message: error,\n            className: \"mb-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 25\n          }, this), success && /*#__PURE__*/_jsxDEV(Alert, {\n            type: \"success\",\n            message: success,\n            className: \"mb-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 27\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                id: \"templateName\",\n                name: \"templateName\",\n                label: \"Template Name\",\n                type: \"text\",\n                value: name,\n                onChange: e => setName(e.target.value),\n                required: true,\n                placeholder: \"Enter template name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 20\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-white mb-2\",\n                children: \"Description\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: description,\n                onChange: e => setDescription(e.target.value),\n                required: true,\n                placeholder: \"Enter template description\",\n                className: \"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800 resize-none h-32\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-white mb-2\",\n                children: \"Category\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: category,\n                onChange: e => setCategory(e.target.value),\n                className: \"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Marketing\",\n                  children: \"Marketing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Transactional\",\n                  children: \"Transactional\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Newsletter\",\n                  children: \"Newsletter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Event\",\n                  children: \"Event\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Onboarding\",\n                  children: \"Onboarding\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Follow-up\",\n                  children: \"Follow-up\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Promotional\",\n                  children: \"Promotional\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 22\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 22\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 20\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(Input, {\n                id: \"thumbnailUrl\",\n                name: \"thumbnailUrl\",\n                label: \"Thumbnail URL (optional)\",\n                type: \"text\",\n                value: thumbnail,\n                onChange: e => setThumbnail(e.target.value),\n                placeholder: \"Enter thumbnail URL or leave blank\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 20\n              }, this), thumbnail && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: thumbnail,\n                  alt: \"Thumbnail preview\",\n                  className: \"h-32 object-cover rounded-lg\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 56\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 34\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 18\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-end\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"submit\",\n                variant: \"primary\",\n                size: \"md\",\n                children: \"Next: Design Template\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 20\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // Step 2 Editor Layout\n      _jsxDEV(\"div\", {\n        className: \"flex flex-grow h-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto flex flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 text-white text-center font-semibold border-b border-gray-700\",\n            children: \"Template Options\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-300 mb-2\",\n                children: \"AI Suggestions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                size: \"sm\",\n                className: \"w-full\",\n                onClick: () => {\n                  setSuggestionType('headline');\n                  setShowAiSuggestions(true);\n                  getSuggestions('Headline suggestion for email', 'headline');\n                },\n                children: \"Get Headline Ideas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                size: \"sm\",\n                className: \"w-full mt-2\",\n                onClick: () => {\n                  setSuggestionType('body');\n                  setShowAiSuggestions(true);\n                  getSuggestions('Body text suggestion for email', 'body');\n                },\n                children: \"Get Body Text Ideas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"secondary\",\n                size: \"sm\",\n                className: \"w-full mt-2\",\n                onClick: () => {\n                  setSuggestionType('cta');\n                  setShowAiSuggestions(true);\n                  getSuggestions('Call to action suggestion', 'cta');\n                },\n                children: \"Get CTA Ideas\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-medium text-gray-300 mb-2\",\n                children: \"Help\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-400\",\n                children: \"Use the MJML editor to create responsive emails. Customize your template using the available blocks and settings.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-grow flex flex-col bg-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(HtmlEmailEditor, {\n            initialHtml: content,\n            onSave: handleHtmlSave,\n            height: \"calc(100vh - 200px)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), showAiSuggestions && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-80 top-40 w-80 z-50\",\n            children: /*#__PURE__*/_jsxDEV(AISuggestionPanel, {\n              type: suggestionType,\n              suggestions: suggestions,\n              isLoading: loadingSuggestions,\n              onApply: suggestion => {\n                // Note: This needs custom handling with MjmlEditor\n                // Likely requires interacting with the GrapesJS editor instance via a ref\n                console.log('Selected suggestion:', suggestion);\n                alert('Applying AI suggestions directly into the MJML editor is not yet implemented.'); // Placeholder\n                setShowAiSuggestions(false);\n              },\n              onClose: () => setShowAiSuggestions(false)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 223,\n    columnNumber: 5\n  }, this);\n};\n_s(TemplateForm, \"DJe6jTAx85zApeBdCmfwmMaMwqo=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = TemplateForm;\nexport default TemplateForm;\nvar _c;\n$RefreshReg$(_c, \"TemplateForm\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "AISuggestionPanel", "<PERSON><PERSON>", "<PERSON><PERSON>", "HtmlEmailEditor", "Input", "useNavigate", "useParams", "templateRecommendationService", "api", "jsxDEV", "_jsxDEV", "TemplateForm", "_s", "navigate", "templateId", "isEditing", "Boolean", "name", "setName", "description", "setDescription", "category", "setCategory", "content", "<PERSON><PERSON><PERSON><PERSON>", "mjml<PERSON><PERSON><PERSON>", "setMjmlContent", "thumbnail", "setThumbnail", "loading", "setLoading", "error", "setError", "success", "setSuccess", "step", "setStep", "showAiSuggestions", "setShowAiSuggestions", "suggestionType", "setSuggestionType", "suggestions", "setSuggestions", "loadingSuggestions", "setLoadingSuggestions", "fetchTemplate", "response", "getTemplateById", "template", "message", "err", "_err$response", "_err$response$data", "data", "handleHtmlSave", "html", "console", "log", "substring", "setHasUnsavedChanges", "handleSubmit", "e", "preventDefault", "encodeURIComponent", "templateData", "JSON", "stringify", "length", "updateTemplate", "createTemplate", "setTimeout", "window", "location", "href", "status", "_err$response$data2", "_err$response$data3", "request", "handleBack", "getSuggestions", "type", "post", "headers", "localStorage", "getItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "variant", "size", "disabled", "onSubmit", "id", "label", "value", "onChange", "target", "required", "placeholder", "src", "alt", "initialHtml", "onSave", "height", "isLoading", "onApply", "suggestion", "alert", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/templates/TemplateForm.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport AISuggestionPanel from 'components/AISuggestionPanel';\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport HtmlEmailEditor from 'components/HtmlEmailEditor';\nimport Input from 'components/Input';\nimport {\n  useNavigate,\n  useParams,\n} from 'react-router-dom';\n// Import services\nimport { templateRecommendationService } from 'services';\nimport api from 'services/api';\n\nconst TemplateForm: React.FC = () => {\n  const navigate = useNavigate();\n  const { templateId } = useParams<{ templateId: string }>();\n  const isEditing = Boolean(templateId);\n  const [name, setName] = useState<string>('');\n  const [description, setDescription] = useState<string>('');\n  const [category, setCategory] = useState<string>('Other');\n  const [content, setContent] = useState<string>('');\n  const [mjmlContent, setMjmlContent] = useState<string>('');\n  const [thumbnail, setThumbnail] = useState<string>('');\n  const [loading, setLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [step, setStep] = useState<number>(1);\n\n  // AI suggestion states\n  const [showAiSuggestions, setShowAiSuggestions] = useState<boolean>(false);\n  const [suggestionType, setSuggestionType] = useState<'headline' | 'body' | 'cta'>('headline');\n  const [suggestions, setSuggestions] = useState<string[]>([]);\n  const [loadingSuggestions, setLoadingSuggestions] = useState<boolean>(false);\n\n  useEffect(() => {\n    if (isEditing && templateId) {\n      fetchTemplate(templateId);\n    }\n  }, [templateId]);\n\n  const fetchTemplate = async (templateId: string) => {\n    setLoading(true);\n    try {\n      // Use the correct service method\n      const response = await templateRecommendationService.getTemplateById(templateId);\n      if (response.success && response.template) {\n        // The template data is under the 'template' key in the response\n        const template = response.template;\n        setName(template.name);\n        setDescription(template.description);\n        setCategory(template.category);\n        setContent(template.content); // Make sure 'content' is included in API response\n        setThumbnail(template.thumbnail || ''); // Handle potentially missing thumbnail\n        \n        // Set MJML content if available\n        if (template.mjmlContent) {\n          setMjmlContent(template.mjmlContent);\n        }\n      } else {\n        setError(response.message || 'Failed to fetch template');\n      }\n    } catch (err: any) {\n      setError(err.response?.data?.message || err.message || 'Failed to fetch template');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle MjmlEditor save event\n  const handleHtmlSave = (html: string) => {\n    console.log('Template HTML saved:', html ? `${html.substring(0, 50)}...` : '(empty)');\n    // Update template content\n    setContent(html);\n    setMjmlContent(html);\n    setHasUnsavedChanges(true);\n  };\n\n  const handleSubmit = async (e?: React.FormEvent) => {\n    e?.preventDefault();\n\n    if (step === 1) {\n      // Validate first step\n      if (!name || !description || !category) {\n        setError('Please fill in all required fields');\n        return;\n      }\n\n      // Generate a thumbnail if not provided\n      if (!thumbnail) {\n        setThumbnail(`https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`);\n      }\n\n      setStep(2);\n      return;\n    }\n\n    // Step 2 submission\n    console.log('[Submit Step 2] Starting...');\n    \n    // Validate that we have content (both HTML and MJML? or just HTML?)\n    // Let's assume HTML is primary for validation\n    if (!content) {\n      setError('Please create a template design before saving');\n      return;\n    }\n\n    setLoading(true);\n    setError(null);\n    console.log('[Submit Step 2] Preparing API call...');\n\n    try {\n      const templateData = {\n        name,\n        description,\n        category,\n        content, // HTML content\n        thumbnail: thumbnail || `https://via.placeholder.com/300x200/1E3A8A/FFFFFF?text=${encodeURIComponent(name)}`,\n        mjmlContent: mjmlContent, // Added MJML content\n      };\n\n      console.log('[Submit Step 2] Template data size:', JSON.stringify(templateData).length, 'bytes');\n      \n      let response;\n\n      if (isEditing && templateId) {\n        console.log('[Submit Step 2] Updating template ID:', templateId);\n        response = await templateRecommendationService.updateTemplate(templateId, templateData);\n      } else {\n        console.log('[Submit Step 2] Creating new template');\n        response = await templateRecommendationService.createTemplate(templateData);\n      }\n\n      console.log('[Submit Step 2] API Response:', response);\n      if (response.success) {\n        console.log('[Submit Step 2] Success! Setting timeout for navigation.');\n        setSuccess(isEditing ? 'Template updated successfully' : 'Template created successfully');\n        \n        // Use window.location for a full page refresh to avoid navigation issues\n        setTimeout(() => {\n          console.log('[Submit Step 2] Navigating back to templates');\n          window.location.href = '/email-templates'; // Update to match the route in App.tsx\n        }, 1500);\n      } else {\n        console.error('[Submit Step 2] API call not successful:', response.message);\n        setError(response.message || (isEditing ? 'Failed to update template' : 'Failed to create template'));\n      }\n    } catch (err: any) {\n      console.error('[Submit Step 2] Error during API call:', err);\n      \n      // Provide more detailed error message based on status code\n      if (err.response) {\n        if (err.response.status === 404) {\n          setError('API endpoint not found. Please check server configuration.');\n        } else if (err.response.status === 413) {\n          setError('Template content is too large. Try simplifying your design.');\n        } else if (err.response.status === 400) {\n          setError(err.response.data?.message || 'Invalid template data. Please check your inputs.');\n        } else if (err.response.status >= 500) {\n          setError('Server error. Please try again later or contact support.');\n        } else {\n          setError(err.response.data?.message || err.message || 'An error occurred while saving the template.');\n        }\n      } else if (err.request) {\n        setError('No response from server. Check your internet connection and try again.');\n      } else {\n        setError(err.message || 'An unexpected error occurred');\n      }\n    } finally {\n      console.log('[Submit Step 2] Setting loading to false.');\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    if (step === 2) {\n      setStep(1);\n    } else {\n      navigate('/email-templates');\n    }\n  };\n\n  // Get AI suggestions for the selected element\n  const getSuggestions = async (content: string, type: 'headline' | 'body' | 'cta') => {\n    try {\n      setLoadingSuggestions(true);\n      setSuggestions([]);\n\n      const response = await api.post('/ai-templates/suggest',\n        { content, suggestionType: type },\n        {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        }\n      );\n\n      if (response.data.success) {\n        setSuggestions(response.data.data.suggestions);\n      } else {\n        console.error('Failed to get suggestions:', response.data.message);\n      }\n    } catch (error) {\n      console.error('Error getting suggestions:', error);\n    } finally {\n      setLoadingSuggestions(false);\n    }\n  };\n\n  if (loading && step === 1) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-800\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col h-screen\">\n      {/* Optional Header for Template Name/Actions */}\n      <div className=\"flex justify-between items-center p-4 bg-gray-800 text-white border-b border-gray-700\">\n        <h1 className=\"text-xl font-semibold\">\n          {isEditing ? `Editing: ${name}` : 'Create New Template'}\n        </h1>\n        <div className=\"flex gap-2\">\n           <Button\n              type=\"button\"\n              onClick={handleBack}\n              variant=\"secondary\"\n              size=\"sm\"\n            >\n              {step === 1 ? 'Back to Templates' : 'Back to Details'}\n            </Button>\n            <Button\n              type=\"button\"\n              onClick={handleSubmit}\n              variant=\"primary\"\n              size=\"sm\"\n              disabled={loading}\n            >\n              {loading ? 'Saving...' : (step === 1 ? 'Next: Design' : (isEditing ? 'Update & Close' : 'Save & Close'))}\n            </Button>\n        </div>\n      </div>\n\n      {/* Main Editor Area */}\n      <div className=\"flex flex-grow overflow-hidden\">\n        {step === 1 ? (\n          // Step 1 Form (centered or styled differently)\n          <div className=\"flex-grow p-6 bg-gray-900 overflow-y-auto\">\n            <div className=\"max-w-2xl mx-auto bg-gray-800 rounded-lg p-8\">\n              {error && <Alert type=\"error\" message={error} className=\"mb-6\" />}\n              {success && <Alert type=\"success\" message={success} className=\"mb-6\" />}\n              <form onSubmit={handleSubmit}>\n                 <div className=\"mb-6\">\n                   <Input id=\"templateName\" name=\"templateName\" label=\"Template Name\" type=\"text\" value={name} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setName(e.target.value)} required placeholder=\"Enter template name\" />\n                 </div>\n                 <div className=\"mb-6\">\n                   <label className=\"block text-white mb-2\">Description</label>\n                   <textarea value={description} onChange={(e) => setDescription(e.target.value)} required placeholder=\"Enter template description\" className=\"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800 resize-none h-32\" />\n                 </div>\n                 <div className=\"mb-6\">\n                   <label className=\"block text-white mb-2\">Category</label>\n                   <select value={category} onChange={(e) => setCategory(e.target.value)} className=\"w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-800\">\n                     {/* Categories must match the backend schema enum values */}\n                     <option value=\"Marketing\">Marketing</option>\n                     <option value=\"Transactional\">Transactional</option>\n                     <option value=\"Newsletter\">Newsletter</option>\n                     <option value=\"Event\">Event</option>\n                     <option value=\"Onboarding\">Onboarding</option>\n                     <option value=\"Follow-up\">Follow-up</option>\n                     <option value=\"Promotional\">Promotional</option>\n                     <option value=\"Other\">Other</option>\n                   </select>\n                 </div>\n                 <div className=\"mb-6\">\n                   <Input id=\"thumbnailUrl\" name=\"thumbnailUrl\" label=\"Thumbnail URL (optional)\" type=\"text\" value={thumbnail} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setThumbnail(e.target.value)} placeholder=\"Enter thumbnail URL or leave blank\" />\n                   {thumbnail && <div className=\"mt-2\"><img src={thumbnail} alt=\"Thumbnail preview\" className=\"h-32 object-cover rounded-lg\" /></div>}\n                 </div>\n                <div className=\"flex justify-end\">\n                   <Button type=\"submit\" variant=\"primary\" size=\"md\">Next: Design Template</Button>\n                 </div>\n              </form>\n            </div>\n          </div>\n        ) : (\n          // Step 2 Editor Layout\n          <div className=\"flex flex-grow h-full\">\n            {/* Left Panel for Tools */}\n            <div className=\"w-64 bg-gray-800 border-r border-gray-700 overflow-y-auto flex flex-col\">\n              <div className=\"p-2 text-white text-center font-semibold border-b border-gray-700\">Template Options</div>\n              <div className=\"p-4 space-y-4\">\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-300 mb-2\">AI Suggestions</h3>\n                  <Button \n                    variant=\"secondary\" \n                    size=\"sm\" \n                    className=\"w-full\"\n                    onClick={() => {\n                      setSuggestionType('headline');\n                      setShowAiSuggestions(true);\n                      getSuggestions('Headline suggestion for email', 'headline');\n                    }}\n                  >\n                    Get Headline Ideas\n                  </Button>\n                  <Button \n                    variant=\"secondary\" \n                    size=\"sm\" \n                    className=\"w-full mt-2\"\n                    onClick={() => {\n                      setSuggestionType('body');\n                      setShowAiSuggestions(true);\n                      getSuggestions('Body text suggestion for email', 'body');\n                    }}\n                  >\n                    Get Body Text Ideas\n                  </Button>\n                  <Button \n                    variant=\"secondary\" \n                    size=\"sm\" \n                    className=\"w-full mt-2\"\n                    onClick={() => {\n                      setSuggestionType('cta');\n                      setShowAiSuggestions(true);\n                      getSuggestions('Call to action suggestion', 'cta');\n                    }}\n                  >\n                    Get CTA Ideas\n                  </Button>\n                </div>\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-300 mb-2\">Help</h3>\n                  <p className=\"text-xs text-gray-400\">Use the MJML editor to create responsive emails. Customize your template using the available blocks and settings.</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Center Panel (MjmlEditor) */}\n            <div className=\"flex-grow flex flex-col bg-gray-700\">\n              <HtmlEmailEditor\n                initialHtml={content}\n                onSave={handleHtmlSave}\n                height=\"calc(100vh - 200px)\"\n              />\n\n              {/* AI Suggestion Panel (conditionally rendered) */}\n              {showAiSuggestions && (\n                <div className=\"absolute right-80 top-40 w-80 z-50\">\n                  <AISuggestionPanel\n                    type={suggestionType}\n                    suggestions={suggestions}\n                    isLoading={loadingSuggestions}\n                    onApply={(suggestion) => {\n                      // Note: This needs custom handling with MjmlEditor\n                      // Likely requires interacting with the GrapesJS editor instance via a ref\n                      console.log('Selected suggestion:', suggestion);\n                      alert('Applying AI suggestions directly into the MJML editor is not yet implemented.'); // Placeholder\n                      setShowAiSuggestions(false);\n                    }}\n                    onClose={() => setShowAiSuggestions(false)}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TemplateForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IACVC,SAAS,EACTC,QAAQ,QACH,OAAO;AAEd,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SACEC,WAAW,EACXC,SAAS,QACJ,kBAAkB;AACzB;AACA,SAASC,6BAA6B,QAAQ,UAAU;AACxD,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES;EAAW,CAAC,GAAGR,SAAS,CAAyB,CAAC;EAC1D,MAAMS,SAAS,GAAGC,OAAO,CAACF,UAAU,CAAC;EACrC,MAAM,CAACG,IAAI,EAAEC,OAAO,CAAC,GAAGnB,QAAQ,CAAS,EAAE,CAAC;EAC5C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAS,OAAO,CAAC;EACzD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAS,EAAE,CAAC;EAClD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAS,EAAE,CAAC;EAC1D,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;EACtD,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAU,KAAK,CAAC;EACtD,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAgB,IAAI,CAAC;EAC3D,MAAM,CAACoC,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAS,CAAC,CAAC;;EAE3C;EACA,MAAM,CAACsC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvC,QAAQ,CAAU,KAAK,CAAC;EAC1E,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAA8B,UAAU,CAAC;EAC7F,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAW,EAAE,CAAC;EAC5D,MAAM,CAAC4C,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7C,QAAQ,CAAU,KAAK,CAAC;EAE5ED,SAAS,CAAC,MAAM;IACd,IAAIiB,SAAS,IAAID,UAAU,EAAE;MAC3B+B,aAAa,CAAC/B,UAAU,CAAC;IAC3B;EACF,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAEhB,MAAM+B,aAAa,GAAG,MAAO/B,UAAkB,IAAK;IAClDgB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMgB,QAAQ,GAAG,MAAMvC,6BAA6B,CAACwC,eAAe,CAACjC,UAAU,CAAC;MAChF,IAAIgC,QAAQ,CAACb,OAAO,IAAIa,QAAQ,CAACE,QAAQ,EAAE;QACzC;QACA,MAAMA,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;QAClC9B,OAAO,CAAC8B,QAAQ,CAAC/B,IAAI,CAAC;QACtBG,cAAc,CAAC4B,QAAQ,CAAC7B,WAAW,CAAC;QACpCG,WAAW,CAAC0B,QAAQ,CAAC3B,QAAQ,CAAC;QAC9BG,UAAU,CAACwB,QAAQ,CAACzB,OAAO,CAAC,CAAC,CAAC;QAC9BK,YAAY,CAACoB,QAAQ,CAACrB,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC;;QAExC;QACA,IAAIqB,QAAQ,CAACvB,WAAW,EAAE;UACxBC,cAAc,CAACsB,QAAQ,CAACvB,WAAW,CAAC;QACtC;MACF,CAAC,MAAM;QACLO,QAAQ,CAACc,QAAQ,CAACG,OAAO,IAAI,0BAA0B,CAAC;MAC1D;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACjBpB,QAAQ,CAAC,EAAAmB,aAAA,GAAAD,GAAG,CAACJ,QAAQ,cAAAK,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcE,IAAI,cAAAD,kBAAA,uBAAlBA,kBAAA,CAAoBH,OAAO,KAAIC,GAAG,CAACD,OAAO,IAAI,0BAA0B,CAAC;IACpF,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwB,cAAc,GAAIC,IAAY,IAAK;IACvCC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEF,IAAI,GAAG,GAAGA,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAAG,SAAS,CAAC;IACrF;IACAlC,UAAU,CAAC+B,IAAI,CAAC;IAChB7B,cAAc,CAAC6B,IAAI,CAAC;IACpBI,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,CAAmB,IAAK;IAClDA,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEC,cAAc,CAAC,CAAC;IAEnB,IAAI3B,IAAI,KAAK,CAAC,EAAE;MACd;MACA,IAAI,CAAClB,IAAI,IAAI,CAACE,WAAW,IAAI,CAACE,QAAQ,EAAE;QACtCW,QAAQ,CAAC,oCAAoC,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI,CAACL,SAAS,EAAE;QACdC,YAAY,CAAC,0DAA0DmC,kBAAkB,CAAC9C,IAAI,CAAC,EAAE,CAAC;MACpG;MAEAmB,OAAO,CAAC,CAAC,CAAC;MACV;IACF;;IAEA;IACAoB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;IAE1C;IACA;IACA,IAAI,CAAClC,OAAO,EAAE;MACZS,QAAQ,CAAC,+CAA+C,CAAC;MACzD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdwB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IAEpD,IAAI;MACF,MAAMO,YAAY,GAAG;QACnB/C,IAAI;QACJE,WAAW;QACXE,QAAQ;QACRE,OAAO;QAAE;QACTI,SAAS,EAAEA,SAAS,IAAI,0DAA0DoC,kBAAkB,CAAC9C,IAAI,CAAC,EAAE;QAC5GQ,WAAW,EAAEA,WAAW,CAAE;MAC5B,CAAC;MAED+B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEQ,IAAI,CAACC,SAAS,CAACF,YAAY,CAAC,CAACG,MAAM,EAAE,OAAO,CAAC;MAEhG,IAAIrB,QAAQ;MAEZ,IAAI/B,SAAS,IAAID,UAAU,EAAE;QAC3B0C,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE3C,UAAU,CAAC;QAChEgC,QAAQ,GAAG,MAAMvC,6BAA6B,CAAC6D,cAAc,CAACtD,UAAU,EAAEkD,YAAY,CAAC;MACzF,CAAC,MAAM;QACLR,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;QACpDX,QAAQ,GAAG,MAAMvC,6BAA6B,CAAC8D,cAAc,CAACL,YAAY,CAAC;MAC7E;MAEAR,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEX,QAAQ,CAAC;MACtD,IAAIA,QAAQ,CAACb,OAAO,EAAE;QACpBuB,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;QACvEvB,UAAU,CAACnB,SAAS,GAAG,+BAA+B,GAAG,+BAA+B,CAAC;;QAEzF;QACAuD,UAAU,CAAC,MAAM;UACfd,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;UAC3Dc,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,kBAAkB,CAAC,CAAC;QAC7C,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,MAAM;QACLjB,OAAO,CAACzB,KAAK,CAAC,0CAA0C,EAAEe,QAAQ,CAACG,OAAO,CAAC;QAC3EjB,QAAQ,CAACc,QAAQ,CAACG,OAAO,KAAKlC,SAAS,GAAG,2BAA2B,GAAG,2BAA2B,CAAC,CAAC;MACvG;IACF,CAAC,CAAC,OAAOmC,GAAQ,EAAE;MACjBM,OAAO,CAACzB,KAAK,CAAC,wCAAwC,EAAEmB,GAAG,CAAC;;MAE5D;MACA,IAAIA,GAAG,CAACJ,QAAQ,EAAE;QAChB,IAAII,GAAG,CAACJ,QAAQ,CAAC4B,MAAM,KAAK,GAAG,EAAE;UAC/B1C,QAAQ,CAAC,4DAA4D,CAAC;QACxE,CAAC,MAAM,IAAIkB,GAAG,CAACJ,QAAQ,CAAC4B,MAAM,KAAK,GAAG,EAAE;UACtC1C,QAAQ,CAAC,6DAA6D,CAAC;QACzE,CAAC,MAAM,IAAIkB,GAAG,CAACJ,QAAQ,CAAC4B,MAAM,KAAK,GAAG,EAAE;UAAA,IAAAC,mBAAA;UACtC3C,QAAQ,CAAC,EAAA2C,mBAAA,GAAAzB,GAAG,CAACJ,QAAQ,CAACO,IAAI,cAAAsB,mBAAA,uBAAjBA,mBAAA,CAAmB1B,OAAO,KAAI,kDAAkD,CAAC;QAC5F,CAAC,MAAM,IAAIC,GAAG,CAACJ,QAAQ,CAAC4B,MAAM,IAAI,GAAG,EAAE;UACrC1C,QAAQ,CAAC,0DAA0D,CAAC;QACtE,CAAC,MAAM;UAAA,IAAA4C,mBAAA;UACL5C,QAAQ,CAAC,EAAA4C,mBAAA,GAAA1B,GAAG,CAACJ,QAAQ,CAACO,IAAI,cAAAuB,mBAAA,uBAAjBA,mBAAA,CAAmB3B,OAAO,KAAIC,GAAG,CAACD,OAAO,IAAI,8CAA8C,CAAC;QACvG;MACF,CAAC,MAAM,IAAIC,GAAG,CAAC2B,OAAO,EAAE;QACtB7C,QAAQ,CAAC,wEAAwE,CAAC;MACpF,CAAC,MAAM;QACLA,QAAQ,CAACkB,GAAG,CAACD,OAAO,IAAI,8BAA8B,CAAC;MACzD;IACF,CAAC,SAAS;MACRO,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgD,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAI3C,IAAI,KAAK,CAAC,EAAE;MACdC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC,MAAM;MACLvB,QAAQ,CAAC,kBAAkB,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMkE,cAAc,GAAG,MAAAA,CAAOxD,OAAe,EAAEyD,IAAiC,KAAK;IACnF,IAAI;MACFpC,qBAAqB,CAAC,IAAI,CAAC;MAC3BF,cAAc,CAAC,EAAE,CAAC;MAElB,MAAMI,QAAQ,GAAG,MAAMtC,GAAG,CAACyE,IAAI,CAAC,uBAAuB,EACrD;QAAE1D,OAAO;QAAEgB,cAAc,EAAEyC;MAAK,CAAC,EACjC;QACEE,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CACF,CAAC;MAED,IAAItC,QAAQ,CAACO,IAAI,CAACpB,OAAO,EAAE;QACzBS,cAAc,CAACI,QAAQ,CAACO,IAAI,CAACA,IAAI,CAACZ,WAAW,CAAC;MAChD,CAAC,MAAM;QACLe,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEe,QAAQ,CAACO,IAAI,CAACJ,OAAO,CAAC;MACpE;IACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRa,qBAAqB,CAAC,KAAK,CAAC;IAC9B;EACF,CAAC;EAED,IAAIf,OAAO,IAAIM,IAAI,KAAK,CAAC,EAAE;IACzB,oBACEzB,OAAA;MAAK2E,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpD5E,OAAA;QAAK2E,SAAS,EAAC;MAA2E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9F,CAAC;EAEV;EAEA,oBACEhF,OAAA;IAAK2E,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAErC5E,OAAA;MAAK2E,SAAS,EAAC,uFAAuF;MAAAC,QAAA,gBACpG5E,OAAA;QAAI2E,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EAClCvE,SAAS,GAAG,YAAYE,IAAI,EAAE,GAAG;MAAqB;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC,eACLhF,OAAA;QAAK2E,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACxB5E,OAAA,CAACR,MAAM;UACJ8E,IAAI,EAAC,QAAQ;UACbW,OAAO,EAAEb,UAAW;UACpBc,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,IAAI;UAAAP,QAAA,EAERnD,IAAI,KAAK,CAAC,GAAG,mBAAmB,GAAG;QAAiB;UAAAoD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACThF,OAAA,CAACR,MAAM;UACL8E,IAAI,EAAC,QAAQ;UACbW,OAAO,EAAE/B,YAAa;UACtBgC,OAAO,EAAC,SAAS;UACjBC,IAAI,EAAC,IAAI;UACTC,QAAQ,EAAEjE,OAAQ;UAAAyD,QAAA,EAEjBzD,OAAO,GAAG,WAAW,GAAIM,IAAI,KAAK,CAAC,GAAG,cAAc,GAAIpB,SAAS,GAAG,gBAAgB,GAAG;QAAgB;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA;MAAK2E,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAC5CnD,IAAI,KAAK,CAAC;MAAA;MACT;MACAzB,OAAA;QAAK2E,SAAS,EAAC,2CAA2C;QAAAC,QAAA,eACxD5E,OAAA;UAAK2E,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAC1DvD,KAAK,iBAAIrB,OAAA,CAACT,KAAK;YAAC+E,IAAI,EAAC,OAAO;YAAC/B,OAAO,EAAElB,KAAM;YAACsD,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAChEzD,OAAO,iBAAIvB,OAAA,CAACT,KAAK;YAAC+E,IAAI,EAAC,SAAS;YAAC/B,OAAO,EAAEhB,OAAQ;YAACoD,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvEhF,OAAA;YAAMqF,QAAQ,EAAEnC,YAAa;YAAA0B,QAAA,gBAC1B5E,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB5E,OAAA,CAACN,KAAK;gBAAC4F,EAAE,EAAC,cAAc;gBAAC/E,IAAI,EAAC,cAAc;gBAACgF,KAAK,EAAC,eAAe;gBAACjB,IAAI,EAAC,MAAM;gBAACkB,KAAK,EAAEjF,IAAK;gBAACkF,QAAQ,EAAGtC,CAAsC,IAAK3C,OAAO,CAAC2C,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;gBAACG,QAAQ;gBAACC,WAAW,EAAC;cAAqB;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrN,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5E,OAAA;gBAAO2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5DhF,OAAA;gBAAUwF,KAAK,EAAE/E,WAAY;gBAACgF,QAAQ,EAAGtC,CAAC,IAAKzC,cAAc,CAACyC,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;gBAACG,QAAQ;gBAACC,WAAW,EAAC,4BAA4B;gBAACjB,SAAS,EAAC;cAAyH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpQ,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5E,OAAA;gBAAO2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDhF,OAAA;gBAAQwF,KAAK,EAAE7E,QAAS;gBAAC8E,QAAQ,EAAGtC,CAAC,IAAKvC,WAAW,CAACuC,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;gBAACb,SAAS,EAAC,wGAAwG;gBAAAC,QAAA,gBAEvL5E,OAAA;kBAAQwF,KAAK,EAAC,WAAW;kBAAAZ,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5ChF,OAAA;kBAAQwF,KAAK,EAAC,eAAe;kBAAAZ,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDhF,OAAA;kBAAQwF,KAAK,EAAC,YAAY;kBAAAZ,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9ChF,OAAA;kBAAQwF,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpChF,OAAA;kBAAQwF,KAAK,EAAC,YAAY;kBAAAZ,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9ChF,OAAA;kBAAQwF,KAAK,EAAC,WAAW;kBAAAZ,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5ChF,OAAA;kBAAQwF,KAAK,EAAC,aAAa;kBAAAZ,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDhF,OAAA;kBAAQwF,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB5E,OAAA,CAACN,KAAK;gBAAC4F,EAAE,EAAC,cAAc;gBAAC/E,IAAI,EAAC,cAAc;gBAACgF,KAAK,EAAC,0BAA0B;gBAACjB,IAAI,EAAC,MAAM;gBAACkB,KAAK,EAAEvE,SAAU;gBAACwE,QAAQ,EAAGtC,CAAsC,IAAKjC,YAAY,CAACiC,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;gBAACI,WAAW,EAAC;cAAoC;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAClP/D,SAAS,iBAAIjB,OAAA;gBAAK2E,SAAS,EAAC,MAAM;gBAAAC,QAAA,eAAC5E,OAAA;kBAAK6F,GAAG,EAAE5E,SAAU;kBAAC6E,GAAG,EAAC,mBAAmB;kBAACnB,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/H,CAAC,eACPhF,OAAA;cAAK2E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC9B5E,OAAA,CAACR,MAAM;gBAAC8E,IAAI,EAAC,QAAQ;gBAACY,OAAO,EAAC,SAAS;gBAACC,IAAI,EAAC,IAAI;gBAAAP,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACAhF,OAAA;QAAK2E,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAEpC5E,OAAA;UAAK2E,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtF5E,OAAA;YAAK2E,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzGhF,OAAA;YAAK2E,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B5E,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAI2E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1EhF,OAAA,CAACR,MAAM;gBACL0F,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,IAAI;gBACTR,SAAS,EAAC,QAAQ;gBAClBM,OAAO,EAAEA,CAAA,KAAM;kBACbnD,iBAAiB,CAAC,UAAU,CAAC;kBAC7BF,oBAAoB,CAAC,IAAI,CAAC;kBAC1ByC,cAAc,CAAC,+BAA+B,EAAE,UAAU,CAAC;gBAC7D,CAAE;gBAAAO,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThF,OAAA,CAACR,MAAM;gBACL0F,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,IAAI;gBACTR,SAAS,EAAC,aAAa;gBACvBM,OAAO,EAAEA,CAAA,KAAM;kBACbnD,iBAAiB,CAAC,MAAM,CAAC;kBACzBF,oBAAoB,CAAC,IAAI,CAAC;kBAC1ByC,cAAc,CAAC,gCAAgC,EAAE,MAAM,CAAC;gBAC1D,CAAE;gBAAAO,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACThF,OAAA,CAACR,MAAM;gBACL0F,OAAO,EAAC,WAAW;gBACnBC,IAAI,EAAC,IAAI;gBACTR,SAAS,EAAC,aAAa;gBACvBM,OAAO,EAAEA,CAAA,KAAM;kBACbnD,iBAAiB,CAAC,KAAK,CAAC;kBACxBF,oBAAoB,CAAC,IAAI,CAAC;kBAC1ByC,cAAc,CAAC,2BAA2B,EAAE,KAAK,CAAC;gBACpD,CAAE;gBAAAO,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNhF,OAAA;cAAA4E,QAAA,gBACE5E,OAAA;gBAAI2E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEhF,OAAA;gBAAG2E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAiH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhF,OAAA;UAAK2E,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClD5E,OAAA,CAACP,eAAe;YACdsG,WAAW,EAAElF,OAAQ;YACrBmF,MAAM,EAAEpD,cAAe;YACvBqD,MAAM,EAAC;UAAqB;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,EAGDrD,iBAAiB,iBAChB3B,OAAA;YAAK2E,SAAS,EAAC,oCAAoC;YAAAC,QAAA,eACjD5E,OAAA,CAACV,iBAAiB;cAChBgF,IAAI,EAAEzC,cAAe;cACrBE,WAAW,EAAEA,WAAY;cACzBmE,SAAS,EAAEjE,kBAAmB;cAC9BkE,OAAO,EAAGC,UAAU,IAAK;gBACvB;gBACA;gBACAtD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqD,UAAU,CAAC;gBAC/CC,KAAK,CAAC,+EAA+E,CAAC,CAAC,CAAC;gBACxFzE,oBAAoB,CAAC,KAAK,CAAC;cAC7B,CAAE;cACF0E,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAAC,KAAK;YAAE;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9E,EAAA,CApWID,YAAsB;EAAA,QACTN,WAAW,EACLC,SAAS;AAAA;AAAA2G,EAAA,GAF5BtG,YAAsB;AAsW5B,eAAeA,YAAY;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}