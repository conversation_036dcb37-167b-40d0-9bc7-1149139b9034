"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[970],{7589:(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var r=s(5043),a=s(9291),i=s(8417),l=s(1411),n=s(9579),o=s(579);const c=()=>{const[e,t]=(0,r.useState)(!1),[s,c]=(0,r.useState)(!0),[d,x]=(0,r.useState)(null),[m,h]=(0,r.useState)(""),[p,y]=(0,r.useState)(null),[u,g]=(0,r.useState)("iphone"),[b,v]=(0,r.useState)([]);(0,r.useEffect)((()=>{(async()=>{c(!0),x(null);try{const e=await n.gx.getPreviewHistory();e.success?v(e.data):x("Failed to load preview history")}catch(e){x(e.message||"Error fetching preview history"),console.error("Error fetching preview history:",e)}finally{c(!1)}})()}),[]);return(0,o.jsxs)("div",{className:"container mx-auto px-4 py-6",children:[(0,o.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,o.jsx)("h1",{className:"text-2xl font-semibold text-text-primary",children:"Mobile Preview"})}),(0,o.jsx)("p",{className:"text-text-secondary mb-6",children:"Preview how your emails will look on different mobile devices before sending."}),d&&(0,o.jsx)(a.A,{type:"error",message:d,onClose:()=>x(null),className:"mb-6"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,o.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,o.jsxs)(l.A,{children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Content & Options"}),(0,o.jsxs)("div",{className:"p-4 space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"emailContent",className:"sr-only",children:"Email HTML Content"}),(0,o.jsx)("textarea",{id:"emailContent",name:"emailContent",rows:15,value:m,onChange:e=>h(e.target.value),placeholder:"Paste your email HTML content here or load a template...",className:"block w-full bg-secondary-bg border border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-text-primary placeholder-gray-500 font-mono text-xs"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-text-secondary mb-2",children:"Device Type"}),(0,o.jsx)("div",{className:"flex items-center space-x-4 bg-secondary-bg p-2 rounded-md border border-gray-700",children:["iphone","android","tablet"].map((e=>(0,o.jsxs)("label",{htmlFor:e,className:"flex items-center justify-center flex-1 px-3 py-1 rounded-md text-sm cursor-pointer transition-colors "+(u===e?"bg-primary text-white":"text-text-secondary hover:bg-gray-700"),children:[(0,o.jsx)("input",{id:e,name:"deviceType",type:"radio",checked:u===e,onChange:()=>g(e),className:"sr-only"}),(0,o.jsx)("span",{className:"capitalize",children:e})]},e)))})]}),(0,o.jsxs)("div",{className:"flex space-x-3 pt-2",children:[(0,o.jsx)(i.A,{onClick:async()=>{if(m.trim()){t(!0),x(null),y(null);try{const e=await n.gx.generatePreview(m,u);e.success?(y(e.data.previewUrl),b.some((t=>t.id===e.data.id))||v((t=>[e.data,...t]))):x(e.message||"Failed to generate preview")}catch(e){x(e.message||"An error occurred while generating preview")}finally{t(!1)}}else x("Email content is required")},disabled:e||!m.trim(),className:"flex-1",children:e?"Generating...":"Generate Preview"}),(0,o.jsx)(i.A,{onClick:async()=>{if(!m.trim())return void x("Email content is required to send a test");const e=prompt("Enter email address to send test to:");if(e&&/\S+@\S+\.\S+/.test(e)){t(!0),x(null);try{const t=await n.gx.sendTestEmail(e,m);t.success?alert(`Test email sent successfully to ${e}`):x(t.message||"Failed to send test email")}catch(s){x(s.message||"An error occurred while sending test email")}finally{t(!1)}}else alert("Invalid email address provided.")},disabled:e||!m.trim(),variant:"secondary",className:"flex-1",children:"Send Test Email"})]})]})]}),(0,o.jsxs)(l.A,{children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:"Preview History"}),s?(0,o.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"Loading history..."}):0===b.length?(0,o.jsx)("div",{className:"p-4 text-center text-text-secondary",children:"No preview history yet."}):(0,o.jsx)("ul",{className:"divide-y divide-gray-700 max-h-60 overflow-y-auto",children:b.map((e=>(0,o.jsx)("li",{className:"p-3 hover:bg-gray-700 cursor-pointer flex justify-between items-center",onClick:()=>{return h((t=e).content),y(t.previewUrl),void g(t.deviceType);var t},children:(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-xs text-text-secondary capitalize",children:e.deviceType}),(0,o.jsx)("p",{className:"text-xs text-text-secondary",children:new Date(e.createdAt).toLocaleString()})]})},e.id)))})]})]}),(0,o.jsxs)(l.A,{className:"lg:col-span-2",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold text-text-primary p-4 border-b border-gray-700",children:["Preview (",u,")"]}),(0,o.jsxs)("div",{className:"p-4 flex items-center justify-center bg-gray-800 min-h-[600px]",children:[" ",p?(0,o.jsx)("iframe",{style:{width:"tablet"===u?"500px":"375px",height:"tablet"===u?"700px":"667px",border:"10px solid #333",borderRadius:"20px",backgroundColor:"white"},src:p,title:`Mobile Preview - ${u}`,sandbox:"allow-scripts allow-same-origin"}):e&&!p?(0,o.jsxs)("div",{className:"text-center text-text-secondary",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"}),"Generating preview..."]}):(0,o.jsxs)("div",{className:"text-center text-text-secondary",children:[(0,o.jsxs)("p",{children:["Generate a preview to see how your email looks on a ",u,"."]}),(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-24 w-24 text-gray-600 mx-auto mt-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:1,children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})]})]})]})]})]})}}}]);
//# sourceMappingURL=970.12af5796.chunk.js.map