{"ast": null, "code": "/* eslint-disable no-restricted-globals, @typescript-eslint/ban-ts-comment, @typescript-eslint/no-unused-vars, @typescript-eslint/no-non-null-assertion */import { makeRequestCall, makeRequestCallFromTimer } from './makeRequestCall.js';\nexport class AsapQueue {\n  // Use the fastest means possible to execute a task in its own turn, with\n  // priority over other events including IO, animation, reflow, and redraw\n  // events in browsers.\n  //\n  // An exception thrown by a task will permanently interrupt the processing of\n  // subsequent tasks. The higher level `asap` function ensures that if an\n  // exception is thrown by a task, that the task queue will continue flushing as\n  // soon as possible, but if you use `rawAsap` directly, you are responsible to\n  // either ensure that no exceptions are thrown from your task, or to manually\n  // call `rawAsap.requestFlush` if an exception is thrown.\n  enqueueTask(task) {\n    const {\n      queue: q,\n      requestFlush\n    } = this;\n    if (!q.length) {\n      requestFlush();\n      this.flushing = true;\n    }\n    // Equivalent to push, but avoids a function call.\n    q[q.length] = task;\n  }\n  constructor() {\n    this.queue = [];\n    // We queue errors to ensure they are thrown in right order (FIFO).\n    // Array-as-queue is good enough here, since we are just dealing with exceptions.\n    this.pendingErrors = [];\n    // Once a flush has been requested, no further calls to `requestFlush` are\n    // necessary until the next `flush` completes.\n    // @ts-ignore\n    this.flushing = false;\n    // The position of the next task to execute in the task queue. This is\n    // preserved between calls to `flush` so that it can be resumed if\n    // a task throws an exception.\n    this.index = 0;\n    // If a task schedules additional tasks recursively, the task queue can grow\n    // unbounded. To prevent memory exhaustion, the task queue will periodically\n    // truncate already-completed tasks.\n    this.capacity = 1024;\n    // The flush function processes all tasks that have been scheduled with\n    // `rawAsap` unless and until one of those tasks throws an exception.\n    // If a task throws an exception, `flush` ensures that its state will remain\n    // consistent and will resume where it left off when called again.\n    // However, `flush` does not make any arrangements to be called again if an\n    // exception is thrown.\n    this.flush = () => {\n      const {\n        queue: q\n      } = this;\n      while (this.index < q.length) {\n        const currentIndex = this.index;\n        // Advance the index before calling the task. This ensures that we will\n        // begin flushing on the next task the task throws an error.\n        this.index++;\n        q[currentIndex].call();\n        // Prevent leaking memory for long chains of recursive calls to `asap`.\n        // If we call `asap` within tasks scheduled by `asap`, the queue will\n        // grow, but to avoid an O(n) walk for every task we execute, we don't\n        // shift tasks off the queue after they have been executed.\n        // Instead, we periodically shift 1024 tasks off the queue.\n        if (this.index > this.capacity) {\n          // Manually shift all values starting at the index back to the\n          // beginning of the queue.\n          for (let scan = 0, newLength = q.length - this.index; scan < newLength; scan++) {\n            q[scan] = q[scan + this.index];\n          }\n          q.length -= this.index;\n          this.index = 0;\n        }\n      }\n      q.length = 0;\n      this.index = 0;\n      this.flushing = false;\n    };\n    // In a web browser, exceptions are not fatal. However, to avoid\n    // slowing down the queue of pending tasks, we rethrow the error in a\n    // lower priority turn.\n    this.registerPendingError = err => {\n      this.pendingErrors.push(err);\n      this.requestErrorThrow();\n    };\n    // `requestFlush` requests that the high priority event queue be flushed as\n    // soon as possible.\n    // This is useful to prevent an error thrown in a task from stalling the event\n    // queue if the exception handled by Node.js’s\n    // `process.on(\"uncaughtException\")` or by a domain.\n    // `requestFlush` is implemented using a strategy based on data collected from\n    // every available SauceLabs Selenium web driver worker at time of writing.\n    // https://docs.google.com/spreadsheets/d/1mG-5UYGup5qxGdEMWkhP6BWCz053NUb2E1QoUTU16uA/edit#gid=783724593\n    this.requestFlush = makeRequestCall(this.flush);\n    this.requestErrorThrow = makeRequestCallFromTimer(() => {\n      // Throw first error\n      if (this.pendingErrors.length) {\n        throw this.pendingErrors.shift();\n      }\n    });\n  }\n} // The message channel technique was discovered by Malte Ubl and was the\n// original foundation for this library.\n// http://www.nonblocking.io/2011/06/windownexttick.html\n// Safari 6.0.5 (at least) intermittently fails to create message ports on a\n// page's first load. Thankfully, this version of Safari supports\n// MutationObservers, so we don't need to fall back in that case.\n// function makeRequestCallFromMessageChannel(callback) {\n//     var channel = new MessageChannel();\n//     channel.port1.onmessage = callback;\n//     return function requestCall() {\n//         channel.port2.postMessage(0);\n//     };\n// }\n// For reasons explained above, we are also unable to use `setImmediate`\n// under any circumstances.\n// Even if we were, there is another bug in Internet Explorer 10.\n// It is not sufficient to assign `setImmediate` to `requestFlush` because\n// `setImmediate` must be called *by name* and therefore must be wrapped in a\n// closure.\n// Never forget.\n// function makeRequestCallFromSetImmediate(callback) {\n//     return function requestCall() {\n//         setImmediate(callback);\n//     };\n// }\n// Safari 6.0 has a problem where timers will get lost while the user is\n// scrolling. This problem does not impact ASAP because Safari 6.0 supports\n// mutation observers, so that implementation is used instead.\n// However, if we ever elect to use timers in Safari, the prevalent work-around\n// is to add a scroll event listener that calls for a flush.\n// `setTimeout` does not call the passed callback if the delay is less than\n// approximately 7 in web workers in Firefox 8 through 18, and sometimes not\n// even then.\n// This is for `asap.js` only.\n// Its name will be periodically randomized to break any code that depends on\n// // its existence.\n// rawAsap.makeRequestCallFromTimer = makeRequestCallFromTimer\n// ASAP was originally a nextTick shim included in Q. This was factored out\n// into this ASAP package. It was later adapted to RSVP which made further\n// amendments. These decisions, particularly to marginalize MessageChannel and\n// to capture the MutationObserver implementation in a closure, were integrated\n// back into ASAP proper.\n// https://github.com/tildeio/rsvp.js/blob/cddf7232546a9cf858524b75cde6f9edf72620a7/lib/rsvp/asap.js", "map": {"version": 3, "names": ["makeRequestCall", "makeRequestCallFromTimer", "AsapQueue", "enqueueTask", "task", "queue", "q", "requestFlush", "length", "flushing", "constructor", "pendingErrors", "index", "capacity", "flush", "currentIndex", "call", "scan", "<PERSON><PERSON><PERSON><PERSON>", "registerPendingError", "err", "push", "requestErrorThrow", "shift"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\DONT DELETE\\driftly-enhanced-current-2.0.0\\frontend\\node_modules\\@react-dnd\\asap\\src\\AsapQueue.ts"], "sourcesContent": ["/* eslint-disable no-restricted-globals, @typescript-eslint/ban-ts-comment, @typescript-eslint/no-unused-vars, @typescript-eslint/no-non-null-assertion */\nimport { makeRequestCall, makeRequestCallFromTimer } from './makeRequestCall.js'\nimport type { Task } from './types.js'\n\nexport class AsapQueue {\n\tprivate queue: Task[] = []\n\t// We queue errors to ensure they are thrown in right order (FIFO).\n\t// Array-as-queue is good enough here, since we are just dealing with exceptions.\n\tprivate pendingErrors: any[] = []\n\t// Once a flush has been requested, no further calls to `requestFlush` are\n\t// necessary until the next `flush` completes.\n\t// @ts-ignore\n\tprivate flushing = false\n\t// `requestFlush` is an implementation-specific method that attempts to kick\n\t// off a `flush` event as quickly as possible. `flush` will attempt to exhaust\n\t// the event queue before yielding to the browser's own event loop.\n\tprivate requestFlush: () => void\n\n\tprivate requestErrorThrow: () => void\n\t// The position of the next task to execute in the task queue. This is\n\t// preserved between calls to `flush` so that it can be resumed if\n\t// a task throws an exception.\n\tprivate index = 0\n\t// If a task schedules additional tasks recursively, the task queue can grow\n\t// unbounded. To prevent memory exhaustion, the task queue will periodically\n\t// truncate already-completed tasks.\n\tprivate capacity = 1024\n\n\tpublic constructor() {\n\t\t// `requestFlush` requests that the high priority event queue be flushed as\n\t\t// soon as possible.\n\t\t// This is useful to prevent an error thrown in a task from stalling the event\n\t\t// queue if the exception handled by Node.js’s\n\t\t// `process.on(\"uncaughtException\")` or by a domain.\n\n\t\t// `requestFlush` is implemented using a strategy based on data collected from\n\t\t// every available SauceLabs Selenium web driver worker at time of writing.\n\t\t// https://docs.google.com/spreadsheets/d/1mG-5UYGup5qxGdEMWkhP6BWCz053NUb2E1QoUTU16uA/edit#gid=783724593\n\t\tthis.requestFlush = makeRequestCall(this.flush)\n\t\tthis.requestErrorThrow = makeRequestCallFromTimer(() => {\n\t\t\t// Throw first error\n\t\t\tif (this.pendingErrors.length) {\n\t\t\t\tthrow this.pendingErrors.shift()\n\t\t\t}\n\t\t})\n\t}\n\n\t// Use the fastest means possible to execute a task in its own turn, with\n\t// priority over other events including IO, animation, reflow, and redraw\n\t// events in browsers.\n\t//\n\t// An exception thrown by a task will permanently interrupt the processing of\n\t// subsequent tasks. The higher level `asap` function ensures that if an\n\t// exception is thrown by a task, that the task queue will continue flushing as\n\t// soon as possible, but if you use `rawAsap` directly, you are responsible to\n\t// either ensure that no exceptions are thrown from your task, or to manually\n\t// call `rawAsap.requestFlush` if an exception is thrown.\n\tpublic enqueueTask(task: Task): void {\n\t\tconst { queue: q, requestFlush } = this\n\t\tif (!q.length) {\n\t\t\trequestFlush()\n\t\t\tthis.flushing = true\n\t\t}\n\t\t// Equivalent to push, but avoids a function call.\n\t\tq[q.length] = task\n\t}\n\n\t// The flush function processes all tasks that have been scheduled with\n\t// `rawAsap` unless and until one of those tasks throws an exception.\n\t// If a task throws an exception, `flush` ensures that its state will remain\n\t// consistent and will resume where it left off when called again.\n\t// However, `flush` does not make any arrangements to be called again if an\n\t// exception is thrown.\n\tprivate flush = () => {\n\t\tconst { queue: q } = this\n\t\twhile (this.index < q.length) {\n\t\t\tconst currentIndex = this.index\n\t\t\t// Advance the index before calling the task. This ensures that we will\n\t\t\t// begin flushing on the next task the task throws an error.\n\t\t\tthis.index++\n\t\t\tq[currentIndex]!.call()\n\t\t\t// Prevent leaking memory for long chains of recursive calls to `asap`.\n\t\t\t// If we call `asap` within tasks scheduled by `asap`, the queue will\n\t\t\t// grow, but to avoid an O(n) walk for every task we execute, we don't\n\t\t\t// shift tasks off the queue after they have been executed.\n\t\t\t// Instead, we periodically shift 1024 tasks off the queue.\n\t\t\tif (this.index > this.capacity) {\n\t\t\t\t// Manually shift all values starting at the index back to the\n\t\t\t\t// beginning of the queue.\n\t\t\t\tfor (\n\t\t\t\t\tlet scan = 0, newLength = q.length - this.index;\n\t\t\t\t\tscan < newLength;\n\t\t\t\t\tscan++\n\t\t\t\t) {\n\t\t\t\t\tq[scan] = q[scan + this.index]!\n\t\t\t\t}\n\t\t\t\tq.length -= this.index\n\t\t\t\tthis.index = 0\n\t\t\t}\n\t\t}\n\t\tq.length = 0\n\t\tthis.index = 0\n\t\tthis.flushing = false\n\t}\n\n\t// In a web browser, exceptions are not fatal. However, to avoid\n\t// slowing down the queue of pending tasks, we rethrow the error in a\n\t// lower priority turn.\n\tpublic registerPendingError = (err: any) => {\n\t\tthis.pendingErrors.push(err)\n\t\tthis.requestErrorThrow()\n\t}\n}\n\n// The message channel technique was discovered by Malte Ubl and was the\n// original foundation for this library.\n// http://www.nonblocking.io/2011/06/windownexttick.html\n\n// Safari 6.0.5 (at least) intermittently fails to create message ports on a\n// page's first load. Thankfully, this version of Safari supports\n// MutationObservers, so we don't need to fall back in that case.\n\n// function makeRequestCallFromMessageChannel(callback) {\n//     var channel = new MessageChannel();\n//     channel.port1.onmessage = callback;\n//     return function requestCall() {\n//         channel.port2.postMessage(0);\n//     };\n// }\n\n// For reasons explained above, we are also unable to use `setImmediate`\n// under any circumstances.\n// Even if we were, there is another bug in Internet Explorer 10.\n// It is not sufficient to assign `setImmediate` to `requestFlush` because\n// `setImmediate` must be called *by name* and therefore must be wrapped in a\n// closure.\n// Never forget.\n\n// function makeRequestCallFromSetImmediate(callback) {\n//     return function requestCall() {\n//         setImmediate(callback);\n//     };\n// }\n\n// Safari 6.0 has a problem where timers will get lost while the user is\n// scrolling. This problem does not impact ASAP because Safari 6.0 supports\n// mutation observers, so that implementation is used instead.\n// However, if we ever elect to use timers in Safari, the prevalent work-around\n// is to add a scroll event listener that calls for a flush.\n\n// `setTimeout` does not call the passed callback if the delay is less than\n// approximately 7 in web workers in Firefox 8 through 18, and sometimes not\n// even then.\n\n// This is for `asap.js` only.\n// Its name will be periodically randomized to break any code that depends on\n// // its existence.\n// rawAsap.makeRequestCallFromTimer = makeRequestCallFromTimer\n\n// ASAP was originally a nextTick shim included in Q. This was factored out\n// into this ASAP package. It was later adapted to RSVP which made further\n// amendments. These decisions, particularly to marginalize MessageChannel and\n// to capture the MutationObserver implementation in a closure, were integrated\n// back into ASAP proper.\n// https://github.com/tildeio/rsvp.js/blob/cddf7232546a9cf858524b75cde6f9edf72620a7/lib/rsvp/asap.js\n"], "mappings": "AAAA,0JACA,SAASA,eAAe,EAAEC,wBAAwB,QAAQ,sBAAsB;AAGhF,OAAO,MAAMC,SAAS;EA2CrB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,WAAkBA,CAACC,IAAU,EAAQ;IACpC,MAAM;MAAEC,KAAK,EAAEC,CAAC;MAAEC;IAAY,CAAE,GAAG,IAAI;IACvC,IAAI,CAACD,CAAC,CAACE,MAAM,EAAE;MACdD,YAAY,EAAE;MACd,IAAI,CAACE,QAAQ,GAAG,IAAI;;IAErB;IACAH,CAAC,CAACA,CAAC,CAACE,MAAM,CAAC,GAAGJ,IAAI;;EApCnBM,YAAA,EAAqB;IAvBrB,KAAQL,KAAK,GAAW,EAAE;IAC1B;IACA;IACA,KAAQM,aAAa,GAAU,EAAE;IACjC;IACA;IACA;IACA,KAAQF,QAAQ,GAAG,KAAK;IAOxB;IACA;IACA;IACA,KAAQG,KAAK,GAAG,CAAC;IACjB;IACA;IACA;IACA,KAAQC,QAAQ,GAAG,IAAI;IAyCvB;IACA;IACA;IACA;IACA;IACA;IACA,KAAQC,KAAK,GAAG,MAAM;MACrB,MAAM;QAAET,KAAK,EAAEC;MAAC,CAAE,GAAG,IAAI;MACzB,OAAO,IAAI,CAACM,KAAK,GAAGN,CAAC,CAACE,MAAM,EAAE;QAC7B,MAAMO,YAAY,GAAG,IAAI,CAACH,KAAK;QAC/B;QACA;QACA,IAAI,CAACA,KAAK,EAAE;QACZN,CAAC,CAACS,YAAY,CAAC,CAAEC,IAAI,EAAE;QACvB;QACA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;UAC/B;UACA;UACA,KACC,IAAII,IAAI,GAAG,CAAC,EAAEC,SAAS,GAAGZ,CAAC,CAACE,MAAM,GAAG,IAAI,CAACI,KAAK,EAC/CK,IAAI,GAAGC,SAAS,EAChBD,IAAI,EAAE,EACL;YACDX,CAAC,CAACW,IAAI,CAAC,GAAGX,CAAC,CAACW,IAAI,GAAG,IAAI,CAACL,KAAK,CAAC;;UAE/BN,CAAC,CAACE,MAAM,IAAI,IAAI,CAACI,KAAK;UACtB,IAAI,CAACA,KAAK,GAAG,CAAC;;;MAGhBN,CAAC,CAACE,MAAM,GAAG,CAAC;MACZ,IAAI,CAACI,KAAK,GAAG,CAAC;MACd,IAAI,CAACH,QAAQ,GAAG,KAAK;KACrB;IAED;IACA;IACA;IACA,KAAOU,oBAAoB,GAAIC,GAAQ,IAAK;MAC3C,IAAI,CAACT,aAAa,CAACU,IAAI,CAACD,GAAG,CAAC;MAC5B,IAAI,CAACE,iBAAiB,EAAE;KACxB;IAlFA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA,IAAI,CAACf,YAAY,GAAGP,eAAe,CAAC,IAAI,CAACc,KAAK,CAAC;IAC/C,IAAI,CAACQ,iBAAiB,GAAGrB,wBAAwB,CAAC,MAAM;MACvD;MACA,IAAI,IAAI,CAACU,aAAa,CAACH,MAAM,EAAE;QAC9B,MAAM,IAAI,CAACG,aAAa,CAACY,KAAK,EAAE;;KAEjC,CAAC;;CAoEH,CAED;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}