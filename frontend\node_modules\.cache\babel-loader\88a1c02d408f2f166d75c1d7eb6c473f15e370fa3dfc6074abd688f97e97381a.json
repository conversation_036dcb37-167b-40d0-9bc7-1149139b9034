{"ast": null, "code": "/**\r\n * BlockLibrary component for Driftly Email Generator\r\n * Displays available blocks that can be dragged into the editor\r\n */import React,{useEffect,useState}from'react';import{useDrag}from'react-dnd';// import { Block } from '../types/editor'; // Adjust path as needed\n// Mock type for standalone use\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BlockLibrary=_ref=>{let{blocks=[],onAddBlock}=_ref;// Default blocks to empty array\nconst[activeCategory,setActiveCategory]=useState('all');const[searchTerm,setSearchTerm]=useState('');const[filteredBlocks,setFilteredBlocks]=useState(blocks);// Get unique categories from blocks\nconst categories=['all',...Array.from(new Set(blocks.map(block=>block.category)))];// Filter blocks when category or search term changes\nuseEffect(()=>{let filtered=blocks;// Filter by category\nif(activeCategory!=='all'){filtered=filtered.filter(block=>block.category===activeCategory);}// Filter by search term\nif(searchTerm.trim()){// Use trim to avoid filtering on whitespace only\nconst term=searchTerm.toLowerCase().trim();filtered=filtered.filter(block=>block.name&&block.name.toLowerCase().includes(term)||block.description&&block.description.toLowerCase().includes(term)||block.tags&&block.tags.some(tag=>tag.toLowerCase().includes(term)));}setFilteredBlocks(filtered);},[blocks,activeCategory,searchTerm]);return/*#__PURE__*/_jsxs(\"div\",{className:\"block-library flex flex-col h-full overflow-hidden\",children:[\" \",/*#__PURE__*/_jsxs(\"div\",{className:\"search-container p-3 border-b border-gray-200\",children:[\" \",/*#__PURE__*/_jsx(\"input\",{type:\"text\",placeholder:\"Search blocks...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),className:\"search-input w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\"// Assuming styles defined in editor.css\n})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"category-tabs flex overflow-x-auto p-2 border-b border-gray-200\",children:[\" \",categories.map(category=>/*#__PURE__*/_jsx(\"button\",{className:`category-tab px-3 py-1 text-sm font-medium rounded-md whitespace-nowrap mr-2 transition-colors ${activeCategory===category?'active bg-indigo-100 text-indigo-700':'text-gray-500 hover:text-gray-700 hover:bg-gray-100'}`// Assuming styles defined in editor.css\n,onClick:()=>setActiveCategory(category),children:category.charAt(0).toUpperCase()+category.slice(1)},category))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"blocks-grid grid grid-cols-1 gap-3 p-3 overflow-y-auto flex-1\",children:[\" \",filteredBlocks.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"no-blocks p-4 text-center text-gray-500\",children:[\" \",\"No blocks found. Try a different search or category.\"]}):filteredBlocks.map(block=>/*#__PURE__*/_jsx(LibraryBlock,{// Use blockId if available, fallback with random\nblock:block,onAddBlock:onAddBlock},block.blockId||`${block.category}-${block.type}-${Math.random()}`))]})]});};// Library Block Component\nconst LibraryBlock=_ref2=>{var _block$category;let{block,onAddBlock}=_ref2;const[{isDragging},drag]=useDrag({type:'LIBRARY_BLOCK',// Use a distinct type for library blocks\nitem:{block},// Pass the entire block data\nend:(item,monitor)=>{// This `end` callback in useDrag is often less reliable for dropping onto a canvas\n// The main logic should be in the drop target (EmailEditor canvas)\n// However, you could potentially use it for revert animations if needed.\n// const dropResult = monitor.getDropResult(); \n// if (item && dropResult) { \n//   // console.log('Dropped block:', block.name);\n//   // onAddBlock might be called here, but better handled by the drop target\n// }\n},collect:monitor=>({isDragging:monitor.isDragging()})});return/*#__PURE__*/_jsxs(\"div\",{ref:drag// Attach the drag source ref\n,className:`library-block flex flex-col bg-white border border-gray-200 rounded-md overflow-hidden cursor-grab hover:border-indigo-300 hover:shadow-sm transition-all ${isDragging?'dragging opacity-50':''}`// Assuming styles defined in editor.css\n// onClick={() => onAddBlock(block)} // Optional: Allow adding by clicking\n,title:`Drag to add ${block.name}`,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"block-preview h-24 bg-gray-100 flex items-center justify-center overflow-hidden\",children:[\" \",block.thumbnail?/*#__PURE__*/_jsx(\"img\",{src:block.thumbnail,alt:block.name,className:\"w-full h-full object-cover\"})// Assuming styles defined in editor.css\n:/*#__PURE__*/_jsx(\"div\",{className:\"block-type-icon w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center text-xl font-bold\",children:(_block$category=block.category)===null||_block$category===void 0?void 0:_block$category.charAt(0).toUpperCase()})// Added optional chaining // Assuming styles defined in editor.css\n]}),/*#__PURE__*/_jsxs(\"div\",{className:\"block-info p-3\",children:[\" \",/*#__PURE__*/_jsx(\"h4\",{className:\"block-name text-sm font-medium text-gray-900 mb-1\",children:block.name}),/*#__PURE__*/_jsx(\"p\",{className:\"block-description text-xs text-gray-500 line-clamp-2\",children:block.description}),\" \"]})]});};export default BlockLibrary;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDrag", "jsx", "_jsx", "jsxs", "_jsxs", "BlockLibrary", "_ref", "blocks", "onAddBlock", "activeCategory", "setActiveCategory", "searchTerm", "setSearchTerm", "filteredBlocks", "setFilteredBlocks", "categories", "Array", "from", "Set", "map", "block", "category", "filtered", "filter", "trim", "term", "toLowerCase", "name", "includes", "description", "tags", "some", "tag", "className", "children", "type", "placeholder", "value", "onChange", "e", "target", "onClick", "char<PERSON>t", "toUpperCase", "slice", "length", "LibraryBlock", "blockId", "Math", "random", "_ref2", "_block$category", "isDragging", "drag", "item", "end", "monitor", "collect", "ref", "title", "thumbnail", "src", "alt"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/BlockLibrary.tsx"], "sourcesContent": ["/**\r\n * BlockLibrary component for Driftly Email Generator\r\n * Displays available blocks that can be dragged into the editor\r\n */\r\n\r\nimport React, {\r\n  useEffect,\r\n  useState,\r\n} from 'react';\r\n\r\nimport { useDrag } from 'react-dnd';\r\n\r\n// import { Block } from '../types/editor'; // Adjust path as needed\r\n\r\n// Mock type for standalone use\r\ninterface Block {\r\n  blockId: string;\r\n  category: string;\r\n  type: string;\r\n  name: string;\r\n  description: string;\r\n  mjml: string;\r\n  thumbnail?: string;\r\n  tags: string[];\r\n  content?: any;\r\n  isCustom?: boolean;\r\n}\r\n\r\n\r\ninterface BlockLibraryProps {\r\n  blocks: Block[];\r\n  onAddBlock: (block: Block) => void;\r\n}\r\n\r\nconst BlockLibrary: React.FC<BlockLibraryProps> = ({ blocks = [], onAddBlock }) => { // Default blocks to empty array\r\n  const [activeCategory, setActiveCategory] = useState<string>('all');\r\n  const [searchTerm, setSearchTerm] = useState<string>('');\r\n  const [filteredBlocks, setFilteredBlocks] = useState<Block[]>(blocks);\r\n  \r\n  // Get unique categories from blocks\r\n  const categories = ['all', ...Array.from(new Set(blocks.map(block => block.category)))];\r\n  \r\n  // Filter blocks when category or search term changes\r\n  useEffect(() => {\r\n    let filtered = blocks;\r\n    \r\n    // Filter by category\r\n    if (activeCategory !== 'all') {\r\n      filtered = filtered.filter(block => block.category === activeCategory);\r\n    }\r\n    \r\n    // Filter by search term\r\n    if (searchTerm.trim()) { // Use trim to avoid filtering on whitespace only\r\n      const term = searchTerm.toLowerCase().trim();\r\n      filtered = filtered.filter(\r\n        block => \r\n          (block.name && block.name.toLowerCase().includes(term)) || \r\n          (block.description && block.description.toLowerCase().includes(term)) ||\r\n          (block.tags && block.tags.some(tag => tag.toLowerCase().includes(term)))\r\n      );\r\n    }\r\n    \r\n    setFilteredBlocks(filtered);\r\n  }, [blocks, activeCategory, searchTerm]);\r\n  \r\n  return (\r\n    <div className=\"block-library flex flex-col h-full overflow-hidden\"> {/* Assuming styles defined in editor.css */}\r\n      <div className=\"search-container p-3 border-b border-gray-200\"> {/* Assuming styles defined in editor.css */}\r\n        <input\r\n          type=\"text\"\r\n          placeholder=\"Search blocks...\"\r\n          value={searchTerm}\r\n          onChange={(e) => setSearchTerm(e.target.value)}\r\n          className=\"search-input w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500\" // Assuming styles defined in editor.css\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"category-tabs flex overflow-x-auto p-2 border-b border-gray-200\"> {/* Assuming styles defined in editor.css */}\r\n        {categories.map(category => (\r\n          <button\r\n            key={category}\r\n            className={`category-tab px-3 py-1 text-sm font-medium rounded-md whitespace-nowrap mr-2 transition-colors ${activeCategory === category ? 'active bg-indigo-100 text-indigo-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'}`} // Assuming styles defined in editor.css\r\n            onClick={() => setActiveCategory(category)}\r\n          >\r\n            {category.charAt(0).toUpperCase() + category.slice(1)}\r\n          </button>\r\n        ))}\r\n      </div>\r\n      \r\n      <div className=\"blocks-grid grid grid-cols-1 gap-3 p-3 overflow-y-auto flex-1\"> {/* Added flex-1 */ /* Assuming styles defined in editor.css */}\r\n        {filteredBlocks.length === 0 ? (\r\n          <div className=\"no-blocks p-4 text-center text-gray-500\"> {/* Assuming styles defined in editor.css */}\r\n            No blocks found. Try a different search or category.\r\n          </div>\r\n        ) : (\r\n          filteredBlocks.map(block => (\r\n            <LibraryBlock \r\n              key={block.blockId || `${block.category}-${block.type}-${Math.random()}`} // Use blockId if available, fallback with random\r\n              block={block} \r\n              onAddBlock={onAddBlock} \r\n            />\r\n          ))\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Library Block Component\r\ninterface LibraryBlockProps {\r\n  block: Block;\r\n  onAddBlock: (block: Block) => void;\r\n}\r\n\r\nconst LibraryBlock: React.FC<LibraryBlockProps> = ({ block, onAddBlock }) => {\r\n  const [{ isDragging }, drag] = useDrag({\r\n    type: 'LIBRARY_BLOCK', // Use a distinct type for library blocks\r\n    item: { block }, // Pass the entire block data\r\n    end: (item, monitor) => {\r\n      // This `end` callback in useDrag is often less reliable for dropping onto a canvas\r\n      // The main logic should be in the drop target (EmailEditor canvas)\r\n      // However, you could potentially use it for revert animations if needed.\r\n      // const dropResult = monitor.getDropResult(); \r\n      // if (item && dropResult) { \r\n      //   // console.log('Dropped block:', block.name);\r\n      //   // onAddBlock might be called here, but better handled by the drop target\r\n      // }\r\n    },\r\n    collect: (monitor) => ({\r\n      isDragging: monitor.isDragging()\r\n    })\r\n  });\r\n  \r\n  return (\r\n    <div\r\n      ref={drag} // Attach the drag source ref\r\n      className={`library-block flex flex-col bg-white border border-gray-200 rounded-md overflow-hidden cursor-grab hover:border-indigo-300 hover:shadow-sm transition-all ${isDragging ? 'dragging opacity-50' : ''}`} // Assuming styles defined in editor.css\r\n      // onClick={() => onAddBlock(block)} // Optional: Allow adding by clicking\r\n      title={`Drag to add ${block.name}`}\r\n    >\r\n      <div className=\"block-preview h-24 bg-gray-100 flex items-center justify-center overflow-hidden\"> {/* Assuming styles defined in editor.css */}\r\n        {block.thumbnail ? (\r\n          <img src={block.thumbnail} alt={block.name} className=\"w-full h-full object-cover\" /> // Assuming styles defined in editor.css\r\n        ) : (\r\n          <div className=\"block-type-icon w-12 h-12 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center text-xl font-bold\">{block.category?.charAt(0).toUpperCase()}</div> // Added optional chaining // Assuming styles defined in editor.css\r\n        )}\r\n      </div>\r\n      <div className=\"block-info p-3\"> {/* Assuming styles defined in editor.css */}\r\n        <h4 className=\"block-name text-sm font-medium text-gray-900 mb-1\">{block.name}</h4>\r\n        <p className=\"block-description text-xs text-gray-500 line-clamp-2\">{block.description}</p> {/* Added line-clamp */}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BlockLibrary; "], "mappings": "AAAA;AACA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,OAASC,OAAO,KAAQ,WAAW,CAEnC;AAEA;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAoBA,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAAiC,IAAhC,CAAEC,MAAM,CAAG,EAAE,CAAEC,UAAW,CAAC,CAAAF,IAAA,CAAO;AACnF,KAAM,CAACG,cAAc,CAAEC,iBAAiB,CAAC,CAAGX,QAAQ,CAAS,KAAK,CAAC,CACnE,KAAM,CAACY,UAAU,CAAEC,aAAa,CAAC,CAAGb,QAAQ,CAAS,EAAE,CAAC,CACxD,KAAM,CAACc,cAAc,CAAEC,iBAAiB,CAAC,CAAGf,QAAQ,CAAUQ,MAAM,CAAC,CAErE;AACA,KAAM,CAAAQ,UAAU,CAAG,CAAC,KAAK,CAAE,GAAGC,KAAK,CAACC,IAAI,CAAC,GAAI,CAAAC,GAAG,CAACX,MAAM,CAACY,GAAG,CAACC,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAEvF;AACAvB,SAAS,CAAC,IAAM,CACd,GAAI,CAAAwB,QAAQ,CAAGf,MAAM,CAErB;AACA,GAAIE,cAAc,GAAK,KAAK,CAAE,CAC5Ba,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACH,KAAK,EAAIA,KAAK,CAACC,QAAQ,GAAKZ,cAAc,CAAC,CACxE,CAEA;AACA,GAAIE,UAAU,CAACa,IAAI,CAAC,CAAC,CAAE,CAAE;AACvB,KAAM,CAAAC,IAAI,CAAGd,UAAU,CAACe,WAAW,CAAC,CAAC,CAACF,IAAI,CAAC,CAAC,CAC5CF,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CACxBH,KAAK,EACFA,KAAK,CAACO,IAAI,EAAIP,KAAK,CAACO,IAAI,CAACD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,EACrDL,KAAK,CAACS,WAAW,EAAIT,KAAK,CAACS,WAAW,CAACH,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAE,EACpEL,KAAK,CAACU,IAAI,EAAIV,KAAK,CAACU,IAAI,CAACC,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACN,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,IAAI,CAAC,CAC1E,CAAC,CACH,CAEAX,iBAAiB,CAACQ,QAAQ,CAAC,CAC7B,CAAC,CAAE,CAACf,MAAM,CAAEE,cAAc,CAAEE,UAAU,CAAC,CAAC,CAExC,mBACEP,KAAA,QAAK6B,SAAS,CAAC,oDAAoD,CAAAC,QAAA,EAAC,GAAC,cACnE9B,KAAA,QAAK6B,SAAS,CAAC,+CAA+C,CAAAC,QAAA,EAAC,GAAC,cAC9DhC,IAAA,UACEiC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAE1B,UAAW,CAClB2B,QAAQ,CAAGC,CAAC,EAAK3B,aAAa,CAAC2B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAC/CJ,SAAS,CAAC,uJAAwJ;AAAA,CACnK,CAAC,EACC,CAAC,cAEN7B,KAAA,QAAK6B,SAAS,CAAC,iEAAiE,CAAAC,QAAA,EAAC,GAAC,CAC/EnB,UAAU,CAACI,GAAG,CAACE,QAAQ,eACtBnB,IAAA,WAEE+B,SAAS,CAAE,kGAAkGxB,cAAc,GAAKY,QAAQ,CAAG,sCAAsC,CAAG,qDAAqD,EAAI;AAAA,CAC7OoB,OAAO,CAAEA,CAAA,GAAM/B,iBAAiB,CAACW,QAAQ,CAAE,CAAAa,QAAA,CAE1Cb,QAAQ,CAACqB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGtB,QAAQ,CAACuB,KAAK,CAAC,CAAC,CAAC,EAJhDvB,QAKC,CACT,CAAC,EACC,CAAC,cAENjB,KAAA,QAAK6B,SAAS,CAAC,+DAA+D,CAAAC,QAAA,EAAC,GAAC,CAC7ErB,cAAc,CAACgC,MAAM,GAAK,CAAC,cAC1BzC,KAAA,QAAK6B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,EAAC,GAAC,CAA6C,sDAEvG,EAAK,CAAC,CAENrB,cAAc,CAACM,GAAG,CAACC,KAAK,eACtBlB,IAAA,CAAC4C,YAAY,EAC+D;AAC1E1B,KAAK,CAAEA,KAAM,CACbZ,UAAU,CAAEA,UAAW,EAFlBY,KAAK,CAAC2B,OAAO,EAAI,GAAG3B,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACe,IAAI,IAAIa,IAAI,CAACC,MAAM,CAAC,CAAC,EAGvE,CACF,CACF,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AAMA,KAAM,CAAAH,YAAyC,CAAGI,KAAA,EAA2B,KAAAC,eAAA,IAA1B,CAAE/B,KAAK,CAAEZ,UAAW,CAAC,CAAA0C,KAAA,CACtE,KAAM,CAAC,CAAEE,UAAW,CAAC,CAAEC,IAAI,CAAC,CAAGrD,OAAO,CAAC,CACrCmC,IAAI,CAAE,eAAe,CAAE;AACvBmB,IAAI,CAAE,CAAElC,KAAM,CAAC,CAAE;AACjBmC,GAAG,CAAEA,CAACD,IAAI,CAAEE,OAAO,GAAK,CACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACD,CACDC,OAAO,CAAGD,OAAO,GAAM,CACrBJ,UAAU,CAAEI,OAAO,CAACJ,UAAU,CAAC,CACjC,CAAC,CACH,CAAC,CAAC,CAEF,mBACEhD,KAAA,QACEsD,GAAG,CAAEL,IAAM;AAAA,CACXpB,SAAS,CAAE,6JAA6JmB,UAAU,CAAG,qBAAqB,CAAG,EAAE,EAAI;AACnN;AAAA,CACAO,KAAK,CAAE,eAAevC,KAAK,CAACO,IAAI,EAAG,CAAAO,QAAA,eAEnC9B,KAAA,QAAK6B,SAAS,CAAC,iFAAiF,CAAAC,QAAA,EAAC,GAAC,CAC/Fd,KAAK,CAACwC,SAAS,cACd1D,IAAA,QAAK2D,GAAG,CAAEzC,KAAK,CAACwC,SAAU,CAACE,GAAG,CAAE1C,KAAK,CAACO,IAAK,CAACM,SAAS,CAAC,4BAA4B,CAAE,CAAE;AAAA,cAEtF/B,IAAA,QAAK+B,SAAS,CAAC,yHAAyH,CAAAC,QAAA,EAAAiB,eAAA,CAAE/B,KAAK,CAACC,QAAQ,UAAA8B,eAAA,iBAAdA,eAAA,CAAgBT,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAM,CAAE;AAC1L,EACE,CAAC,cACNvC,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,GAAC,cAC/BhC,IAAA,OAAI+B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAEd,KAAK,CAACO,IAAI,CAAK,CAAC,cACnFzB,IAAA,MAAG+B,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAAEd,KAAK,CAACS,WAAW,CAAI,CAAC,IAAC,EACzF,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}