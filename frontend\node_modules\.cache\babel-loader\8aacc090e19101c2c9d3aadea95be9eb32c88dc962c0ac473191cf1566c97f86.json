{"ast": null, "code": "import'grapesjs/dist/css/grapes.min.css';// Basic GrapesJS CSS\nimport React,{forwardRef,useEffect,useImperativeHandle,useRef}from'react';import grapesjs from'grapesjs';// @ts-ignore - grapesjs-mjml lacks official types\nimport grapesjsMjml from'grapesjs-mjml';// Define the Ref type for exposing editor methods\nimport{jsx as _jsx}from\"react/jsx-runtime\";const MjmlEditor=/*#__PURE__*/forwardRef((_ref,ref)=>{let{initialMjml='',initialHtml='',onSave,height='70vh'}=_ref;const editorRef=useRef(null);const grapesEditor=useRef(null);// Initialize GrapesJS Editor\nuseEffect(()=>{if(!editorRef.current)return;// Check for DOM element\n// Always destroy and recreate the editor to ensure consistent behavior\nif(grapesEditor.current){console.log(\"[MjmlEditor] Cleaning up previous editor instance\");grapesEditor.current.destroy();grapesEditor.current=null;}console.log(\"[MjmlEditor] Initializing editor with props:\",{hasMjml:!!initialMjml,mjmlLength:(initialMjml===null||initialMjml===void 0?void 0:initialMjml.length)||0,hasHtml:!!initialHtml,htmlLength:(initialHtml===null||initialHtml===void 0?void 0:initialHtml.length)||0});try{const editor=grapesjs.init({container:editorRef.current,fromElement:false,// Don't load from existing HTML/CSS in the container\nheight:String(height),width:'auto',storageManager:false,// Disable default storage manager\nplugins:[grapesjsMjml],pluginsOpts:{'grapesjs-mjml':{// MJML plugin options (optional)\n// columnsPadding: '0px',\nuseXmlParser:true,// Use the faster XML parser\nresetBlocks:false// Try keeping default GrapesJS blocks\n// ... other options\n}}// Optional: Configure panels, blocks, styles etc.\n});// Make sure the editor was initialized properly\nif(!editor){console.error(\"[MjmlEditor] Failed to initialize editor\");return;}grapesEditor.current=editor;// Register missing commands that are expected by the editor\nif(!editor.Commands.has('mjml-get-code')){console.log(\"[MjmlEditor] Registering missing mjml-get-code command\");editor.Commands.add('mjml-get-code',{run:editor=>{const mjml=editor.getHtml();// Simple implementation for missing command\nreturn{mjml:mjml,html:mjml// We'll process this later if needed\n};}});}if(!editor.Commands.has('gjs-get-html')){console.log(\"[MjmlEditor] Registering missing gjs-get-html command\");editor.Commands.add('gjs-get-html',{run:editor=>{return editor.getHtml({component:editor.getWrapper()});}});}// Use a small timeout to ensure editor is fully initialized\nsetTimeout(()=>{if(!grapesEditor.current){console.error(\"[MjmlEditor] Editor instance not available after timeout\");return;}// Verify the editor's components API is available\nif(!grapesEditor.current.setComponents){console.error(\"[MjmlEditor] Editor's setComponents method is not available\");return;}try{// Load initial content\nif(initialMjml){console.log(\"[MjmlEditor] Loading initial MJML:\",initialMjml.substring(0,100)+\"...\");try{grapesEditor.current.setComponents(initialMjml);// Use setComponents for MJML\nconsole.log(\"[MjmlEditor] Successfully loaded MJML content\");}catch(e){console.error(\"[MjmlEditor] Error loading initial MJML:\",e);// Fallback to HTML if MJML fails?\nif(initialHtml){console.log(\"[MjmlEditor] Falling back to loading initial HTML\");grapesEditor.current.setComponents(initialHtml);// Use setComponents for HTML as well\nconsole.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");}}}else if(initialHtml){console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\",initialHtml.substring(0,100)+\"...\");grapesEditor.current.setComponents(initialHtml);console.log(\"[MjmlEditor] Successfully loaded HTML content\");}else{// Load default MJML template if nothing is provided\nconsole.log(\"[MjmlEditor] No content provided, loading default template\");grapesEditor.current.setComponents(`\n                <mjml>\n                  <mj-body>\n                    <mj-section>\n                      <mj-column>\n                        <mj-text>Start designing your email!</mj-text>\n                      </mj-column>\n                    </mj-section>\n                  </mj-body>\n                </mjml>\n              `);}}catch(error){console.error(\"[MjmlEditor] Error in content loading phase:\",error);}},100);// Declare timeout variable in outer scope so it's accessible in cleanup function\nlet saveTimeout;let isSaving=false;// Flag to prevent multiple simultaneous save operations\n// Attach save listener with debounce\neditor.on('change:changesCount',()=>{if(onSave&&editor&&!isSaving){// Only proceed if not already saving\n// Clear existing timeout\nif(saveTimeout)clearTimeout(saveTimeout);// Set a new timeout\nsaveTimeout=setTimeout(()=>{// Assign to the outer scope variable\ntry{isSaving=true;// Set saving flag\n// Simplify code retrieval: Prioritize commands, fallback to getHtml()\nlet finalMjml='';let finalHtml='';try{// Try the specific mjml command first\nconst mjmlCode=editor.runCommand('mjml-get-code');if(mjmlCode&&typeof mjmlCode==='object'){// Command should return object {mjml, html}\nfinalMjml=mjmlCode.mjml||'';finalHtml=mjmlCode.html||'';console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");}}catch(cmdErr){console.warn(\"'mjml-get-code' command failed, using fallback methods:\",cmdErr);}// If command failed or didn't return expected structure, use fallbacks\nif(!finalMjml&&!finalHtml){finalMjml=editor.getHtml()||'';// Often gets MJML\ntry{// Try getting HTML via command\nfinalHtml=editor.runCommand('gjs-get-html')||'';}catch(htmlCmdErr){console.warn(\"'gjs-get-html' command failed:\",htmlCmdErr);}// As a last resort for HTML, maybe just use the component HTML (less reliable)\nif(!finalHtml){finalHtml=editor.getHtml({component:editor.getWrapper()})||'';}console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");}// Don't save if we have no content, prevents potential refresh cycles\nif(!finalMjml.trim()){console.log(\"[MjmlEditor] No MJML content to save, skipping save\");isSaving=false;return;}console.log(\"[MjmlEditor] Attempting to call onSave...\");// Call onSave as long as the editor instance exists and the prop was passed\n// Even if mjml/html strings are empty, let the parent decide what to do\nonSave(finalMjml,finalHtml);console.log(\"[MjmlEditor] onSave callback executed.\");}catch(error){console.error(\"Error during editor change listener:\",error);}finally{isSaving=false;// Reset flag whether save succeeded or failed\n}},500);// 500ms debounce\n}});// Return cleanup function\nreturn()=>{if(saveTimeout)clearTimeout(saveTimeout);// Now accessible here\nif(grapesEditor.current){// Clean up panels, commands, etc. specific to this instance if necessary\ntry{grapesEditor.current.destroy();}catch(destroyError){console.error(\"[MjmlEditor] Error during editor cleanup:\",destroyError);}grapesEditor.current=null;}};}catch(initError){console.error(\"[MjmlEditor] Critical error during editor initialization:\",initError);}},[initialMjml,initialHtml,height,onSave]);// Rerun if initial content or dimensions change\n// Expose save method via ref\nuseImperativeHandle(ref,()=>({save:async()=>{let generatedCode={mjml:'',html:''};// Initialize with empty strings\nif(grapesEditor.current){try{// Check if command exists first to avoid warnings\nconst editor=grapesEditor.current;if(editor.Commands.has('mjml-get-code')){// Try the primary command\nconst result=editor.runCommand('mjml-get-code');// Check if the command returned the expected object structure\nif(result&&typeof result==='object'&&'mjml'in result&&'html'in result){generatedCode.mjml=result.mjml||'';generatedCode.html=result.html||'';console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\",{mjml:generatedCode.mjml.substring(0,50)+'...',html:generatedCode.html.substring(0,50)+'...'});}else{console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");// Throw an error to trigger the catch block for fallback logic\nthrow new Error(\"Command returned unexpected structure\");}}else{// Command doesn't exist, go straight to fallback\nthrow new Error(\"mjml-get-code command not available\");}}catch(cmdErr){console.warn(\"mjml-get-code command failed on manual save, using fallback:\",cmdErr);try{// Fallback attempts\nconst editor=grapesEditor.current;const rawMjml=editor.getHtml()||'';let generatedHtml='';// Try gjs-get-html only if it exists\nif(editor.Commands.has('gjs-get-html')){generatedHtml=editor.runCommand('gjs-get-html')||'';}else{// Direct fallback to component HTML\ngeneratedHtml=editor.getHtml({component:editor.getWrapper()})||'';}if(rawMjml||generatedHtml){// Use if *either* fallback worked\ngeneratedCode.mjml=rawMjml;generatedCode.html=generatedHtml||rawMjml;// Use MJML as HTML if no HTML generated\nconsole.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");}else{console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");}}catch(fallbackErr){console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\",fallbackErr);}}}else{console.error(\"[MjmlEditor] Manual Save - Editor not available.\");}// Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\nawait new Promise(resolve=>setTimeout(resolve,100));// Delay 100ms\n// Re-fetch the HTML specifically after the delay, as it might have updated\nif(grapesEditor.current&&!generatedCode.html.trim()){// Only re-fetch if HTML was initially empty\nconsole.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");try{const editor=grapesEditor.current;let potentiallyUpdatedHtml='';if(editor.Commands.has('gjs-get-html')){potentiallyUpdatedHtml=editor.runCommand('gjs-get-html');}if(!potentiallyUpdatedHtml){potentiallyUpdatedHtml=editor.getHtml({component:editor.getWrapper()})||'';}if(potentiallyUpdatedHtml.trim()){console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");generatedCode.html=potentiallyUpdatedHtml;}else{console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");// If still no HTML but we have MJML, use that\nif(generatedCode.mjml&&!generatedCode.html){generatedCode.html=generatedCode.mjml;}}}catch(refetchErr){console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\",refetchErr);}}// ALWAYS return the potentially updated generatedCode object\nreturn generatedCode;},getEditor:()=>grapesEditor.current}));return/*#__PURE__*/_jsx(\"div\",{ref:editorRef,style:{height:height}});});// Assign display name for debugging\nMjmlEditor.displayName='MjmlEditor';export default MjmlEditor;", "map": {"version": 3, "names": ["React", "forwardRef", "useEffect", "useImperativeHandle", "useRef", "<PERSON><PERSON><PERSON>", "grapesjsMjml", "jsx", "_jsx", "MjmlEditor", "_ref", "ref", "initialMjml", "initialHtml", "onSave", "height", "editor<PERSON><PERSON>", "grapesEditor", "current", "console", "log", "destroy", "hasMjml", "mjm<PERSON><PERSON><PERSON><PERSON>", "length", "hasHtml", "htmlLength", "editor", "init", "container", "fromElement", "String", "width", "storageManager", "plugins", "pluginsOpts", "useXmlParser", "resetBlocks", "error", "Commands", "has", "add", "run", "mjml", "getHtml", "html", "component", "getWrapper", "setTimeout", "setComponents", "substring", "e", "saveTimeout", "isSaving", "on", "clearTimeout", "finalMjml", "finalHtml", "mjmlCode", "runCommand", "cmdErr", "warn", "htmlCmdErr", "trim", "destroyError", "initError", "save", "generatedCode", "result", "Error", "rawMjml", "generatedHtml", "fallbackErr", "Promise", "resolve", "potentiallyUpdatedHtml", "refetchErr", "getEditor", "style", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/components/MjmlEditor.tsx"], "sourcesContent": ["import 'grapesjs/dist/css/grapes.min.css'; // Basic GrapesJS CSS\r\n\r\nimport React, {\r\n  forwardRef,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n} from 'react';\r\n\r\nimport grapesjs, { Editor } from 'grapesjs';\r\n// @ts-ignore - grapesjs-mjml lacks official types\r\nimport grapesjsMjml from 'grapesjs-mjml';\r\n\r\n// Define the Ref type for exposing editor methods\r\nexport interface MjmlEditorRef {\r\n  save: () => Promise<{ mjml: string; html: string }>;\r\n  getEditor: () => Editor | null;\r\n}\r\n\r\ninterface MjmlEditorProps {\r\n  initialMjml?: string;\r\n  initialHtml?: string; // Added to potentially load HTML if MJML is missing\r\n  onSave?: (mjml: string, html: string) => void;\r\n  height?: string | number;\r\n}\r\n\r\nconst MjmlEditor = forwardRef<MjmlEditorRef, MjmlEditorProps>(\r\n  ({ initialMjml = '', initialHtml = '', onSave, height = '70vh' }, ref) => {\r\n    const editorRef = useRef<HTMLDivElement>(null);\r\n    const grapesEditor = useRef<Editor | null>(null);\r\n\r\n    // Initialize GrapesJS Editor\r\n    useEffect(() => {\r\n      if (!editorRef.current) return; // Check for DOM element\r\n      \r\n      // Always destroy and recreate the editor to ensure consistent behavior\r\n      if (grapesEditor.current) {\r\n        console.log(\"[MjmlEditor] Cleaning up previous editor instance\");\r\n        grapesEditor.current.destroy();\r\n        grapesEditor.current = null;\r\n      }\r\n\r\n      console.log(\"[MjmlEditor] Initializing editor with props:\", {\r\n        hasMjml: !!initialMjml,\r\n        mjmlLength: initialMjml?.length || 0,\r\n        hasHtml: !!initialHtml,\r\n        htmlLength: initialHtml?.length || 0\r\n      });\r\n\r\n      try {\r\n        const editor = grapesjs.init({\r\n          container: editorRef.current,\r\n          fromElement: false, // Don't load from existing HTML/CSS in the container\r\n          height: String(height),\r\n          width: 'auto',\r\n          storageManager: false, // Disable default storage manager\r\n          plugins: [grapesjsMjml],\r\n          pluginsOpts: {\r\n            'grapesjs-mjml': {\r\n              // MJML plugin options (optional)\r\n              // columnsPadding: '0px',\r\n               useXmlParser: true, // Use the faster XML parser\r\n               resetBlocks: false, // Try keeping default GrapesJS blocks\r\n               // ... other options\r\n            }\r\n          },\r\n          // Optional: Configure panels, blocks, styles etc.\r\n        });\r\n\r\n        // Make sure the editor was initialized properly\r\n        if (!editor) {\r\n          console.error(\"[MjmlEditor] Failed to initialize editor\");\r\n          return;\r\n        }\r\n\r\n        grapesEditor.current = editor;\r\n\r\n        // Register missing commands that are expected by the editor\r\n        if (!editor.Commands.has('mjml-get-code')) {\r\n          console.log(\"[MjmlEditor] Registering missing mjml-get-code command\");\r\n          editor.Commands.add('mjml-get-code', {\r\n            run: (editor) => {\r\n              const mjml = editor.getHtml();\r\n              // Simple implementation for missing command\r\n              return { \r\n                mjml: mjml,\r\n                html: mjml // We'll process this later if needed\r\n              };\r\n            }\r\n          });\r\n        }\r\n\r\n        if (!editor.Commands.has('gjs-get-html')) {\r\n          console.log(\"[MjmlEditor] Registering missing gjs-get-html command\");\r\n          editor.Commands.add('gjs-get-html', {\r\n            run: (editor) => {\r\n              return editor.getHtml({ component: editor.getWrapper() });\r\n            }\r\n          });\r\n        }\r\n\r\n        // Use a small timeout to ensure editor is fully initialized\r\n        setTimeout(() => {\r\n          if (!grapesEditor.current) {\r\n            console.error(\"[MjmlEditor] Editor instance not available after timeout\");\r\n            return;\r\n          }\r\n          \r\n          // Verify the editor's components API is available\r\n          if (!grapesEditor.current.setComponents) {\r\n            console.error(\"[MjmlEditor] Editor's setComponents method is not available\");\r\n            return;\r\n          }\r\n          \r\n          try {\r\n            // Load initial content\r\n            if (initialMjml) {\r\n              console.log(\"[MjmlEditor] Loading initial MJML:\", initialMjml.substring(0, 100) + \"...\");\r\n              try {\r\n                grapesEditor.current.setComponents(initialMjml); // Use setComponents for MJML\r\n                console.log(\"[MjmlEditor] Successfully loaded MJML content\");\r\n              } catch (e) {\r\n                console.error(\"[MjmlEditor] Error loading initial MJML:\", e);\r\n                // Fallback to HTML if MJML fails?\r\n                if (initialHtml) {\r\n                  console.log(\"[MjmlEditor] Falling back to loading initial HTML\");\r\n                  grapesEditor.current.setComponents(initialHtml); // Use setComponents for HTML as well\r\n                  console.log(\"[MjmlEditor] Successfully loaded HTML as fallback\");\r\n                }\r\n              }\r\n            } else if (initialHtml) {\r\n              console.log(\"[MjmlEditor] Loading initial HTML (MJML not provided):\", initialHtml.substring(0, 100) + \"...\");\r\n              grapesEditor.current.setComponents(initialHtml);\r\n              console.log(\"[MjmlEditor] Successfully loaded HTML content\");\r\n            } else {\r\n              // Load default MJML template if nothing is provided\r\n              console.log(\"[MjmlEditor] No content provided, loading default template\");\r\n              grapesEditor.current.setComponents(`\r\n                <mjml>\r\n                  <mj-body>\r\n                    <mj-section>\r\n                      <mj-column>\r\n                        <mj-text>Start designing your email!</mj-text>\r\n                      </mj-column>\r\n                    </mj-section>\r\n                  </mj-body>\r\n                </mjml>\r\n              `);\r\n            }\r\n          } catch (error) {\r\n            console.error(\"[MjmlEditor] Error in content loading phase:\", error);\r\n          }\r\n        }, 100);\r\n\r\n        // Declare timeout variable in outer scope so it's accessible in cleanup function\r\n        let saveTimeout: NodeJS.Timeout | undefined;\r\n        let isSaving = false; // Flag to prevent multiple simultaneous save operations\r\n        \r\n        // Attach save listener with debounce\r\n        editor.on('change:changesCount', () => {\r\n          if (onSave && editor && !isSaving) { // Only proceed if not already saving\r\n            // Clear existing timeout\r\n            if (saveTimeout) clearTimeout(saveTimeout);\r\n            \r\n            // Set a new timeout\r\n            saveTimeout = setTimeout(() => { // Assign to the outer scope variable\r\n              try {\r\n                isSaving = true; // Set saving flag\r\n                // Simplify code retrieval: Prioritize commands, fallback to getHtml()\r\n                let finalMjml = '';\r\n                let finalHtml = '';\r\n\r\n                try {\r\n                  // Try the specific mjml command first\r\n                  const mjmlCode = editor.runCommand('mjml-get-code');\r\n                  if (mjmlCode && typeof mjmlCode === 'object') { // Command should return object {mjml, html}\r\n                    finalMjml = mjmlCode.mjml || '';\r\n                    finalHtml = mjmlCode.html || '';\r\n                    console.log(\"[MjmlEditor] Got code via 'mjml-get-code' command\");\r\n                  }\r\n                } catch (cmdErr) {\r\n                   console.warn(\"'mjml-get-code' command failed, using fallback methods:\", cmdErr);\r\n                }\r\n\r\n                // If command failed or didn't return expected structure, use fallbacks\r\n                if (!finalMjml && !finalHtml) {\r\n                    finalMjml = editor.getHtml() || ''; // Often gets MJML\r\n                    try {\r\n                      // Try getting HTML via command\r\n                      finalHtml = editor.runCommand('gjs-get-html') || ''; \r\n                    } catch(htmlCmdErr) {\r\n                       console.warn(\"'gjs-get-html' command failed:\", htmlCmdErr);\r\n                    }\r\n                    // As a last resort for HTML, maybe just use the component HTML (less reliable)\r\n                    if (!finalHtml) {\r\n                      finalHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                    }\r\n                    console.log(\"[MjmlEditor] Using fallback getHtml()/gjs-get-html()\");\r\n                }\r\n                \r\n                // Don't save if we have no content, prevents potential refresh cycles\r\n                if (!finalMjml.trim()) {\r\n                  console.log(\"[MjmlEditor] No MJML content to save, skipping save\");\r\n                  isSaving = false;\r\n                  return;\r\n                }\r\n                \r\n                console.log(\"[MjmlEditor] Attempting to call onSave...\");\r\n                // Call onSave as long as the editor instance exists and the prop was passed\r\n                // Even if mjml/html strings are empty, let the parent decide what to do\r\n                onSave(finalMjml, finalHtml);\r\n                console.log(\"[MjmlEditor] onSave callback executed.\");\r\n\r\n             } catch (error) {\r\n                 console.error(\"Error during editor change listener:\", error);\r\n             } finally {\r\n                 isSaving = false; // Reset flag whether save succeeded or failed\r\n             }\r\n            }, 500); // 500ms debounce\r\n          }\r\n        });\r\n\r\n        // Return cleanup function\r\n        return () => {\r\n          if (saveTimeout) clearTimeout(saveTimeout); // Now accessible here\r\n          if (grapesEditor.current) {\r\n             // Clean up panels, commands, etc. specific to this instance if necessary\r\n             try {\r\n               grapesEditor.current.destroy();\r\n             } catch (destroyError) {\r\n               console.error(\"[MjmlEditor] Error during editor cleanup:\", destroyError);\r\n             }\r\n             grapesEditor.current = null;\r\n          }\r\n        };\r\n      } catch (initError) {\r\n        console.error(\"[MjmlEditor] Critical error during editor initialization:\", initError);\r\n      }\r\n    }, [initialMjml, initialHtml, height, onSave]); // Rerun if initial content or dimensions change\r\n\r\n    // Expose save method via ref\r\n    useImperativeHandle(ref, () => ({\r\n      save: async () => {\r\n        let generatedCode = { mjml: '', html: '' }; // Initialize with empty strings\r\n        if (grapesEditor.current) {\r\n           try {\r\n               // Check if command exists first to avoid warnings\r\n               const editor = grapesEditor.current;\r\n               if (editor.Commands.has('mjml-get-code')) {\r\n                 // Try the primary command\r\n                 const result = editor.runCommand('mjml-get-code');\r\n                 // Check if the command returned the expected object structure\r\n                 if (result && typeof result === 'object' && 'mjml' in result && 'html' in result) {\r\n                   generatedCode.mjml = result.mjml || '';\r\n                   generatedCode.html = result.html || '';\r\n                   console.log(\"[MjmlEditor] Manual Save - MJML/HTML from command:\", { mjml: generatedCode.mjml.substring(0,50)+'...', html: generatedCode.html.substring(0,50)+'...' });\r\n                 } else {\r\n                    console.warn(\"'mjml-get-code' command did not return expected object, trying fallbacks.\");\r\n                    // Throw an error to trigger the catch block for fallback logic\r\n                    throw new Error(\"Command returned unexpected structure\"); \r\n                 }\r\n               } else {\r\n                 // Command doesn't exist, go straight to fallback\r\n                 throw new Error(\"mjml-get-code command not available\");\r\n               }\r\n           } catch (cmdErr) {\r\n               console.warn(\"mjml-get-code command failed on manual save, using fallback:\", cmdErr);\r\n               try {\r\n                 // Fallback attempts\r\n                 const editor = grapesEditor.current;\r\n                 const rawMjml = editor.getHtml() || '';\r\n                 let generatedHtml = '';\r\n                 \r\n                 // Try gjs-get-html only if it exists\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   generatedHtml = editor.runCommand('gjs-get-html') || '';\r\n                 } else {\r\n                   // Direct fallback to component HTML\r\n                   generatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                  \r\n                 if (rawMjml || generatedHtml) { // Use if *either* fallback worked\r\n                     generatedCode.mjml = rawMjml;\r\n                     generatedCode.html = generatedHtml || rawMjml; // Use MJML as HTML if no HTML generated\r\n                     console.log(\"[MjmlEditor] Manual Save - Using getHtml/gjs-get-html for save.\");\r\n                 } else {\r\n                     console.error(\"[MjmlEditor] Manual Save - Fallback methods also failed to get content.\");\r\n                 }\r\n               } catch (fallbackErr) {\r\n                  console.error(\"[MjmlEditor] Manual Save - Error during fallback retrieval:\", fallbackErr);\r\n               }\r\n           }\r\n        } else {\r\n          console.error(\"[MjmlEditor] Manual Save - Editor not available.\");\r\n        }\r\n\r\n        // Add a small delay to allow potential async HTML generation within GrapesJS/plugin to settle\r\n        await new Promise(resolve => setTimeout(resolve, 100)); // Delay 100ms\r\n        \r\n        // Re-fetch the HTML specifically after the delay, as it might have updated\r\n        if (grapesEditor.current && !generatedCode.html.trim()) { // Only re-fetch if HTML was initially empty\r\n            console.log(\"[MjmlEditor] Manual Save - Re-fetching HTML after delay...\");\r\n            try {\r\n                 const editor = grapesEditor.current;\r\n                 \r\n                 let potentiallyUpdatedHtml = '';\r\n                 if (editor.Commands.has('gjs-get-html')) {\r\n                   potentiallyUpdatedHtml = editor.runCommand('gjs-get-html');\r\n                 }\r\n                 \r\n                 if (!potentiallyUpdatedHtml) {\r\n                   potentiallyUpdatedHtml = editor.getHtml({ component: editor.getWrapper() }) || '';\r\n                 }\r\n                 \r\n                 if (potentiallyUpdatedHtml.trim()) {\r\n                     console.log(\"[MjmlEditor] Manual Save - Found updated HTML after delay.\");\r\n                     generatedCode.html = potentiallyUpdatedHtml;\r\n                 } else {\r\n                    console.log(\"[MjmlEditor] Manual Save - HTML still empty after delay.\");\r\n                    // If still no HTML but we have MJML, use that\r\n                    if (generatedCode.mjml && !generatedCode.html) {\r\n                      generatedCode.html = generatedCode.mjml;\r\n                    }\r\n                 }\r\n            } catch (refetchErr) {\r\n                console.error(\"[MjmlEditor] Manual Save - Error re-fetching HTML after delay:\", refetchErr);\r\n            }\r\n        }\r\n\r\n        // ALWAYS return the potentially updated generatedCode object\r\n        return generatedCode; \r\n      },\r\n       getEditor: () => grapesEditor.current,\r\n    }));\r\n\r\n    return <div ref={editorRef} style={{ height: height }} />;\r\n  }\r\n);\r\n\r\n// Assign display name for debugging\r\nMjmlEditor.displayName = 'MjmlEditor';\r\n\r\nexport default MjmlEditor;"], "mappings": "AAAA,MAAO,kCAAkC,CAAE;AAE3C,MAAO,CAAAA,KAAK,EACVC,UAAU,CACVC,SAAS,CACTC,mBAAmB,CACnBC,MAAM,KACD,OAAO,CAEd,MAAO,CAAAC,QAAQ,KAAkB,UAAU,CAC3C;AACA,MAAO,CAAAC,YAAY,KAAM,eAAe,CAExC;AAAA,OAAAC,GAAA,IAAAC,IAAA,yBAaA,KAAM,CAAAC,UAAU,cAAGR,UAAU,CAC3B,CAAAS,IAAA,CAAkEC,GAAG,GAAK,IAAzE,CAAEC,WAAW,CAAG,EAAE,CAAEC,WAAW,CAAG,EAAE,CAAEC,MAAM,CAAEC,MAAM,CAAG,MAAO,CAAC,CAAAL,IAAA,CAC9D,KAAM,CAAAM,SAAS,CAAGZ,MAAM,CAAiB,IAAI,CAAC,CAC9C,KAAM,CAAAa,YAAY,CAAGb,MAAM,CAAgB,IAAI,CAAC,CAEhD;AACAF,SAAS,CAAC,IAAM,CACd,GAAI,CAACc,SAAS,CAACE,OAAO,CAAE,OAAQ;AAEhC;AACA,GAAID,YAAY,CAACC,OAAO,CAAE,CACxBC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAChEH,YAAY,CAACC,OAAO,CAACG,OAAO,CAAC,CAAC,CAC9BJ,YAAY,CAACC,OAAO,CAAG,IAAI,CAC7B,CAEAC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAE,CAC1DE,OAAO,CAAE,CAAC,CAACV,WAAW,CACtBW,UAAU,CAAE,CAAAX,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEY,MAAM,GAAI,CAAC,CACpCC,OAAO,CAAE,CAAC,CAACZ,WAAW,CACtBa,UAAU,CAAE,CAAAb,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEW,MAAM,GAAI,CACrC,CAAC,CAAC,CAEF,GAAI,CACF,KAAM,CAAAG,MAAM,CAAGtB,QAAQ,CAACuB,IAAI,CAAC,CAC3BC,SAAS,CAAEb,SAAS,CAACE,OAAO,CAC5BY,WAAW,CAAE,KAAK,CAAE;AACpBf,MAAM,CAAEgB,MAAM,CAAChB,MAAM,CAAC,CACtBiB,KAAK,CAAE,MAAM,CACbC,cAAc,CAAE,KAAK,CAAE;AACvBC,OAAO,CAAE,CAAC5B,YAAY,CAAC,CACvB6B,WAAW,CAAE,CACX,eAAe,CAAE,CACf;AACA;AACCC,YAAY,CAAE,IAAI,CAAE;AACpBC,WAAW,CAAE,KAAO;AACpB;AACH,CACF,CACA;AACF,CAAC,CAAC,CAEF;AACA,GAAI,CAACV,MAAM,CAAE,CACXR,OAAO,CAACmB,KAAK,CAAC,0CAA0C,CAAC,CACzD,OACF,CAEArB,YAAY,CAACC,OAAO,CAAGS,MAAM,CAE7B;AACA,GAAI,CAACA,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC,CAAE,CACzCrB,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC,CACrEO,MAAM,CAACY,QAAQ,CAACE,GAAG,CAAC,eAAe,CAAE,CACnCC,GAAG,CAAGf,MAAM,EAAK,CACf,KAAM,CAAAgB,IAAI,CAAGhB,MAAM,CAACiB,OAAO,CAAC,CAAC,CAC7B;AACA,MAAO,CACLD,IAAI,CAAEA,IAAI,CACVE,IAAI,CAAEF,IAAK;AACb,CAAC,CACH,CACF,CAAC,CAAC,CACJ,CAEA,GAAI,CAAChB,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAC,CAAE,CACxCrB,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CACpEO,MAAM,CAACY,QAAQ,CAACE,GAAG,CAAC,cAAc,CAAE,CAClCC,GAAG,CAAGf,MAAM,EAAK,CACf,MAAO,CAAAA,MAAM,CAACiB,OAAO,CAAC,CAAEE,SAAS,CAAEnB,MAAM,CAACoB,UAAU,CAAC,CAAE,CAAC,CAAC,CAC3D,CACF,CAAC,CAAC,CACJ,CAEA;AACAC,UAAU,CAAC,IAAM,CACf,GAAI,CAAC/B,YAAY,CAACC,OAAO,CAAE,CACzBC,OAAO,CAACmB,KAAK,CAAC,0DAA0D,CAAC,CACzE,OACF,CAEA;AACA,GAAI,CAACrB,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAAE,CACvC9B,OAAO,CAACmB,KAAK,CAAC,6DAA6D,CAAC,CAC5E,OACF,CAEA,GAAI,CACF;AACA,GAAI1B,WAAW,CAAE,CACfO,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAER,WAAW,CAACsC,SAAS,CAAC,CAAC,CAAE,GAAG,CAAC,CAAG,KAAK,CAAC,CACxF,GAAI,CACFjC,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAACrC,WAAW,CAAC,CAAE;AACjDO,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC9D,CAAE,MAAO+B,CAAC,CAAE,CACVhC,OAAO,CAACmB,KAAK,CAAC,0CAA0C,CAAEa,CAAC,CAAC,CAC5D;AACA,GAAItC,WAAW,CAAE,CACfM,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAChEH,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAACpC,WAAW,CAAC,CAAE;AACjDM,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAClE,CACF,CACF,CAAC,IAAM,IAAIP,WAAW,CAAE,CACtBM,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAEP,WAAW,CAACqC,SAAS,CAAC,CAAC,CAAE,GAAG,CAAC,CAAG,KAAK,CAAC,CAC5GjC,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAACpC,WAAW,CAAC,CAC/CM,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC,CAC9D,CAAC,IAAM,CACL;AACAD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CACzEH,YAAY,CAACC,OAAO,CAAC+B,aAAa,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,CAAC,CACJ,CACF,CAAE,MAAOX,KAAK,CAAE,CACdnB,OAAO,CAACmB,KAAK,CAAC,8CAA8C,CAAEA,KAAK,CAAC,CACtE,CACF,CAAC,CAAE,GAAG,CAAC,CAEP;AACA,GAAI,CAAAc,WAAuC,CAC3C,GAAI,CAAAC,QAAQ,CAAG,KAAK,CAAE;AAEtB;AACA1B,MAAM,CAAC2B,EAAE,CAAC,qBAAqB,CAAE,IAAM,CACrC,GAAIxC,MAAM,EAAIa,MAAM,EAAI,CAAC0B,QAAQ,CAAE,CAAE;AACnC;AACA,GAAID,WAAW,CAAEG,YAAY,CAACH,WAAW,CAAC,CAE1C;AACAA,WAAW,CAAGJ,UAAU,CAAC,IAAM,CAAE;AAC/B,GAAI,CACFK,QAAQ,CAAG,IAAI,CAAE;AACjB;AACA,GAAI,CAAAG,SAAS,CAAG,EAAE,CAClB,GAAI,CAAAC,SAAS,CAAG,EAAE,CAElB,GAAI,CACF;AACA,KAAM,CAAAC,QAAQ,CAAG/B,MAAM,CAACgC,UAAU,CAAC,eAAe,CAAC,CACnD,GAAID,QAAQ,EAAI,MAAO,CAAAA,QAAQ,GAAK,QAAQ,CAAE,CAAE;AAC9CF,SAAS,CAAGE,QAAQ,CAACf,IAAI,EAAI,EAAE,CAC/Bc,SAAS,CAAGC,QAAQ,CAACb,IAAI,EAAI,EAAE,CAC/B1B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAClE,CACF,CAAE,MAAOwC,MAAM,CAAE,CACdzC,OAAO,CAAC0C,IAAI,CAAC,yDAAyD,CAAED,MAAM,CAAC,CAClF,CAEA;AACA,GAAI,CAACJ,SAAS,EAAI,CAACC,SAAS,CAAE,CAC1BD,SAAS,CAAG7B,MAAM,CAACiB,OAAO,CAAC,CAAC,EAAI,EAAE,CAAE;AACpC,GAAI,CACF;AACAa,SAAS,CAAG9B,MAAM,CAACgC,UAAU,CAAC,cAAc,CAAC,EAAI,EAAE,CACrD,CAAE,MAAMG,UAAU,CAAE,CACjB3C,OAAO,CAAC0C,IAAI,CAAC,gCAAgC,CAAEC,UAAU,CAAC,CAC7D,CACA;AACA,GAAI,CAACL,SAAS,CAAE,CACdA,SAAS,CAAG9B,MAAM,CAACiB,OAAO,CAAC,CAAEE,SAAS,CAAEnB,MAAM,CAACoB,UAAU,CAAC,CAAE,CAAC,CAAC,EAAI,EAAE,CACtE,CACA5B,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC,CACvE,CAEA;AACA,GAAI,CAACoC,SAAS,CAACO,IAAI,CAAC,CAAC,CAAE,CACrB5C,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC,CAClEiC,QAAQ,CAAG,KAAK,CAChB,OACF,CAEAlC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC,CACxD;AACA;AACAN,MAAM,CAAC0C,SAAS,CAAEC,SAAS,CAAC,CAC5BtC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CAExD,CAAE,MAAOkB,KAAK,CAAE,CACZnB,OAAO,CAACmB,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAChE,CAAC,OAAS,CACNe,QAAQ,CAAG,KAAK,CAAE;AACtB,CACD,CAAC,CAAE,GAAG,CAAC,CAAE;AACX,CACF,CAAC,CAAC,CAEF;AACA,MAAO,IAAM,CACX,GAAID,WAAW,CAAEG,YAAY,CAACH,WAAW,CAAC,CAAE;AAC5C,GAAInC,YAAY,CAACC,OAAO,CAAE,CACvB;AACA,GAAI,CACFD,YAAY,CAACC,OAAO,CAACG,OAAO,CAAC,CAAC,CAChC,CAAE,MAAO2C,YAAY,CAAE,CACrB7C,OAAO,CAACmB,KAAK,CAAC,2CAA2C,CAAE0B,YAAY,CAAC,CAC1E,CACA/C,YAAY,CAACC,OAAO,CAAG,IAAI,CAC9B,CACF,CAAC,CACH,CAAE,MAAO+C,SAAS,CAAE,CAClB9C,OAAO,CAACmB,KAAK,CAAC,2DAA2D,CAAE2B,SAAS,CAAC,CACvF,CACF,CAAC,CAAE,CAACrD,WAAW,CAAEC,WAAW,CAAEE,MAAM,CAAED,MAAM,CAAC,CAAC,CAAE;AAEhD;AACAX,mBAAmB,CAACQ,GAAG,CAAE,KAAO,CAC9BuD,IAAI,CAAE,KAAAA,CAAA,GAAY,CAChB,GAAI,CAAAC,aAAa,CAAG,CAAExB,IAAI,CAAE,EAAE,CAAEE,IAAI,CAAE,EAAG,CAAC,CAAE;AAC5C,GAAI5B,YAAY,CAACC,OAAO,CAAE,CACvB,GAAI,CACA;AACA,KAAM,CAAAS,MAAM,CAAGV,YAAY,CAACC,OAAO,CACnC,GAAIS,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,eAAe,CAAC,CAAE,CACxC;AACA,KAAM,CAAA4B,MAAM,CAAGzC,MAAM,CAACgC,UAAU,CAAC,eAAe,CAAC,CACjD;AACA,GAAIS,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,EAAI,MAAM,EAAI,CAAAA,MAAM,EAAI,MAAM,EAAI,CAAAA,MAAM,CAAE,CAChFD,aAAa,CAACxB,IAAI,CAAGyB,MAAM,CAACzB,IAAI,EAAI,EAAE,CACtCwB,aAAa,CAACtB,IAAI,CAAGuB,MAAM,CAACvB,IAAI,EAAI,EAAE,CACtC1B,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAE,CAAEuB,IAAI,CAAEwB,aAAa,CAACxB,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAEL,IAAI,CAAEsB,aAAa,CAACtB,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAM,CAAC,CAAC,CACvK,CAAC,IAAM,CACJ/B,OAAO,CAAC0C,IAAI,CAAC,2EAA2E,CAAC,CACzF;AACA,KAAM,IAAI,CAAAQ,KAAK,CAAC,uCAAuC,CAAC,CAC3D,CACF,CAAC,IAAM,CACL;AACA,KAAM,IAAI,CAAAA,KAAK,CAAC,qCAAqC,CAAC,CACxD,CACJ,CAAE,MAAOT,MAAM,CAAE,CACbzC,OAAO,CAAC0C,IAAI,CAAC,8DAA8D,CAAED,MAAM,CAAC,CACpF,GAAI,CACF;AACA,KAAM,CAAAjC,MAAM,CAAGV,YAAY,CAACC,OAAO,CACnC,KAAM,CAAAoD,OAAO,CAAG3C,MAAM,CAACiB,OAAO,CAAC,CAAC,EAAI,EAAE,CACtC,GAAI,CAAA2B,aAAa,CAAG,EAAE,CAEtB;AACA,GAAI5C,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAC,CAAE,CACvC+B,aAAa,CAAG5C,MAAM,CAACgC,UAAU,CAAC,cAAc,CAAC,EAAI,EAAE,CACzD,CAAC,IAAM,CACL;AACAY,aAAa,CAAG5C,MAAM,CAACiB,OAAO,CAAC,CAAEE,SAAS,CAAEnB,MAAM,CAACoB,UAAU,CAAC,CAAE,CAAC,CAAC,EAAI,EAAE,CAC1E,CAEA,GAAIuB,OAAO,EAAIC,aAAa,CAAE,CAAE;AAC5BJ,aAAa,CAACxB,IAAI,CAAG2B,OAAO,CAC5BH,aAAa,CAACtB,IAAI,CAAG0B,aAAa,EAAID,OAAO,CAAE;AAC/CnD,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC,CAClF,CAAC,IAAM,CACHD,OAAO,CAACmB,KAAK,CAAC,yEAAyE,CAAC,CAC5F,CACF,CAAE,MAAOkC,WAAW,CAAE,CACnBrD,OAAO,CAACmB,KAAK,CAAC,6DAA6D,CAAEkC,WAAW,CAAC,CAC5F,CACJ,CACH,CAAC,IAAM,CACLrD,OAAO,CAACmB,KAAK,CAAC,kDAAkD,CAAC,CACnE,CAEA;AACA,KAAM,IAAI,CAAAmC,OAAO,CAACC,OAAO,EAAI1B,UAAU,CAAC0B,OAAO,CAAE,GAAG,CAAC,CAAC,CAAE;AAExD;AACA,GAAIzD,YAAY,CAACC,OAAO,EAAI,CAACiD,aAAa,CAACtB,IAAI,CAACkB,IAAI,CAAC,CAAC,CAAE,CAAE;AACtD5C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CACzE,GAAI,CACC,KAAM,CAAAO,MAAM,CAAGV,YAAY,CAACC,OAAO,CAEnC,GAAI,CAAAyD,sBAAsB,CAAG,EAAE,CAC/B,GAAIhD,MAAM,CAACY,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAC,CAAE,CACvCmC,sBAAsB,CAAGhD,MAAM,CAACgC,UAAU,CAAC,cAAc,CAAC,CAC5D,CAEA,GAAI,CAACgB,sBAAsB,CAAE,CAC3BA,sBAAsB,CAAGhD,MAAM,CAACiB,OAAO,CAAC,CAAEE,SAAS,CAAEnB,MAAM,CAACoB,UAAU,CAAC,CAAE,CAAC,CAAC,EAAI,EAAE,CACnF,CAEA,GAAI4B,sBAAsB,CAACZ,IAAI,CAAC,CAAC,CAAE,CAC/B5C,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CACzE+C,aAAa,CAACtB,IAAI,CAAG8B,sBAAsB,CAC/C,CAAC,IAAM,CACJxD,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC,CACvE;AACA,GAAI+C,aAAa,CAACxB,IAAI,EAAI,CAACwB,aAAa,CAACtB,IAAI,CAAE,CAC7CsB,aAAa,CAACtB,IAAI,CAAGsB,aAAa,CAACxB,IAAI,CACzC,CACH,CACL,CAAE,MAAOiC,UAAU,CAAE,CACjBzD,OAAO,CAACmB,KAAK,CAAC,gEAAgE,CAAEsC,UAAU,CAAC,CAC/F,CACJ,CAEA;AACA,MAAO,CAAAT,aAAa,CACtB,CAAC,CACAU,SAAS,CAAEA,CAAA,GAAM5D,YAAY,CAACC,OACjC,CAAC,CAAC,CAAC,CAEH,mBAAOV,IAAA,QAAKG,GAAG,CAAEK,SAAU,CAAC8D,KAAK,CAAE,CAAE/D,MAAM,CAAEA,MAAO,CAAE,CAAE,CAAC,CAC3D,CACF,CAAC,CAED;AACAN,UAAU,CAACsE,WAAW,CAAG,YAAY,CAErC,cAAe,CAAAtE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}