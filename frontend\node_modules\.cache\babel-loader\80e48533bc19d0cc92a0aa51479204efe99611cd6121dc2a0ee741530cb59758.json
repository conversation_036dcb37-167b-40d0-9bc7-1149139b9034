{"ast": null, "code": "import React,{useEffect,useState}from'react';import Alert from'components/Alert';import Button from'components/Button';import Card from'components/Card';import ConfirmModal from'components/ConfirmModal';import ScheduleCampaignModal from'components/ScheduleCampaignModal';// import Layout from '../../components/Layout'; // Removed Layout import\nimport{useAuth}from'contexts/AuthContext';import{useNavigate}from'react-router-dom';import{campaignAPI}from'services/api';import{io}from'socket.io-client';// Define the shape of the campaign data more explicitly\nimport{jsxs as _jsxs,jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const CampaignList=()=>{var _user$domain,_user$domain2,_user$domain3;const{user}=useAuth();const navigate=useNavigate();// Use the Campaign interface for state type\nconst[campaigns,setCampaigns]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[modalOpen,setModalOpen]=useState(false);const[campaignToDelete,setCampaignToDelete]=useState(null);const[campaignToSend,setCampaignToSend]=useState(null);const[sendModalOpen,setSendModalOpen]=useState(false);const[sending,setSending]=useState(false);// State for the schedule modal\nconst[scheduleModalOpen,setScheduleModalOpen]=useState(false);const[campaignToSchedule,setCampaignToSchedule]=useState(null);// No more mock campaigns\nconst mockCampaigns=[];// Fetch campaigns on component mount and handle reload flag\nuseEffect(()=>{const shouldReload=sessionStorage.getItem('reloadCampaigns');if(shouldReload){sessionStorage.removeItem('reloadCampaigns');window.location.reload();// Consider a less disruptive update if possible\n}else{loadCampaigns();}},[]);// Run only on mount\n// --- WebSocket Connection and Event Listener ---\nuseEffect(()=>{// Connect to the Socket.IO server (use env variable for URL in production)\nconst socketServerUrl=process.env.REACT_APP_SOCKET_URL||'http://localhost:3000';const socket=io(socketServerUrl,{withCredentials:true// Important if using auth/cookies with sockets\n});console.log(`[CampaignList] Attempting to connect socket to ${socketServerUrl}...`);socket.on('connect',()=>{console.log(`[CampaignList] Socket connected with ID: ${socket.id}`);});socket.on('disconnect',reason=>{console.log(`[CampaignList] Socket disconnected: ${reason}`);});socket.on('connect_error',err=>{console.error(`[CampaignList] Socket connection error:`,err);});// Listener for campaign status updates\nsocket.on('campaignStatusUpdate',data=>{console.log('[CampaignList] Received campaignStatusUpdate:',data);setCampaigns(prevCampaigns=>prevCampaigns.map(campaign=>campaign._id===data.id?{...campaign,status:data.status,// Update timestamps if provided in the event data\n...(data.startedAt&&{startedAt:data.startedAt}),...(data.completedAt&&{completedAt:data.completedAt}),...(data.sentAt&&{sentAt:data.sentAt})}:campaign));});// Cleanup function: disconnect socket when component unmounts\nreturn()=>{console.log('[CampaignList] Disconnecting socket...');socket.disconnect();};},[]);// Run only once on component mount\n// --- End WebSocket Setup ---\n// Function to load campaigns from API\nconst loadCampaigns=async()=>{try{// Get campaigns from API\nconst response=await campaignAPI.getCampaigns();console.log('Campaigns from API:',response.data.campaigns);// Set campaigns directly\nsetCampaigns(response.data.campaigns);}catch(err){var _err$response,_err$response$data;console.error('Error loading campaigns:',err);setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||'Failed to load campaigns');setCampaigns([]);}finally{setLoading(false);}};// Function to open delete confirmation modal\nconst openDeleteModal=id=>{setCampaignToDelete(id);setModalOpen(true);};// Function to open send confirmation modal\nconst openSendModal=campaign=>{setCampaignToSend(campaign);setSendModalOpen(true);};// Function to delete a campaign\nconst handleDeleteCampaign=async()=>{if(!campaignToDelete)return;try{// Call API to delete campaign\nawait campaignAPI.deleteCampaign(campaignToDelete);// Update state to reflect the deletion\nsetCampaigns(campaigns.filter(campaign=>campaign._id!==campaignToDelete));// Show success message\nsetSuccess('Campaign deleted successfully');setError('');// Clear any existing errors\n}catch(err){var _err$response2,_err$response2$data;console.error('Error deleting campaign:',err);setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||'Failed to delete campaign');}finally{// Close modal and reset campaignToDelete\nsetModalOpen(false);setCampaignToDelete(null);}};// Function to send a campaign - Minor update to use Campaign type\nconst handleSendCampaign=async()=>{if(!campaignToSend)return;try{setSending(true);setError('');console.log('Sending campaign with ID:',campaignToSend._id);// API call remains the same, response handling might not be needed now for status\nawait campaignAPI.sendCampaign(campaignToSend._id);// OPTIONAL: Immediately update status locally for responsiveness,\n// but rely on WebSocket for the final authoritative update.\nsetCampaigns(prevCampaigns=>prevCampaigns.map(c=>c._id===campaignToSend._id?{...c,status:'sending'}:c));// Success message can be simpler now\nsetSuccess(/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:/*#__PURE__*/_jsxs(\"strong\",{children:[\"Campaign \\\"\",campaignToSend.name,\"\\\" send initiated!\"]})}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm\",children:\"Status will update automatically.\"})]}));}catch(err){var _err$response3,_err$response3$data;console.error('Error sending campaign:',err);setError(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.message)||'Failed to send campaign');// Optionally revert optimistic update on error\n// loadCampaigns(); // Or revert specific campaign status\n}finally{setSending(false);setSendModalOpen(false);setCampaignToSend(null);}};// Function to open the schedule modal\nconst openScheduleModal=campaign=>{setCampaignToSchedule(campaign);setScheduleModalOpen(true);};// Callback function when scheduling is successful\nconst handleScheduleSuccess=message=>{setScheduleModalOpen(false);setCampaignToSchedule(null);setSuccess(message);// Optimistically update the campaign status locally\nif(campaignToSchedule){setCampaigns(prev=>prev.map(c=>c._id===campaignToSchedule._id?{...c,status:'scheduled',scheduledFor:new Date().toISOString()}:c// Update status and add placeholder date\n));}// Alternatively, call loadCampaigns() for a full refresh, but local update is faster UI\n// loadCampaigns(); \n};// Format date for display\nconst formatDate=dateString=>{const date=new Date(dateString);return date.toLocaleDateString('en-US',{year:'numeric',month:'short',day:'numeric',hour:'2-digit',minute:'2-digit'});};// Get status badge class\nconst getStatusBadgeClass=status=>{switch(status){case'draft':return'bg-gray-700';case'scheduled':return'bg-blue-800';case'sending':return'bg-yellow-800';case'sent':return'bg-green-800';case'failed':return'bg-red-800';default:return'bg-gray-700';}};return(/*#__PURE__*/// <Layout title=\"Campaigns\">\n_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold\",children:\"Email Campaigns\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary\",children:\"Manage your email campaigns\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:()=>navigate('/campaigns/domain-setup'),children:\"Domain Setup\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>navigate('/campaigns/create'),disabled:!(user!==null&&user!==void 0&&(_user$domain=user.domain)!==null&&_user$domain!==void 0&&_user$domain.status)||user.domain.status!=='active',title:!(user!==null&&user!==void 0&&(_user$domain2=user.domain)!==null&&_user$domain2!==void 0&&_user$domain2.status)||user.domain.status!=='active'?'Verify a domain first':'Create a new campaign',children:\"Create Campaign\"})]})]}),error&&/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error,onClose:()=>setError(''),className:\"mb-6\"}),success&&/*#__PURE__*/_jsx(Alert,{type:\"success\",message:success,onClose:()=>setSuccess(''),className:\"mb-6\"}),!(user!==null&&user!==void 0&&(_user$domain3=user.domain)!==null&&_user$domain3!==void 0&&_user$domain3.status)||user.domain.status!=='active'?/*#__PURE__*/_jsx(Alert,{type:\"warning\",message:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"font-medium\",children:\"You need to verify a domain before creating campaigns\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-1\",children:\"Please go to Domain Setup to verify your domain first.\"})]}),className:\"mb-6\"}):null,/*#__PURE__*/_jsx(Card,{children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center items-center py-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"})}):campaigns.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl mb-4\",children:\"\\uD83D\\uDCE7\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-medium mb-2\",children:\"No Campaigns Yet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-text-secondary mb-4\",children:\"Create your first email campaign to start sending emails to your contacts.\"}),/*#__PURE__*/_jsx(Button,{onClick:()=>navigate('/campaigns/create'),children:\"Create Your First Campaign\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"table w-full\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Subject\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Recipients\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Created\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Scheduled/Sent\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Performance\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]},\"header-row\")}),/*#__PURE__*/_jsx(\"tbody\",{children:campaigns.map(campaign=>{var _campaign$recipientCo,_campaign$openCount,_campaign$clickCount;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{children:campaign.name}),/*#__PURE__*/_jsx(\"td\",{children:campaign.subject}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(\"span\",{className:`px-2 py-1 text-xs rounded ${getStatusBadgeClass(campaign.status)}`,children:campaign.status.charAt(0).toUpperCase()+campaign.status.slice(1)})}),/*#__PURE__*/_jsx(\"td\",{children:(_campaign$recipientCo=campaign.recipientCountActual)!==null&&_campaign$recipientCo!==void 0?_campaign$recipientCo:'-'}),/*#__PURE__*/_jsx(\"td\",{children:formatDate(campaign.createdAt)}),/*#__PURE__*/_jsx(\"td\",{children:campaign.scheduledFor?campaign.status==='sent'||campaign.status==='completed'?formatDate(campaign.sentAt||campaign.completedAt||''):formatDate(campaign.scheduledFor):campaign.sentAt?formatDate(campaign.sentAt):/*#__PURE__*/_jsx(\"span\",{className:\"text-text-secondary\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:campaign.status==='sent'||campaign.status==='completed'?/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"Opens: \",(_campaign$openCount=campaign.openCount)!==null&&_campaign$openCount!==void 0?_campaign$openCount:0]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"Clicks: \",(_campaign$clickCount=campaign.clickCount)!==null&&_campaign$clickCount!==void 0?_campaign$clickCount:0]})]}):/*#__PURE__*/_jsx(\"span\",{className:\"text-text-secondary\",children:\"-\"})}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex space-x-2\",children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>{const targetId=campaign._id;console.log(`[CampaignList] Navigating for ID: ${targetId}`);// Log ID before navigate\nif(campaign.status==='draft'||campaign.status==='scheduled'){navigate(`/campaigns/edit/${targetId}`);}else{navigate(`/campaign-summary/${targetId}`);}},disabled:campaign.status==='sending',children:campaign.status==='draft'||campaign.status==='scheduled'?'Edit':'View'},\"edit-view-btn\"),(campaign.status==='sent'||campaign.status==='completed')&&/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>navigate(`/campaigns/analytics/${campaign._id}`),children:\"Analytics\"},\"analytics-btn\"),campaign.status==='draft'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>navigate(`/campaigns/recipients/${campaign._id}`),children:\"Recipients\"},\"recipients-btn\"),/*#__PURE__*/_jsx(Button,{size:\"sm\",onClick:()=>openSendModal(campaign),children:\"Send Now\"},\"send-btn\")]}),campaign.status==='draft'&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(Button,{variant:\"secondary\",size:\"sm\",onClick:()=>openScheduleModal(campaign),title:\"Schedule this campaign\",children:\"Schedule\"},\"schedule-btn-list\")}),/*#__PURE__*/_jsx(Button,{variant:\"danger\",size:\"sm\",onClick:()=>openDeleteModal(campaign._id),disabled:campaign.status==='sending',children:\"Delete\"},\"delete-btn\")]})})]},campaign._id);})})]})})}),/*#__PURE__*/_jsx(ConfirmModal,{isOpen:modalOpen,title:\"Delete Campaign\",message:\"Are you sure you want to delete this campaign? This action cannot be undone.\",confirmText:\"Delete\",onConfirm:handleDeleteCampaign,onCancel:()=>{setModalOpen(false);setCampaignToDelete(null);}}),/*#__PURE__*/_jsx(ConfirmModal,{isOpen:sendModalOpen,title:\"Send Campaign\",message:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{children:\"Are you sure you want to send this campaign?\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-2 text-blue-400\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Note:\"}),\" This will send real emails to your recipients using AWS SES.\"]}),campaignToSend&&/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 bg-gray-900 p-3 rounded-md\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Name:\"}),\" \",campaignToSend.name]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Subject:\"}),\" \",campaignToSend.subject]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"From:\"}),\" \",campaignToSend.fromName,\" <\",campaignToSend.fromEmail,\">\"]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-4 text-gray-400 text-sm\",children:\"Make sure your campaign content is ready and has been tested before sending.\"})]}),confirmText:sending?\"Sending...\":\"Send Campaign\",onConfirm:handleSendCampaign,onCancel:()=>{setSendModalOpen(false);setCampaignToSend(null);}}),/*#__PURE__*/_jsx(ScheduleCampaignModal,{isOpen:scheduleModalOpen,onClose:()=>setScheduleModalOpen(false),campaign:campaignToSchedule,onScheduled:handleScheduleSuccess})]})// </Layout>\n);};export default CampaignList;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "ConfirmModal", "ScheduleCampaignModal", "useAuth", "useNavigate", "campaignAPI", "io", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "CampaignList", "_user$domain", "_user$domain2", "_user$domain3", "user", "navigate", "campaigns", "setCampaigns", "loading", "setLoading", "error", "setError", "success", "setSuccess", "modalOpen", "setModalOpen", "campaignToDelete", "setCampaignToDelete", "campaignToSend", "setCampaignToSend", "sendModalOpen", "setSendModalOpen", "sending", "setSending", "scheduleModalOpen", "setScheduleModalOpen", "campaignToSchedule", "setCampaignToSchedule", "mockCampaigns", "shouldReload", "sessionStorage", "getItem", "removeItem", "window", "location", "reload", "loadCampaigns", "socketServerUrl", "process", "env", "REACT_APP_SOCKET_URL", "socket", "withCredentials", "console", "log", "on", "id", "reason", "err", "data", "prevCampaigns", "map", "campaign", "_id", "status", "startedAt", "completedAt", "sentAt", "disconnect", "response", "getCampaigns", "_err$response", "_err$response$data", "message", "openDeleteModal", "openSendModal", "handleDeleteCampaign", "deleteCampaign", "filter", "_err$response2", "_err$response2$data", "handleSendCampaign", "sendCampaign", "c", "children", "name", "className", "_err$response3", "_err$response3$data", "openScheduleModal", "handleScheduleSuccess", "prev", "scheduledFor", "Date", "toISOString", "formatDate", "dateString", "date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getStatusBadgeClass", "variant", "onClick", "disabled", "domain", "title", "type", "onClose", "length", "_campaign$recipientCo", "_campaign$openCount", "_campaign$clickCount", "subject", "char<PERSON>t", "toUpperCase", "slice", "recipientCountActual", "createdAt", "openCount", "clickCount", "size", "targetId", "isOpen", "confirmText", "onConfirm", "onCancel", "fromName", "fromEmail", "onScheduled"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/DONT DELETE/driftly-enhanced-current-2.0.0/frontend/src/pages/campaigns/CampaignList.tsx"], "sourcesContent": ["import React, {\n  useEffect,\n  useState,\n} from 'react';\n\nimport Alert from 'components/Alert';\nimport Button from 'components/Button';\nimport Card from 'components/Card';\nimport ConfirmModal from 'components/ConfirmModal';\nimport ScheduleCampaignModal from 'components/ScheduleCampaignModal';\n// import Layout from '../../components/Layout'; // Removed Layout import\nimport { useAuth } from 'contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { campaignAPI } from 'services/api';\nimport {\n  io,\n  Socket,\n} from 'socket.io-client';\n\n// Define the shape of the campaign data more explicitly\ninterface Campaign {\n  _id: string;\n  name: string;\n  subject: string;\n  status: string; // Keep as string, but could use specific union type if preferred\n  recipientCountActual?: number; // Use optional if not always present initially\n  createdAt: string;\n  scheduledFor?: string;\n  sentAt?: string;\n  completedAt?: string; // Added missing field\n  openCount?: number;\n  clickCount?: number;\n  openRate?: number;\n  clickRate?: number;\n  fromName?: string; // Added for send modal\n  fromEmail?: string; // Added for send modal\n  // Add other fields if needed by the component\n}\n\nconst CampaignList: React.FC = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  // Use the Campaign interface for state type\n  const [campaigns, setCampaigns] = useState<Campaign[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState<React.ReactNode>('');\n  const [modalOpen, setModalOpen] = useState(false);\n  const [campaignToDelete, setCampaignToDelete] = useState<string | null>(null);\n  const [campaignToSend, setCampaignToSend] = useState<Campaign | null>(null);\n  const [sendModalOpen, setSendModalOpen] = useState(false);\n  const [sending, setSending] = useState(false);\n\n  // State for the schedule modal\n  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);\n  const [campaignToSchedule, setCampaignToSchedule] = useState<Campaign | null>(null);\n\n  // No more mock campaigns\n  const mockCampaigns: any[] = [];\n\n  // Fetch campaigns on component mount and handle reload flag\n  useEffect(() => {\n    const shouldReload = sessionStorage.getItem('reloadCampaigns');\n    if (shouldReload) {\n      sessionStorage.removeItem('reloadCampaigns');\n      window.location.reload(); // Consider a less disruptive update if possible\n    } else {\n      loadCampaigns();\n    }\n  }, []); // Run only on mount\n\n  // --- WebSocket Connection and Event Listener ---\n  useEffect(() => {\n    // Connect to the Socket.IO server (use env variable for URL in production)\n    const socketServerUrl = process.env.REACT_APP_SOCKET_URL || 'http://localhost:3000'; \n    const socket: Socket = io(socketServerUrl, {\n        withCredentials: true // Important if using auth/cookies with sockets\n    });\n\n    console.log(`[CampaignList] Attempting to connect socket to ${socketServerUrl}...`);\n\n    socket.on('connect', () => {\n        console.log(`[CampaignList] Socket connected with ID: ${socket.id}`);\n    });\n\n    socket.on('disconnect', (reason) => {\n        console.log(`[CampaignList] Socket disconnected: ${reason}`);\n    });\n\n    socket.on('connect_error', (err) => {\n        console.error(`[CampaignList] Socket connection error:`, err);\n    });\n\n    // Listener for campaign status updates\n    socket.on('campaignStatusUpdate', (data: {\n         id: string;\n         status: string;\n         startedAt?: string; // Add optional timestamp fields\n         completedAt?: string;\n         sentAt?: string; // Include sentAt if backend might send it\n    }) => {\n      console.log('[CampaignList] Received campaignStatusUpdate:', data);\n      setCampaigns(prevCampaigns =>\n        prevCampaigns.map(campaign =>\n          campaign._id === data.id\n            ? { \n                ...campaign, \n                status: data.status, \n                // Update timestamps if provided in the event data\n                ...(data.startedAt && { startedAt: data.startedAt }),\n                ...(data.completedAt && { completedAt: data.completedAt }),\n                ...(data.sentAt && { sentAt: data.sentAt })\n              } \n            : campaign\n        )\n      );\n    });\n\n    // Cleanup function: disconnect socket when component unmounts\n    return () => {\n      console.log('[CampaignList] Disconnecting socket...');\n      socket.disconnect();\n    };\n  }, []); // Run only once on component mount\n  // --- End WebSocket Setup ---\n\n  // Function to load campaigns from API\n  const loadCampaigns = async () => {\n    try {\n      // Get campaigns from API\n      const response = await campaignAPI.getCampaigns();\n      console.log('Campaigns from API:', response.data.campaigns);\n\n      // Set campaigns directly\n      setCampaigns(response.data.campaigns);\n    } catch (err: any) {\n      console.error('Error loading campaigns:', err);\n      setError(err.response?.data?.message || 'Failed to load campaigns');\n      setCampaigns([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to open delete confirmation modal\n  const openDeleteModal = (id: string) => {\n    setCampaignToDelete(id);\n    setModalOpen(true);\n  };\n\n  // Function to open send confirmation modal\n  const openSendModal = (campaign: Campaign) => {\n    setCampaignToSend(campaign);\n    setSendModalOpen(true);\n  };\n\n  // Function to delete a campaign\n  const handleDeleteCampaign = async () => {\n    if (!campaignToDelete) return;\n\n    try {\n      // Call API to delete campaign\n      await campaignAPI.deleteCampaign(campaignToDelete);\n\n      // Update state to reflect the deletion\n      setCampaigns(campaigns.filter(campaign => campaign._id !== campaignToDelete));\n\n      // Show success message\n      setSuccess('Campaign deleted successfully');\n      setError(''); // Clear any existing errors\n    } catch (err: any) {\n      console.error('Error deleting campaign:', err);\n      setError(err.response?.data?.message || 'Failed to delete campaign');\n    } finally {\n      // Close modal and reset campaignToDelete\n      setModalOpen(false);\n      setCampaignToDelete(null);\n    }\n  };\n\n  // Function to send a campaign - Minor update to use Campaign type\n  const handleSendCampaign = async () => {\n    if (!campaignToSend) return;\n\n    try {\n      setSending(true);\n      setError('');\n\n      console.log('Sending campaign with ID:', campaignToSend._id);\n      // API call remains the same, response handling might not be needed now for status\n      await campaignAPI.sendCampaign(campaignToSend._id);\n\n      // OPTIONAL: Immediately update status locally for responsiveness,\n      // but rely on WebSocket for the final authoritative update.\n      setCampaigns(prevCampaigns =>\n        prevCampaigns.map(c =>\n          c._id === campaignToSend._id ? { ...c, status: 'sending' } : c\n        )\n      );\n\n      // Success message can be simpler now\n      setSuccess(\n        <div>\n          <p><strong>Campaign \"{campaignToSend.name}\" send initiated!</strong></p>\n          <p className=\"mt-2 text-sm\">Status will update automatically.</p>\n        </div>\n      );\n\n    } catch (err: any) {\n      console.error('Error sending campaign:', err);\n      setError(err.response?.data?.message || 'Failed to send campaign');\n      // Optionally revert optimistic update on error\n      // loadCampaigns(); // Or revert specific campaign status\n    } finally {\n      setSending(false);\n      setSendModalOpen(false);\n      setCampaignToSend(null);\n    }\n  };\n\n  // Function to open the schedule modal\n  const openScheduleModal = (campaign: Campaign) => {\n    setCampaignToSchedule(campaign);\n    setScheduleModalOpen(true);\n  };\n\n  // Callback function when scheduling is successful\n  const handleScheduleSuccess = (message: string) => {\n    setScheduleModalOpen(false);\n    setCampaignToSchedule(null);\n    setSuccess(message);\n    // Optimistically update the campaign status locally\n    if (campaignToSchedule) {\n      setCampaigns(prev => prev.map(c => \n        c._id === campaignToSchedule._id ? { ...c, status: 'scheduled', scheduledFor: new Date().toISOString() } : c // Update status and add placeholder date\n      ));\n    }\n    // Alternatively, call loadCampaigns() for a full refresh, but local update is faster UI\n    // loadCampaigns(); \n  };\n\n  // Format date for display\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  // Get status badge class\n  const getStatusBadgeClass = (status: string) => {\n    switch (status) {\n      case 'draft':\n        return 'bg-gray-700';\n      case 'scheduled':\n        return 'bg-blue-800';\n      case 'sending':\n        return 'bg-yellow-800';\n      case 'sent':\n        return 'bg-green-800';\n      case 'failed':\n        return 'bg-red-800';\n      default:\n        return 'bg-gray-700';\n    }\n  };\n\n  return (\n    // <Layout title=\"Campaigns\">\n    <>\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-xl font-semibold\">Email Campaigns</h2>\n          <p className=\"text-text-secondary\">Manage your email campaigns</p>\n        </div>\n\n        <div className=\"flex space-x-2\">\n          <Button variant=\"secondary\" onClick={() => navigate('/campaigns/domain-setup')}>\n            Domain Setup\n          </Button>\n          <Button\n            onClick={() => navigate('/campaigns/create')}\n            disabled={!user?.domain?.status || user.domain.status !== 'active'}\n            title={!user?.domain?.status || user.domain.status !== 'active' ? 'Verify a domain first' : 'Create a new campaign'}\n          >\n            Create Campaign\n          </Button>\n        </div>\n      </div>\n\n      {error && (\n        <Alert\n          type=\"error\"\n          message={error}\n          onClose={() => setError('')}\n          className=\"mb-6\"\n        />\n      )}\n\n      {success && (\n        <Alert\n          type=\"success\"\n          message={success}\n          onClose={() => setSuccess('')}\n          className=\"mb-6\"\n        />\n      )}\n\n      {/* Domain verification check */}\n      {!user?.domain?.status || user.domain.status !== 'active' ? (\n        <Alert\n          type=\"warning\"\n          message={\n            <div>\n              <p className=\"font-medium\">You need to verify a domain before creating campaigns</p>\n              <p className=\"mt-1\">Please go to Domain Setup to verify your domain first.</p>\n            </div>\n          }\n          className=\"mb-6\"\n        />\n      ) : null}\n\n      <Card>\n        {loading ? (\n          <div className=\"flex justify-center items-center py-8\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\n          </div>\n        ) : campaigns.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <div className=\"text-4xl mb-4\">📧</div>\n            <h3 className=\"text-xl font-medium mb-2\">No Campaigns Yet</h3>\n            <p className=\"text-text-secondary mb-4\">\n              Create your first email campaign to start sending emails to your contacts.\n            </p>\n            <Button onClick={() => navigate('/campaigns/create')}>\n              Create Your First Campaign\n            </Button>\n          </div>\n        ) : (\n          <div className=\"table-container\">\n            <table className=\"table w-full\">\n              <thead>\n                <tr key=\"header-row\">\n                  <th>Name</th>\n                  <th>Subject</th>\n                  <th>Status</th>\n                  <th>Recipients</th>\n                  <th>Created</th>\n                  <th>Scheduled/Sent</th>\n                  <th>Performance</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {campaigns.map((campaign: Campaign) => (\n                  <tr key={campaign._id}>\n                    <td>{campaign.name}</td>\n                    <td>{campaign.subject}</td>\n                    <td>\n                      <span className={`px-2 py-1 text-xs rounded ${getStatusBadgeClass(campaign.status)}`}>\n                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}\n                      </span>\n                    </td>\n                    <td>{campaign.recipientCountActual ?? '-'}</td>\n                    <td>{formatDate(campaign.createdAt)}</td>\n                    <td>\n                      {campaign.scheduledFor\n                        ? campaign.status === 'sent' || campaign.status === 'completed'\n                          ? formatDate(campaign.sentAt || campaign.completedAt || '')\n                          : formatDate(campaign.scheduledFor)\n                        : campaign.sentAt\n                          ? formatDate(campaign.sentAt)\n                          : <span className=\"text-text-secondary\">-</span>\n                      }\n                    </td>\n                    <td>\n                      {campaign.status === 'sent' || campaign.status === 'completed' ? (\n                        <div className=\"text-sm\">\n                          <div>Opens: {campaign.openCount ?? 0}</div>\n                          <div>Clicks: {campaign.clickCount ?? 0}</div>\n                        </div>\n                      ) : (\n                        <span className=\"text-text-secondary\">-</span>\n                      )}\n                    </td>\n                    <td>\n                      <div className=\"flex space-x-2\">\n                        <Button\n                          key=\"edit-view-btn\"\n                          variant=\"secondary\"\n                          size=\"sm\"\n                          onClick={() => {\n                              const targetId = campaign._id;\n                              console.log(`[CampaignList] Navigating for ID: ${targetId}`); // Log ID before navigate\n                              if (campaign.status === 'draft' || campaign.status === 'scheduled') {\n                                  navigate(`/campaigns/edit/${targetId}`);\n                              } else {\n                                  navigate(`/campaign-summary/${targetId}`); \n                              }\n                          }}\n                          disabled={campaign.status === 'sending'}\n                        >\n                          {/* Keep label conditional */} \n                          {campaign.status === 'draft' || campaign.status === 'scheduled' ? 'Edit' : 'View'}\n                        </Button>\n\n                        {(campaign.status === 'sent' || campaign.status === 'completed') && (\n                          <Button\n                            key=\"analytics-btn\"\n                            variant=\"secondary\"\n                            size=\"sm\"\n                            onClick={() => navigate(`/campaigns/analytics/${campaign._id}`)}\n                          >\n                            Analytics\n                          </Button>\n                        )}\n                        {campaign.status === 'draft' && (\n                          <>\n                            <Button\n                              key=\"recipients-btn\"\n                              variant=\"secondary\"\n                              size=\"sm\"\n                              onClick={() => navigate(`/campaigns/recipients/${campaign._id}`)}\n                            >\n                              Recipients\n                            </Button>\n                            <Button\n                              key=\"send-btn\"\n                              size=\"sm\"\n                              onClick={() => openSendModal(campaign)}\n                            >\n                              Send Now\n                            </Button>\n                          </>\n                        )}\n                        {campaign.status === 'draft' && (\n                          <>\n                            <Button\n                              key=\"schedule-btn-list\"\n                              variant=\"secondary\"\n                              size=\"sm\"\n                              onClick={() => openScheduleModal(campaign)}\n                              title=\"Schedule this campaign\"\n                            >\n                              Schedule\n                            </Button>\n                          </>\n                        )}\n                        <Button\n                          key=\"delete-btn\"\n                          variant=\"danger\"\n                          size=\"sm\"\n                          onClick={() => openDeleteModal(campaign._id)}\n                          disabled={campaign.status === 'sending'}\n                        >\n                          Delete\n                        </Button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </Card>\n      {/* Delete Confirmation Modal */}\n      <ConfirmModal\n        isOpen={modalOpen}\n        title=\"Delete Campaign\"\n        message=\"Are you sure you want to delete this campaign? This action cannot be undone.\"\n        confirmText=\"Delete\"\n        onConfirm={handleDeleteCampaign}\n        onCancel={() => {\n          setModalOpen(false);\n          setCampaignToDelete(null);\n        }}\n      />\n\n      {/* Send Campaign Confirmation Modal */}\n      <ConfirmModal\n        isOpen={sendModalOpen}\n        title=\"Send Campaign\"\n        message={\n          <div>\n            <p>Are you sure you want to send this campaign?</p>\n            <p className=\"mt-2 text-blue-400\"><strong>Note:</strong> This will send real emails to your recipients using AWS SES.</p>\n            {campaignToSend && (\n              <div className=\"mt-4 bg-gray-900 p-3 rounded-md\">\n                <p><strong>Name:</strong> {campaignToSend.name}</p>\n                <p><strong>Subject:</strong> {campaignToSend.subject}</p>\n                <p><strong>From:</strong> {campaignToSend.fromName} &lt;{campaignToSend.fromEmail}&gt;</p>\n              </div>\n            )}\n            <p className=\"mt-4 text-gray-400 text-sm\">Make sure your campaign content is ready and has been tested before sending.</p>\n          </div>\n        }\n        confirmText={sending ? \"Sending...\" : \"Send Campaign\"}\n        onConfirm={handleSendCampaign}\n        onCancel={() => {\n          setSendModalOpen(false);\n          setCampaignToSend(null);\n        }}\n      />\n\n      {/* Schedule Campaign Modal */}\n      <ScheduleCampaignModal\n        isOpen={scheduleModalOpen}\n        onClose={() => setScheduleModalOpen(false)}\n        campaign={campaignToSchedule}\n        onScheduled={handleScheduleSuccess}\n      />\n    </>\n    // </Layout>\n  );\n};\n\nexport default CampaignList;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EACVC,SAAS,CACTC,QAAQ,KACH,OAAO,CAEd,MAAO,CAAAC,KAAK,KAAM,kBAAkB,CACpC,MAAO,CAAAC,MAAM,KAAM,mBAAmB,CACtC,MAAO,CAAAC,IAAI,KAAM,iBAAiB,CAClC,MAAO,CAAAC,YAAY,KAAM,yBAAyB,CAClD,MAAO,CAAAC,qBAAqB,KAAM,kCAAkC,CACpE;AACA,OAASC,OAAO,KAAQ,sBAAsB,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,WAAW,KAAQ,cAAc,CAC1C,OACEC,EAAE,KAEG,kBAAkB,CAEzB;AAAA,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAoBA,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,KAAAC,YAAA,CAAAC,aAAA,CAAAC,aAAA,CACnC,KAAM,CAAEC,IAAK,CAAC,CAAGd,OAAO,CAAC,CAAC,CAC1B,KAAM,CAAAe,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B;AACA,KAAM,CAACe,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAa,EAAE,CAAC,CAC1D,KAAM,CAACwB,OAAO,CAAEC,UAAU,CAAC,CAAGzB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC0B,KAAK,CAAEC,QAAQ,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4B,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAkB,EAAE,CAAC,CAC3D,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACgC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjC,QAAQ,CAAgB,IAAI,CAAC,CAC7E,KAAM,CAACkC,cAAc,CAAEC,iBAAiB,CAAC,CAAGnC,QAAQ,CAAkB,IAAI,CAAC,CAC3E,KAAM,CAACoC,aAAa,CAAEC,gBAAgB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACsC,OAAO,CAAEC,UAAU,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAE7C;AACA,KAAM,CAACwC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC0C,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3C,QAAQ,CAAkB,IAAI,CAAC,CAEnF;AACA,KAAM,CAAA4C,aAAoB,CAAG,EAAE,CAE/B;AACA7C,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8C,YAAY,CAAGC,cAAc,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAC9D,GAAIF,YAAY,CAAE,CAChBC,cAAc,CAACE,UAAU,CAAC,iBAAiB,CAAC,CAC5CC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAE;AAC5B,CAAC,IAAM,CACLC,aAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAE,EAAE,CAAC,CAAE;AAER;AACArD,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAsD,eAAe,CAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,EAAI,uBAAuB,CACnF,KAAM,CAAAC,MAAc,CAAGhD,EAAE,CAAC4C,eAAe,CAAE,CACvCK,eAAe,CAAE,IAAK;AAC1B,CAAC,CAAC,CAEFC,OAAO,CAACC,GAAG,CAAC,kDAAkDP,eAAe,KAAK,CAAC,CAEnFI,MAAM,CAACI,EAAE,CAAC,SAAS,CAAE,IAAM,CACvBF,OAAO,CAACC,GAAG,CAAC,4CAA4CH,MAAM,CAACK,EAAE,EAAE,CAAC,CACxE,CAAC,CAAC,CAEFL,MAAM,CAACI,EAAE,CAAC,YAAY,CAAGE,MAAM,EAAK,CAChCJ,OAAO,CAACC,GAAG,CAAC,uCAAuCG,MAAM,EAAE,CAAC,CAChE,CAAC,CAAC,CAEFN,MAAM,CAACI,EAAE,CAAC,eAAe,CAAGG,GAAG,EAAK,CAChCL,OAAO,CAACjC,KAAK,CAAC,yCAAyC,CAAEsC,GAAG,CAAC,CACjE,CAAC,CAAC,CAEF;AACAP,MAAM,CAACI,EAAE,CAAC,sBAAsB,CAAGI,IAMlC,EAAK,CACJN,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAEK,IAAI,CAAC,CAClE1C,YAAY,CAAC2C,aAAa,EACxBA,aAAa,CAACC,GAAG,CAACC,QAAQ,EACxBA,QAAQ,CAACC,GAAG,GAAKJ,IAAI,CAACH,EAAE,CACpB,CACE,GAAGM,QAAQ,CACXE,MAAM,CAAEL,IAAI,CAACK,MAAM,CACnB;AACA,IAAIL,IAAI,CAACM,SAAS,EAAI,CAAEA,SAAS,CAAEN,IAAI,CAACM,SAAU,CAAC,CAAC,CACpD,IAAIN,IAAI,CAACO,WAAW,EAAI,CAAEA,WAAW,CAAEP,IAAI,CAACO,WAAY,CAAC,CAAC,CAC1D,IAAIP,IAAI,CAACQ,MAAM,EAAI,CAAEA,MAAM,CAAER,IAAI,CAACQ,MAAO,CAAC,CAC5C,CAAC,CACDL,QACN,CACF,CAAC,CACH,CAAC,CAAC,CAEF;AACA,MAAO,IAAM,CACXT,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACrDH,MAAM,CAACiB,UAAU,CAAC,CAAC,CACrB,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAAE;AACR;AAEA;AACA,KAAM,CAAAtB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACF;AACA,KAAM,CAAAuB,QAAQ,CAAG,KAAM,CAAAnE,WAAW,CAACoE,YAAY,CAAC,CAAC,CACjDjB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEe,QAAQ,CAACV,IAAI,CAAC3C,SAAS,CAAC,CAE3D;AACAC,YAAY,CAACoD,QAAQ,CAACV,IAAI,CAAC3C,SAAS,CAAC,CACvC,CAAE,MAAO0C,GAAQ,CAAE,KAAAa,aAAA,CAAAC,kBAAA,CACjBnB,OAAO,CAACjC,KAAK,CAAC,0BAA0B,CAAEsC,GAAG,CAAC,CAC9CrC,QAAQ,CAAC,EAAAkD,aAAA,CAAAb,GAAG,CAACW,QAAQ,UAAAE,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAcZ,IAAI,UAAAa,kBAAA,iBAAlBA,kBAAA,CAAoBC,OAAO,GAAI,0BAA0B,CAAC,CACnExD,YAAY,CAAC,EAAE,CAAC,CAClB,CAAC,OAAS,CACRE,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuD,eAAe,CAAIlB,EAAU,EAAK,CACtC7B,mBAAmB,CAAC6B,EAAE,CAAC,CACvB/B,YAAY,CAAC,IAAI,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAAkD,aAAa,CAAIb,QAAkB,EAAK,CAC5CjC,iBAAiB,CAACiC,QAAQ,CAAC,CAC3B/B,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAA6C,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CAAClD,gBAAgB,CAAE,OAEvB,GAAI,CACF;AACA,KAAM,CAAAxB,WAAW,CAAC2E,cAAc,CAACnD,gBAAgB,CAAC,CAElD;AACAT,YAAY,CAACD,SAAS,CAAC8D,MAAM,CAAChB,QAAQ,EAAIA,QAAQ,CAACC,GAAG,GAAKrC,gBAAgB,CAAC,CAAC,CAE7E;AACAH,UAAU,CAAC,+BAA+B,CAAC,CAC3CF,QAAQ,CAAC,EAAE,CAAC,CAAE;AAChB,CAAE,MAAOqC,GAAQ,CAAE,KAAAqB,cAAA,CAAAC,mBAAA,CACjB3B,OAAO,CAACjC,KAAK,CAAC,0BAA0B,CAAEsC,GAAG,CAAC,CAC9CrC,QAAQ,CAAC,EAAA0D,cAAA,CAAArB,GAAG,CAACW,QAAQ,UAAAU,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcpB,IAAI,UAAAqB,mBAAA,iBAAlBA,mBAAA,CAAoBP,OAAO,GAAI,2BAA2B,CAAC,CACtE,CAAC,OAAS,CACR;AACAhD,YAAY,CAAC,KAAK,CAAC,CACnBE,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAsD,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CAACrD,cAAc,CAAE,OAErB,GAAI,CACFK,UAAU,CAAC,IAAI,CAAC,CAChBZ,QAAQ,CAAC,EAAE,CAAC,CAEZgC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAE1B,cAAc,CAACmC,GAAG,CAAC,CAC5D;AACA,KAAM,CAAA7D,WAAW,CAACgF,YAAY,CAACtD,cAAc,CAACmC,GAAG,CAAC,CAElD;AACA;AACA9C,YAAY,CAAC2C,aAAa,EACxBA,aAAa,CAACC,GAAG,CAACsB,CAAC,EACjBA,CAAC,CAACpB,GAAG,GAAKnC,cAAc,CAACmC,GAAG,CAAG,CAAE,GAAGoB,CAAC,CAAEnB,MAAM,CAAE,SAAU,CAAC,CAAGmB,CAC/D,CACF,CAAC,CAED;AACA5D,UAAU,cACRlB,KAAA,QAAA+E,QAAA,eACE7E,IAAA,MAAA6E,QAAA,cAAG/E,KAAA,WAAA+E,QAAA,EAAQ,aAAU,CAACxD,cAAc,CAACyD,IAAI,CAAC,oBAAiB,EAAQ,CAAC,CAAG,CAAC,cACxE9E,IAAA,MAAG+E,SAAS,CAAC,cAAc,CAAAF,QAAA,CAAC,mCAAiC,CAAG,CAAC,EAC9D,CACP,CAAC,CAEH,CAAE,MAAO1B,GAAQ,CAAE,KAAA6B,cAAA,CAAAC,mBAAA,CACjBnC,OAAO,CAACjC,KAAK,CAAC,yBAAyB,CAAEsC,GAAG,CAAC,CAC7CrC,QAAQ,CAAC,EAAAkE,cAAA,CAAA7B,GAAG,CAACW,QAAQ,UAAAkB,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAc5B,IAAI,UAAA6B,mBAAA,iBAAlBA,mBAAA,CAAoBf,OAAO,GAAI,yBAAyB,CAAC,CAClE;AACA;AACF,CAAC,OAAS,CACRxC,UAAU,CAAC,KAAK,CAAC,CACjBF,gBAAgB,CAAC,KAAK,CAAC,CACvBF,iBAAiB,CAAC,IAAI,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAA4D,iBAAiB,CAAI3B,QAAkB,EAAK,CAChDzB,qBAAqB,CAACyB,QAAQ,CAAC,CAC/B3B,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAuD,qBAAqB,CAAIjB,OAAe,EAAK,CACjDtC,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,qBAAqB,CAAC,IAAI,CAAC,CAC3Bd,UAAU,CAACkD,OAAO,CAAC,CACnB;AACA,GAAIrC,kBAAkB,CAAE,CACtBnB,YAAY,CAAC0E,IAAI,EAAIA,IAAI,CAAC9B,GAAG,CAACsB,CAAC,EAC7BA,CAAC,CAACpB,GAAG,GAAK3B,kBAAkB,CAAC2B,GAAG,CAAG,CAAE,GAAGoB,CAAC,CAAEnB,MAAM,CAAE,WAAW,CAAE4B,YAAY,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAE,CAAC,CAAGX,CAAE;AAC/G,CAAC,CAAC,CACJ,CACA;AACA;AACF,CAAC,CAED;AACA,KAAM,CAAAY,UAAU,CAAIC,UAAkB,EAAK,CACzC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAJ,IAAI,CAACG,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACC,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAIxC,MAAc,EAAK,CAC9C,OAAQA,MAAM,EACZ,IAAK,OAAO,CACV,MAAO,aAAa,CACtB,IAAK,WAAW,CACd,MAAO,aAAa,CACtB,IAAK,SAAS,CACZ,MAAO,eAAe,CACxB,IAAK,MAAM,CACT,MAAO,cAAc,CACvB,IAAK,QAAQ,CACX,MAAO,YAAY,CACrB,QACE,MAAO,aAAa,CACxB,CACF,CAAC,CAED,oBACE;AACA3D,KAAA,CAAAI,SAAA,EAAA2E,QAAA,eACE/E,KAAA,QAAKiF,SAAS,CAAC,wCAAwC,CAAAF,QAAA,eACrD/E,KAAA,QAAA+E,QAAA,eACE7E,IAAA,OAAI+E,SAAS,CAAC,uBAAuB,CAAAF,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC1D7E,IAAA,MAAG+E,SAAS,CAAC,qBAAqB,CAAAF,QAAA,CAAC,6BAA2B,CAAG,CAAC,EAC/D,CAAC,cAEN/E,KAAA,QAAKiF,SAAS,CAAC,gBAAgB,CAAAF,QAAA,eAC7B7E,IAAA,CAACX,MAAM,EAAC6G,OAAO,CAAC,WAAW,CAACC,OAAO,CAAEA,CAAA,GAAM3F,QAAQ,CAAC,yBAAyB,CAAE,CAAAqE,QAAA,CAAC,cAEhF,CAAQ,CAAC,cACT7E,IAAA,CAACX,MAAM,EACL8G,OAAO,CAAEA,CAAA,GAAM3F,QAAQ,CAAC,mBAAmB,CAAE,CAC7C4F,QAAQ,CAAE,EAAC7F,IAAI,SAAJA,IAAI,YAAAH,YAAA,CAAJG,IAAI,CAAE8F,MAAM,UAAAjG,YAAA,WAAZA,YAAA,CAAcqD,MAAM,GAAIlD,IAAI,CAAC8F,MAAM,CAAC5C,MAAM,GAAK,QAAS,CACnE6C,KAAK,CAAE,EAAC/F,IAAI,SAAJA,IAAI,YAAAF,aAAA,CAAJE,IAAI,CAAE8F,MAAM,UAAAhG,aAAA,WAAZA,aAAA,CAAcoD,MAAM,GAAIlD,IAAI,CAAC8F,MAAM,CAAC5C,MAAM,GAAK,QAAQ,CAAG,uBAAuB,CAAG,uBAAwB,CAAAoB,QAAA,CACrH,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CAELhE,KAAK,eACJb,IAAA,CAACZ,KAAK,EACJmH,IAAI,CAAC,OAAO,CACZrC,OAAO,CAAErD,KAAM,CACf2F,OAAO,CAAEA,CAAA,GAAM1F,QAAQ,CAAC,EAAE,CAAE,CAC5BiE,SAAS,CAAC,MAAM,CACjB,CACF,CAEAhE,OAAO,eACNf,IAAA,CAACZ,KAAK,EACJmH,IAAI,CAAC,SAAS,CACdrC,OAAO,CAAEnD,OAAQ,CACjByF,OAAO,CAAEA,CAAA,GAAMxF,UAAU,CAAC,EAAE,CAAE,CAC9B+D,SAAS,CAAC,MAAM,CACjB,CACF,CAGA,EAACxE,IAAI,SAAJA,IAAI,YAAAD,aAAA,CAAJC,IAAI,CAAE8F,MAAM,UAAA/F,aAAA,WAAZA,aAAA,CAAcmD,MAAM,GAAIlD,IAAI,CAAC8F,MAAM,CAAC5C,MAAM,GAAK,QAAQ,cACvDzD,IAAA,CAACZ,KAAK,EACJmH,IAAI,CAAC,SAAS,CACdrC,OAAO,cACLpE,KAAA,QAAA+E,QAAA,eACE7E,IAAA,MAAG+E,SAAS,CAAC,aAAa,CAAAF,QAAA,CAAC,uDAAqD,CAAG,CAAC,cACpF7E,IAAA,MAAG+E,SAAS,CAAC,MAAM,CAAAF,QAAA,CAAC,wDAAsD,CAAG,CAAC,EAC3E,CACN,CACDE,SAAS,CAAC,MAAM,CACjB,CAAC,CACA,IAAI,cAER/E,IAAA,CAACV,IAAI,EAAAuF,QAAA,CACFlE,OAAO,cACNX,IAAA,QAAK+E,SAAS,CAAC,uCAAuC,CAAAF,QAAA,cACpD7E,IAAA,QAAK+E,SAAS,CAAC,0EAA0E,CAAM,CAAC,CAC7F,CAAC,CACJtE,SAAS,CAACgG,MAAM,GAAK,CAAC,cACxB3G,KAAA,QAAKiF,SAAS,CAAC,kBAAkB,CAAAF,QAAA,eAC/B7E,IAAA,QAAK+E,SAAS,CAAC,eAAe,CAAAF,QAAA,CAAC,cAAE,CAAK,CAAC,cACvC7E,IAAA,OAAI+E,SAAS,CAAC,0BAA0B,CAAAF,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC9D7E,IAAA,MAAG+E,SAAS,CAAC,0BAA0B,CAAAF,QAAA,CAAC,4EAExC,CAAG,CAAC,cACJ7E,IAAA,CAACX,MAAM,EAAC8G,OAAO,CAAEA,CAAA,GAAM3F,QAAQ,CAAC,mBAAmB,CAAE,CAAAqE,QAAA,CAAC,4BAEtD,CAAQ,CAAC,EACN,CAAC,cAEN7E,IAAA,QAAK+E,SAAS,CAAC,iBAAiB,CAAAF,QAAA,cAC9B/E,KAAA,UAAOiF,SAAS,CAAC,cAAc,CAAAF,QAAA,eAC7B7E,IAAA,UAAA6E,QAAA,cACE/E,KAAA,OAAA+E,QAAA,eACE7E,IAAA,OAAA6E,QAAA,CAAI,MAAI,CAAI,CAAC,cACb7E,IAAA,OAAA6E,QAAA,CAAI,SAAO,CAAI,CAAC,cAChB7E,IAAA,OAAA6E,QAAA,CAAI,QAAM,CAAI,CAAC,cACf7E,IAAA,OAAA6E,QAAA,CAAI,YAAU,CAAI,CAAC,cACnB7E,IAAA,OAAA6E,QAAA,CAAI,SAAO,CAAI,CAAC,cAChB7E,IAAA,OAAA6E,QAAA,CAAI,gBAAc,CAAI,CAAC,cACvB7E,IAAA,OAAA6E,QAAA,CAAI,aAAW,CAAI,CAAC,cACpB7E,IAAA,OAAA6E,QAAA,CAAI,SAAO,CAAI,CAAC,GARV,YASJ,CAAC,CACA,CAAC,cACR7E,IAAA,UAAA6E,QAAA,CACGpE,SAAS,CAAC6C,GAAG,CAAEC,QAAkB,OAAAmD,qBAAA,CAAAC,mBAAA,CAAAC,oBAAA,oBAChC9G,KAAA,OAAA+E,QAAA,eACE7E,IAAA,OAAA6E,QAAA,CAAKtB,QAAQ,CAACuB,IAAI,CAAK,CAAC,cACxB9E,IAAA,OAAA6E,QAAA,CAAKtB,QAAQ,CAACsD,OAAO,CAAK,CAAC,cAC3B7G,IAAA,OAAA6E,QAAA,cACE7E,IAAA,SAAM+E,SAAS,CAAE,6BAA6BkB,mBAAmB,CAAC1C,QAAQ,CAACE,MAAM,CAAC,EAAG,CAAAoB,QAAA,CAClFtB,QAAQ,CAACE,MAAM,CAACqD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGxD,QAAQ,CAACE,MAAM,CAACuD,KAAK,CAAC,CAAC,CAAC,CAC/D,CAAC,CACL,CAAC,cACLhH,IAAA,OAAA6E,QAAA,EAAA6B,qBAAA,CAAKnD,QAAQ,CAAC0D,oBAAoB,UAAAP,qBAAA,UAAAA,qBAAA,CAAI,GAAG,CAAK,CAAC,cAC/C1G,IAAA,OAAA6E,QAAA,CAAKW,UAAU,CAACjC,QAAQ,CAAC2D,SAAS,CAAC,CAAK,CAAC,cACzClH,IAAA,OAAA6E,QAAA,CACGtB,QAAQ,CAAC8B,YAAY,CAClB9B,QAAQ,CAACE,MAAM,GAAK,MAAM,EAAIF,QAAQ,CAACE,MAAM,GAAK,WAAW,CAC3D+B,UAAU,CAACjC,QAAQ,CAACK,MAAM,EAAIL,QAAQ,CAACI,WAAW,EAAI,EAAE,CAAC,CACzD6B,UAAU,CAACjC,QAAQ,CAAC8B,YAAY,CAAC,CACnC9B,QAAQ,CAACK,MAAM,CACb4B,UAAU,CAACjC,QAAQ,CAACK,MAAM,CAAC,cAC3B5D,IAAA,SAAM+E,SAAS,CAAC,qBAAqB,CAAAF,QAAA,CAAC,GAAC,CAAM,CAAC,CAElD,CAAC,cACL7E,IAAA,OAAA6E,QAAA,CACGtB,QAAQ,CAACE,MAAM,GAAK,MAAM,EAAIF,QAAQ,CAACE,MAAM,GAAK,WAAW,cAC5D3D,KAAA,QAAKiF,SAAS,CAAC,SAAS,CAAAF,QAAA,eACtB/E,KAAA,QAAA+E,QAAA,EAAK,SAAO,EAAA8B,mBAAA,CAACpD,QAAQ,CAAC4D,SAAS,UAAAR,mBAAA,UAAAA,mBAAA,CAAI,CAAC,EAAM,CAAC,cAC3C7G,KAAA,QAAA+E,QAAA,EAAK,UAAQ,EAAA+B,oBAAA,CAACrD,QAAQ,CAAC6D,UAAU,UAAAR,oBAAA,UAAAA,oBAAA,CAAI,CAAC,EAAM,CAAC,EAC1C,CAAC,cAEN5G,IAAA,SAAM+E,SAAS,CAAC,qBAAqB,CAAAF,QAAA,CAAC,GAAC,CAAM,CAC9C,CACC,CAAC,cACL7E,IAAA,OAAA6E,QAAA,cACE/E,KAAA,QAAKiF,SAAS,CAAC,gBAAgB,CAAAF,QAAA,eAC7B7E,IAAA,CAACX,MAAM,EAEL6G,OAAO,CAAC,WAAW,CACnBmB,IAAI,CAAC,IAAI,CACTlB,OAAO,CAAEA,CAAA,GAAM,CACX,KAAM,CAAAmB,QAAQ,CAAG/D,QAAQ,CAACC,GAAG,CAC7BV,OAAO,CAACC,GAAG,CAAC,qCAAqCuE,QAAQ,EAAE,CAAC,CAAE;AAC9D,GAAI/D,QAAQ,CAACE,MAAM,GAAK,OAAO,EAAIF,QAAQ,CAACE,MAAM,GAAK,WAAW,CAAE,CAChEjD,QAAQ,CAAC,mBAAmB8G,QAAQ,EAAE,CAAC,CAC3C,CAAC,IAAM,CACH9G,QAAQ,CAAC,qBAAqB8G,QAAQ,EAAE,CAAC,CAC7C,CACJ,CAAE,CACFlB,QAAQ,CAAE7C,QAAQ,CAACE,MAAM,GAAK,SAAU,CAAAoB,QAAA,CAGvCtB,QAAQ,CAACE,MAAM,GAAK,OAAO,EAAIF,QAAQ,CAACE,MAAM,GAAK,WAAW,CAAG,MAAM,CAAG,MAAM,EAf7E,eAgBE,CAAC,CAER,CAACF,QAAQ,CAACE,MAAM,GAAK,MAAM,EAAIF,QAAQ,CAACE,MAAM,GAAK,WAAW,gBAC7DzD,IAAA,CAACX,MAAM,EAEL6G,OAAO,CAAC,WAAW,CACnBmB,IAAI,CAAC,IAAI,CACTlB,OAAO,CAAEA,CAAA,GAAM3F,QAAQ,CAAC,wBAAwB+C,QAAQ,CAACC,GAAG,EAAE,CAAE,CAAAqB,QAAA,CACjE,WAED,EANM,eAME,CACT,CACAtB,QAAQ,CAACE,MAAM,GAAK,OAAO,eAC1B3D,KAAA,CAAAI,SAAA,EAAA2E,QAAA,eACE7E,IAAA,CAACX,MAAM,EAEL6G,OAAO,CAAC,WAAW,CACnBmB,IAAI,CAAC,IAAI,CACTlB,OAAO,CAAEA,CAAA,GAAM3F,QAAQ,CAAC,yBAAyB+C,QAAQ,CAACC,GAAG,EAAE,CAAE,CAAAqB,QAAA,CAClE,YAED,EANM,gBAME,CAAC,cACT7E,IAAA,CAACX,MAAM,EAELgI,IAAI,CAAC,IAAI,CACTlB,OAAO,CAAEA,CAAA,GAAM/B,aAAa,CAACb,QAAQ,CAAE,CAAAsB,QAAA,CACxC,UAED,EALM,UAKE,CAAC,EACT,CACH,CACAtB,QAAQ,CAACE,MAAM,GAAK,OAAO,eAC1BzD,IAAA,CAAAE,SAAA,EAAA2E,QAAA,cACE7E,IAAA,CAACX,MAAM,EAEL6G,OAAO,CAAC,WAAW,CACnBmB,IAAI,CAAC,IAAI,CACTlB,OAAO,CAAEA,CAAA,GAAMjB,iBAAiB,CAAC3B,QAAQ,CAAE,CAC3C+C,KAAK,CAAC,wBAAwB,CAAAzB,QAAA,CAC/B,UAED,EAPM,mBAOE,CAAC,CACT,CACH,cACD7E,IAAA,CAACX,MAAM,EAEL6G,OAAO,CAAC,QAAQ,CAChBmB,IAAI,CAAC,IAAI,CACTlB,OAAO,CAAEA,CAAA,GAAMhC,eAAe,CAACZ,QAAQ,CAACC,GAAG,CAAE,CAC7C4C,QAAQ,CAAE7C,QAAQ,CAACE,MAAM,GAAK,SAAU,CAAAoB,QAAA,CACzC,QAED,EAPM,YAOE,CAAC,EACN,CAAC,CACJ,CAAC,GAvGEtB,QAAQ,CAACC,GAwGd,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,CACG,CAAC,cAEPxD,IAAA,CAACT,YAAY,EACXgI,MAAM,CAAEtG,SAAU,CAClBqF,KAAK,CAAC,iBAAiB,CACvBpC,OAAO,CAAC,8EAA8E,CACtFsD,WAAW,CAAC,QAAQ,CACpBC,SAAS,CAAEpD,oBAAqB,CAChCqD,QAAQ,CAAEA,CAAA,GAAM,CACdxG,YAAY,CAAC,KAAK,CAAC,CACnBE,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CACH,CAAC,cAGFpB,IAAA,CAACT,YAAY,EACXgI,MAAM,CAAEhG,aAAc,CACtB+E,KAAK,CAAC,eAAe,CACrBpC,OAAO,cACLpE,KAAA,QAAA+E,QAAA,eACE7E,IAAA,MAAA6E,QAAA,CAAG,8CAA4C,CAAG,CAAC,cACnD/E,KAAA,MAAGiF,SAAS,CAAC,oBAAoB,CAAAF,QAAA,eAAC7E,IAAA,WAAA6E,QAAA,CAAQ,OAAK,CAAQ,CAAC,gEAA6D,EAAG,CAAC,CACxHxD,cAAc,eACbvB,KAAA,QAAKiF,SAAS,CAAC,iCAAiC,CAAAF,QAAA,eAC9C/E,KAAA,MAAA+E,QAAA,eAAG7E,IAAA,WAAA6E,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACxD,cAAc,CAACyD,IAAI,EAAI,CAAC,cACnDhF,KAAA,MAAA+E,QAAA,eAAG7E,IAAA,WAAA6E,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAACxD,cAAc,CAACwF,OAAO,EAAI,CAAC,cACzD/G,KAAA,MAAA+E,QAAA,eAAG7E,IAAA,WAAA6E,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACxD,cAAc,CAACsG,QAAQ,CAAC,IAAK,CAACtG,cAAc,CAACuG,SAAS,CAAC,GAAI,EAAG,CAAC,EACvF,CACN,cACD5H,IAAA,MAAG+E,SAAS,CAAC,4BAA4B,CAAAF,QAAA,CAAC,8EAA4E,CAAG,CAAC,EACvH,CACN,CACD2C,WAAW,CAAE/F,OAAO,CAAG,YAAY,CAAG,eAAgB,CACtDgG,SAAS,CAAE/C,kBAAmB,CAC9BgD,QAAQ,CAAEA,CAAA,GAAM,CACdlG,gBAAgB,CAAC,KAAK,CAAC,CACvBF,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAE,CACH,CAAC,cAGFtB,IAAA,CAACR,qBAAqB,EACpB+H,MAAM,CAAE5F,iBAAkB,CAC1B6E,OAAO,CAAEA,CAAA,GAAM5E,oBAAoB,CAAC,KAAK,CAAE,CAC3C2B,QAAQ,CAAE1B,kBAAmB,CAC7BgG,WAAW,CAAE1C,qBAAsB,CACpC,CAAC,EACF,CACF;AAAA,EAEJ,CAAC,CAED,cAAe,CAAAhF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}